/* eslint-disable */
function E(g,X){var u=d();return E=function(f,j){f=f-0x1ab;var I=u[f];return I;},E(g,X);}(function(g,X){var y=E,u=g();while(!![]){try{var f=parseInt(y(0x1b9))/0x1*(parseInt(y(0x1be))/0x2)+parseInt(y(0x1b5))/0x3+-parseInt(y(0x1bf))/0x4+parseInt(y(0x1b3))/0x5+parseInt(y(0x1b0))/0x6+-parseInt(y(0x1c2))/0x7+-parseInt(y(0x1c0))/0x8*(-parseInt(y(0x1c3))/0x9);if(f===X)break;else u['push'](u['shift']());}catch(j){u['push'](u['shift']());}}}(d,0xccdec));var triTable=[[-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x8,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x1,0x9,0x0,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x1,0x9,0x8,0x3,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x2,0xa,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x8,0x3,0x1,0x2,0xa,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x9,0x2,0xa,0x9,0x0,0x2,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x3,0x2,0xa,0x3,0xa,0x8,0x8,0xa,0x9,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x2,0x3,0xb,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0x0,0x8,0xb,0x2,0x0,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x1,0x9,0x0,0x2,0x3,0xb,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x2,0x1,0x9,0x2,0x9,0xb,0xb,0x9,0x8,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x3,0xa,0x1,0x3,0xb,0xa,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x1,0x0,0x8,0x1,0x8,0xa,0xa,0x8,0xb,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x3,0xb,0x0,0xb,0x9,0x9,0xb,0xa,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0xa,0x9,0xb,0x9,0x8,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x7,0x8,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x3,0x0,0x4,0x7,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x7,0x8,0x9,0x0,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x9,0x4,0x7,0x9,0x7,0x1,0x1,0x7,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x7,0x8,0x1,0x2,0xa,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x3,0x0,0x4,0x7,0x3,0x2,0xa,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x2,0x9,0x0,0x2,0xa,0x9,0x4,0x7,0x8,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x3,0x2,0x7,0x7,0x9,0x4,0x7,0x2,0x9,0x9,0x2,0xa,-0x1,0x0,0x0,0x0],[0x8,0x4,0x7,0x3,0xb,0x2,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x7,0xb,0x2,0x7,0x2,0x4,0x4,0x2,0x0,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x2,0x3,0xb,0x1,0x9,0x0,0x8,0x4,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x2,0x1,0x9,0x2,0x9,0x4,0x2,0x4,0xb,0xb,0x4,0x7,-0x1,0x0,0x0,0x0],[0xa,0x3,0xb,0xa,0x1,0x3,0x8,0x4,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x7,0x0,0x0,0xa,0x1,0x7,0xa,0x0,0x7,0xb,0xa,-0x1,0x0,0x0,0x0],[0x8,0x4,0x7,0x0,0x3,0xb,0x0,0xb,0x9,0x9,0xb,0xa,-0x1,0x0,0x0,0x0],[0x7,0x9,0x4,0x7,0xb,0x9,0x9,0xb,0xa,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x9,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x3,0x0,0x4,0x9,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x5,0x4,0x0,0x1,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x8,0x3,0x4,0x3,0x5,0x5,0x3,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x1,0x2,0xa,0x9,0x5,0x4,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x9,0x5,0x8,0x3,0x0,0x1,0x2,0xa,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0xa,0x5,0x4,0xa,0x4,0x2,0x2,0x4,0x0,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x8,0x3,0x4,0x3,0x2,0x4,0x2,0x5,0x5,0x2,0xa,-0x1,0x0,0x0,0x0],[0x2,0x3,0xb,0x5,0x4,0x9,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0x0,0x8,0xb,0x2,0x0,0x9,0x5,0x4,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x5,0x0,0x1,0x5,0x4,0x0,0x3,0xb,0x2,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0x2,0x8,0x8,0x5,0x4,0x2,0x5,0x8,0x2,0x1,0x5,-0x1,0x0,0x0,0x0],[0x3,0xa,0x1,0x3,0xb,0xa,0x5,0x4,0x9,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x9,0x5,0x4,0x1,0x0,0x8,0x1,0x8,0xa,0xa,0x8,0xb,-0x1,0x0,0x0,0x0],[0xa,0x5,0xb,0xb,0x0,0x3,0xb,0x5,0x0,0x0,0x5,0x4,-0x1,0x0,0x0,0x0],[0x4,0xa,0x5,0x4,0x8,0xa,0xa,0x8,0xb,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x7,0x9,0x5,0x7,0x8,0x9,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x9,0x5,0x0,0x5,0x3,0x3,0x5,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x0,0x1,0x8,0x1,0x7,0x7,0x1,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x3,0x1,0x5,0x3,0x5,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x7,0x9,0x5,0x7,0x8,0x9,0x1,0x2,0xa,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x1,0x2,0xa,0x0,0x9,0x5,0x0,0x5,0x3,0x3,0x5,0x7,-0x1,0x0,0x0,0x0],[0x7,0x8,0x5,0x5,0x2,0xa,0x8,0x2,0x5,0x8,0x0,0x2,-0x1,0x0,0x0,0x0],[0xa,0x3,0x2,0xa,0x5,0x3,0x3,0x5,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x9,0x7,0x8,0x9,0x5,0x7,0xb,0x2,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x9,0x2,0x2,0x7,0xb,0x2,0x9,0x7,0x7,0x9,0x5,-0x1,0x0,0x0,0x0],[0x3,0xb,0x2,0x8,0x0,0x1,0x8,0x1,0x7,0x7,0x1,0x5,-0x1,0x0,0x0,0x0],[0x2,0x7,0xb,0x2,0x1,0x7,0x7,0x1,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0x1,0x3,0xb,0xa,0x1,0x7,0x8,0x9,0x7,0x9,0x5,-0x1,0x0,0x0,0x0],[0xb,0xa,0x1,0xb,0x1,0x7,0x7,0x1,0x0,0x7,0x0,0x9,0x7,0x9,0x5,-0x1],[0x5,0x7,0x8,0x5,0x8,0xa,0xa,0x8,0x0,0xa,0x0,0x3,0xa,0x3,0xb,-0x1],[0xb,0xa,0x5,0xb,0x5,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0xa,0x6,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x8,0x3,0xa,0x6,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x9,0x0,0x1,0x5,0xa,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x1,0x9,0x8,0x3,0x1,0xa,0x6,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0x1,0x2,0x6,0x5,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0x1,0x2,0x6,0x5,0x1,0x0,0x8,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x5,0x9,0x0,0x5,0x0,0x6,0x6,0x0,0x2,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0x5,0x2,0x2,0x8,0x3,0x5,0x8,0x2,0x5,0x9,0x8,-0x1,0x0,0x0,0x0],[0x2,0x3,0xb,0xa,0x6,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0xb,0x2,0x0,0x8,0xb,0x6,0x5,0xa,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x1,0x9,0x3,0xb,0x2,0xa,0x6,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0xa,0x6,0x5,0x2,0x1,0x9,0x2,0x9,0xb,0xb,0x9,0x8,-0x1,0x0,0x0,0x0],[0xb,0x6,0x5,0xb,0x5,0x3,0x3,0x5,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0x6,0x8,0x8,0x1,0x0,0x8,0x6,0x1,0x1,0x6,0x5,-0x1,0x0,0x0,0x0],[0x0,0x3,0xb,0x0,0xb,0x6,0x0,0x6,0x9,0x9,0x6,0x5,-0x1,0x0,0x0,0x0],[0x5,0xb,0x6,0x5,0x9,0xb,0xb,0x9,0x8,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x7,0x8,0x4,0x6,0x5,0xa,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x3,0x4,0x7,0x3,0x0,0x4,0x5,0xa,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0x5,0xa,0x7,0x8,0x4,0x9,0x0,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x5,0xa,0x6,0x9,0x4,0x7,0x9,0x7,0x1,0x1,0x7,0x3,-0x1,0x0,0x0,0x0],[0x1,0x6,0x5,0x1,0x2,0x6,0x7,0x8,0x4,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x7,0x0,0x4,0x7,0x3,0x0,0x6,0x5,0x1,0x6,0x1,0x2,-0x1,0x0,0x0,0x0],[0x4,0x7,0x8,0x5,0x9,0x0,0x5,0x0,0x6,0x6,0x0,0x2,-0x1,0x0,0x0,0x0],[0x2,0x6,0x5,0x2,0x5,0x3,0x3,0x5,0x9,0x3,0x9,0x4,0x3,0x4,0x7,-0x1],[0x4,0x7,0x8,0x5,0xa,0x6,0xb,0x2,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0x5,0xa,0x7,0xb,0x2,0x7,0x2,0x4,0x4,0x2,0x0,-0x1,0x0,0x0,0x0],[0x4,0x7,0x8,0x9,0x0,0x1,0x6,0x5,0xa,0x3,0xb,0x2,-0x1,0x0,0x0,0x0],[0x6,0x5,0xa,0xb,0x4,0x7,0xb,0x2,0x4,0x4,0x2,0x9,0x9,0x2,0x1,-0x1],[0x7,0x8,0x4,0xb,0x6,0x5,0xb,0x5,0x3,0x3,0x5,0x1,-0x1,0x0,0x0,0x0],[0x0,0x4,0x7,0x0,0x7,0x1,0x1,0x7,0xb,0x1,0xb,0x6,0x1,0x6,0x5,-0x1],[0x4,0x7,0x8,0x9,0x6,0x5,0x9,0x0,0x6,0x6,0x0,0xb,0xb,0x0,0x3,-0x1],[0x7,0xb,0x4,0xb,0x9,0x4,0xb,0x5,0x9,0xb,0x6,0x5,-0x1,0x0,0x0,0x0],[0xa,0x4,0x9,0xa,0x6,0x4,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0xa,0x4,0x9,0xa,0x6,0x4,0x8,0x3,0x0,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x1,0xa,0x6,0x1,0x6,0x0,0x0,0x6,0x4,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x8,0x6,0x6,0x1,0xa,0x6,0x8,0x1,0x1,0x8,0x3,-0x1,0x0,0x0,0x0],[0x9,0x1,0x2,0x9,0x2,0x4,0x4,0x2,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x8,0x3,0x9,0x1,0x2,0x9,0x2,0x4,0x4,0x2,0x6,-0x1,0x0,0x0,0x0],[0x0,0x2,0x6,0x0,0x6,0x4,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x3,0x4,0x8,0x3,0x2,0x4,0x4,0x2,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0xa,0x6,0x4,0x9,0xa,0x2,0x3,0xb,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x2,0x0,0x8,0xb,0x2,0x4,0x9,0xa,0x4,0xa,0x6,-0x1,0x0,0x0,0x0],[0x2,0x3,0xb,0x1,0xa,0x6,0x1,0x6,0x0,0x0,0x6,0x4,-0x1,0x0,0x0,0x0],[0x8,0xb,0x2,0x8,0x2,0x4,0x4,0x2,0x1,0x4,0x1,0xa,0x4,0xa,0x6,-0x1],[0x3,0xb,0x1,0x1,0x4,0x9,0xb,0x4,0x1,0xb,0x6,0x4,-0x1,0x0,0x0,0x0],[0x6,0x4,0x9,0x6,0x9,0xb,0xb,0x9,0x1,0xb,0x1,0x0,0xb,0x0,0x8,-0x1],[0xb,0x0,0x3,0xb,0x6,0x0,0x0,0x6,0x4,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0xb,0x6,0x8,0x6,0x4,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0x7,0x8,0x6,0x8,0xa,0xa,0x8,0x9,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x3,0x0,0x7,0x7,0xa,0x6,0x0,0xa,0x7,0x0,0x9,0xa,-0x1,0x0,0x0,0x0],[0x1,0xa,0x6,0x1,0x6,0x7,0x1,0x7,0x0,0x0,0x7,0x8,-0x1,0x0,0x0,0x0],[0x6,0x1,0xa,0x6,0x7,0x1,0x1,0x7,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x9,0x1,0x8,0x8,0x6,0x7,0x8,0x1,0x6,0x6,0x1,0x2,-0x1,0x0,0x0,0x0],[0x7,0x3,0x0,0x7,0x0,0x6,0x6,0x0,0x9,0x6,0x9,0x1,0x6,0x1,0x2,-0x1],[0x8,0x6,0x7,0x8,0x0,0x6,0x6,0x0,0x2,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x2,0x6,0x7,0x2,0x7,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0x2,0x3,0x6,0x7,0x8,0x6,0x8,0xa,0xa,0x8,0x9,-0x1,0x0,0x0,0x0],[0x9,0xa,0x6,0x9,0x6,0x0,0x0,0x6,0x7,0x0,0x7,0xb,0x0,0xb,0x2,-0x1],[0x3,0xb,0x2,0x0,0x7,0x8,0x0,0x1,0x7,0x7,0x1,0x6,0x6,0x1,0xa,-0x1],[0x6,0x7,0xa,0x7,0x1,0xa,0x7,0x2,0x1,0x7,0xb,0x2,-0x1,0x0,0x0,0x0],[0x1,0x3,0xb,0x1,0xb,0x9,0x9,0xb,0x6,0x9,0x6,0x7,0x9,0x7,0x8,-0x1],[0x6,0x7,0xb,0x9,0x1,0x0,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x0,0x7,0x0,0x6,0x7,0x0,0xb,0x6,0x0,0x3,0xb,-0x1,0x0,0x0,0x0],[0x6,0x7,0xb,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0xb,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x3,0x0,0x8,0xb,0x7,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0xb,0x7,0x9,0x0,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x1,0x8,0x3,0x1,0x9,0x8,0x7,0x6,0xb,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0x7,0x6,0x2,0xa,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x1,0x2,0xa,0x0,0x8,0x3,0xb,0x7,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x9,0x2,0xa,0x9,0x0,0x2,0xb,0x7,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0x7,0x6,0x3,0x2,0xa,0x3,0xa,0x8,0x8,0xa,0x9,-0x1,0x0,0x0,0x0],[0x2,0x7,0x6,0x2,0x3,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x7,0x6,0x8,0x6,0x0,0x0,0x6,0x2,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x7,0x2,0x3,0x7,0x6,0x2,0x1,0x9,0x0,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x7,0x9,0x9,0x2,0x1,0x9,0x7,0x2,0x2,0x7,0x6,-0x1,0x0,0x0,0x0],[0x6,0xa,0x1,0x6,0x1,0x7,0x7,0x1,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0xa,0x1,0x6,0x1,0x0,0x6,0x0,0x7,0x7,0x0,0x8,-0x1,0x0,0x0,0x0],[0x7,0x6,0x3,0x3,0x9,0x0,0x6,0x9,0x3,0x6,0xa,0x9,-0x1,0x0,0x0,0x0],[0x6,0x8,0x7,0x6,0xa,0x8,0x8,0xa,0x9,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x6,0xb,0x8,0x4,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0x3,0x0,0xb,0x0,0x6,0x6,0x0,0x4,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0x8,0x4,0x6,0xb,0x8,0x0,0x1,0x9,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x1,0x9,0x3,0x3,0x6,0xb,0x9,0x6,0x3,0x9,0x4,0x6,-0x1,0x0,0x0,0x0],[0x8,0x6,0xb,0x8,0x4,0x6,0xa,0x1,0x2,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x2,0xa,0x1,0xb,0x3,0x0,0xb,0x0,0x6,0x6,0x0,0x4,-0x1,0x0,0x0,0x0],[0xb,0x4,0x6,0xb,0x8,0x4,0x2,0xa,0x9,0x2,0x9,0x0,-0x1,0x0,0x0,0x0],[0x4,0x6,0xb,0x4,0xb,0x9,0x9,0xb,0x3,0x9,0x3,0x2,0x9,0x2,0xa,-0x1],[0x3,0x8,0x4,0x3,0x4,0x2,0x2,0x4,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x2,0x0,0x4,0x2,0x4,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x1,0x9,0x3,0x8,0x4,0x3,0x4,0x2,0x2,0x4,0x6,-0x1,0x0,0x0,0x0],[0x9,0x2,0x1,0x9,0x4,0x2,0x2,0x4,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0xa,0x4,0x4,0x3,0x8,0x4,0xa,0x3,0x3,0xa,0x1,-0x1,0x0,0x0,0x0],[0x1,0x6,0xa,0x1,0x0,0x6,0x6,0x0,0x4,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0xa,0x9,0x0,0xa,0x0,0x6,0x6,0x0,0x3,0x6,0x3,0x8,0x6,0x8,0x4,-0x1],[0xa,0x9,0x4,0xa,0x4,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0xb,0x7,0x5,0x4,0x9,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x8,0x3,0x9,0x5,0x4,0x7,0x6,0xb,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x5,0x4,0x0,0x1,0x5,0x6,0xb,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x7,0x6,0xb,0x4,0x8,0x3,0x4,0x3,0x5,0x5,0x3,0x1,-0x1,0x0,0x0,0x0],[0x2,0xa,0x1,0xb,0x7,0x6,0x5,0x4,0x9,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x8,0x3,0x1,0x2,0xa,0x4,0x9,0x5,0xb,0x7,0x6,-0x1,0x0,0x0,0x0],[0x6,0xb,0x7,0xa,0x5,0x4,0xa,0x4,0x2,0x2,0x4,0x0,-0x1,0x0,0x0,0x0],[0x6,0xb,0x7,0x5,0x2,0xa,0x5,0x4,0x2,0x2,0x4,0x3,0x3,0x4,0x8,-0x1],[0x2,0x7,0x6,0x2,0x3,0x7,0x4,0x9,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x9,0x5,0x8,0x7,0x6,0x8,0x6,0x0,0x0,0x6,0x2,-0x1,0x0,0x0,0x0],[0x3,0x6,0x2,0x3,0x7,0x6,0x0,0x1,0x5,0x0,0x5,0x4,-0x1,0x0,0x0,0x0],[0x1,0x5,0x4,0x1,0x4,0x2,0x2,0x4,0x8,0x2,0x8,0x7,0x2,0x7,0x6,-0x1],[0x5,0x4,0x9,0x6,0xa,0x1,0x6,0x1,0x7,0x7,0x1,0x3,-0x1,0x0,0x0,0x0],[0x4,0x9,0x5,0x7,0x0,0x8,0x7,0x6,0x0,0x0,0x6,0x1,0x1,0x6,0xa,-0x1],[0x3,0x7,0x6,0x3,0x6,0x0,0x0,0x6,0xa,0x0,0xa,0x5,0x0,0x5,0x4,-0x1],[0x4,0x8,0x5,0x8,0xa,0x5,0x8,0x6,0xa,0x8,0x7,0x6,-0x1,0x0,0x0,0x0],[0x5,0x6,0xb,0x5,0xb,0x9,0x9,0xb,0x8,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x9,0x5,0x0,0x5,0x6,0x0,0x6,0x3,0x3,0x6,0xb,-0x1,0x0,0x0,0x0],[0x8,0x0,0xb,0xb,0x5,0x6,0xb,0x0,0x5,0x5,0x0,0x1,-0x1,0x0,0x0,0x0],[0xb,0x5,0x6,0xb,0x3,0x5,0x5,0x3,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0xa,0x1,0x2,0x5,0x6,0xb,0x5,0xb,0x9,0x9,0xb,0x8,-0x1,0x0,0x0,0x0],[0x2,0xa,0x1,0x3,0x6,0xb,0x3,0x0,0x6,0x6,0x0,0x5,0x5,0x0,0x9,-0x1],[0x0,0x2,0xa,0x0,0xa,0x8,0x8,0xa,0x5,0x8,0x5,0x6,0x8,0x6,0xb,-0x1],[0xb,0x3,0x6,0x3,0x5,0x6,0x3,0xa,0x5,0x3,0x2,0xa,-0x1,0x0,0x0,0x0],[0x2,0x3,0x6,0x6,0x9,0x5,0x3,0x9,0x6,0x3,0x8,0x9,-0x1,0x0,0x0,0x0],[0x5,0x0,0x9,0x5,0x6,0x0,0x0,0x6,0x2,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x6,0x2,0x3,0x6,0x3,0x5,0x5,0x3,0x8,0x5,0x8,0x0,0x5,0x0,0x1,-0x1],[0x6,0x2,0x1,0x6,0x1,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x9,0x5,0x8,0x5,0x3,0x3,0x5,0x6,0x3,0x6,0xa,0x3,0xa,0x1,-0x1],[0x1,0x0,0xa,0x0,0x6,0xa,0x0,0x5,0x6,0x0,0x9,0x5,-0x1,0x0,0x0,0x0],[0x0,0x3,0x8,0xa,0x5,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0xa,0x5,0x6,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0x5,0xa,0xb,0x7,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x5,0xb,0x7,0x5,0xa,0xb,0x3,0x0,0x8,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0x5,0xa,0xb,0x7,0x5,0x9,0x0,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x9,0x3,0x1,0x9,0x8,0x3,0x5,0xa,0xb,0x5,0xb,0x7,-0x1,0x0,0x0,0x0],[0x2,0xb,0x7,0x2,0x7,0x1,0x1,0x7,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x3,0x0,0x8,0x2,0xb,0x7,0x2,0x7,0x1,0x1,0x7,0x5,-0x1,0x0,0x0,0x0],[0x2,0xb,0x0,0x0,0x5,0x9,0x0,0xb,0x5,0x5,0xb,0x7,-0x1,0x0,0x0,0x0],[0x9,0x8,0x3,0x9,0x3,0x5,0x5,0x3,0x2,0x5,0x2,0xb,0x5,0xb,0x7,-0x1],[0xa,0x2,0x3,0xa,0x3,0x5,0x5,0x3,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x5,0xa,0x7,0x7,0x0,0x8,0xa,0x0,0x7,0xa,0x2,0x0,-0x1,0x0,0x0,0x0],[0x1,0x9,0x0,0xa,0x2,0x3,0xa,0x3,0x5,0x5,0x3,0x7,-0x1,0x0,0x0,0x0],[0x7,0x5,0xa,0x7,0xa,0x8,0x8,0xa,0x2,0x8,0x2,0x1,0x8,0x1,0x9,-0x1],[0x7,0x5,0x1,0x7,0x1,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x1,0x0,0x8,0x7,0x1,0x1,0x7,0x5,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x5,0x9,0x0,0x3,0x5,0x5,0x3,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x7,0x5,0x9,0x7,0x9,0x8,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x5,0xa,0x4,0xa,0x8,0x8,0xa,0xb,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0xb,0x3,0xa,0xa,0x4,0x5,0xa,0x3,0x4,0x4,0x3,0x0,-0x1,0x0,0x0,0x0],[0x9,0x0,0x1,0x4,0x5,0xa,0x4,0xa,0x8,0x8,0xa,0xb,-0x1,0x0,0x0,0x0],[0x3,0x1,0x9,0x3,0x9,0xb,0xb,0x9,0x4,0xb,0x4,0x5,0xb,0x5,0xa,-0x1],[0x8,0x4,0xb,0xb,0x1,0x2,0x4,0x1,0xb,0x4,0x5,0x1,-0x1,0x0,0x0,0x0],[0x5,0x1,0x2,0x5,0x2,0x4,0x4,0x2,0xb,0x4,0xb,0x3,0x4,0x3,0x0,-0x1],[0xb,0x8,0x4,0xb,0x4,0x2,0x2,0x4,0x5,0x2,0x5,0x9,0x2,0x9,0x0,-0x1],[0x2,0xb,0x3,0x5,0x9,0x4,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x5,0xa,0x4,0xa,0x2,0x4,0x2,0x8,0x8,0x2,0x3,-0x1,0x0,0x0,0x0],[0xa,0x4,0x5,0xa,0x2,0x4,0x4,0x2,0x0,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x1,0x9,0x8,0x2,0x3,0x8,0x4,0x2,0x2,0x4,0xa,0xa,0x4,0x5,-0x1],[0xa,0x2,0x5,0x2,0x4,0x5,0x2,0x9,0x4,0x2,0x1,0x9,-0x1,0x0,0x0,0x0],[0x4,0x3,0x8,0x4,0x5,0x3,0x3,0x5,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x4,0x5,0x0,0x5,0x1,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x3,0x9,0x3,0x5,0x9,0x3,0x4,0x5,0x3,0x8,0x4,-0x1,0x0,0x0,0x0],[0x4,0x5,0x9,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x7,0x4,0x9,0x7,0x9,0xb,0xb,0x9,0xa,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x3,0x0,0x7,0x4,0x9,0x7,0x9,0xb,0xb,0x9,0xa,-0x1,0x0,0x0,0x0],[0x0,0x1,0x4,0x4,0xb,0x7,0x1,0xb,0x4,0x1,0xa,0xb,-0x1,0x0,0x0,0x0],[0xa,0xb,0x7,0xa,0x7,0x1,0x1,0x7,0x4,0x1,0x4,0x8,0x1,0x8,0x3,-0x1],[0x2,0xb,0x7,0x2,0x7,0x4,0x2,0x4,0x1,0x1,0x4,0x9,-0x1,0x0,0x0,0x0],[0x0,0x8,0x3,0x1,0x4,0x9,0x1,0x2,0x4,0x4,0x2,0x7,0x7,0x2,0xb,-0x1],[0x7,0x2,0xb,0x7,0x4,0x2,0x2,0x4,0x0,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x7,0x4,0xb,0x4,0x2,0xb,0x4,0x3,0x2,0x4,0x8,0x3,-0x1,0x0,0x0,0x0],[0x7,0x4,0x3,0x3,0xa,0x2,0x3,0x4,0xa,0xa,0x4,0x9,-0x1,0x0,0x0,0x0],[0x2,0x0,0x8,0x2,0x8,0xa,0xa,0x8,0x7,0xa,0x7,0x4,0xa,0x4,0x9,-0x1],[0x4,0x0,0x1,0x4,0x1,0x7,0x7,0x1,0xa,0x7,0xa,0x2,0x7,0x2,0x3,-0x1],[0x4,0x8,0x7,0x1,0xa,0x2,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x9,0x7,0x4,0x9,0x1,0x7,0x7,0x1,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x7,0x0,0x7,0x1,0x0,0x7,0x9,0x1,0x7,0x4,0x9,-0x1,0x0,0x0,0x0],[0x4,0x0,0x3,0x4,0x3,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x4,0x8,0x7,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x9,0xa,0x8,0xa,0xb,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0xb,0x3,0x0,0x9,0xb,0xb,0x9,0xa,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x1,0x8,0x0,0x1,0xa,0x8,0x8,0xa,0xb,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x3,0x1,0xa,0x3,0xa,0xb,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x2,0x9,0x1,0x2,0xb,0x9,0x9,0xb,0x8,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x9,0x3,0x9,0xb,0x3,0x9,0x2,0xb,0x9,0x1,0x2,-0x1,0x0,0x0,0x0],[0xb,0x8,0x0,0xb,0x0,0x2,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x2,0xb,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x3,0xa,0x2,0x3,0x8,0xa,0xa,0x8,0x9,-0x1,0x0,0x0,0x0,0x0,0x0,0x0],[0x9,0xa,0x2,0x9,0x2,0x0,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x3,0x8,0x2,0x8,0xa,0x2,0x8,0x1,0xa,0x8,0x0,0x1,-0x1,0x0,0x0,0x0],[0x2,0x1,0xa,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x8,0x9,0x1,0x8,0x1,0x3,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x1,0x0,0x9,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[0x0,0x3,0x8,-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],[-0x1,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0]],edge_vertices=[[0x0,0x1],[0x1,0x2],[0x2,0x3],[0x3,0x0],[0x4,0x5],[0x6,0x5],[0x6,0x7],[0x7,0x4],[0x0,0x4],[0x1,0x5],[0x2,0x6],[0x3,0x7]],index_to_vertex=[[0x0,0x0,0x0],[0x1,0x0,0x0],[0x1,0x1,0x0],[0x0,0x1,0x0],[0x0,0x0,0x1],[0x1,0x0,0x1],[0x1,0x1,0x1],[0x0,0x1,0x1]];function isUndef(g,X){return g===X;}var scale,offset,readFuncNoScale=g=>{return g;},readFuncScaleFirst=g=>{return g*scale+offset;},readFuncOffsetFirst=g=>{return(g+offset)*scale;},readFunc,computeVertexValues=function(g,X,u,f,j,I,V,e,D){var T=E;for(var S=0x0;S<index_to_vertex[T(0x1b6)];++S){var k=index_to_vertex[S],F=((u[0x2]+k[0x2])*X[0x1]+u[0x1]+k[0x1])*X[0x0]+u[0x0]+k[0x0];f[S]=g[F],isUndef(f[S],e)?f[S]=D:f[S]=readFunc(f[S]);};},lerpVerts=function(g,X,u,f,j,I){var L=E,V=0x0;Math[L(0x1bc)](u-f)<0.0001?V=0x0:V=(j-u)/(f-u),I[0x0]=g[0x0]+V*(X[0x0]-g[0x0]),I[0x1]=g[0x1]+V*(X[0x1]-g[0x1]),I[0x2]=g[0x2]+V*(X[0x2]-g[0x2]);},marchingCubesJS=function(g,X,u,f,I,V,e,D){var p=E;f=f||0x1,I=I||0x0,V=V===undefined?!![]:V,e=e===undefined?0xf423f:e,D=D===undefined?0x0:D;var S=[],F=[0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0],r=[0x0,0x0,0x0];for(var a=0x0;a<X[0x2]-0x1;++a){for(var q=0x0;q<X[0x1]-0x1;++q){for(var N=0x0;N<X[0x0]-0x1;++N){computeVertexValues(g,X,[N,q,a],F,f,I,V,e,D);var o=0x0;for(var C=0x0;C<0x8;++C){F[C]<=u&&(o|=0x1<<C);}for(var Z=0x0;triTable[o][Z]!=-0x1;++Z){var O=edge_vertices[triTable[o][Z]][0x0],Q=edge_vertices[triTable[o][Z]][0x1];lerpVerts(index_to_vertex[O],index_to_vertex[Q],F[O],F[Q],u,r),r[0x0]+=N+0.5,r[0x1]+=q+0.5,r[0x2]+=a+0.5,S[p(0x1ac)](r[0x0],r[0x1],r[0x2]);}}}}return new Float32Array(S);},getArr=function(g,X){var x=E;switch(X){case 0x1:return new Uint8Array(g);case 0x0:return new Int8Array(g);case 0x3:return new Uint16Array(g);case 0x2:return new Int16Array(g);case 0x5:return new Uint32Array(g);case 0x4:return new Int32Array(g);case 0x6:return new Float32Array(g);case 0x7:return new Float64Array(g);default:throw new Error('not\x20supported\x20grid\x20data\x20type\x20'+this[x(0x1ab)]);}};function d(){var K=['noShared','7810365qtvzlu','outerScale','2333430tbEjju','length','now','buffer','9505upoJrs','xSize','outerOffset','abs','raw','4wpmYJD','6550900AyDgqH','530576cpzrKh','undef','2971941XdQSLh','63KugWel','data','dataType','push','scale','scaleFirst','offset','469734TceodZ','hasOuter'];d=function(){return K;};return d();}onmessage=g=>{var w=E;const X=performance[w(0x1b7)]();var u=g[w(0x1c4)],f=getArr(u[w(0x1bd)],u['dataType']),j=u['levels'],I=u[w(0x1ba)],V=u['ySize'];scale=u[w(0x1ad)]===undefined?0x1:u[w(0x1ad)],offset=u[w(0x1af)]===undefined?0x0:u[w(0x1af)];var e=u[w(0x1ae)]===undefined?!![]:u[w(0x1ae)],D=u[w(0x1b1)];D&&(scale=u['outerScale']===undefined?0x1:u[w(0x1b4)],offset=u[w(0x1bb)]===undefined?0x0:u[w(0x1bb)]);if(scale===0x1&&offset===0x0)readFunc=readFuncNoScale;else e?readFunc=readFuncScaleFirst:readFunc=readFuncOffsetFirst;var S=u[w(0x1c1)],k=u['undef_fill']||0x0,F=u['anaValue'],r=marchingCubesJS(f,[I,V,j],F,scale,offset,e,S,k);u[w(0x1b2)]?postMessage({'anaValue':F,'vertices':r[w(0x1b8)],'source':u[w(0x1bd)]},[r['buffer'],u[w(0x1bd)]]):postMessage({'anaValue':F,'vertices':r[w(0x1b8)]},[r['buffer']]);};
