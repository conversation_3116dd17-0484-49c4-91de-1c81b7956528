<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0" />
    <meta name="author" content="火星科技 http://mars3d.cn " />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="x5-fullscreen" content="true" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <!-- 标题及搜索关键字 -->
    <meta name="keywords" content="火星科技,cesium,3D,GIS,marsgis,三维,地球,地图,开发,框架,系统,示例,资料,模型,离线,外包,合肥,安徽,中国" />
    <meta
      name="description"
      content="火星科技 合肥火星 合肥火星科技 合肥火星科技有限公司 leaflet leaflet框架 leaflet开发 cesium cesium开发 cesium框架 三维 地球 模型  gis marsgis 地图离线 地图开发 地图框架 地图外包 框架 开发 外包  地图离线 二维地图 三维地图 全景漫游 地理信息系统 云GIS 三维GIS GIS平台 WebGIS"
    />

    <link rel="shortcut icon" type="image/x-icon" href="/img/favicon/favicon.ico" />
    <title>图层管理 | Mars3D | 三维地图 | 火星科技 | 合肥火星科技有限公司</title>

    <!--第三方lib-->
    <script
      type="text/javascript"
      src="/lib/include-lib.js"
      libpath="/lib/"
      include="jquery,layer,toastr,font-awesome,ztree,bootstrap,layer,haoutil,mars3d"
    ></script>

    <link href="/css/style.css" rel="stylesheet" />
  </head>

  <body class="dark">
    <div id="mars3dContainer" class="mars3d-container"></div>
    <!-- 面板 -->
    <div class="infoview" style="overflow: auto; top: 5px; height: calc(100% - 90px)">
      <ul id="treeOverlays" class="ztree"></ul>
    </div>

    <script src="/js/common.js"></script>
    <script src="./map.js"></script>

    <script type="text/javascript">
      "use script" //开发环境建议开启严格模式

      function initUI(options) {
        initTree()
      }

      //===========================树控件处理============================
      function initTree() {
        const result = getLayrsTree({
          basemaps: true, // 是否取config.json中的basempas
          filter: function (layer) {
            if (!layer.name) {
              console.log("未命名图层不加入图层管理", layer)
              return false // 未命名图层不在管理器展示
            }
            return true
          },
          forEach: function (item, layer) {
            let node = {
              id: item.id,
              pId: item.pid ?? -1,
              name: item.name,
              checked: item.show
            }

            if (item.group) {
              node.icon = item.layers?.length > 0 ? "/lib/jquery/ztree/css/mars/images/layerGroup.png" : "/lib/jquery/ztree/css/mars/images/folder.png"
              node.open = item.open !== false
            } else {
              node.icon = "/lib/jquery/ztree/css/mars/images/layer.png"
            }
            return node
          }
        })
        console.log("图层树信息", result)

        //初始化树
        let setting = {
          check: {
            enable: true
          },
          data: {
            simpleData: {
              enable: true
            }
          },
          callback: {
            onCheck: treeOverlays_onCheck,
            onDblClick: treeOverlays_onDblClick
          }
        }
        $.fn.zTree.init($("#treeOverlays"), setting, result.list)
      }

      function treeOverlays_onCheck(e, treeId, chktreeNode) {
        let treeObj = $.fn.zTree.getZTreeObj(treeId)
        //获得所有改变check状态的节点
        let changedNodes = treeObj.getChangeCheckedNodes()

        haoutil.array.remove(changedNodes, chktreeNode)
        changedNodes.push(chktreeNode)

        for (let i = changedNodes.length - 1; i >= 0; i--) {
          let treeNode = changedNodes[i]
          treeNode.checkedOld = treeNode.checked

          if (treeNode.check_Child_State == 1) {
            // 0:无子节点被勾选,  1:部分子节点被勾选,  2:全部子节点被勾选, -1:不存在子节点 或 子节点全部设置为 nocheck = true
            continue
          }

          let layer = getLayerById(treeNode.id)
          if (layer == null) {
            continue
          }

          //显示隐藏透明度设置view
          if (treeNode.checked) {
            $("#" + treeNode.tId + "_range").show()
          } else {
            $("#" + treeNode.tId + "_range").hide()
          }

          updateLayerShow(layer, treeNode.checked)
        }
      }

      function treeOverlays_onDblClick(event, treeId, treeNode) {
        if (treeNode == null || treeNode.id == null) {
          return
        }
        let layer = getLayerById(treeNode.id)
        if (layer == null) {
          return
        }
        layer.flyTo()
      }
    </script>
  </body>
</html>
