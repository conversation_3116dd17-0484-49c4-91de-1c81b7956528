declare module "@quickearth/cesium" {

    // Generated by dts-bundle v0.7.3
// Dependencies for this module:
//   ../../@quickearth/core
//   ../../../../public/libs/cesium/c.d.ts

//@ts-ignore
import { IImageTileRule, IMap, MapToolService, IBatchLayerConfig, IConfigCreatableLayer } from "@quickearth/core";
//@ts-ignore
import { EasingFunction, Event, PrimitiveCollection, Viewer } from "cesium";
//@ts-ignore
import { Cartesian4, Matrix4 } from "cesium";
//@ts-ignore
import { IPixelLayerStyle3DOptions, PixelLayerStyle3D, IGridDataOptions, IGridDataProvider, GridGLBaseStyle } from "@quickearth/core";
//@ts-ignore
import { Event, PrimitiveCollection } from "cesium";
//@ts-ignore
import { CollisionDetectMode, IConfigCreatableLayer, IFeaturesProvider } from "@quickearth/core";
//@ts-ignore
import { FeatureStyle3D, IFeatureStyle3DOptions } from "@quickearth/core";
//@ts-ignore
import { Cartesian2, Cartesian4, Matrix4 } from "cesium";
//@ts-ignore
import { IPixelLayerStyle3DOptions, PixelLayerStyle3D, IGridDataProvider, GridGLBaseStyle } from "@quickearth/core";
//@ts-ignore
import { Camera, Cartesian4 } from "cesium";
//@ts-ignore
import { GridGLBaseStyle, IGridDataProvider, IVolumeLayerStyleOptions, VolumeLayerStyle } from '@quickearth/core';
//@ts-ignore
import { IWind3DLayerStyleOptions, Wind3DLayerStyle, IWindDataProvider, IGridDataProvider, GridGLBaseStyle } from "@quickearth/core";
//@ts-ignore
import { Cartesian4 } from "cesium";
//@ts-ignore
import { IGeometry3DLayerStyleOptions, Geometry3DLayerStyle, IGridDataProvider, GridGLBaseStyle } from "@quickearth/core";
//@ts-ignore
import { IWindArrowLayerStyleOptions, IWindDataProvider, IFeaturesProvider, WindArrowLayerStyle, GridGLBaseStyle } from "@quickearth/core";
//@ts-ignore
import { IPixelLayerStyleOptions, PixelLayerStyle, GridGLBaseStyle } from "@quickearth/core";
//@ts-ignore
import { IGridDataProvider } from "@quickearth/core";
//@ts-ignore
import { IPointImageStyle3DOptions, IFeaturesProvider, PointImageStyle3D, GridGLBaseStyle, StyleBaseClass } from "@quickearth/core";
//@ts-ignore
import { IGridDataProvider, IFeaturesProvider, ICSStyleOptions } from "@quickearth/core";
//@ts-ignore
import { CSStyle3D } from "@quickearth/core";
//@ts-ignore
import { TracingService } from "@quickearth/core";
//@ts-ignore
import { Color, ImageryProvider, Primitive } from "cesium";
//@ts-ignore
import { Spectra, IGeometry3DLayerStyleOptions, IMeshDataProvider, IGridDataProvider, IVolumeLayerStyleOptions } from "@quickearth/core";
//@ts-ignore
import { Texture } from "cesium";
//@ts-ignore
import { IAnimationableLayer, IConfigCreatableLayer, GridData, IGridDataProvider } from "@quickearth/core";
//@ts-ignore
import { IAnimationableLayer, IConfigCreatableLayer, GridData, IGridDataProvider, GridGLBaseStyle } from "@quickearth/core";
//@ts-ignore
import { Appearance, Cartesian4, Command, GeometryInstance, Matrix4, Primitive, ShadowMode, Texture, VertexArray } from "cesium";
//@ts-ignore
import { IGridDataProvider, ILayerDataConfig, IAnimationableLayer, IConfigCreatableLayer, StyleBaseClass, StopRules, GridGLBaseStyle } from "@quickearth/core";

/**
    * Cesium视图构建对象
    *
    * @export
    * @interface ICViewOptions
    * @extends {Viewer.ConstructorOptions}
    */
export interface ICViewOptions extends Viewer.ConstructorOptions {
        /**
            *
            * 初始化时的视野范围
            * @type {{
            *         west: number,
            *         south: number,
            *         east: number,
            *         north: number
            *     }}
            * @memberof ICViewOptions
            */
        defaultRect?: {
                west: number;
                south: number;
                east: number;
                north: number;
        };
        /**
            * 默认的切片背景，可以从[predefinedImageTiles]中进行选取
            *
            * @type {((string | IImageTileRule)[])}
            * @memberof ICViewOptions
            */
        defaultTiles?: (string | IImageTileRule)[];
        /**
            * 是否增加大气效果，如果显示，则缩小后地图颜色会减淡，默认关闭
            *
            * @type {boolean}
            * @memberof ICViewOptions
            */
        showGroundAtmosphere?: boolean;
        /**
            * 是否监听摄像机的高度显著变化。默认监听。
            * 如果不需要对高度发生变化后进行响应可以关闭，如果使用了矢量图层（CGeoJSONLayer）中的避免遮盖（avoidCollision）功能，则需要打开，否则遮盖功能会出现异常。
            *
            * @type {boolean}
            * @memberof ICViewOptions
            */
        handleCameraChange?: boolean;
        /**
            * 是否显示更高清的星空图，默认false
            */
        betterStars?: boolean;
        /**
            * 使用的地形服务。支持cesium和中科星图（可以通过preDeinfedImageTileTokens.zkxt来设置token），不设置则使用cesium默认值。
            *
            * 如果不设置，则可以自行设置terrainProvider。
            */
        defaultTerrain?: "cesium" | "zkxt";
}
/**
    * 基于Cesium Viewer的视图
    *
    * @export
    * @class CView
    * @extends {Viewer}
    * @implements {IMap}
    */
export class CView extends Viewer implements IMap {
        /**
            * 默认参数
            *
            * @static
            * @type {ICViewOptions}
            * @memberof CView
            */
        static DefaultOptions: ICViewOptions;
        toolService: MapToolService;
        /**
            * 等值线图层容器
            *
            * @type {PrimitiveCollection}
            * @memberof CView
            */
        contourLayers: PrimitiveCollection;
        /**
            * 栅格图层容器
            *
            * @type {PrimitiveCollection}
            * @memberof CView
            */
        gridLayers: PrimitiveCollection;
        /**
            * 背景图层容器
            *
            * @type {PrimitiveCollection}
            * @memberof CView
            */
        baseMapLayers: PrimitiveCollection;
        /**
            * 顶层地图容器
            *
            * @type {PrimitiveCollection}
            * @memberof CView
            */
        topMapLayers: PrimitiveCollection;
        /**
            * 标签图层容器
            *
            * @type {PrimitiveCollection}
            * @memberof CView
            */
        markerLayers: PrimitiveCollection;
        /**
            *站点图层容器
            *
            * @type {PrimitiveCollection}
            * @memberof CView
            */
        featureLayers: PrimitiveCollection;
        /**
            * 顶层图层容器
            *
            * @type {PrimitiveCollection}
            * @memberof CView
            */
        topMostLayers: PrimitiveCollection;
        /**
            * 地表图层容器
            *
            * @type {PrimitiveCollection}
            * @memberof CView
            */
        groundLayers: PrimitiveCollection;
        /**
            * 摄像机高度发生显著变化（超过10000米）的事件
            *
            * @type {Event}
            * @memberof CView
            */
        cameraHeightChanged: Event;
        protected options: ICViewOptions;
        /**
            * 构建CView
            * @param {(string | Element)} container 容器对象
            * @param {ICViewOptions} [options] 构建参数
            * @memberof CView
            */
        constructor(container: string | Element, options?: ICViewOptions);
        lookAt(lon: number, lat: number, height?: number, options?: {
                heading?: number;
                pitch?: number;
        }): void;
        /**
            * async版本的camera.flyTo，简化了传值方式
            * @param lon 经度
            * @param lat 纬度
            * @param height 高度，单位是米
            * @param options
            * @returns
            */
        goto(lon: number, lat: number, height?: number, options?: {
                heading?: number;
                pitch?: number;
                duration?: number;
                easing?: EasingFunction.Callback;
        }): Promise<void>;
        getCameraHeight(): number;
        get cameraHeight(): number;
        setView(lat: any, lon: any, opt_zoom: any, opt_altitude: any, opt_heading: any, opt_tilt: any): void;
        getZoom(): number;
        getCenterLonLat(): number[];
        addLayerByConfig(config: IBatchLayerConfig): Promise<IConfigCreatableLayer[]>;
}

/**
    * Cesium的填色图构建参数
    *
    * @export
    * @interface ICPixelLayerOptions
    * @extends {IC2DLayerOptions}
    */
export interface ICPixelLayerOptions extends IC2DLayerOptions {
        indices?: Uint32Array;
        ids?: Float32Array;
}
/**
    * Cesium的填色图层
    *
    * @export
    * @class CPixelLayer
    * @extends {C2DLayer<IPixelLayerStyle3DOptions>}
    */
export class CPixelLayer extends C2DLayer<IPixelLayerStyle3DOptions> {
        /**
            * 默认参数
            *
            * @static
            * @type {ICPixelLayerOptions}
            * @memberof CPixelLayer
            */
        static DefaultOptions: ICPixelLayerOptions;
        protected fillColors: Cartesian4[];
        protected fillSteps: number[];
        protected drawOptions: PixelLayerStyle3D;
        protected splineMat: Matrix4;
        protected options: ICPixelLayerOptions;
        /**
            * 构建图层
            * @param {ICPixelLayerOptions} options 构建参数
            * @memberof CPixelLayer
            */
        constructor(options?: ICPixelLayerOptions);
        /**
            * 设置绘制参数
            * @param options 绘制参数
            * @param partialUpdate 是否局部更新，默认true
            * @returns
            */
        setDrawOptions(options: string | IPixelLayerStyle3DOptions | PixelLayerStyle3D, partialUpdate?: boolean): this;
        /**
            * 设置实际渲染的格点属性。主要在提供了高程信息做贴地时，匹配高程分辨率实现好的贴地效果。
            * @param options
            * @returns
            */
        setTargetGridOptions(options: IGridDataOptions): this;
        setDataSource(dataSource: IGridDataProvider): this;
        protected resetCommand(resetVao?: boolean): void;
        /**
            * 系统函数，请勿框架外部调用
            * @param frameState
            */
        update(frameState?: any): void;
        destroy(): void;
        protected _createStyleObjectFromStyleOptions(options: IPixelLayerStyle3DOptions): GridGLBaseStyle<IPixelLayerStyle3DOptions>;
}

/**
    * Cesium中PrimitiveCollection的构建参数
    *
    * @export
    * @interface IPrimitiveCollectionsOptions
    */
export interface IPrimitiveCollectionsOptions {
        /**
            * 是否显示
            *
            * @type {boolean}
            * @memberof IPrimitiveCollectionsOptions
            */
        show?: boolean;
        /**
            * 是否在图元被移除的时候销毁图元
            *
            * @type {boolean}
            * @memberof IPrimitiveCollectionsOptions
            */
        destroyPrimitives?: boolean;
}
/**
    * Cesium下矢量图层的构造参数
    *
    * @export
    * @interface ICGeoJSONLayerOptions
    * @extends {IPrimitiveCollectionsOptions}
    */
export interface ICGeoJSONLayerOptions extends IPrimitiveCollectionsOptions {
        /**
            *
            * 是否开启调试模式下的信息显示。如果为false，即使处于调试模式也不会显示相关调试信息。默认为false。
            * @type {boolean}
            * @memberof ICGeoJSONLayerOptions
            */
        debugShowPerformance?: boolean;
        /**
            *
            * 是否缓存图层样式。如果是每次刷新都要更新样式，请设置为false。默认为true。
            * @type {boolean}
            * @memberof ICGeoJSONLayerOptions
            */
        cacheDrawOptions?: boolean;
        /**
         *
         * 是否监听数据源的更新消息，如果监听则更新后自动触发重绘。默认为false。
         * @type {boolean}
         * @memberof ICGeoJSONLayerOptions
         */
        trackDataSource?: boolean;
        lineTop?: boolean;
        name?: string;
        view?: CView;
        handleResize?: boolean;
        /**
            * 是否在碰撞检测之前（如果在样式中开启了）预先创建所有对象
            * 预先创建会消耗较多时间，但是后续检测会加速，否则每次检测完成后会重新创建对象，每次消耗的时间类似，第一次加载会比较快。
            * 默认预先创建，当存在大量文本的时候建议更换为每次创建。
            *
            * @type {boolean}
            * @memberof ICGeoJSONLayerOptions
            */
        collisionCreateAll?: boolean;
        /**
            * 碰撞检测模式，默认是仅隐藏实际被检测到的标签
            */
        collisionDetectMode?: CollisionDetectMode;
        /**
            *
            * 数据拾取的方式。默认为none，不进行数据拾取。
            * @type {("none" "all")}
            * @memberof ICGeoJSONLayerOptions
            */
        pickType?: "none" | "all";
        /**
            * 是否允许开启点的碰撞检测，默认不允许，此时样式中点的avoidCollision失效（标签的仍然生效）
            * 如果希望点本身也支持碰撞检测，请开启该选项，同时设置样式中的avoidCollision为true。
            */
        allowPointCollisionDetect?: boolean;
}
/**
    * Cesium三维矢量图层
    *
    * @export
    * @class CGeoJSONLayer
    * @extends {PrimitiveCollection}
    * @implements {IConfigCreatableLayer}
    */
export class CGeoJSONLayer extends PrimitiveCollection implements IConfigCreatableLayer {
        /**
            * 默认参数
            *
            * @static
            * @type {ICGeoJSONLayerOptions}
            * @memberof CGeoJSONLayer
            */
        static DefaultOptions: ICGeoJSONLayerOptions;
        /**
            * 拾取事件
            *
            * @type {Event}
            * @memberof CGeoJSONLayer
            */
        pickEvent: Event;
        protected options: ICGeoJSONLayerOptions;
        protected drawOptions: FeatureStyle3D;
        protected dataSource: IFeaturesProvider;
        /**
            * 构建CGeoJSONLayer
            * @param {ICGeoJSONLayerOptions} [options]
            * @memberof CGeoJSONLayer
            */
        constructor(options?: ICGeoJSONLayerOptions);
        /**
            * 设置渲染参数
            *
            * @param {(FeatureStyle | IFeatureStyleOptions | string)} drawOptions
            * @param {boolean} [partialUpdate=true] 是否部分更新参数。如果部分更新则未更新部分使用现有参数，否则全量构建行的样式对象。如果传入的已经是FeatureStyle对象，则该参数不生效，直接使用传入的参数。
            * @return {*}  {this}
            * @memberof LGeoJSONLayer
            */
        setDrawOptions(drawOptions: FeatureStyle3D | IFeatureStyle3DOptions | string, partialUpdate?: boolean): this;
        /**
            * 设置数据源
            *
            * @param {IFeaturesProvider} dataSource 数据源
            * @return {*}  {this}
            * @memberof LGeoJSONLayer
            */
        setDataSource(dataSource: IFeaturesProvider): this;
        setVisible(visible: boolean): this;
        isVisible(): boolean;
        protected handlePick: (movement: any) => void;
        protected resetCollisionObjects: () => void;
        protected redraw(): any;
        protected redrawFromDataSourceTracking: () => void;
        protected _createStyleObjectFromStyleOptions(options: IFeatureStyle3DOptions): FeatureStyle3D;
        destroy(): void;
        update(frameState: any): void;
}

/**
    * Cesium的切面图填色构建参数
    *
    * @export
    * @interface ICSectionLayerOptions
    * @extends {IC3DLayerOptions}
    */
export interface ICSectionLayerOptions extends IC3DLayerOptions {
        /**
            * 固定平面，默认是经纬度平面，即做垂直剖面
            */
        fixedPlane?: CFixedPlane;
        /**
            * 剖面模式，默认单个剖面，可选多个剖面
            */
        sectionMode?: "single" | "multiple";
}
export enum CFixedPlane {
        /**
            * 固定经纬度平面，如下平面，剖面的y轴值高度，x在固定平面上，固定平面的x轴是lat方向（值是经度），y轴是lon方向（值是纬度）
            */
        lonLat = 0,
        /**
            * 固定经度和高度平面，如右平面，剖面的y轴是纬度，x在固定平面上，固定平面的x轴是lon方向（值是纬度），y轴是level
            */
        lonZ = 1,
        /**
            * 固定纬度和高度平面，如近平面，剖面的y轴是经度，x在固定平面上，固定平面的x轴是lat方向（值是经度），y轴是level
            */
        latZ = 2,
        /**
            * 采用固定剖面显示平面的方式，默认将经度方向（沿南北）的剖面显示在经度的最小值上，将纬度方向（沿东西）方向的剖面显示在纬度的最大值上
            * 可以通过setFixedLonLat来设置投影平面的经度和纬度
            * 该模式下仅支持显示经纬度直线上的剖面，不支持显示任意路径的剖面
            */
        fixedLonLat = 3
}
export interface ISectionPoint {
        x: number;
        y: number;
}
/**
    * Cesium的切面填色图层
    */
export class CSectionLayer extends C3DLayer<IPixelLayerStyle3DOptions> {
        /**
            * 默认参数
            *
            * @static
            * @type {ICSectionLayerOptions}
            * @memberof CPixelLayer
            */
        static DefaultOptions: ICSectionLayerOptions;
        protected options: ICSectionLayerOptions;
        protected fillColors: Cartesian4[];
        protected fillSteps: number[];
        protected drawOptions: PixelLayerStyle3D;
        protected splineMat: Matrix4;
        /**
            * 构建图层
            * @param {ICPixelLayerOptions} options 构建参数
            * @memberof CPixelLayer
            */
        constructor(options?: ICSectionLayerOptions);
        /**
            * 设置绘制参数
            * @param options 绘制参数
            * @param partialUpdate 是否局部更新，默认true
            * @returns
            */
        setDrawOptions(options: string | IPixelLayerStyle3DOptions | PixelLayerStyle3D, partialUpdate?: boolean): this;
        setDataSource(dataSource: IGridDataProvider): this;
        /**
            * 删除指定的剖面路径
            * @param key 要删除的剖面路径key，如果不传则清空
            */
        removeSectionPath(key?: string): void;
        /**
            * 设置剖面路径，默认是单剖面，每次会清楚现有剖面，如果构建图层时设置模式为多剖面，则每次会叠加新的剖面，可以手动调用removeSectionPath来进行删除旧的剖面
            * @param path 剖面路径点
            * @param fixedPlane 剖面平面，默认是以经纬度为底面，如果是多剖面模式，所有剖面的fixPlane都是相同，因此如果剖面点设置的不对，结果会异常
            * @param redraw 默认为true，如果在加入地图之前设置，可以传入false
            * @param key 剖面的唯一识别id，当sectionMode是multiple的时候，同一个key新的剖面会覆盖原来的剖面，single的时候该参数无效果
            * @param interpPath 是否度路径进行插值，通常如果是用户手绘的简单路径，需要插值出精细的点，如果是代码生成的路径本身点数已经精细，则设置为false，避免插值耗时太久
            * @returns
            */
        setSectionPath(path: ISectionPoint[] | number[][], fixedPlane?: CFixedPlane, redraw?: boolean, key?: string, interpPath?: boolean): this;
        setLatSection(lat?: number, key?: string): void;
        setLonSection(lon?: number, key?: string): void;
        protected resetCommand(resetVao?: boolean): void;
        /**
            * 设置固定位经纬度平面上的剖面位置
            * 该模式会使不规则路径失效
            * @param lon
            * @param lat
            * @param updateFixedPlane
            */
        setFixedLonLat(point?: Cartesian2): void;
        /**
            * 系统函数，请勿框架外部调用
            * @param frameState
            */
        update(frameState?: any): void;
        protected _createStyleObjectFromStyleOptions(options: IPixelLayerStyle3DOptions): GridGLBaseStyle<IPixelLayerStyle3DOptions>;
}

export interface ICVolumeLayerOptions extends IC3DLayerOptions {
    camera?: Camera;
}
export class CVolumeLayer extends C3DLayer<IVolumeLayerStyleOptions> {
    static DefaultOptions: ICVolumeLayerOptions;
    constructor(options: ICVolumeLayerOptions);
    protected options: ICVolumeLayerOptions;
    protected colors: Cartesian4[];
    protected steps: number[];
    protected drawOptions: VolumeLayerStyle;
    setDrawOptions(options: IVolumeLayerStyleOptions, partialUpdate?: boolean): this;
    setDataSource(dataSource: IGridDataProvider): this;
    update(frameState?: any): void;
    protected resetCommand(): void;
    protected _createStyleObjectFromStyleOptions(options: IVolumeLayerStyleOptions): GridGLBaseStyle<IVolumeLayerStyleOptions>;
}

/**
    *
    * @export
    * @interface ICWindLayerOptions
    * @extends {IC3DLayerOptions}
    */
export interface ICWindLayerOptions extends IC3DLayerOptions {
        /**
            * 是否在移动的时候禁止拖尾，默认true
            */
        disableFadeWhenMoving?: boolean;
        /**
            * 是否在移动的时候隐藏粒子，默认false
            */
        hideWhenMoving?: boolean;
}
export class CWindLayer extends C3DLayer<IWind3DLayerStyleOptions> {
        /**
            * 默认参数
            *
            * @static
            * @type {ICWindLayerOptions}
            * @memberof CWindLayer
            */
        static DefaultOptions: ICWindLayerOptions;
        protected fillColors: Cartesian4[];
        protected fillSteps: number[];
        protected drawOptions: Wind3DLayerStyle;
        protected splineMat: Matrix4;
        protected dataSource: IWindDataProvider;
        protected options: ICWindLayerOptions;
        /**
            * 构建图层
            * @param {ICPixelLayerOptions} options 构建参数
            * @memberof CPixelLayer
            */
        constructor(options?: ICWindLayerOptions);
        setDataSource(dataSource: IGridDataProvider): this;
        /**
            * 设置绘制参数
            * @param options 绘制参数
            * @param partialUpdate 是否局部更新，默认true
            * @returns
            */
        setDrawOptions(options: string | IWind3DLayerStyleOptions | Wind3DLayerStyle, partialUpdate?: boolean): this;
        protected resetCommand(resetVao?: boolean): void;
        /**
            * 系统函数，请勿框架外部调用
            * @param frameState
            */
        update(frameState?: any): void;
        destroy(): void;
        protected _createStyleObjectFromStyleOptions(options: IWind3DLayerStyleOptions): GridGLBaseStyle<IWind3DLayerStyleOptions>;
}

/**
    *
    * @export
    * @interface ICTubeLayerOptions
    * @extends {IC3DLayerOptions}
    */
export interface ICGeometryLayerOptions extends IC3DLayerOptions {
        vertices: Float32Array | Float32Array[];
        indices: Uint32Array | Uint16Array | Uint32Array[] | Uint16Array[];
        normals: Float32Array | Float32Array[];
        st?: Float32Array | Float32Array[];
        /**
            * 数值，表示具体的函数，此处提供数值将不使用dataSource中的数值，每个顶点对应一个数值
            */
        values: Float32Array | Float32Array[];
}
export class CGeometryLayer extends C3DLayer<IGeometry3DLayerStyleOptions> {
        /**
            * 默认参数
            *
            * @static
            * @type {ICWindLayerOptions}
            * @memberof CWindLayer
            */
        static DefaultOptions: ICGeometryLayerOptions;
        protected fillColors: Cartesian4[];
        protected fillSteps: number[];
        protected drawOptions: Geometry3DLayerStyle;
        protected dataSource: IGridDataProvider;
        protected options: ICGeometryLayerOptions;
        /**
            * 构建图层
            * @param {ICGeometryLayerOptions} options 构建参数
            * @memberof CGeometryLayer
            */
        constructor(options?: ICGeometryLayerOptions);
        /**
            * 设置绘制参数
            * @param options 绘制参数
            * @param partialUpdate 是否局部更新，默认true
            * @returns
            */
        setDrawOptions(options: string | IGeometry3DLayerStyleOptions | Geometry3DLayerStyle, partialUpdate?: boolean): this;
        setDataSource(dataSource: IGridDataProvider): this;
        protected resetCommand(resetVao?: boolean): void;
        /**
            * 系统函数，请勿框架外部调用
            * @param frameState
            */
        update(frameState?: any): void;
        destroy(): void;
        protected _createStyleObjectFromStyleOptions(options: IGeometry3DLayerStyleOptions): GridGLBaseStyle<IGeometry3DLayerStyleOptions>;
}

export interface ICWindArrowLayerOptions extends ICLayerOptions {
        /**
            * 是否将创建箭头的worker保留在缓存，如果需要频繁更新箭头形状，则建议设置为true，默认为false
            */
        preserveGeometryWorker?: boolean;
        /**
            * 当数据源是格点数据时，支持自定义显示点位置的生成
            *
            * 返回 [lon,lat,hgt]
            */
        pointGenerator?: (idx: number) => number[];
}
/**
    * 三维风矢量场，用于展示静态风向箭头，不支持基于数据源监听的动画
    */
export class CWindArrowLayer extends CLayer<IWindArrowLayerStyleOptions> {
        static DefaultOptions: ICWindArrowLayerOptions;
        constructor(options?: ICWindArrowLayerOptions);
        protected fillColors: Cartesian4[];
        protected fillSteps: number[];
        protected dataSource: IWindDataProvider | IFeaturesProvider;
        protected options: ICWindArrowLayerOptions;
        protected drawOptions: WindArrowLayerStyle;
        setDataSource(dataSource: any, forceUpdate?: boolean): this;
        /**
            * 设置绘制参数
            * @param options 绘制参数
            * @param partialUpdate 是否局部更新，默认true
            * @returns
            */
        setDrawOptions(options: string | IWindArrowLayerStyleOptions | WindArrowLayerStyle, partialUpdate?: boolean): this;
        protected resetCommand(resetVao?: boolean): void;
        /**
            * 系统函数，请勿框架外部调用
            * @param frameState
            */
        update(frameState?: any): void;
        destroy(): void;
        protected _createStyleObjectFromStyleOptions(options: IWindArrowLayerStyleOptions): GridGLBaseStyle<IWindArrowLayerStyleOptions>;
}

export interface ICGroundLayerOptions extends IC2DLayerOptions {
        /**
            * 是否关闭光照，默认true
            */
        flat?: boolean;
        /**
            * 生成网格的度数间隔，默认是格点分辨率中更细的一个，默认最小值不小于0.01，不大于1
            * 过于细致的网格会导致第一次渲染的时候加载时间较常
            * 只有在网格分辨率很大（如超过了1度）或者很小（如小于0.01度）的时候才需要手动设置
            */
        granularity?: number;
        /**
            * 是否贴于地表，默认是
            * 本图层仅支持二维数据显示，如果无需贴地，建议使用CPixelLayer以便支持三维数据
            */
        onGround?: boolean;
}
/**
    * 格点贴地图层
    */
export class CGroundLayer extends C2DLayer<IPixelLayerStyleOptions> {
        static DefaultOptions: ICGroundLayerOptions;
        constructor(options?: ICGroundLayerOptions);
        protected options: ICGroundLayerOptions;
        protected fillColors: Cartesian4[];
        protected fillSteps: number[];
        protected drawOptions: PixelLayerStyle;
        protected splineMat: number[];
        /**
            * 设置绘制参数
            * @param options 绘制参数
            * @param partialUpdate 是否局部更新，默认true
            * @returns
            */
        setDrawOptions(options: string | IPixelLayerStyleOptions | PixelLayerStyle, partialUpdate?: boolean): this;
        update(frameState?: any): void;
        setDataSource(dataSource: IGridDataProvider): this;
        protected _createStyleObjectFromStyleOptions(options: IPixelLayerStyleOptions): GridGLBaseStyle<IPixelLayerStyleOptions>;
}

/**
  * 三维点贴图图层
  * 该图层适用于大量空间点根据TextureMap进行贴图显示，少量点建议使用CGeoJSONLayer，功能更多，使用更灵活
  */
export class CPointImageLayer extends CLayer<IPointImageStyle3DOptions> {
    protected dataSource: IFeaturesProvider;
    protected drawOptions: PointImageStyle3D;
    setDataSource(dataSource: IFeaturesProvider): this;
    setDrawOptions(drawOptions: string | IPointImageStyle3DOptions | GridGLBaseStyle<IPointImageStyle3DOptions>, partialUpdate?: boolean): this;
    protected resetCommand(resetVao?: boolean): void;
    protected _createStyleObjectFromStyleOptions(options: IPointImageStyle3DOptions): StyleBaseClass<IPointImageStyle3DOptions>;
    /**
     * 系统函数，请勿框架外部调用
     * @param frameState
     */
    update(frameState?: any): void;
    destroy(): void;
}

/**
    * 等值线色斑图层构建参数，同时支持矢量和栅格数据源
    */
export interface ICCSLayerOptions extends ICGeoJSONLayerOptions {
        /**
            * 是否使用wasm追踪。wasm追踪等值线速度快，但是会有边框，可以通过设置mask屏蔽。
            * 默认false
            */
        wasm?: boolean;
}
/**
    * 等值线色斑图层。使用格点数据源，自动分析色斑和等值线，支持更新数据源
    */
export class CCSLayer extends CGeoJSONLayer {
        static DefaultOptions: ICCSLayerOptions;
        /**
            * 构建等值线色斑标签图层
            * @param options 构建参数
            */
        constructor(options?: ICCSLayerOptions);
        /**
            * 绘制参数
            */
        drawOptions: CSStyle3D;
        protected options: ICCSLayerOptions;
        protected originalSource: IGridDataProvider | IFeaturesProvider;
        setDrawOptions(drawOptions: ICSStyleOptions, partialUpdate?: boolean): this;
        setDataSource(dataSource: IGridDataProvider | IFeaturesProvider): this;
        protected redrawFromDataSourceTracking: () => void;
        protected _createStyleObjectFromStyleOptions(options: ICSStyleOptions): CSStyle3D;
        static qeName: string;
}

/**
    * Leaflet下的等值线、色斑图追踪服务
    *
    * @export
    * @class LTracingService
    * @extends {TracingService<CGeoJSONLayer, ICGeoJSONLayerOptions>}
    */
export class CTracingService extends TracingService<CGeoJSONLayer, ICGeoJSONLayerOptions> {
        /**
            * 构建 LTracingService
            * @memberof LTracingService
            */
        constructor();
}

/**
  * 将Spectra对象转为Cesium的颜色
  *
  * @export
  * @param {Spectra} color Spectra对象
  * @return {*}  {Color}
  */
export function cColor(color: Spectra): Color;
export function createCTileLayer(tileName: string): ImageryProvider;
export function mcbGeometryLayerCreator(drawOptions: IGeometry3DLayerStyleOptions, dataSource: IMeshDataProvider, source: IGridDataProvider): Primitive;
export function mcbLayerCreator(style: IVolumeLayerStyleOptions, dataSource: IMeshDataProvider): Primitive;

/**
    * Cesium二维格点基础图层构建参数
    *
    * @export
    * @interface IC2DLayerOptions
    */
export interface IC2DLayerOptions extends ICLayerOptions {
}
/**
    * 基于Cesium Primitive的二维图层抽象类。
    * 提供了数据texture生成、数据源监听后更新texture等功能
    *
    * @export
    * @abstract
    * @class C2DLayer
    * @extends {Primitive}
    */
export abstract class C2DLayer<TDrawOptions> extends CLayer<TDrawOptions> implements IAnimationableLayer, IConfigCreatableLayer {
        /**
            * 默认参数
            *
            * @static
            * @type {IC2DLayerOptions}
            * @memberof C2DLayer
            */
        static DefaultOptions: IC2DLayerOptions;
        protected options: IC2DLayerOptions;
        protected preTexture: Texture;
        protected preGrid: GridData;
        protected preTexture2: Texture;
        protected preGrid2: GridData;
        protected dataPercent: number;
        protected currentGrid: GridData;
        protected currentTexture: Texture;
        protected currentGrid2: GridData;
        protected currentTexture2: Texture;
        protected preHeight: number;
        protected currentHeight: number;
        protected dataSource: IGridDataProvider;
        /**
            * 构建图层实例
            * @param {IC2DLayerOptions} [options] 构建参数
            * @memberof C2DLayer
            */
        constructor(options?: IC2DLayerOptions);
        /**
            * 设置图层参数
            *
            * @param {IC2DLayerOptions} options 参数
            * @param {boolean} [partialUpdate=true] 是否部分更新，默认是
            * @return {*}
            * @memberof C2DLayer
            */
        setLayerOptions(options: IC2DLayerOptions, partialUpdate?: boolean): void;
        /**
                    * 设置数据源
                    *
                    * @param {IGridDataProvider} dataSource
                    * @return {*}  {this}
                    * @memberof C2DLayer
                    */
        setDataSource(dataSource: IGridDataProvider): this;
        /**
            *
            * 内部调用
            * @memberof C2DLayer
            */
        clearPreDataSource(): void;
        protected unsetDataSourceListener(): void;
        protected setDataSourceListener(): void;
        protected updateTDataPercent: () => void;
        protected updateZDataPercent: () => void;
        protected transferCurrentData: () => void;
}

export interface IC3DLayerOptions extends ICLayerOptions {
}
export abstract class C3DLayer<TDrawOptions> extends CLayer<TDrawOptions> implements IAnimationableLayer, IConfigCreatableLayer {
    /**
     * 默认参数
     *
     * @static
     * @type {IC3DLayerOptions}
     * @memberof C3DLayer
     */
    static DefaultOptions: IC3DLayerOptions;
    constructor(options?: IC3DLayerOptions);
    protected options: IC3DLayerOptions;
    protected currentGrids: GridData[];
    protected currentGrids2: GridData[];
    protected currentGrids3: GridData[];
    protected preGrids: GridData[];
    protected preGrids2: GridData[];
    protected preGrids3: GridData[];
    protected currentTexture: CesiumDataTexture3D;
    protected currentTexture2: CesiumDataTexture3D;
    protected currentTexture3: CesiumDataTexture3D;
    protected preTexture: CesiumDataTexture3D;
    protected preTexture2: CesiumDataTexture3D;
    protected preTexture3: CesiumDataTexture3D;
    protected dataSource: IGridDataProvider;
    protected dataPercent: number;
    protected zValuesWithScale: number[];
    protected resetCommand(resetVao?: boolean): void;
    protected setZScale(): void;
    setDrawOptions(drawOptions: string | TDrawOptions | GridGLBaseStyle<TDrawOptions>, partialUpdate?: boolean): this;
    protected unsetDataSourceListener(): void;
    protected updateTDataPercent: () => void;
    protected transferCurrentData: () => void;
    protected setDataSourceListener(): void;
    setLayerOptions(options: IC3DLayerOptions, partialUpdate?: boolean): void;
    setDataSource(dataSource: IGridDataProvider): this;
    clearPreDataSource(): void;
    update(frameState?: any): void;
}

export interface IPrimitiveLayerOptions {
        geometryInstances?: GeometryInstance[] | GeometryInstance;
        appearance?: Appearance;
        depthFailAppearance?: Appearance;
        show?: boolean;
        modelMatrix?: Matrix4;
        vertexCacheOptimize?: boolean;
        interleave?: boolean;
        compressVertices?: boolean;
        releaseGeometryInstances?: boolean;
        allowPicking?: boolean;
        cull?: boolean;
        asynchronous?: boolean;
        debugShowBoundingVolume?: boolean;
        shadows?: ShadowMode;
        id?: string;
}
export interface ICLayerOptions extends IPrimitiveLayerOptions {
        /**
         *
         * 是否跟踪数据源变化，如果是，会默认刷新，默认是
         * @type {boolean}
         * @memberof ICGridLayerOptions
         */
        trackDataSource?: boolean;
        /**
            *
            * 是否缓存绘制样式，默认是，如果是样式不断刷新的图层则不要缓存
            * @type {boolean}
            * @memberof ICGridLayerOptions
            */
        cacheDrawOptions?: boolean;
        /**
            *
            * 是否进行自动补帧，如果是，会记录上一次数据源，并根据当前数据源中对应的tz小数部分进行补帧
            * @type {boolean}
            * @memberof ICGridLayerOptions
            */
        interpFromPreSource?: boolean;
        /**
         *
         * 图层名称，默认为系统唯一id
         * @type {string}
         * @memberof ICGridLayerOptions
         */
        name?: string;
        /**
            *
            * 是否开启调试模式下的信息显示。如果为false，即使处于调试模式也不会显示相关调试信息。默认为false。
            * @type {boolean}
            * @memberof ICGridLayerOptions
            */
        debugShowPerformance?: boolean;
        appearance?: Appearance;
        /**
            *
            * 掩膜边界。可以是一个GeoJSON的多边形对象集合，也可以是保存这个集合的资源ID
            * @type {(string|object)}
            * @memberof IC2DLayerOptions
            */
        mask?: string | GeoJSON.GeoJsonObject;
        /**
            * 高度信息，可以是高度数组（格点属性需要与数据源一致），或者是数据源创建参数，或者是可以返回数据源创建参数的loader描述，如 u-500#res
            *
            * @type {(GridData|ILayerDataConfig|string)}
            * @memberof IC2DLayerOptions
            */
        heightData?: IGridDataProvider | ILayerDataConfig | string;
        /**
            * 高度贴图的缺测值，默认9999
            */
        heightUndef?: number;
}
export abstract class CLayer<TDrawOptions> extends Primitive implements IAnimationableLayer, IConfigCreatableLayer {
        /**
         * 默认参数
         *
         * @static
         * @type {ICLayerOptions}
         * @memberof CLayer
         */
        static DefaultOptions: ICLayerOptions;
        constructor(options?: ICLayerOptions);
        protected options: ICLayerOptions;
        protected sizeChanged: boolean;
        protected sizeChanging: boolean;
        protected resizeHandler: any;
        protected _visible: boolean;
        protected drawOptions: StyleBaseClass<TDrawOptions>;
        protected emptyTexture: {};
        protected maskCommand: Command;
        protected maskClearCommand: Command;
        protected maskTexture: Texture;
        protected maskDepthTexture: Texture;
        protected maskVao: VertexArray;
        protected heightTexture: Texture;
        protected dataSource: any;
        protected vectorGridMode: "sd" | "uv";
        protected parseNumberStopRules(sr: StopRules): {
                numbers: number[];
                steps: number[];
        };
        protected resetMaskCommand(): void;
        protected createMaskVao(context: any, attrLocations: any): any;
        protected createMaskCommand(context: any): void;
        /**
            * 设置图层参数
            *
            * @param {ICLayerOptions} options
            * @param {boolean} [partialUpdate=true]
            * @memberof CGridLayer
            */
        setLayerOptions(options: ICLayerOptions, partialUpdate?: boolean): void;
        /**
            * 设置图层数据源
            *
            * @param {IGridDataProvider} dataSource
            * @return {*}  {this}
            * @memberof CGridLayer
            */
        abstract setDataSource(dataSource: any): this;
        /**
            * 设置绘制参数
            *
            * @param {(string | TDrawOptions | GridGLBaseStyle<TDrawOptions>)} drawOptions
            * @param {boolean} [partialUpdate=true]
            * @return {*}  {this}
            * @memberof CGridLayer
            */
        setDrawOptions(drawOptions: string | TDrawOptions | GridGLBaseStyle<TDrawOptions>, partialUpdate?: boolean): this;
        protected abstract _createStyleObjectFromStyleOptions(options: TDrawOptions): StyleBaseClass<TDrawOptions>;
        protected parseColorStopRules(sr: StopRules): {
                colors: Cartesian4[];
                steps: number[];
        };
        protected handleSizeChanged(): void;
        setVisible(visible: boolean): this;
        isVisible(): boolean;
        clearPreDataSource(): void;
        protected resetCommand(resetVao?: boolean): void;
        /**
            * 内部调用
            *
            * @param {*} [frameState]
            * @return {*}
            * @memberof C2DLayer
            */
        update(frameState?: any): void;
        destroy(): void;
}

/**
    * Cesium三维贴图对象参数
    *
    * @export
    * @interface ICesiumDataTexture3DOptions
    */
export interface ICesiumDataTexture3DOptions {
        /**
            * 层次数
            *
            * @type {number}
            * @memberof ICesiumDataTexture3DOptions
            */
        depth: number;
        /**
            * 执行环境
            *
            * @type {*}
            * @memberof ICesiumDataTexture3DOptions
            */
        context: any;
        /**
            * 宽度
            *
            * @type {number}
            * @memberof ICesiumDataTexture3DOptions
            */
        width: number;
        /**
            * 高度
            *
            * @type {number}
            * @memberof ICesiumDataTexture3DOptions
            */
        height: number;
        /**
            * 数据源
            *
            * @type {*}
            * @memberof ICesiumDataTexture3DOptions
            */
        source?: any;
        /**
            * 像元格式
            *
            * @type {*}
            * @memberof ICesiumDataTexture3DOptions
            */
        pixelFormat?: any;
        /**
            * 像元数据类型
            *
            * @type {*}
            * @memberof ICesiumDataTexture3DOptions
            */
        pixelDatatype?: any;
        /**
            * 是否预乘透明度
            *
            * @type {boolean}
            * @memberof ICesiumDataTexture3DOptions
            */
        preMultiplyAlpha?: boolean;
        /**
            * 是否翻转Y轴
            *
            * @type {boolean}
            * @memberof ICesiumDataTexture3DOptions
            */
        flipY?: boolean;
        /**
            * 采样方式
            *
            * @type {*}
            * @memberof ICesiumDataTexture3DOptions
            */
        sampler?: any;
}
/**
    * Cesium的三维纹理贴图
    *
    * @export
    * @class CesiumDataTexture3D
    */
export class CesiumDataTexture3D {
        /**
            * 构建CesiumDataTexture3D
            * @param {ICesiumDataTexture3DOptions} options
            * @memberof CesiumDataTexture3D
            */
        constructor(options: ICesiumDataTexture3DOptions);
        get id(): any;
        get sampler(): any;
        set sampler(sampler: any);
        get pixelFormat(): any;
        get pixelDatatype(): any;
        get dimensions(): any;
        get preMultiplyAlpha(): any;
        get flipY(): any;
        get width(): any;
        get height(): any;
        get sizeInBytes(): any;
        get _target(): any;
        copyFrom: () => void;
        copyFromFramebuffer: () => void;
        generateMipmap: () => void;
        isDestroyed: () => boolean;
        destroy: () => void;
        static create(options: any): CesiumDataTexture3D;
}

    


}