!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).kgUtil={})}(this,(function(e){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function n(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var i,a={exports:{}};
/*!

	JSZip v3.10.1 - A JavaScript class for generating and reading zip files
	<http://stuartk.com/jszip>

	(c) 2009-2016 <PERSON> <stuart [at] stuartk.com>
	Dual licenced under the MIT license or GPLv3. See https://raw.github.com/Stuk/jszip/main/LICENSE.markdown.

	JSZip uses the library pako released under the MIT license :
	https://github.com/nodeca/pako/blob/main/LICENSE
	*/var s=(i||(i=1,function(e){e.exports=function e(t,r,i){function a(o,l){if(!r[o]){if(!t[o]){if(!l&&n)return n(o);if(s)return s(o,!0);var u=new Error("Cannot find module '"+o+"'");throw u.code="MODULE_NOT_FOUND",u}var h=r[o]={exports:{}};t[o][0].call(h.exports,(function(e){return a(t[o][1][e]||e)}),h,h.exports,e,t,r,i)}return r[o].exports}for(var s=n,o=0;o<i.length;o++)a(i[o]);return a}({1:[function(e,t,r){var n=e("./utils"),i=e("./support"),a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";r.encode=function(e){for(var t,r,i,s,o,l,u,h=[],c=0,f=e.length,d=f,p="string"!==n.getTypeOf(e);c<e.length;)d=f-c,i=p?(t=e[c++],r=c<f?e[c++]:0,c<f?e[c++]:0):(t=e.charCodeAt(c++),r=c<f?e.charCodeAt(c++):0,c<f?e.charCodeAt(c++):0),s=t>>2,o=(3&t)<<4|r>>4,l=1<d?(15&r)<<2|i>>6:64,u=2<d?63&i:64,h.push(a.charAt(s)+a.charAt(o)+a.charAt(l)+a.charAt(u));return h.join("")},r.decode=function(e){var t,r,n,s,o,l,u=0,h=0,c="data:";if(e.substr(0,c.length)===c)throw new Error("Invalid base64 input, it looks like a data url.");var f,d=3*(e=e.replace(/[^A-Za-z0-9+/=]/g,"")).length/4;if(e.charAt(e.length-1)===a.charAt(64)&&d--,e.charAt(e.length-2)===a.charAt(64)&&d--,d%1!=0)throw new Error("Invalid base64 input, bad content length.");for(f=i.uint8array?new Uint8Array(0|d):new Array(0|d);u<e.length;)t=a.indexOf(e.charAt(u++))<<2|(s=a.indexOf(e.charAt(u++)))>>4,r=(15&s)<<4|(o=a.indexOf(e.charAt(u++)))>>2,n=(3&o)<<6|(l=a.indexOf(e.charAt(u++))),f[h++]=t,64!==o&&(f[h++]=r),64!==l&&(f[h++]=n);return f}},{"./support":30,"./utils":32}],2:[function(e,t,r){var n=e("./external"),i=e("./stream/DataWorker"),a=e("./stream/Crc32Probe"),s=e("./stream/DataLengthProbe");function o(e,t,r,n,i){this.compressedSize=e,this.uncompressedSize=t,this.crc32=r,this.compression=n,this.compressedContent=i}o.prototype={getContentWorker:function(){var e=new i(n.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new s("data_length")),t=this;return e.on("end",(function(){if(this.streamInfo.data_length!==t.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")})),e},getCompressedWorker:function(){return new i(n.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},o.createWorkerFrom=function(e,t,r){return e.pipe(new a).pipe(new s("uncompressedSize")).pipe(t.compressWorker(r)).pipe(new s("compressedSize")).withStreamInfo("compression",t)},t.exports=o},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(e,t,r){var n=e("./stream/GenericWorker");r.STORE={magic:"\0\0",compressWorker:function(){return new n("STORE compression")},uncompressWorker:function(){return new n("STORE decompression")}},r.DEFLATE=e("./flate")},{"./flate":7,"./stream/GenericWorker":28}],4:[function(e,t,r){var n=e("./utils"),i=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();t.exports=function(e,t){return void 0!==e&&e.length?"string"!==n.getTypeOf(e)?function(e,t,r,n){var a=i,s=n+r;e^=-1;for(var o=n;o<s;o++)e=e>>>8^a[255&(e^t[o])];return~e}(0|t,e,e.length,0):function(e,t,r,n){var a=i,s=n+r;e^=-1;for(var o=n;o<s;o++)e=e>>>8^a[255&(e^t.charCodeAt(o))];return~e}(0|t,e,e.length,0):0}},{"./utils":32}],5:[function(e,t,r){r.base64=!1,r.binary=!1,r.dir=!1,r.createFolders=!0,r.date=null,r.compression=null,r.compressionOptions=null,r.comment=null,r.unixPermissions=null,r.dosPermissions=null},{}],6:[function(e,t,r){var n=null;n="undefined"!=typeof Promise?Promise:e("lie"),t.exports={Promise:n}},{lie:37}],7:[function(e,t,r){var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,i=e("pako"),a=e("./utils"),s=e("./stream/GenericWorker"),o=n?"uint8array":"array";function l(e,t){s.call(this,"FlateWorker/"+e),this._pako=null,this._pakoAction=e,this._pakoOptions=t,this.meta={}}r.magic="\b\0",a.inherits(l,s),l.prototype.processChunk=function(e){this.meta=e.meta,null===this._pako&&this._createPako(),this._pako.push(a.transformTo(o,e.data),!1)},l.prototype.flush=function(){s.prototype.flush.call(this),null===this._pako&&this._createPako(),this._pako.push([],!0)},l.prototype.cleanUp=function(){s.prototype.cleanUp.call(this),this._pako=null},l.prototype._createPako=function(){this._pako=new i[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var e=this;this._pako.onData=function(t){e.push({data:t,meta:e.meta})}},r.compressWorker=function(e){return new l("Deflate",e)},r.uncompressWorker=function(){return new l("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:38}],8:[function(e,t,r){function n(e,t){var r,n="";for(r=0;r<t;r++)n+=String.fromCharCode(255&e),e>>>=8;return n}function i(e,t,r,i,s,h){var c,f,d=e.file,p=e.compression,m=h!==o.utf8encode,_=a.transformTo("string",h(d.name)),g=a.transformTo("string",o.utf8encode(d.name)),y=d.comment,b=a.transformTo("string",h(y)),v=a.transformTo("string",o.utf8encode(y)),w=g.length!==d.name.length,k=v.length!==y.length,x="",S="",z="",C=d.dir,E=d.date,A={crc32:0,compressedSize:0,uncompressedSize:0};t&&!r||(A.crc32=e.crc32,A.compressedSize=e.compressedSize,A.uncompressedSize=e.uncompressedSize);var O=0;t&&(O|=8),m||!w&&!k||(O|=2048);var I=0,T=0;C&&(I|=16),"UNIX"===s?(T=798,I|=function(e,t){var r=e;return e||(r=t?16893:33204),(65535&r)<<16}(d.unixPermissions,C)):(T=20,I|=function(e){return 63&(e||0)}(d.dosPermissions)),c=E.getUTCHours(),c<<=6,c|=E.getUTCMinutes(),c<<=5,c|=E.getUTCSeconds()/2,f=E.getUTCFullYear()-1980,f<<=4,f|=E.getUTCMonth()+1,f<<=5,f|=E.getUTCDate(),w&&(S=n(1,1)+n(l(_),4)+g,x+="up"+n(S.length,2)+S),k&&(z=n(1,1)+n(l(b),4)+v,x+="uc"+n(z.length,2)+z);var B="";return B+="\n\0",B+=n(O,2),B+=p.magic,B+=n(c,2),B+=n(f,2),B+=n(A.crc32,4),B+=n(A.compressedSize,4),B+=n(A.uncompressedSize,4),B+=n(_.length,2),B+=n(x.length,2),{fileRecord:u.LOCAL_FILE_HEADER+B+_+x,dirRecord:u.CENTRAL_FILE_HEADER+n(T,2)+B+n(b.length,2)+"\0\0\0\0"+n(I,4)+n(i,4)+_+x+b}}var a=e("../utils"),s=e("../stream/GenericWorker"),o=e("../utf8"),l=e("../crc32"),u=e("../signature");function h(e,t,r,n){s.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=t,this.zipPlatform=r,this.encodeFileName=n,this.streamFiles=e,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}a.inherits(h,s),h.prototype.push=function(e){var t=e.meta.percent||0,r=this.entriesCount,n=this._sources.length;this.accumulate?this.contentBuffer.push(e):(this.bytesWritten+=e.data.length,s.prototype.push.call(this,{data:e.data,meta:{currentFile:this.currentFile,percent:r?(t+100*(r-n-1))/r:100}}))},h.prototype.openedSource=function(e){this.currentSourceOffset=this.bytesWritten,this.currentFile=e.file.name;var t=this.streamFiles&&!e.file.dir;if(t){var r=i(e,t,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:r.fileRecord,meta:{percent:0}})}else this.accumulate=!0},h.prototype.closedSource=function(e){this.accumulate=!1;var t=this.streamFiles&&!e.file.dir,r=i(e,t,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(r.dirRecord),t)this.push({data:function(e){return u.DATA_DESCRIPTOR+n(e.crc32,4)+n(e.compressedSize,4)+n(e.uncompressedSize,4)}(e),meta:{percent:100}});else for(this.push({data:r.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},h.prototype.flush=function(){for(var e=this.bytesWritten,t=0;t<this.dirRecords.length;t++)this.push({data:this.dirRecords[t],meta:{percent:100}});var r=this.bytesWritten-e,i=function(e,t,r,i,s){var o=a.transformTo("string",s(i));return u.CENTRAL_DIRECTORY_END+"\0\0\0\0"+n(e,2)+n(e,2)+n(t,4)+n(r,4)+n(o.length,2)+o}(this.dirRecords.length,r,e,this.zipComment,this.encodeFileName);this.push({data:i,meta:{percent:100}})},h.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},h.prototype.registerPrevious=function(e){this._sources.push(e);var t=this;return e.on("data",(function(e){t.processChunk(e)})),e.on("end",(function(){t.closedSource(t.previous.streamInfo),t._sources.length?t.prepareNextSource():t.end()})),e.on("error",(function(e){t.error(e)})),this},h.prototype.resume=function(){return!!s.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},h.prototype.error=function(e){var t=this._sources;if(!s.prototype.error.call(this,e))return!1;for(var r=0;r<t.length;r++)try{t[r].error(e)}catch(e){}return!0},h.prototype.lock=function(){s.prototype.lock.call(this);for(var e=this._sources,t=0;t<e.length;t++)e[t].lock()},t.exports=h},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(e,t,r){var n=e("../compressions"),i=e("./ZipFileWorker");r.generateWorker=function(e,t,r){var a=new i(t.streamFiles,r,t.platform,t.encodeFileName),s=0;try{e.forEach((function(e,r){s++;var i=function(e,t){var r=e||t,i=n[r];if(!i)throw new Error(r+" is not a valid compression method !");return i}(r.options.compression,t.compression),o=r.options.compressionOptions||t.compressionOptions||{},l=r.dir,u=r.date;r._compressWorker(i,o).withStreamInfo("file",{name:e,dir:l,date:u,comment:r.comment||"",unixPermissions:r.unixPermissions,dosPermissions:r.dosPermissions}).pipe(a)})),a.entriesCount=s}catch(e){a.error(e)}return a}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(e,t,r){function n(){if(!(this instanceof n))return new n;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files=Object.create(null),this.comment=null,this.root="",this.clone=function(){var e=new n;for(var t in this)"function"!=typeof this[t]&&(e[t]=this[t]);return e}}(n.prototype=e("./object")).loadAsync=e("./load"),n.support=e("./support"),n.defaults=e("./defaults"),n.version="3.10.1",n.loadAsync=function(e,t){return(new n).loadAsync(e,t)},n.external=e("./external"),t.exports=n},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(e,t,r){var n=e("./utils"),i=e("./external"),a=e("./utf8"),s=e("./zipEntries"),o=e("./stream/Crc32Probe"),l=e("./nodejsUtils");function u(e){return new i.Promise((function(t,r){var n=e.decompressed.getContentWorker().pipe(new o);n.on("error",(function(e){r(e)})).on("end",(function(){n.streamInfo.crc32!==e.decompressed.crc32?r(new Error("Corrupted zip : CRC32 mismatch")):t()})).resume()}))}t.exports=function(e,t){var r=this;return t=n.extend(t||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:a.utf8decode}),l.isNode&&l.isStream(e)?i.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):n.prepareContent("the loaded zip file",e,!0,t.optimizedBinaryString,t.base64).then((function(e){var r=new s(t);return r.load(e),r})).then((function(e){var r=[i.Promise.resolve(e)],n=e.files;if(t.checkCRC32)for(var a=0;a<n.length;a++)r.push(u(n[a]));return i.Promise.all(r)})).then((function(e){for(var i=e.shift(),a=i.files,s=0;s<a.length;s++){var o=a[s],l=o.fileNameStr,u=n.resolve(o.fileNameStr);r.file(u,o.decompressed,{binary:!0,optimizedBinaryString:!0,date:o.date,dir:o.dir,comment:o.fileCommentStr.length?o.fileCommentStr:null,unixPermissions:o.unixPermissions,dosPermissions:o.dosPermissions,createFolders:t.createFolders}),o.dir||(r.file(u).unsafeOriginalName=l)}return i.zipComment.length&&(r.comment=i.zipComment),r}))}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(e,t,r){var n=e("../utils"),i=e("../stream/GenericWorker");function a(e,t){i.call(this,"Nodejs stream input adapter for "+e),this._upstreamEnded=!1,this._bindStream(t)}n.inherits(a,i),a.prototype._bindStream=function(e){var t=this;(this._stream=e).pause(),e.on("data",(function(e){t.push({data:e,meta:{percent:0}})})).on("error",(function(e){t.isPaused?this.generatedError=e:t.error(e)})).on("end",(function(){t.isPaused?t._upstreamEnded=!0:t.end()}))},a.prototype.pause=function(){return!!i.prototype.pause.call(this)&&(this._stream.pause(),!0)},a.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},t.exports=a},{"../stream/GenericWorker":28,"../utils":32}],13:[function(e,t,r){var n=e("readable-stream").Readable;function i(e,t,r){n.call(this,t),this._helper=e;var i=this;e.on("data",(function(e,t){i.push(e)||i._helper.pause(),r&&r(t)})).on("error",(function(e){i.emit("error",e)})).on("end",(function(){i.push(null)}))}e("../utils").inherits(i,n),i.prototype._read=function(){this._helper.resume()},t.exports=i},{"../utils":32,"readable-stream":16}],14:[function(e,t,r){t.exports={isNode:"undefined"!=typeof Buffer,newBufferFrom:function(e,t){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(e,t);if("number"==typeof e)throw new Error('The "data" argument must not be a number');return new Buffer(e,t)},allocBuffer:function(e){if(Buffer.alloc)return Buffer.alloc(e);var t=new Buffer(e);return t.fill(0),t},isBuffer:function(e){return Buffer.isBuffer(e)},isStream:function(e){return e&&"function"==typeof e.on&&"function"==typeof e.pause&&"function"==typeof e.resume}}},{}],15:[function(e,t,r){function n(e,t,r){var n,i=a.getTypeOf(t),o=a.extend(r||{},l);o.date=o.date||new Date,null!==o.compression&&(o.compression=o.compression.toUpperCase()),"string"==typeof o.unixPermissions&&(o.unixPermissions=parseInt(o.unixPermissions,8)),o.unixPermissions&&16384&o.unixPermissions&&(o.dir=!0),o.dosPermissions&&16&o.dosPermissions&&(o.dir=!0),o.dir&&(e=m(e)),o.createFolders&&(n=p(e))&&_.call(this,n,!0);var c="string"===i&&!1===o.binary&&!1===o.base64;r&&void 0!==r.binary||(o.binary=!c),(t instanceof u&&0===t.uncompressedSize||o.dir||!t||0===t.length)&&(o.base64=!1,o.binary=!0,t="",o.compression="STORE",i="string");var g=null;g=t instanceof u||t instanceof s?t:f.isNode&&f.isStream(t)?new d(e,t):a.prepareContent(e,t,o.binary,o.optimizedBinaryString,o.base64);var y=new h(e,g,o);this.files[e]=y}var i=e("./utf8"),a=e("./utils"),s=e("./stream/GenericWorker"),o=e("./stream/StreamHelper"),l=e("./defaults"),u=e("./compressedObject"),h=e("./zipObject"),c=e("./generate"),f=e("./nodejsUtils"),d=e("./nodejs/NodejsStreamInputAdapter"),p=function(e){"/"===e.slice(-1)&&(e=e.substring(0,e.length-1));var t=e.lastIndexOf("/");return 0<t?e.substring(0,t):""},m=function(e){return"/"!==e.slice(-1)&&(e+="/"),e},_=function(e,t){return t=void 0!==t?t:l.createFolders,e=m(e),this.files[e]||n.call(this,e,null,{dir:!0,createFolders:t}),this.files[e]};function g(e){return"[object RegExp]"===Object.prototype.toString.call(e)}var y={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(e){var t,r,n;for(t in this.files)n=this.files[t],(r=t.slice(this.root.length,t.length))&&t.slice(0,this.root.length)===this.root&&e(r,n)},filter:function(e){var t=[];return this.forEach((function(r,n){e(r,n)&&t.push(n)})),t},file:function(e,t,r){if(1!==arguments.length)return e=this.root+e,n.call(this,e,t,r),this;if(g(e)){var i=e;return this.filter((function(e,t){return!t.dir&&i.test(e)}))}var a=this.files[this.root+e];return a&&!a.dir?a:null},folder:function(e){if(!e)return this;if(g(e))return this.filter((function(t,r){return r.dir&&e.test(t)}));var t=this.root+e,r=_.call(this,t),n=this.clone();return n.root=r.name,n},remove:function(e){e=this.root+e;var t=this.files[e];if(t||("/"!==e.slice(-1)&&(e+="/"),t=this.files[e]),t&&!t.dir)delete this.files[e];else for(var r=this.filter((function(t,r){return r.name.slice(0,e.length)===e})),n=0;n<r.length;n++)delete this.files[r[n].name];return this},generate:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(e){var t,r={};try{if((r=a.extend(e||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:i.utf8encode})).type=r.type.toLowerCase(),r.compression=r.compression.toUpperCase(),"binarystring"===r.type&&(r.type="string"),!r.type)throw new Error("No output type specified.");a.checkSupport(r.type),"darwin"!==r.platform&&"freebsd"!==r.platform&&"linux"!==r.platform&&"sunos"!==r.platform||(r.platform="UNIX"),"win32"===r.platform&&(r.platform="DOS");var n=r.comment||this.comment||"";t=c.generateWorker(this,r,n)}catch(e){(t=new s("error")).error(e)}return new o(t,r.type||"string",r.mimeType)},generateAsync:function(e,t){return this.generateInternalStream(e).accumulate(t)},generateNodeStream:function(e,t){return(e=e||{}).type||(e.type="nodebuffer"),this.generateInternalStream(e).toNodejsStream(t)}};t.exports=y},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,"./zipObject":35}],16:[function(e,t,r){t.exports=e("stream")},{stream:void 0}],17:[function(e,t,r){var n=e("./DataReader");function i(e){n.call(this,e);for(var t=0;t<this.data.length;t++)e[t]=255&e[t]}e("../utils").inherits(i,n),i.prototype.byteAt=function(e){return this.data[this.zero+e]},i.prototype.lastIndexOfSignature=function(e){for(var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),i=e.charCodeAt(3),a=this.length-4;0<=a;--a)if(this.data[a]===t&&this.data[a+1]===r&&this.data[a+2]===n&&this.data[a+3]===i)return a-this.zero;return-1},i.prototype.readAndCheckSignature=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),i=e.charCodeAt(3),a=this.readData(4);return t===a[0]&&r===a[1]&&n===a[2]&&i===a[3]},i.prototype.readData=function(e){if(this.checkOffset(e),0===e)return[];var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=i},{"../utils":32,"./DataReader":18}],18:[function(e,t,r){var n=e("../utils");function i(e){this.data=e,this.length=e.length,this.index=0,this.zero=0}i.prototype={checkOffset:function(e){this.checkIndex(this.index+e)},checkIndex:function(e){if(this.length<this.zero+e||e<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+e+"). Corrupted zip ?")},setIndex:function(e){this.checkIndex(e),this.index=e},skip:function(e){this.setIndex(this.index+e)},byteAt:function(){},readInt:function(e){var t,r=0;for(this.checkOffset(e),t=this.index+e-1;t>=this.index;t--)r=(r<<8)+this.byteAt(t);return this.index+=e,r},readString:function(e){return n.transformTo("string",this.readData(e))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var e=this.readInt(4);return new Date(Date.UTC(1980+(e>>25&127),(e>>21&15)-1,e>>16&31,e>>11&31,e>>5&63,(31&e)<<1))}},t.exports=i},{"../utils":32}],19:[function(e,t,r){var n=e("./Uint8ArrayReader");function i(e){n.call(this,e)}e("../utils").inherits(i,n),i.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=i},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(e,t,r){var n=e("./DataReader");function i(e){n.call(this,e)}e("../utils").inherits(i,n),i.prototype.byteAt=function(e){return this.data.charCodeAt(this.zero+e)},i.prototype.lastIndexOfSignature=function(e){return this.data.lastIndexOf(e)-this.zero},i.prototype.readAndCheckSignature=function(e){return e===this.readData(4)},i.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=i},{"../utils":32,"./DataReader":18}],21:[function(e,t,r){var n=e("./ArrayReader");function i(e){n.call(this,e)}e("../utils").inherits(i,n),i.prototype.readData=function(e){if(this.checkOffset(e),0===e)return new Uint8Array(0);var t=this.data.subarray(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=i},{"../utils":32,"./ArrayReader":17}],22:[function(e,t,r){var n=e("../utils"),i=e("../support"),a=e("./ArrayReader"),s=e("./StringReader"),o=e("./NodeBufferReader"),l=e("./Uint8ArrayReader");t.exports=function(e){var t=n.getTypeOf(e);return n.checkSupport(t),"string"!==t||i.uint8array?"nodebuffer"===t?new o(e):i.uint8array?new l(n.transformTo("uint8array",e)):new a(n.transformTo("array",e)):new s(e)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(e,t,r){r.LOCAL_FILE_HEADER="PK",r.CENTRAL_FILE_HEADER="PK",r.CENTRAL_DIRECTORY_END="PK",r.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",r.ZIP64_CENTRAL_DIRECTORY_END="PK",r.DATA_DESCRIPTOR="PK\b"},{}],24:[function(e,t,r){var n=e("./GenericWorker"),i=e("../utils");function a(e){n.call(this,"ConvertWorker to "+e),this.destType=e}i.inherits(a,n),a.prototype.processChunk=function(e){this.push({data:i.transformTo(this.destType,e.data),meta:e.meta})},t.exports=a},{"../utils":32,"./GenericWorker":28}],25:[function(e,t,r){var n=e("./GenericWorker"),i=e("../crc32");function a(){n.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}e("../utils").inherits(a,n),a.prototype.processChunk=function(e){this.streamInfo.crc32=i(e.data,this.streamInfo.crc32||0),this.push(e)},t.exports=a},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(e,t,r){var n=e("../utils"),i=e("./GenericWorker");function a(e){i.call(this,"DataLengthProbe for "+e),this.propName=e,this.withStreamInfo(e,0)}n.inherits(a,i),a.prototype.processChunk=function(e){if(e){var t=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=t+e.data.length}i.prototype.processChunk.call(this,e)},t.exports=a},{"../utils":32,"./GenericWorker":28}],27:[function(e,t,r){var n=e("../utils"),i=e("./GenericWorker");function a(e){i.call(this,"DataWorker");var t=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,e.then((function(e){t.dataIsReady=!0,t.data=e,t.max=e&&e.length||0,t.type=n.getTypeOf(e),t.isPaused||t._tickAndRepeat()}),(function(e){t.error(e)}))}n.inherits(a,i),a.prototype.cleanUp=function(){i.prototype.cleanUp.call(this),this.data=null},a.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,n.delay(this._tickAndRepeat,[],this)),!0)},a.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(n.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},a.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var e=null,t=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":e=this.data.substring(this.index,t);break;case"uint8array":e=this.data.subarray(this.index,t);break;case"array":case"nodebuffer":e=this.data.slice(this.index,t)}return this.index=t,this.push({data:e,meta:{percent:this.max?this.index/this.max*100:0}})},t.exports=a},{"../utils":32,"./GenericWorker":28}],28:[function(e,t,r){function n(e){this.name=e||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}n.prototype={push:function(e){this.emit("data",e)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(e){this.emit("error",e)}return!0},error:function(e){return!this.isFinished&&(this.isPaused?this.generatedError=e:(this.isFinished=!0,this.emit("error",e),this.previous&&this.previous.error(e),this.cleanUp()),!0)},on:function(e,t){return this._listeners[e].push(t),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(e,t){if(this._listeners[e])for(var r=0;r<this._listeners[e].length;r++)this._listeners[e][r].call(this,t)},pipe:function(e){return e.registerPrevious(this)},registerPrevious:function(e){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=e.streamInfo,this.mergeStreamInfo(),this.previous=e;var t=this;return e.on("data",(function(e){t.processChunk(e)})),e.on("end",(function(){t.end()})),e.on("error",(function(e){t.error(e)})),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;var e=this.isPaused=!1;return this.generatedError&&(this.error(this.generatedError),e=!0),this.previous&&this.previous.resume(),!e},flush:function(){},processChunk:function(e){this.push(e)},withStreamInfo:function(e,t){return this.extraStreamInfo[e]=t,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var e in this.extraStreamInfo)Object.prototype.hasOwnProperty.call(this.extraStreamInfo,e)&&(this.streamInfo[e]=this.extraStreamInfo[e])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var e="Worker "+this.name;return this.previous?this.previous+" -> "+e:e}},t.exports=n},{}],29:[function(e,t,r){var n=e("../utils"),i=e("./ConvertWorker"),a=e("./GenericWorker"),s=e("../base64"),o=e("../support"),l=e("../external"),u=null;if(o.nodestream)try{u=e("../nodejs/NodejsStreamOutputAdapter")}catch(e){}function h(e,t){return new l.Promise((function(r,i){var a=[],o=e._internalType,l=e._outputType,u=e._mimeType;e.on("data",(function(e,r){a.push(e),t&&t(r)})).on("error",(function(e){a=[],i(e)})).on("end",(function(){try{var e=function(e,t,r){switch(e){case"blob":return n.newBlob(n.transformTo("arraybuffer",t),r);case"base64":return s.encode(t);default:return n.transformTo(e,t)}}(l,function(e,t){var r,n=0,i=null,a=0;for(r=0;r<t.length;r++)a+=t[r].length;switch(e){case"string":return t.join("");case"array":return Array.prototype.concat.apply([],t);case"uint8array":for(i=new Uint8Array(a),r=0;r<t.length;r++)i.set(t[r],n),n+=t[r].length;return i;case"nodebuffer":return Buffer.concat(t);default:throw new Error("concat : unsupported type '"+e+"'")}}(o,a),u);r(e)}catch(e){i(e)}a=[]})).resume()}))}function c(e,t,r){var s=t;switch(t){case"blob":case"arraybuffer":s="uint8array";break;case"base64":s="string"}try{this._internalType=s,this._outputType=t,this._mimeType=r,n.checkSupport(s),this._worker=e.pipe(new i(s)),e.lock()}catch(e){this._worker=new a("error"),this._worker.error(e)}}c.prototype={accumulate:function(e){return h(this,e)},on:function(e,t){var r=this;return"data"===e?this._worker.on(e,(function(e){t.call(r,e.data,e.meta)})):this._worker.on(e,(function(){n.delay(t,arguments,r)})),this},resume:function(){return n.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(e){if(n.checkSupport("nodestream"),"nodebuffer"!==this._outputType)throw new Error(this._outputType+" is not supported by this method");return new u(this,{objectMode:"nodebuffer"!==this._outputType},e)}},t.exports=c},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(e,t,r){if(r.base64=!0,r.array=!0,r.string=!0,r.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,r.nodebuffer="undefined"!=typeof Buffer,r.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)r.blob=!1;else{var n=new ArrayBuffer(0);try{r.blob=0===new Blob([n],{type:"application/zip"}).size}catch(e){try{var i=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);i.append(n),r.blob=0===i.getBlob("application/zip").size}catch(e){r.blob=!1}}}try{r.nodestream=!!e("readable-stream").Readable}catch(e){r.nodestream=!1}},{"readable-stream":16}],31:[function(e,t,r){for(var n=e("./utils"),i=e("./support"),a=e("./nodejsUtils"),s=e("./stream/GenericWorker"),o=new Array(256),l=0;l<256;l++)o[l]=252<=l?6:248<=l?5:240<=l?4:224<=l?3:192<=l?2:1;function u(){s.call(this,"utf-8 decode"),this.leftOver=null}function h(){s.call(this,"utf-8 encode")}o[254]=o[254]=1,r.utf8encode=function(e){return i.nodebuffer?a.newBufferFrom(e,"utf-8"):function(e){var t,r,n,a,s,o=e.length,l=0;for(a=0;a<o;a++)55296==(64512&(r=e.charCodeAt(a)))&&a+1<o&&56320==(64512&(n=e.charCodeAt(a+1)))&&(r=65536+(r-55296<<10)+(n-56320),a++),l+=r<128?1:r<2048?2:r<65536?3:4;for(t=i.uint8array?new Uint8Array(l):new Array(l),a=s=0;s<l;a++)55296==(64512&(r=e.charCodeAt(a)))&&a+1<o&&56320==(64512&(n=e.charCodeAt(a+1)))&&(r=65536+(r-55296<<10)+(n-56320),a++),r<128?t[s++]=r:(r<2048?t[s++]=192|r>>>6:(r<65536?t[s++]=224|r>>>12:(t[s++]=240|r>>>18,t[s++]=128|r>>>12&63),t[s++]=128|r>>>6&63),t[s++]=128|63&r);return t}(e)},r.utf8decode=function(e){return i.nodebuffer?n.transformTo("nodebuffer",e).toString("utf-8"):function(e){var t,r,i,a,s=e.length,l=new Array(2*s);for(t=r=0;t<s;)if((i=e[t++])<128)l[r++]=i;else if(4<(a=o[i]))l[r++]=65533,t+=a-1;else{for(i&=2===a?31:3===a?15:7;1<a&&t<s;)i=i<<6|63&e[t++],a--;1<a?l[r++]=65533:i<65536?l[r++]=i:(i-=65536,l[r++]=55296|i>>10&1023,l[r++]=56320|1023&i)}return l.length!==r&&(l.subarray?l=l.subarray(0,r):l.length=r),n.applyFromCharCode(l)}(e=n.transformTo(i.uint8array?"uint8array":"array",e))},n.inherits(u,s),u.prototype.processChunk=function(e){var t=n.transformTo(i.uint8array?"uint8array":"array",e.data);if(this.leftOver&&this.leftOver.length){if(i.uint8array){var a=t;(t=new Uint8Array(a.length+this.leftOver.length)).set(this.leftOver,0),t.set(a,this.leftOver.length)}else t=this.leftOver.concat(t);this.leftOver=null}var s=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;0<=r&&128==(192&e[r]);)r--;return r<0||0===r?t:r+o[e[r]]>t?r:t}(t),l=t;s!==t.length&&(i.uint8array?(l=t.subarray(0,s),this.leftOver=t.subarray(s,t.length)):(l=t.slice(0,s),this.leftOver=t.slice(s,t.length))),this.push({data:r.utf8decode(l),meta:e.meta})},u.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:r.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},r.Utf8DecodeWorker=u,n.inherits(h,s),h.prototype.processChunk=function(e){this.push({data:r.utf8encode(e.data),meta:e.meta})},r.Utf8EncodeWorker=h},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(e,t,r){var n=e("./support"),i=e("./base64"),a=e("./nodejsUtils"),s=e("./external");function o(e){return e}function l(e,t){for(var r=0;r<e.length;++r)t[r]=255&e.charCodeAt(r);return t}e("setimmediate"),r.newBlob=function(e,t){r.checkSupport("blob");try{return new Blob([e],{type:t})}catch(r){try{var n=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return n.append(e),n.getBlob(t)}catch(e){throw new Error("Bug : can't construct the Blob.")}}};var u={stringifyByChunk:function(e,t,r){var n=[],i=0,a=e.length;if(a<=r)return String.fromCharCode.apply(null,e);for(;i<a;)"array"===t||"nodebuffer"===t?n.push(String.fromCharCode.apply(null,e.slice(i,Math.min(i+r,a)))):n.push(String.fromCharCode.apply(null,e.subarray(i,Math.min(i+r,a)))),i+=r;return n.join("")},stringifyByChar:function(e){for(var t="",r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t},applyCanBeUsed:{uint8array:function(){try{return n.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(e){return!1}}(),nodebuffer:function(){try{return n.nodebuffer&&1===String.fromCharCode.apply(null,a.allocBuffer(1)).length}catch(e){return!1}}()}};function h(e){var t=65536,n=r.getTypeOf(e),i=!0;if("uint8array"===n?i=u.applyCanBeUsed.uint8array:"nodebuffer"===n&&(i=u.applyCanBeUsed.nodebuffer),i)for(;1<t;)try{return u.stringifyByChunk(e,n,t)}catch(e){t=Math.floor(t/2)}return u.stringifyByChar(e)}function c(e,t){for(var r=0;r<e.length;r++)t[r]=e[r];return t}r.applyFromCharCode=h;var f={};f.string={string:o,array:function(e){return l(e,new Array(e.length))},arraybuffer:function(e){return f.string.uint8array(e).buffer},uint8array:function(e){return l(e,new Uint8Array(e.length))},nodebuffer:function(e){return l(e,a.allocBuffer(e.length))}},f.array={string:h,array:o,arraybuffer:function(e){return new Uint8Array(e).buffer},uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return a.newBufferFrom(e)}},f.arraybuffer={string:function(e){return h(new Uint8Array(e))},array:function(e){return c(new Uint8Array(e),new Array(e.byteLength))},arraybuffer:o,uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return a.newBufferFrom(new Uint8Array(e))}},f.uint8array={string:h,array:function(e){return c(e,new Array(e.length))},arraybuffer:function(e){return e.buffer},uint8array:o,nodebuffer:function(e){return a.newBufferFrom(e)}},f.nodebuffer={string:h,array:function(e){return c(e,new Array(e.length))},arraybuffer:function(e){return f.nodebuffer.uint8array(e).buffer},uint8array:function(e){return c(e,new Uint8Array(e.length))},nodebuffer:o},r.transformTo=function(e,t){if(t=t||"",!e)return t;r.checkSupport(e);var n=r.getTypeOf(t);return f[n][e](t)},r.resolve=function(e){for(var t=e.split("/"),r=[],n=0;n<t.length;n++){var i=t[n];"."===i||""===i&&0!==n&&n!==t.length-1||(".."===i?r.pop():r.push(i))}return r.join("/")},r.getTypeOf=function(e){return"string"==typeof e?"string":"[object Array]"===Object.prototype.toString.call(e)?"array":n.nodebuffer&&a.isBuffer(e)?"nodebuffer":n.uint8array&&e instanceof Uint8Array?"uint8array":n.arraybuffer&&e instanceof ArrayBuffer?"arraybuffer":void 0},r.checkSupport=function(e){if(!n[e.toLowerCase()])throw new Error(e+" is not supported by this platform")},r.MAX_VALUE_16BITS=65535,r.MAX_VALUE_32BITS=-1,r.pretty=function(e){var t,r,n="";for(r=0;r<(e||"").length;r++)n+="\\x"+((t=e.charCodeAt(r))<16?"0":"")+t.toString(16).toUpperCase();return n},r.delay=function(e,t,r){setImmediate((function(){e.apply(r||null,t||[])}))},r.inherits=function(e,t){function r(){}r.prototype=t.prototype,e.prototype=new r},r.extend=function(){var e,t,r={};for(e=0;e<arguments.length;e++)for(t in arguments[e])Object.prototype.hasOwnProperty.call(arguments[e],t)&&void 0===r[t]&&(r[t]=arguments[e][t]);return r},r.prepareContent=function(e,t,a,o,u){return s.Promise.resolve(t).then((function(e){return n.blob&&(e instanceof Blob||-1!==["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(e)))&&"undefined"!=typeof FileReader?new s.Promise((function(t,r){var n=new FileReader;n.onload=function(e){t(e.target.result)},n.onerror=function(e){r(e.target.error)},n.readAsArrayBuffer(e)})):e})).then((function(t){var h=r.getTypeOf(t);return h?("arraybuffer"===h?t=r.transformTo("uint8array",t):"string"===h&&(u?t=i.decode(t):a&&!0!==o&&(t=function(e){return l(e,n.uint8array?new Uint8Array(e.length):new Array(e.length))}(t))),t):s.Promise.reject(new Error("Can't read the data of '"+e+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))}))}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,setimmediate:54}],33:[function(e,t,r){var n=e("./reader/readerFor"),i=e("./utils"),a=e("./signature"),s=e("./zipEntry"),o=e("./support");function l(e){this.files=[],this.loadOptions=e}l.prototype={checkSignature:function(e){if(!this.reader.readAndCheckSignature(e)){this.reader.index-=4;var t=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+i.pretty(t)+", expected "+i.pretty(e)+")")}},isSignature:function(e,t){var r=this.reader.index;this.reader.setIndex(e);var n=this.reader.readString(4)===t;return this.reader.setIndex(r),n},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var e=this.reader.readData(this.zipCommentLength),t=o.uint8array?"uint8array":"array",r=i.transformTo(t,e);this.zipComment=this.loadOptions.decodeFileName(r)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var e,t,r,n=this.zip64EndOfCentralSize-44;0<n;)e=this.reader.readInt(2),t=this.reader.readInt(4),r=this.reader.readData(t),this.zip64ExtensibleData[e]={id:e,length:t,value:r}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var e,t;for(e=0;e<this.files.length;e++)t=this.files[e],this.reader.setIndex(t.localHeaderOffset),this.checkSignature(a.LOCAL_FILE_HEADER),t.readLocalPart(this.reader),t.handleUTF8(),t.processAttributes()},readCentralDir:function(){var e;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(a.CENTRAL_FILE_HEADER);)(e=new s({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(e);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var e=this.reader.lastIndexOfSignature(a.CENTRAL_DIRECTORY_END);if(e<0)throw this.isSignature(0,a.LOCAL_FILE_HEADER)?new Error("Corrupted zip: can't find end of central directory"):new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(e);var t=e;if(this.checkSignature(a.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===i.MAX_VALUE_16BITS||this.diskWithCentralDirStart===i.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===i.MAX_VALUE_16BITS||this.centralDirRecords===i.MAX_VALUE_16BITS||this.centralDirSize===i.MAX_VALUE_32BITS||this.centralDirOffset===i.MAX_VALUE_32BITS){if(this.zip64=!0,(e=this.reader.lastIndexOfSignature(a.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(e),this.checkSignature(a.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,a.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(a.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(a.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var r=this.centralDirOffset+this.centralDirSize;this.zip64&&(r+=20,r+=12+this.zip64EndOfCentralSize);var n=t-r;if(0<n)this.isSignature(t,a.CENTRAL_FILE_HEADER)||(this.reader.zero=n);else if(n<0)throw new Error("Corrupted zip: missing "+Math.abs(n)+" bytes.")},prepareReader:function(e){this.reader=n(e)},load:function(e){this.prepareReader(e),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},t.exports=l},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utils":32,"./zipEntry":34}],34:[function(e,t,r){var n=e("./reader/readerFor"),i=e("./utils"),a=e("./compressedObject"),s=e("./crc32"),o=e("./utf8"),l=e("./compressions"),u=e("./support");function h(e,t){this.options=e,this.loadOptions=t}h.prototype={isEncrypted:function(){return!(1&~this.bitFlag)},useUTF8:function(){return!(2048&~this.bitFlag)},readLocalPart:function(e){var t,r;if(e.skip(22),this.fileNameLength=e.readInt(2),r=e.readInt(2),this.fileName=e.readData(this.fileNameLength),e.skip(r),-1===this.compressedSize||-1===this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(null===(t=function(e){for(var t in l)if(Object.prototype.hasOwnProperty.call(l,t)&&l[t].magic===e)return l[t];return null}(this.compressionMethod)))throw new Error("Corrupted zip : compression "+i.pretty(this.compressionMethod)+" unknown (inner file : "+i.transformTo("string",this.fileName)+")");this.decompressed=new a(this.compressedSize,this.uncompressedSize,this.crc32,t,e.readData(this.compressedSize))},readCentralPart:function(e){this.versionMadeBy=e.readInt(2),e.skip(2),this.bitFlag=e.readInt(2),this.compressionMethod=e.readString(2),this.date=e.readDate(),this.crc32=e.readInt(4),this.compressedSize=e.readInt(4),this.uncompressedSize=e.readInt(4);var t=e.readInt(2);if(this.extraFieldsLength=e.readInt(2),this.fileCommentLength=e.readInt(2),this.diskNumberStart=e.readInt(2),this.internalFileAttributes=e.readInt(2),this.externalFileAttributes=e.readInt(4),this.localHeaderOffset=e.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");e.skip(t),this.readExtraFields(e),this.parseZIP64ExtraField(e),this.fileComment=e.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var e=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0==e&&(this.dosPermissions=63&this.externalFileAttributes),3==e&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var e=n(this.extraFields[1].value);this.uncompressedSize===i.MAX_VALUE_32BITS&&(this.uncompressedSize=e.readInt(8)),this.compressedSize===i.MAX_VALUE_32BITS&&(this.compressedSize=e.readInt(8)),this.localHeaderOffset===i.MAX_VALUE_32BITS&&(this.localHeaderOffset=e.readInt(8)),this.diskNumberStart===i.MAX_VALUE_32BITS&&(this.diskNumberStart=e.readInt(4))}},readExtraFields:function(e){var t,r,n,i=e.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});e.index+4<i;)t=e.readInt(2),r=e.readInt(2),n=e.readData(r),this.extraFields[t]={id:t,length:r,value:n};e.setIndex(i)},handleUTF8:function(){var e=u.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=o.utf8decode(this.fileName),this.fileCommentStr=o.utf8decode(this.fileComment);else{var t=this.findExtraFieldUnicodePath();if(null!==t)this.fileNameStr=t;else{var r=i.transformTo(e,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(r)}var n=this.findExtraFieldUnicodeComment();if(null!==n)this.fileCommentStr=n;else{var a=i.transformTo(e,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(a)}}},findExtraFieldUnicodePath:function(){var e=this.extraFields[28789];if(e){var t=n(e.value);return 1!==t.readInt(1)||s(this.fileName)!==t.readInt(4)?null:o.utf8decode(t.readData(e.length-5))}return null},findExtraFieldUnicodeComment:function(){var e=this.extraFields[25461];if(e){var t=n(e.value);return 1!==t.readInt(1)||s(this.fileComment)!==t.readInt(4)?null:o.utf8decode(t.readData(e.length-5))}return null}},t.exports=h},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(e,t,r){function n(e,t,r){this.name=e,this.dir=r.dir,this.date=r.date,this.comment=r.comment,this.unixPermissions=r.unixPermissions,this.dosPermissions=r.dosPermissions,this._data=t,this._dataBinary=r.binary,this.options={compression:r.compression,compressionOptions:r.compressionOptions}}var i=e("./stream/StreamHelper"),a=e("./stream/DataWorker"),s=e("./utf8"),o=e("./compressedObject"),l=e("./stream/GenericWorker");n.prototype={internalStream:function(e){var t=null,r="string";try{if(!e)throw new Error("No output type specified.");var n="string"===(r=e.toLowerCase())||"text"===r;"binarystring"!==r&&"text"!==r||(r="string"),t=this._decompressWorker();var a=!this._dataBinary;a&&!n&&(t=t.pipe(new s.Utf8EncodeWorker)),!a&&n&&(t=t.pipe(new s.Utf8DecodeWorker))}catch(e){(t=new l("error")).error(e)}return new i(t,r,"")},async:function(e,t){return this.internalStream(e).accumulate(t)},nodeStream:function(e,t){return this.internalStream(e||"nodebuffer").toNodejsStream(t)},_compressWorker:function(e,t){if(this._data instanceof o&&this._data.compression.magic===e.magic)return this._data.getCompressedWorker();var r=this._decompressWorker();return this._dataBinary||(r=r.pipe(new s.Utf8EncodeWorker)),o.createWorkerFrom(r,e,t)},_decompressWorker:function(){return this._data instanceof o?this._data.getContentWorker():this._data instanceof l?this._data:new a(this._data)}};for(var u=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],h=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},c=0;c<u.length;c++)n.prototype[u[c]]=h;t.exports=n},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(e,r,n){(function(e){var t,n,i=e.MutationObserver||e.WebKitMutationObserver;if(i){var a=0,s=new i(h),o=e.document.createTextNode("");s.observe(o,{characterData:!0}),t=function(){o.data=a=++a%2}}else if(e.setImmediate||void 0===e.MessageChannel)t="document"in e&&"onreadystatechange"in e.document.createElement("script")?function(){var t=e.document.createElement("script");t.onreadystatechange=function(){h(),t.onreadystatechange=null,t.parentNode.removeChild(t),t=null},e.document.documentElement.appendChild(t)}:function(){setTimeout(h,0)};else{var l=new e.MessageChannel;l.port1.onmessage=h,t=function(){l.port2.postMessage(0)}}var u=[];function h(){var e,t;n=!0;for(var r=u.length;r;){for(t=u,u=[],e=-1;++e<r;)t[e]();r=u.length}n=!1}r.exports=function(e){1!==u.push(e)||n||t()}}).call(this,void 0!==t?t:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],37:[function(e,t,r){var n=e("immediate");function i(){}var a={},s=["REJECTED"],o=["FULFILLED"],l=["PENDING"];function u(e){if("function"!=typeof e)throw new TypeError("resolver must be a function");this.state=l,this.queue=[],this.outcome=void 0,e!==i&&d(this,e)}function h(e,t,r){this.promise=e,"function"==typeof t&&(this.onFulfilled=t,this.callFulfilled=this.otherCallFulfilled),"function"==typeof r&&(this.onRejected=r,this.callRejected=this.otherCallRejected)}function c(e,t,r){n((function(){var n;try{n=t(r)}catch(n){return a.reject(e,n)}n===e?a.reject(e,new TypeError("Cannot resolve promise with itself")):a.resolve(e,n)}))}function f(e){var t=e&&e.then;if(e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof t)return function(){t.apply(e,arguments)}}function d(e,t){var r=!1;function n(t){r||(r=!0,a.reject(e,t))}function i(t){r||(r=!0,a.resolve(e,t))}var s=p((function(){t(i,n)}));"error"===s.status&&n(s.value)}function p(e,t){var r={};try{r.value=e(t),r.status="success"}catch(e){r.status="error",r.value=e}return r}(t.exports=u).prototype.finally=function(e){if("function"!=typeof e)return this;var t=this.constructor;return this.then((function(r){return t.resolve(e()).then((function(){return r}))}),(function(r){return t.resolve(e()).then((function(){throw r}))}))},u.prototype.catch=function(e){return this.then(null,e)},u.prototype.then=function(e,t){if("function"!=typeof e&&this.state===o||"function"!=typeof t&&this.state===s)return this;var r=new this.constructor(i);return this.state!==l?c(r,this.state===o?e:t,this.outcome):this.queue.push(new h(r,e,t)),r},h.prototype.callFulfilled=function(e){a.resolve(this.promise,e)},h.prototype.otherCallFulfilled=function(e){c(this.promise,this.onFulfilled,e)},h.prototype.callRejected=function(e){a.reject(this.promise,e)},h.prototype.otherCallRejected=function(e){c(this.promise,this.onRejected,e)},a.resolve=function(e,t){var r=p(f,t);if("error"===r.status)return a.reject(e,r.value);var n=r.value;if(n)d(e,n);else{e.state=o,e.outcome=t;for(var i=-1,s=e.queue.length;++i<s;)e.queue[i].callFulfilled(t)}return e},a.reject=function(e,t){e.state=s,e.outcome=t;for(var r=-1,n=e.queue.length;++r<n;)e.queue[r].callRejected(t);return e},u.resolve=function(e){return e instanceof this?e:a.resolve(new this(i),e)},u.reject=function(e){var t=new this(i);return a.reject(t,e)},u.all=function(e){var t=this;if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var r=e.length,n=!1;if(!r)return this.resolve([]);for(var s=new Array(r),o=0,l=-1,u=new this(i);++l<r;)h(e[l],l);return u;function h(e,i){t.resolve(e).then((function(e){s[i]=e,++o!==r||n||(n=!0,a.resolve(u,s))}),(function(e){n||(n=!0,a.reject(u,e))}))}},u.race=function(e){var t=this;if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var r=e.length,n=!1;if(!r)return this.resolve([]);for(var s,o=-1,l=new this(i);++o<r;)s=e[o],t.resolve(s).then((function(e){n||(n=!0,a.resolve(l,e))}),(function(e){n||(n=!0,a.reject(l,e))}));return l}},{immediate:36}],38:[function(e,t,r){var n={};(0,e("./lib/utils/common").assign)(n,e("./lib/deflate"),e("./lib/inflate"),e("./lib/zlib/constants")),t.exports=n},{"./lib/deflate":39,"./lib/inflate":40,"./lib/utils/common":41,"./lib/zlib/constants":44}],39:[function(e,t,r){var n=e("./zlib/deflate"),i=e("./utils/common"),a=e("./utils/strings"),s=e("./zlib/messages"),o=e("./zlib/zstream"),l=Object.prototype.toString,u=0,h=-1,c=0,f=8;function d(e){if(!(this instanceof d))return new d(e);this.options=i.assign({level:h,method:f,chunkSize:16384,windowBits:15,memLevel:8,strategy:c,to:""},e||{});var t=this.options;t.raw&&0<t.windowBits?t.windowBits=-t.windowBits:t.gzip&&0<t.windowBits&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new o,this.strm.avail_out=0;var r=n.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(r!==u)throw new Error(s[r]);if(t.header&&n.deflateSetHeader(this.strm,t.header),t.dictionary){var p;if(p="string"==typeof t.dictionary?a.string2buf(t.dictionary):"[object ArrayBuffer]"===l.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,(r=n.deflateSetDictionary(this.strm,p))!==u)throw new Error(s[r]);this._dict_set=!0}}function p(e,t){var r=new d(t);if(r.push(e,!0),r.err)throw r.msg||s[r.err];return r.result}d.prototype.push=function(e,t){var r,s,o=this.strm,h=this.options.chunkSize;if(this.ended)return!1;s=t===~~t?t:!0===t?4:0,"string"==typeof e?o.input=a.string2buf(e):"[object ArrayBuffer]"===l.call(e)?o.input=new Uint8Array(e):o.input=e,o.next_in=0,o.avail_in=o.input.length;do{if(0===o.avail_out&&(o.output=new i.Buf8(h),o.next_out=0,o.avail_out=h),1!==(r=n.deflate(o,s))&&r!==u)return this.onEnd(r),!(this.ended=!0);0!==o.avail_out&&(0!==o.avail_in||4!==s&&2!==s)||("string"===this.options.to?this.onData(a.buf2binstring(i.shrinkBuf(o.output,o.next_out))):this.onData(i.shrinkBuf(o.output,o.next_out)))}while((0<o.avail_in||0===o.avail_out)&&1!==r);return 4===s?(r=n.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===u):2!==s||(this.onEnd(u),!(o.avail_out=0))},d.prototype.onData=function(e){this.chunks.push(e)},d.prototype.onEnd=function(e){e===u&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Deflate=d,r.deflate=p,r.deflateRaw=function(e,t){return(t=t||{}).raw=!0,p(e,t)},r.gzip=function(e,t){return(t=t||{}).gzip=!0,p(e,t)}},{"./utils/common":41,"./utils/strings":42,"./zlib/deflate":46,"./zlib/messages":51,"./zlib/zstream":53}],40:[function(e,t,r){var n=e("./zlib/inflate"),i=e("./utils/common"),a=e("./utils/strings"),s=e("./zlib/constants"),o=e("./zlib/messages"),l=e("./zlib/zstream"),u=e("./zlib/gzheader"),h=Object.prototype.toString;function c(e){if(!(this instanceof c))return new c(e);this.options=i.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&0<=t.windowBits&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(0<=t.windowBits&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),15<t.windowBits&&t.windowBits<48&&!(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var r=n.inflateInit2(this.strm,t.windowBits);if(r!==s.Z_OK)throw new Error(o[r]);this.header=new u,n.inflateGetHeader(this.strm,this.header)}function f(e,t){var r=new c(t);if(r.push(e,!0),r.err)throw r.msg||o[r.err];return r.result}c.prototype.push=function(e,t){var r,o,l,u,c,f,d=this.strm,p=this.options.chunkSize,m=this.options.dictionary,_=!1;if(this.ended)return!1;o=t===~~t?t:!0===t?s.Z_FINISH:s.Z_NO_FLUSH,"string"==typeof e?d.input=a.binstring2buf(e):"[object ArrayBuffer]"===h.call(e)?d.input=new Uint8Array(e):d.input=e,d.next_in=0,d.avail_in=d.input.length;do{if(0===d.avail_out&&(d.output=new i.Buf8(p),d.next_out=0,d.avail_out=p),(r=n.inflate(d,s.Z_NO_FLUSH))===s.Z_NEED_DICT&&m&&(f="string"==typeof m?a.string2buf(m):"[object ArrayBuffer]"===h.call(m)?new Uint8Array(m):m,r=n.inflateSetDictionary(this.strm,f)),r===s.Z_BUF_ERROR&&!0===_&&(r=s.Z_OK,_=!1),r!==s.Z_STREAM_END&&r!==s.Z_OK)return this.onEnd(r),!(this.ended=!0);d.next_out&&(0!==d.avail_out&&r!==s.Z_STREAM_END&&(0!==d.avail_in||o!==s.Z_FINISH&&o!==s.Z_SYNC_FLUSH)||("string"===this.options.to?(l=a.utf8border(d.output,d.next_out),u=d.next_out-l,c=a.buf2string(d.output,l),d.next_out=u,d.avail_out=p-u,u&&i.arraySet(d.output,d.output,l,u,0),this.onData(c)):this.onData(i.shrinkBuf(d.output,d.next_out)))),0===d.avail_in&&0===d.avail_out&&(_=!0)}while((0<d.avail_in||0===d.avail_out)&&r!==s.Z_STREAM_END);return r===s.Z_STREAM_END&&(o=s.Z_FINISH),o===s.Z_FINISH?(r=n.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===s.Z_OK):o!==s.Z_SYNC_FLUSH||(this.onEnd(s.Z_OK),!(d.avail_out=0))},c.prototype.onData=function(e){this.chunks.push(e)},c.prototype.onEnd=function(e){e===s.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Inflate=c,r.inflate=f,r.inflateRaw=function(e,t){return(t=t||{}).raw=!0,f(e,t)},r.ungzip=f},{"./utils/common":41,"./utils/strings":42,"./zlib/constants":44,"./zlib/gzheader":47,"./zlib/inflate":49,"./zlib/messages":51,"./zlib/zstream":53}],41:[function(e,t,r){var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;r.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var n in r)r.hasOwnProperty(n)&&(e[n]=r[n])}}return e},r.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var i={arraySet:function(e,t,r,n,i){if(t.subarray&&e.subarray)e.set(t.subarray(r,r+n),i);else for(var a=0;a<n;a++)e[i+a]=t[r+a]},flattenChunks:function(e){var t,r,n,i,a,s;for(t=n=0,r=e.length;t<r;t++)n+=e[t].length;for(s=new Uint8Array(n),t=i=0,r=e.length;t<r;t++)a=e[t],s.set(a,i),i+=a.length;return s}},a={arraySet:function(e,t,r,n,i){for(var a=0;a<n;a++)e[i+a]=t[r+a]},flattenChunks:function(e){return[].concat.apply([],e)}};r.setTyped=function(e){e?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,i)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,a))},r.setTyped(n)},{}],42:[function(e,t,r){var n=e("./common"),i=!0,a=!0;try{String.fromCharCode.apply(null,[0])}catch(e){i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){a=!1}for(var s=new n.Buf8(256),o=0;o<256;o++)s[o]=252<=o?6:248<=o?5:240<=o?4:224<=o?3:192<=o?2:1;function l(e,t){if(t<65537&&(e.subarray&&a||!e.subarray&&i))return String.fromCharCode.apply(null,n.shrinkBuf(e,t));for(var r="",s=0;s<t;s++)r+=String.fromCharCode(e[s]);return r}s[254]=s[254]=1,r.string2buf=function(e){var t,r,i,a,s,o=e.length,l=0;for(a=0;a<o;a++)55296==(64512&(r=e.charCodeAt(a)))&&a+1<o&&56320==(64512&(i=e.charCodeAt(a+1)))&&(r=65536+(r-55296<<10)+(i-56320),a++),l+=r<128?1:r<2048?2:r<65536?3:4;for(t=new n.Buf8(l),a=s=0;s<l;a++)55296==(64512&(r=e.charCodeAt(a)))&&a+1<o&&56320==(64512&(i=e.charCodeAt(a+1)))&&(r=65536+(r-55296<<10)+(i-56320),a++),r<128?t[s++]=r:(r<2048?t[s++]=192|r>>>6:(r<65536?t[s++]=224|r>>>12:(t[s++]=240|r>>>18,t[s++]=128|r>>>12&63),t[s++]=128|r>>>6&63),t[s++]=128|63&r);return t},r.buf2binstring=function(e){return l(e,e.length)},r.binstring2buf=function(e){for(var t=new n.Buf8(e.length),r=0,i=t.length;r<i;r++)t[r]=e.charCodeAt(r);return t},r.buf2string=function(e,t){var r,n,i,a,o=t||e.length,u=new Array(2*o);for(r=n=0;r<o;)if((i=e[r++])<128)u[n++]=i;else if(4<(a=s[i]))u[n++]=65533,r+=a-1;else{for(i&=2===a?31:3===a?15:7;1<a&&r<o;)i=i<<6|63&e[r++],a--;1<a?u[n++]=65533:i<65536?u[n++]=i:(i-=65536,u[n++]=55296|i>>10&1023,u[n++]=56320|1023&i)}return l(u,n)},r.utf8border=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;0<=r&&128==(192&e[r]);)r--;return r<0||0===r?t:r+s[e[r]]>t?r:t}},{"./common":41}],43:[function(e,t,r){t.exports=function(e,t,r,n){for(var i=65535&e,a=e>>>16&65535,s=0;0!==r;){for(r-=s=2e3<r?2e3:r;a=a+(i=i+t[n++]|0)|0,--s;);i%=65521,a%=65521}return i|a<<16}},{}],44:[function(e,t,r){t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],45:[function(e,t,r){var n=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();t.exports=function(e,t,r,i){var a=n,s=i+r;e^=-1;for(var o=i;o<s;o++)e=e>>>8^a[255&(e^t[o])];return~e}},{}],46:[function(e,t,r){var n,i=e("../utils/common"),a=e("./trees"),s=e("./adler32"),o=e("./crc32"),l=e("./messages"),u=0,h=4,c=0,f=-2,d=-1,p=4,m=2,_=8,g=9,y=286,b=30,v=19,w=2*y+1,k=15,x=3,S=258,z=S+x+1,C=42,E=113,A=1,O=2,I=3,T=4;function B(e,t){return e.msg=l[t],t}function R(e){return(e<<1)-(4<e?9:0)}function D(e){for(var t=e.length;0<=--t;)e[t]=0}function F(e){var t=e.state,r=t.pending;r>e.avail_out&&(r=e.avail_out),0!==r&&(i.arraySet(e.output,t.pending_buf,t.pending_out,r,e.next_out),e.next_out+=r,t.pending_out+=r,e.total_out+=r,e.avail_out-=r,t.pending-=r,0===t.pending&&(t.pending_out=0))}function P(e,t){a._tr_flush_block(e,0<=e.block_start?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,F(e.strm)}function N(e,t){e.pending_buf[e.pending++]=t}function L(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function U(e,t){var r,n,i=e.max_chain_length,a=e.strstart,s=e.prev_length,o=e.nice_match,l=e.strstart>e.w_size-z?e.strstart-(e.w_size-z):0,u=e.window,h=e.w_mask,c=e.prev,f=e.strstart+S,d=u[a+s-1],p=u[a+s];e.prev_length>=e.good_match&&(i>>=2),o>e.lookahead&&(o=e.lookahead);do{if(u[(r=t)+s]===p&&u[r+s-1]===d&&u[r]===u[a]&&u[++r]===u[a+1]){a+=2,r++;do{}while(u[++a]===u[++r]&&u[++a]===u[++r]&&u[++a]===u[++r]&&u[++a]===u[++r]&&u[++a]===u[++r]&&u[++a]===u[++r]&&u[++a]===u[++r]&&u[++a]===u[++r]&&a<f);if(n=S-(f-a),a=f-S,s<n){if(e.match_start=t,o<=(s=n))break;d=u[a+s-1],p=u[a+s]}}}while((t=c[t&h])>l&&0!=--i);return s<=e.lookahead?s:e.lookahead}function j(e){var t,r,n,a,l,u,h,c,f,d,p=e.w_size;do{if(a=e.window_size-e.lookahead-e.strstart,e.strstart>=p+(p-z)){for(i.arraySet(e.window,e.window,p,p,0),e.match_start-=p,e.strstart-=p,e.block_start-=p,t=r=e.hash_size;n=e.head[--t],e.head[t]=p<=n?n-p:0,--r;);for(t=r=p;n=e.prev[--t],e.prev[t]=p<=n?n-p:0,--r;);a+=p}if(0===e.strm.avail_in)break;if(u=e.strm,h=e.window,c=e.strstart+e.lookahead,d=void 0,(f=a)<(d=u.avail_in)&&(d=f),r=0===d?0:(u.avail_in-=d,i.arraySet(h,u.input,u.next_in,d,c),1===u.state.wrap?u.adler=s(u.adler,h,d,c):2===u.state.wrap&&(u.adler=o(u.adler,h,d,c)),u.next_in+=d,u.total_in+=d,d),e.lookahead+=r,e.lookahead+e.insert>=x)for(l=e.strstart-e.insert,e.ins_h=e.window[l],e.ins_h=(e.ins_h<<e.hash_shift^e.window[l+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[l+x-1])&e.hash_mask,e.prev[l&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=l,l++,e.insert--,!(e.lookahead+e.insert<x)););}while(e.lookahead<z&&0!==e.strm.avail_in)}function M(e,t){for(var r,n;;){if(e.lookahead<z){if(j(e),e.lookahead<z&&t===u)return A;if(0===e.lookahead)break}if(r=0,e.lookahead>=x&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+x-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==r&&e.strstart-r<=e.w_size-z&&(e.match_length=U(e,r)),e.match_length>=x)if(n=a._tr_tally(e,e.strstart-e.match_start,e.match_length-x),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=x){for(e.match_length--;e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+x-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart,0!=--e.match_length;);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else n=a._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(n&&(P(e,!1),0===e.strm.avail_out))return A}return e.insert=e.strstart<x-1?e.strstart:x-1,t===h?(P(e,!0),0===e.strm.avail_out?I:T):e.last_lit&&(P(e,!1),0===e.strm.avail_out)?A:O}function Z(e,t){for(var r,n,i;;){if(e.lookahead<z){if(j(e),e.lookahead<z&&t===u)return A;if(0===e.lookahead)break}if(r=0,e.lookahead>=x&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+x-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=x-1,0!==r&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-z&&(e.match_length=U(e,r),e.match_length<=5&&(1===e.strategy||e.match_length===x&&4096<e.strstart-e.match_start)&&(e.match_length=x-1)),e.prev_length>=x&&e.match_length<=e.prev_length){for(i=e.strstart+e.lookahead-x,n=a._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-x),e.lookahead-=e.prev_length-1,e.prev_length-=2;++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+x-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!=--e.prev_length;);if(e.match_available=0,e.match_length=x-1,e.strstart++,n&&(P(e,!1),0===e.strm.avail_out))return A}else if(e.match_available){if((n=a._tr_tally(e,0,e.window[e.strstart-1]))&&P(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return A}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(n=a._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<x-1?e.strstart:x-1,t===h?(P(e,!0),0===e.strm.avail_out?I:T):e.last_lit&&(P(e,!1),0===e.strm.avail_out)?A:O}function W(e,t,r,n,i){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=n,this.func=i}function G(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=_,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new i.Buf16(2*w),this.dyn_dtree=new i.Buf16(2*(2*b+1)),this.bl_tree=new i.Buf16(2*(2*v+1)),D(this.dyn_ltree),D(this.dyn_dtree),D(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new i.Buf16(k+1),this.heap=new i.Buf16(2*y+1),D(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new i.Buf16(2*y+1),D(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function H(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=m,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?C:E,e.adler=2===t.wrap?0:1,t.last_flush=u,a._tr_init(t),c):B(e,f)}function K(e){var t=H(e);return t===c&&function(e){e.window_size=2*e.w_size,D(e.head),e.max_lazy_match=n[e.level].max_lazy,e.good_match=n[e.level].good_length,e.nice_match=n[e.level].nice_length,e.max_chain_length=n[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=x-1,e.match_available=0,e.ins_h=0}(e.state),t}function X(e,t,r,n,a,s){if(!e)return f;var o=1;if(t===d&&(t=6),n<0?(o=0,n=-n):15<n&&(o=2,n-=16),a<1||g<a||r!==_||n<8||15<n||t<0||9<t||s<0||p<s)return B(e,f);8===n&&(n=9);var l=new G;return(e.state=l).strm=e,l.wrap=o,l.gzhead=null,l.w_bits=n,l.w_size=1<<l.w_bits,l.w_mask=l.w_size-1,l.hash_bits=a+7,l.hash_size=1<<l.hash_bits,l.hash_mask=l.hash_size-1,l.hash_shift=~~((l.hash_bits+x-1)/x),l.window=new i.Buf8(2*l.w_size),l.head=new i.Buf16(l.hash_size),l.prev=new i.Buf16(l.w_size),l.lit_bufsize=1<<a+6,l.pending_buf_size=4*l.lit_bufsize,l.pending_buf=new i.Buf8(l.pending_buf_size),l.d_buf=1*l.lit_bufsize,l.l_buf=3*l.lit_bufsize,l.level=t,l.strategy=s,l.method=r,K(e)}n=[new W(0,0,0,0,(function(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(j(e),0===e.lookahead&&t===u)return A;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var n=e.block_start+r;if((0===e.strstart||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,P(e,!1),0===e.strm.avail_out))return A;if(e.strstart-e.block_start>=e.w_size-z&&(P(e,!1),0===e.strm.avail_out))return A}return e.insert=0,t===h?(P(e,!0),0===e.strm.avail_out?I:T):(e.strstart>e.block_start&&(P(e,!1),e.strm.avail_out),A)})),new W(4,4,8,4,M),new W(4,5,16,8,M),new W(4,6,32,32,M),new W(4,4,16,16,Z),new W(8,16,32,32,Z),new W(8,16,128,128,Z),new W(8,32,128,256,Z),new W(32,128,258,1024,Z),new W(32,258,258,4096,Z)],r.deflateInit=function(e,t){return X(e,t,_,15,8,0)},r.deflateInit2=X,r.deflateReset=K,r.deflateResetKeep=H,r.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?f:(e.state.gzhead=t,c):f},r.deflate=function(e,t){var r,i,s,l;if(!e||!e.state||5<t||t<0)return e?B(e,f):f;if(i=e.state,!e.output||!e.input&&0!==e.avail_in||666===i.status&&t!==h)return B(e,0===e.avail_out?-5:f);if(i.strm=e,r=i.last_flush,i.last_flush=t,i.status===C)if(2===i.wrap)e.adler=0,N(i,31),N(i,139),N(i,8),i.gzhead?(N(i,(i.gzhead.text?1:0)+(i.gzhead.hcrc?2:0)+(i.gzhead.extra?4:0)+(i.gzhead.name?8:0)+(i.gzhead.comment?16:0)),N(i,255&i.gzhead.time),N(i,i.gzhead.time>>8&255),N(i,i.gzhead.time>>16&255),N(i,i.gzhead.time>>24&255),N(i,9===i.level?2:2<=i.strategy||i.level<2?4:0),N(i,255&i.gzhead.os),i.gzhead.extra&&i.gzhead.extra.length&&(N(i,255&i.gzhead.extra.length),N(i,i.gzhead.extra.length>>8&255)),i.gzhead.hcrc&&(e.adler=o(e.adler,i.pending_buf,i.pending,0)),i.gzindex=0,i.status=69):(N(i,0),N(i,0),N(i,0),N(i,0),N(i,0),N(i,9===i.level?2:2<=i.strategy||i.level<2?4:0),N(i,3),i.status=E);else{var d=_+(i.w_bits-8<<4)<<8;d|=(2<=i.strategy||i.level<2?0:i.level<6?1:6===i.level?2:3)<<6,0!==i.strstart&&(d|=32),d+=31-d%31,i.status=E,L(i,d),0!==i.strstart&&(L(i,e.adler>>>16),L(i,65535&e.adler)),e.adler=1}if(69===i.status)if(i.gzhead.extra){for(s=i.pending;i.gzindex<(65535&i.gzhead.extra.length)&&(i.pending!==i.pending_buf_size||(i.gzhead.hcrc&&i.pending>s&&(e.adler=o(e.adler,i.pending_buf,i.pending-s,s)),F(e),s=i.pending,i.pending!==i.pending_buf_size));)N(i,255&i.gzhead.extra[i.gzindex]),i.gzindex++;i.gzhead.hcrc&&i.pending>s&&(e.adler=o(e.adler,i.pending_buf,i.pending-s,s)),i.gzindex===i.gzhead.extra.length&&(i.gzindex=0,i.status=73)}else i.status=73;if(73===i.status)if(i.gzhead.name){s=i.pending;do{if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>s&&(e.adler=o(e.adler,i.pending_buf,i.pending-s,s)),F(e),s=i.pending,i.pending===i.pending_buf_size)){l=1;break}l=i.gzindex<i.gzhead.name.length?255&i.gzhead.name.charCodeAt(i.gzindex++):0,N(i,l)}while(0!==l);i.gzhead.hcrc&&i.pending>s&&(e.adler=o(e.adler,i.pending_buf,i.pending-s,s)),0===l&&(i.gzindex=0,i.status=91)}else i.status=91;if(91===i.status)if(i.gzhead.comment){s=i.pending;do{if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>s&&(e.adler=o(e.adler,i.pending_buf,i.pending-s,s)),F(e),s=i.pending,i.pending===i.pending_buf_size)){l=1;break}l=i.gzindex<i.gzhead.comment.length?255&i.gzhead.comment.charCodeAt(i.gzindex++):0,N(i,l)}while(0!==l);i.gzhead.hcrc&&i.pending>s&&(e.adler=o(e.adler,i.pending_buf,i.pending-s,s)),0===l&&(i.status=103)}else i.status=103;if(103===i.status&&(i.gzhead.hcrc?(i.pending+2>i.pending_buf_size&&F(e),i.pending+2<=i.pending_buf_size&&(N(i,255&e.adler),N(i,e.adler>>8&255),e.adler=0,i.status=E)):i.status=E),0!==i.pending){if(F(e),0===e.avail_out)return i.last_flush=-1,c}else if(0===e.avail_in&&R(t)<=R(r)&&t!==h)return B(e,-5);if(666===i.status&&0!==e.avail_in)return B(e,-5);if(0!==e.avail_in||0!==i.lookahead||t!==u&&666!==i.status){var p=2===i.strategy?function(e,t){for(var r;;){if(0===e.lookahead&&(j(e),0===e.lookahead)){if(t===u)return A;break}if(e.match_length=0,r=a._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(P(e,!1),0===e.strm.avail_out))return A}return e.insert=0,t===h?(P(e,!0),0===e.strm.avail_out?I:T):e.last_lit&&(P(e,!1),0===e.strm.avail_out)?A:O}(i,t):3===i.strategy?function(e,t){for(var r,n,i,s,o=e.window;;){if(e.lookahead<=S){if(j(e),e.lookahead<=S&&t===u)return A;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=x&&0<e.strstart&&(n=o[i=e.strstart-1])===o[++i]&&n===o[++i]&&n===o[++i]){s=e.strstart+S;do{}while(n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&i<s);e.match_length=S-(s-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=x?(r=a._tr_tally(e,1,e.match_length-x),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=a._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(P(e,!1),0===e.strm.avail_out))return A}return e.insert=0,t===h?(P(e,!0),0===e.strm.avail_out?I:T):e.last_lit&&(P(e,!1),0===e.strm.avail_out)?A:O}(i,t):n[i.level].func(i,t);if(p!==I&&p!==T||(i.status=666),p===A||p===I)return 0===e.avail_out&&(i.last_flush=-1),c;if(p===O&&(1===t?a._tr_align(i):5!==t&&(a._tr_stored_block(i,0,0,!1),3===t&&(D(i.head),0===i.lookahead&&(i.strstart=0,i.block_start=0,i.insert=0))),F(e),0===e.avail_out))return i.last_flush=-1,c}return t!==h?c:i.wrap<=0?1:(2===i.wrap?(N(i,255&e.adler),N(i,e.adler>>8&255),N(i,e.adler>>16&255),N(i,e.adler>>24&255),N(i,255&e.total_in),N(i,e.total_in>>8&255),N(i,e.total_in>>16&255),N(i,e.total_in>>24&255)):(L(i,e.adler>>>16),L(i,65535&e.adler)),F(e),0<i.wrap&&(i.wrap=-i.wrap),0!==i.pending?c:1)},r.deflateEnd=function(e){var t;return e&&e.state?(t=e.state.status)!==C&&69!==t&&73!==t&&91!==t&&103!==t&&t!==E&&666!==t?B(e,f):(e.state=null,t===E?B(e,-3):c):f},r.deflateSetDictionary=function(e,t){var r,n,a,o,l,u,h,d,p=t.length;if(!e||!e.state)return f;if(2===(o=(r=e.state).wrap)||1===o&&r.status!==C||r.lookahead)return f;for(1===o&&(e.adler=s(e.adler,t,p,0)),r.wrap=0,p>=r.w_size&&(0===o&&(D(r.head),r.strstart=0,r.block_start=0,r.insert=0),d=new i.Buf8(r.w_size),i.arraySet(d,t,p-r.w_size,r.w_size,0),t=d,p=r.w_size),l=e.avail_in,u=e.next_in,h=e.input,e.avail_in=p,e.next_in=0,e.input=t,j(r);r.lookahead>=x;){for(n=r.strstart,a=r.lookahead-(x-1);r.ins_h=(r.ins_h<<r.hash_shift^r.window[n+x-1])&r.hash_mask,r.prev[n&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=n,n++,--a;);r.strstart=n,r.lookahead=x-1,j(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=x-1,r.match_available=0,e.next_in=u,e.input=h,e.avail_in=l,r.wrap=o,c},r.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./messages":51,"./trees":52}],47:[function(e,t,r){t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],48:[function(e,t,r){t.exports=function(e,t){var r,n,i,a,s,o,l,u,h,c,f,d,p,m,_,g,y,b,v,w,k,x,S,z,C;r=e.state,n=e.next_in,z=e.input,i=n+(e.avail_in-5),a=e.next_out,C=e.output,s=a-(t-e.avail_out),o=a+(e.avail_out-257),l=r.dmax,u=r.wsize,h=r.whave,c=r.wnext,f=r.window,d=r.hold,p=r.bits,m=r.lencode,_=r.distcode,g=(1<<r.lenbits)-1,y=(1<<r.distbits)-1;e:do{p<15&&(d+=z[n++]<<p,p+=8,d+=z[n++]<<p,p+=8),b=m[d&g];t:for(;;){if(d>>>=v=b>>>24,p-=v,0==(v=b>>>16&255))C[a++]=65535&b;else{if(!(16&v)){if(!(64&v)){b=m[(65535&b)+(d&(1<<v)-1)];continue t}if(32&v){r.mode=12;break e}e.msg="invalid literal/length code",r.mode=30;break e}w=65535&b,(v&=15)&&(p<v&&(d+=z[n++]<<p,p+=8),w+=d&(1<<v)-1,d>>>=v,p-=v),p<15&&(d+=z[n++]<<p,p+=8,d+=z[n++]<<p,p+=8),b=_[d&y];r:for(;;){if(d>>>=v=b>>>24,p-=v,!(16&(v=b>>>16&255))){if(!(64&v)){b=_[(65535&b)+(d&(1<<v)-1)];continue r}e.msg="invalid distance code",r.mode=30;break e}if(k=65535&b,p<(v&=15)&&(d+=z[n++]<<p,(p+=8)<v&&(d+=z[n++]<<p,p+=8)),l<(k+=d&(1<<v)-1)){e.msg="invalid distance too far back",r.mode=30;break e}if(d>>>=v,p-=v,(v=a-s)<k){if(h<(v=k-v)&&r.sane){e.msg="invalid distance too far back",r.mode=30;break e}if(S=f,(x=0)===c){if(x+=u-v,v<w){for(w-=v;C[a++]=f[x++],--v;);x=a-k,S=C}}else if(c<v){if(x+=u+c-v,(v-=c)<w){for(w-=v;C[a++]=f[x++],--v;);if(x=0,c<w){for(w-=v=c;C[a++]=f[x++],--v;);x=a-k,S=C}}}else if(x+=c-v,v<w){for(w-=v;C[a++]=f[x++],--v;);x=a-k,S=C}for(;2<w;)C[a++]=S[x++],C[a++]=S[x++],C[a++]=S[x++],w-=3;w&&(C[a++]=S[x++],1<w&&(C[a++]=S[x++]))}else{for(x=a-k;C[a++]=C[x++],C[a++]=C[x++],C[a++]=C[x++],2<(w-=3););w&&(C[a++]=C[x++],1<w&&(C[a++]=C[x++]))}break}}break}}while(n<i&&a<o);n-=w=p>>3,d&=(1<<(p-=w<<3))-1,e.next_in=n,e.next_out=a,e.avail_in=n<i?i-n+5:5-(n-i),e.avail_out=a<o?o-a+257:257-(a-o),r.hold=d,r.bits=p}},{}],49:[function(e,t,r){var n=e("../utils/common"),i=e("./adler32"),a=e("./crc32"),s=e("./inffast"),o=e("./inftrees"),l=1,u=2,h=0,c=-2,f=1,d=852,p=592;function m(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function _(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new n.Buf16(320),this.work=new n.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function g(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=f,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new n.Buf32(d),t.distcode=t.distdyn=new n.Buf32(p),t.sane=1,t.back=-1,h):c}function y(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,g(e)):c}function b(e,t){var r,n;return e&&e.state?(n=e.state,t<0?(r=0,t=-t):(r=1+(t>>4),t<48&&(t&=15)),t&&(t<8||15<t)?c:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,y(e))):c}function v(e,t){var r,n;return e?(n=new _,(e.state=n).window=null,(r=b(e,t))!==h&&(e.state=null),r):c}var w,k,x=!0;function S(e){if(x){var t;for(w=new n.Buf32(512),k=new n.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(o(l,e.lens,0,288,w,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;o(u,e.lens,0,32,k,0,e.work,{bits:5}),x=!1}e.lencode=w,e.lenbits=9,e.distcode=k,e.distbits=5}function z(e,t,r,i){var a,s=e.state;return null===s.window&&(s.wsize=1<<s.wbits,s.wnext=0,s.whave=0,s.window=new n.Buf8(s.wsize)),i>=s.wsize?(n.arraySet(s.window,t,r-s.wsize,s.wsize,0),s.wnext=0,s.whave=s.wsize):(i<(a=s.wsize-s.wnext)&&(a=i),n.arraySet(s.window,t,r-i,a,s.wnext),(i-=a)?(n.arraySet(s.window,t,r-i,i,0),s.wnext=i,s.whave=s.wsize):(s.wnext+=a,s.wnext===s.wsize&&(s.wnext=0),s.whave<s.wsize&&(s.whave+=a))),0}r.inflateReset=y,r.inflateReset2=b,r.inflateResetKeep=g,r.inflateInit=function(e){return v(e,15)},r.inflateInit2=v,r.inflate=function(e,t){var r,d,p,_,g,y,b,v,w,k,x,C,E,A,O,I,T,B,R,D,F,P,N,L,U=0,j=new n.Buf8(4),M=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return c;12===(r=e.state).mode&&(r.mode=13),g=e.next_out,p=e.output,b=e.avail_out,_=e.next_in,d=e.input,y=e.avail_in,v=r.hold,w=r.bits,k=y,x=b,P=h;e:for(;;)switch(r.mode){case f:if(0===r.wrap){r.mode=13;break}for(;w<16;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}if(2&r.wrap&&35615===v){j[r.check=0]=255&v,j[1]=v>>>8&255,r.check=a(r.check,j,2,0),w=v=0,r.mode=2;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&v)<<8)+(v>>8))%31){e.msg="incorrect header check",r.mode=30;break}if(8!=(15&v)){e.msg="unknown compression method",r.mode=30;break}if(w-=4,F=8+(15&(v>>>=4)),0===r.wbits)r.wbits=F;else if(F>r.wbits){e.msg="invalid window size",r.mode=30;break}r.dmax=1<<F,e.adler=r.check=1,r.mode=512&v?10:12,w=v=0;break;case 2:for(;w<16;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}if(r.flags=v,8!=(255&r.flags)){e.msg="unknown compression method",r.mode=30;break}if(57344&r.flags){e.msg="unknown header flags set",r.mode=30;break}r.head&&(r.head.text=v>>8&1),512&r.flags&&(j[0]=255&v,j[1]=v>>>8&255,r.check=a(r.check,j,2,0)),w=v=0,r.mode=3;case 3:for(;w<32;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}r.head&&(r.head.time=v),512&r.flags&&(j[0]=255&v,j[1]=v>>>8&255,j[2]=v>>>16&255,j[3]=v>>>24&255,r.check=a(r.check,j,4,0)),w=v=0,r.mode=4;case 4:for(;w<16;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}r.head&&(r.head.xflags=255&v,r.head.os=v>>8),512&r.flags&&(j[0]=255&v,j[1]=v>>>8&255,r.check=a(r.check,j,2,0)),w=v=0,r.mode=5;case 5:if(1024&r.flags){for(;w<16;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}r.length=v,r.head&&(r.head.extra_len=v),512&r.flags&&(j[0]=255&v,j[1]=v>>>8&255,r.check=a(r.check,j,2,0)),w=v=0}else r.head&&(r.head.extra=null);r.mode=6;case 6:if(1024&r.flags&&(y<(C=r.length)&&(C=y),C&&(r.head&&(F=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),n.arraySet(r.head.extra,d,_,C,F)),512&r.flags&&(r.check=a(r.check,d,C,_)),y-=C,_+=C,r.length-=C),r.length))break e;r.length=0,r.mode=7;case 7:if(2048&r.flags){if(0===y)break e;for(C=0;F=d[_+C++],r.head&&F&&r.length<65536&&(r.head.name+=String.fromCharCode(F)),F&&C<y;);if(512&r.flags&&(r.check=a(r.check,d,C,_)),y-=C,_+=C,F)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=8;case 8:if(4096&r.flags){if(0===y)break e;for(C=0;F=d[_+C++],r.head&&F&&r.length<65536&&(r.head.comment+=String.fromCharCode(F)),F&&C<y;);if(512&r.flags&&(r.check=a(r.check,d,C,_)),y-=C,_+=C,F)break e}else r.head&&(r.head.comment=null);r.mode=9;case 9:if(512&r.flags){for(;w<16;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}if(v!==(65535&r.check)){e.msg="header crc mismatch",r.mode=30;break}w=v=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=12;break;case 10:for(;w<32;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}e.adler=r.check=m(v),w=v=0,r.mode=11;case 11:if(0===r.havedict)return e.next_out=g,e.avail_out=b,e.next_in=_,e.avail_in=y,r.hold=v,r.bits=w,2;e.adler=r.check=1,r.mode=12;case 12:if(5===t||6===t)break e;case 13:if(r.last){v>>>=7&w,w-=7&w,r.mode=27;break}for(;w<3;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}switch(r.last=1&v,w-=1,3&(v>>>=1)){case 0:r.mode=14;break;case 1:if(S(r),r.mode=20,6!==t)break;v>>>=2,w-=2;break e;case 2:r.mode=17;break;case 3:e.msg="invalid block type",r.mode=30}v>>>=2,w-=2;break;case 14:for(v>>>=7&w,w-=7&w;w<32;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}if((65535&v)!=(v>>>16^65535)){e.msg="invalid stored block lengths",r.mode=30;break}if(r.length=65535&v,w=v=0,r.mode=15,6===t)break e;case 15:r.mode=16;case 16:if(C=r.length){if(y<C&&(C=y),b<C&&(C=b),0===C)break e;n.arraySet(p,d,_,C,g),y-=C,_+=C,b-=C,g+=C,r.length-=C;break}r.mode=12;break;case 17:for(;w<14;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}if(r.nlen=257+(31&v),v>>>=5,w-=5,r.ndist=1+(31&v),v>>>=5,w-=5,r.ncode=4+(15&v),v>>>=4,w-=4,286<r.nlen||30<r.ndist){e.msg="too many length or distance symbols",r.mode=30;break}r.have=0,r.mode=18;case 18:for(;r.have<r.ncode;){for(;w<3;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}r.lens[M[r.have++]]=7&v,v>>>=3,w-=3}for(;r.have<19;)r.lens[M[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,N={bits:r.lenbits},P=o(0,r.lens,0,19,r.lencode,0,r.work,N),r.lenbits=N.bits,P){e.msg="invalid code lengths set",r.mode=30;break}r.have=0,r.mode=19;case 19:for(;r.have<r.nlen+r.ndist;){for(;I=(U=r.lencode[v&(1<<r.lenbits)-1])>>>16&255,T=65535&U,!((O=U>>>24)<=w);){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}if(T<16)v>>>=O,w-=O,r.lens[r.have++]=T;else{if(16===T){for(L=O+2;w<L;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}if(v>>>=O,w-=O,0===r.have){e.msg="invalid bit length repeat",r.mode=30;break}F=r.lens[r.have-1],C=3+(3&v),v>>>=2,w-=2}else if(17===T){for(L=O+3;w<L;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}w-=O,F=0,C=3+(7&(v>>>=O)),v>>>=3,w-=3}else{for(L=O+7;w<L;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}w-=O,F=0,C=11+(127&(v>>>=O)),v>>>=7,w-=7}if(r.have+C>r.nlen+r.ndist){e.msg="invalid bit length repeat",r.mode=30;break}for(;C--;)r.lens[r.have++]=F}}if(30===r.mode)break;if(0===r.lens[256]){e.msg="invalid code -- missing end-of-block",r.mode=30;break}if(r.lenbits=9,N={bits:r.lenbits},P=o(l,r.lens,0,r.nlen,r.lencode,0,r.work,N),r.lenbits=N.bits,P){e.msg="invalid literal/lengths set",r.mode=30;break}if(r.distbits=6,r.distcode=r.distdyn,N={bits:r.distbits},P=o(u,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,N),r.distbits=N.bits,P){e.msg="invalid distances set",r.mode=30;break}if(r.mode=20,6===t)break e;case 20:r.mode=21;case 21:if(6<=y&&258<=b){e.next_out=g,e.avail_out=b,e.next_in=_,e.avail_in=y,r.hold=v,r.bits=w,s(e,x),g=e.next_out,p=e.output,b=e.avail_out,_=e.next_in,d=e.input,y=e.avail_in,v=r.hold,w=r.bits,12===r.mode&&(r.back=-1);break}for(r.back=0;I=(U=r.lencode[v&(1<<r.lenbits)-1])>>>16&255,T=65535&U,!((O=U>>>24)<=w);){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}if(I&&!(240&I)){for(B=O,R=I,D=T;I=(U=r.lencode[D+((v&(1<<B+R)-1)>>B)])>>>16&255,T=65535&U,!(B+(O=U>>>24)<=w);){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}v>>>=B,w-=B,r.back+=B}if(v>>>=O,w-=O,r.back+=O,r.length=T,0===I){r.mode=26;break}if(32&I){r.back=-1,r.mode=12;break}if(64&I){e.msg="invalid literal/length code",r.mode=30;break}r.extra=15&I,r.mode=22;case 22:if(r.extra){for(L=r.extra;w<L;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}r.length+=v&(1<<r.extra)-1,v>>>=r.extra,w-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=23;case 23:for(;I=(U=r.distcode[v&(1<<r.distbits)-1])>>>16&255,T=65535&U,!((O=U>>>24)<=w);){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}if(!(240&I)){for(B=O,R=I,D=T;I=(U=r.distcode[D+((v&(1<<B+R)-1)>>B)])>>>16&255,T=65535&U,!(B+(O=U>>>24)<=w);){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}v>>>=B,w-=B,r.back+=B}if(v>>>=O,w-=O,r.back+=O,64&I){e.msg="invalid distance code",r.mode=30;break}r.offset=T,r.extra=15&I,r.mode=24;case 24:if(r.extra){for(L=r.extra;w<L;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}r.offset+=v&(1<<r.extra)-1,v>>>=r.extra,w-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg="invalid distance too far back",r.mode=30;break}r.mode=25;case 25:if(0===b)break e;if(C=x-b,r.offset>C){if((C=r.offset-C)>r.whave&&r.sane){e.msg="invalid distance too far back",r.mode=30;break}E=C>r.wnext?(C-=r.wnext,r.wsize-C):r.wnext-C,C>r.length&&(C=r.length),A=r.window}else A=p,E=g-r.offset,C=r.length;for(b<C&&(C=b),b-=C,r.length-=C;p[g++]=A[E++],--C;);0===r.length&&(r.mode=21);break;case 26:if(0===b)break e;p[g++]=r.length,b--,r.mode=21;break;case 27:if(r.wrap){for(;w<32;){if(0===y)break e;y--,v|=d[_++]<<w,w+=8}if(x-=b,e.total_out+=x,r.total+=x,x&&(e.adler=r.check=r.flags?a(r.check,p,x,g-x):i(r.check,p,x,g-x)),x=b,(r.flags?v:m(v))!==r.check){e.msg="incorrect data check",r.mode=30;break}w=v=0}r.mode=28;case 28:if(r.wrap&&r.flags){for(;w<32;){if(0===y)break e;y--,v+=d[_++]<<w,w+=8}if(v!==(4294967295&r.total)){e.msg="incorrect length check",r.mode=30;break}w=v=0}r.mode=29;case 29:P=1;break e;case 30:P=-3;break e;case 31:return-4;default:return c}return e.next_out=g,e.avail_out=b,e.next_in=_,e.avail_in=y,r.hold=v,r.bits=w,(r.wsize||x!==e.avail_out&&r.mode<30&&(r.mode<27||4!==t))&&z(e,e.output,e.next_out,x-e.avail_out)?(r.mode=31,-4):(k-=e.avail_in,x-=e.avail_out,e.total_in+=k,e.total_out+=x,r.total+=x,r.wrap&&x&&(e.adler=r.check=r.flags?a(r.check,p,x,e.next_out-x):i(r.check,p,x,e.next_out-x)),e.data_type=r.bits+(r.last?64:0)+(12===r.mode?128:0)+(20===r.mode||15===r.mode?256:0),(0==k&&0===x||4===t)&&P===h&&(P=-5),P)},r.inflateEnd=function(e){if(!e||!e.state)return c;var t=e.state;return t.window&&(t.window=null),e.state=null,h},r.inflateGetHeader=function(e,t){var r;return e&&e.state&&2&(r=e.state).wrap?((r.head=t).done=!1,h):c},r.inflateSetDictionary=function(e,t){var r,n=t.length;return e&&e.state?0!==(r=e.state).wrap&&11!==r.mode?c:11===r.mode&&i(1,t,n,0)!==r.check?-3:z(e,t,n,n)?(r.mode=31,-4):(r.havedict=1,h):c},r.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./inffast":48,"./inftrees":50}],50:[function(e,t,r){var n=e("../utils/common"),i=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],a=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],s=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],o=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(e,t,r,l,u,h,c,f){var d,p,m,_,g,y,b,v,w,k=f.bits,x=0,S=0,z=0,C=0,E=0,A=0,O=0,I=0,T=0,B=0,R=null,D=0,F=new n.Buf16(16),P=new n.Buf16(16),N=null,L=0;for(x=0;x<=15;x++)F[x]=0;for(S=0;S<l;S++)F[t[r+S]]++;for(E=k,C=15;1<=C&&0===F[C];C--);if(C<E&&(E=C),0===C)return u[h++]=20971520,u[h++]=20971520,f.bits=1,0;for(z=1;z<C&&0===F[z];z++);for(E<z&&(E=z),x=I=1;x<=15;x++)if(I<<=1,(I-=F[x])<0)return-1;if(0<I&&(0===e||1!==C))return-1;for(P[1]=0,x=1;x<15;x++)P[x+1]=P[x]+F[x];for(S=0;S<l;S++)0!==t[r+S]&&(c[P[t[r+S]]++]=S);if(y=0===e?(R=N=c,19):1===e?(R=i,D-=257,N=a,L-=257,256):(R=s,N=o,-1),x=z,g=h,O=S=B=0,m=-1,_=(T=1<<(A=E))-1,1===e&&852<T||2===e&&592<T)return 1;for(;;){for(b=x-O,w=c[S]<y?(v=0,c[S]):c[S]>y?(v=N[L+c[S]],R[D+c[S]]):(v=96,0),d=1<<x-O,z=p=1<<A;u[g+(B>>O)+(p-=d)]=b<<24|v<<16|w,0!==p;);for(d=1<<x-1;B&d;)d>>=1;if(0!==d?(B&=d-1,B+=d):B=0,S++,0==--F[x]){if(x===C)break;x=t[r+c[S]]}if(E<x&&(B&_)!==m){for(0===O&&(O=E),g+=z,I=1<<(A=x-O);A+O<C&&!((I-=F[A+O])<=0);)A++,I<<=1;if(T+=1<<A,1===e&&852<T||2===e&&592<T)return 1;u[m=B&_]=E<<24|A<<16|g-h}}return 0!==B&&(u[g+B]=x-O<<24|64<<16),f.bits=E,0}},{"../utils/common":41}],51:[function(e,t,r){t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],52:[function(e,t,r){var n=e("../utils/common"),i=0,a=1;function s(e){for(var t=e.length;0<=--t;)e[t]=0}var o=0,l=29,u=256,h=u+1+l,c=30,f=19,d=2*h+1,p=15,m=16,_=7,g=256,y=16,b=17,v=18,w=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],k=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],x=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],S=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],z=new Array(2*(h+2));s(z);var C=new Array(2*c);s(C);var E=new Array(512);s(E);var A=new Array(256);s(A);var O=new Array(l);s(O);var I,T,B,R=new Array(c);function D(e,t,r,n,i){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=e&&e.length}function F(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function P(e){return e<256?E[e]:E[256+(e>>>7)]}function N(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function L(e,t,r){e.bi_valid>m-r?(e.bi_buf|=t<<e.bi_valid&65535,N(e,e.bi_buf),e.bi_buf=t>>m-e.bi_valid,e.bi_valid+=r-m):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function U(e,t,r){L(e,r[2*t],r[2*t+1])}function j(e,t){for(var r=0;r|=1&e,e>>>=1,r<<=1,0<--t;);return r>>>1}function M(e,t,r){var n,i,a=new Array(p+1),s=0;for(n=1;n<=p;n++)a[n]=s=s+r[n-1]<<1;for(i=0;i<=t;i++){var o=e[2*i+1];0!==o&&(e[2*i]=j(a[o]++,o))}}function Z(e){var t;for(t=0;t<h;t++)e.dyn_ltree[2*t]=0;for(t=0;t<c;t++)e.dyn_dtree[2*t]=0;for(t=0;t<f;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*g]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function W(e){8<e.bi_valid?N(e,e.bi_buf):0<e.bi_valid&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function G(e,t,r,n){var i=2*t,a=2*r;return e[i]<e[a]||e[i]===e[a]&&n[t]<=n[r]}function H(e,t,r){for(var n=e.heap[r],i=r<<1;i<=e.heap_len&&(i<e.heap_len&&G(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!G(t,n,e.heap[i],e.depth));)e.heap[r]=e.heap[i],r=i,i<<=1;e.heap[r]=n}function K(e,t,r){var n,i,a,s,o=0;if(0!==e.last_lit)for(;n=e.pending_buf[e.d_buf+2*o]<<8|e.pending_buf[e.d_buf+2*o+1],i=e.pending_buf[e.l_buf+o],o++,0===n?U(e,i,t):(U(e,(a=A[i])+u+1,t),0!==(s=w[a])&&L(e,i-=O[a],s),U(e,a=P(--n),r),0!==(s=k[a])&&L(e,n-=R[a],s)),o<e.last_lit;);U(e,g,t)}function X(e,t){var r,n,i,a=t.dyn_tree,s=t.stat_desc.static_tree,o=t.stat_desc.has_stree,l=t.stat_desc.elems,u=-1;for(e.heap_len=0,e.heap_max=d,r=0;r<l;r++)0!==a[2*r]?(e.heap[++e.heap_len]=u=r,e.depth[r]=0):a[2*r+1]=0;for(;e.heap_len<2;)a[2*(i=e.heap[++e.heap_len]=u<2?++u:0)]=1,e.depth[i]=0,e.opt_len--,o&&(e.static_len-=s[2*i+1]);for(t.max_code=u,r=e.heap_len>>1;1<=r;r--)H(e,a,r);for(i=l;r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],H(e,a,1),n=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=n,a[2*i]=a[2*r]+a[2*n],e.depth[i]=(e.depth[r]>=e.depth[n]?e.depth[r]:e.depth[n])+1,a[2*r+1]=a[2*n+1]=i,e.heap[1]=i++,H(e,a,1),2<=e.heap_len;);e.heap[--e.heap_max]=e.heap[1],function(e,t){var r,n,i,a,s,o,l=t.dyn_tree,u=t.max_code,h=t.stat_desc.static_tree,c=t.stat_desc.has_stree,f=t.stat_desc.extra_bits,m=t.stat_desc.extra_base,_=t.stat_desc.max_length,g=0;for(a=0;a<=p;a++)e.bl_count[a]=0;for(l[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<d;r++)_<(a=l[2*l[2*(n=e.heap[r])+1]+1]+1)&&(a=_,g++),l[2*n+1]=a,u<n||(e.bl_count[a]++,s=0,m<=n&&(s=f[n-m]),o=l[2*n],e.opt_len+=o*(a+s),c&&(e.static_len+=o*(h[2*n+1]+s)));if(0!==g){do{for(a=_-1;0===e.bl_count[a];)a--;e.bl_count[a]--,e.bl_count[a+1]+=2,e.bl_count[_]--,g-=2}while(0<g);for(a=_;0!==a;a--)for(n=e.bl_count[a];0!==n;)u<(i=e.heap[--r])||(l[2*i+1]!==a&&(e.opt_len+=(a-l[2*i+1])*l[2*i],l[2*i+1]=a),n--)}}(e,t),M(a,u,e.bl_count)}function Y(e,t,r){var n,i,a=-1,s=t[1],o=0,l=7,u=4;for(0===s&&(l=138,u=3),t[2*(r+1)+1]=65535,n=0;n<=r;n++)i=s,s=t[2*(n+1)+1],++o<l&&i===s||(o<u?e.bl_tree[2*i]+=o:0!==i?(i!==a&&e.bl_tree[2*i]++,e.bl_tree[2*y]++):o<=10?e.bl_tree[2*b]++:e.bl_tree[2*v]++,a=i,u=(o=0)===s?(l=138,3):i===s?(l=6,3):(l=7,4))}function q(e,t,r){var n,i,a=-1,s=t[1],o=0,l=7,u=4;for(0===s&&(l=138,u=3),n=0;n<=r;n++)if(i=s,s=t[2*(n+1)+1],!(++o<l&&i===s)){if(o<u)for(;U(e,i,e.bl_tree),0!=--o;);else 0!==i?(i!==a&&(U(e,i,e.bl_tree),o--),U(e,y,e.bl_tree),L(e,o-3,2)):o<=10?(U(e,b,e.bl_tree),L(e,o-3,3)):(U(e,v,e.bl_tree),L(e,o-11,7));a=i,u=(o=0)===s?(l=138,3):i===s?(l=6,3):(l=7,4)}}s(R);var J=!1;function V(e,t,r,i){L(e,(o<<1)+(i?1:0),3),function(e,t,r){W(e),N(e,r),N(e,~r),n.arraySet(e.pending_buf,e.window,t,r,e.pending),e.pending+=r}(e,t,r)}r._tr_init=function(e){J||(function(){var e,t,r,n,i,a=new Array(p+1);for(n=r=0;n<l-1;n++)for(O[n]=r,e=0;e<1<<w[n];e++)A[r++]=n;for(A[r-1]=n,n=i=0;n<16;n++)for(R[n]=i,e=0;e<1<<k[n];e++)E[i++]=n;for(i>>=7;n<c;n++)for(R[n]=i<<7,e=0;e<1<<k[n]-7;e++)E[256+i++]=n;for(t=0;t<=p;t++)a[t]=0;for(e=0;e<=143;)z[2*e+1]=8,e++,a[8]++;for(;e<=255;)z[2*e+1]=9,e++,a[9]++;for(;e<=279;)z[2*e+1]=7,e++,a[7]++;for(;e<=287;)z[2*e+1]=8,e++,a[8]++;for(M(z,h+1,a),e=0;e<c;e++)C[2*e+1]=5,C[2*e]=j(e,5);I=new D(z,w,u+1,h,p),T=new D(C,k,0,c,p),B=new D(new Array(0),x,0,f,_)}(),J=!0),e.l_desc=new F(e.dyn_ltree,I),e.d_desc=new F(e.dyn_dtree,T),e.bl_desc=new F(e.bl_tree,B),e.bi_buf=0,e.bi_valid=0,Z(e)},r._tr_stored_block=V,r._tr_flush_block=function(e,t,r,n){var s,o,l=0;0<e.level?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,r=4093624447;for(t=0;t<=31;t++,r>>>=1)if(1&r&&0!==e.dyn_ltree[2*t])return i;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return a;for(t=32;t<u;t++)if(0!==e.dyn_ltree[2*t])return a;return i}(e)),X(e,e.l_desc),X(e,e.d_desc),l=function(e){var t;for(Y(e,e.dyn_ltree,e.l_desc.max_code),Y(e,e.dyn_dtree,e.d_desc.max_code),X(e,e.bl_desc),t=f-1;3<=t&&0===e.bl_tree[2*S[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),s=e.opt_len+3+7>>>3,(o=e.static_len+3+7>>>3)<=s&&(s=o)):s=o=r+5,r+4<=s&&-1!==t?V(e,t,r,n):4===e.strategy||o===s?(L(e,2+(n?1:0),3),K(e,z,C)):(L(e,4+(n?1:0),3),function(e,t,r,n){var i;for(L(e,t-257,5),L(e,r-1,5),L(e,n-4,4),i=0;i<n;i++)L(e,e.bl_tree[2*S[i]+1],3);q(e,e.dyn_ltree,t-1),q(e,e.dyn_dtree,r-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,l+1),K(e,e.dyn_ltree,e.dyn_dtree)),Z(e),n&&W(e)},r._tr_tally=function(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(A[r]+u+1)]++,e.dyn_dtree[2*P(t)]++),e.last_lit===e.lit_bufsize-1},r._tr_align=function(e){L(e,2,3),U(e,g,z),function(e){16===e.bi_valid?(N(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):8<=e.bi_valid&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)}},{"../utils/common":41}],53:[function(e,t,r){t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],54:[function(e,r,n){(function(e){!function(e,t){if(!e.setImmediate){var r,n,i,a,s=1,o={},l=!1,u=e.document,h=Object.getPrototypeOf&&Object.getPrototypeOf(e);h=h&&h.setTimeout?h:e,r="[object process]"==={}.toString.call(e.process)?function(e){process.nextTick((function(){f(e)}))}:function(){if(e.postMessage&&!e.importScripts){var t=!0,r=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=r,t}}()?(a="setImmediate$"+Math.random()+"$",e.addEventListener?e.addEventListener("message",d,!1):e.attachEvent("onmessage",d),function(t){e.postMessage(a+t,"*")}):e.MessageChannel?((i=new MessageChannel).port1.onmessage=function(e){f(e.data)},function(e){i.port2.postMessage(e)}):u&&"onreadystatechange"in u.createElement("script")?(n=u.documentElement,function(e){var t=u.createElement("script");t.onreadystatechange=function(){f(e),t.onreadystatechange=null,n.removeChild(t),t=null},n.appendChild(t)}):function(e){setTimeout(f,0,e)},h.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var i={callback:e,args:t};return o[s]=i,r(s),s++},h.clearImmediate=c}function c(e){delete o[e]}function f(e){if(l)setTimeout(f,0,e);else{var r=o[e];if(r){l=!0;try{!function(e){var r=e.callback,n=e.args;switch(n.length){case 0:r();break;case 1:r(n[0]);break;case 2:r(n[0],n[1]);break;case 3:r(n[0],n[1],n[2]);break;default:r.apply(t,n)}}(r)}finally{c(e),l=!1}}}}function d(t){t.source===e&&"string"==typeof t.data&&0===t.data.indexOf(a)&&f(+t.data.slice(a.length))}}("undefined"==typeof self?void 0===e?this:e:self)}).call(this,void 0!==t?t:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[10])(10)}(a)),a.exports),o=r(s);const l=/\s*/g,u=/^\s*|\s*$/g,h=/\s+/;function c(e){if(!e||!e.length)return 0;for(var t=0,r=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t)|0;return r}function f(e,t){return e.getElementsByTagName(t)}function d(e,t){return e.getAttribute(t)}function p(e,t){const r=f(e,t);return r.length?r[0]:null}function m(e){for(var t=0,r=[];t<e.length;t++)r[t]=parseFloat(e[t]);return r}function _(e){var t;return e&&(t=e).normalize&&t.normalize(),e&&e.textContent||""}function g(e){return m(e.replace(l,"").split(","))}function y(e){const t=e.replace(u,"").split(h),r=[];for(let e=0;e<t.length;e++)r.push(g(t[e]));return r}let b;function v(e){return void 0!==e.xml?e.xml:b.serializeToString(e)}function w(e){const t={type:"FeatureCollection",features:[]},r={},n={},i={},a=["Polygon","LineString","Point","Track","gx:Track"],s=f(e,"Placemark"),o=f(e,"Style"),l=f(e,"StyleMap");for(let e=0;e<o.length;e++){const t=c(v(o[e])).toString(16);r["#"+d(o[e],"id")]=t,n[t]=o[e]}for(let e=0;e<l.length;e++){r["#"+d(l[e],"id")]=c(v(l[e])).toString(16);const t=f(l[e],"Pair"),n={};for(let e=0;e<t.length;e++)n[_(p(t[e],"key"))]=_(p(t[e],"styleUrl"));i["#"+d(l[e],"id")]=n}for(let e=0;e<s.length;e++)t.features=t.features.concat(w(s[e]));function u(e){let t,r;return"#"===(e=e||"").substr(0,1)&&(e=e.substr(1)),6!==e.length&&3!==e.length||(t=e),8===e.length&&(r=parseInt(e.substr(0,2),16)/255,t="#"+e.substr(6,2)+e.substr(4,2)+e.substr(2,2)),[t,isNaN(r)?void 0:r]}function h(e){let t=f(e,"coord");const r=[],n=[];0===t.length&&(t=f(e,"gx:coord"));for(let e=0;e<t.length;e++)r.push(m(_(t[e]).split(" ")));const i=f(e,"when");for(let e=0;e<i.length;e++)n.push(_(i[e]));return{coords:r,times:n}}function b(e){let t,r,n,i,s;const o=[],l=[];if(p(e,"MultiGeometry"))return b(p(e,"MultiGeometry"));if(p(e,"MultiTrack"))return b(p(e,"MultiTrack"));if(p(e,"gx:MultiTrack"))return b(p(e,"gx:MultiTrack"));for(n=0;n<a.length;n++)if(r=f(e,a[n]),r)for(i=0;i<r.length;i++)if(t=r[i],"Point"===a[n])o.push({type:"Point",coordinates:g(_(p(t,"coordinates")))});else if("LineString"===a[n])o.push({type:"LineString",coordinates:y(_(p(t,"coordinates")))});else if("Polygon"===a[n]){const e=f(t,"LinearRing"),r=[];for(s=0;s<e.length;s++)r.push(y(_(p(e[s],"coordinates"))));o.push({type:"Polygon",coordinates:r})}else if("Track"===a[n]||"gx:Track"===a[n]){const e=h(t);o.push({type:"LineString",coordinates:e.coords}),e.times.length&&l.push(e.times)}return{geoms:o,coordTimes:l}}function w(e){const t=b(e);let a;const s={},o=_(p(e,"name"));let l=_(p(e,"styleUrl"));const h=_(p(e,"description")),c=p(e,"TimeSpan"),m=p(e,"TimeStamp"),g=p(e,"ExtendedData");let y=p(e,"LineStyle"),v=p(e,"PolyStyle");const w=p(e,"visibility");if(!t.geoms.length)return[];if(o&&(s.name=o),l){"#"!==l[0]&&(l="#"+l),s.styleUrl=l,r[l]&&(s.styleHash=r[l]),i[l]&&(s.styleMapHash=i[l],s.styleHash=r[i[l].normal]);const e=n[s.styleHash];e&&(y||(y=p(e,"LineStyle")),v||(v=p(e,"PolyStyle")))}if(h&&(s.description=h),c){const e=_(p(c,"begin")),t=_(p(c,"end"));s.timespan={begin:e,end:t}}if(m&&(s.timestamp=_(p(m,"when"))),y){const e=u(_(p(y,"color"))),t=e[0],r=e[1],n=parseFloat(_(p(y,"width")));t&&(s.stroke=t),isNaN(r)||(s["stroke-opacity"]=r),isNaN(n)||(s["stroke-width"]=n)}if(v){const e=u(_(p(v,"color"))),t=e[0],r=e[1],n=_(p(v,"fill")),i=_(p(v,"outline"));t&&(s.fill=t),isNaN(r)||(s["fill-opacity"]=r),n&&(s["fill-opacity"]="1"===n?s["fill-opacity"]||1:0),i&&(s["stroke-opacity"]="1"===i?s["stroke-opacity"]||1:0)}if(g){const e=f(g,"Data"),t=f(g,"SimpleData");for(a=0;a<e.length;a++){const t=e[a].getAttribute("name");let r=_(p(e[a],"value"));try{r=JSON.parse(r)}catch(e){}s[t]=r}for(a=0;a<t.length;a++){const e=t[a].getAttribute("name");let r=_(t[a]);try{r=JSON.parse(r)}catch(e){}s[e]=r}}w&&(s.visibility=_(w)),t.coordTimes.length&&(s.coordTimes=1===t.coordTimes.length?t.coordTimes[0]:t.coordTimes);const k={type:"Feature",geometry:1===t.geoms.length?t.geoms[0]:{type:"GeometryCollection",geometries:t.geoms},properties:s};return d(e,"id")&&(k.id=d(e,"id")),[k]}return t}function k(e,t){return'<?xml version="1.0" encoding="UTF-8"?>'+T("kml",T("Document",function(e){return void 0!==e.documentName?T("name",e.documentName):""}(t=t||{documentName:void 0,documentDescription:void 0,name:"name",description:"description",simplestyle:!1,timestamp:"timestamp"})+function(e){return void 0!==e.documentDescription?T("description",e.documentDescription):""}(t)+function(e,t){if(!e.type)return"";const r=[];switch(e.type){case"FeatureCollection":return e.features?e.features.map(x(t,r)).join(""):"";case"Feature":return x(t,r)(e);default:return x(t,r)({type:"Feature",geometry:e,properties:{}})}}(e,t)),[["xmlns","http://www.opengis.net/kml/2.2"]])}function x(e,t){return function(r){if(!r.properties||!S.valid(r.geometry))return"";const n=S.any(r.geometry);if(!n)return"";let i="",a="";if(e.simplestyle){const e=function(e){let t="";e["marker-symbol"]&&(t=t+"ms"+e["marker-symbol"]);e["marker-color"]&&(t=t+"mc"+e["marker-color"].replace("#",""));e["marker-size"]&&(t=t+"ms"+e["marker-size"]);e.stroke&&(t=t+"s"+e.stroke.replace("#",""));e["stroke-width"]&&(t=t+"sw"+e["stroke-width"].toString().replace(".",""));e["stroke-opacity"]&&(t=t+"mo"+e["stroke-opacity"].toString().replace(".",""));e.fill&&(t=t+"f"+e.fill.replace("#",""));e["fill-opacity"]&&(t=t+"fo"+e["fill-opacity"].toString().replace(".",""));return t}(r.properties);e&&(S.isPoint(r.geometry)&&function(e){return!!(e["marker-size"]||e["marker-symbol"]||e["marker-color"])}(r.properties)?(-1===t.indexOf(e)&&(i=function(e,t){return T("Style",T("IconStyle",T("Icon",T("href",function(e){return e["marker-symbol"]}(e))))+T("hotSpot","",[["xunits","fraction"],["yunits","fraction"],["x",.5],["y",.5]]),[["id",t]])}(r.properties,e),t.push(e)),a=T("styleUrl","#"+e)):(S.isPolygon(r.geometry)||S.isLine(r.geometry))&&function(e){for(const t in e)if({stroke:!0,"stroke-opacity":!0,"stroke-width":!0,fill:!0,"fill-opacity":!0}[t])return!0}(r.properties)&&(-1===t.indexOf(e)&&(i=function(e,t){const r=T("LineStyle",[T("color",C(e.stroke,e["stroke-opacity"])||"ff555555")+T("width",void 0===e["stroke-width"]?2:e["stroke-width"])]);let n="";(e.fill||e["fill-opacity"])&&(n=T("PolyStyle",[T("color",C(e.fill,e["fill-opacity"])||"88555555")]));return T("Style",r+n,[["id",t]])}(r.properties,e),t.push(e)),a=T("styleUrl","#"+e)))}return i+T("Placemark",function(e,t){return e[t.name]?T("name",A(e[t.name])):""}(r.properties,e)+function(e,t){return e[t.description]?T("description",A(e[t.description])):""}(r.properties,e)+function(e){const t=[];for(const r in e){const n=e[r];E(n)?t.push(`<Data name ="${r}"><value>${JSON.stringify(n)}</value></Data>`):t.push(`<Data name ="${r}"><value>${n}</value></Data>`)}return T("ExtendedData",t.join(""))}(r.properties)+function(e,t){return e[t.timestamp]?T("TimeStamp",T("when",A(e[t.timestamp]))):""}(r.properties,e)+n+a)}}"undefined"!=typeof XMLSerializer?b=new XMLSerializer:"object"!=typeof e||"object"!=typeof process||process.browser||(b=new(require("xmldom").XMLSerializer));const S={Point:function(e){return T("Point",T("coordinates",e.coordinates.join(",")))},LineString:function(e){return T("LineString",T("coordinates",z(e.coordinates)))},Polygon:function(e){if(!e.coordinates.length)return"";const t=e.coordinates[0],r=e.coordinates.slice(1),n=T("outerBoundaryIs",T("LinearRing",T("coordinates",z(t)))),i=r.map((function(e){return T("innerBoundaryIs",T("LinearRing",T("coordinates",z(e))))})).join("");return T("Polygon",n+i)},MultiPoint:function(e){return e.coordinates.length?T("MultiGeometry",e.coordinates.map((function(e){return S.Point({coordinates:e})})).join("")):""},MultiPolygon:function(e){return e.coordinates.length?T("MultiGeometry",e.coordinates.map((function(e){return S.Polygon({coordinates:e})})).join("")):""},MultiLineString:function(e){return e.coordinates.length?T("MultiGeometry",e.coordinates.map((function(e){return S.LineString({coordinates:e})})).join("")):""},GeometryCollection:function(e){return T("MultiGeometry",e.geometries.map(S.any).join(""))},valid:function(e){return e&&e.type&&(e.coordinates||"GeometryCollection"===e.type&&e.geometries&&e.geometries.every(S.valid))},any:function(e){return S[e.type]?S[e.type](e):""},isPoint:function(e){return"Point"===e.type||"MultiPoint"===e.type},isPolygon:function(e){return"Polygon"===e.type||"MultiPolygon"===e.type},isLine:function(e){return"LineString"===e.type||"MultiLineString"===e.type}};function z(e){return e.map((function(e){return e.join(",")})).join(" ")}function C(e,t){if("string"!=typeof e)return"";if(3===(e=e.replace("#","").toLowerCase()).length)e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2];else if(6!==e.length)return"";const r=e[0]+e[1],n=e[2]+e[3],i=e[4]+e[5];let a="ff";return"number"==typeof t&&t>=0&&t<=1&&(a=(255*t).toString(16),a.indexOf(".")>-1&&(a=a.substr(0,a.indexOf("."))),a.length<2&&(a="0"+a)),a+i+n+r}function E(e){return"[object Object]"===Object.prototype.toString.call(e)}function A(e){return e?e.toString().replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"):""}function O(e){return Object.keys(e).length?" "+Object.keys(e).map((function(t){return t+'="'+function(e,t){if(null==e)return;t=(t||"").replace(/[^&"<>\']/g,"");const r="([&\"<>'])".replace(new RegExp("["+t+"]","g"),"");return e.replace(new RegExp(r,"g"),(function(e,t){return I[t]}))}(e[t])+'"'})).join(" "):""}const I={">":"&gt;","<":"&lt;","'":"&apos;",'"':"&quot;","&":"&amp;"};function T(e,t,r){return(Array.isArray(t)||"string"==typeof t)&&(r=t,t={}),Array.isArray(r)&&(r="\n"+r.map((function(e){return"  "+e})).join("\n")+"\n"),"<"+e+O(t)+">"+r+"</"+e+">"}function B(e){if(!e.properties)return;const t=e.properties.style;if(t){if(t.image)return e.properties["marker-symbol"]=t.image,void(t.outlineColor&&(e.properties["marker-color"]=t.outlineColor));t.color&&(e.properties.fill=t.color,t.opacity&&(e.properties["fill-opacity"]=t.opacity)),t.outlineColor&&(e.properties.stroke=t.outlineColor,e.properties["stroke-width"]=t.outlineWidth??1,e.properties["stroke-opacity"]=t.outlineOpacity??t.opacity??1),t.html&&(t.html=function(e){const t=document.createElement("div");null!=t.textContent?t.textContent=e:t.innerText=e;return t.innerHTML}(t.html)),t.closure&&e.geometry?.coordinates?.length>0&&e.geometry.coordinates.push(e.geometry.coordinates[0])}const r=e?.properties?.type;if("polygon"===r){const t=e.geometry.coordinates[0];t?.length>2&&t.push(t[0])}else if("rectangle"===r){const t=e.geometry.coordinates,r=t[0][0],n=t[1][0],i=t[0][1],a=t[1][1];e.geometry={type:"Polygon",coordinates:[[[r,a],[r,i],[n,i],[n,a],[r,a]]]}}}const R=e=>(new DOMParser).parseFromString(e,"application/xml"),D=e=>e.split(".").pop(),F=e=>(new o).loadAsync(e).then((e=>{let t=null;return e.forEach(((e,r)=>{"kml"===D(e)&&null===t&&(t=r.async("string").then(R))})),t}));e.toGeoJSON=function(e,t){if(e){if("string"==typeof(r=e)&&r.constructor===String){const r=D(e);if("kml"===r)return function(e,t){const r={method:"GET",headers:{"Content-Type":"application/xml"},...t};return fetch(e,r).then((e=>e.text())).then((e=>R(e)))}(e,t).then((function(e){return w(e)}));if("kmz"===r)return function(e,t){const r={method:"GET",...t};return fetch(e,r).then((e=>e.blob()))}(e,t).then((function(e){return F(e)})).then((function(e){return w(e)}));{const t=w(R(e));return Promise.resolve(t)}}if(e.getRootNode){const t=w(e);return Promise.resolve(t)}return F(e).then((function(e){return w(e)}));var r}console.warn("toGeoJSON参数不能为空")},e.toKml=function(e,t){return e.features?e.features.forEach((e=>{B(e)})):B(e),k(e,t)}}));
