﻿.layersctl_rMenu {
  position: absolute;
  z-index: 999999;
  display: none;
  box-shadow: 0 1px 7px rgba(0, 0, 0, 0.4);
  -webkit-border-radius: 4px;
  border-radius: 4px;
  padding: 4px 0;
  background-color: #fff;
  cursor: default;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.layersctl_rMenu ul {
  margin: 0px;
  padding: 0px;
}

.layersctl_rMenu ul li {
  display: block;
  color: #222;
  font-size: 12px;
  line-height: 20px;
  text-decoration: none;
  padding: 0 12px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  cursor: default;
  outline: none;
}

/* 滑动条设置 */
.slider {
  width: 50px !important;
  margin-left: 6px;
  margin-top: -7px;
}

.slider-handle {
  top: 4px;
  width: 12px;
  height: 12px;
}

.slider-selection {
  background: rgb(88, 178, 231);
}

/* 滑动条内的小方块样式 */
::-webkit-scrollbar-corner {
  display: none;
}