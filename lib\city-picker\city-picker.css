/* 2016-10-30 17:08:13 | 修改 木遥（微信:  http://marsgis.cn/weixin.html ） */
.city-picker-input {
    opacity: 0 !important;
    top: -9999px;
    left: -9999px;
    position: absolute;
}

.city-picker-span {
    position: relative;
    display: block;
    outline: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    border-bottom: 1px solid #ccc;
   background-color:transparent!important; 
    color: #ccc;
    cursor: pointer;
}

.city-picker-span > .placeholder {
    color: #aaa;
}

.city-picker-span > .arrow {
    position: absolute;
    top: 50%;
    right: 8px;
    width: 10px;
    margin-top: -3px;
    height: 5px;
    /*background: url(../images/drop-arrow.png) -10px -25px no-repeat;*/
}

.city-picker-span.focus,
.city-picker-span.open {
    border-bottom-color: #46A4FF;
}

.city-picker-span.open > .arrow {
    background-position: -10px -10px;
}

.city-picker-span > .title > span {
    color: #fff;
    padding: 5px;
    border-radius: 3px;
}

.city-picker-span > .title > span:hover {
    background-color: rgba(63, 72, 84, 0.9);
}

.city-picker-dropdown {
    position: absolute;
    width: 225px;
    left: -9999px;
    top: -9999px;
    outline: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    z-index: 999999;
    display: none;
    min-width: 240px;
    margin-bottom: 20px;
}

.city-select-wrap {
    /*box-shadow: 0 1px 5px rgba(0, 0, 0, 0.5);*/
}

.city-select-tab {
    border-bottom: 1px solid #ccc;
    background: #f0f0f0;
    font-size: 13px;
}
.dark .city-select-tab {
  background: #087698;
}
    
.city-select-tab > a {
    display: inline-block;
    padding: 8px 22px;
    border-left: 1px solid #ccc;
    border-bottom: 1px solid transparent;
    color: #4D4D4D;
    text-align: center;
    outline: 0;
    text-decoration: none;
    cursor: pointer;
    font-size: 13px;
    margin-bottom: -1px;
}

.city-select-tab > a.active {
   background-color:transparent!important; 
    border-bottom: 1px solid #fff;
    color: #46A4FF;
}

.city-select-tab > a:first-child {
    border-left: none;
}

.city-select-tab > a:last-child.active {
    border-right: 1px solid #ccc;
}

.city-select-content {
    width: 100%;
    min-height: 10px;
    background: rgba(63, 72, 84, 0.9);
    padding: 5px 5px;
	box-sizing: border-box;
}

.city-select {
    font-size: 13px;
}

.city-select dl {
    line-height: 2;
    clear: both;
    padding: 3px 0;
    margin: 0;
}

.city-select dt {
    position: absolute;
    width: 2.5em;
    font-weight: 500;
    text-align: right;
    line-height: 2;
}

.city-select dd {
    margin-left: 0;
    line-height: 2;
}

.city-select.province dd {
    margin-left: 3em;
}

.city-select a {
    display: inline-block;
    padding: 0 10px;
    outline: 0;
    text-decoration: none;
    white-space: nowrap;
    margin-right: 2px;
    text-decoration: none;
    color: #fff;
    cursor: pointer;
}

.city-select a:hover,
.city-select a:focus {
    background-color: rgba(63, 72, 84, 0.9);
    border-radius: 2px;
    color: #46A4FF;
}

.city-select a.active {
    background-color: #46A4FF;
    color: #fff;
    border-radius: 2px;
}
