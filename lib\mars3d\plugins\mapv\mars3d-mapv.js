/**
 * Mars3D平台插件,结合mapv可视化功能插件  mars3d-mapv
 *
 * 版本信息：v3.10.3
 * 编译日期：2025-08-17 12:11
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：火星科技免费公开版 ，2025-07-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d')), (window.mapv || require('mapv'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d', 'mapv'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-mapv"] = {}, global.mars3d, global.mapv));
})(this, (function (exports, mars3d, mapv) { 
'use strict';function _0xe78f(){const _0x26c506=['ug9SEwDVBG','twfWvKXHEwvY','Cg9PBNrFy291BNq','x2nVB3jKAw5HDgvZ','Bw91C2vTB3zL','x2rHDge','Bw91C2vTB3zLrxzLBNq','yMLUzev2zw50','C3rVCefUAwfTyxrPB24','zMvHDhvYzxm','mZyZme9eu1v6Dq','Bw91C2veB3DU','zhjHDW','ntC5otuYzxnyzeLm','D2LUzg93ug9ZAxrPB24','zhjHD0nVBNrLEhq','ndH4Bw9sAg8','CMvTB3zLqwXSrgf0yq','ywjZB2X1Dgu','CMvUzgvY','Aw5PDa','x3bVAw50zxjfDMvUDhm','y2X1C3rLCG','mtKZnZmWqvfNBLPY','BwfWDG','z2v0t3DUuhjVCgvYDhLezxnJCMLWDg9Y','z2v0q29UDgv4Da','BwfW','y2fUDMfZtgf5zxi','6k+35BYv5ywLig1HChyG5BQtia','D2LKDgG','ChjVCgvYDgLLCW','txvSDgLqB2X5z29U','B3b0Aw9UCW','mtC0mZe2s3brv0H5','yMLUza','y2XPy2S','AxnfBMfIBgvKvgLTzq','Cg9PBNrdB3vUDe1HEa','y29UDgv4Da','C2v0','zNjVBurLz3jLzxm','A2v5CW','x29UtwfWq2XPy2S','rg9TvxrPBa','zMLSBfn0EwXL','ywrK','yw5PBwf0B3i','y2fUDMfZ','BwfYCZnKlw1HChy','C3rLCa','Cg9ZDfjLBMrLCG','x29UtwfWtw91C2vnB3zL','C2v0wKLUzgv4','DxbKyxrLrgf0yq','z2XVyMfSq29TCg9ZAxrLt3bLCMf0Aw9U','DhjLzxm','C3rLChnsyw5Nzq','BwfWDKrLChrOvgvZDa','zgf0yvnLDa','BwfYCZnKlw1HChBMJ5lKU7yG5RoO5yAm5OIq5yQF','x2nHy2HLx2v2zw50','C3rVCa','ywrKrgf0yq','CMvTB3zLq2HPBgq','ndyWmhrPqM1WvW','z2XVyMu','Eg1PBG','rwXSAxbZB2LKywXpy2nSDwrLCG','Cg9PBNrLCKv2zw50CW','zgvZDgLUyxrPB24TB3v0','x19WCM90B19F','y2fTzxjH','zgf0yq','zgvMyxvSDa','mta4odmYodvVr3rYrMK','x21HCa','CMDIysGWlcaWlcaWlcaUmsK','B2zM','EKLUzgv4','x21HCfzszw5KzxjLCG','y2X1C3rLCKrHDgftzxq','yxv0BW','DxbKyxrL','y2fTzxjHtw92zuvUza','uMvJDgfUz2XL','x29Utw92zuvUzev2zw50','rgf0yvnLDa','DxbKyxrLq2fSBgjHy2S','rxzLBNruExbL','yw5PBwf0Aw9U','CMvTB3zLrgf0yq','zwXSAxbZB2LK','tg9N','C2nHBgu','Cg9PBNrdB3vUDe1PBG','Bw91C2vnB3zL','BM9Uzq','q2vZAxvT','BwfWDKf1Dg9izwLNAhq','ohnesev0sq','q09mt1jFqLvgrKvsx0jjva','Bwf4q2X1C3rLCLPVB20','oduZmJi3BMTcv3fM','y2XPy2TfDMvUDa','Bwv0Ag9KCW','yw5PBwf0B3jnB3zLC3rHCNrfDMvUDa','y2XLyxi','x3jLC2v0','C2L6zq','zNvUy3rPB24','zgvMAw5LuhjVCgvYDhK','BgvUz3rO','y3jLyxrL','ug9PBNruCMfUCW','BwLUu2L6zq','Cg9ZAxrPB24','ChjVy2vZC0rHDge','zgLZCgXHEq','zgvWDgHuzxn0','Cg9ZAxrPB25xqW','Aw5PDerHDgfsyw5Nzq','ChvZAa','AgvPz2H0','Bgv2zwW','AxngB3jTyxq','C2f2zq','mtqXmJaXndbnzuPJB2u','ywXS','z2v0wM9VBq','Ew1HEa','Bgf5zxi','zM9YrwfJAa','x29Utw92zvn0yxj0rxzLBNq','z2v0','y29VCMrPBMf0zxm','x3nLDe9WDgLVBNniB29R','C3r5Bgu','BwfWDKzPEgvKsgvPz2H0','rMvHDhvYzunVBgXLy3rPB24','Bwf4u2L6zq','z2v0uMvJDgfUz2XL','C2nLBMu'];_0xe78f=function(){return _0x26c506;};return _0xe78f();}(function(_0x2be711,_0x24be20){const _0x581cc5={_0x396a89:0x3d,_0x34a4db:0x165,_0x18ae75:0x168,_0x168b64:0x1b1,_0x5df39e:0x9e},_0x3aeba9={_0x4d6d88:0x4c},_0xea68be=_0x2be711();function _0xef85a8(_0x5a74dc,_0x3acce4){return _0x1553(_0x3acce4- -0x16b,_0x5a74dc);}function _0x5d5931(_0x3e5f69,_0x59d3fe){return _0x1553(_0x3e5f69-_0x3aeba9._0x4d6d88,_0x59d3fe);}while(!![]){try{const _0x47ba53=-parseInt(_0xef85a8(-0x1f,-_0x581cc5._0x396a89))/0x1+-parseInt(_0x5d5931(_0x581cc5._0x34a4db,0x130))/0x2+-parseInt(_0xef85a8(-0x12,-0x55))/0x3*(parseInt(_0x5d5931(0x199,0x1d9))/0x4)+-parseInt(_0x5d5931(0x16f,0x160))/0x5*(parseInt(_0x5d5931(_0x581cc5._0x18ae75,0x17d))/0x6)+parseInt(_0xef85a8(-0x48,-0x14))/0x7+-parseInt(_0x5d5931(0x1bc,_0x581cc5._0x168b64))/0x8*(-parseInt(_0xef85a8(-_0x581cc5._0x5df39e,-0x87))/0x9)+parseInt(_0xef85a8(-0x87,-0x6f))/0xa;if(_0x47ba53===_0x24be20)break;else _0xea68be['push'](_0xea68be['shift']());}catch(_0x4398dc){_0xea68be['push'](_0xea68be['shift']());}}}(_0xe78f,0xdab44));function _interopNamespace(_0x147bb0){const _0x4aee2b={_0x5ac988:0x177},_0x4fdc76={_0x515cd3:0xb0},_0x117652={_0x372be2:0x77},_0x2a8609={_0x46eac2:0x28a};if(_0x147bb0&&_0x147bb0['__esModule'])return _0x147bb0;var _0x3fafeb=Object['create'](null);function _0xd0a2d3(_0x2b27ff,_0x11f79c){return _0x1553(_0x2b27ff- -_0x2a8609._0x46eac2,_0x11f79c);}function _0x392b08(_0x3d5a34,_0x265933){return _0x1553(_0x265933-0x182,_0x3d5a34);}return _0x147bb0&&Object[_0xd0a2d3(-0x154,-_0x4aee2b._0x5ac988)](_0x147bb0)[_0x392b08(0x2b1,0x283)](function(_0x45e623){function _0x26cbf8(_0x55d051,_0x1cd4b5){return _0xd0a2d3(_0x55d051- -_0x117652._0x372be2,_0x1cd4b5);}function _0x349fd6(_0x4af5ba,_0x479083){return _0xd0a2d3(_0x479083-0x84,_0x4af5ba);}if(_0x45e623!==_0x349fd6(-0xc0,-_0x4fdc76._0x515cd3)){var _0x34cdb3=Object[_0x349fd6(-0x124,-0xe1)](_0x147bb0,_0x45e623);Object['defineProperty'](_0x3fafeb,_0x45e623,_0x34cdb3['get']?_0x34cdb3:{'enumerable':!![],'get':function(){return _0x147bb0[_0x45e623];}});}}),_0x3fafeb['default']=_0x147bb0,_0x3fafeb;}var mars3d__namespace=_interopNamespace(mars3d),mapv__namespace=_interopNamespace(mapv);const Cesium$1=mars3d__namespace['Cesium'],baiduMapLayer=mapv__namespace?mapv__namespace['baiduMapLayer']:null,BaseLayer$1=baiduMapLayer?baiduMapLayer[_0x89611a(-0x243,-0x229)]:Function;class MapVRenderer extends BaseLayer$1{constructor(_0x4240d6,_0x20105a,_0x3b87ed,_0x2c1bb0){const _0x428218={_0x48cce9:0x46e,_0x3dcc8c:0x4ac,_0x6aa8bd:0x427},_0x118435={_0x49578a:0x6ca},_0x7aec3e={_0x42a981:0x4b2};super(_0x4240d6,_0x20105a,_0x3b87ed);if(!BaseLayer$1)return;this['map']=_0x4240d6,this[_0x5e2ca6(0x459,0x419)]=_0x4240d6[_0x4ab94e(0x241,0x205)];function _0x4ab94e(_0x449046,_0x465f09){return _0x89611a(_0x465f09,_0x449046-_0x7aec3e._0x42a981);}this['dataSet']=_0x20105a;function _0x5e2ca6(_0xb194e,_0x2a9160){return _0x89611a(_0x2a9160,_0xb194e-_0x118435._0x49578a);}_0x3b87ed=_0x3b87ed||{},this[_0x5e2ca6(_0x428218._0x48cce9,_0x428218._0x3dcc8c)](_0x3b87ed),this['argCheck'](_0x3b87ed),this['initDevicePixelRatio'](),this['canvasLayer']=_0x2c1bb0,this[_0x5e2ca6(0x462,_0x428218._0x6aa8bd)]=!0x1,this[_0x5e2ca6(0x4b4,0x4d8)]=_0x3b87ed[_0x5e2ca6(0x4b4,0x483)];}['initDevicePixelRatio'](){this['devicePixelRatio']=window['devicePixelRatio']||0x1;}['addAnimatorEvent'](){}[_0x89611a(-0x284,-0x295)](){const _0x5c717a={_0x244749:0xc2,_0x337610:0x10e},_0x3fd072=this['options'][_0x574ac5(-_0x5c717a._0x244749,-0x103)];function _0x574ac5(_0x687948,_0x5368c4){return _0x89611a(_0x5368c4,_0x687948-0x154);}function _0x18a7d8(_0x2ea902,_0x11350c){return _0x89611a(_0x11350c,_0x2ea902-0x528);}this['isEnabledTime']()&&this[_0x574ac5(-0xed,-_0x5c717a._0x337610)]&&(this['steps'][_0x18a7d8(0x2ea,0x2c2)]=_0x3fd072[_0x18a7d8(0x2f1,0x32c)]['start']);}['animatorMoveendEvent'](){this['isEnabledTime']()&&this['animator'];}['getContext'](){const _0x17784a={_0x17ad55:0x27c,_0x41516c:0x2b6};function _0x4433f5(_0xc6a455,_0x2435f7){return _0x1324f1(_0x2435f7- -0x3e,_0xc6a455);}function _0x2c9469(_0x30596a,_0x2052fa){return _0x89611a(_0x2052fa,_0x30596a-0x6ff);}return this['canvasLayer']['canvas'][_0x4433f5(-0x28b,-_0x17784a._0x17ad55)](this[_0x4433f5(-_0x17784a._0x41516c,-0x26f)]);}[_0x1324f1(-0x244,-0x224)](_0x48e00f){const _0x3870fd={_0x293866:0x2a5,_0x573375:0x15b,_0x5ad137:0x12b,_0x202228:0x171,_0x9d557a:0x131,_0x35f667:0x316,_0x4c0a17:0x2a1,_0x323f52:0x2f3},_0xb58a2c={_0x3027ad:0x513};function _0x30da3c(_0x161b75,_0x5c831d){return _0x89611a(_0x161b75,_0x5c831d-0x11e);}this['options']=_0x48e00f;function _0x40aa08(_0x13436e,_0xfdfff1){return _0x1324f1(_0xfdfff1-_0xb58a2c._0x3027ad,_0x13436e);}this[_0x40aa08(0x2a0,_0x3870fd._0x293866)](_0x48e00f),this[_0x30da3c(-_0x3870fd._0x573375,-_0x3870fd._0x5ad137)]=this[_0x40aa08(0x2d2,0x2dc)][_0x30da3c(-0xf7,-0x12b)]||'2d',Cesium$1['defined'](this[_0x30da3c(-_0x3870fd._0x202228,-_0x3870fd._0x9d557a)][_0x40aa08(_0x3870fd._0x35f667,0x30a)])&&this[_0x40aa08(_0x3870fd._0x4c0a17,0x2d7)]&&this['canvasLayer'][_0x40aa08(0x31b,0x2f0)]&&this[_0x30da3c(-0xf0,-0x136)][_0x40aa08(_0x3870fd._0x323f52,0x2f0)](this['options'][_0x30da3c(-0x13f,-0x103)]),this['initAnimator']();}['_canvasUpdate'](_0x341a05){const _0x3f73d5={_0x559ec6:0x376,_0x44e245:0x48e,_0x3a37a5:0x4af,_0x278051:0x364,_0x451435:0x388,_0x3c11c6:0x379,_0x199e64:0x35c,_0x23c6e2:0x49e,_0xdfdaa7:0x475,_0x4e7438:0x354,_0x5c4582:0x48c,_0x105e16:0x45b,_0x2625d5:0x484,_0x1cd3e0:0x397,_0x193187:0x376,_0x52375c:0x364,_0x566452:0x33a,_0x4e8c05:0x48a,_0x27084d:0x467,_0xf544fa:0x44a,_0x87ed0d:0x488,_0x204d07:0x346,_0x401a23:0x447,_0x5c80fd:0x358,_0x564f66:0x35f,_0x4debe0:0x49a,_0x3ac0ad:0x4a7,_0x48043a:0x48a,_0x155bfa:0x4c1},_0x19c37b={_0x2572f6:0x6c1},_0x5cdec4={_0x472b01:0x137,_0xb3c9e6:0x169,_0xd8317f:0x147,_0x23356f:0x14b,_0x5925c5:0x15f,_0x29329c:0x199},_0x52c11e={_0x53ba0f:0x366};if(!this['canvasLayer']||this[_0x244e1d(_0x3f73d5._0x559ec6,0x346)])return;const _0xe07e4d=this['scene'],_0x1ad31d=this[_0x244e1d(0x329,0x35f)][_0x55c6db(0x4a9,0x4c3)],_0x52dcae=this['getContext']();if(this[_0x55c6db(0x471,_0x3f73d5._0x44e245)]()){if(void 0x0===_0x341a05)return void this['clear'](_0x52dcae);this['context']==='2d'&&(_0x52dcae[_0x55c6db(0x479,0x458)](),_0x52dcae[_0x244e1d(0x347,0x375)]=_0x55c6db(0x4c7,_0x3f73d5._0x3a37a5),_0x52dcae['fillStyle']=_0x244e1d(_0x3f73d5._0x278051,0x38b),_0x52dcae['fillRect'](0x0,0x0,_0x52dcae[_0x244e1d(_0x3f73d5._0x451435,0x36e)][_0x244e1d(_0x3f73d5._0x3c11c6,_0x3f73d5._0x199e64)],_0x52dcae[_0x244e1d(0x33d,0x36e)]['height']),_0x52dcae['restore']());}else this['clear'](_0x52dcae);if(this['context']==='2d')for(const _0x3e16dd in this['options']){_0x52dcae[_0x3e16dd]=this['options'][_0x3e16dd];}else _0x52dcae['clear'](_0x52dcae[_0x55c6db(_0x3f73d5._0x23c6e2,0x4ce)]);const _0x14d155={'transferCoordinate':function(_0x1dea4f){const _0x14b158={_0x48075b:0x230},_0x3d4437=null;let _0x3750a7=_0xe07e4d['mapvFixedHeight'];_0xe07e4d['mapvAutoHeight']&&(_0x3750a7=_0xe07e4d['getHeight'](Cesium$1['Cartographic'][_0x4a8b35(0x11e,_0x5cdec4._0x472b01)](_0x1dea4f[0x0],_0x1dea4f[0x1])));const _0x1577a6=Cesium$1['Cartesian3'][_0x4a8b35(_0x5cdec4._0xb3c9e6,_0x5cdec4._0x472b01)](_0x1dea4f[0x0],_0x1dea4f[0x1],_0x3750a7);function _0x567a62(_0x37e3f4,_0x4ca7d2){return _0x55c6db(_0x4ca7d2,_0x37e3f4- -_0x52c11e._0x53ba0f);}if(!_0x1577a6)return _0x3d4437;function _0x4a8b35(_0xc7783c,_0x301006){return _0x244e1d(_0xc7783c,_0x301006- -_0x14b158._0x48075b);}const _0xf1d5f=mars3d__namespace[_0x567a62(0xe6,0xea)]['toWindowCoordinates'](_0xe07e4d,_0x1577a6);if(!_0xf1d5f)return _0x3d4437;if(_0xe07e4d[_0x4a8b35(0x15f,0x148)]&&_0xe07e4d['mode']===Cesium$1['SceneMode']['SCENE3D']){const _0x277dfb=new Cesium$1[(_0x567a62(_0x5cdec4._0xd8317f,_0x5cdec4._0x23356f))](_0xe07e4d[_0x567a62(0x145,0x115)][_0x567a62(_0x5cdec4._0x5925c5,_0x5cdec4._0x29329c)],_0xe07e4d[_0x4a8b35(0x146,0x156)][_0x567a62(0xec,0x103)]),_0x57b471=_0x277dfb['isPointVisible'](_0x1577a6);if(!_0x57b471)return _0x3d4437;}return[_0xf1d5f['x'],_0xf1d5f['y']];}};void 0x0!==_0x341a05&&(_0x14d155['filter']=function(_0x18c3f9){const _0x25d537=_0x1ad31d['trails']||0xa;return!!(_0x341a05&&_0x18c3f9['time']>_0x341a05-_0x25d537&&_0x18c3f9['time']<_0x341a05);});let _0x56468a;if(this['options'][_0x55c6db(0x440,_0x3f73d5._0xdfdaa7)]===_0x244e1d(0x36e,_0x3f73d5._0x4e7438)&&(!this['options'][_0x244e1d(0x393,0x3a4)]||this['options'][_0x244e1d(0x399,0x3a4)]>=this[_0x55c6db(_0x3f73d5._0x5c4582,_0x3f73d5._0x105e16)]())){this[_0x55c6db(0x4c6,_0x3f73d5._0x2625d5)]['getExtent']();const _0x263161=this['getZoom'](),_0x4af478=this['supercluster']['getClusters']([-0xb4,-0x5a,0xb4,0x5a],_0x263161);this['pointCountMax']=this['supercluster'][_0x244e1d(0x3a0,0x376)][_0x263161]['max'],this['pointCountMin']=this['supercluster'][_0x244e1d(_0x3f73d5._0x1cd3e0,_0x3f73d5._0x193187)][_0x263161]['min'];let _0x835c4={},_0x277d17=null,_0x327ac1=null;if(this[_0x244e1d(0x324,_0x3f73d5._0x52375c)]===this[_0x244e1d(0x3b2,0x39d)])_0x277d17=this['options']['fillStyle'],_0x327ac1=this[_0x55c6db(0x44b,0x48a)][_0x244e1d(0x2f0,0x322)]||0x8;else{const _0x3817f0={};_0x3817f0['min']=this['pointCountMin'],_0x3817f0['max']=this['pointCountMax'],_0x3817f0[_0x244e1d(_0x3f73d5._0x566452,0x322)]=this[_0x55c6db(0x47a,_0x3f73d5._0x4e8c05)]['minSize']||0x8,_0x3817f0[_0x55c6db(0x483,0x466)]=this['options']['maxSize']||0x1e,_0x3817f0['gradient']=this['options']['gradient'],_0x835c4=new mapv__namespace['utilDataRangeIntensity'](_0x3817f0);}for(let _0x526583=0x0;_0x526583<_0x4af478[_0x55c6db(_0x3f73d5._0x27084d,_0x3f73d5._0xf544fa)];_0x526583++){const _0x2bc766=_0x4af478[_0x526583];_0x2bc766['properties']&&_0x2bc766[_0x55c6db(0x485,_0x3f73d5._0x87ed0d)]['cluster_id']?(_0x4af478[_0x526583]['size']=_0x327ac1||_0x835c4['getSize'](_0x2bc766['properties']['point_count']),_0x4af478[_0x526583][_0x244e1d(_0x3f73d5._0x204d07,0x36b)]=_0x277d17||_0x835c4['getColor'](_0x2bc766[_0x244e1d(0x364,0x35d)][_0x55c6db(0x4a1,0x46b)])):_0x4af478[_0x526583][_0x55c6db(0x45c,_0x3f73d5._0x401a23)]=this[_0x55c6db(0x462,0x48a)]['size'];}this[_0x244e1d(_0x3f73d5._0x5c80fd,0x38f)][_0x244e1d(0x367,0x366)](_0x4af478),_0x56468a=this['clusterDataSet']['get'](_0x14d155);}else _0x56468a=this['dataSet']['get'](_0x14d155);this[_0x244e1d(0x359,0x324)](_0x56468a);this['options']['unit']==='m'&&this[_0x244e1d(0x357,_0x3f73d5._0x564f66)]['size']&&(this['options']['_size']=this['options']['size']);function _0x55c6db(_0x398d5f,_0x11241b){return _0x1324f1(_0x11241b-_0x19c37b._0x2572f6,_0x398d5f);}const _0x1da4f6=mars3d__namespace['PointTrans']['toWindowCoordinates'](_0xe07e4d,Cesium$1['Cartesian3']['fromDegrees'](0x0,0x0));if(!_0x1da4f6)return;function _0x244e1d(_0x45b179,_0x48a985){return _0x1324f1(_0x48a985-0x596,_0x45b179);}this[_0x244e1d(0x36f,0x34d)](_0x52dcae,new mapv__namespace[(_0x55c6db(_0x3f73d5._0x4debe0,0x4c0))](_0x56468a),this['options'],_0x1da4f6),this[_0x55c6db(_0x3f73d5._0x3ac0ad,_0x3f73d5._0x48043a)]['updateCallback']&&this['options'][_0x55c6db(0x4e1,_0x3f73d5._0x155bfa)](_0x341a05);}['updateData'](_0x35d5e2,_0x3704a3){const _0x1ab54b={_0x3db3f8:0x1ff,_0x32382f:0x3d3,_0x588071:0x3bd};let _0x835d76=_0x35d5e2;_0x835d76&&_0x835d76['get']&&(_0x835d76=_0x835d76[_0x307fd1(_0x1ab54b._0x3db3f8,0x1ba)]()),void 0x0!==_0x835d76&&this['dataSet']['set'](_0x835d76);function _0x328f2f(_0xb59ab,_0x198bc8){return _0x89611a(_0x198bc8,_0xb59ab-0x622);}const _0x2d321f={};function _0x307fd1(_0x367a15,_0x41603b){return _0x1324f1(_0x41603b-0x41b,_0x367a15);}_0x2d321f[_0x328f2f(_0x1ab54b._0x32382f,_0x1ab54b._0x588071)]=_0x3704a3,super['update'](_0x2d321f);}['addData'](_0x23ff2d,_0x47a6cf){const _0x5c5d12={_0x22c492:0x63e};let _0x596e49=_0x23ff2d;function _0x382a6e(_0x51d6b6,_0x4a1265){return _0x89611a(_0x4a1265,_0x51d6b6-_0x5c5d12._0x22c492);}const _0x35c2c6={};_0x35c2c6['options']=_0x47a6cf,(_0x23ff2d&&_0x23ff2d['get']&&(_0x596e49=_0x23ff2d['get']()),this['dataSet'][_0x382a6e(0x3fc,0x3c0)](_0x596e49),this['update'](_0x35c2c6));}['getData'](){const _0x399614={_0xf8e609:0x33a};function _0xb9287b(_0x20f717,_0x2c273d){return _0x89611a(_0x20f717,_0x2c273d-0x56f);}return this[_0xb9287b(0x2ff,_0x399614._0xf8e609)];}['removeData'](_0x4b2f9b){const _0x45c68c={_0x283399:0x64,_0x460f11:0x38,_0x5c481e:0x151},_0x930423={_0xcf54e5:0x79};function _0x33f407(_0x337a87,_0x1e7974){return _0x1324f1(_0x1e7974-0x378,_0x337a87);}function _0x20e908(_0x4fc4d6,_0x28261c){return _0x1324f1(_0x4fc4d6-0x281,_0x28261c);}if(this[_0x20e908(_0x45c68c._0x283399,_0x45c68c._0x460f11)]){const _0x59e8d2=this[_0x33f407(0x16e,0x15b)][_0x33f407(0x134,0x117)]({'filter':function(_0x2dfb70){function _0x2a563f(_0x341457,_0x66c702){return _0x33f407(_0x66c702,_0x341457- -0x86);}return _0x4b2f9b==null||typeof _0x4b2f9b!==_0x2a563f(_0x930423._0xcf54e5,0x99)||!_0x4b2f9b(_0x2dfb70);}});this[_0x33f407(_0x45c68c._0x5c481e,0x15b)]['set'](_0x59e8d2);const _0x109555={};_0x109555['options']=null,this['update'](_0x109555);}}['clearData'](){function _0x4ce051(_0x4ba88c,_0x551575){return _0x1324f1(_0x551575-0x2db,_0x4ba88c);}this['dataSet']&&this['dataSet']['clear']();const _0x161b6d={};_0x161b6d['options']=null,this[_0x4ce051(0xbe,0xd6)](_0x161b6d);}[_0x1324f1(-0x24c,-0x221)](){this['canvasLayer']['draw']();}['clear'](_0x572e42){const _0x256e32={_0x22c5e7:0x3b1,_0x5c7ca6:0x3e7},_0x2919ab={_0x4963cb:0x60f};function _0x51929e(_0x14b352,_0x4e15dc){return _0x1324f1(_0x4e15dc-_0x2919ab._0x4963cb,_0x14b352);}function _0x172b6a(_0x5cfd55,_0xc74bb6){return _0x89611a(_0xc74bb6,_0x5cfd55-0x128);}_0x572e42&&_0x572e42['clearRect']&&_0x572e42['clearRect'](0x0,0x0,_0x572e42[_0x172b6a(-0x118,-0x106)]['width'],_0x572e42[_0x51929e(_0x256e32._0x22c5e7,_0x256e32._0x5c7ca6)][_0x51929e(0x3ac,0x3a3)]);}['getZoom'](){const _0x26e89a={_0x30879c:0x2c};function _0x5116fa(_0x39d38f,_0x345a8e){return _0x89611a(_0x39d38f,_0x345a8e-_0x26e89a._0x30879c);}return this['map'][_0x5116fa(-0x28d,-0x257)];}['destroy'](){const _0x2e493e={_0x26431d:0x2f,_0x3095b6:0x42},_0x4d5fa0={_0x299727:0x2a4};this[_0x4d3f07(0x10,_0x2e493e._0x26431d)](this['getContext']());function _0x4d3f07(_0x152ea7,_0xc55feb){return _0x89611a(_0xc55feb,_0x152ea7-_0x4d5fa0._0x299727);}function _0x11cf28(_0x34f93e,_0x184517){return _0x1324f1(_0x184517-0x18c,_0x34f93e);}this['clearData'](),this['animator']&&this[_0x11cf28(-0x87,-0x9d)][_0x4d3f07(0x72,0x9c)](),this[_0x4d3f07(0x63,_0x2e493e._0x3095b6)]=null,this['canvasLayer']=null;}}if(mapv__namespace!==null&&mapv__namespace!==void 0x0&&mapv__namespace[_0x1324f1(-0x201,-0x1fd)])mapv__namespace['DataSet']['prototype']['transferCoordinate']=function(_0x147f1b,_0x3f3e65,_0x27aaa5,_0x22482a){const _0x10f6de={_0x2c7bb5:0x2a6,_0x351d73:0x215,_0x3913e7:0x286},_0x579386={_0x43e8b7:0x97,_0x5a0ad0:0x66},_0x3a13e7={_0x47fa0a:0x29b};_0x22482a=_0x22482a||_0x416b03(-0x2a3,-0x2bd),_0x27aaa5=_0x27aaa5||_0x11562f(-0x228,-0x22e);for(let _0x4df713=0x0;_0x4df713<_0x147f1b[_0x416b03(-0x2c5,-0x29f)];_0x4df713++){const _0x2b6edf=_0x147f1b[_0x4df713]['geometry'],_0x2cc96b=_0x2b6edf[_0x27aaa5];switch(_0x2b6edf['type']){case'Point':{const _0x18f7b=_0x3f3e65(_0x2cc96b);_0x18f7b?_0x2b6edf[_0x22482a]=_0x18f7b:_0x2b6edf[_0x22482a]=[-0x3e7,-0x3e7];}break;case'LineString':{const _0x4a3cf9=[];for(let _0x1fe808=0x0;_0x1fe808<_0x2cc96b['length'];_0x1fe808++){const _0x291883=_0x3f3e65(_0x2cc96b[_0x1fe808]);_0x291883&&_0x4a3cf9['push'](_0x291883);}_0x2b6edf[_0x22482a]=_0x4a3cf9;}break;case'MultiLineString':case _0x416b03(-_0x10f6de._0x2c7bb5,-0x2b2):{const _0x30515a=_0x4254a5(_0x2cc96b);_0x2b6edf[_0x22482a]=_0x30515a;}break;case _0x11562f(-0x200,-_0x10f6de._0x351d73):{const _0x2d78f3=[];for(let _0x4c4b93=0x0;_0x4c4b93<_0x2cc96b[_0x11562f(-0x23f,-_0x10f6de._0x3913e7)];_0x4c4b93++){const _0x3fb7bd=_0x4254a5(_0x2cc96b[_0x4c4b93]);_0x3fb7bd[_0x11562f(-0x23f,-0x26c)]>0x0&&_0x2d78f3['push'](_0x3fb7bd);}_0x2b6edf[_0x22482a]=_0x2d78f3;}break;}}function _0x4254a5(_0x28b572){function _0x562c2f(_0x22d8ff,_0x177b9c){return _0x416b03(_0x22d8ff-0xa9,_0x177b9c);}function _0x4352e1(_0x54508e,_0x6320b4){return _0x11562f(_0x6320b4-_0x3a13e7._0x47fa0a,_0x54508e);}const _0x14e34a=[];for(let _0x3fc100=0x0;_0x3fc100<_0x28b572['length'];_0x3fc100++){const _0x1d7e6d=_0x28b572[_0x3fc100],_0x4dae27=[];for(let _0x4a1699=0x0;_0x4a1699<_0x1d7e6d[_0x4352e1(_0x579386._0x43e8b7,0x5c)];_0x4a1699++){const _0x247fbd=_0x3f3e65(_0x1d7e6d[_0x4a1699]);_0x247fbd&&_0x4dae27['push'](_0x247fbd);}_0x4dae27['length']>0x0&&_0x14e34a[_0x4352e1(0x6c,_0x579386._0x5a0ad0)](_0x4dae27);}return _0x14e34a;}function _0x416b03(_0x3d1414,_0x1b0171){return _0x1324f1(_0x3d1414- -0x4e,_0x1b0171);}function _0x11562f(_0x250d55,_0x1985c5){return _0x89611a(_0x1985c5,_0x250d55-0x50);}return _0x147f1b;};else throw new Error(_0x89611a(-0x224,-0x253));const Cesium=mars3d__namespace[_0x1324f1(-0x1f6,-0x1d2)];function _0x1553(_0x444190,_0x2ed951){const _0xe78f24=_0xe78f();return _0x1553=function(_0x1553ed,_0x5ca0ca){_0x1553ed=_0x1553ed-0xe4;let _0x474fd7=_0xe78f24[_0x1553ed];if(_0x1553['CKTuZY']===undefined){var _0x209b54=function(_0x26af1c){const _0x10959c='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';let _0x40604e='',_0x1b7769='';for(let _0x231972=0x0,_0xcd64ef,_0x31f1ab,_0x537fa4=0x0;_0x31f1ab=_0x26af1c['charAt'](_0x537fa4++);~_0x31f1ab&&(_0xcd64ef=_0x231972%0x4?_0xcd64ef*0x40+_0x31f1ab:_0x31f1ab,_0x231972++%0x4)?_0x40604e+=String['fromCharCode'](0xff&_0xcd64ef>>(-0x2*_0x231972&0x6)):0x0){_0x31f1ab=_0x10959c['indexOf'](_0x31f1ab);}for(let _0x1be2b9=0x0,_0x147bb0=_0x40604e['length'];_0x1be2b9<_0x147bb0;_0x1be2b9++){_0x1b7769+='%'+('00'+_0x40604e['charCodeAt'](_0x1be2b9)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0x1b7769);};_0x1553['tejCvI']=_0x209b54,_0x444190=arguments,_0x1553['CKTuZY']=!![];}const _0x3d96b1=_0xe78f24[0x0],_0x4e97be=_0x1553ed+_0x3d96b1,_0x7a066d=_0x444190[_0x4e97be];return!_0x7a066d?(_0x474fd7=_0x1553['tejCvI'](_0x474fd7),_0x444190[_0x4e97be]=_0x474fd7):_0x474fd7=_0x7a066d,_0x474fd7;},_0x1553(_0x444190,_0x2ed951);}function _0x1324f1(_0x179788,_0x2bf286){const _0xdcfd6b={_0x537134:0x364};return _0x1553(_0x179788- -_0xdcfd6b._0x537134,_0x2bf286);}const BaseLayer=mars3d__namespace['layer']['BaseLayer'];class MapVLayer extends BaseLayer{constructor(_0x1f8a73,_0x18bb72){const _0xda59c6={_0x1cb792:0x14f,_0x4b13d:0x189,_0x4a32ec:0x473},_0x12f639={_0xa26c87:0x686};function _0xc9acc6(_0x7a6109,_0x5def16){return _0x1324f1(_0x7a6109-0xe8,_0x5def16);}super(_0x1f8a73);function _0x274f8d(_0x127971,_0x20c7cd){return _0x1324f1(_0x20c7cd-_0x12f639._0xa26c87,_0x127971);}this['_pointerEvents']=this[_0xc9acc6(-_0xda59c6._0x1cb792,-_0xda59c6._0x4b13d)][_0x274f8d(0x4b0,_0xda59c6._0x4a32ec)],this['dataSet']=_0x18bb72||new mapv__namespace['DataSet'](_0x1f8a73['data']),this[_0x274f8d(0x417,0x45e)]=null;}get['pointerEvents'](){const _0x47f634={_0x4ae189:0x495},_0x49d1d9={_0x5be688:0x70f};function _0x9c5b6e(_0x514cf0,_0x1c91a2){return _0x1324f1(_0x1c91a2-_0x49d1d9._0x5be688,_0x514cf0);}return this[_0x9c5b6e(_0x47f634._0x4ae189,0x4cc)];}set['pointerEvents'](_0x129494){const _0x11ca7c={_0x460172:0x18c,_0x4a9d5d:0x165};this['_pointerEvents']=_0x129494;function _0x4dabd6(_0x1f3900,_0x4e031b){return _0x89611a(_0x1f3900,_0x4e031b-0x2fa);}function _0x8ada23(_0x1715bd,_0x5b3e6e){return _0x1324f1(_0x1715bd-0x87,_0x5b3e6e);}this['canvas']&&(_0x129494?this[_0x8ada23(-0x1a1,-0x167)][_0x4dabd6(0x81,0x84)][_0x8ada23(-_0x11ca7c._0x460172,-0x1a3)]=_0x8ada23(-0x1e0,-0x1a0):this['canvas']['style'][_0x8ada23(-0x18c,-_0x11ca7c._0x4a9d5d)]='none');}['_showHook'](_0x1af4bf){const _0x452bde={_0x280671:0x234},_0x39b9f9={_0x369189:0x3d};function _0x35160f(_0x16a201,_0x3bf4ef){return _0x89611a(_0x3bf4ef,_0x16a201-0x57d);}function _0x13aceb(_0x173f5a,_0x1732df){return _0x1324f1(_0x1732df-_0x39b9f9._0x369189,_0x173f5a);}_0x1af4bf?this['canvas']['style'][_0x13aceb(-0x232,-0x234)]='block':this['canvas']['style'][_0x13aceb(-0x25e,-_0x452bde._0x280671)]=_0x35160f(0x36e,0x3b1);}['_mountedHook'](){const _0x48bfe3={_0x50c0e1:0x363,_0x3ccb59:0x38f,_0x976bf6:0x217},_0x24359={_0x456870:0x59b},_0x2a3af4={_0x34b113:0x498};function _0x14fbed(_0x4b1369,_0x328b09){return _0x1324f1(_0x4b1369-_0x2a3af4._0x34b113,_0x328b09);}function _0x213620(_0x4d8cd4,_0x1058a0){return _0x1324f1(_0x1058a0-_0x24359._0x456870,_0x4d8cd4);}this['_map']['scene']['mapvDepthTest']=this['options'][_0x213620(_0x48bfe3._0x50c0e1,0x32b)]??!![],this[_0x213620(0x359,_0x48bfe3._0x3ccb59)]['scene'][_0x213620(0x38c,0x3a6)]=this['options']['clampToGround']??![],this['_map']['scene'][_0x14fbed(0x23b,_0x48bfe3._0x976bf6)]=this['options']['fixedHeight']??0x0;}['_addedHook'](){const _0x5a1857={_0x74e9a:0x37a,_0x10664:0x3a8,_0x5f22a8:0xee,_0x4e2ee2:0x110};this[_0x53f23d(0x3b0,0x3a6)]&&(!this['dataSet'][_0x53f23d(0x37a,0x350)]||this['dataSet']['_data']['length']===0x0)&&(this['dataSet'][_0x53f23d(_0x5a1857._0x74e9a,_0x5a1857._0x10664)]=[]['concat'](this[_0x475d25(-_0x5a1857._0x5f22a8,-0x101)]['_dataCache']));this['_mapVRenderer']=new MapVRenderer(this[_0x475d25(-0xbd,-0xf0)],this[_0x475d25(-_0x5a1857._0x4e2ee2,-0x101)],this['options'],this),this['initDevicePixelRatio']();function _0x53f23d(_0x54894c,_0x524221){return _0x1324f1(_0x54894c-0x5cd,_0x524221);}this['canvas']=this['_createCanvas'](),this[_0x475d25(-0x171,-0x129)]=this['render'][_0x475d25(-0x148,-0x119)](this),this['bindEvent']();function _0x475d25(_0x3dd19a,_0x50169c){return _0x1324f1(_0x50169c-0x11c,_0x3dd19a);}this['_reset']();}['_removedHook'](){this['unbindEvent']();this['_mapVRenderer']&&(this['_mapVRenderer']['destroy'](),this['_mapVRenderer']=null);function _0x26b8c2(_0x32eb0f,_0x2b853c){return _0x89611a(_0x32eb0f,_0x2b853c-0x1a4);}function _0xaf939d(_0x2d52ae,_0x5f4dc3){return _0x1324f1(_0x5f4dc3-0x693,_0x2d52ae);}this[_0x26b8c2(-0xad,-0x9c)]['parentElement'][_0xaf939d(0x486,0x47b)](this['canvas']);}['initDevicePixelRatio'](){this['devicePixelRatio']=window['devicePixelRatio']||0x1;}[_0x89611a(-0x27d,-0x269)](){const _0x31226f={_0x37e740:0x17e,_0x4f7874:0x181,_0x4b530c:0x2ff,_0x42a4d9:0x2c5,_0x1932b0:0x174};var _0x29f888,_0x46d45a;this['_map']['on'](mars3d__namespace[_0x3a57b9(0x181,0x197)][_0x3a57b9(0x133,0x12b)],this['_onMoveStartEvent'],this);function _0x2e1312(_0x2b6837,_0x87075b){return _0x89611a(_0x2b6837,_0x87075b-0x531);}this['_map']['on'](mars3d__namespace['EventType']['cameraMoveStart'],this['_onMoveStartEvent'],this),this[_0x2e1312(0x329,0x30d)]['on'](mars3d__namespace['EventType']['cameraMoveEnd'],this[_0x3a57b9(_0x31226f._0x37e740,_0x31226f._0x4f7874)],this);(_0x29f888=this['options'])!==null&&_0x29f888!==void 0x0&&(_0x29f888=_0x29f888['methods'])!==null&&_0x29f888!==void 0x0&&_0x29f888['click']&&this['_map']['on'](mars3d__namespace['EventType']['click'],this['_onMapClick'],this);function _0x3a57b9(_0x152ce7,_0x3e2b1d){return _0x1324f1(_0x152ce7-0x380,_0x3e2b1d);}(_0x46d45a=this['options'])!==null&&_0x46d45a!==void 0x0&&(_0x46d45a=_0x46d45a['methods'])!==null&&_0x46d45a!==void 0x0&&_0x46d45a[_0x2e1312(_0x31226f._0x4b530c,_0x31226f._0x42a4d9)]&&this[_0x3a57b9(_0x31226f._0x1932b0,0x1b1)]['on'](mars3d__namespace[_0x2e1312(0x2f1,0x31a)][_0x3a57b9(0x188,0x199)],this['_onMapMouseMove'],this);}['unbindEvent'](){const _0x5e57df={_0x1cd390:0x10e,_0x5523ff:0xb6,_0x4ae025:0x10c,_0x47fdc7:0x147,_0x8ed9b6:0x119,_0x321f64:0x124,_0x251568:0x445,_0x5b8154:0x10e,_0x3fbf95:0x131,_0x201741:0x433,_0x2db3bb:0x40d,_0x43a434:0x9a,_0x414619:0x3c6,_0xec24af:0x12f,_0x291bbd:0x44c},_0x1f0613={_0x44eeff:0x330};function _0x3bafb9(_0x4c1690,_0x47a7e0){return _0x89611a(_0x47a7e0,_0x4c1690-_0x1f0613._0x44eeff);}var _0x5e4c57,_0x977250;this['_map'][_0x3bafb9(_0x5e57df._0x1cd390,0xd1)](mars3d__namespace['EventType']['mouseDown'],this[_0x3bafb9(_0x5e57df._0x5523ff,0xbb)],this),this[_0x3bafb9(_0x5e57df._0x4ae025,_0x5e57df._0x47fdc7)][_0x3bafb9(0x10e,0xf6)](mars3d__namespace[_0x3bafb9(_0x5e57df._0x8ed9b6,_0x5e57df._0x321f64)]['cameraMoveStart'],this[_0x3bafb9(0xb6,0xae)],this),this['_map']['off'](mars3d__namespace[_0x8e0f43(0x449,_0x5e57df._0x251568)][_0x3bafb9(0x114,0x147)],this['_onMoveEndEvent'],this),this[_0x8e0f43(0x41b,0x438)][_0x3bafb9(_0x5e57df._0x5b8154,_0x5e57df._0x3fbf95)](mars3d__namespace['EventType'][_0x3bafb9(0xf3,0xc0)],this[_0x8e0f43(0x3b1,0x3c9)],this);function _0x8e0f43(_0x21cb23,_0x65ed62){return _0x89611a(_0x21cb23,_0x65ed62-0x65c);}(_0x5e4c57=this[_0x8e0f43(_0x5e57df._0x201741,_0x5e57df._0x2db3bb)])!==null&&_0x5e4c57!==void 0x0&&(_0x5e4c57=_0x5e4c57[_0x3bafb9(_0x5e57df._0x43a434,0x54)])!==null&&_0x5e4c57!==void 0x0&&_0x5e4c57['click']&&this[_0x3bafb9(0x10c,0xec)]['off'](mars3d__namespace['EventType']['click'],this['_onMapClick'],this),(_0x977250=this['options'])!==null&&_0x977250!==void 0x0&&(_0x977250=_0x977250[_0x8e0f43(0x3e7,_0x5e57df._0x414619)])!==null&&_0x977250!==void 0x0&&_0x977250['mousemove']&&this[_0x3bafb9(0x10c,_0x5e57df._0xec24af)]['off'](mars3d__namespace['EventType'][_0x8e0f43(0x469,_0x5e57df._0x291bbd)],this[_0x3bafb9(0xf4,0x10b)],this);}['_onMoveStartEvent'](){const _0x4167e6={_0x330f2c:0x2dc,_0x5cb5fe:0x2ad,_0x4d67fc:0x3f6},_0x2c7fb3={_0x3a7fec:0x633};function _0x5005b9(_0xfb6c8e,_0xac9512){return _0x89611a(_0xfb6c8e,_0xac9512-_0x2c7fb3._0x3a7fec);}function _0x58cdc7(_0x5a8d85,_0x4f0096){return _0x1324f1(_0x4f0096-0x4b9,_0x5a8d85);}this['_mapVRenderer']&&(this['_mapVRenderer']['animatorMovestartEvent'](),this['_map'][_0x5005b9(0x406,0x411)](mars3d__namespace['EventType']['postRender'],this['_reset'],this),this[_0x58cdc7(_0x4167e6._0x330f2c,_0x4167e6._0x5cb5fe)]['on'](mars3d__namespace['EventType'][_0x5005b9(0x3be,_0x4167e6._0x4d67fc)],this['_reset'],this));}['_onMoveEndEvent'](){const _0x4f630f={_0x433ce6:0x223},_0x56b21b={_0x27d484:0x3},_0x4f336f={_0x1b2bbf:0x6eb};function _0x458c30(_0x303cea,_0x4de6ac){return _0x89611a(_0x4de6ac,_0x303cea-_0x4f336f._0x1b2bbf);}function _0x19ff8f(_0x4b166a,_0x1cfc9a){return _0x89611a(_0x4b166a,_0x1cfc9a- -_0x56b21b._0x27d484);}this[_0x458c30(0x4cb,0x4cf)]&&(this['_map']['off'](mars3d__namespace['EventType']['postRender'],this['_reset'],this),this[_0x19ff8f(-0x253,-_0x4f630f._0x433ce6)]['animatorMoveendEvent'](),this['_reset']());}[_0x1324f1(-0x25f,-0x296)](_0x5ed29c,_0x2c7b82){this['_removedHook'](),this['_addedHook']();}[_0x1324f1(-0x219,-0x22f)](_0x3cdb56){const _0x1cba3f={_0xd1a958:0x562};function _0x5172a0(_0x4e1a3c,_0x3b0774){return _0x89611a(_0x3b0774,_0x4e1a3c-_0x1cba3f._0xd1a958);}this[_0x5172a0(0x342,0x350)]&&this['_mapVRenderer']['addData'](_0x3cdb56,this['options']);}[_0x89611a(-0x22c,-0x23a)](_0x2f5f94){const _0x4b1756={_0x4eda5f:0x117},_0x1dbcb8={_0x458aec:0x135};function _0x3d8c1e(_0x118996,_0x106762){return _0x89611a(_0x118996,_0x106762-_0x1dbcb8._0x458aec);}this['_mapVRenderer']&&this['_mapVRenderer']['updateData'](_0x2f5f94,this[_0x3d8c1e(-_0x4b1756._0x4eda5f,-0x11a)]);}['getData'](){return this['_mapVRenderer']&&(this['dataSet']=this['_mapVRenderer']['getData']()),this['dataSet'];}[_0x1324f1(-0x1fd,-0x201)](_0x39b831){this['_mapVRenderer']&&this['_mapVRenderer']['removeData'](_0x39b831);}[_0x1324f1(-0x247,-0x231)](){function _0x43aabf(_0x367862,_0x4cf130){return _0x1324f1(_0x367862-0x363,_0x4cf130);}this['_mapVRenderer']&&this[_0x43aabf(0x15b,0x130)]['clearData']();}['_createCanvas'](){const _0x55616d={_0x3dea82:0x359,_0x5bfabc:0x323,_0x9c0e22:0x3a8,_0x2abecc:0x38d,_0x11fe65:0x333,_0x29529f:0x2f4,_0xf751d3:0x39a,_0x3386b4:0x32f,_0x4dd801:0x371,_0x121feb:0x2fa,_0x426e69:0x348,_0x1a063e:0x35d,_0x4ed8ad:0x393,_0x34f4b7:0x36f},_0x3f1b41={_0x1e3725:0x5b1},_0x1e32e1=mars3d__namespace[_0x13608d(_0x55616d._0x3dea82,0x36d)][_0x13608d(0x2f8,_0x55616d._0x5bfabc)](_0xe8de5d(0x37e,_0x55616d._0x9c0e22),_0x13608d(0x353,0x372),this[_0x13608d(0x39f,_0x55616d._0x2abecc)]['container']);_0x1e32e1['id']=this['id'],_0x1e32e1['style'][_0xe8de5d(_0x55616d._0x11fe65,_0x55616d._0x29529f)]=_0x13608d(0x37a,0x353),_0x1e32e1['style']['top']='0px',_0x1e32e1['style']['left']='0px',_0x1e32e1['width']=parseInt(this[_0xe8de5d(_0x55616d._0xf751d3,0x38b)][_0x13608d(_0x55616d._0x3386b4,_0x55616d._0x4dd801)]['width']),_0x1e32e1[_0xe8de5d(0x33a,_0x55616d._0x121feb)]=parseInt(this['_map']['canvas'][_0xe8de5d(0x33a,0x37c)]),_0x1e32e1[_0x13608d(0x329,0x33b)]['width']=this[_0xe8de5d(0x39a,0x383)]['canvas']['style'][_0xe8de5d(0x36c,0x389)],_0x1e32e1['style']['height']=this['_map']['canvas']['style']['height'],_0x1e32e1[_0xe8de5d(_0x55616d._0x426e69,_0x55616d._0x1a063e)]['pointerEvents']=this['_pointerEvents']?_0x13608d(_0x55616d._0x4ed8ad,0x393):_0x13608d(0x3a3,0x3a2);function _0x13608d(_0x300544,_0x1307d7){return _0x89611a(_0x300544,_0x1307d7-_0x3f1b41._0x1e3725);}function _0xe8de5d(_0x9a5eff,_0x4fdcbf){return _0x1324f1(_0x9a5eff-0x5a6,_0x4fdcbf);}_0x1e32e1['style'][_0x13608d(0x350,0x390)]=this['options'][_0xe8de5d(0x39d,0x3a4)]??0x9;if(this['options']['context']==='2d'){const _0x9abcd=this['devicePixelRatio'];_0x1e32e1['getContext'](this['options']['context'])[_0xe8de5d(0x3ac,_0x55616d._0x34f4b7)](_0x9abcd,_0x9abcd);}return _0x1e32e1;}['_reset'](){this['resize'](),this['render']();}[_0x89611a(-0x298,-0x264)](){this['_reset']();}['remove'](){const _0xfea941={_0x38c38c:0x44d,_0x372769:0x441,_0xd1071f:0x42d,_0x5d153e:0x3f0};this['_mapVRenderer']&&(this['_mapVRenderer']['destroy'](),this[_0x5e94ff(_0xfea941._0x38c38c,_0xfea941._0x372769)]=null);function _0x5e94ff(_0x41cf6f,_0x52e566){return _0x1324f1(_0x41cf6f-0x655,_0x52e566);}function _0x49a9df(_0x595405,_0x2df974){return _0x89611a(_0x595405,_0x2df974-0x3ea);}this[_0x5e94ff(_0xfea941._0xd1071f,_0xfea941._0x5d153e)]['parentElement']['removeChild'](this['canvas']);}['render'](){this['_mapVRenderer']['_canvasUpdate']();}['resize'](){const _0x2b914e={_0x13428b:0x45,_0x391da3:0x95,_0x1c99a5:0x5c,_0x598ce4:0x5,_0x3761a1:0x53,_0xddfef0:0x97,_0x2e4179:0x6c};function _0xc4e249(_0x10d9f3,_0x31f79e){return _0x1324f1(_0x10d9f3-0x2c8,_0x31f79e);}function _0x108232(_0x325506,_0x198e4a){return _0x1324f1(_0x325506-0x2a3,_0x198e4a);}if(this['canvas']){const _0xba425e=this['canvas'];_0xba425e[_0x108232(_0x2b914e._0x13428b,0x18)]['position']='absolute',_0xba425e['style']['top']='0px',_0xba425e['style']['left']='0px',_0xba425e['width']=parseInt(this[_0xc4e249(0xbc,0xa7)]['canvas'][_0x108232(0x69,_0x2b914e._0x391da3)]),_0xba425e[_0x108232(0x37,0x54)]=parseInt(this['_map']['canvas'][_0xc4e249(_0x2b914e._0x1c99a5,0x65)]),_0xba425e['style']['width']=this['_map']['canvas'][_0x108232(0x45,_0x2b914e._0x598ce4)]['width'],_0xba425e['style'][_0xc4e249(0x5c,_0x2b914e._0x3761a1)]=this[_0x108232(_0x2b914e._0xddfef0,_0x2b914e._0x2e4179)]['canvas']['style']['height'];}}[_0x89611a(-0x267,-0x272)](_0x551bcd){const _0x5c86ad={_0x1a06d1:0x1d4,_0x31175f:0x1a2,_0xfc66b6:0x70,_0x59c2b2:0x188,_0x1c28a2:0x1a8,_0x12ab8d:0x160,_0x30d66a:0x75,_0x4a8bfc:0xba},_0x38cf77={_0x4946b8:0x7b};function _0x1df79d(_0x3bf6aa,_0x30dde8){return _0x1324f1(_0x3bf6aa-_0x38cf77._0x4946b8,_0x30dde8);}if(!this[_0x1df79d(-0x1a2,-0x1ca)]||!this['dataSet'][_0x1df79d(-0x1d8,-0x1a5)])return;const _0x6ec3f3={};_0x6ec3f3['type']=_0x5dcc9f(0x7e,0x50);function _0x5dcc9f(_0x28624f,_0x38e0a4){return _0x1324f1(_0x28624f-0x2da,_0x38e0a4);}_0x6ec3f3[_0x1df79d(-_0x5c86ad._0x1a06d1,-0x1ad)]=this[_0x1df79d(-_0x5c86ad._0x31175f,-0x160)][_0x1df79d(-0x1d8,-0x1e2)];const _0x8adbb6=mars3d__namespace['PolyUtil']['getExtentByGeoJSON'](_0x6ec3f3);if(!_0x8adbb6)return;return _0x551bcd!==null&&_0x551bcd!==void 0x0&&_0x551bcd[_0x5dcc9f(_0x5c86ad._0xfc66b6,0xab)]?_0x8adbb6:Cesium[_0x1df79d(-_0x5c86ad._0x59c2b2,-_0x5c86ad._0x1c28a2)]['fromDegrees'](_0x8adbb6[_0x1df79d(-0x19a,-_0x5c86ad._0x12ab8d)],_0x8adbb6['ymin'],_0x8adbb6['xmax'],_0x8adbb6[_0x5dcc9f(_0x5c86ad._0x30d66a,_0x5c86ad._0x4a8bfc)]);}['_onMapClick'](_0x3d3080){const _0x512d92={_0x33d86e:0x12a};function _0x16b5a2(_0x5efb44,_0x13a01b){return _0x1324f1(_0x13a01b-0x30f,_0x5efb44);}function _0x500237(_0x3540d1,_0x43eaf8){return _0x1324f1(_0x3540d1-0x45e,_0x43eaf8);}this['_cache_event']=_0x3d3080,this[_0x16b5a2(_0x512d92._0x33d86e,0x107)]&&this['_mapVRenderer'][_0x500237(0x1df,0x214)](_0x3d3080['windowPosition'],_0x3d3080);}[_0x1324f1(-0x224,-0x21e)](_0x803db9){const _0x5bd082={_0x495eb5:0x325,_0x55c493:0x2f6};function _0x4f7f75(_0x3a862a,_0x5158c3){return _0x89611a(_0x5158c3,_0x3a862a-0x558);}this[_0x4f7f75(_0x5bd082._0x495eb5,0x2fa)]=_0x803db9;function _0x433cde(_0x19fcdf,_0x5ae301){return _0x89611a(_0x19fcdf,_0x5ae301-0x48d);}this['_mapVRenderer']&&this['_mapVRenderer'][_0x433cde(0x248,0x223)](_0x803db9[_0x4f7f75(_0x5bd082._0x55c493,0x2c3)],_0x803db9);}['on'](_0x50b8e6,_0x3b0955,_0x43a41f){const _0x122365={_0x49b5a8:0x1d9,_0x45d313:0x123,_0x59c06d:0x1a2,_0x211f1d:0x19e,_0x6e6d6f:0x123,_0x535e5c:0x200,_0x28ca8c:0x1c8},_0x52a6f1={_0x282d47:0x50c,_0x5e8ff8:0x506},_0x2bf0d2={_0x1ad3a4:0x185,_0x2e6d51:0x13f,_0x7d2096:0x104},_0x358f4e={_0x40a170:0x255},_0xf418de={_0x14bf77:0x4f};this[_0xec0ff6(-_0x122365._0x49b5a8,-0x200)]['methods']=this['options']['methods']||{};function _0x1e203f(_0x4b97c2,_0x12b895){return _0x89611a(_0x4b97c2,_0x12b895-0x3b9);}function _0xec0ff6(_0x5b91bd,_0x1f3816){return _0x89611a(_0x5b91bd,_0x1f3816-_0xf418de._0x14bf77);}if(_0x50b8e6===mars3d__namespace['EventType']['click'])this[_0x1e203f(0x12f,0x16a)][_0x1e203f(0xf4,_0x122365._0x45d313)]['click']=_0x45dd64=>{function _0x213901(_0x337ac0,_0x3805b7){return _0xec0ff6(_0x337ac0,_0x3805b7-_0x358f4e._0x40a170);}function _0x51cf41(_0x34230e,_0x445afd){return _0xec0ff6(_0x445afd,_0x34230e-0x33d);}if(_0x45dd64){const _0x124f55={...this[_0x51cf41(0x159,_0x2bf0d2._0x1ad3a4)]};_0x124f55[_0x51cf41(0x110,0xd0)]=this,_0x124f55['data']=_0x45dd64,_0x3b0955[_0x51cf41(_0x2bf0d2._0x2e6d51,_0x2bf0d2._0x7d2096)](_0x43a41f)(_0x124f55);}},this['_map']['on'](mars3d__namespace[_0x1e203f(0x162,0x1a2)]['click'],this['_onMapClick'],this);else _0x50b8e6===mars3d__namespace[_0x1e203f(0x191,_0x122365._0x59c06d)][_0x1e203f(_0x122365._0x211f1d,0x1a9)]&&(this['options'][_0x1e203f(0xe4,_0x122365._0x6e6d6f)]['mousemove']=_0x11b920=>{function _0x1e66cb(_0x2d2b3f,_0x3d8757){return _0xec0ff6(_0x2d2b3f,_0x3d8757-0x6de);}if(_0x11b920){const _0x3e21e0={...this['_cache_event']};_0x3e21e0['layer']=this,_0x3e21e0[_0x1e66cb(_0x52a6f1._0x282d47,_0x52a6f1._0x5e8ff8)]=_0x11b920,_0x3b0955['bind'](_0x43a41f)(_0x3e21e0);}},this['_map']['on'](mars3d__namespace[_0xec0ff6(-_0x122365._0x535e5c,-_0x122365._0x28ca8c)]['mouseMove'],this[_0x1e203f(0x1ab,0x17d)],this));return this;}[_0x1324f1(-0x20a,-0x251)](_0xc51134,_0x35fb0d){const _0x443069={_0x846dd5:0x8,_0xe03e63:0x4d,_0x14b788:0x19,_0x1c9358:0x4f5,_0x348fad:0x531,_0x701ec9:0xa,_0x4ec813:0x40};if(_0xc51134===_0x499907(-0x20,-0x9)){var _0x362680;this[_0x499907(_0x443069._0x846dd5,-0x1)][_0x499907(0xa,_0x443069._0xe03e63)](_0xc51134,this[_0x499907(-_0x443069._0x14b788,0x16)],this),(_0x362680=this[_0x499907(-0x23,-0x54)]['methods'])!==null&&_0x362680!==void 0x0&&_0x362680['mousemove']&&delete this['options']['methods'][_0x5d3ee4(_0x443069._0x1c9358,0x4f1)];}else{if(_0xc51134===_0x5d3ee4(_0x443069._0x348fad,0x4fb)){var _0x26e4fd;this['_map'][_0x499907(_0x443069._0x701ec9,0x14)](_0xc51134,this['_onMapMouseMove'],this),(_0x26e4fd=this['options']['methods'])!==null&&_0x26e4fd!==void 0x0&&_0x26e4fd[_0x499907(-_0x443069._0x4ec813,-0x5b)]&&delete this['options']['methods'][_0x499907(-0x40,-0x7f)];}}function _0x5d3ee4(_0x35fd58,_0x1b16ae){return _0x1324f1(_0x35fd58-0x729,_0x1b16ae);}function _0x499907(_0x3e308e,_0x789279){return _0x1324f1(_0x3e308e-0x214,_0x789279);}return this;}}mars3d__namespace['LayerUtil']['register'](_0x1324f1(-0x240,-0x210),MapVLayer),mars3d__namespace[_0x89611a(-0x292,-0x27c)][_0x1324f1(-0x257,-0x288)]=MapVLayer,mars3d__namespace['mapv']=mapv__namespace,mars3d__namespace[_0x1324f1(-0x1fb,-0x1df)]['logInfo'](_0x89611a(-0x25c,-0x234)),exports[_0x1324f1(-0x257,-0x25b)]=MapVLayer,Object[_0x1324f1(-0x22e,-0x1f4)](mapv)[_0x1324f1(-0x263,-0x27d)](function(_0x45c292){if(_0x45c292!=='default'&&!exports['hasOwnProperty'](_0x45c292))Object['defineProperty'](exports,_0x45c292,{'enumerable':!![],'get':function(){return mapv[_0x45c292];}});});const _0x1be2b9={};function _0x89611a(_0x4350fe,_0x307f80){return _0x1553(_0x307f80- -0x37c,_0x4350fe);}_0x1be2b9['value']=!![],Object[_0x1324f1(-0x278,-0x2af)](exports,'__esModule',_0x1be2b9);
}));
