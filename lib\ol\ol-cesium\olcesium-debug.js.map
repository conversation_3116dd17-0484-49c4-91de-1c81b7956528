{"version": 3, "file": "olcesium-debug.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACgE;AACnB;AACA;;;AAG7C;AACA;AACA,aAAa,SAAS;AACtB,aAAa,eAAe;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,eAAe;AAC5B;AACA;AACA;AACA,eAAe,gDAAgD;AAC/D;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,wBAAwB,gDAAM;AAC9B;AACA;;AAEA;AACA,6BAA6B,0DAAY;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,oDAAU;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,aAAa,WAAW;AACxB,aAAa,QAAQ;AACrB,aAAa,eAAe;AAC5B;AACA;AACA;AACA;AACA,yCAAyC,oDAAU;AACnD;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA,aAAa,gBAAgB;AAC7B,cAAc,SAAS;AACvB;AACA;AACA;AACA,gBAAgB,gDAAM;AACtB;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,0CAA0C,qDAAmB;AAC7D;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,gBAAgB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,gDAAM;AACtB;AACA;AACA,MAAM,yDAAmB;AACzB,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA,aAAa,eAAe;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,0DAAY;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,gBAAgB;AAC7B;AACA;AACA;AACA,iBAAiB,gDAAM;;AAEvB;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,KAAK;;AAEL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,yDAAmB;AAC3B,OAAO;AACP;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA,mBAAmB,qDAAmB;AACtC;AACA;AACA,6CAA6C,qDAAmB;AAChE;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,IAAI;AACjB;AACA;AACA;AACA;;AAEA;AACA,aAAa,IAAI;AACjB;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,IAAI;AACjB,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA,aAAa,yCAAyC;AACtD,cAAc;AACd;AACA;AACA;AACA;AACA;;;AAGA,iEAAe,oBAAoB,EAAC;;;;;;;;;;;;;;;AChVpC;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,eAAe;AAC5B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA,iEAAe,cAAc,EAAC;;;;;;;;;;;;;;;;;;;;;ACtE9B;AACA;AACA;;AAEgE;AACjB;AACP;AACiD;;AAEzF;AACA;AACA;AACA;AACA,aAAa,eAAe;AAC5B,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA,aAAa,gBAAgB;AAC7B,aAAa,iBAAiB;AAC9B,aAAa,SAAS;AACtB,cAAc,gBAAgB;AAC9B;AACA;AACA;AACA;AACA,sBAAsB,SAAS;AAC/B;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa,UAAU;AACvB;AACA;AACA;AACA;AACA,MAAM,yDAAmB;AACzB;AACA;;AAEA;AACA;AACA,uBAAuB,wDAAY;AACnC,yBAAyB,wDAAY;AACrC;;AAEA;AACA;;AAEA;;AAEA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA,aAAa,GAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,cAAc,kBAAkB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc,QAAQ;AACtB;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc,QAAQ;AACtB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,gBAAgB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc,yBAAyB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,gBAAgB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,QAAQ,mDAAS;AACjB,QAAQ,mDAAS;AACjB;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA,cAAc,0BAA0B;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM,mDAAS;AACf,MAAM,mDAAS;AACf;AACA;AACA;AACA;;AAEA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,cAAc,QAAQ;AACtB;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,0CAA0C,mDAAS;AACnD,QAAQ,mDAAS;AACjB;AACA;AACA;AACA;;AAEA;;AAEA,eAAe,2BAA2B;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,yBAAyB,mDAAS;;AAElC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mBAAmB,gEAAwB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,mDAAS;AACf,MAAM,mDAAS;;AAEf;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA,oEAAoE;AACpE;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,aAAa,UAAU;AACvB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,cAAc,QAAQ;AACtB;AACA;AACA;AACA,WAAW,mEAAyB;AACpC;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,cAAc,QAAQ;AACtB;AACA;AACA;AACA,WAAW,mEAAyB;AACpC;AACA;;;AAGA,iEAAe,MAAM,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3dtB;AACA;AACA;AACiD;AACN;AACM;AACE;AACoB;AAChB;AACM;AAC5B;AAC6C;AACC;;;AAG/E;AACA,aAAa,QAAQ;AACrB,cAAc,gBAAgB;AAC9B,cAAc,6BAA6B;AAC3C;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,eAAe;AAC5B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA,aAAa,wBAAwB;AACrC;AACA;AACA;AACA;AACA,qCAAqC,4DAAc;;AAEnD,uBAAuB,oDAAY;AACnC;AACA;AACA;AACA;AACA,mBAAmB,gDAAM;AACzB;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,oDAAY;AACpB;AACA;AACA;;AAEA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,iDAAiD;AAC9D;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,mBAAmB;AAChC,aAAa,kBAAkB;AAC/B,aAAa,eAAe;AAC5B,aAAa,SAAS;AACtB,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA,OAAO;AACP;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP,MAAM;AACN;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,aAAa,+BAA+B;AAC5C,aAAa,SAAS;AACtB,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA,WAAW,qEAA6B;AACxC;;AAEA;AACA;AACA,aAAa,+BAA+B;AAC5C,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,mBAAmB;AAChC,aAAa,kBAAkB;AAC/B,aAAa,kBAAkB;AAC/B,aAAa,iBAAiB;AAC9B,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,mBAAmB;AAChC,aAAa,iBAAiB;AAC9B,aAAa,mBAAmB;AAChC,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;;AAEA,4BAA4B,gBAAgB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,6BAA6B;AAC1C,aAAa,uCAAuC;AACpD,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,mBAAmB;AAChC,aAAa,iBAAiB;AAC9B,cAAc,mBAAmB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,iBAAiB;AAC9B,aAAa,oBAAoB;AACjC,aAAa,iBAAiB;AAC9B,cAAc,6BAA6B;AAC3C;AACA;AACA;;AAEA,iBAAiB,sEAA8B;AAC/C;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa,kFAA0C;AACvD,YAAY,kFAA0C;;AAEtD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,8BAA8B,4DAAuB;AACrD,0BAA0B,oFAA4C;AACtE,aAAa,4EAAkC;AAC/C;AACA;AACA,UAAU;AACV;AACA;AACA,2DAA2D,iBAAiB;AAC5E,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA,WAAW;AACX;AACA;AACA,WAAW;AACX;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,SAAS;AACtB,aAAa,eAAe;AAC5B,aAAa,2DAA2D;AACxE,cAAc,yBAAyB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,qBAAqB;AAClC,aAAa,oBAAoB;AACjC,aAAa,iBAAiB;AAC9B,cAAc,6BAA6B;AAC3C;AACA;AACA;;AAEA,iBAAiB,sEAA8B;AAC/C;;AAEA,sBAAsB,oFAA4C;AAClE;;AAEA;AACA;;AAEA,uEAAuE,4EAAkC;AACzG;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT,QAAQ;AACR;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,kBAAkB;AAC/B,aAAa,oBAAoB;AACjC,aAAa,iBAAiB;AAC9B,cAAc,6BAA6B;AAC3C;AACA;AACA;;AAEA,iBAAiB,sEAA8B;AAC/C;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,4DAAc;AACnC;AACA;;AAEA;AACA;AACA;AACA,wBAAwB,wBAAwB;AAChD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA,sBAAsB,kBAAkB;AACxC;AACA,0BAA0B,oFAA4C;AACtE;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,4BAA4B;AACxD;AACA;AACA;AACA,eAAe,4EAAkC;AACjD;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,0EAA0E,gCAAgC;AAC1G;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,aAAa,gCAAgC;AAC7C,aAAa,YAAY;AACzB,aAAa,mBAAmB;AAChC,cAAc;AACd;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;;AAEA;AACA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,gBAAgB;AAC7B,aAAa,oBAAoB;AACjC,aAAa,iBAAiB;AAC9B,aAAa,iBAAiB;AAC9B,aAAa,6BAA6B;AAC1C,aAAa,8BAA8B;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,yDAAW;AACzC;AACA;AACA;;AAEA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,kFAA0C;AACjE;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,mCAAmC,sCAAsC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;;AAEA,gCAAgC,yDAAW;AAC3C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,oDAAY;AACnC;AACA,qBAAqB,oDAAY;AACjC;;AAEA,mBAAmB,gDAAM;AACzB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,gBAAgB;AAC7B,aAAa,oBAAoB;AACjC,aAAa,iBAAiB;AAC9B,aAAa,6BAA6B;AAC1C,aAAa,8BAA8B;AAC3C;AACA,cAAc,kBAAkB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,sEAA8B;;AAE/C;AACA;AACA;AACA,2CAA2C,6BAA6B;AACxE;AACA;AACA,mCAAmC,6BAA6B,oBAAoB,GAAG,kBAAkB;AACzG;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,mBAAmB;AAChC,aAAa,oBAAoB;AACjC,aAAa,iBAAiB;AAC9B,aAAa,6BAA6B;AAC1C,aAAa,8BAA8B;AAC3C;AACA,cAAc,kBAAkB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA,8BAA8B,qBAAqB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,8BAA8B,0BAA0B;AACxD;AACA;AACA;AACA,8BAA8B,uBAAuB;AACrD;AACA;AACA;AACA,8DAA8D,mBAAmB;AACjF;AACA;;AAEA;AACA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,mBAAmB;AAChC,aAAa,gBAAgB;AAC7B,cAAc,wBAAwB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,+CAA+C,kBAAkB;AACjE;AACA;AACA,yBAAyB,uDAAS;AAClC,4BAA4B,kEAAoB;AAChD;AACA;AACA;AACA,+BAA+B,+BAA+B,MAAM;;AAEpE,uBAAuB,kFAA0C;;AAEjE;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,yDAAyD;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,wBAAwB;AAC9E;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,YAAY;AACzB,aAAa,iBAAiB;AAC9B,aAAa,SAAS;AACtB,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;;AAEA;AACA,YAAY,qEAA6B;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;;AAEA;;AAEA;AACA;AACA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,4BAA4B;AACzC,aAAa,QAAQ;AACrB,cAAc,yBAAyB;AACvC;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,aAAa;AAC1B,aAAa,iBAAiB;AAC9B,aAAa,oBAAoB;AACjC,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,mBAAmB;AACjD,oCAAoC,4DAAc;AAClD;AACA;;AAEA;AACA;AACA,iCAAiC,4DAAc;AAC/C;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,aAAa,gCAAgC;AAC7C,aAAa,aAAa;AAC1B,aAAa,iBAAiB;AAC9B,aAAa,wEAAwE;AACrF,aAAa,oBAAoB;AACjC,cAAc,kBAAkB;AAChC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,mDAAmD,gDAAM;AACzD;AACA;AACA;AACA;AACA,mCAAmC,gDAAM;AACzC;AACA;;AAEA;AACA;AACA;AACA,sCAAsC,6BAA6B;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,0BAA0B,gBAAgB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,0BAA0B,iBAAiB;AAC3C;AACA;AACA;AACA,0BAA0B,qBAAqB;AAC/C;AACA;AACA;AACA,0BAA0B,kBAAkB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,sDAAsD,eAAe;AACrE;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa,mCAAmC;AAChD,aAAa,UAAU;AACvB,aAAa,qCAAqC;AAClD,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B,6DAAe;AACzC;AACA;;AAEA,qCAAqC,4DAAc;AACnD;AACA,4BAA4B,uEAA8B;AAC1D;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAgB;AAChB;AACA;AACA,sBAAsB,mBAAmB;AACzC;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,gDAAM;AAChC;AACA;;AAEA;AACA;;AAEA;AACA;AACA,aAAa,mCAAmC;AAChD,aAAa,UAAU;AACvB,aAAa,aAAa;AAC1B,aAAa,wEAAwE;AACrF,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,cAAc;AACd;AACA;AACA,oBAAoB,mBAAmB;AACvC;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA,iEAAe,gBAAgB,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACryCG;AACG;AACE;AACD;AACS;AACC;AACH;AAC8B;AACQ;;;AAGpF,mBAAmB,yDAAG;AACtB,oBAAoB,0DAAK;AACzB,cAAc,2DAAM;AACpB;AACA;AACA,GAAG;AACH,CAAC;;;AAGc;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,+CAAa;AACpC;AACA;AACA;AACA;AACA;AACA,yBAAyB,+DAAQ;AACjC,oDAAoD,+DAAQ;AAC5D;;AAEA,qBAAqB,gEAAwB;AAC7C,yBAAyB,0EAAsB;AAC/C;;AAEA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ,+CAAU;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,4BAA4B;AAClD;AACA;AACA;AACA;AACA;AACA,YAAY,+CAAU;AACtB;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,cAAc,EAAE,GAAG,EAAE,GAAG,EAAE;AAC1B;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA,0BAA0B,uDAAS,2BAA2B,wCAAwC;AACtG;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;ACrKA;AACA;AACA;AAC2C;AACH;AACP;AACA;AACoB;AAChB;AACwB;AACA;AACE;;;AAG/D;AACA,aAAa,QAAQ;AACrB,cAAc,6BAA6B;AAC3C,cAAc,eAAe;AAC7B,cAAc;AACd,6EAA6E;AAC7E;AACA,yCAAyC,qDAAqD;AAC9F,cAAc,+BAA+B;AAC7C,cAAc,SAAS;AACvB;AACA;;;AAGA;AACA;AACA,aAAa,kBAAkB;AAC/B;AACA;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,4BAA4B,wDAAY;;AAExC;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA,yCAAyC;;AAEzC,wCAAwC,MAAM,OAAO,WAAW,YAAY;;AAE5E;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,kCAAkC,SAAS,kBAAkB;AAC7D;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;;;AAGA;AACA;AACA,oDAAoD,QAAQ;AAC5D;AACA;AACA;;;AAGA;AACA,iBAAiB;AACjB;AACA;AACA,iCAAiC,oBAAoB;AACrD;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,iDAAiD;AACjD,iDAAiD;;AAEjD;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,oBAAoB,qBAAqB,MAAM;AAC/C;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,QAAQ;;AAER;AACA;AACA;AACA,QAAQ;;AAER;;AAEA;;AAEA;AACA,cAAc;AACd;AACA;AACA,uBAAuB,kDAAU;;AAEjC;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;;AAEA,6CAA6C;AAC7C;AACA;AACA,8BAA8B;AAC9B;AACA,QAAQ;AACR;;AAEA;AACA;;AAEA;AACA;AACA,YAAY,8DAAsB;AAClC,YAAY,8DAAsB;AAClC,YAAY,+DAAuB;AACnC;;AAEA;AACA;;AAEA,2CAA2C,QAAQ;AACnD;AACA;;AAEA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;;AAEA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;;AAEA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;;AAEA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;;AAEA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,iBAAiB;AACjB;AACA;AACA;;AAEA;AACA,iBAAiB;AACjB;AACA;AACA;;AAEA;AACA,iBAAiB;AACjB;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,mDAAmD;AACnD;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,YAAY;AACZ;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;;AAEA;AACA;AACA,gBAAgB,SAAS;AACzB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,0DAAkB;AACnD;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD;AACpD;AACA;AACA;AACA,aAAa,QAAQ;AACrB,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,QAAQ,IAAI,qBAAqB;AAC1G;AACA;;AAEA;AACA,YAAY;AACZ;AACA;AACA;AACA;;AAEA;AACA,aAAa,YAAY;AACzB;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,2CAA2C,yDAAW;AACtD;AACA;AACA,eAAe,kFAA0C;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,iEAAe,QAAQ,EAAC;;;;;;;;;;;;;;;;;ACntBxB;AACA;AACA;AAC+D;AAC9B;;AAEjC;AACA;AACA,YAAY,SAAS;AACrB,YAAY,eAAe;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;;AAEA;AACA,aAAa;AACb;AACA;AACA;;AAEA;AACA,aAAa;AACb;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;;AAGA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,uBAAuB,yBAAyB;AAChD,aAAa,UAAU;AACvB;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,UAAU;AACvB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,YAAY,qBAAqB;AACjC;AACA;AACA;AACA,+BAA+B,YAAY;AAC3C;AACA;;AAEA;AACA;AACA;AACA;AACA,0CAA0C,2BAA2B;AACrE;;AAEA;AACA,YAAY,YAAY;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,+DAAuB;AACrD;AACA;AACA;AACA,KAAK;;AAEL,sBAAsB,gDAAM;AAC5B;AACA;;AAEA;AACA,YAAY,qBAAqB;AACjC;AACA;AACA;AACA,sCAAsC,YAAY;AAClD;AACA;;AAEA;AACA;AACA,YAAY,YAAY;AACxB;AACA;AACA;AACA,sBAAsB,gDAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;;AAGA,iEAAe,mBAAmB,EAAC;;;;;;;;;;;;;;;;;;;;AClKnC;AACA;AACA;AAC6C;AACA;AACoB;AAChC;;AAEjC,iCAAiC,gEAAwB;AACzD;AACA;AACA;AACA,aAAa,SAAS;AACtB,aAAa,eAAe;AAC5B;AACA,eAAe;AACf;AACA;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oBAAoB,4BAA4B;AAChD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa,gBAAgB;AAC7B,aAAa,qBAAqB;AAClC,cAAc,+BAA+B;AAC7C;AACA;AACA;AACA;AACA,mBAAmB,wEAAgC;AACnD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gBAAgB,gDAAM;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,0BAA0B;AACpD,YAAY,4EAAoC;AAChD;AACA,SAAS;AACT,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,0BAA0B;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA,sBAAsB,0BAA0B;AAChD,QAAQ,4EAAoC;AAC5C;;AAEA;AACA;AACA;AACA,wBAAwB,0BAA0B;AAClD,6DAA6D;AAC7D;AACA;AACA,6BAA6B,gDAAM,YAAY;AAC/C;AACA,OAAO;;AAEP;AACA;AACA,wBAAwB,0BAA0B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,gDAAM;;AAErB,6BAA6B,0DAAY;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAI,oDAAU;AACd,eAAe,gDAAM,qBAAqB,gDAAM;AAChD;;AAEA;AACA,wBAAwB,gDAAM;AAC9B;AACA;AACA,kDAAkD,gCAAgC;AAClF;AACA,KAAK;AACL;;AAEA;AACA,aAAa,qBAAqB;AAClC;AACA;AACA;AACA;AACA;;;AAGA,iEAAe,kBAAkB,EAAC;;;;;;;;;;;;;;;;;;;;;;AC7MlC;AACA;AACA;AACsC;AACD;AACgB;AACW;;;AAGhE;AACA;AACA,aAAa,QAAQ;AACrB,cAAc,eAAe;AAC7B,cAAc,WAAW;AACzB,cAAc,gDAAgD;AAC9D;;;AAGA,kCAAkC,sDAAS;AAC3C;AACA,aAAa,kCAAkC;AAC/C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA,aAAa,MAAM;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA,oBAAoB,8BAA8B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,iBAAiB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,cAAc,eAAe;AAC7B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,oDAAU;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,qDAAS;AACrC,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,sBAAsB,kBAAkB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,wDAAc;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,yDAAmB;AACvB;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA,iEAAe,mBAAmB,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClSnC;AACA;AACA;AACiD;AACJ;AACM;AACN;AACA;AACE;AACQ;AACU;AACR;;AAEzD,iCAAiC,gEAAwB;AACzD;AACA;AACA,aAAa,SAAS;AACtB,aAAa,eAAe;AAC5B,aAAa,wBAAwB;AACrC,eAAe;AACf;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,0CAA0C,4DAAoB;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,kCAAkC;AACxD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,yCAAyC;AACtD,aAAa,mBAAmB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,6BAA6B,2DAAa,wBAAwB,+DAAiB;AACnF;AACA;AACA,sCAAsC,0DAAY;;AAElD;AACA,0BAA0B,6DAAe;AACzC;AACA;;AAEA;AACA;AACA;;AAEA,qCAAqC,4DAAc;AACnD;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,wBAAwB,oDAAU;AAClC;AACA,OAAO;AACP,KAAK;AACL;;AAEA;AACA;AACA,8BAA8B,2DAAa;AAC3C,8BAA8B,0DAAY;AAC1C;AACA;AACA;AACA;AACA,4BAA4B,gDAAM;AAClC;AACA;AACA,KAAK;;AAEL;AACA,iBAAiB,gDAAM;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL,sBAAsB,oDAAU;AAChC;AACA;AACA,KAAK;;AAEL,sBAAsB,oDAAU;AAChC;AACA;AACA,KAAK;;AAEL,sBAAsB,oDAAU;AAChC;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA,iEAAe,kBAAkB,EAAC;;;;;;;;;;;;;;;AC1LlC;AACA;AACA;AACe;AACf;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;ACvCA;AACA;AACA;AAC6D;AACvB;AACJ;AACG;AACO;;;AAG5C;AACA,aAAa,QAAQ;AACrB,cAAc,6BAA6B;AAC3C,cAAc,+BAA+B;AAC7C,cAAc,QAAQ;AACtB;;;AAGA,8BAA8B,yDAAY;AAC1C;AACA,aAAa,QAAQ;AACrB,aAAa,8BAA8B;AAC3C;AACA;AACA,0BAA0B,yDAAyD,IAAI;;AAEvF;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,eAAe,QAAQ;AACvB;AACA;AACA,8BAA8B,mDAAS;;AAEvC;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;;;AAGA;AACA,cAAc;AACd;AACA;AACA;AACA,mCAAmC,8DAAqB;AACxD;AACA;AACA;AACA;;;AAGA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA,uGAAuG;AACvG;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,qBAAqB,oDAAQ,EAAE,cAAc;AAC7C;AACA;AACA;AACA;AACA;;;AAGA;AACA,aAAa,eAAe;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA,aAAa,eAAe;AAC5B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,yCAAyC,gBAAgB;AACzD;AACA;AACA;AACA;AACA;AACA,eAAe,mEAA2B;AAC1C;AACA;AACA,SAAS;AACT,QAAQ;AACR;AACA;AACA;AACA,eAAe,yEAAiC;AAChD;AACA,KAAK;AACL;;;AAGA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,eAAe;AACf;AACA;AACA,yCAAyC,gBAAgB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;;AAE3B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;;;AAGA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;;;AAGA;AACA,cAAc;AACd;AACA;AACA;AACA;;;AAGA;AACA,cAAc;AACd;AACA;AACA;AACA,wBAAwB,8EAAsC;AAC9D;AACA;;;AAGA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA,mBAAmB,gEAAwB;AAC3C;AACA,MAAM,4EAAoC;AAC1C;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,mBAAmB;AAChC,aAAa,SAAS;AACtB,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA,iEAAe,OAAO,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzZvB;AACA;AACA;AACoD;AACT;AACE;AACoB;AACN;AACN;AACE;AACJ;AACM;AACG;AACQ;AACnC;AACwB;AACJ;AACE;;AAEvD;;;AAGA;AACA,aAAa,QAAQ;AACrB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB;;;AAGA;AACA;AACA,aAAa,QAAQ;AACrB,cAAc,QAAQ;AACtB,cAAc,0BAA0B;AACxC,cAAc,kBAAkB;AAChC;;;;AAIA;AACA,aAAa,QAAQ;AACrB,cAAc,oCAAoC;AAClD,cAAc,4CAA4C;AAC1D;;;AAGA;AACA;AACA;AACA,WAAW,eAAe;AAC1B,WAAW,oBAAoB;AAC/B,YAAY,oBAAoB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,WAAW,eAAe;AAC1B,WAAW,oBAAoB;AAC/B,WAAW,QAAQ;AACnB,YAAY,4BAA4B;AACxC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA,WAAW,mBAAmB;AAC9B,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,mBAAmB;AACzC;AACA;AACA;AACA;AACA,GAAG;AACH;;;AAGA;AACA,WAAW,eAAe;AAC1B,WAAW,SAAS;AACpB,WAAW,qBAAqB;AAChC,WAAW,qBAAqB;AAChC,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA,WAAW,gBAAgB;AAC3B,WAAW,QAAQ;AACnB,WAAW,oBAAoB;AAC/B,WAAW,iBAAiB;AAC5B,WAAW,yBAAyB;AACpC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,wDAAwD;AACxD,8CAA8C,gDAAY;AAC1D;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA,WAAW,eAAe;AAC1B,WAAW,QAAQ;AACnB,WAAW,oBAAoB;AAC/B,WAAW,yBAAyB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,WAAW,eAAe;AAC1B,WAAW,oBAAoB;AAC/B,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,WAAW,eAAe;AAC1B,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,WAAW,eAAe;AAC1B,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA,WAAW,eAAe;AAC1B,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA,WAAW,eAAe;AAC1B,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,WAAW,oBAAoB;AAC/B,WAAW,oBAAoB;AAC/B,WAAW,oBAAoB;AAC/B,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,eAAe;AAC1B,WAAW,oBAAoB;AAC/B,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA,WAAW,WAAW;AACtB,WAAW,mBAAmB;AAC9B,YAAY,kBAAkB;AAC9B;AACA;AACA;AACA;AACA,gBAAgB,2DAAe;AAC/B;AACA,IAAI;AACJ;AACA;AACA;;;AAGA;AACA,WAAW,SAAS;AACpB,WAAW,mBAAmB;AAC9B,WAAW,UAAU;AACrB,WAAW,gBAAgB;AAC3B,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,8DAAgB;AACxC,sCAAsC,wEAAwB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,6DAAe;AAChC;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA,wBAAwB,+DAAiB;AACzC,qBAAqB,qEAA4B;;AAEjD;AACA;AACA;AACA;;AAEA;AACA,qBAAqB,mEAAyB;AAC9C;AACA;AACA;AACA;AACA;AACA,IAAI,2BAA2B,iEAAmB;AAClD,qBAAqB,qEAA4B;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,IAAI,2BAA2B,gEAAkB;AACjD,qBAAqB,qEAA4B;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,qDAAe;AACtC;AACA;;AAEA,qBAAqB,+DAAkB;AACvC;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,iBAAiB;AACjB,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,SAAS;AACpB,WAAW,gBAAgB;AAC3B,WAAW,qBAAqB;AAChC,YAAY,sBAAsB;AAClC;AACA;AACA;;AAEA,2BAA2B,yDAAW,0BAA0B,0DAAY;AAC5E,yBAAyB,gEAAe;AACxC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,kCAAkC,WAAW;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA,WAAW,wBAAwB;AACnC,WAAW,sBAAsB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;;AAGA;AACA;AACA,WAAW,eAAe;AAC1B,YAAY,oBAAoB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,WAAW,wBAAwB;AACnC,YAAY,4BAA4B;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,wBAAwB;AAC1C;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA,WAAW,IAAI;AACf,WAAW,oBAAoB;AAC/B,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA,mBAAmB,+CAAa;AAChC,eAAe,+CAAa;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,WAAW,8CAA8C;AACzD,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;;AAGA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY;AACZ;AACA;AACA;AACA;AACA,gBAAgB,uBAAuB;AACvC;AACA;AACA,4BAA4B,EAAE;AAC9B;AACA;AACA;AACA,mCAAmC,0BAA0B;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA,WAAW,SAAS;AACpB,WAAW,eAAe;AAC1B,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;AAGA;AACA,WAAW,eAAe;AAC1B,WAAW,QAAQ;AACnB,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,GAAG;AACH;;;AAGA;AACA;AACA;AACA,WAAW,SAAS;AACpB,WAAW,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,oBAAoB;AAC/B,aAAa,SAAS;AACtB;AACA;AACA,gCAAgC,+CAAa;AAC7C,gCAAgC,+CAAa;AAC7C;AACA;;;AAGO;AACP;AACA,gBAAgB,aAAa;AAC7B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,iEAAe,OAAO,EAAC;;;AAGvB;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,wBAAwB;AACnC,WAAW,sCAAsC;AACjD,YAAY,QAAQ;AACpB;AACA;AACO;AACP;AACA;AACA,oCAAoC;AACpC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,wBAAwB;AACnC,WAAW,sCAAsC;AACjD,YAAY,QAAQ;AACpB;AACA;AACO;AACP;AACA;AACA;AACA,oCAAoC;AACpC;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;AC/0BA;AACA;AACA;AACgD;AACd;AACc;AACS;;;AAGzD;AACA,yBAAyB,8CAAU;AACnC;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,CAAC;;;AAGD;AACA;AACA;AACA;AACA,aAAa,SAAS;AACtB,aAAa,sBAAsB;AACnC,aAAa,qBAAqB;AAClC;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;;AAGA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,oEAA4B;AACrD,uBAAuB;;AAEvB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA,8BAA8B,+CAAa;AAC3C;AACA;AACA;AACA,QAAQ,6BAA6B,+CAAa;AAClD;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,uEAA6B;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,sBAAsB,6BAA6B;AACnD,oBAAoB;AACpB,GAAG;;AAEH;AACA,sBAAsB,6BAA6B;AACnD,oBAAoB;AACpB,GAAG;;AAEH;AACA,sBAAsB,6BAA6B;AACnD;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,sBAAsB,6BAA6B;AACnD;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,sBAAsB,6BAA6B;AACnD;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,sBAAsB,6BAA6B;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,sBAAsB,6BAA6B;AACnD,oBAAoB;AACpB,GAAG;;AAEH;AACA,uBAAuB;AACvB,GAAG;;AAEH;AACA,sBAAsB,6BAA6B;AACnD,oBAAoB;AACpB,GAAG;;AAEH;AACA,sBAAsB,6BAA6B;AACnD,oBAAoB;AACpB,GAAG;;AAEH;AACA,uBAAuB;AACvB,GAAG;;AAEH;AACA,uBAAuB;AACvB;AACA,CAAC;;;AAGD,iEAAe,iBAAiB,EAAC;;;;;;;;;;;;;;;;;ACzQjC;AACA;AACA;AACgE;;;AAGhE;AACA;AACA,aAAa,QAAQ;AACrB,cAAc,8CAA8C;AAC5D,cAAc,6BAA6B;AAC3C,cAAc,4DAA4D;AAC1E,cAAc,6BAA6B;AAC3C;;;AAGA;AACA;AACA;AACA,YAAY,8BAA8B;AAC1C,YAAY,eAAe;AAC3B;AACA;AACA,uDAAuD,MAAM;AAC7D;;AAEA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,8BAA8B,qDAAmB;AACjD;AACA;;AAEA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;;;AAGA,iEAAe,sBAAsB,EAAC;;;;;;;;;;;;;;;;AC/DtC;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;AACO;AACP;AACA;;;AAGA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;AACO;AACP;AACA;;;;;;;;;;;;;;;;;;;;;ACnBA;AACA;AACA;AACA;;;AAGA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY;AACZ;AACA;AACA;AACA;;;AAGA;AACA,UAAU;AACV;AACA;AACA;;;AAGA;AACA,UAAU;AACV;AACA;AACA;;;AAGA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,qEAAqE,2BAA2B;AAChG;AACA;AACA,gDAAgD;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,kBAAkB;AAC7B,aAAa,oBAAoB;AACjC;AACA;AACA,oBAAoB,oBAAoB;AACxC;AACA;;AAEA;AACA,WAAW,eAAe;AAC1B,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,YAAY;AACZ;AACO;AACP;AACA;AACA;AACA;AACA,oBAAoB,sBAAsB;AAC1C;;AAEA;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;AACO;AACP;AACA;;AAEA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,yBAAyB;AACpC;AACO;AACP;AACA;AACA,kBAAkB,YAAY;AAC9B,cAAc;AACd;AACA;AACA,kBAAkB,gBAAgB;AAClC;AACA;AACA;;AAEA;AACA,WAAW,MAAM;AACjB,aAAa,MAAM;AACnB;AACO;AACP;AACA;;AAEA;AACA,WAAW,MAAM;AACjB;AACO;AACP;AACA;AACA;AACA;;AAEA;AACA,WAAW,cAAc;AACzB;AACO;AACP;AACA;AACA;;AAEA,iEAAe,OAAO,EAAC;;;;;;;;;;;AC/IvB;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;ACN0C;;AAEwB;AACJ;AACA;;AAEvB;AAC0B;AACU;;AAErB;AACN;;;AAGhD,iEAAe,yDAAQ,EAAC;;AAExB;AACA;AACA;AACA,gBAAgB,yDAAQ;;AAExB,4BAA4B,qEAAoB;AAChD,0BAA0B,mEAAkB;AAC5C,0BAA0B,mEAAkB;;AAE5C,YAAY,qDAAY;AACxB,8BAA8B,uEAAiB;AAC/C,mCAAmC,4EAAsB;;AAEzD;AACA,0BAA0B,mEAAU;AACpC,uBAAuB,gEAAO", "sources": ["webpack://olcs_unused_var/./src/olcs/AbstractSynchronizer.js", "webpack://olcs_unused_var/./src/olcs/AutoRenderLoop.js", "webpack://olcs_unused_var/./src/olcs/Camera.js", "webpack://olcs_unused_var/./src/olcs/FeatureConverter.js", "webpack://olcs_unused_var/./src/olcs/MVTImageryProvider.js", "webpack://olcs_unused_var/./src/olcs/OLCesium.js", "webpack://olcs_unused_var/./src/olcs/OverlaySynchronizer.js", "webpack://olcs_unused_var/./src/olcs/RasterSynchronizer.js", "webpack://olcs_unused_var/./src/olcs/SynchronizedOverlay.js", "webpack://olcs_unused_var/./src/olcs/VectorSynchronizer.js", "webpack://olcs_unused_var/./src/olcs/contrib/LazyLoader.js", "webpack://olcs_unused_var/./src/olcs/contrib/Manager.js", "webpack://olcs_unused_var/./src/olcs/core.js", "webpack://olcs_unused_var/./src/olcs/core/OLImageryProvider.js", "webpack://olcs_unused_var/./src/olcs/core/VectorLayerCounterpart.js", "webpack://olcs_unused_var/./src/olcs/math.js", "webpack://olcs_unused_var/./src/olcs/util.js", "webpack://olcs_unused_var/external var \"ol.Observable\"", "webpack://olcs_unused_var/external var \"ol.Overlay\"", "webpack://olcs_unused_var/external var \"ol.easing\"", "webpack://olcs_unused_var/external var \"ol.extent\"", "webpack://olcs_unused_var/external var \"ol.format.MVT\"", "webpack://olcs_unused_var/external var \"ol.geom.Geometry\"", "webpack://olcs_unused_var/external var \"ol.geom.Point\"", "webpack://olcs_unused_var/external var \"ol.geom.Polygon\"", "webpack://olcs_unused_var/external var \"ol.geom.SimpleGeometry\"", "webpack://olcs_unused_var/external var \"ol.layer.Group\"", "webpack://olcs_unused_var/external var \"ol.layer.Image\"", "webpack://olcs_unused_var/external var \"ol.layer.Layer\"", "webpack://olcs_unused_var/external var \"ol.layer.Tile\"", "webpack://olcs_unused_var/external var \"ol.layer.Vector\"", "webpack://olcs_unused_var/external var \"ol.layer.VectorTile\"", "webpack://olcs_unused_var/external var \"ol.proj\"", "webpack://olcs_unused_var/external var \"ol.render\"", "webpack://olcs_unused_var/external var \"ol.source\"", "webpack://olcs_unused_var/external var \"ol.source.Cluster\"", "webpack://olcs_unused_var/external var \"ol.source.Image\"", "webpack://olcs_unused_var/external var \"ol.source.ImageStatic\"", "webpack://olcs_unused_var/external var \"ol.source.ImageWMS\"", "webpack://olcs_unused_var/external var \"ol.source.TileImage\"", "webpack://olcs_unused_var/external var \"ol.source.TileWMS\"", "webpack://olcs_unused_var/external var \"ol.source.Vector\"", "webpack://olcs_unused_var/external var \"ol.source.VectorTile\"", "webpack://olcs_unused_var/external var \"ol.structs.LRUCache\"", "webpack://olcs_unused_var/external var \"ol.style.Icon\"", "webpack://olcs_unused_var/external var \"ol.style.Stroke\"", "webpack://olcs_unused_var/external var \"ol.style.Style\"", "webpack://olcs_unused_var/external var \"ol.tilegrid\"", "webpack://olcs_unused_var/external var \"ol.tileurlfunction\"", "webpack://olcs_unused_var/external var \"ol.util\"", "webpack://olcs_unused_var/webpack/bootstrap", "webpack://olcs_unused_var/webpack/runtime/compat get default export", "webpack://olcs_unused_var/webpack/runtime/define property getters", "webpack://olcs_unused_var/webpack/runtime/hasOwnProperty shorthand", "webpack://olcs_unused_var/webpack/runtime/make namespace object", "webpack://olcs_unused_var/./src/index.library.js"], "sourcesContent": ["/**\n * @module olcs.AbstractSynchronizer\n */\nimport {unB<PERSON><PERSON>ey as olObservableUn<PERSON>y<PERSON>ey} from 'ol/Observable.js';\nimport olLayerGroup from 'ol/layer/Group.js';\nimport {olcsListen, getUid} from './util.js';\n\n\nclass AbstractSynchronizer {\n  /**\n   * @param {!ol.Map} map\n   * @param {!Cesium.Scene} scene\n   * @template T\n   * @abstract\n   * @api\n   */\n  constructor(map, scene) {\n    /**\n     * @type {!ol.Map}\n     * @protected\n     */\n    this.map = map;\n\n    /**\n     * @type {ol.View}\n     * @protected\n     */\n    this.view = map.getView();\n\n    /**\n     * @type {!Cesium.Scene}\n     * @protected\n     */\n    this.scene = scene;\n\n    /**\n     * @type {ol.Collection.<ol.layer.Base>}\n     * @protected\n     */\n    this.olLayers = map.getLayerGroup().getLayers();\n\n    /**\n     * @type {ol.layer.Group}\n     */\n    this.mapLayerGroup = map.getLayerGroup();\n\n    /**\n     * Map of OpenLayers layer ids (from getUid) to the Cesium ImageryLayers.\n     * Null value means, that we are unable to create equivalent layers.\n     * @type {Object.<string, ?Array.<T>>}\n     * @protected\n     */\n    this.layerMap = {};\n\n    /**\n     * Map of listen keys for OpenLayers layer layers ids (from getUid).\n     * @type {!Object.<string, Array<ol.EventsKey>>}\n     * @protected\n     */\n    this.olLayerListenKeys = {};\n\n    /**\n     * Map of listen keys for OpenLayers layer groups ids (from getUid).\n     * @type {!Object.<string, !Array.<ol.EventsKey>>}\n     * @private\n     */\n    this.olGroupListenKeys_ = {};\n  }\n\n  /**\n   * Destroy all and perform complete synchronization of the layers.\n   * @api\n   */\n  synchronize() {\n    this.destroyAll();\n    this.addLayers_(this.mapLayerGroup);\n  }\n\n  /**\n   * Order counterparts using the same algorithm as the Openlayers renderer:\n   * z-index then original sequence order.\n   * @protected\n   */\n  orderLayers() {\n    // Ordering logics is handled in subclasses.\n  }\n\n  /**\n   * Add a layer hierarchy.\n   * @param {ol.layer.Base} root\n   * @private\n   */\n  addLayers_(root) {\n    /** @type {Array<import('olsc/core.js').LayerWithParents>} */\n    const fifo = [{\n      layer: root,\n      parents: []\n    }];\n    while (fifo.length > 0) {\n      const olLayerWithParents = fifo.splice(0, 1)[0];\n      const olLayer = olLayerWithParents.layer;\n      const olLayerId = getUid(olLayer).toString();\n      this.olLayerListenKeys[olLayerId] = [];\n      console.assert(!this.layerMap[olLayerId]);\n\n      let cesiumObjects = null;\n      if (olLayer instanceof olLayerGroup) {\n        this.listenForGroupChanges_(olLayer);\n        if (olLayer !== this.mapLayerGroup) {\n          cesiumObjects = this.createSingleLayerCounterparts(olLayerWithParents);\n        }\n        if (!cesiumObjects) {\n          olLayer.getLayers().forEach((l) => {\n            if (l) {\n              const newOlLayerWithParents = {\n                layer: l,\n                parents: olLayer === this.mapLayerGroup ?\n                  [] :\n                  [olLayerWithParents.layer].concat(olLayerWithParents.parents)\n              };\n              fifo.push(newOlLayerWithParents);\n            }\n          });\n        }\n      } else {\n        cesiumObjects = this.createSingleLayerCounterparts(olLayerWithParents);\n        if (!cesiumObjects) {\n          // keep an eye on the layers that once failed to be added (might work when the layer is updated)\n          // for example when a source is set after the layer is added to the map\n          const layerId = olLayerId;\n          const layerWithParents = olLayerWithParents;\n          const onLayerChange = (e) => {\n            const cesiumObjs = this.createSingleLayerCounterparts(layerWithParents);\n            if (cesiumObjs) {\n              // unsubscribe event listener\n              layerWithParents.layer.un('change', onLayerChange);\n              this.addCesiumObjects_(cesiumObjs, layerId, layerWithParents.layer);\n              this.orderLayers();\n            }\n          };\n          this.olLayerListenKeys[olLayerId].push(olcsListen(layerWithParents.layer, 'change', onLayerChange));\n        }\n      }\n      // add Cesium layers\n      if (cesiumObjects) {\n        this.addCesiumObjects_(cesiumObjects, olLayerId, olLayer);\n      }\n    }\n\n    this.orderLayers();\n  }\n\n  /**\n   * Add Cesium objects.\n   * @param {Array.<T>} cesiumObjects\n   * @param {string} layerId\n   * @param {ol.layer.Base} layer\n   * @private\n   */\n  addCesiumObjects_(cesiumObjects, layerId, layer) {\n    this.layerMap[layerId] = cesiumObjects;\n    this.olLayerListenKeys[layerId].push(olcsListen(layer, 'change:zIndex', () => this.orderLayers()));\n    cesiumObjects.forEach((cesiumObject) => {\n      this.addCesiumObject(cesiumObject);\n    });\n  }\n\n  /**\n   * Remove and destroy a single layer.\n   * @param {ol.layer.Layer} layer\n   * @return {boolean} counterpart destroyed\n   * @private\n   */\n  removeAndDestroySingleLayer_(layer) {\n    const uid = getUid(layer).toString();\n    const counterparts = this.layerMap[uid];\n    if (!!counterparts) {\n      counterparts.forEach((counterpart) => {\n        this.removeSingleCesiumObject(counterpart, false);\n        this.destroyCesiumObject(counterpart);\n      });\n      this.olLayerListenKeys[uid].forEach(olObservableUnByKey);\n      delete this.olLayerListenKeys[uid];\n    }\n    delete this.layerMap[uid];\n    return !!counterparts;\n  }\n\n  /**\n   * Unlisten a single layer group.\n   * @param {ol.layer.Group} group\n   * @private\n   */\n  unlistenSingleGroup_(group) {\n    if (group === this.mapLayerGroup) {\n      return;\n    }\n    const uid = getUid(group).toString();\n    const keys = this.olGroupListenKeys_[uid];\n    keys.forEach((key) => {\n      olObservableUnByKey(key);\n    });\n    delete this.olGroupListenKeys_[uid];\n    delete this.layerMap[uid];\n  }\n\n  /**\n   * Remove layer hierarchy.\n   * @param {ol.layer.Base} root\n   * @private\n   */\n  removeLayer_(root) {\n    if (!!root) {\n      const fifo = [root];\n      while (fifo.length > 0) {\n        const olLayer = fifo.splice(0, 1)[0];\n        const done = this.removeAndDestroySingleLayer_(olLayer);\n        if (olLayer instanceof olLayerGroup) {\n          this.unlistenSingleGroup_(olLayer);\n          if (!done) {\n            // No counterpart for the group itself so removing\n            // each of the child layers.\n            olLayer.getLayers().forEach((l) => {\n              fifo.push(l);\n            });\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * Register listeners for single layer group change.\n   * @param {ol.layer.Group} group\n   * @private\n   */\n  listenForGroupChanges_(group) {\n    const uuid = getUid(group).toString();\n\n    console.assert(this.olGroupListenKeys_[uuid] === undefined);\n\n    const listenKeyArray = [];\n    this.olGroupListenKeys_[uuid] = listenKeyArray;\n\n    // only the keys that need to be relistened when collection changes\n    let contentKeys = [];\n    const listenAddRemove = (function() {\n      const collection = group.getLayers();\n      if (collection) {\n        contentKeys = [\n          collection.on('add', (event) => {\n            this.addLayers_(event.element);\n          }),\n          collection.on('remove', (event) => {\n            this.removeLayer_(event.element);\n          })\n        ];\n        listenKeyArray.push(...contentKeys);\n      }\n    }).bind(this);\n\n    listenAddRemove();\n\n    listenKeyArray.push(group.on('change:layers', (e) => {\n      contentKeys.forEach((el) => {\n        const i = listenKeyArray.indexOf(el);\n        if (i >= 0) {\n          listenKeyArray.splice(i, 1);\n        }\n        olObservableUnByKey(el);\n      });\n      listenAddRemove();\n    }));\n  }\n\n  /**\n   * Destroys all the created Cesium objects.\n   * @protected\n   */\n  destroyAll() {\n    this.removeAllCesiumObjects(true); // destroy\n    let objKey;\n    for (objKey in this.olGroupListenKeys_) {\n      const keys = this.olGroupListenKeys_[objKey];\n      keys.forEach(olObservableUnByKey);\n    }\n    for (objKey in this.olLayerListenKeys) {\n      this.olLayerListenKeys[objKey].forEach(olObservableUnByKey);\n    }\n    this.olGroupListenKeys_ = {};\n    this.olLayerListenKeys = {};\n    this.layerMap = {};\n  }\n\n  /**\n   * Adds a single Cesium object to the collection.\n   * @param {!T} object\n   * @abstract\n   * @protected\n   */\n  addCesiumObject(object) {}\n\n  /**\n   * @param {!T} object\n   * @abstract\n   * @protected\n   */\n  destroyCesiumObject(object) {}\n\n  /**\n   * Remove single Cesium object from the collection.\n   * @param {!T} object\n   * @param {boolean} destroy\n   * @abstract\n   * @protected\n   */\n  removeSingleCesiumObject(object, destroy) {}\n\n  /**\n   * Remove all Cesium objects from the collection.\n   * @param {boolean} destroy\n   * @abstract\n   * @protected\n   */\n  removeAllCesiumObjects(destroy) {}\n\n  /**\n   * @param {import('olsc/core.js').LayerWithParents} olLayerWithParents\n   * @return {?Array.<T>}\n   * @abstract\n   * @protected\n   */\n  createSingleLayerCounterparts(olLayerWithParents) {}\n}\n\n\nexport default AbstractSynchronizer;\n", "/**\n * @module olcs.AutoRenderLoop\n */\n\nclass AutoRenderLoop {\n  /**\n   * @constructor\n   * @param {olcs.OLCesium} ol3d\n   */\n  constructor(ol3d) {\n    this.ol3d = ol3d;\n    this.scene_ = ol3d.getCesiumScene();\n    this.canvas_ = this.scene_.canvas;\n    this._boundNotifyRepaintRequired = this.notifyRepaintRequired.bind(this);\n\n    this.repaintEventNames_ = [\n      'mousemove', 'mousedown', 'mouseup',\n      'touchstart', 'touchend', 'touchmove',\n      'pointerdown', 'pointerup', 'pointermove',\n      'wheel'\n    ];\n\n    this.enable();\n  }\n\n  /**\n   * Enable.\n   */\n  enable() {\n    this.scene_.requestRenderMode = true;\n    this.scene_.maximumRenderTimeChange = 1000;\n    for (const repaintKey of this.repaintEventNames_) {\n      this.canvas_.addEventListener(repaintKey, this._boundNotifyRepaintRequired, false);\n    }\n\n    window.addEventListener('resize', this._boundNotifyRepaintRequired, false);\n\n    // Listen for changes on the layer group\n    this.ol3d.getOlMap().getLayerGroup().on('change', this._boundNotifyRepaintRequired);\n  }\n\n  /**\n   * Disable.\n   */\n  disable() {\n    for (const repaintKey of this.repaintEventNames_) {\n      this.canvas_.removeEventListener(repaintKey, this._boundNotifyRepaintRequired, false);\n    }\n\n    window.removeEventListener('resize', this._boundNotifyRepaintRequired, false);\n\n    this.ol3d.getOlMap().getLayerGroup().un('change', this._boundNotifyRepaintRequired);\n    this.scene_.requestRenderMode = false;\n  }\n\n  /**\n   * Restart render loop.\n   * Force a restart of the render loop.\n   * @api\n   */\n  restartRenderLoop() {\n    this.notifyRepaintRequired();\n  }\n\n  notifyRepaintRequired() {\n    this.scene_.requestRender();\n  }\n}\n\n\nexport default AutoRenderLoop;\n", "/**\n * @module olcs.Camera\n */\n\nimport {unB<PERSON><PERSON>ey as olObservableUn<PERSON>y<PERSON><PERSON>} from 'ol/Observable.js';\nimport {toRadians, toDegrees} from './math.js';\nimport {getTransform} from 'ol/proj.js';\nimport olcsCore, {calcDistanceForResolution, calcResolutionForDistance} from './core.js';\n\nclass Camera {\n  /**\n   * This object takes care of additional 3d-specific properties of the view and\n   * ensures proper synchronization with the underlying raw Cesium.Camera object.\n   * @param {!Cesium.Scene} scene\n   * @param {!ol.Map} map\n   * @api\n   */\n  constructor(scene, map) {\n    /**\n     * @type {!Cesium.Scene}\n     * @private\n     */\n    this.scene_ = scene;\n\n    /**\n     * @type {!Cesium.Camera}\n     * @private\n     */\n    this.cam_ = scene.camera;\n\n    /**\n     * @type {!ol.Map}\n     * @private\n     */\n    this.map_ = map;\n\n    /**\n     * @type {?ol.View}\n     * @private\n     */\n    this.view_ = null;\n\n    /**\n     * @type {?ol.<PERSON>}\n     * @private\n     */\n    this.viewListenKey_ = null;\n\n    /**\n     * @type {!ol.TransformFunction}\n     * @private\n     */\n    this.toLonLat_ = Camera.identityProjection;\n\n    /**\n     * @type {!ol.TransformFunction}\n     * @private\n     */\n    this.fromLonLat_ = Camera.identityProjection;\n\n    /**\n     * 0 -- topdown, PI/2 -- the horizon\n     * @type {number}\n     * @private\n     */\n    this.tilt_ = 0;\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.distance_ = 0;\n\n    /**\n     * @type {?Cesium.Matrix4}\n     * @private\n     */\n    this.lastCameraViewMatrix_ = null;\n\n    /**\n     * This is used to discard change events on view caused by updateView method.\n     * @type {boolean}\n     * @private\n     */\n    this.viewUpdateInProgress_ = false;\n\n    this.map_.on('change:view', (e) => {\n      this.setView_(this.map_.getView());\n    });\n    this.setView_(this.map_.getView());\n  }\n\n  /**\n   * @param {Array.<number>} input Input coordinate array.\n   * @param {Array.<number>=} opt_output Output array of coordinate values.\n   * @param {number=} opt_dimension Dimension.\n   * @return {Array.<number>} Input coordinate array (same array as input).\n   */\n  static identityProjection(input, opt_output, opt_dimension) {\n    const dim = opt_dimension || input.length;\n    if (opt_output) {\n      for (let i = 0; i < dim; ++i) {\n        opt_output[i] = input[i];\n      }\n    }\n    return input;\n  }\n\n  /**\n   * @param {?ol.View} view New view to use.\n   * @private\n   */\n  setView_(view) {\n    if (this.view_) {\n      olObservableUnByKey(this.viewListenKey_);\n      this.viewListenKey_ = null;\n    }\n\n    this.view_ = view;\n    if (view) {\n      const toLonLat = getTransform(view.getProjection(), 'EPSG:4326');\n      const fromLonLat = getTransform('EPSG:4326', view.getProjection());\n      console.assert(toLonLat && fromLonLat);\n\n      this.toLonLat_ = toLonLat;\n      this.fromLonLat_ = fromLonLat;\n\n      this.viewListenKey_ = view.on('propertychange', e => this.handleViewEvent_(e));\n\n      this.readFromView();\n    } else {\n      this.toLonLat_ = Camera.identityProjection;\n      this.fromLonLat_ = Camera.identityProjection;\n    }\n  }\n\n  /**\n   * @param {?} e\n   * @private\n   */\n  handleViewEvent_(e) {\n    if (!this.viewUpdateInProgress_) {\n      this.readFromView();\n    }\n  }\n\n  /**\n   * @param {number} heading In radians.\n   * @api\n   */\n  setHeading(heading) {\n    if (!this.view_) {\n      return;\n    }\n\n    this.view_.setRotation(heading);\n  }\n\n  /**\n   * @return {number|undefined} Heading in radians.\n   * @api\n   */\n  getHeading() {\n    if (!this.view_) {\n      return undefined;\n    }\n    const rotation = this.view_.getRotation();\n    return rotation || 0;\n  }\n\n  /**\n   * @param {number} tilt In radians.\n   * @api\n   */\n  setTilt(tilt) {\n    this.tilt_ = tilt;\n    this.updateCamera_();\n  }\n\n  /**\n   * @return {number} Tilt in radians.\n   * @api\n   */\n  getTilt() {\n    return this.tilt_;\n  }\n\n  /**\n   * @param {number} distance In meters.\n   * @api\n   */\n  setDistance(distance) {\n    this.distance_ = distance;\n    this.updateCamera_();\n    this.updateView();\n  }\n\n  /**\n   * @return {number} Distance in meters.\n   * @api\n   */\n  getDistance() {\n    return this.distance_;\n  }\n\n  /**\n   * Shortcut for ol.View.setCenter().\n   * @param {!ol.Coordinate} center Same projection as the ol.View.\n   * @api\n   */\n  setCenter(center) {\n    if (!this.view_) {\n      return;\n    }\n    this.view_.setCenter(center);\n  }\n\n  /**\n   * Shortcut for ol.View.getCenter().\n   * @return {ol.Coordinate|undefined} Same projection as the ol.View.\n   * @api\n   */\n  getCenter() {\n    if (!this.view_) {\n      return undefined;\n    }\n    return this.view_.getCenter();\n  }\n\n  /**\n   * Sets the position of the camera.\n   * @param {!ol.Coordinate} position Same projection as the ol.View.\n   * @api\n   */\n  setPosition(position) {\n    if (!this.toLonLat_) {\n      return;\n    }\n    const ll = this.toLonLat_(position);\n    console.assert(ll);\n\n    const carto = new Cesium.Cartographic(\n        toRadians(ll[0]),\n        toRadians(ll[1]),\n        this.getAltitude());\n\n    this.cam_.setView({\n      destination: Cesium.Ellipsoid.WGS84.cartographicToCartesian(carto)\n    });\n    this.updateView();\n  }\n\n  /**\n   * Calculates position under the camera.\n   * @return {!ol.Coordinate|undefined} Same projection as the ol.View.\n   * @api\n   */\n  getPosition() {\n    if (!this.fromLonLat_) {\n      return undefined;\n    }\n    const carto = Cesium.Ellipsoid.WGS84.cartesianToCartographic(this.cam_.position);\n\n    const pos = this.fromLonLat_([\n      toDegrees(carto.longitude),\n      toDegrees(carto.latitude)\n    ]);\n    console.assert(pos);\n    return pos;\n  }\n\n  /**\n   * @param {number} altitude In meters.\n   * @api\n   */\n  setAltitude(altitude) {\n    const carto = Cesium.Ellipsoid.WGS84.cartesianToCartographic(\n        this.cam_.position);\n    carto.height = altitude;\n    this.cam_.position = Cesium.Ellipsoid.WGS84.cartographicToCartesian(carto);\n\n    this.updateView();\n  }\n\n  /**\n   * @return {number} Altitude in meters.\n   * @api\n   */\n  getAltitude() {\n    const carto = Cesium.Ellipsoid.WGS84.cartesianToCartographic(\n        this.cam_.position);\n\n    return carto.height;\n  }\n\n  /**\n   * Updates the state of the underlying Cesium.Camera\n   * according to the current values of the properties.\n   * @private\n   */\n  updateCamera_() {\n    if (!this.view_ || !this.toLonLat_) {\n      return;\n    }\n    const center = this.view_.getCenter();\n    if (!center) {\n      return;\n    }\n    const ll = this.toLonLat_(center);\n    console.assert(ll);\n\n    const carto = new Cesium.Cartographic(toRadians(ll[0]),\n        toRadians(ll[1]));\n    if (this.scene_.globe) {\n      const height = this.scene_.globe.getHeight(carto);\n      carto.height = height || 0;\n    }\n\n    const destination = Cesium.Ellipsoid.WGS84.cartographicToCartesian(carto);\n\n    /** @type {Cesium.optionsOrientation} */\n    const orientation = {\n      pitch: this.tilt_ - Cesium.Math.PI_OVER_TWO,\n      heading: -this.view_.getRotation(),\n      roll: undefined\n    };\n    this.cam_.setView({\n      destination,\n      orientation\n    });\n\n    this.cam_.moveBackward(this.distance_);\n\n    this.checkCameraChange(true);\n  }\n\n  /**\n   * Calculates the values of the properties from the current ol.View state.\n   * @api\n   */\n  readFromView() {\n    if (!this.view_ || !this.toLonLat_) {\n      return;\n    }\n    const center = this.view_.getCenter();\n    if (center === undefined || center === null) {\n      return;\n    }\n    const ll = this.toLonLat_(center);\n    console.assert(ll);\n\n    const resolution = this.view_.getResolution();\n    this.distance_ = this.calcDistanceForResolution(\n        resolution || 0, toRadians(ll[1]));\n\n    this.updateCamera_();\n  }\n\n  /**\n   * Calculates the values of the properties from the current Cesium.Camera state.\n   * Modifies the center, resolution and rotation properties of the view.\n   * @api\n   */\n  updateView() {\n    if (!this.view_ || !this.fromLonLat_) {\n      return;\n    }\n    this.viewUpdateInProgress_ = true;\n\n    // target & distance\n    const ellipsoid = Cesium.Ellipsoid.WGS84;\n    const scene = this.scene_;\n    const target = olcsCore.pickCenterPoint(scene);\n\n    let bestTarget = target;\n    if (!bestTarget) {\n      //TODO: how to handle this properly ?\n      const globe = scene.globe;\n      const carto = this.cam_.positionCartographic.clone();\n      const height = globe.getHeight(carto);\n      carto.height = height || 0;\n      bestTarget = Cesium.Ellipsoid.WGS84.cartographicToCartesian(carto);\n    }\n    this.distance_ = Cesium.Cartesian3.distance(bestTarget, this.cam_.position);\n    const bestTargetCartographic = ellipsoid.cartesianToCartographic(bestTarget);\n    this.view_.setCenter(this.fromLonLat_([\n      toDegrees(bestTargetCartographic.longitude),\n      toDegrees(bestTargetCartographic.latitude)]));\n\n    // resolution\n    this.view_.setResolution(\n        this.calcResolutionForDistance(this.distance_,\n            bestTargetCartographic ? bestTargetCartographic.latitude : 0));\n\n\n    /*\n     * Since we are positioning the target, the values of heading and tilt\n     * need to be calculated _at the target_.\n     */\n    if (target) {\n      const pos = this.cam_.position;\n\n      // normal to the ellipsoid at the target\n      const targetNormal = new Cesium.Cartesian3();\n      ellipsoid.geocentricSurfaceNormal(target, targetNormal);\n\n      // vector from the target to the camera\n      const targetToCamera = new Cesium.Cartesian3();\n      Cesium.Cartesian3.subtract(pos, target, targetToCamera);\n      Cesium.Cartesian3.normalize(targetToCamera, targetToCamera);\n\n\n      // HEADING\n      const up = this.cam_.up;\n      const right = this.cam_.right;\n      const normal = new Cesium.Cartesian3(-target.y, target.x, 0); // what is it?\n      const heading = Cesium.Cartesian3.angleBetween(right, normal);\n      const cross = Cesium.Cartesian3.cross(target, up, new Cesium.Cartesian3());\n      const orientation = cross.z;\n\n      this.view_.setRotation((orientation < 0 ? heading : -heading));\n\n      // TILT\n      const tiltAngle = Math.acos(\n          Cesium.Cartesian3.dot(targetNormal, targetToCamera));\n      this.tilt_ = isNaN(tiltAngle) ? 0 : tiltAngle;\n    } else {\n      // fallback when there is no target\n      this.view_.setRotation(this.cam_.heading);\n      this.tilt_ = -this.cam_.pitch + Math.PI / 2;\n    }\n\n    this.viewUpdateInProgress_ = false;\n  }\n\n  /**\n   * Check if the underlying camera state has changed and ensure synchronization.\n   * @param {boolean=} opt_dontSync Do not synchronize the view.\n   */\n  checkCameraChange(opt_dontSync) {\n    const old = this.lastCameraViewMatrix_;\n    const current = this.cam_.viewMatrix;\n\n    if (!old || !Cesium.Matrix4.equalsEpsilon(old, current, 1e-5)) {\n      this.lastCameraViewMatrix_ = current.clone();\n      if (opt_dontSync !== true) {\n        this.updateView();\n      }\n    }\n  }\n\n  /**\n   * calculate the distance between camera and centerpoint based on the resolution and latitude value\n   * @param {number} resolution Number of map units per pixel.\n   * @param {number} latitude Latitude in radians.\n   * @return {number} The calculated distance.\n   * @api\n   */\n  calcDistanceForResolution(resolution, latitude) {\n    return calcDistanceForResolution(resolution, latitude, this.scene_, this.view_.getProjection());\n  }\n\n  /**\n   * calculate the resolution based on a distance(camera to position) and latitude value\n   * @param {number} distance\n   * @param {number} latitude\n   * @return {number} The calculated resolution.\n   * @api\n   */\n  calcResolutionForDistance(distance, latitude) {\n    return calcResolutionForDistance(distance, latitude, this.scene_, this.view_.getProjection());\n  }\n}\n\n\nexport default Camera;\n", "/**\n * @module olcs.FeatureConverter\n */\nimport olGeomGeometry from 'ol/geom/Geometry.js';\nimport olStyleIcon from 'ol/style/Icon.js';\nimport olSourceVector from 'ol/source/Vector.js';\nimport olSourceCluster from 'ol/source/Cluster.js';\nimport {circular as olCreateCircularPolygon} from 'ol/geom/Polygon.js';\nimport {boundingExtent, getCenter} from 'ol/extent.js';\nimport olGeomSimpleGeometry from 'ol/geom/SimpleGeometry.js';\nimport olcsCore from './core.js';\nimport olcsCoreVectorLayerCounterpart from './core/VectorLayerCounterpart.js';\nimport olcsUtil, {getUid, isGroundPolylinePrimitiveSupported} from './util.js';\n\n\n/**\n * @typedef {Object} ModelStyle\n * @property {Cesium.Matrix4} [debugModelMatrix]\n * @property {Cesium.ModelFromGltfOptions} cesiumOptions\n */\n\n\nclass FeatureConverter {\n  /**\n   * Concrete base class for converting from OpenLayers3 vectors to Cesium\n   * primitives.\n   * Extending this class is possible provided that the extending class and\n   * the library are compiled together by the closure compiler.\n   * @param {!Cesium.Scene} scene Cesium scene.\n   * @constructor\n   * @api\n   */\n  constructor(scene) {\n\n    /**\n     * @protected\n     */\n    this.scene = scene;\n\n    /**\n     * Bind once to have a unique function for using as a listener\n     * @type {function(ol.source.Vector.Event)}\n     * @private\n     */\n    this.boundOnRemoveOrClearFeatureListener_ = this.onRemoveOrClearFeature_.bind(this);\n\n    /**\n     * @type {Cesium.Cartesian3}\n     * @private\n     */\n    this.defaultBillboardEyeOffset_ = new Cesium.Cartesian3(0, 0, 10);\n  }\n\n  /**\n   * @param {ol.source.Vector.Event} evt\n   * @private\n   */\n  onRemoveOrClearFeature_(evt) {\n    const source = evt.target;\n    console.assert(source instanceof olSourceVector);\n\n    const cancellers = olcsUtil.obj(source)['olcs_cancellers'];\n    if (cancellers) {\n      const feature = evt.feature;\n      if (feature) {\n        // remove\n        const id = getUid(feature);\n        const canceller = cancellers[id];\n        if (canceller) {\n          canceller();\n          delete cancellers[id];\n        }\n      } else {\n        // clear\n        for (const key in cancellers) {\n          if (cancellers.hasOwnProperty(key)) {\n            cancellers[key]();\n          }\n        }\n        olcsUtil.obj(source)['olcs_cancellers'] = {};\n      }\n    }\n  }\n\n  /**\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature.\n   * @param {!Cesium.Primitive|Cesium.Label|Cesium.Billboard} primitive\n   * @protected\n   */\n  setReferenceForPicking(layer, feature, primitive) {\n    primitive.olLayer = layer;\n    primitive.olFeature = feature;\n  }\n\n  /**\n   * Basics primitive creation using a color attribute.\n   * Note that Cesium has 'interior' and outline geometries.\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature.\n   * @param {!ol.geom.Geometry} olGeometry OpenLayers geometry.\n   * @param {!Cesium.Geometry} geometry\n   * @param {!Cesium.Color} color\n   * @param {number=} opt_lineWidth\n   * @return {Cesium.Primitive}\n   * @protected\n   */\n  createColoredPrimitive(layer, feature, olGeometry, geometry, color, opt_lineWidth) {\n    const createInstance = function(geometry, color) {\n      const instance = new Cesium.GeometryInstance({\n        // always update Cesium externs before adding a property\n        geometry\n      });\n      if (color && !(color instanceof Cesium.ImageMaterialProperty)) {\n        instance.attributes = {\n          color: Cesium.ColorGeometryInstanceAttribute.fromColor(color)\n        };\n      }\n      return instance;\n    };\n\n    const options = {\n      // always update Cesium externs before adding a property\n      flat: true, // work with all geometries\n      renderState: {\n        depthTest: {\n          enabled: true\n        }\n      }\n    };\n\n    if (opt_lineWidth !== undefined) {\n      if (!options.renderState) {\n        options.renderState = {};\n      }\n      options.renderState.lineWidth = opt_lineWidth;\n    }\n\n    const instances = createInstance(geometry, color);\n\n    const heightReference = this.getHeightReference(layer, feature, olGeometry);\n\n    let primitive;\n\n    if (heightReference === Cesium.HeightReference.CLAMP_TO_GROUND) {\n      const ctor = instances.geometry.constructor;\n      if (ctor && !ctor['createShadowVolume']) {\n        return null;\n      }\n      primitive = new Cesium.GroundPrimitive({\n        geometryInstances: instances\n      });\n    } else {\n      primitive = new Cesium.Primitive({\n        geometryInstances: instances\n      });\n    }\n\n    if (color instanceof Cesium.ImageMaterialProperty) {\n      const dataUri = color.image.getValue().toDataURL();\n\n      primitive.appearance = new Cesium.MaterialAppearance({\n        flat: true,\n        renderState: {\n          depthTest: {\n            enabled: true\n          }\n        },\n        material: new Cesium.Material({\n          fabric: {\n            type: 'Image',\n            uniforms: {\n              image: dataUri\n            }\n          }\n        })\n      });\n    } else {\n      primitive.appearance = new Cesium.PerInstanceColorAppearance(options);\n    }\n\n    this.setReferenceForPicking(layer, feature, primitive);\n    return primitive;\n  }\n\n  /**\n   * Return the fill or stroke color from a plain ol style.\n   * @param {!ol.style.Style|ol.style.Text} style\n   * @param {boolean} outline\n   * @return {!Cesium.Color}\n   * @protected\n   */\n  extractColorFromOlStyle(style, outline) {\n    const fillColor = style.getFill() ? style.getFill().getColor() : null;\n    const strokeColor = style.getStroke() ? style.getStroke().getColor() : null;\n\n    let olColor = 'black';\n    if (strokeColor && outline) {\n      olColor = strokeColor;\n    } else if (fillColor) {\n      olColor = fillColor;\n    }\n\n    return olcsCore.convertColorToCesium(olColor);\n  }\n\n  /**\n   * Return the width of stroke from a plain ol style.\n   * @param {!ol.style.Style|ol.style.Text} style\n   * @return {number}\n   * @protected\n   */\n  extractLineWidthFromOlStyle(style) {\n    // Handling of line width WebGL limitations is handled by Cesium.\n    const width = style.getStroke() ? style.getStroke().getWidth() : undefined;\n    return width !== undefined ? width : 1;\n  }\n\n  /**\n   * Create a primitive collection out of two Cesium geometries.\n   * Only the OpenLayers style colors will be used.\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature.\n   * @param {!ol.geom.Geometry} olGeometry OpenLayers geometry.\n   * @param {!Cesium.Geometry} fillGeometry\n   * @param {!Cesium.Geometry} outlineGeometry\n   * @param {!ol.style.Style} olStyle\n   * @return {!Cesium.PrimitiveCollection}\n   * @protected\n   */\n  wrapFillAndOutlineGeometries(layer, feature, olGeometry, fillGeometry, outlineGeometry, olStyle) {\n    const fillColor = this.extractColorFromOlStyle(olStyle, false);\n    const outlineColor = this.extractColorFromOlStyle(olStyle, true);\n\n    const primitives = new Cesium.PrimitiveCollection();\n    if (olStyle.getFill()) {\n      const p1 = this.createColoredPrimitive(layer, feature, olGeometry,\n          fillGeometry, fillColor);\n      console.assert(!!p1);\n      primitives.add(p1);\n    }\n\n    if (olStyle.getStroke() && outlineGeometry) {\n      const width = this.extractLineWidthFromOlStyle(olStyle);\n      const p2 = this.createColoredPrimitive(layer, feature, olGeometry,\n          outlineGeometry, outlineColor, width);\n      if (p2) {\n        // Some outline geometries are not supported by Cesium in clamp to ground\n        // mode. These primitives are skipped.\n        primitives.add(p2);\n      }\n    }\n\n    return primitives;\n  }\n\n  // Geometry converters\n  /**\n   * Create a Cesium primitive if style has a text component.\n   * Eventually return a PrimitiveCollection including current primitive.\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature..\n   * @param {!ol.geom.Geometry} geometry\n   * @param {!ol.style.Style} style\n   * @param {!Cesium.Primitive} primitive current primitive\n   * @return {!Cesium.PrimitiveCollection}\n   * @protected\n   */\n  addTextStyle(layer, feature, geometry, style, primitive) {\n    let primitives;\n    if (!(primitive instanceof Cesium.PrimitiveCollection)) {\n      primitives = new Cesium.PrimitiveCollection();\n      primitives.add(primitive);\n    } else {\n      primitives = primitive;\n    }\n\n    if (!style.getText()) {\n      return primitives;\n    }\n\n    const text = /** @type {!ol.style.Text} */ (style.getText());\n    const label = this.olGeometry4326TextPartToCesium(layer, feature, geometry,\n        text);\n    if (label) {\n      primitives.add(label);\n    }\n    return primitives;\n  }\n\n  /**\n   * Add a billboard to a Cesium.BillboardCollection.\n   * Overriding this wrapper allows manipulating the billboard options.\n   * @param {!Cesium.BillboardCollection} billboards\n   * @param {!Cesium.optionsBillboardCollectionAdd} bbOptions\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature.\n   * @param {!ol.geom.Geometry} geometry\n   * @param {!ol.style.Style} style\n   * @return {!Cesium.Billboard} newly created billboard\n   * @api\n   */\n  csAddBillboard(billboards, bbOptions, layer, feature, geometry, style) {\n    if (!bbOptions.eyeOffset) {\n      bbOptions.eyeOffset = this.defaultBillboardEyeOffset_;\n    }\n    const bb = billboards.add(bbOptions);\n    this.setReferenceForPicking(layer, feature, bb);\n    return bb;\n  }\n\n  /**\n   * Convert an OpenLayers circle geometry to Cesium.\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature..\n   * @param {!ol.geom.Circle} olGeometry OpenLayers circle geometry.\n   * @param {!ol.ProjectionLike} projection\n   * @param {!ol.style.Style} olStyle\n   * @return {!Cesium.PrimitiveCollection} primitives\n   * @api\n   */\n  olCircleGeometryToCesium(layer, feature, olGeometry, projection, olStyle) {\n\n    olGeometry = olcsCore.olGeometryCloneTo4326(olGeometry, projection);\n    console.assert(olGeometry.getType() == 'Circle');\n\n    // ol.Coordinate\n    let center = olGeometry.getCenter();\n    const height = center.length == 3 ? center[2] : 0.0;\n    let point = center.slice();\n    point[0] += olGeometry.getRadius();\n\n    // Cesium\n    center = olcsCore.ol4326CoordinateToCesiumCartesian(center);\n    point = olcsCore.ol4326CoordinateToCesiumCartesian(point);\n\n    // Accurate computation of straight distance\n    const radius = Cesium.Cartesian3.distance(center, point);\n\n    const fillGeometry = new Cesium.CircleGeometry({\n      // always update Cesium externs before adding a property\n      center,\n      radius,\n      height\n    });\n\n    let outlinePrimitive, outlineGeometry;\n    if (this.getHeightReference(layer, feature, olGeometry) === Cesium.HeightReference.CLAMP_TO_GROUND) {\n      const width = this.extractLineWidthFromOlStyle(olStyle);\n      if (width) {\n        const circlePolygon = olCreateCircularPolygon(olGeometry.getCenter(), radius);\n        const positions = olcsCore.ol4326CoordinateArrayToCsCartesians(circlePolygon.getLinearRing(0).getCoordinates());\n        if (!isGroundPolylinePrimitiveSupported(this.scene)) {\n          const color = this.extractColorFromOlStyle(olStyle, true);\n          outlinePrimitive = this.createStackedGroundCorridors(layer, feature, width, color, positions);\n        } else {\n          outlinePrimitive = new Cesium.GroundPolylinePrimitive({\n            geometryInstances: new Cesium.GeometryInstance({\n              geometry: new Cesium.GroundPolylineGeometry({positions, width}),\n            }),\n            appearance: new Cesium.PolylineMaterialAppearance({\n              material: this.olStyleToCesium(feature, olStyle, true),\n            }),\n            classificationType: Cesium.ClassificationType.TERRAIN,\n          });\n          outlinePrimitive.readyPromise.then(() => {\n            this.setReferenceForPicking(layer, feature, outlinePrimitive._primitive);\n          });\n        }\n      }\n    } else {\n      outlineGeometry = new Cesium.CircleOutlineGeometry({\n        // always update Cesium externs before adding a property\n        center,\n        radius,\n        extrudedHeight: height,\n        height\n      });\n    }\n\n    const primitives = this.wrapFillAndOutlineGeometries(\n        layer, feature, olGeometry, fillGeometry, outlineGeometry, olStyle);\n\n    if (outlinePrimitive) {\n      primitives.add(outlinePrimitive);\n    }\n    return this.addTextStyle(layer, feature, olGeometry, olStyle, primitives);\n  }\n\n  /**\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature..\n   * @param {!number} width The width of the line.\n   * @param {!Cesium.Color} color The color of the line.\n   * @param {!Array<Cesium.Cartesian3>|Array<Array<Cesium.Cartesian3>>} positions The vertices of the line(s).\n   * @return {!Cesium.GroundPrimitive} primitive\n   */\n  createStackedGroundCorridors(layer, feature, width, color, positions) {\n    // Convert positions to an Array if it isn't\n    if (!Array.isArray(positions[0])) {\n      positions = [positions];\n    }\n    width = Math.max(3, width); // A <3px width is too small for ground primitives\n    const geometryInstances = [];\n    let previousDistance = 0;\n    // A stack of ground lines with increasing width (in meters) are created.\n    // Only one of these lines is displayed at any time giving a feeling of continuity.\n    // The values for the distance and width factor are more or less arbitrary.\n    // Applications can override this logics by subclassing the FeatureConverter class.\n    for (const distance of [1000, 4000, 16000, 64000, 254000, 1000000, 10000000]) {\n      width *= 2.14;\n      const geometryOptions = {\n        // always update Cesium externs before adding a property\n        width,\n        vertexFormat: Cesium.VertexFormat.POSITION_ONLY\n      };\n      for (const linePositions of positions) {\n        geometryOptions.positions = linePositions;\n        geometryInstances.push(new Cesium.GeometryInstance({\n          geometry: new Cesium.CorridorGeometry(geometryOptions),\n          attributes: {\n            color: Cesium.ColorGeometryInstanceAttribute.fromColor(color),\n            distanceDisplayCondition: new Cesium.DistanceDisplayConditionGeometryInstanceAttribute(previousDistance, distance - 1)\n          }\n        }));\n      }\n      previousDistance = distance;\n    }\n    return new Cesium.GroundPrimitive({\n      // always update Cesium externs before adding a property\n      geometryInstances\n    });\n  }\n\n  /**\n   * Convert an OpenLayers line string geometry to Cesium.\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature..\n   * @param {!ol.geom.LineString} olGeometry OpenLayers line string geometry.\n   * @param {!ol.ProjectionLike} projection\n   * @param {!ol.style.Style} olStyle\n   * @return {!Cesium.PrimitiveCollection} primitives\n   * @api\n   */\n  olLineStringGeometryToCesium(layer, feature, olGeometry, projection, olStyle) {\n\n    olGeometry = olcsCore.olGeometryCloneTo4326(olGeometry, projection);\n    console.assert(olGeometry.getType() == 'LineString');\n\n    const positions = olcsCore.ol4326CoordinateArrayToCsCartesians(olGeometry.getCoordinates());\n    const width = this.extractLineWidthFromOlStyle(olStyle);\n\n    let outlinePrimitive;\n    const heightReference = this.getHeightReference(layer, feature, olGeometry);\n\n    if (heightReference === Cesium.HeightReference.CLAMP_TO_GROUND && !isGroundPolylinePrimitiveSupported(this.scene)) {\n      const color = this.extractColorFromOlStyle(olStyle, true);\n      outlinePrimitive = this.createStackedGroundCorridors(layer, feature, width, color, positions);\n    } else {\n      const appearance = new Cesium.PolylineMaterialAppearance({\n        // always update Cesium externs before adding a property\n        material: this.olStyleToCesium(feature, olStyle, true)\n      });\n      const geometryOptions = {\n        // always update Cesium externs before adding a property\n        positions,\n        width,\n      };\n      const primitiveOptions = {\n        // always update Cesium externs before adding a property\n        appearance\n      };\n      if (heightReference === Cesium.HeightReference.CLAMP_TO_GROUND) {\n        const geometry = new Cesium.GroundPolylineGeometry(geometryOptions);\n        primitiveOptions.geometryInstances = new Cesium.GeometryInstance({\n          geometry\n        }),\n        outlinePrimitive = new Cesium.GroundPolylinePrimitive(primitiveOptions);\n        outlinePrimitive.readyPromise.then(() => {\n          this.setReferenceForPicking(layer, feature, outlinePrimitive._primitive);\n        });\n      } else {\n        geometryOptions.vertexFormat = appearance.vertexFormat;\n        const geometry = new Cesium.PolylineGeometry(geometryOptions);\n        primitiveOptions.geometryInstances = new Cesium.GeometryInstance({\n          geometry\n        }),\n        outlinePrimitive = new Cesium.Primitive(primitiveOptions);\n      }\n    }\n\n    this.setReferenceForPicking(layer, feature, outlinePrimitive);\n\n    return this.addTextStyle(layer, feature, olGeometry, olStyle, outlinePrimitive);\n  }\n\n  /**\n   * Convert an OpenLayers polygon geometry to Cesium.\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature..\n   * @param {!ol.geom.Polygon} olGeometry OpenLayers polygon geometry.\n   * @param {!ol.ProjectionLike} projection\n   * @param {!ol.style.Style} olStyle\n   * @return {!Cesium.PrimitiveCollection} primitives\n   * @api\n   */\n  olPolygonGeometryToCesium(layer, feature, olGeometry, projection, olStyle) {\n\n    olGeometry = olcsCore.olGeometryCloneTo4326(olGeometry, projection);\n    console.assert(olGeometry.getType() == 'Polygon');\n\n    const heightReference = this.getHeightReference(layer, feature, olGeometry);\n\n    let fillGeometry, outlineGeometry, outlinePrimitive;\n    if ((olGeometry.getCoordinates()[0].length == 5) &&\n        (feature.getGeometry().get('olcs.polygon_kind') === 'rectangle')) {\n      // Create a rectangle according to the longitude and latitude curves\n      const coordinates = olGeometry.getCoordinates()[0];\n      // Extract the West, South, East, North coordinates\n      const extent = boundingExtent(coordinates);\n      const rectangle = Cesium.Rectangle.fromDegrees(extent[0], extent[1],\n          extent[2], extent[3]);\n\n      // Extract the average height of the vertices\n      let maxHeight = 0.0;\n      if (coordinates[0].length == 3) {\n        for (let c = 0; c < coordinates.length; c++) {\n          maxHeight = Math.max(maxHeight, coordinates[c][2]);\n        }\n      }\n\n      // Render the cartographic rectangle\n      fillGeometry = new Cesium.RectangleGeometry({\n        ellipsoid: Cesium.Ellipsoid.WGS84,\n        rectangle,\n        height: maxHeight\n      });\n\n      outlineGeometry = new Cesium.RectangleOutlineGeometry({\n        ellipsoid: Cesium.Ellipsoid.WGS84,\n        rectangle,\n        height: maxHeight\n      });\n    } else {\n      const rings = olGeometry.getLinearRings();\n      // always update Cesium externs before adding a property\n      const hierarchy = {};\n      const polygonHierarchy = hierarchy;\n      console.assert(rings.length > 0);\n\n      for (let i = 0; i < rings.length; ++i) {\n        const olPos = rings[i].getCoordinates();\n        const positions = olcsCore.ol4326CoordinateArrayToCsCartesians(olPos);\n        console.assert(positions && positions.length > 0);\n        if (i == 0) {\n          hierarchy.positions = positions;\n        } else {\n          if (!hierarchy.holes) {\n            hierarchy.holes = [];\n          }\n          hierarchy.holes.push({\n            positions\n          });\n        }\n      }\n\n      fillGeometry = new Cesium.PolygonGeometry({\n        // always update Cesium externs before adding a property\n        polygonHierarchy,\n        perPositionHeight: true\n      });\n\n      // Since Cesium doesn't yet support Polygon outlines on terrain yet (coming soon...?)\n      // we don't create an outline geometry if clamped, but instead do the polyline method\n      // for each ring. Most of this code should be removeable when Cesium adds\n      // support for Polygon outlines on terrain.\n      if (heightReference === Cesium.HeightReference.CLAMP_TO_GROUND) {\n        const width = this.extractLineWidthFromOlStyle(olStyle);\n        if (width > 0) {\n          const positions = [hierarchy.positions];\n          if (hierarchy.holes) {\n            for (let i = 0; i < hierarchy.holes.length; ++i) {\n              positions.push(hierarchy.holes[i].positions);\n            }\n          }\n          if (!isGroundPolylinePrimitiveSupported(this.scene)) {\n            const color = this.extractColorFromOlStyle(olStyle, true);\n            outlinePrimitive = this.createStackedGroundCorridors(layer, feature, width, color, positions);\n          } else {\n            const appearance = new Cesium.PolylineMaterialAppearance({\n              // always update Cesium externs before adding a property\n              material: this.olStyleToCesium(feature, olStyle, true)\n            });\n            const geometryInstances = [];\n            for (const linePositions of positions) {\n              const polylineGeometry = new Cesium.GroundPolylineGeometry({positions: linePositions, width});\n              geometryInstances.push(new Cesium.GeometryInstance({\n                geometry: polylineGeometry\n              }));\n            }\n            const primitiveOptions = {\n              // always update Cesium externs before adding a property\n              appearance,\n              geometryInstances\n            };\n            outlinePrimitive = new Cesium.GroundPolylinePrimitive(primitiveOptions);\n            outlinePrimitive.readyPromise.then(() => {\n              this.setReferenceForPicking(layer, feature, outlinePrimitive._primitive);\n            });\n          }\n        }\n      } else {\n        // Actually do the normal polygon thing. This should end the removable\n        // section of code described above.\n        outlineGeometry = new Cesium.PolygonOutlineGeometry({\n          // always update Cesium externs before adding a property\n          polygonHierarchy: hierarchy,\n          perPositionHeight: true\n        });\n      }\n    }\n\n    const primitives = this.wrapFillAndOutlineGeometries(\n        layer, feature, olGeometry, fillGeometry, outlineGeometry, olStyle);\n\n    if (outlinePrimitive) {\n      primitives.add(outlinePrimitive);\n    }\n\n    return this.addTextStyle(layer, feature, olGeometry, olStyle, primitives);\n  }\n\n  /**\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {ol.Feature} feature OpenLayers feature..\n   * @param {!ol.geom.Geometry} geometry\n   * @return {!Cesium.HeightReference}\n   * @api\n   */\n  getHeightReference(layer, feature, geometry) {\n\n    // Read from the geometry\n    let altitudeMode = geometry.get('altitudeMode');\n\n    // Or from the feature\n    if (altitudeMode === undefined) {\n      altitudeMode = feature.get('altitudeMode');\n    }\n\n    // Or from the layer\n    if (altitudeMode === undefined) {\n      altitudeMode = layer.get('altitudeMode');\n    }\n\n    let heightReference = Cesium.HeightReference.NONE;\n    if (altitudeMode === 'clampToGround') {\n      heightReference = Cesium.HeightReference.CLAMP_TO_GROUND;\n    } else if (altitudeMode === 'relativeToGround') {\n      heightReference = Cesium.HeightReference.RELATIVE_TO_GROUND;\n    }\n\n    return heightReference;\n  }\n\n  /**\n   * Convert a point geometry to a Cesium BillboardCollection.\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature..\n   * @param {!ol.geom.Point} olGeometry OpenLayers point geometry.\n   * @param {!ol.ProjectionLike} projection\n   * @param {!ol.style.Style} style\n   * @param {!ol.style.Image} imageStyle\n   * @param {!Cesium.BillboardCollection} billboards\n   * @param {function(!Cesium.Billboard)=} opt_newBillboardCallback Called when the new billboard is added.\n   * @api\n   */\n  createBillboardFromImage(\n      layer,\n      feature,\n      olGeometry,\n      projection,\n      style,\n      imageStyle,\n      billboards,\n      opt_newBillboardCallback\n  ) {\n\n    if (imageStyle instanceof olStyleIcon) {\n      // make sure the image is scheduled for load\n      imageStyle.load();\n    }\n\n    const image = imageStyle.getImage(1); // get normal density\n    const isImageLoaded = function(image) {\n      return image.src != '' &&\n          image.naturalHeight != 0 &&\n          image.naturalWidth != 0 &&\n          image.complete;\n    };\n    const reallyCreateBillboard = (function() {\n      if (!image) {\n        return;\n      }\n      if (!(image instanceof HTMLCanvasElement ||\n          image instanceof Image ||\n          image instanceof HTMLImageElement)) {\n        return;\n      }\n      const center = olGeometry.getCoordinates();\n      const position = olcsCore.ol4326CoordinateToCesiumCartesian(center);\n      let color;\n      const opacity = imageStyle.getOpacity();\n      if (opacity !== undefined) {\n        color = new Cesium.Color(1.0, 1.0, 1.0, opacity);\n      }\n\n      const scale = imageStyle.getScale();\n      const heightReference = this.getHeightReference(layer, feature, olGeometry);\n\n      const bbOptions = /** @type {Cesium.optionsBillboardCollectionAdd} */ ({\n        // always update Cesium externs before adding a property\n        image,\n        color,\n        scale,\n        heightReference,\n        position\n      });\n\n      // merge in cesium options from openlayers feature\n      Object.assign(bbOptions, feature.get('cesiumOptions'));\n\n      if (imageStyle instanceof olStyleIcon) {\n        const anchor = imageStyle.getAnchor();\n        if (anchor) {\n          bbOptions.pixelOffset = new Cesium.Cartesian2((image.width / 2 - anchor[0]) * scale, (image.height / 2 - anchor[1]) * scale);\n        }\n      }\n\n      const bb = this.csAddBillboard(billboards, bbOptions, layer, feature, olGeometry, style);\n      if (opt_newBillboardCallback) {\n        opt_newBillboardCallback(bb);\n      }\n    }).bind(this);\n\n    if (image instanceof Image && !isImageLoaded(image)) {\n      // Cesium requires the image to be loaded\n      let cancelled = false;\n      const source = layer.getSource();\n      const canceller = function() {\n        cancelled = true;\n      };\n      source.on(['removefeature', 'clear'],\n          this.boundOnRemoveOrClearFeatureListener_);\n      let cancellers = olcsUtil.obj(source)['olcs_cancellers'];\n      if (!cancellers) {\n        cancellers = olcsUtil.obj(source)['olcs_cancellers'] = {};\n      }\n\n      const fuid = getUid(feature);\n      if (cancellers[fuid]) {\n        // When the feature change quickly, a canceller may still be present so\n        // we cancel it here to prevent creation of a billboard.\n        cancellers[fuid]();\n      }\n      cancellers[fuid] = canceller;\n\n      const listener = function() {\n        image.removeEventListener('load', listener);\n        if (!billboards.isDestroyed() && !cancelled) {\n          // Create billboard if the feature is still displayed on the map.\n          reallyCreateBillboard();\n        }\n      };\n\n      image.addEventListener('load', listener);\n    } else {\n      reallyCreateBillboard();\n    }\n  }\n\n  /**\n   * Convert a point geometry to a Cesium BillboardCollection.\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature..\n   * @param {!ol.geom.Point} olGeometry OpenLayers point geometry.\n   * @param {!ol.ProjectionLike} projection\n   * @param {!ol.style.Style} style\n   * @param {!Cesium.BillboardCollection} billboards\n   * @param {function(!Cesium.Billboard)=} opt_newBillboardCallback Called when\n   * the new billboard is added.\n   * @return {Cesium.Primitive} primitives\n   * @api\n   */\n  olPointGeometryToCesium(\n      layer,\n      feature,\n      olGeometry,\n      projection,\n      style,\n      billboards,\n      opt_newBillboardCallback\n  ) {\n    console.assert(olGeometry.getType() == 'Point');\n    olGeometry = olcsCore.olGeometryCloneTo4326(olGeometry, projection);\n\n    let modelPrimitive = null;\n    const imageStyle = style.getImage();\n    if (imageStyle) {\n      const olcsModelFunction = /** @type {function():olcsx.ModelStyle} */ (olGeometry.get('olcs_model') || feature.get('olcs_model'));\n      if (olcsModelFunction) {\n        const olcsModel = olcsModelFunction();\n        const options = /** @type {Cesium.ModelFromGltfOptions} */ (Object.assign({}, {scene: this.scene}, olcsModel.cesiumOptions));\n        const model = Cesium.Model.fromGltf(options);\n        modelPrimitive = new Cesium.PrimitiveCollection();\n        modelPrimitive.add(model);\n        if (olcsModel.debugModelMatrix) {\n          modelPrimitive.add(new Cesium.DebugModelMatrixPrimitive({\n            modelMatrix: olcsModel.debugModelMatrix\n          }));\n        }\n      } else {\n        this.createBillboardFromImage(layer, feature, olGeometry, projection, style, imageStyle, billboards, opt_newBillboardCallback);\n      }\n    }\n\n    if (style.getText()) {\n      return this.addTextStyle(layer, feature, olGeometry, style, modelPrimitive || new Cesium.Primitive());\n    } else {\n      return modelPrimitive;\n    }\n  }\n\n  /**\n   * Convert an OpenLayers multi-something geometry to Cesium.\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature..\n   * @param {!ol.geom.Geometry} geometry OpenLayers geometry.\n   * @param {!ol.ProjectionLike} projection\n   * @param {!ol.style.Style} olStyle\n   * @param {!Cesium.BillboardCollection} billboards\n   * @param {function(!Cesium.Billboard)=} opt_newBillboardCallback Called when\n   * the new billboard is added.\n   * @return {Cesium.Primitive} primitives\n   * @api\n   */\n  olMultiGeometryToCesium(\n      layer,\n      feature,\n      geometry,\n      projection,\n      olStyle,\n      billboards,\n      opt_newBillboardCallback\n  ) {\n    // Do not reproject to 4326 now because it will be done later.\n\n    // FIXME: would be better to combine all child geometries in one primitive\n    // instead we create n primitives for simplicity.\n    const accumulate = function(geometries, functor) {\n      const primitives = new Cesium.PrimitiveCollection();\n      geometries.forEach((geometry) => {\n        primitives.add(functor(layer, feature, geometry, projection, olStyle));\n      });\n      return primitives;\n    };\n\n    let subgeos;\n    switch (geometry.getType()) {\n      case 'MultiPoint':\n        geometry = /** @type {!ol.geom.MultiPoint} */ (geometry);\n        subgeos = geometry.getPoints();\n        if (olStyle.getText()) {\n          const primitives = new Cesium.PrimitiveCollection();\n          subgeos.forEach((geometry) => {\n            console.assert(geometry);\n            const result = this.olPointGeometryToCesium(layer, feature, geometry,\n                projection, olStyle, billboards, opt_newBillboardCallback);\n            if (result) {\n              primitives.add(result);\n            }\n          });\n          return primitives;\n        } else {\n          subgeos.forEach((geometry) => {\n            console.assert(geometry);\n            this.olPointGeometryToCesium(layer, feature, geometry, projection,\n                olStyle, billboards, opt_newBillboardCallback);\n          });\n          return null;\n        }\n      case 'MultiLineString':\n        geometry = /** @type {!ol.geom.MultiLineString} */ (geometry);\n        subgeos = geometry.getLineStrings();\n        return accumulate(subgeos, this.olLineStringGeometryToCesium.bind(this));\n      case 'MultiPolygon':\n        geometry = /** @type {!ol.geom.MultiPolygon} */ (geometry);\n        subgeos = geometry.getPolygons();\n        return accumulate(subgeos, this.olPolygonGeometryToCesium.bind(this));\n      default:\n        console.assert(false, `Unhandled multi geometry type${geometry.getType()}`);\n    }\n  }\n\n  /**\n   * Convert an OpenLayers text style to Cesium.\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature..\n   * @param {!ol.geom.Geometry} geometry\n   * @param {!ol.style.Text} style\n   * @return {Cesium.LabelCollection} Cesium primitive\n   * @api\n   */\n  olGeometry4326TextPartToCesium(layer, feature, geometry, style) {\n    const text = style.getText();\n    if (!text) {\n      return null;\n    }\n\n    const labels = new Cesium.LabelCollection({scene: this.scene});\n    // TODO: export and use the text draw position from OpenLayers .\n    // See src/ol/render/vector.js\n    const extentCenter = getCenter(geometry.getExtent());\n    if (geometry instanceof olGeomSimpleGeometry) {\n      const first = geometry.getFirstCoordinate();\n      extentCenter[2] = first.length == 3 ? first[2] : 0.0;\n    }\n    const options = /** @type {Cesium.optionsLabelCollection} */ ({});\n\n    options.position = olcsCore.ol4326CoordinateToCesiumCartesian(extentCenter);\n\n    options.text = text;\n\n    options.heightReference = this.getHeightReference(layer, feature, geometry);\n\n    const offsetX = style.getOffsetX();\n    const offsetY = style.getOffsetY();\n    if (offsetX != 0 && offsetY != 0) {\n      const offset = new Cesium.Cartesian2(offsetX, offsetY);\n      options.pixelOffset = offset;\n    }\n\n    options.font = style.getFont() || '10px sans-serif'; // OpenLayers default\n\n    let labelStyle = undefined;\n    if (style.getFill()) {\n      options.fillColor = this.extractColorFromOlStyle(style, false);\n      labelStyle = Cesium.LabelStyle.FILL;\n    }\n    if (style.getStroke()) {\n      options.outlineWidth = this.extractLineWidthFromOlStyle(style);\n      options.outlineColor = this.extractColorFromOlStyle(style, true);\n      labelStyle = Cesium.LabelStyle.OUTLINE;\n    }\n    if (style.getFill() && style.getStroke()) {\n      labelStyle = Cesium.LabelStyle.FILL_AND_OUTLINE;\n    }\n    options.style = labelStyle;\n\n    let horizontalOrigin;\n    switch (style.getTextAlign()) {\n      case 'left':\n        horizontalOrigin = Cesium.HorizontalOrigin.LEFT;\n        break;\n      case 'right':\n        horizontalOrigin = Cesium.HorizontalOrigin.RIGHT;\n        break;\n      case 'center':\n      default:\n        horizontalOrigin = Cesium.HorizontalOrigin.CENTER;\n    }\n    options.horizontalOrigin = horizontalOrigin;\n\n    if (style.getTextBaseline()) {\n      let verticalOrigin;\n      switch (style.getTextBaseline()) {\n        case 'top':\n          verticalOrigin = Cesium.VerticalOrigin.TOP;\n          break;\n        case 'middle':\n          verticalOrigin = Cesium.VerticalOrigin.CENTER;\n          break;\n        case 'bottom':\n          verticalOrigin = Cesium.VerticalOrigin.BOTTOM;\n          break;\n        case 'alphabetic':\n          verticalOrigin = Cesium.VerticalOrigin.TOP;\n          break;\n        case 'hanging':\n          verticalOrigin = Cesium.VerticalOrigin.BOTTOM;\n          break;\n        default:\n          console.assert(false, `unhandled baseline ${style.getTextBaseline()}`);\n      }\n      options.verticalOrigin = verticalOrigin;\n    }\n\n\n    const l = labels.add(options);\n    this.setReferenceForPicking(layer, feature, l);\n    return labels;\n  }\n\n  /**\n   * Convert an OpenLayers style to a Cesium Material.\n   * @param {ol.Feature} feature OpenLayers feature..\n   * @param {!ol.style.Style} style\n   * @param {boolean} outline\n   * @return {Cesium.Material}\n   * @api\n   */\n  olStyleToCesium(feature, style, outline) {\n    const fill = style.getFill();\n    const stroke = style.getStroke();\n    if ((outline && !stroke) || (!outline && !fill)) {\n      return null; // FIXME use a default style? Developer error?\n    }\n\n    let color = outline ? stroke.getColor() : fill.getColor();\n    color = olcsCore.convertColorToCesium(color);\n\n    if (outline && stroke.getLineDash()) {\n      return Cesium.Material.fromType('Stripe', {\n        // always update Cesium externs before adding a property\n        horizontal: false,\n        repeat: 500, // TODO how to calculate this?\n        evenColor: color,\n        oddColor: new Cesium.Color(0, 0, 0, 0) // transparent\n      });\n    } else {\n      return Cesium.Material.fromType('Color', {\n        // always update Cesium externs before adding a property\n        color\n      });\n    }\n\n  }\n\n  /**\n   * Compute OpenLayers plain style.\n   * Evaluates style function, blend arrays, get default style.\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature\n   * @param {ol.StyleFunction|undefined} fallbackStyleFunction\n   * @param {number} resolution\n   * @return {Array.<!ol.style.Style>} null if no style is available\n   * @api\n   */\n  computePlainStyle(layer, feature, fallbackStyleFunction, resolution) {\n    /**\n     * @type {ol.FeatureStyleFunction|undefined}\n     */\n    const featureStyleFunction = feature.getStyleFunction();\n\n    /**\n     * @type {ol.style.Style|Array.<ol.style.Style>}\n     */\n    let style = null;\n\n    if (featureStyleFunction) {\n      style = featureStyleFunction(feature, resolution);\n    }\n\n    if (!style && fallbackStyleFunction) {\n      style = fallbackStyleFunction(feature, resolution);\n    }\n\n    if (!style) {\n      // The feature must not be displayed\n      return null;\n    }\n\n    // FIXME combine materials as in cesium-materials-pack?\n    // then this function must return a custom material\n    // More simply, could blend the colors like described in\n    // http://en.wikipedia.org/wiki/Alpha_compositing\n    return Array.isArray(style) ? style : [style];\n  }\n\n  /**\n   * @protected\n   * @param {!ol.Feature} feature\n   * @param {!ol.style.Style} style\n   * @param {!ol.geom.Geometry=} opt_geom Geometry to be converted.\n   * @return {ol.geom.Geometry|undefined}\n   */\n  getGeometryFromFeature(feature, style, opt_geom) {\n    if (opt_geom) {\n      return opt_geom;\n    }\n\n    const geom3d = /** @type {!ol.geom.Geometry} */(feature.get('olcs.3d_geometry'));\n    if (geom3d && geom3d instanceof olGeomGeometry) {\n      return geom3d;\n    }\n\n    if (style) {\n      const geomFuncRes = style.getGeometryFunction()(feature);\n      if (geomFuncRes instanceof olGeomGeometry) {\n        return geomFuncRes;\n      }\n    }\n\n    return feature.getGeometry();\n  }\n\n  /**\n   * Convert one OpenLayers feature up to a collection of Cesium primitives.\n   * @param {ol.layer.Vector|ol.layer.Image} layer\n   * @param {!ol.Feature} feature OpenLayers feature.\n   * @param {!ol.style.Style} style\n   * @param {!import('olcs/core/VectorLayerConterpart.js').OlFeatureToCesiumContext} context\n   * @param {!ol.geom.Geometry=} opt_geom Geometry to be converted.\n   * @return {Cesium.Primitive} primitives\n   * @api\n   */\n  olFeatureToCesium(layer, feature, style, context, opt_geom) {\n    let geom = this.getGeometryFromFeature(feature, style, opt_geom);\n\n    if (!geom) {\n      // OpenLayers features may not have a geometry\n      // See http://geojson.org/geojson-spec.html#feature-objects\n      return null;\n    }\n\n    const proj = context.projection;\n    const newBillboardAddedCallback = function(bb) {\n      const featureBb = context.featureToCesiumMap[getUid(feature)];\n      if (featureBb instanceof Array) {\n        featureBb.push(bb);\n      }\n      else {\n        context.featureToCesiumMap[getUid(feature)] = [bb];\n      }\n    };\n\n    switch (geom.getType()) {\n      case 'GeometryCollection':\n        const primitives = new Cesium.PrimitiveCollection();\n        const collection = /** @type {!ol.geom.GeometryCollection} */ (geom);\n        // TODO: use getGeometriesArray() instead\n        collection.getGeometries().forEach((geom) => {\n          if (geom) {\n            const prims = this.olFeatureToCesium(layer, feature, style, context,\n                geom);\n            if (prims) {\n              primitives.add(prims);\n            }\n          }\n        });\n        return primitives;\n      case 'Point':\n        geom = /** @type {!ol.geom.Point} */ (geom);\n        const bbs = context.billboards;\n        const result = this.olPointGeometryToCesium(layer, feature, geom, proj,\n            style, bbs, newBillboardAddedCallback);\n        if (!result) {\n          // no wrapping primitive\n          return null;\n        } else {\n          return result;\n        }\n      case 'Circle':\n        geom = /** @type {!ol.geom.Circle} */ (geom);\n        return this.olCircleGeometryToCesium(layer, feature, geom, proj,\n            style);\n      case 'LineString':\n        geom = /** @type {!ol.geom.LineString} */ (geom);\n        return this.olLineStringGeometryToCesium(layer, feature, geom, proj,\n            style);\n      case 'Polygon':\n        geom = /** @type {!ol.geom.Polygon} */ (geom);\n        return this.olPolygonGeometryToCesium(layer, feature, geom, proj,\n            style);\n      case 'MultiPoint':\n      case 'MultiLineString':\n      case 'MultiPolygon':\n        const result2 = this.olMultiGeometryToCesium(layer, feature, geom, proj,\n            style, context.billboards, newBillboardAddedCallback);\n        if (!result2) {\n          // no wrapping primitive\n          return null;\n        } else {\n          return result2;\n        }\n      case 'LinearRing':\n        throw new Error('LinearRing should only be part of polygon.');\n      default:\n        throw new Error(`Ol geom type not handled : ${geom.getType()}`);\n    }\n  }\n\n  /**\n   * Convert an OpenLayers vector layer to Cesium primitive collection.\n   * For each feature, the associated primitive will be stored in\n   * `featurePrimitiveMap`.\n   * @param {!(ol.layer.Vector|ol.layer.Image)} olLayer\n   * @param {!ol.View} olView\n   * @param {!Object.<number, !Cesium.Primitive>} featurePrimitiveMap\n   * @return {!olcs.core.VectorLayerCounterpart}\n   * @api\n   */\n  olVectorLayerToCesium(olLayer, olView, featurePrimitiveMap) {\n    const proj = olView.getProjection();\n    const resolution = olView.getResolution();\n\n    if (resolution === undefined || !proj) {\n      console.assert(false, 'View not ready');\n      // an assertion is not enough for closure to assume resolution and proj\n      // are defined\n      throw new Error('View not ready');\n    }\n\n    let source = olLayer.getSource();\n    if (source instanceof olSourceCluster) {\n      source = source.getSource();\n    }\n\n    console.assert(source instanceof olSourceVector);\n    const features = source.getFeatures();\n    const counterpart = new olcsCoreVectorLayerCounterpart(proj, this.scene);\n    const context = counterpart.context;\n    for (let i = 0; i < features.length; ++i) {\n      const feature = features[i];\n      if (!feature) {\n        continue;\n      }\n      /**\n       * @type {ol.StyleFunction|undefined}\n       */\n      const layerStyle = olLayer.getStyleFunction();\n      const styles = this.computePlainStyle(olLayer, feature, layerStyle,\n          resolution);\n      if (!styles || !styles.length) {\n        // only 'render' features with a style\n        continue;\n      }\n\n      /**\n       * @type {Cesium.Primitive|null}\n       */\n      let primitives = null;\n      for (let i = 0; i < styles.length; i++) {\n        const prims = this.olFeatureToCesium(olLayer, feature, styles[i], context);\n        if (prims) {\n          if (!primitives) {\n            primitives = prims;\n          } else if (prims) {\n            let i = 0, prim;\n            while ((prim = prims.get(i))) {\n              primitives.add(prim);\n              i++;\n            }\n          }\n        }\n      }\n      if (!primitives) {\n        continue;\n      }\n      featurePrimitiveMap[getUid(feature)] = primitives;\n      counterpart.getRootPrimitive().add(primitives);\n    }\n\n    return counterpart;\n  }\n\n  /**\n   * Convert an OpenLayers feature to Cesium primitive collection.\n   * @param {!(ol.layer.Vector|ol.layer.Image)} layer\n   * @param {!ol.View} view\n   * @param {!ol.Feature} feature\n   * @param {!import('olcs/core/VectorLayerConterpart.js').OlFeatureToCesiumContext} context\n   * @return {Cesium.Primitive}\n   * @api\n   */\n  convert(layer, view, feature, context) {\n    const proj = view.getProjection();\n    const resolution = view.getResolution();\n\n    if (resolution == undefined || !proj) {\n      return null;\n    }\n\n    /**\n     * @type {ol.StyleFunction|undefined}\n     */\n    const layerStyle = layer.getStyleFunction();\n\n    const styles = this.computePlainStyle(layer, feature, layerStyle, resolution);\n\n    if (!styles || !styles.length) {\n      // only 'render' features with a style\n      return null;\n    }\n\n    context.projection = proj;\n\n    /**\n     * @type {Cesium.Primitive|null}\n     */\n    let primitives = null;\n    for (let i = 0; i < styles.length; i++) {\n      const prims = this.olFeatureToCesium(layer, feature, styles[i], context);\n      if (!primitives) {\n        primitives = prims;\n      } else if (prims) {\n        let i = 0, prim;\n        while ((prim = prims.get(i))) {\n          primitives.add(prim);\n          i++;\n        }\n      }\n    }\n    return primitives;\n  }\n}\n\n\nexport default FeatureConverter;\n", "import MVT from 'ol/format/MVT.js';\nimport Style from 'ol/style/Style.js';\nimport Stroke from 'ol/style/Stroke.js';\nimport {toContext} from 'ol/render.js';\nimport {get as getProjection} from 'ol/proj.js';\nimport {VERSION as OL_VERSION} from 'ol/util.js';\nimport LRUCache from 'ol/structs/LRUCache.js';\nimport {getForProjection as getTilegridForProjection} from 'ol/tilegrid.js';\nimport {createFromTemplates as createTileUrlFunctions} from 'ol/tileurlfunction.js';\n\n\nconst format = new MVT();\nconst styles = [new Style({\n  stroke: new Stroke({\n    color: 'blue',\n    width: 2\n  })\n})];\n\n\nexport default class MVTImageryProvider {\n  constructor(options) {\n    this.urls = options.urls;\n    this.ready = true;\n    this.readyPromise = Promise.resolve(true);\n    this.tileWidth = 256;\n    this.tileHeight = 256;\n    this.maximumLevel = options.maximumLevel || 20;\n    this.minimumLevel = options.minimumLevel || 0;\n    this.tilingScheme = new Cesium.WebMercatorTilingScheme;\n    this.rectangle = options.rectangle || this.tilingScheme.rectangle;\n    this.errorEvent = new Cesium.Event();\n    this.credit = options.credit;\n    this.hasAlphaChannel = true;\n    this.styleFunction_ = options.styleFunction || (() => styles);\n    this.projection_ = getProjection('EPSG:3857');\n    this.emptyCanvas_ = document.createElement('canvas');\n    this.emptyCanvas_.width = 1;\n    this.emptyCanvas_.height = 1;\n    this.tileRectangle_ = new Cesium.Rectangle();\n    const cacheSize = options.cacheSize !== undefined ? options.cacheSize : 50;\n    this.tileCache = new LRUCache(cacheSize);\n    this.featureCache = options.featureCache || new LRUCache(cacheSize);\n    // to avoid too frequent cache grooming we allow x2 capacity\n\n    const tileGrid = getTilegridForProjection(this.projection_);\n    this.tileFunction_ = createTileUrlFunctions(this.urls, tileGrid);\n  }\n\n  getTileCredits() {\n    return [];\n  }\n\n  pickFeatures() {\n  }\n\n\n  getTileFeatures(z, x, y) {\n    const cacheKey = this.getCacheKey_(z, x, y);\n    let promise;\n    if (this.featureCache.containsKey(cacheKey)) {\n      promise = this.featureCache.get(cacheKey);\n    }\n    if (!promise) {\n      const url = this.getUrl_(z, x, y);\n      promise = fetch(url)\n          .then(r => (r.ok ? r : Promise.reject(r)))\n          .then(r => r.arrayBuffer())\n          .then(buffer => this.readFeaturesFromBuffer(buffer));\n      this.featureCache.set(cacheKey, promise);\n      if (this.featureCache.getCount() > 2 * this.featureCache.highWaterMark) {\n        while (this.featureCache.canExpireCache()) {\n          this.featureCache.pop();\n        }\n      }\n    }\n    return promise;\n  }\n\n  readFeaturesFromBuffer(buffer) {\n    let options;\n    if (OL_VERSION <= '6.4.4') {\n      // See https://github.com/openlayers/openlayers/pull/11540\n      options = {\n        extent: [0, 0, 4096, 4096],\n        dataProjection: format.dataProjection,\n        featureProjection: format.dataProjection\n      };\n    }\n    const features = format.readFeatures(buffer, options);\n    const scaleFactor = this.tileWidth / 4096;\n    features.forEach((f) => {\n      const flatCoordinates = f.getFlatCoordinates();\n      let flip = false;\n      for (let i = 0; i < flatCoordinates.length; ++i) {\n        flatCoordinates[i] *= scaleFactor;\n        if (flip) {\n          // FIXME: why do we need this now?\n          flatCoordinates[i] = this.tileWidth - flatCoordinates[i];\n        }\n        if (OL_VERSION <= '6.4.4') {\n          flip = !flip;\n        }\n      }\n    });\n\n    return features;\n  }\n\n  getUrl_(z, x, y) {\n    const url = this.tileFunction_([z, x, y]);\n    return url;\n  }\n\n  getCacheKey_(z, x, y) {\n    return `${z}_${x}_${y}`;\n  }\n\n  requestImage(x, y, z, request) {\n    if (z < this.minimumLevel) {\n      return this.emptyCanvas_;\n    }\n\n    try {\n      const cacheKey = this.getCacheKey_(z, x, y);\n      let promise;\n      if (this.tileCache.containsKey(cacheKey)) {\n        promise = this.tileCache.get(cacheKey);\n      }\n      if (!promise) {\n        promise = this.getTileFeatures(z, x, y)\n            .then((features) => {\n            // FIXME: here we suppose the 2D projection is in meters\n              this.tilingScheme.tileXYToNativeRectangle(x, y, z, this.tileRectangle_);\n              const resolution = (this.tileRectangle_.east - this.tileRectangle_.west) / this.tileWidth;\n              return this.rasterizeFeatures(features, this.styleFunction_, resolution);\n            });\n        this.tileCache.set(cacheKey, promise);\n        if (this.tileCache.getCount() > 2 * this.tileCache.highWaterMark) {\n          while (this.tileCache.canExpireCache()) {\n            this.tileCache.pop();\n          }\n        }\n      }\n      return promise;\n    } catch (e) {\n      console.trace(e);\n      this.raiseEvent('could not render pbf to tile', e);\n    }\n  }\n\n  rasterizeFeatures(features, styleFunction, resolution) {\n    const canvas = document.createElement('canvas');\n    const vectorContext = toContext(canvas.getContext('2d'), {size: [this.tileWidth, this.tileHeight]});\n    features.forEach((f) => {\n      const styles = styleFunction(f, resolution);\n      if (styles) {\n        styles.forEach((style) => {\n          vectorContext.setStyle(style);\n          vectorContext.drawGeometry(f);\n        });\n      }\n    });\n    return canvas;\n  }\n}\n", "/**\n * @module olcs.OLCesium\n */\nimport olGeomPoint from 'ol/geom/Point.js';\nimport {getTransform} from 'ol/proj.js';\nimport olcsUtil from './util.js';\nimport olcsCore from './core.js';\nimport olcsAuto<PERSON>ender<PERSON>oop from './AutoRenderLoop.js';\nimport olcsCamera from './Camera.js';\nimport olcsRasterSynchronizer from './RasterSynchronizer.js';\nimport olcsVectorSynchronizer from './VectorSynchronizer.js';\nimport olcsOverlaySynchronizer from './OverlaySynchronizer.js';\n\n\n/**\n * @typedef {Object} OLCesiumOptions\n * @property {import('ol/Map.js').default} map The OpenLayers map we want to show on a Cesium scene.\n * @property {Cesium.Viewer} [viewer]   Cesium viewer.\n * @property {function(!import('ol/Map.js').default, !Cesium.Scene, !Cesium.DataSourceCollection): Array<import('olcs/AbstractSynchronizer.js').default>}\n *      [createSynchronizers] Callback function which will be called by the {@link olcs.OLCesium}\n *      constructor to create custom synchronizers. Receives an `ol.Map` and a `Cesium.Scene` as arguments,\n *      and needs to return an array of {@link import('olcs/AbstractSynchronizer.js').default}.\n * @property {function(): Cesium.JulianDate} [time] Control the current time used by Cesium.\n * @property {boolean} [stopOpenLayersEventsPropagation] Prevent propagation of mouse/touch events to\n *      OpenLayers when Cesium is active.\n */\n\n\nclass OLCesium {\n  /**\n   * @param {!OLCesiumOptions} options Options.\n   * @constructor\n   * @api\n   */\n  constructor(options) {\n\n    /**\n     * @type {olcs.AutoRenderLoop}\n     * @private\n     */\n    this.autoRenderLoop_ = null;\n\n    /**\n     * @type {!ol.Map}\n     * @private\n     */\n    this.map_ = options.map;\n\n    /**\n     * @type {!function(): Cesium.JulianDate}\n     * @private\n     */\n    this.time_ = options.time || function() {\n      return Cesium.JulianDate.now();\n    };\n\n    /**\n     * No change of the view projection.\n     * @private\n     */\n    this.to4326Transform_ = getTransform(this.map_.getView().getProjection(), 'EPSG:4326');\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.resolutionScale_ = 1.0;\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.canvasClientWidth_ = 0.0;\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.canvasClientHeight_ = 0.0;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.resolutionScaleChanged_ = true; // force resize\n\n    const fillArea = 'position:absolute;top:0;left:0;width:100%;height:100%;';\n\n    /**\n     * @type {!Element}\n     * @private\n     */\n    this.container_ = document.createElement('DIV');\n    const containerAttribute = document.createAttribute('style');\n    containerAttribute.value = `${fillArea}visibility:hidden;`;\n    this.container_.setAttributeNode(containerAttribute);\n\n\n    //火星科技添加\n    let viewer = options.viewer\n \n    viewer.container.appendChild(this.container_);\n\n    // /**\n    //  * Whether the Cesium container is placed over the ol map.\n    //  * a target => side by side mode\n    //  * no target => over map mode\n    //  * @type {boolean}\n    //  * @private\n    //  */\n    // this.isOverMap_ = false; \n\n\n    // if (this.isOverMap_ && options.stopOpenLayersEventsPropagation) {\n    //   const overlayEvents = ['click', 'dblclick', 'mousedown', 'touchstart', 'MSPointerDown', 'pointerdown', 'mousewheel', 'wheel'];\n    //   for (let i = 0, ii = overlayEvents.length; i < ii; ++i) {\n    //     this.container_.addEventListener(overlayEvents[i], evt => evt.stopPropagation());\n    //   }\n    // }\n\n\n    // /**\n    //  * @type {!HTMLCanvasElement}\n    //  * @private\n    //  */\n    // this.canvas_ = /** @type {!HTMLCanvasElement} */ (document.createElement('CANVAS'));\n    // const canvasAttribute = document.createAttribute('style');\n    // canvasAttribute.value = fillArea;\n    // this.canvas_.setAttributeNode(canvasAttribute);\n\n    // if (olcsUtil.supportsImageRenderingPixelated()) {\n    //   // non standard CSS4\n    //   this.canvas_.style['imageRendering'] = olcsUtil.imageRenderingValue();\n    // }\n\n    // this.canvas_.oncontextmenu = function() { return false; };\n    // this.canvas_.onselectstart = function() { return false; };\n\n    // this.container_.appendChild(this.canvas_);\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.enabled_ = false;\n\n    /**\n     * @type {!Array.<ol.interaction.Interaction>}\n     * @private\n     */\n    this.pausedInteractions_ = [];\n\n    /**\n     * @type {?ol.layer.Group}\n     * @private\n     */\n    this.hiddenRootGroup_ = null;\n\n    // const sceneOptions = options.sceneOptions !== undefined ? options.sceneOptions :\n    //   /** @type {Cesium.SceneOptions} */ ({});\n    // sceneOptions.canvas = this.canvas_;\n    // sceneOptions.scene3DOnly = true;\n\n    /**\n     * @type {!Cesium.Scene}\n     * @private\n     */\n    this.scene_ = viewer.scene;\n\n    // const sscc = this.scene_.screenSpaceCameraController;\n\n    // sscc.tiltEventTypes.push({\n    //   'eventType': Cesium.CameraEventType.LEFT_DRAG,\n    //   'modifier': Cesium.KeyboardEventModifier.SHIFT\n    // });\n\n    // sscc.tiltEventTypes.push({\n    //   'eventType': Cesium.CameraEventType.LEFT_DRAG,\n    //   'modifier': Cesium.KeyboardEventModifier.ALT\n    // });\n\n    // sscc.enableLook = false;\n\n    // this.scene_.camera.constrainedAxis = Cesium.Cartesian3.UNIT_Z;\n\n    /**\n     * @type {!olcs.Camera}\n     * @private\n     */\n    this.camera_ = new olcsCamera(this.scene_, this.map_);\n\n    /**\n     * @type {!Cesium.Globe}\n     * @private\n     */\n    this.globe_ = viewer.scene.globe;\n    // this.globe_.baseColor = Cesium.Color.WHITE;\n    // this.scene_.globe = this.globe_;\n    // this.scene_.skyAtmosphere = new Cesium.SkyAtmosphere();\n\n    // The first layer of Cesium is special; using a 1x1 transparent image to workaround it.\n    // See https://github.com/AnalyticalGraphicsInc/cesium/issues/1323 for details.\n    // const firstImageryProvider = new Cesium.SingleTileImageryProvider({\n    //   url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=',\n    //   rectangle: Cesium.Rectangle.fromDegrees(0, 0, 1, 1) // the Rectangle dimensions are arbitrary\n    // });\n    // this.globe_.imageryLayers.addImageryProvider(firstImageryProvider, 0);\n\n    this.dataSourceCollection_ = viewer.dataSources\n    this.dataSourceDisplay_ = viewer.dataSourceDisplay\n\n    const synchronizers = options.createSynchronizers ?\n      options.createSynchronizers(this.map_, this.scene_, this.dataSourceCollection_) : [\n        new olcsRasterSynchronizer(this.map_, this.scene_),\n        new olcsVectorSynchronizer(this.map_, this.scene_),\n        new olcsOverlaySynchronizer(this.map_, this.scene_)\n      ];\n\n    // Assures correct canvas size after initialisation\n    // this.handleResize_();\n\n    for (let i = synchronizers.length - 1; i >= 0; --i) {\n      synchronizers[i].synchronize();\n    }\n\n    // /**\n    //  * Time of the last rendered frame, as returned by `performance.now()`.\n    //  * @type {number}\n    //  * @private\n    //  */\n    // this.lastFrameTime_ = 0;\n\n    // /**\n    //  * The identifier returned by `requestAnimationFrame`.\n    //  * @type {number|undefined}\n    //  * @private\n    //  */\n    // this.renderId_ = undefined;\n\n    // /**\n    //  * Target frame rate for the render loop.\n    //  * @type {number}\n    //  * @private\n    //  */\n    // this.targetFrameRate_ = Number.POSITIVE_INFINITY;\n\n    // /**\n    //  * If the Cesium render loop is being blocked.\n    //  * @type {boolean}\n    //  * @private\n    //  */\n    // this.blockCesiumRendering_ = false;\n\n    // /**\n    //  * If the warmup routine is active.\n    //  * @type {boolean}\n    //  * @private\n    //  */\n    // this.warmingUp_ = false;\n\n    /**\n     * @type {ol.Feature}\n     * @private\n     */\n    this.trackedFeature_ = null;\n\n    /**\n     * @type {Cesium.Entity}\n     * @private\n     */\n    this.trackedEntity_ = null;\n\n    // /**\n    //  * @type {Cesium.EntityView}\n    //  * @private\n    //  */\n    // this.entityView_ = null;\n\n    // /**\n    //  * @type {boolean}\n    //  * @private\n    //  */\n    // this.needTrackedEntityUpdate_ = false;\n\n    // /**\n    //  * @type {!Cesium.BoundingSphere}\n    //  */\n    // this.boundingSphereScratch_ = new Cesium.BoundingSphere();\n\n    // const eventHelper = new Cesium.EventHelper();\n    // eventHelper.add(this.scene_.postRender, OLCesium.prototype.updateTrackedEntity_, this);\n\n    // Cesium has a mechanism to prevent the camera to go under the terrain.\n    // Unfortunately, it is only active when all the terrain has been loaded, which:\n    // - does not prevent the camera to sink under terrain anymore;\n    // - introduce a jumping effect once all terrain has been loaded and the position of the camera is finally fixed.\n    // The property below enables a workaround found in the Camptocamp Cesium fork.\n    // See also https://github.com/AnalyticalGraphicsInc/cesium/issues/5999.\n    // Cesium.Camera.enableSuspendTerrainAdjustment = false;\n\n    viewer.scene.postUpdate.addEventListener(this.onAnimationFrame_, this);\n  }\n\n  // /**\n  //  * Render the Cesium scene.\n  //  * @private\n  //  */\n  // render_() {\n  //   // // if a call to `requestAnimationFrame` is pending, cancel it\n  //   // if (this.renderId_ !== undefined) {\n  //   //   cancelAnimationFrame(this.renderId_);\n  //   //   this.renderId_ = undefined;\n  //   // }\n\n  //   // // only render if Cesium is enabled/warming and rendering hasn't been blocked\n  //   // if ((this.enabled_ || this.warmingUp_) && !this.blockCesiumRendering_) {\n  //   //   this.renderId_ = requestAnimationFrame(this.onAnimationFrame_.bind(this));\n  //   // }\n  // }\n\n  /**\n   * Callback for `requestAnimationFrame`.\n   * @param {number} frameTime The frame time, from `performance.now()`.\n   * @private\n   */\n  onAnimationFrame_(frameTime) {\n    // this.renderId_ = undefined;\n\n    // // check if a frame was rendered within the target frame rate\n    // const interval = 1000.0 / this.targetFrameRate_;\n    // const delta = frameTime - this.lastFrameTime_;\n    // if (delta < interval) {\n    //   // too soon, don't render yet\n    //   this.render_();\n    //   return;\n    // }\n\n    // time to render a frame, save the time\n    // this.lastFrameTime_ = frameTime;\n\n    // const julianDate = this.time_();\n    // this.scene_.initializeFrame();\n    // this.handleResize_();\n    // this.dataSourceDisplay_.update(julianDate);\n\n    // // Update tracked entity\n    // if (this.entityView_) {\n    //   const trackedEntity = this.trackedEntity_;\n    //   const trackedState = this.dataSourceDisplay_.getBoundingSphere(trackedEntity, false, this.boundingSphereScratch_);\n    //   if (trackedState === Cesium.BoundingSphereState.DONE) {\n    //     this.boundingSphereScratch_.radius = 1; // a radius of 1 is enough for tracking points\n    //     this.entityView_.update(julianDate, this.boundingSphereScratch_);\n    //   }\n    // }\n\n    // this.scene_.render(julianDate);\n    this.camera_.checkCameraChange();\n\n    // request the next render call after this one completes to ensure the browser doesn't get backed up\n    // this.render_();\n  }\n\n  /**\n   * @private\n   */\n  // updateTrackedEntity_() {\n  //   if (!this.needTrackedEntityUpdate_) {\n  //     return;\n  //   }\n\n  //   const trackedEntity = this.trackedEntity_;\n  //   const scene = this.scene_;\n\n  //   const state = this.dataSourceDisplay_.getBoundingSphere(trackedEntity, false, this.boundingSphereScratch_);\n  //   if (state === Cesium.BoundingSphereState.PENDING) {\n  //     return;\n  //   }\n\n  //   scene.screenSpaceCameraController.enableTilt = false;\n\n  //   const bs = state !== Cesium.BoundingSphereState.FAILED ? this.boundingSphereScratch_ : undefined;\n  //   if (bs) {\n  //     bs.radius = 1;\n  //   }\n  //   this.entityView_ = new Cesium.EntityView(trackedEntity, scene, scene.mapProjection.ellipsoid);\n  //   this.entityView_.update(this.time_(), bs);\n  //   this.needTrackedEntityUpdate_ = false;\n  // }\n\n  // /**\n  //  * @private\n  //  */\n  // handleResize_() {\n  //   let width = this.canvas_.clientWidth;\n  //   let height = this.canvas_.clientHeight;\n\n  //   if (width === 0 | height === 0) {\n  //     // The canvas DOM element is not ready yet.\n  //     return;\n  //   }\n\n  //   if (width === this.canvasClientWidth_ &&\n  //       height === this.canvasClientHeight_ &&\n  //       !this.resolutionScaleChanged_) {\n  //     return;\n  //   }\n\n  //   let resolutionScale = this.resolutionScale_;\n  //   if (!olcsUtil.supportsImageRenderingPixelated()) {\n  //     resolutionScale *= window.devicePixelRatio || 1.0;\n  //   }\n  //   this.resolutionScaleChanged_ = false;\n\n  //   this.canvasClientWidth_ = width;\n  //   this.canvasClientHeight_ = height;\n\n  //   width *= resolutionScale;\n  //   height *= resolutionScale;\n\n  //   this.canvas_.width = width;\n  //   this.canvas_.height = height;\n  //   // this.scene_.camera.frustum.aspectRatio = width / height;\n  // }\n\n  /**\n   * @return {!olcs.Camera}\n   * @api\n   */\n  getCamera() {\n    return this.camera_;\n  }\n\n  /**\n   * @return {!ol.Map}\n   * @api\n   */\n  getOlMap() {\n    return this.map_;\n  }\n\n  /**\n   * @return {!ol.View}\n   * @api\n   */\n  getOlView() {\n    const view = this.map_.getView();\n    console.assert(view);\n    return view;\n  }\n\n  /**\n   * @return {!Cesium.Scene}\n   * @api\n   */\n  getCesiumScene() {\n    return this.scene_;\n  }\n\n  /**\n   * @return {!Cesium.DataSourceCollection}\n   * @api\n   */\n  getDataSources() {\n    return this.dataSourceCollection_;\n  }\n\n  /**\n   * @return {!Cesium.DataSourceDisplay}\n   * @api\n   */\n  getDataSourceDisplay() {\n    return this.dataSourceDisplay_;\n  }\n\n  /**\n   * @return {boolean}\n   * @api\n   */\n  getEnabled() {\n    return this.enabled_;\n  }\n\n  /**\n   * Enables/disables the Cesium.\n   * This modifies the visibility style of the container element.\n   * @param {boolean} enable\n   * @api\n   */\n  setEnabled(enable) {\n    if (this.enabled_ === enable) {\n      return;\n    }\n    this.enabled_ = enable;\n\n    // some Cesium operations are operating with canvas.clientWidth,\n    // so we can't remove it from DOM or even make display:none;\n    // this.container_.style.visibility = this.enabled_ ? 'visible' : 'hidden';\n    // let interactions;\n    if (this.enabled_) {\n      // this.throwOnUnitializedMap_();\n      // if (this.isOverMap_) {\n      //   interactions = this.map_.getInteractions();\n      //   interactions.forEach((el, i, arr) => {\n      //     this.pausedInteractions_.push(el);\n      //   });\n      //   interactions.clear();\n\n      //   this.map_.addInteraction = interaction => this.pausedInteractions_.push(interaction);\n      //   this.map_.removeInteraction = interaction =>\n      //     this.pausedInteractions_ = this.pausedInteractions_.filter(i => i !== interaction);\n\n      //   const rootGroup = this.map_.getLayerGroup();\n      //   if (rootGroup.getVisible()) {\n      //     this.hiddenRootGroup_ = rootGroup;\n      //     this.hiddenRootGroup_.setVisible(false);\n      //   }\n\n      //   this.map_.getOverlayContainer().classList.add('olcs-hideoverlay');\n      // }\n\n      this.camera_.readFromView();\n      // this.render_();\n    } else {\n      // if (this.isOverMap_) {\n      //   interactions = this.map_.getInteractions();\n      //   this.pausedInteractions_.forEach((interaction) => {\n      //     interactions.push(interaction);\n      //   });\n      //   this.pausedInteractions_.length = 0;\n\n      //   this.map_.addInteraction = interaction => this.map_.getInteractions().push(interaction);\n      //   this.map_.removeInteraction = interaction => this.map_.getInteractions().remove(interaction);\n\n      //   this.map_.getOverlayContainer().classList.remove('olcs-hideoverlay');\n      //   if (this.hiddenRootGroup_) {\n      //     this.hiddenRootGroup_.setVisible(true);\n      //     this.hiddenRootGroup_ = null;\n      //   }\n      // }\n\n      this.camera_.updateView();\n    }\n  }\n\n  /**\n   * Preload Cesium so that it is ready when transitioning from 2D to 3D.\n   * @param {number} height Target height of the camera\n   * @param {number} timeout Milliseconds after which the warming will stop\n   * @api\n  */\n  warmUp(height, timeout) {\n    if (this.enabled_) {\n      // already enabled\n      return;\n    }\n    this.throwOnUnitializedMap_();\n    this.camera_.readFromView();\n    const ellipsoid = this.globe_.ellipsoid;\n    const csCamera = this.scene_.camera;\n    const position = ellipsoid.cartesianToCartographic(csCamera.position);\n    if (position.height < height) {\n      position.height = height;\n      csCamera.position = ellipsoid.cartographicToCartesian(position);\n    }\n\n    // this.warmingUp_ = true;\n    // // this.render_();\n\n    // setTimeout(() => {\n    //   this.warmingUp_ = false;\n    // }, timeout);\n  }\n\n  // /**\n  //  * Block Cesium rendering to save resources.\n  //  * @param {boolean} block True to block.\n  //  * @api\n  // */\n  // setBlockCesiumRendering(block) {\n  //   if (this.blockCesiumRendering_ !== block) {\n  //     this.blockCesiumRendering_ = block;\n\n  //     // reset the render loop\n  //     this.render_();\n  //   }\n  // }\n\n  /**\n   * Render the globe only when necessary in order to save resources.\n   * Experimental.\n   * @api\n   */\n  enableAutoRenderLoop() {\n    if (!this.autoRenderLoop_) {\n      this.autoRenderLoop_ = new olcsAutoRenderLoop(this);\n    }\n  }\n\n  /**\n   * Get the autorender loop.\n   * @return {?olcs.AutoRenderLoop}\n   * @api\n  */\n  getAutoRenderLoop() {\n    return this.autoRenderLoop_;\n  }\n\n  /**\n   * The 3D Cesium globe is rendered in a canvas with two different dimensions:\n   * clientWidth and clientHeight which are the dimension on the screen and\n   * width and height which are the dimensions of the drawing buffer.\n   *\n   * By using a resolution scale lower than 1.0, it is possible to render the\n   * globe in a buffer smaller than the canvas client dimensions and improve\n   * performance, at the cost of quality.\n   *\n   * Pixel ratio should also be taken into account; by default, a device with\n   * pixel ratio of 2.0 will have a buffer surface 4 times bigger than the client\n   * surface.\n   *\n   * @param {number} value\n   * @this {olcs.OLCesium}\n   * @api\n   */\n  setResolutionScale(value) {\n    value = Math.max(0, value);\n    if (value !== this.resolutionScale_) {\n      this.resolutionScale_ = Math.max(0, value);\n      this.resolutionScaleChanged_ = true;\n      if (this.autoRenderLoop_) {\n        this.autoRenderLoop_.restartRenderLoop();\n      }\n    }\n  }\n\n  // /**\n  //  * Set the target frame rate for the renderer. Set to `Number.POSITIVE_INFINITY`\n  //  * to render as quickly as possible.\n  //  * @param {number} value The frame rate, in frames per second.\n  //  * @api\n  //  */\n  // setTargetFrameRate(value) {\n  //   if (this.targetFrameRate_ !== value) {\n  //     this.targetFrameRate_ = value;\n\n  //     // reset the render loop\n  //     this.render_();\n  //   }\n  // }\n\n  /**\n   * Check if OpenLayers map is not properly initialized.\n   * @private\n   */\n  throwOnUnitializedMap_() {\n    const map = this.map_;\n    const view = map.getView();\n    const center = view.getCenter();\n    if (!view.isDef() || isNaN(center[0]) || isNaN(center[1])) {\n      throw new Error(`The OpenLayers map is not properly initialized: ${center} / ${view.getResolution()}`);\n    }\n  }\n\n  /**\n   * @type {ol.Feature}\n   */\n  get trackedFeature() {\n    return this.trackedFeature_;\n  }\n\n  /**\n   * @param {ol.Feature} feature\n   */\n  set trackedFeature(feature) {\n    if (this.trackedFeature_ !== feature) {\n\n      const scene = this.scene_;\n\n      //Stop tracking\n      if (!feature || !feature.getGeometry()) {\n        // this.needTrackedEntityUpdate_ = false;\n        scene.screenSpaceCameraController.enableTilt = true;\n\n        if (this.trackedEntity_) {\n          this.dataSourceDisplay_.defaultDataSource.entities.remove(this.trackedEntity_);\n        }\n        this.trackedEntity_ = null;\n        this.trackedFeature_ = null;\n        this.entityView_ = null;\n        scene.camera.lookAtTransform(Cesium.Matrix4.IDENTITY);\n        return;\n      }\n\n      this.trackedFeature_ = feature;\n\n      //We can't start tracking immediately, so we set a flag and start tracking\n      //when the bounding sphere is ready (most likely next frame).\n      // this.needTrackedEntityUpdate_ = true;\n\n      const to4326Transform = this.to4326Transform_;\n      const toCesiumPosition = function() {\n        const geometry = feature.getGeometry();\n        console.assert(geometry instanceof olGeomPoint);\n        const coo = geometry.getCoordinates();\n        const coo4326 = to4326Transform(coo, undefined, coo.length);\n        return olcsCore.ol4326CoordinateToCesiumCartesian(coo4326);\n      };\n\n      // Create an invisible point entity for tracking.\n      // It is independant from the primitive/geometry created by the vector synchronizer.\n      const options = {\n        'position': new Cesium.CallbackProperty((time, result) => toCesiumPosition(), false),\n        'point': {\n          'pixelSize': 1,\n          'color': Cesium.Color.TRANSPARENT\n        }\n      };\n\n      this.trackedEntity_ = this.dataSourceDisplay_.defaultDataSource.entities.add(options);\n    }\n  }\n}\n\nexport default OLCesium;\n", "/**\n * @module olcs.OverlaySynchronizer\n */\nimport olcsSynchronizedOverlay from './SynchronizedOverlay.js';\nimport {getUid} from './util.js';\n\nclass OverlaySynchronizer {\n  /**\n  * @param {!ol.Map} map\n  * @param {!Cesium.Scene} scene\n  * @constructor\n  * @template T\n  * @api\n  */\n  constructor(map, scene) {\n    /**\n    * @type {!ol.Map}\n    * @protected\n    */\n    this.map = map;\n\n    /**\n    * @type {ol.Collection.<ol.Overlay>}\n    * @private\n    */\n    this.overlays_ = this.map.getOverlays();\n\n    /**\n    * @type {!Cesium.Scene}\n    * @protected\n    */\n    this.scene = scene;\n\n    /**\n    * @private\n    * @type {!Element}\n    */\n    this.overlayContainerStopEvent_ = document.createElement('DIV');\n    this.overlayContainerStopEvent_.className = 'ol-overlaycontainer-stopevent';\n    const overlayEvents = ['click', 'dblclick', 'mousedown', 'touchstart', 'MSPointerDown', 'pointerdown', 'mousewheel', 'wheel'];\n    overlayEvents.forEach((event) => {\n      this.overlayContainerStopEvent_.addEventListener(event, evt => evt.stopPropagation());\n    });\n    this.scene.canvas.parentElement.appendChild(this.overlayContainerStopEvent_);\n\n    /**\n    * @private\n    * @type {!Element}\n    */\n    this.overlayContainer_ = document.createElement('DIV');\n    this.overlayContainer_.className = 'ol-overlaycontainer';\n    this.scene.canvas.parentElement.appendChild(this.overlayContainer_);\n\n\n    /**\n    * @type {!Object<?,olcs.SynchronizedOverlay>}\n    * @private\n    */\n    this.overlayMap_ = {};\n  }\n\n  /**\n  * Get the element that serves as a container for overlays that don't allow\n  * event propagation. Elements added to this container won't let mousedown and\n  * touchstart events through to the map, so clicks and gestures on an overlay\n  * don't trigger any {@link ol.MapBrowserEvent}.\n  * @return {!Element} The map's overlay container that stops events.\n  */\n  getOverlayContainerStopEvent() {\n    return this.overlayContainerStopEvent_;\n  }\n\n  /**\n  * Get the element that serves as a container for overlays.\n  * @return {!Element} The map's overlay container.\n  */\n  getOverlayContainer() {\n    return this.overlayContainer_;\n  }\n\n  /**\n  * Destroy all and perform complete synchronization of the overlays.\n  * @api\n  */\n  synchronize() {\n    this.destroyAll();\n    this.addOverlays();\n    this.overlays_.on('add', this.addOverlayFromEvent_.bind(this));\n    this.overlays_.on('remove', this.removeOverlayFromEvent_.bind(this));\n  }\n\n  /**\n  * @param {ol.Collection.Event} event\n  * @private\n  */\n  addOverlayFromEvent_(event) {\n    const overlay = /** @type {ol.Overlay} */ (event.element);\n    this.addOverlay(overlay);\n  }\n\n  /**\n  * @api\n  */\n  addOverlays() {\n    this.overlays_.forEach((overlay) => { this.addOverlay(overlay); });\n  }\n\n  /**\n  * @param {ol.Overlay} overlay\n  * @api\n  */\n  addOverlay(overlay) {\n    if (!overlay) {\n      return;\n    }\n    const cesiumOverlay = new olcsSynchronizedOverlay({\n      scene: this.scene,\n      synchronizer: this,\n      parent: overlay\n    });\n\n    const overlayId = getUid(overlay).toString();\n    this.overlayMap_[overlayId] = cesiumOverlay;\n  }\n\n  /**\n  * @param {ol.Collection.Event} event\n  * @private\n  */\n  removeOverlayFromEvent_(event) {\n    const removedOverlay = /** @type {ol.Overlay} */ (event.element);\n    this.removeOverlay(removedOverlay);\n  }\n\n  /**\n  * Removes an overlay from the scene\n  * @param {ol.Overlay} overlay\n  * @api\n  */\n  removeOverlay(overlay) {\n    const overlayId = getUid(overlay).toString();\n    const csOverlay = this.overlayMap_[overlayId];\n    if (csOverlay) {\n      csOverlay.destroy();\n      delete this.overlayMap_[overlayId];\n    }\n  }\n\n  /**\n  * Destroys all the created Cesium objects.\n  * @protected\n  */\n  destroyAll() {\n    Object.keys(this.overlayMap_).forEach((key) => {\n      const overlay = this.overlayMap_[key];\n      overlay.destroy();\n      delete this.overlayMap_[key];\n    });\n  }\n}\n\n\nexport default OverlaySynchronizer;\n", "/**\n * @module olcs.RasterSynchronizer\n */\nimport olLayerGroup from 'ol/layer/Group.js';\nimport {getUid, stableSort} from './util.js';\nimport olcsAbstractSynchronizer from './AbstractSynchronizer.js';\nimport olcsCore from './core.js';\n\nclass RasterSynchronizer extends olcsAbstractSynchronizer {\n  /**\n   * This object takes care of one-directional synchronization of\n   * Openlayers raster layers to the given Cesium globe.\n   * @param {!ol.Map} map\n   * @param {!Cesium.Scene} scene\n   * @constructor\n   * @extends {olcsAbstractSynchronizer.<Cesium.ImageryLayer>}\n   * @api\n   */\n  constructor(map, scene) {\n    super(map, scene);\n\n    /**\n     * @type {!Cesium.ImageryLayerCollection}\n     * @private\n     */\n    this.cesiumLayers_ = scene.imageryLayers;\n\n    /**\n     * @type {!Cesium.ImageryLayerCollection}\n     * @private\n     */\n    this.ourLayers_ = new Cesium.ImageryLayerCollection();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  addCesiumObject(object) {\n    this.cesiumLayers_.add(object);\n    this.ourLayers_.add(object);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  destroyCesiumObject(object) {\n    object.destroy();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  removeSingleCesiumObject(object, destroy) {\n    this.cesiumLayers_.remove(object, destroy);\n    this.ourLayers_.remove(object, false);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  removeAllCesiumObjects(destroy) {\n    for (let i = 0; i < this.ourLayers_.length; ++i) {\n      this.cesiumLayers_.remove(this.ourLayers_.get(i), destroy);\n    }\n    this.ourLayers_.removeAll(false);\n  }\n\n  /**\n   * Creates an array of Cesium.ImageryLayer.\n   * May be overriden by child classes to implement custom behavior.\n   * The default implementation handles tiled imageries in EPSG:4326 or\n   * EPSG:3859.\n   * @param {!ol.layer.Base} olLayer\n   * @param {!ol.proj.Projection} viewProj Projection of the view.\n   * @return {?Array.<!Cesium.ImageryLayer>} array or null if not possible\n   * (or supported)\n   * @protected\n   */\n  convertLayerToCesiumImageries(olLayer, viewProj) {\n    const result = olcsCore.tileLayerToImageryLayer(this.map, olLayer, viewProj);\n    return result ? [result] : null;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  createSingleLayerCounterparts(olLayerWithParents) {\n    const olLayer = olLayerWithParents.layer;\n    const uid = getUid(olLayer).toString();\n    const viewProj = this.view.getProjection();\n    console.assert(viewProj);\n    const cesiumObjects = this.convertLayerToCesiumImageries(olLayer, viewProj);\n    if (cesiumObjects) {\n      const listenKeyArray = [];\n      [olLayerWithParents.layer].concat(olLayerWithParents.parents).forEach((olLayerItem) => {\n        listenKeyArray.push(olLayerItem.on(['change:opacity', 'change:visible'], () => {\n          // the compiler does not seem to be able to infer this\n          console.assert(cesiumObjects);\n          for (let i = 0; i < cesiumObjects.length; ++i) {\n            olcsCore.updateCesiumLayerProperties(olLayerWithParents, cesiumObjects[i]);\n          }\n        }));\n      });\n\n      if (olLayer.getStyleFunction) {\n        let previousStyleFunction = olLayer.getStyleFunction();\n        // there is no convenient way to detect a style function change in OL\n        listenKeyArray.push(olLayer.on('change', () => {\n          const currentStyleFunction = olLayer.getStyleFunction();\n          if (previousStyleFunction === currentStyleFunction) {\n            return;\n          }\n          previousStyleFunction = currentStyleFunction;\n          for (let i = 0; i < cesiumObjects.length; ++i) {\n            const csObj = cesiumObjects[i];\n            // clear cache and set new style\n            if (csObj._imageryCache && csObj.imageryProvider.cache_) {\n              csObj._imageryCache = {};\n              csObj.imageryProvider.cache_ = {};\n              csObj.imageryProvider.styleFunction_ = currentStyleFunction;\n            }\n          }\n          this.scene.requestRender();\n        }));\n      }\n\n      for (let i = 0; i < cesiumObjects.length; ++i) {\n        olcsCore.updateCesiumLayerProperties(olLayerWithParents, cesiumObjects[i]);\n      }\n\n      // there is no way to modify Cesium layer extent,\n      // we have to recreate when OpenLayers layer extent changes:\n      listenKeyArray.push(olLayer.on('change:extent', (e) => {\n        for (let i = 0; i < cesiumObjects.length; ++i) {\n          this.cesiumLayers_.remove(cesiumObjects[i], true); // destroy\n          this.ourLayers_.remove(cesiumObjects[i], false);\n        }\n        delete this.layerMap[getUid(olLayer)]; // invalidate the map entry\n        this.synchronize();\n      }));\n\n      listenKeyArray.push(olLayer.on('change', (e) => {\n        // when the source changes, re-add the layer to force update\n        for (let i = 0; i < cesiumObjects.length; ++i) {\n          const position = this.cesiumLayers_.indexOf(cesiumObjects[i]);\n          if (position >= 0) {\n            this.cesiumLayers_.remove(cesiumObjects[i], false);\n            this.cesiumLayers_.add(cesiumObjects[i], position);\n          }\n        }\n      }));\n\n      this.olLayerListenKeys[uid].push(...listenKeyArray);\n    }\n\n    return Array.isArray(cesiumObjects) ? cesiumObjects : null;\n  }\n\n  /**\n   * Order counterparts using the same algorithm as the Openlayers renderer:\n   * z-index then original sequence order.\n   * @override\n   * @protected\n   */\n  orderLayers() {\n    const layers = [];\n    const zIndices = {};\n    const queue = [this.mapLayerGroup];\n\n    while (queue.length > 0) {\n      const olLayer = queue.splice(0, 1)[0];\n      layers.push(olLayer);\n      zIndices[getUid(olLayer)] = olLayer.getZIndex() || 0;\n\n      if (olLayer instanceof olLayerGroup) {\n        const sublayers = olLayer.getLayers();\n        if (sublayers) {\n          // Prepend queue with sublayers in order\n          queue.unshift(...sublayers.getArray());\n        }\n      }\n    }\n\n    stableSort(layers, (layer1, layer2) =>\n      zIndices[getUid(layer1)] - zIndices[getUid(layer2)]\n    );\n\n    layers.forEach((olLayer) => {\n      const olLayerId = getUid(olLayer).toString();\n      const cesiumObjects = this.layerMap[olLayerId];\n      if (cesiumObjects) {\n        cesiumObjects.forEach((cesiumObject) => { this.raiseToTop(cesiumObject); });\n      }\n    });\n  }\n\n  /**\n   * @param {Cesium.ImageryLayer} counterpart\n   */\n  raiseToTop(counterpart) {\n    this.cesiumLayers_.raiseToTop(counterpart);\n  }\n}\n\n\nexport default RasterSynchronizer;\n", "/**\n * @module olcs.SynchronizedOverlay\n */\nimport olOverlay from 'ol/Overlay.js';\nimport {transform} from 'ol/proj.js';\nimport {removeNode, removeChildren} from './util.js';\nimport {unByKey as olObservableUn<PERSON>y<PERSON>ey} from 'ol/Observable.js';\n\n\n/**\n * Options for SynchronizedOverlay\n * @typedef {Object} SynchronizedOverlayOptions\n * @property {!Cesium.Scene} scene\n * @property {olOverlay} parent\n * @property {!import('olsc/OverlaySynchronizer.js').default} synchronizer\n */\n\n\nclass SynchronizedOverlay extends olOverlay {\n  /**\n   * @param {olcsx.SynchronizedOverlayOptions} options SynchronizedOverlay Options.\n   * @api\n   */\n  constructor(options) {\n    const parent = options.parent;\n    super(parent.getOptions());\n\n    /**\n     * @private\n     * @type {?Function}\n     */\n    this.scenePostRenderListenerRemover_ = null;\n\n    /**\n     * @private\n     * @type {!Cesium.Scene}\n     */\n    this.scene_ = options.scene;\n\n    /**\n     * @private\n     * @type {!olcs.OverlaySynchronizer}\n     */\n    this.synchronizer_ = options.synchronizer;\n\n    /**\n     * @private\n     * @type {!ol.Overlay}\n     */\n    this.parent_ = parent;\n\n    /**\n     * @private\n     * @type {ol.Coordinate|undefined}\n     */\n    this.positionWGS84_ = undefined;\n\n    /**\n     * @private\n     * @type {MutationObserver}\n     */\n    this.observer_ = new MutationObserver(this.handleElementChanged.bind(this));\n\n    /**\n     * @private\n     * @type {Array.<MutationObserver>}\n     */\n    this.attributeObserver_ = [];\n\n    /**\n     * @private\n     * @type {Array<ol.EventsKey>}\n     */\n    this.listenerKeys_ = [];\n    // synchronize our Overlay with the parent Overlay\n    const setPropertyFromEvent = event => this.setPropertyFromEvent_(event);\n    this.listenerKeys_.push(this.parent_.on('change:position', setPropertyFromEvent));\n    this.listenerKeys_.push(this.parent_.on('change:element', setPropertyFromEvent));\n    this.listenerKeys_.push(this.parent_.on('change:offset', setPropertyFromEvent));\n    this.listenerKeys_.push(this.parent_.on('change:position', setPropertyFromEvent));\n    this.listenerKeys_.push(this.parent_.on('change:positioning', setPropertyFromEvent));\n\n    this.setProperties(this.parent_.getProperties());\n\n    this.handleMapChanged();\n    this.handleElementChanged();\n  }\n\n  /**\n   * @param {Node} target\n   * @private\n   */\n  observeTarget_(target) {\n    if (!this.observer_) {\n      // not ready, skip the event (this occurs on construction)\n      return;\n    }\n    this.observer_.disconnect();\n    this.observer_.observe(target, {\n      attributes: false,\n      childList: true,\n      characterData: true,\n      subtree: true\n    });\n    this.attributeObserver_.forEach((observer) => {\n      observer.disconnect();\n    });\n    this.attributeObserver_.length = 0;\n    for (let i = 0; i < target.childNodes.length; i++) {\n      const node = target.childNodes[i];\n      if (node.nodeType === 1) {\n        const observer = new MutationObserver(this.handleElementChanged.bind(this));\n        observer.observe(node, {\n          attributes: true,\n          subtree: true\n        });\n        this.attributeObserver_.push(observer);\n      }\n    }\n  }\n\n  /**\n   *\n   * @param {ol.Object.Event} event\n   * @private\n   */\n  setPropertyFromEvent_(event) {\n    if (event.target && event.key) {\n      this.set(event.key, event.target.get(event.key));\n    }\n  }\n\n  /**\n   * Get the scene associated with this overlay.\n   * @see ol.Overlay.prototype.getMap\n   * @return {!Cesium.Scene} The scene that the overlay is part of.\n   * @api\n   */\n  getScene() {\n    return this.scene_;\n  }\n\n  /**\n   * @override\n   */\n  handleMapChanged() {\n    if (this.scenePostRenderListenerRemover_) {\n      this.scenePostRenderListenerRemover_();\n      removeNode(this.element);\n    }\n    this.scenePostRenderListenerRemover_ = null;\n    const scene = this.getScene();\n    if (scene) {\n      this.scenePostRenderListenerRemover_ = scene.postRender.addEventListener(this.updatePixelPosition.bind(this));\n      this.updatePixelPosition();\n      const container = this.stopEvent ?\n        this.synchronizer_.getOverlayContainerStopEvent() : this.synchronizer_.getOverlayContainer();\n      if (this.insertFirst) {\n        container.insertBefore(this.element, container.childNodes[0] || null);\n      } else {\n        container.appendChild(this.element);\n      }\n    }\n  }\n\n  /**\n   * @override\n   */\n  handlePositionChanged() {\n    // transform position to WGS84\n    const position = this.getPosition();\n    if (position) {\n      const sourceProjection = this.parent_.getMap().getView().getProjection();\n      this.positionWGS84_ = transform(position, sourceProjection, 'EPSG:4326');\n    } else {\n      this.positionWGS84_ = undefined;\n    }\n    this.updatePixelPosition();\n  }\n\n  /**\n   * @override\n   */\n  handleElementChanged() {\n    function cloneNode(node, parent) {\n      const clone = node.cloneNode();\n      if (node.nodeName === 'CANVAS') {\n        const ctx = clone.getContext('2d');\n        ctx.drawImage(node, 0, 0);\n      }\n      if (parent) {\n        parent.appendChild(clone);\n      }\n      if (node.nodeType != Node.TEXT_NODE) {\n        clone.addEventListener('click', (event) => {\n          node.dispatchEvent(new MouseEvent('click', event));\n          event.stopPropagation();\n        });\n      }\n      const nodes = node.childNodes;\n      for (let i = 0; i < nodes.length; i++) {\n        if (!nodes[i]) {\n          continue;\n        }\n        cloneNode(nodes[i], clone);\n      }\n      return clone;\n    }\n    removeChildren(this.element);\n    const element = this.getElement();\n    if (element) {\n      if (element.parentNode && element.parentNode.childNodes) {\n        for (const node of element.parentNode.childNodes) {\n          const clonedNode = cloneNode(node, null);\n          this.element.appendChild(clonedNode);\n        }\n      }\n    }\n    if (element.parentNode) {\n      // set new Observer\n      this.observeTarget_(element.parentNode);\n    }\n  }\n\n  /**\n   * @override\n   */\n  updatePixelPosition() {\n    const position = this.positionWGS84_;\n    if (!this.scene_ || !position) {\n      this.setVisible(false);\n      return;\n    }\n    let height = 0;\n    if (position.length === 2) {\n      const globeHeight = this.scene_.globe.getHeight(Cesium.Cartographic.fromDegrees(position[0], position[1]));\n      if (globeHeight && this.scene_.globe.tilesLoaded) {\n        position[2] = globeHeight;\n      }\n      if (globeHeight) {\n        height = globeHeight;\n      }\n    } else {\n      height = position[2];\n    }\n    const cartesian = Cesium.Cartesian3.fromDegrees(position[0], position[1], height);\n    const camera = this.scene_.camera;\n    const ellipsoidBoundingSphere = new Cesium.BoundingSphere(new Cesium.Cartesian3(), 6356752);\n    const occluder = new Cesium.Occluder(ellipsoidBoundingSphere, camera.position);\n    // check if overlay position is behind the horizon\n    if (!occluder.isPointVisible(cartesian)) {\n      this.setVisible(false);\n      return;\n    }\n    const cullingVolume = camera.frustum.computeCullingVolume(camera.position, camera.direction, camera.up);\n    // check if overlay position is visible from the camera\n    if (cullingVolume.computeVisibility(new Cesium.BoundingSphere(cartesian)) !== 1) {\n      this.setVisible(false);\n      return;\n    }\n    this.setVisible(true);\n\n    const pixelCartesian = this.scene_.cartesianToCanvasCoordinates(cartesian);\n    const pixel = [pixelCartesian.x, pixelCartesian.y];\n    const mapSize = [this.scene_.canvas.width, this.scene_.canvas.height];\n    this.updateRenderedPosition(pixel, mapSize);\n  }\n\n  /**\n   * Destroys the overlay, removing all its listeners and elements\n   * @api\n   */\n  destroy() {\n    if (this.scenePostRenderListenerRemover_) {\n      this.scenePostRenderListenerRemover_();\n    }\n    if (this.observer_) {\n      this.observer_.disconnect();\n    }\n    olObservableUnByKey(this.listenerKeys_);\n    this.listenerKeys_.splice(0);\n    if (this.element.removeNode) {\n      this.element.removeNode(true);\n    } else {\n      this.element.remove();\n    }\n    this.element = null;\n  }\n}\n\nexport default SynchronizedOverlay;\n", "/**\n * @module olcs.VectorSynchronizer\n */\nimport olSourceVector from 'ol/source/Vector.js';\nimport olLayerLayer from 'ol/layer/Layer.js';\nimport olSourceCluster from 'ol/source/Cluster.js';\nimport olLayerImage from 'ol/layer/Image.js';\nimport {olcsListen, getUid} from './util.js';\nimport olLayerVector from 'ol/layer/Vector.js';\nimport olLayerVectorTile from 'ol/layer/VectorTile.js';\nimport olcsAbstractSynchronizer from './AbstractSynchronizer.js';\nimport olcsFeatureConverter from './FeatureConverter.js';\n\nclass VectorSynchronizer extends olcsAbstractSynchronizer {\n  /**\n   * Unidirectionally synchronize OpenLayers vector layers to Cesium.\n   * @param {!ol.Map} map\n   * @param {!Cesium.Scene} scene\n   * @param {olcs.FeatureConverter=} opt_converter\n   * @extends {olcs.AbstractSynchronizer.<olcs.core.VectorLayerCounterpart>}\n   * @api\n   */\n  constructor(map, scene, opt_converter) {\n    super(map, scene);\n\n    /**\n     * @protected\n     */\n    this.converter = opt_converter || new olcsFeatureConverter(scene);\n\n    /**\n     * @private\n     */\n    this.csAllPrimitives_ = new Cesium.PrimitiveCollection();\n    scene.primitives.add(this.csAllPrimitives_);\n    this.csAllPrimitives_.destroyPrimitives = false;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  addCesiumObject(counterpart) {\n    console.assert(counterpart);\n    counterpart.getRootPrimitive()['counterpart'] = counterpart;\n    this.csAllPrimitives_.add(counterpart.getRootPrimitive());\n  }\n\n  /**\n   * @inheritDoc\n   */\n  destroyCesiumObject(object) {\n    object.getRootPrimitive().destroy();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  removeSingleCesiumObject(object, destroy) {\n    object.destroy();\n    this.csAllPrimitives_.destroyPrimitives = destroy;\n    this.csAllPrimitives_.remove(object.getRootPrimitive());\n    this.csAllPrimitives_.destroyPrimitives = false;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  removeAllCesiumObjects(destroy) {\n    this.csAllPrimitives_.destroyPrimitives = destroy;\n    if (destroy) {\n      for (let i = 0; i < this.csAllPrimitives_.length; ++i) {\n        this.csAllPrimitives_.get(i)['counterpart'].destroy();\n      }\n    }\n    this.csAllPrimitives_.removeAll();\n    this.csAllPrimitives_.destroyPrimitives = false;\n  }\n\n  /**\n   * Synchronizes the layer visibility properties\n   * to the given Cesium Primitive.\n   * @param {import('olsc/core.js').LayerWithParents} olLayerWithParents\n   * @param {!Cesium.Primitive} csPrimitive\n   */\n  updateLayerVisibility(olLayerWithParents, csPrimitive) {\n    let visible = true;\n    [olLayerWithParents.layer].concat(olLayerWithParents.parents).forEach((olLayer) => {\n      const layerVisible = olLayer.getVisible();\n      if (layerVisible !== undefined) {\n        visible &= layerVisible;\n      } else {\n        visible = false;\n      }\n    });\n    csPrimitive.show = visible;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  createSingleLayerCounterparts(olLayerWithParents) {\n    const olLayer = olLayerWithParents.layer;\n    if (!(olLayer instanceof olLayerVector) || olLayer instanceof olLayerVectorTile) {\n      return null;\n    }\n    console.assert(olLayer instanceof olLayerLayer);\n\n    let source = olLayer.getSource();\n    if (source instanceof olSourceCluster) {\n      source = source.getSource();\n    }\n\n    if (!source) {\n      return null;\n    }\n\n    console.assert(source instanceof olSourceVector);\n    console.assert(this.view);\n\n    const view = this.view;\n    const featurePrimitiveMap = {};\n    const counterpart = this.converter.olVectorLayerToCesium(olLayer, view,\n        featurePrimitiveMap);\n    const csPrimitives = counterpart.getRootPrimitive();\n    const olListenKeys = counterpart.olListenKeys;\n\n    [olLayerWithParents.layer].concat(olLayerWithParents.parents).forEach((olLayerItem) => {\n      olListenKeys.push(olcsListen(olLayerItem, 'change:visible', () => {\n        this.updateLayerVisibility(olLayerWithParents, csPrimitives);\n      }));\n    });\n    this.updateLayerVisibility(olLayerWithParents, csPrimitives);\n\n    const onAddFeature = (function(feature) {\n      console.assert(\n          (olLayer instanceof olLayerVector) ||\n          (olLayer instanceof olLayerImage)\n      );\n      const context = counterpart.context;\n      const prim = this.converter.convert(olLayer, view, feature, context);\n      if (prim) {\n        featurePrimitiveMap[getUid(feature)] = prim;\n        csPrimitives.add(prim);\n      }\n    }).bind(this);\n\n    const onRemoveFeature = (function(feature) {\n      const id = getUid(feature);\n      const context = counterpart.context;\n      const bbs = context.featureToCesiumMap[id];\n      if (bbs) {\n        delete context.featureToCesiumMap[id];\n        bbs.forEach((bb) => {\n          if (bb instanceof Cesium.Billboard) {\n            context.billboards.remove(bb);\n          }\n        });\n      }\n      const csPrimitive = featurePrimitiveMap[id];\n      delete featurePrimitiveMap[id];\n      if (csPrimitive) {\n        csPrimitives.remove(csPrimitive);\n      }\n    }).bind(this);\n\n    olListenKeys.push(olcsListen(source, 'addfeature', (e) => {\n      console.assert(e.feature);\n      onAddFeature(e.feature);\n    }, this));\n\n    olListenKeys.push(olcsListen(source, 'removefeature', (e) => {\n      console.assert(e.feature);\n      onRemoveFeature(e.feature);\n    }, this));\n\n    olListenKeys.push(olcsListen(source, 'changefeature', (e) => {\n      const feature = e.feature;\n      console.assert(feature);\n      onRemoveFeature(feature);\n      onAddFeature(feature);\n    }, this));\n\n    return counterpart ? [counterpart] : null;\n  }\n}\n\nexport default VectorSynchronizer;\n", "/**\n * @module olcs.contrib.LazyLoader\n */\nexport default class <PERSON><PERSON><PERSON>oader {\n  /**\n   * @param {string} url\n   * @api\n   */\n  constructor(url) {\n    /**\n     * @type {Promise<undefined>}\n     * @protected\n     */\n    this.promise;\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.url_ = url;\n  }\n\n  /**\n   * @return {Promise<undefined>}\n   * @api\n   */\n  load() {\n    if (!this.promise) {\n      // not yet loading\n      this.promise = new Promise((resolve, reject) => {\n        const script = document.createElement('script');\n        script.onload = () => resolve();\n        script.onerror = () => reject();\n        document.head.appendChild(script);\n        script.src = this.url_;\n      });\n    }\n    return this.promise;\n  }\n}\n", "/**\n * @module olcs.contrib.Manager\n */\nimport olcsContribLazyLoader from '../contrib/LazyLoader.js';\nimport OLCesium from '../OLCesium.js';\nimport olcsCore from '../core.js';\nimport {toRadians} from '../math.js';\nimport olObservable from 'ol/Observable.js';\n\n\n/**\n * @typedef {Object} ManagerOptions\n * @property {import('ol/Map.js').default} map\n * @property {import('ol/extent.js').Extent} [cameraExtentInRadians]\n * @property {string} [cesiumIonDefaultAccessToken]\n */\n\n\nconst Manager = class extends olObservable {\n  /**\n   * @param {string} cesiumUrl\n   * @param {olcsx.contrib.ManagerOptions} options\n   * @api\n   */\n  constructor(cesiumUrl, {map, cameraExtentInRadians, cesiumIonDefaultAccessToken} = {}) {\n\n    super();\n\n    /**\n     * @type {string}\n     * @private\n     */\n    this.cesiumUrl_ = cesiumUrl;\n\n    /**\n     * @type {ol.Map}\n     * @protected\n     */\n    this.map = map;\n\n    /**\n     * @type {ol.Extent}\n     * @protected\n     */\n    this.cameraExtentInRadians = cameraExtentInRadians || null;\n\n    /**\n     * @private\n     * @type {Cesium.BoundingSphere}\n     */\n    this.boundingSphere_;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.blockLimiter_ = false;\n\n    /**\n     * @type {Promise.<olcs.OLCesium>}\n     * @private\n     */\n    this.promise_;\n\n    /**\n     * @type {string}\n     * @private\n     */\n    this.cesiumIonDefaultAccessToken_ = cesiumIonDefaultAccessToken;\n\n    /**\n     * @type {olcs.OLCesium}\n     * @protected\n     */\n    this.ol3d;\n\n    /**\n     * @const {number} Tilt angle in radians\n     * @private\n     */\n    this.cesiumInitialTilt_ = toRadians(50);\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.fogDensity = 0.0001;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.fogSSEFactor = 25;\n\n    /**\n     * Limit the minimum distance to the terrain to 2m.\n     * @protected\n     * @type {number}\n     */\n    this.minimumZoomDistance = 2;\n\n    /**\n     * Limit the maximum distance to the earth to 10'000km.\n     * @protected\n     * @type {number}\n     */\n    this.maximumZoomDistance = 10000000;\n\n    // when closer to 3000m, restrict the available positions harder\n    /**\n     * @protected\n     * @param {number} height\n     */\n    this.limitCameraToBoundingSphereRatio = height => (height > 3000 ? 9 : 3);\n  }\n\n\n  /**\n   * @return {Promise.<olcs.OLCesium>}\n   */\n  load() {\n    if (!this.promise_) {\n      const cesiumLazyLoader = new olcsContribLazyLoader(this.cesiumUrl_);\n      this.promise_ = cesiumLazyLoader.load().then(() => this.onCesiumLoaded());\n    }\n    return this.promise_;\n  }\n\n\n  /**\n   * @protected\n   * @return {olcs.OLCesium}\n   */\n  onCesiumLoaded() {\n    if (this.cameraExtentInRadians) {\n      const rect = new Cesium.Rectangle(...this.cameraExtentInRadians);\n      // Set the fly home rectangle\n      Cesium.Camera.DEFAULT_VIEW_RECTANGLE = rect;\n      this.boundingSphere_ = Cesium.BoundingSphere.fromRectangle3D(rect, Cesium.Ellipsoid.WGS84, 300); // lux mean height is 300m\n    }\n\n    if (this.cesiumIonDefaultAccessToken_) {\n      Cesium.Ion.defaultAccessToken = this.cesiumIonDefaultAccessToken_;\n    }\n\n    this.ol3d = this.instantiateOLCesium();\n    const scene = this.ol3d.getCesiumScene();\n    this.configureForUsability(scene);\n    this.configureForPerformance(scene);\n    this.dispatchEvent('load');\n    return this.ol3d;\n  }\n\n\n  /**\n   * Application code should override this method.\n   * @return {olcs.OLCesium}\n   */\n  instantiateOLCesium() {\n    console.assert(this.map);\n    const ol3d = new OLCesium({map: this.map});\n    const scene = ol3d.getCesiumScene();\n    const terrainProvider = Cesium.createWorldTerrain();\n    scene.terrainProvider = terrainProvider;\n    return ol3d;\n  }\n\n\n  /**\n   * @param {!Cesium.Scene} scene The scene, passed as parameter for convenience.\n   * @protected\n   */\n  configureForPerformance(scene) {\n    const fog = scene.fog;\n    fog.enabled = true;\n    fog.density = this.fogDensity;\n    fog.screenSpaceErrorFactor = this.fogSSEFactor;\n  }\n\n\n  /**\n   * @param {!Cesium.Scene} scene The scene, passed as parameter for convenience.\n   * @protected\n   */\n  configureForUsability(scene) {\n    const sscController = scene.screenSpaceCameraController;\n    sscController.minimumZoomDistance = this.minimumZoomDistance;\n    sscController.maximumZoomDistance = this.maximumZoomDistance;\n\n    // Do not see through the terrain. Seeing through the terrain does not make\n    // sense anyway, except for debugging\n    scene.globe.depthTestAgainstTerrain = true;\n\n    // Use white instead of the black default colour for the globe when tiles are missing\n    scene.globe.baseColor = Cesium.Color.WHITE;\n    scene.backgroundColor = Cesium.Color.WHITE;\n\n    if (this.boundingSphere_) {\n      scene.postRender.addEventListener(this.limitCameraToBoundingSphere.bind(this), scene);\n    }\n    // Stop rendering Cesium when there is nothing to do. This drastically reduces CPU/GPU consumption.\n    this.ol3d.enableAutoRenderLoop();\n  }\n\n\n  /**\n   * Constrain the camera so that it stays close to the bounding sphere of the map extent.\n   * Near the ground the allowed distance is shorter.\n   * @protected\n   */\n  limitCameraToBoundingSphere() {\n    if (this.boundingSphere_ && !this.blockLimiter_) {\n      const scene = this.ol3d.getCesiumScene();\n      const camera = scene.camera;\n      const position = camera.position;\n      const carto = Cesium.Cartographic.fromCartesian(position);\n      const ratio = this.limitCameraToBoundingSphereRatio(carto.height);\n      if (Cesium.Cartesian3.distance(this.boundingSphere_.center, position) > this.boundingSphere_.radius * ratio) {\n        const currentlyFlying = camera.flying;\n        if (currentlyFlying === true) {\n          // There is a flying property and its value is true\n          return;\n        } else {\n          this.blockLimiter_ = true;\n          const unblockLimiter = () => this.blockLimiter_ = false;\n          camera.flyToBoundingSphere(this.boundingSphere_, {\n            complete: unblockLimiter,\n            cancel: unblockLimiter\n          });\n        }\n      }\n    }\n  }\n\n\n  /**\n   * Enable or disable ol3d with a default animation.\n   * @export\n   * @return {Promise<undefined>}\n   */\n  toggle3d() {\n    return this.load().then((/** @const {!olcs.OLCesium} */ ol3d) => {\n      const is3DCurrentlyEnabled = ol3d.getEnabled();\n      const scene = ol3d.getCesiumScene();\n      if (is3DCurrentlyEnabled) {\n        // Disable 3D\n        console.assert(this.map);\n        return olcsCore.resetToNorthZenith(this.map, scene).then(() => {\n          ol3d.setEnabled(false);\n          this.dispatchEvent('toggle');\n        });\n      } else {\n        // Enable 3D\n        ol3d.setEnabled(true);\n        this.dispatchEvent('toggle');\n        return olcsCore.rotateAroundBottomCenter(scene, this.cesiumInitialTilt_);\n      }\n    });\n  }\n\n\n  /**\n   * Enable ol3d with a view built from parameters.\n   *\n   * @export\n   * @param {number} lon\n   * @param {number} lat\n   * @param {number} elevation\n   * @param {number} headingDeg Heading value in degrees.\n   * @param {number} pitchDeg Pitch value in degrees.\n   * @returns {Promise<undefined>}\n   */\n  set3dWithView(lon, lat, elevation, headingDeg, pitchDeg) {\n    return this.load().then((/** @const {!olcs.OLCesium} */ ol3d) => {\n      const is3DCurrentlyEnabled = ol3d.getEnabled();\n      const scene = ol3d.getCesiumScene();\n      const camera = scene.camera;\n      const destination = Cesium.Cartesian3.fromDegrees(lon, lat, elevation);\n      const heading = Cesium.Math.toRadians(headingDeg);\n      const pitch = Cesium.Math.toRadians(pitchDeg);\n      const roll = 0;\n      const orientation = {heading, pitch, roll};\n\n      if (!is3DCurrentlyEnabled) {\n        ol3d.setEnabled(true);\n        this.dispatchEvent('toggle');\n      }\n\n      camera.setView({\n        destination,\n        orientation\n      });\n    });\n  }\n\n\n  /**\n   * @export\n   * @return {boolean}\n   */\n  is3dEnabled() {\n    return !!this.ol3d && this.ol3d.getEnabled();\n  }\n\n\n  /**\n   * @return {number}\n   */\n  getHeading() {\n    return this.map ? this.map.getView().getRotation() || 0 : 0;\n  }\n\n\n  /**\n   * @return {number|undefined}\n   */\n  getTiltOnGlobe() {\n    const scene = this.ol3d.getCesiumScene();\n    const tiltOnGlobe = olcsCore.computeSignedTiltAngleOnGlobe(scene);\n    return -tiltOnGlobe;\n  }\n\n\n  /**\n   * @param {number} angle\n   */\n  setHeading(angle) {\n    const scene = this.ol3d.getCesiumScene();\n    const bottom = olcsCore.pickBottomPoint(scene);\n    if (bottom) {\n      olcsCore.setHeadingUsingBottomCenter(scene, angle, bottom);\n    }\n  }\n\n  /**\n   * @export\n   * @return {olcs.OLCesium}\n   */\n  getOl3d() {\n    return this.ol3d;\n  }\n\n  /**\n   * @export\n   * @return {!ol.View}\n   */\n  getOlView() {\n    const view = this.map.getView();\n    console.assert(view);\n    return view;\n  }\n\n  /**\n   * @export\n   * @return {Cesium.Matrix4}\n   */\n  getCesiumViewMatrix() {\n    return this.ol3d.getCesiumScene().camera.viewMatrix;\n  }\n\n  /**\n   * @export\n   * @return {!Cesium.Scene}\n   */\n  getCesiumScene() {\n    return this.ol3d.getCesiumScene();\n  }\n\n  /**\n   * @export\n   * @param {!Cesium.Rectangle} rectangle\n   * @param {number=} offset in meters\n   * @return {Promise<undefined>}\n   */\n  flyToRectangle(rectangle, offset = 0) {\n    const camera = this.getCesiumScene().camera;\n    const destination = camera.getRectangleCameraCoordinates(rectangle);\n\n    const mag = Cesium.Cartesian3.magnitude(destination) + offset;\n    Cesium.Cartesian3.normalize(destination, destination);\n    Cesium.Cartesian3.multiplyByScalar(destination, mag, destination);\n\n    return new Promise((resolve, reject) => {\n      if (!this.cameraExtentInRadians) {\n        reject();\n        return;\n      }\n\n      camera.flyTo({\n        destination,\n        complete: () => resolve(),\n        cancel: () => reject(),\n        endTransform: Cesium.Matrix4.IDENTITY\n      });\n    });\n  }\n\n  /**\n   * @protected\n   * @return {Cesium.Rectangle|undefined}\n   */\n  getCameraExtentRectangle() {\n    if (this.cameraExtentInRadians) {\n      return new Cesium.Rectangle(...this.cameraExtentInRadians);\n    }\n  }\n};\n\n\nexport default Manager;\n", "/**\n * @module olcs.core\n */\nimport {linear as linearEasing} from 'ol/easing.js';\nimport olLayerTile from 'ol/layer/Tile.js';\nimport olLayerImage from 'ol/layer/Image.js';\nimport {get as getProjection, transformExtent} from 'ol/proj.js';\nimport olSourceImageStatic from 'ol/source/ImageStatic.js';\nimport olSourceImageWMS from 'ol/source/ImageWMS.js';\nimport olSourceTileImage from 'ol/source/TileImage.js';\nimport olSourceTileWMS from 'ol/source/TileWMS.js';\nimport olSourceVectorTile from 'ol/source/VectorTile.js';\nimport {defaultImageLoadFunction} from 'ol/source/Image.js';\nimport olcsCoreOLImageryProvider from './core/OLImageryProvider.js';\nimport olcsUtil from './util.js';\nimport MVTImageryProvider from './MVTImageryProvider.js';\nimport VectorTileLayer from 'ol/layer/VectorTile.js';\nimport {getCenter as getExtentCenter} from 'ol/extent';\n\nconst exports = {};\n\n\n/**\n * @typedef {Object} CesiumUrlDefinition\n * @property {string} url\n * @property {string} subdomains\n */\n\n\n/**\n * Options for rotate around axis core function.\n * @typedef {Object} RotateAroundAxisOption\n * @property {number} [duration]\n * @property {function(number): number} [easing]\n * @property {function(): void} [callback]\n */\n\n\n\n/**\n * @typedef {Object} LayerWithParents\n * @property {import('ol/layer/Base.js').default} layer\n * @property {Array<import('ol/layer/Group.js').default>} parents\n */\n\n\n/**\n * Compute the pixel width and height of a point in meters using the\n * camera frustum.\n * @param {!Cesium.Scene} scene\n * @param {!Cesium.Cartesian3} target\n * @return {!Cesium.Cartesian2} the pixel size\n * @api\n */\nexports.computePixelSizeAtCoordinate = function(scene, target) {\n  const camera = scene.camera;\n  const canvas = scene.canvas;\n  const frustum = camera.frustum;\n  const distance = Cesium.Cartesian3.magnitude(Cesium.Cartesian3.subtract(\n      camera.position, target, new Cesium.Cartesian3()));\n  return frustum.getPixelDimensions(canvas.clientWidth, canvas.clientHeight,\n      distance, scene.pixelRatio, new Cesium.Cartesian2());\n};\n\n\n/**\n * Compute bounding box around a target point.\n * @param {!Cesium.Scene} scene\n * @param {!Cesium.Cartesian3} target\n * @param {number} amount Half the side of the box, in pixels.\n * @return {Array<Cesium.Cartographic>} bottom left and top right\n * coordinates of the box\n */\nexports.computeBoundingBoxAtTarget = function(scene, target, amount) {\n  const pixelSize = exports.computePixelSizeAtCoordinate(scene, target);\n  const transform = Cesium.Transforms.eastNorthUpToFixedFrame(target);\n\n  const bottomLeft = Cesium.Matrix4.multiplyByPoint(\n      transform,\n      new Cesium.Cartesian3(-pixelSize.x * amount, -pixelSize.y * amount, 0),\n      new Cesium.Cartesian3());\n\n  const topRight = Cesium.Matrix4.multiplyByPoint(\n      transform,\n      new Cesium.Cartesian3(pixelSize.x * amount, pixelSize.y * amount, 0),\n      new Cesium.Cartesian3());\n\n  return Cesium.Ellipsoid.WGS84.cartesianArrayToCartographicArray(\n      [bottomLeft, topRight]);\n};\n\n\n/**\n *\n * @param {!ol.geom.Geometry} geometry\n * @param {number} height\n * @api\n */\nexports.applyHeightOffsetToGeometry = function(geometry, height) {\n  geometry.applyTransform((input, output, stride) => {\n    console.assert(input === output);\n    if (stride !== undefined && stride >= 3) {\n      for (let i = 0; i < output.length; i += stride) {\n        output[i + 2] = output[i + 2] + height;\n      }\n    }\n    return output;\n  });\n};\n\n\n/**\n * @param {ol.Coordinate} coordinates\n * @param {number=} rotation\n * @param {!Cesium.Cartesian3=} translation\n * @param {!Cesium.Cartesian3=} scale\n * @return {!Cesium.Matrix4}\n * @api\n */\nexports.createMatrixAtCoordinates = function(coordinates, rotation = 0, translation = Cesium.Cartesian3.ZERO, scale = new Cesium.Cartesian3(1, 1, 1)) {\n  const position = exports.ol4326CoordinateToCesiumCartesian(coordinates);\n  const rawMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(position);\n  const quaternion = Cesium.Quaternion.fromAxisAngle(Cesium.Cartesian3.UNIT_Z, -rotation);\n  const rotationMatrix = Cesium.Matrix4.fromTranslationQuaternionRotationScale(translation, quaternion, scale);\n  return Cesium.Matrix4.multiply(rawMatrix, rotationMatrix, new Cesium.Matrix4());\n};\n\n\n/**\n * @param {!Cesium.Camera} camera\n * @param {number} angle\n * @param {!Cesium.Cartesian3} axis\n * @param {!Cesium.Matrix4} transform\n * @param {RotateAroundAxisOption=} opt_options\n * @api\n */\nexports.rotateAroundAxis = function(camera, angle, axis, transform,\n    opt_options) {\n  const clamp = Cesium.Math.clamp;\n  const defaultValue = Cesium.defaultValue;\n\n  const options = opt_options || {};\n  const duration = defaultValue(options.duration, 500); // ms\n  const easing = defaultValue(options.easing, linearEasing);\n  const callback = options.callback;\n\n  let lastProgress = 0;\n  const oldTransform = new Cesium.Matrix4();\n\n  const start = Date.now();\n  const step = function() {\n    const timestamp = Date.now();\n    const timeDifference = timestamp - start;\n    const progress = easing(clamp(timeDifference / duration, 0, 1));\n    console.assert(progress >= lastProgress);\n\n    camera.transform.clone(oldTransform);\n    const stepAngle = (progress - lastProgress) * angle;\n    lastProgress = progress;\n    camera.lookAtTransform(transform);\n    camera.rotate(axis, stepAngle);\n    camera.lookAtTransform(oldTransform);\n\n    if (progress < 1) {\n      window.requestAnimationFrame(step);\n    } else {\n      if (callback) {\n        callback();\n      }\n    }\n  };\n  window.requestAnimationFrame(step);\n};\n\n\n/**\n * @param {!Cesium.Scene} scene\n * @param {number} heading\n * @param {!Cesium.Cartesian3} bottomCenter\n * @param {RotateAroundAxisOption=} opt_options\n * @api\n */\nexports.setHeadingUsingBottomCenter = function(scene, heading,\n    bottomCenter, opt_options) {\n  const camera = scene.camera;\n  // Compute the camera position to zenith quaternion\n  const angleToZenith = exports.computeAngleToZenith(scene, bottomCenter);\n  const axis = camera.right;\n  const quaternion = Cesium.Quaternion.fromAxisAngle(axis, angleToZenith);\n  const rotation = Cesium.Matrix3.fromQuaternion(quaternion);\n\n  // Get the zenith point from the rotation of the position vector\n  const vector = new Cesium.Cartesian3();\n  Cesium.Cartesian3.subtract(camera.position, bottomCenter, vector);\n  const zenith = new Cesium.Cartesian3();\n  Cesium.Matrix3.multiplyByVector(rotation, vector, zenith);\n  Cesium.Cartesian3.add(zenith, bottomCenter, zenith);\n\n  // Actually rotate around the zenith normal\n  const transform = Cesium.Matrix4.fromTranslation(zenith);\n  const rotateAroundAxis = exports.rotateAroundAxis;\n  rotateAroundAxis(camera, heading, zenith, transform, opt_options);\n};\n\n\n/**\n * Get the 3D position of the given pixel of the canvas.\n * @param {!Cesium.Scene} scene\n * @param {!Cesium.Cartesian2} pixel\n * @return {!Cesium.Cartesian3|undefined}\n * @api\n */\nexports.pickOnTerrainOrEllipsoid = function(scene, pixel) {\n  const ray = scene.camera.getPickRay(pixel);\n  const target = scene.globe.pick(ray, scene);\n  return target || scene.camera.pickEllipsoid(pixel);\n};\n\n\n/**\n * Get the 3D position of the point at the bottom-center of the screen.\n * @param {!Cesium.Scene} scene\n * @return {!Cesium.Cartesian3|undefined}\n * @api\n */\nexports.pickBottomPoint = function(scene) {\n  const canvas = scene.canvas;\n  const bottom = new Cesium.Cartesian2(\n      canvas.clientWidth / 2, canvas.clientHeight);\n  return exports.pickOnTerrainOrEllipsoid(scene, bottom);\n};\n\n\n/**\n * Get the 3D position of the point at the center of the screen.\n * @param {!Cesium.Scene} scene\n * @return {!Cesium.Cartesian3|undefined}\n * @api\n */\nexports.pickCenterPoint = function(scene) {\n  const canvas = scene.canvas;\n  const center = new Cesium.Cartesian2(\n      canvas.clientWidth / 2,\n      canvas.clientHeight / 2);\n  return exports.pickOnTerrainOrEllipsoid(scene, center);\n};\n\n\n/**\n * Compute the signed tilt angle on globe, between the opposite of the\n * camera direction and the target normal. Return undefined if there is no\n * intersection of the camera direction with the globe.\n * @param {!Cesium.Scene} scene\n * @return {number|undefined}\n * @api\n */\nexports.computeSignedTiltAngleOnGlobe = function(scene) {\n  const camera = scene.camera;\n  const ray = new Cesium.Ray(camera.position, camera.direction);\n  let target = scene.globe.pick(ray, scene);\n\n  if (!target) {\n    // no tiles in the area were loaded?\n    const ellipsoid = Cesium.Ellipsoid.WGS84;\n    const obj = Cesium.IntersectionTests.rayEllipsoid(ray, ellipsoid);\n    if (obj) {\n      target = Cesium.Ray.getPoint(ray, obj.start);\n    }\n  }\n\n  if (!target) {\n    return undefined;\n  }\n\n  const normal = new Cesium.Cartesian3();\n  Cesium.Ellipsoid.WGS84.geocentricSurfaceNormal(target, normal);\n\n  const angleBetween = exports.signedAngleBetween;\n  const angle = angleBetween(camera.direction, normal, camera.right) - Math.PI;\n  return Cesium.Math.convertLongitudeRange(angle);\n};\n\n\n/**\n * Compute the ray from the camera to the bottom-center of the screen.\n * @param {!Cesium.Scene} scene\n * @return {!Cesium.Ray}\n */\nexports.bottomFovRay = function(scene) {\n  const camera = scene.camera;\n  const fovy2 = camera.frustum.fovy / 2;\n  const direction = camera.direction;\n  const rotation = Cesium.Quaternion.fromAxisAngle(camera.right, fovy2);\n  const matrix = Cesium.Matrix3.fromQuaternion(rotation);\n  const vector = new Cesium.Cartesian3();\n  Cesium.Matrix3.multiplyByVector(matrix, direction, vector);\n  return new Cesium.Ray(camera.position, vector);\n};\n\n\n/**\n * Compute the angle between two Cartesian3.\n * @param {!Cesium.Cartesian3} first\n * @param {!Cesium.Cartesian3} second\n * @param {!Cesium.Cartesian3} normal Normal to test orientation against.\n * @return {number}\n */\nexports.signedAngleBetween = function(first, second, normal) {\n  // We are using the dot for the angle.\n  // Then the cross and the dot for the sign.\n  const a = new Cesium.Cartesian3();\n  const b = new Cesium.Cartesian3();\n  const c = new Cesium.Cartesian3();\n  Cesium.Cartesian3.normalize(first, a);\n  Cesium.Cartesian3.normalize(second, b);\n  Cesium.Cartesian3.cross(a, b, c);\n\n  const cosine = Cesium.Cartesian3.dot(a, b);\n  const sine = Cesium.Cartesian3.magnitude(c);\n\n  // Sign of the vector product and the orientation normal\n  const sign = Cesium.Cartesian3.dot(normal, c);\n  const angle = Math.atan2(sine, cosine);\n  return sign >= 0 ? angle : -angle;\n};\n\n\n/**\n * Compute the rotation angle around a given point, needed to reach the\n * zenith position.\n * At a zenith position, the camera direction is going througth the earth\n * center and the frustrum bottom ray is going through the chosen pivot\n * point.\n * The bottom-center of the screen is a good candidate for the pivot point.\n * @param {!Cesium.Scene} scene\n * @param {!Cesium.Cartesian3} pivot Point around which the camera rotates.\n * @return {number}\n * @api\n */\nexports.computeAngleToZenith = function(scene, pivot) {\n  // This angle is the sum of the angles 'fy' and 'a', which are defined\n  // using the pivot point and its surface normal.\n  //        Zenith |    camera\n  //           \\   |   /\n  //            \\fy|  /\n  //             \\ |a/\n  //              \\|/pivot\n  const camera = scene.camera;\n  const fy = camera.frustum.fovy / 2;\n  const ray = exports.bottomFovRay(scene);\n  const direction = Cesium.Cartesian3.clone(ray.direction);\n  Cesium.Cartesian3.negate(direction, direction);\n\n  const normal = new Cesium.Cartesian3();\n  Cesium.Ellipsoid.WGS84.geocentricSurfaceNormal(pivot, normal);\n\n  const left = new Cesium.Cartesian3();\n  Cesium.Cartesian3.negate(camera.right, left);\n\n  const a = exports.signedAngleBetween(normal, direction, left);\n  return a + fy;\n};\n\n\n/**\n * Convert an OpenLayers extent to a Cesium rectangle.\n * @param {ol.Extent} extent Extent.\n * @param {ol.ProjectionLike} projection Extent projection.\n * @return {Cesium.Rectangle} The corresponding Cesium rectangle.\n * @api\n */\nexports.extentToRectangle = function(extent, projection) {\n  if (extent && projection) {\n    const ext = transformExtent(extent, projection, 'EPSG:4326');\n    return Cesium.Rectangle.fromDegrees(ext[0], ext[1], ext[2], ext[3]);\n  } else {\n    return null;\n  }\n};\n\n\n/**\n * @param {!ol.Map} olMap\n * @param {!ol.source.Source} source\n * @param {!ol.View} viewProj\n * @param {!ol.layer.Base} olLayer\n * @return {!Cesium.ImageryProvider}\n */\nexports.sourceToImageryProvider = function(olMap, source, viewProj, olLayer) {\n  const skip = source.get('olcs_skip');\n  if (skip) {\n    return null;\n  }\n  let provider = null;\n  // Convert ImageWMS to TileWMS\n  if (source instanceof olSourceImageWMS && source.getUrl() &&\n    source.getImageLoadFunction() === defaultImageLoadFunction) {\n    const sourceProps = {\n      'olcs.proxy': source.get('olcs.proxy'),\n      'olcs.extent': source.get('olcs.extent'),\n      'olcs.projection': source.get('olcs.projection'),\n      'olcs.imagesource': source\n    };\n    source = new olSourceTileWMS({\n      url: source.getUrl(),\n      attributions: source.getAttributions(),\n      projection: source.getProjection(),\n      params: source.getParams()\n    });\n    source.setProperties(sourceProps);\n  }\n\n  if (source instanceof olSourceTileImage) {\n    let projection = olcsUtil.getSourceProjection(source);\n\n    if (!projection) {\n      // if not explicit, assume the same projection as view\n      projection = viewProj;\n    }\n\n    if (exports.isCesiumProjection(projection)) {\n      provider = new olcsCoreOLImageryProvider(olMap, source, viewProj);\n    }\n    // Projection not supported by Cesium\n    else {\n      return null;\n    }\n  } else if (source instanceof olSourceImageStatic) {\n    let projection = olcsUtil.getSourceProjection(source);\n    if (!projection) {\n      projection = viewProj;\n    }\n    if (exports.isCesiumProjection(projection)) {\n      provider = new Cesium.SingleTileImageryProvider({\n        url: source.getUrl(),\n        rectangle: new Cesium.Rectangle.fromDegrees(\n            source.getImageExtent()[0],\n            source.getImageExtent()[1],\n            source.getImageExtent()[2],\n            source.getImageExtent()[3]\n        )\n      });\n    }\n    // Projection not supported by Cesium\n    else {\n      return null;\n    }\n  } else if (source instanceof olSourceVectorTile) {\n    let projection = olcsUtil.getSourceProjection(source);\n\n    if (!projection) {\n      projection = viewProj;\n    }\n    if (skip === false) {\n      // MVT is experimental, it should be whitelisted to be synchronized\n      const fromCode = projection.getCode().split(':')[1];\n      const urls = source.urls.map(u => u.replace(fromCode, '3857'));\n      const extent = olLayer.getExtent();\n      const rectangle = exports.extentToRectangle(extent, projection);\n      const minimumLevel = source.get('olcs_minimumLevel');\n      const attributionsFunction = source.getAttributions();\n      const styleFunction = olLayer.getStyleFunction();\n      let credit;\n      if (extent && attributionsFunction) {\n        const center = getExtentCenter(extent);\n        credit = attributionsFunctionToCredits(attributionsFunction, 0, center, extent)[0];\n      }\n\n      provider = new MVTImageryProvider({\n        credit,\n        rectangle,\n        minimumLevel,\n        styleFunction,\n        urls\n      });\n      return provider;\n    }\n    return null; // FIXME: it is disabled by default right now\n  } else {\n    // sources other than TileImage|ImageStatic are currently not supported\n    return null;\n  }\n  return provider;\n};\n\n/**\n * Creates Cesium.ImageryLayer best corresponding to the given ol.layer.Layer.\n * Only supports raster layers and static images\n * @param {!ol.Map} olMap\n * @param {!ol.layer.Base} olLayer\n * @param {!ol.proj.Projection} viewProj Projection of the view.\n * @return {?Cesium.ImageryLayer} null if not possible (or supported)\n * @api\n */\nexports.tileLayerToImageryLayer = function(olMap, olLayer, viewProj) {\n\n  if (!(olLayer instanceof olLayerTile) && !(olLayer instanceof olLayerImage) &&\n    !(olLayer instanceof VectorTileLayer)) {\n    return null;\n  }\n\n  const source = olLayer.getSource();\n  if (!source) {\n    return null;\n  }\n  let provider = source.get('olcs_provider');\n  if (!provider) {\n    provider = this.sourceToImageryProvider(olMap, source, viewProj, olLayer);\n  }\n  if (!provider) {\n    return null;\n  }\n\n  const layerOptions = {};\n\n  const forcedExtent = /** @type {ol.Extent} */ (olLayer.get('olcs.extent'));\n  const ext = forcedExtent || olLayer.getExtent();\n  if (ext) {\n    layerOptions.rectangle = exports.extentToRectangle(ext, viewProj);\n  }\n\n  const cesiumLayer = new Cesium.ImageryLayer(provider, layerOptions);\n  return cesiumLayer;\n};\n\n\n/**\n * Synchronizes the layer rendering properties (opacity, visible)\n * to the given Cesium ImageryLayer.\n * @param {olcsx.LayerWithParents} olLayerWithParents\n * @param {!Cesium.ImageryLayer} csLayer\n * @api\n */\nexports.updateCesiumLayerProperties = function(olLayerWithParents, csLayer) {\n  let opacity = 1;\n  let visible = true;\n  [olLayerWithParents.layer].concat(olLayerWithParents.parents).forEach((olLayer) => {\n    const layerOpacity = olLayer.getOpacity();\n    if (layerOpacity !== undefined) {\n      opacity *= layerOpacity;\n    }\n    const layerVisible = olLayer.getVisible();\n    if (layerVisible !== undefined) {\n      visible &= layerVisible;\n    }\n  });\n  csLayer.alpha = opacity;\n  csLayer.show = visible;\n};\n\n\n/**\n * Convert a 2D or 3D OpenLayers coordinate to Cesium.\n * @param {ol.Coordinate} coordinate Ol3 coordinate.\n * @return {!Cesium.Cartesian3} Cesium cartesian coordinate\n * @api\n */\nexports.ol4326CoordinateToCesiumCartesian = function(coordinate) {\n  const coo = coordinate;\n  return coo.length > 2 ?\n    Cesium.Cartesian3.fromDegrees(coo[0], coo[1], coo[2]) :\n    Cesium.Cartesian3.fromDegrees(coo[0], coo[1]);\n};\n\n\n/**\n * Convert an array of 2D or 3D OpenLayers coordinates to Cesium.\n * @param {Array.<!ol.Coordinate>} coordinates Ol3 coordinates.\n * @return {!Array.<Cesium.Cartesian3>} Cesium cartesian coordinates\n * @api\n */\nexports.ol4326CoordinateArrayToCsCartesians = function(coordinates) {\n  console.assert(coordinates !== null);\n  const toCartesian = exports.ol4326CoordinateToCesiumCartesian;\n  const cartesians = [];\n  for (let i = 0; i < coordinates.length; ++i) {\n    cartesians.push(toCartesian(coordinates[i]));\n  }\n  return cartesians;\n};\n\n\n/**\n * Reproject an OpenLayers geometry to EPSG:4326 if needed.\n * The geometry will be cloned only when original projection is not EPSG:4326\n * and the properties will be shallow copied.\n * @param {!T} geometry\n * @param {!ol.ProjectionLike} projection\n * @return {!T}\n * @template T\n * @api\n */\nexports.olGeometryCloneTo4326 = function(geometry, projection) {\n  console.assert(projection);\n\n  const proj4326 = getProjection('EPSG:4326');\n  const proj = getProjection(projection);\n  if (proj !== proj4326) {\n    const properties = geometry.getProperties();\n    geometry = geometry.clone();\n    geometry.transform(proj, proj4326);\n    geometry.setProperties(properties);\n  }\n  return geometry;\n};\n\n\n/**\n * Convert an OpenLayers color to Cesium.\n * @param {ol.Color|CanvasGradient|CanvasPattern|string} olColor\n * @return {!Cesium.Color}\n * @api\n */\nexports.convertColorToCesium = function(olColor) {\n  olColor = olColor || 'black';\n  if (Array.isArray(olColor)) {\n    return new Cesium.Color(\n        Cesium.Color.byteToFloat(olColor[0]),\n        Cesium.Color.byteToFloat(olColor[1]),\n        Cesium.Color.byteToFloat(olColor[2]),\n        olColor[3]\n    );\n  } else if (typeof olColor == 'string') {\n    return Cesium.Color.fromCssColorString(olColor);\n  } else if (olColor instanceof CanvasPattern || olColor instanceof CanvasGradient) {\n    // Render the CanvasPattern/CanvasGradient into a canvas that will be sent to Cesium as material\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    canvas.width = canvas.height = 256;\n    ctx.fillStyle = olColor;\n    ctx.fillRect(0, 0, canvas.width, canvas.height);\n    return new Cesium.ImageMaterialProperty({\n      image: canvas\n    });\n  }\n  console.assert(false, 'impossible');\n};\n\n\n/**\n * Convert an OpenLayers url to Cesium.\n * @param {string} url\n * @return {!CesiumUrlDefinition}\n * @api\n */\nexports.convertUrlToCesium = function(url) {\n  let subdomains = '';\n  const re = /\\{(\\d|[a-z])-(\\d|[a-z])\\}/;\n  const match = re.exec(url);\n  if (match) {\n    url = url.replace(re, '{s}');\n    const startCharCode = match[1].charCodeAt(0);\n    const stopCharCode = match[2].charCodeAt(0);\n    let charCode;\n    for (charCode = startCharCode; charCode <= stopCharCode; ++charCode) {\n      subdomains += String.fromCharCode(charCode);\n    }\n  }\n  return {\n    url,\n    subdomains\n  };\n};\n\n\n/**\n * Animate the return to a top-down view from the zenith.\n * The camera is rotated to orient to the North.\n * @param {!ol.Map} map\n * @param {!Cesium.Scene} scene\n * @return {Promise<undefined>}\n * @api\n */\nexports.resetToNorthZenith = function(map, scene) {\n  return new Promise((resolve, reject) => {\n    const camera = scene.camera;\n    const pivot = exports.pickBottomPoint(scene);\n    if (!pivot) {\n      reject('Could not get bottom pivot');\n      return;\n    }\n\n    const currentHeading = map.getView().getRotation();\n    if (currentHeading === undefined) {\n      reject('The view is not initialized');\n      return;\n    }\n    const angle = exports.computeAngleToZenith(scene, pivot);\n\n    // Point to North\n    exports.setHeadingUsingBottomCenter(scene, currentHeading, pivot);\n\n    // Go to zenith\n    const transform = Cesium.Matrix4.fromTranslation(pivot);\n    const axis = camera.right;\n    const options = {\n      callback: () => {\n        const view = map.getView();\n        exports.normalizeView(view);\n        resolve();\n      }\n    };\n    exports.rotateAroundAxis(camera, -angle, axis, transform, options);\n  });\n};\n\n\n/**\n * @param {!Cesium.Scene} scene\n * @param {number} angle in radian\n * @return {Promise<undefined>}\n * @api\n */\nexports.rotateAroundBottomCenter = function(scene, angle) {\n  return new Promise((resolve, reject) => {\n    const camera = scene.camera;\n    const pivot = exports.pickBottomPoint(scene);\n    if (!pivot) {\n      reject('could not get bottom pivot');\n      return;\n    }\n\n    const options = {callback: resolve};\n    const transform = Cesium.Matrix4.fromTranslation(pivot);\n    const axis = camera.right;\n    const rotateAroundAxis = exports.rotateAroundAxis;\n    rotateAroundAxis(camera, -angle, axis, transform, options);\n  });\n};\n\n\n/**\n * Set the OpenLayers view to a specific rotation and\n * the nearest resolution.\n * @param {ol.View} view\n * @param {number=} angle\n * @api\n */\nexports.normalizeView = function(view, angle = 0) {\n  const resolution = view.getResolution();\n  view.setRotation(angle);\n  if (view.constrainResolution) {\n    view.setResolution(view.constrainResolution(resolution));\n  } else {\n    view.setResolution(view.getConstrainedResolution(resolution));\n  }\n};\n\n/**\n * Check if the given projection is managed by Cesium (WGS84 or Mercator Spheric)\n *\n * @param {ol.proj.Projection} projection Projection to check.\n * @returns {boolean} Whether it's managed by Cesium.\n */\nexports.isCesiumProjection = function(projection) {\n  const is3857 = projection === getProjection('EPSG:3857');\n  const is4326 = projection === getProjection('EPSG:4326');\n  return is3857 || is4326;\n};\n\n\nexport function attributionsFunctionToCredits(attributionsFunction, zoom, center, extent) {\n  const frameState = {\n    viewState: {zoom, center},\n    extent,\n  };\n\n  if (!attributionsFunction) {\n    return [];\n  }\n  let attributions = attributionsFunction(frameState);\n  if (!Array.isArray(attributions)) {\n    attributions = [attributions];\n  }\n\n  return attributions.map(html => new Cesium.Credit(html, true));\n}\n\nexport default exports;\n\n\n/**\n * calculate the distance between camera and centerpoint based on the resolution and latitude value\n * @param {number} resolution Number of map units per pixel.\n * @param {number} latitude Latitude in radians.\n * @param {import('cesium').Scene} scene.\n * @param {import('ol/proj/Projection').default} projection View projection.\n * @return {number} The calculated distance.\n * @api\n */\nexport function calcDistanceForResolution(resolution, latitude, scene, projection) {\n  const canvas = scene.canvas;\n  const camera = scene.camera;\n  const fovy = camera.frustum.fovy; // vertical field of view\n  console.assert(!isNaN(fovy));\n  const metersPerUnit = projection.getMetersPerUnit();\n\n  // number of \"map units\" visible in 2D (vertically)\n  const visibleMapUnits = resolution * canvas.clientHeight;\n\n  // The metersPerUnit does not take latitude into account, but it should\n  // be lower with increasing latitude -- we have to compensate.\n  // In 3D it is not possible to maintain the resolution at more than one point,\n  // so it only makes sense to use the latitude of the \"target\" point.\n  const relativeCircumference = Math.cos(Math.abs(latitude));\n\n  // how many meters should be visible in 3D\n  const visibleMeters = visibleMapUnits * metersPerUnit * relativeCircumference;\n\n  // distance required to view the calculated length in meters\n  //\n  //  fovy/2\n  //    |\\\n  //  x | \\\n  //    |--\\\n  // visibleMeters/2\n  const requiredDistance = (visibleMeters / 2) / Math.tan(fovy / 2);\n\n  // NOTE: This calculation is not absolutely precise, because metersPerUnit\n  // is a great simplification. It does not take ellipsoid/terrain into account.\n\n  return requiredDistance;\n}\n\n/**\n * calculate the resolution based on a distance(camera to position) and latitude value\n * @param {number} distance\n * @param {number} latitude\n * @param {import('cesium').Scene} scene.\n * @param {import('ol/proj/Projection').default} projection View projection.\n * @return {number} The calculated resolution.\n * @api\n */\nexport function calcResolutionForDistance(distance, latitude, scene, projection) {\n  // See the reverse calculation (calcDistanceForResolution) for details\n  const canvas = scene.canvas;\n  const camera = scene.camera;\n  const fovy = camera.frustum.fovy; // vertical field of view\n  console.assert(!isNaN(fovy));\n  const metersPerUnit = projection.getMetersPerUnit();\n\n  const visibleMeters = 2 * distance * Math.tan(fovy / 2);\n  const relativeCircumference = Math.cos(Math.abs(latitude));\n  const visibleMapUnits = visibleMeters / metersPerUnit / relativeCircumference;\n  const resolution = visibleMapUnits / canvas.clientHeight;\n\n  return resolution;\n}\n", "/**\n * @module olcs.core.OLImageryProvider\n */\nimport {get as getProjection} from 'ol/proj.js';\nimport olcsUtil from '../util.js';\nimport {Tile as TileSource} from 'ol/source.js';\nimport {attributionsFunctionToCredits} from '../core.js';\n\n\nconst olUseNewCoordinates = (function() {\n  const tileSource = new TileSource({\n    projection: 'EPSG:3857',\n    wrapX: true\n  });\n  const tileCoord = tileSource.getTileCoordForTileUrlFunction([6, -31, 22]);\n  return tileCoord && tileCoord[1] === 33 && tileCoord[2] === 22;\n  // See b/test/spec/ol/source/tile.test.js\n  // of e9a30c5cb7e3721d9370025fbe5472c322847b35 in OpenLayers repository\n})();\n\n\nclass OLImageryProvider /* should not extend Cesium.ImageryProvider */ {\n  /**\n   * Special class derived from Cesium.ImageryProvider\n   * that is connected to the given ol.source.TileImage.\n   * @param {!ol.Map} olMap\n   * @param {!ol.source.TileImage} source\n   * @param {ol.proj.Projection=} opt_fallbackProj Projection to assume if the\n   *                                               projection of the source is not defined.\n   * @constructor\n   * @extends {Cesium.ImageryProvider}\n   */\n  constructor(olMap, source, opt_fallbackProj) {\n    // Do not extend or call super constructor from\n    // Cesium.ImageryProvider since this particular function is a\n    // 'non instanciable interface' which throws on instanciation.\n\n    /**\n     * @type {!ol.source.TileImage}\n     * @private\n     */\n    this.source_ = source;\n\n    /**\n     * @type {?ol.proj.Projection}\n     * @private\n     */\n    this.projection_ = null;\n\n    /**\n     * @type {?ol.proj.Projection}\n     * @private\n     */\n    this.fallbackProj_ = opt_fallbackProj || null;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.ready_ = false;\n\n    /**\n     * @type {?Cesium.TilingScheme}\n     * @private\n     */\n    this.tilingScheme_ = null;\n\n    /**\n     * @type {?Cesium.Rectangle}\n     * @private\n     */\n    this.rectangle_ = null;\n\n    /**\n     * @type {!ol.Map}\n     * @private\n     */\n    this.map_ = olMap;\n\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.shouldRequestNextLevel = false;\n\n    const proxy = this.source_.get('olcs.proxy');\n    if (proxy) {\n      if (typeof proxy === 'function') {\n        this.proxy_ = {\n          'getURL': proxy\n        };\n      } else if (typeof proxy === 'string') {\n        this.proxy_ = new Cesium.DefaultProxy(proxy);\n      }\n    }\n\n    this.errorEvent_ = new Cesium.Event();\n\n    this.emptyCanvas_ = document.createElement('canvas');\n    this.emptyCanvas_.width = 1;\n    this.emptyCanvas_.height = 1;\n\n    this.source_.on('change', (e) => {\n      this.handleSourceChanged_();\n    });\n    this.handleSourceChanged_();\n  }\n\n  /**\n   * Checks if the underlying source is ready and cached required data.\n   * @private\n   */\n  handleSourceChanged_(frameState) {\n    if (!this.ready_ && this.source_.getState() == 'ready') {\n      this.projection_ = olcsUtil.getSourceProjection(this.source_) || this.fallbackProj_;\n      const options = {numberOfLevelZeroTilesX: 1, numberOfLevelZeroTilesY: 1};\n\n      if (this.source_.tileGrid !== null) {\n        // Get the number of tiles at level 0 if it is defined\n        this.source_.tileGrid.forEachTileCoord(this.projection_.getExtent(), 0, ([zoom, xIndex, yIndex]) => {\n          options.numberOfLevelZeroTilesX = xIndex + 1;\n          options.numberOfLevelZeroTilesY = yIndex + 1;\n        });\n      }\n\n      if (this.projection_ == getProjection('EPSG:4326')) {\n        // Cesium zoom level 0 is OpenLayers zoom level 1 for layer in EPSG:4326 with a single tile on level 0\n        this.shouldRequestNextLevel = options.numberOfLevelZeroTilesX === 1 && options.numberOfLevelZeroTilesY === 1;\n        this.tilingScheme_ = new Cesium.GeographicTilingScheme(options);\n      } else if (this.projection_ == getProjection('EPSG:3857')) {\n        this.shouldRequestNextLevel = false;\n        this.tilingScheme_ = new Cesium.WebMercatorTilingScheme(options);\n      } else {\n        return;\n      }\n      this.rectangle_ = this.tilingScheme_.rectangle;\n\n      this.ready_ = true;\n    }\n  }\n\n  /**\n   * Generates the proper attributions for a given position and zoom\n   * level.\n   * @export\n   * @override\n   */\n  getTileCredits(x, y, level) {\n    const attributionsFunction = this.source_.getAttributions();\n    if (!attributionsFunction) {\n      return [];\n    }\n    const extent = this.map_.getView().calculateExtent(this.map_.getSize());\n    const center = this.map_.getView().getCenter();\n    const zoom = this.shouldRequestNextLevel ? level + 1 : level;\n    return attributionsFunctionToCredits(attributionsFunction, zoom, center, extent);\n  }\n\n  /**\n   * @export\n   * @override\n   */\n  requestImage(x, y, level) {\n    const tileUrlFunction = this.source_.getTileUrlFunction();\n    if (tileUrlFunction && this.projection_) {\n\n      const z_ = this.shouldRequestNextLevel ? level + 1 : level;\n\n      let y_ = y;\n      if (!olUseNewCoordinates) {\n        // OpenLayers version 3 to 5 tile coordinates increase from bottom to top\n        y_ = -y - 1;\n      }\n      let url = tileUrlFunction.call(this.source_, [z_, x, y_], 1, this.projection_);\n      if (this.proxy_) {\n        url = this.proxy_.getURL(url);\n      }\n      return url ? Cesium.ImageryProvider.loadImage(this, url) : this.emptyCanvas_;\n    } else {\n      // return empty canvas to stop Cesium from retrying later\n      return this.emptyCanvas_;\n    }\n  }\n}\n\n// definitions of getters that are required to be present\n// in the Cesium.ImageryProvider instance:\nObject.defineProperties(OLImageryProvider.prototype, {\n  'ready': {\n    'get': /** @this {olcs.core.OLImageryProvider} */\n        function() {return this.ready_;}\n  },\n\n  'rectangle': {\n    'get': /** @this {olcs.core.OLImageryProvider} */\n        function() {return this.rectangle_;}\n  },\n\n  'tileWidth': {\n    'get': /** @this {olcs.core.OLImageryProvider} */\n        function() {\n          const tg = this.source_.getTileGrid();\n          return tg ? (Array.isArray(tg.getTileSize(0)) ? tg.getTileSize(0)[0] : tg.getTileSize(0)) : 256;\n        }\n  },\n\n  'tileHeight': {\n    'get': /** @this {olcs.core.OLImageryProvider} */\n        function() {\n          const tg = this.source_.getTileGrid();\n          return tg ? (Array.isArray(tg.getTileSize(0)) ? tg.getTileSize(0)[1] : tg.getTileSize(0)) : 256;\n        }\n  },\n\n  'maximumLevel': {\n    'get': /** @this {olcs.core.OLImageryProvider} */\n        function() {\n          const tg = this.source_.getTileGrid();\n          return tg ? tg.getMaxZoom() : 18;\n        }\n  },\n\n  'minimumLevel': {\n    'get': /** @this {olcs.core.OLImageryProvider} */\n        function() {\n          // WARNING: Do not use the minimum level (at least until the extent is\n          // properly set). Cesium assumes the minimumLevel to contain only\n          // a few tiles and tries to load them all at once -- this can\n          // freeze and/or crash the browser !\n          return 0;\n          //var tg = this.source_.getTileGrid();\n          //return tg ? tg.getMinZoom() : 0;\n        }\n  },\n\n  'tilingScheme': {\n    'get': /** @this {olcs.core.OLImageryProvider} */\n        function() {return this.tilingScheme_;}\n  },\n\n  'tileDiscardPolicy': {\n    'get': function() {return undefined;}\n  },\n\n  'errorEvent': {\n    'get': /** @this {olcs.core.OLImageryProvider} */\n        function() {return this.errorEvent_;}\n  },\n\n  'proxy': {\n    'get': /** @this {olcs.core.OLImageryProvider} */\n        function() {return this.proxy_;}\n  },\n\n  'hasAlphaChannel': {\n    'get': function() {return true;}\n  },\n\n  'pickFeatures': {\n    'get': function() {return undefined;}\n  }\n});\n\n\nexport default OLImageryProvider;\n", "/**\n * @module olcs.core.VectorLayerCounterpart\n */\nimport {unByKey as olObservableUnByKey} from 'ol/Observable.js';\n\n\n/**\n * Context for feature conversion.\n * @typedef {Object} OlFeatureToCesiumContext\n * @property {!(import('ol/Projection.js').default|string)} projection\n * @property {!Cesium.PrimitiveCollection} primitives\n * @property {Object<number, Array<!Cesium.Primitive|!Cesium.Billboard>>} featureToCesiumMap\n * @property {!Cesium.BillboardCollection} billboards\n */\n\n\nclass VectorLayerCounterpart {\n  /**\n  * Result of the conversion of an OpenLayers layer to Cesium.\n  * @param {!(ol.proj.Projection|string)} layerProjection\n  * @param {!Cesium.Scene} scene\n  */\n  constructor(layerProjection, scene) {\n    const billboards = new Cesium.BillboardCollection({scene});\n    const primitives = new Cesium.PrimitiveCollection();\n\n    /**\n    * @type {!Array.<ol.EventsKey>}\n    */\n    this.olListenKeys = [];\n\n    this.rootCollection_ = new Cesium.PrimitiveCollection();\n    /**\n    * @type {!OlFeatureToCesiumContext}\n    */\n    this.context = {\n      projection: layerProjection,\n      billboards,\n      featureToCesiumMap: {},\n      primitives\n    };\n\n    this.rootCollection_.add(billboards);\n    this.rootCollection_.add(primitives);\n  }\n\n  /**\n  * Unlisten.\n  */\n  destroy() {\n    this.olListenKeys.forEach(olObservableUnByKey);\n    this.olListenKeys.length = 0;\n  }\n\n  /**\n  * @return {!Cesium.Primitive}\n  */\n  getRootPrimitive() {\n    return this.rootCollection_;\n  }\n}\n\n\nexport default VectorLayerCounterpart;\n", "/**\n * Converts radians to to degrees.\n *\n * @param {number} angleInRadians Angle in radians.\n * @return {number} Angle in degrees.\n */\nexport function toDegrees(angleInRadians) {\n  return angleInRadians * 180 / Math.PI;\n}\n\n\n/**\n * Converts degrees to radians.\n *\n * @param {number} angleInDegrees Angle in degrees.\n * @return {number} Angle in radians.\n */\nexport function toRadians(angleInDegrees) {\n  return angleInDegrees * Math.PI / 180;\n}\n", "/**\n * @module olcs.util\n */\nconst exports = {};\n\n\n/**\n * Cast to object.\n * @param {Object} param\n * @return {Object}\n */\nexports.obj = function(param) {\n  return param;\n};\n\n\n/**\n * @type {boolean|undefined}\n * @private\n */\nexports.supportsImageRenderingPixelatedResult_ = undefined;\n\n\n/**\n * @type {string|undefined}\n * @private\n */\nexports.imageRenderingValueResult_ = undefined;\n\n\n/**\n * @return {boolean}\n */\nexports.supportsImageRenderingPixelated = function() {\n  if (exports.supportsImageRenderingPixelatedResult_ === undefined) {\n    const canvas = document.createElement('canvas');\n    canvas.setAttribute('style', 'image-rendering: -moz-crisp-edges; image-rendering: pixelated;');\n    // canvas.style.imageRendering will be undefined, null or an\n    // empty string on unsupported browsers.\n    const tmp = canvas.style['imageRendering']; // non standard\n    exports.supportsImageRenderingPixelatedResult_ = !!tmp;\n    if (exports.supportsImageRenderingPixelatedResult_) {\n      exports.imageRenderingValueResult_ = tmp;\n    }\n  }\n  return exports.supportsImageRenderingPixelatedResult_;\n};\n\n\n/**\n * @return {string}\n */\nexports.imageRenderingValue = function() {\n  exports.supportsImageRenderingPixelated();\n  return exports.imageRenderingValueResult_ || '';\n};\n\n/**\n * Return the projection of the source that Cesium should use.\n *\n * @param {ol.source.Source} source Source.\n * @returns {ol.proj.Projection} The projection of the source.\n */\nexports.getSourceProjection = function(source) {\n  return /** @type {ol.proj.Projection} */ (source.get('olcs.projection'))\n    || source.getProjection();\n};\n\n/**\n * @param {ol.Observable} observable\n * @param {string} type\n * @param {Function} listener\n * @return {!ol.events.EventsKey}\n */\nexport function olcsListen(observable, type, listener) {\n  // See https://github.com/openlayers/openlayers/pull/8481\n  // ol.events.listen is internal so we use `on` instead.\n  // And since `on` as a convoluted API (can return an EventsKey or an array of them)\n  // we use a cast here.\n  return /** @type {!ol.events.EventsKey} */ (observable.on(type, listener));\n}\n\n/**\n * Counter for getUid.\n * @type {number}\n */\nlet uidCounter_ = 0;\n\n/**\n * Gets a unique ID for an object. This mutates the object so that further calls\n * with the same object as a parameter returns the same value. Unique IDs are generated\n * as a strictly increasing sequence. Adapted from goog.getUid.\n *\n * @param {Object} obj The object to get the unique ID for.\n * @return {number} The unique ID for the object.\n */\nexport function getUid(obj) {\n  return obj.olcs_uid || (obj.olcs_uid = ++uidCounter_);\n}\n\n/**\n * Sort the passed array such that the relative order of equal elements is preverved.\n * See https://en.wikipedia.org/wiki/Sorting_algorithm#Stability for details.\n * @param {Array<*>} arr The array to sort (modifies original).\n * @param {!function(*, *): number} compareFnc Comparison function.\n */\nexport function stableSort(arr, compareFnc) {\n  const length = arr.length;\n  const tmp = Array(arr.length);\n  for (let i = 0; i < length; i++) {\n    tmp[i] = {index: i, value: arr[i]};\n  }\n  tmp.sort((a, b) => compareFnc(a.value, b.value) || a.index - b.index);\n  for (let i = 0; i < arr.length; i++) {\n    arr[i] = tmp[i].value;\n  }\n}\n\n/**\n * @param {Node} node The node to remove.\n * @returns {Node} The node that was removed or null.\n */\nexport function removeNode(node) {\n  return node && node.parentNode ? node.parentNode.removeChild(node) : null;\n}\n\n/**\n * @param {Node} node The node to remove the children from.\n */\nexport function removeChildren(node) {\n  while (node.lastChild) {\n    node.removeChild(node.lastChild);\n  }\n}\n\n/**\n * @param {Cesium.Scene} scene The scene.\n */\nexport function isGroundPolylinePrimitiveSupported(scene) {\n  const obj = Cesium.GroundPolylinePrimitive;\n  return obj && obj.isSupported(scene);\n}\n\nexport default exports;\n", "module.exports = ol.Observable;", "module.exports = ol.Overlay;", "module.exports = ol.easing;", "module.exports = ol.extent;", "module.exports = ol.format.MVT;", "module.exports = ol.geom.Geometry;", "module.exports = ol.geom.Point;", "module.exports = ol.geom.Polygon;", "module.exports = ol.geom.SimpleGeometry;", "module.exports = ol.layer.Group;", "module.exports = ol.layer.Image;", "module.exports = ol.layer.Layer;", "module.exports = ol.layer.Tile;", "module.exports = ol.layer.Vector;", "module.exports = ol.layer.VectorTile;", "module.exports = ol.proj;", "module.exports = ol.render;", "module.exports = ol.source;", "module.exports = ol.source.Cluster;", "module.exports = ol.source.Image;", "module.exports = ol.source.ImageStatic;", "module.exports = ol.source.ImageWMS;", "module.exports = ol.source.TileImage;", "module.exports = ol.source.TileWMS;", "module.exports = ol.source.Vector;", "module.exports = ol.source.VectorTile;", "module.exports = ol.structs.LRUCache;", "module.exports = ol.style.Icon;", "module.exports = ol.style.Stroke;", "module.exports = ol.style.Style;", "module.exports = ol.tilegrid;", "module.exports = ol.tileurlfunction;", "module.exports = ol.util;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import OLCesium from './olcs/OLCesium.js';\n\nimport AbstractSynchronizer from './olcs/AbstractSynchronizer.js';\nimport RasterSynchronizer from './olcs/RasterSynchronizer.js';\nimport VectorSynchronizer from './olcs/VectorSynchronizer.js';\n\nimport * as core from './olcs/core.js';\nimport OLImageryProvider from './olcs/core/OLImageryProvider.js';\nimport VectorLayerCounterpart from './olcs/core/VectorLayerCounterpart.js';\n\nimport LazyLoader from './olcs/contrib/LazyLoader.js';\nimport Manager from './olcs/contrib/Manager.js';\n\n\nexport default OLCesium;\n\n// Using var for phantomJS\n// eslint-disable-next-line no-var\nvar olcs = window['olcs'] = {};\nolcs.OLCesium = OLCesium;\n\nolcs.AbstractSynchronizer = AbstractSynchronizer;\nolcs.RasterSynchronizer = RasterSynchronizer;\nolcs.VectorSynchronizer = VectorSynchronizer;\n\nolcs.core = core.default;\nolcs.core.OLImageryProvider = OLImageryProvider;\nolcs.core.VectorLayerCounterpart = VectorLayerCounterpart;\n\nolcs.contrib = {};\nolcs.contrib.LazyLoader = LazyLoader;\nolcs.contrib.Manager = Manager;\n"], "names": [], "sourceRoot": ""}