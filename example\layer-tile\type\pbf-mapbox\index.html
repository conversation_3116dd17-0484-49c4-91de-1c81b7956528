<!-- 2017-12-4 14:24:10 | 修改 木遥（微信:  http://marsgis.cn/weixin.html ） -->
<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="x5-fullscreen" content="true" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />

    <!-- 标题及搜索关键字 -->
    <meta name="keywords" content="火星科技,cesium,3D,GIS,marsgis,三维,地球,地图,开发,框架,系统,示例,资料,模型,离线,外包,合肥,安徽,中国" />
    <meta
      name="description"
      content="火星科技 合肥火星 合肥火星科技 合肥火星科技有限公司 leaflet leaflet框架 leaflet开发 cesium cesium开发 cesium框架 三维 地球 模型  gis marsgis 地图离线 地图开发 地图框架 地图外包 框架 开发 外包  地图离线 二维地图 三维地图 全景漫游 地理信息系统 云GIS 三维GIS GIS平台 WebGIS"
    />

    <link rel="shortcut icon" type="image/x-icon" href="/img/favicon/favicon.ico" />
    <title>Mapbox | Mars3D | 三维地图 | 火星科技 | 合肥火星科技有限公司</title>

    <script
      type="text/javascript"
      src="../lib/include-lib.js"
      libpath="../lib/"
      include="jquery,font-awesome,bootstrap,layer,localforage,haoutil,mars3d,es5-widget,cesium-pbf-mapbox"
    ></script>

    <link href="css/style.css" rel="stylesheet" />
  </head>

  <body class="dark">
    <div id="mars3dContainer" class="mars3d-container"></div>

    <script src="/js/common.js"></script>
    <script src="./map.js"></script>
    <script type="text/javascript">
      "use script" //开发环境建议开启严格模式

      function initUI() {
        activatePOIQuery(map)
      }

      //附加：激活POI查询widget，用于在底图上叠加显示数据
      function activatePOIQuery(map) {
        es5widget.activate({
          map: map,
          name: "POI查询",
          uri: "widgets/queryPoiBar/widget.js"
        })
      }
    </script>
  </body>
</html>
