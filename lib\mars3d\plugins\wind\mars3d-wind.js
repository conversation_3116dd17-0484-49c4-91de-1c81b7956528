/**
 * Mars3D平台插件,支持气象 风向图 功能插件  mars3d-wind
 *
 * 版本信息：v3.10.3
 * 编译日期：2025-08-17 12:11
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：火星科技免费公开版 ，2025-07-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-wind"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';(function(_0x5a3d31,_0x120cf1){const _0x2a3ddb=_0x5a3d31();function _0xe21acc(_0x102a3e,_0x25e22c){return _0x449a(_0x25e22c-0x21c,_0x102a3e);}function _0x5a9172(_0x2684c3,_0x365a9d){return _0x449a(_0x2684c3-0x1ac,_0x365a9d);}while(!![]){try{const _0xaa660e=parseInt(_0x5a9172(0x3e9,0x379))/0x1+parseInt(_0xe21acc(0x43c,0x43e))/0x2+-parseInt(_0x5a9172(0x3f9,0x44e))/0x3+-parseInt(_0xe21acc(0x379,0x384))/0x4+parseInt(_0xe21acc(0x3b0,0x413))/0x5+-parseInt(_0xe21acc(0x434,0x3ad))/0x6*(-parseInt(_0xe21acc(0x413,0x3e3))/0x7)+-parseInt(_0x5a9172(0x39b,0x423))/0x8*(parseInt(_0xe21acc(0x41f,0x3b6))/0x9);if(_0xaa660e===_0x120cf1)break;else _0x2a3ddb['push'](_0x2a3ddb['shift']());}catch(_0x47e864){_0x2a3ddb['push'](_0x2a3ddb['shift']());}}}(_0x3486,0x8f1c6));function _0x449a(_0x14abe8,_0x208916){const _0x34863c=_0x3486();return _0x449a=function(_0x449a7e,_0x386499){_0x449a7e=_0x449a7e-0x166;let _0x40c5d3=_0x34863c[_0x449a7e];return _0x40c5d3;},_0x449a(_0x14abe8,_0x208916);}function _interopNamespace(_0x204fa4){function _0x10e7cf(_0x47ff1d,_0x5e00a6){return _0x449a(_0x47ff1d-0x272,_0x5e00a6);}if(_0x204fa4&&_0x204fa4['__esModule'])return _0x204fa4;var _0x3318ca=Object[_0x44fa0c(0x498,0x4c6)](null);function _0x44fa0c(_0x4b14ad,_0x414737){return _0x449a(_0x4b14ad-0x305,_0x414737);}return _0x204fa4&&Object['keys'](_0x204fa4)[_0x10e7cf(0x478,0x4d4)](function(_0x152f01){function _0x1a29ea(_0x343971,_0xb8afff){return _0x10e7cf(_0x343971- -0x592,_0xb8afff);}function _0x49686e(_0x364a23,_0x12a545){return _0x10e7cf(_0x364a23-0x7,_0x12a545);}if(_0x152f01!==_0x49686e(0x4e6,0x56a)){var _0x281548=Object[_0x1a29ea(-0x194,-0x20f)](_0x204fa4,_0x152f01);Object['defineProperty'](_0x3318ca,_0x152f01,_0x281548['get']?_0x281548:{'enumerable':!![],'get':function(){return _0x204fa4[_0x152f01];}});}}),_0x3318ca['default']=_0x204fa4,_0x3318ca;}var mars3d__namespace=_interopNamespace(mars3d);const Cesium$2=mars3d__namespace['Cesium'];function getU(_0x38aa6d,_0x38a8da){const _0xf39cfa=_0x38aa6d*Math[_0x36f3c1(0x36c,0x3e5)](Cesium$2[_0x52b341(0x20d,0x20c)][_0x52b341(0x1e1,0x201)](_0x38a8da));function _0x52b341(_0x4b05a8,_0x24d542){return _0x449a(_0x24d542-0x4,_0x4b05a8);}function _0x36f3c1(_0x1b8e8b,_0x33494d){return _0x449a(_0x1b8e8b-0x112,_0x33494d);}return _0xf39cfa;}function getV(_0x24bb83,_0x5032d3){function _0x387a11(_0x3f7cd5,_0x2bde6){return _0x449a(_0x2bde6-0x1e9,_0x3f7cd5);}const _0x39dd59=_0x24bb83*Math['sin'](Cesium$2['Math'][_0x387a11(0x436,0x3e6)](_0x5032d3));return _0x39dd59;}function getSpeed(_0x4b228f,_0x265a26){const _0xeaa199=Math['sqrt'](Math['pow'](_0x4b228f,0x2)+Math[_0x35e7b1(0x340,0x363)](_0x265a26,0x2));function _0x35e7b1(_0x38da00,_0x285d8d){return _0x449a(_0x285d8d-0x18d,_0x38da00);}return _0xeaa199;}function getDirection(_0x2367fe,_0x1db358){let _0x3baa5f=Cesium$2['Math'][_0x74d708(0x484,0x440)](Math['atan2'](_0x1db358,_0x2367fe));_0x3baa5f+=_0x3baa5f<0x0?0x168:0x0;function _0x74d708(_0x22074a,_0x22e2c2){return _0x449a(_0x22074a-0x212,_0x22e2c2);}return _0x3baa5f;}const _0x3b4216={};_0x3b4216['__proto__']=null,_0x3b4216['getU']=getU,_0x3b4216[_0x130459(0x1c5,0x183)]=getV,_0x3b4216['getSpeed']=getSpeed,_0x3b4216['getDirection']=getDirection;var WindUtil=_0x3b4216,updatePositionShader='#version\x20300\x20es\x0aprecision\x20highp\x20float;\x0a\x0auniform\x20sampler2D\x20currentParticlesPosition;\x0auniform\x20sampler2D\x20particlesSpeed;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0aout\x20vec4\x20fragColor;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20获取当前粒子的位置\x0a\x20\x20vec2\x20currentPos\x20=\x20texture(currentParticlesPosition,\x20v_textureCoordinates).rg;\x0a\x20\x20\x20\x20//\x20获取粒子的速度\x0a\x20\x20vec2\x20speed\x20=\x20texture(particlesSpeed,\x20v_textureCoordinates).rg;\x0a\x20\x20\x20\x20//\x20计算下一个位置\x0a\x20\x20vec2\x20nextPos\x20=\x20currentPos\x20+\x20speed;\x0a\x0a\x20\x20\x20\x20//\x20将新的位置写入\x20fragColor\x0a\x20\x20fragColor\x20=\x20vec4(nextPos,\x200.0f,\x201.0f);\x0a}\x0a',calculateSpeedShader='#version\x20300\x20es\x0a\x0a//\x20the\x20size\x20of\x20UV\x20textures:\x20width\x20=\x20lon,\x20height\x20=\x20lat\x0auniform\x20sampler2D\x20U;\x20//\x20eastward\x20wind\x0auniform\x20sampler2D\x20V;\x20//\x20northward\x20wind\x0auniform\x20sampler2D\x20currentParticlesPosition;\x20//\x20(lon,\x20lat,\x20lev)\x0a\x0auniform\x20vec2\x20uRange;\x20//\x20(min,\x20max)\x0auniform\x20vec2\x20vRange;\x20//\x20(min,\x20max)\x0auniform\x20vec2\x20speedRange;\x20//\x20(min,\x20max)\x0auniform\x20vec2\x20dimension;\x20//\x20(lon,\x20lat)\x0auniform\x20vec2\x20minimum;\x20//\x20minimum\x20of\x20each\x20dimension\x0auniform\x20vec2\x20maximum;\x20//\x20maximum\x20of\x20each\x20dimension\x0a\x0auniform\x20float\x20speedScaleFactor;\x0auniform\x20float\x20frameRateAdjustment;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0avec2\x20getInterval(vec2\x20maximum,\x20vec2\x20minimum,\x20vec2\x20dimension)\x20{\x0a\x20\x20return\x20(maximum\x20-\x20minimum)\x20/\x20(dimension\x20-\x201.0f);\x0a}\x0a\x0avec2\x20mapPositionToNormalizedIndex2D(vec2\x20lonLat)\x20{\x0a\x20\x20\x20\x20//\x20ensure\x20the\x20range\x20of\x20longitude\x20and\x20latitude\x0a\x20\x20lonLat.x\x20=\x20clamp(lonLat.x,\x20minimum.x,\x20maximum.x);\x0a\x20\x20lonLat.y\x20=\x20clamp(lonLat.y,\x20minimum.y,\x20maximum.y);\x0a\x0a\x20\x20vec2\x20interval\x20=\x20getInterval(maximum,\x20minimum,\x20dimension);\x0a\x0a\x20\x20vec2\x20index2D\x20=\x20vec2(0.0f);\x0a\x20\x20index2D.x\x20=\x20(lonLat.x\x20-\x20minimum.x)\x20/\x20interval.x;\x0a\x20\x20index2D.y\x20=\x20(lonLat.y\x20-\x20minimum.y)\x20/\x20interval.y;\x0a\x0a\x20\x20vec2\x20normalizedIndex2D\x20=\x20vec2(index2D.x\x20/\x20dimension.x,\x20index2D.y\x20/\x20dimension.y);\x0a\x20\x20return\x20normalizedIndex2D;\x0a}\x0a\x0afloat\x20getWindComponent(sampler2D\x20componentTexture,\x20vec2\x20lonLat)\x20{\x0a\x20\x20vec2\x20normalizedIndex2D\x20=\x20mapPositionToNormalizedIndex2D(lonLat);\x0a\x20\x20float\x20result\x20=\x20texture(componentTexture,\x20normalizedIndex2D).r;\x0a\x20\x20return\x20result;\x0a}\x0a\x0avec2\x20getWindComponents(vec2\x20lonLat)\x20{\x0a\x20\x20vec2\x20normalizedIndex2D\x20=\x20mapPositionToNormalizedIndex2D(lonLat);\x0a\x20\x20float\x20u\x20=\x20texture(U,\x20normalizedIndex2D).r;\x0a\x20\x20float\x20v\x20=\x20texture(V,\x20normalizedIndex2D).r;\x0a\x20\x20return\x20vec2(u,\x20v);\x0a}\x0a\x0avec2\x20bilinearInterpolation(vec2\x20lonLat)\x20{\x0a\x20\x20float\x20lon\x20=\x20lonLat.x;\x0a\x20\x20float\x20lat\x20=\x20lonLat.y;\x0a\x0a\x20\x20vec2\x20interval\x20=\x20getInterval(maximum,\x20minimum,\x20dimension);\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20grid\x20cell\x20coordinates\x0a\x20\x20float\x20lon0\x20=\x20floor(lon\x20/\x20interval.x)\x20*\x20interval.x;\x0a\x20\x20float\x20lon1\x20=\x20lon0\x20+\x20interval.x;\x0a\x20\x20float\x20lat0\x20=\x20floor(lat\x20/\x20interval.y)\x20*\x20interval.y;\x0a\x20\x20float\x20lat1\x20=\x20lat0\x20+\x20interval.y;\x0a\x0a\x20\x20\x20\x20//\x20Get\x20wind\x20vectors\x20at\x20four\x20corners\x0a\x20\x20vec2\x20v00\x20=\x20getWindComponents(vec2(lon0,\x20lat0));\x0a\x20\x20vec2\x20v10\x20=\x20getWindComponents(vec2(lon1,\x20lat0));\x0a\x20\x20vec2\x20v01\x20=\x20getWindComponents(vec2(lon0,\x20lat1));\x0a\x20\x20vec2\x20v11\x20=\x20getWindComponents(vec2(lon1,\x20lat1));\x0a\x0a\x20\x20\x20\x20//\x20Check\x20if\x20all\x20wind\x20vectors\x20are\x20zero\x0a\x20\x20if(length(v00)\x20==\x200.0f\x20&&\x20length(v10)\x20==\x200.0f\x20&&\x20length(v01)\x20==\x200.0f\x20&&\x20length(v11)\x20==\x200.0f)\x20{\x0a\x20\x20\x20\x20return\x20vec2(0.0f,\x200.0f);\x0a\x20\x20}\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20interpolation\x20weights\x0a\x20\x20float\x20s\x20=\x20(lon\x20-\x20lon0)\x20/\x20interval.x;\x0a\x20\x20float\x20t\x20=\x20(lat\x20-\x20lat0)\x20/\x20interval.y;\x0a\x0a\x20\x20\x20\x20//\x20Perform\x20bilinear\x20interpolation\x20on\x20vector\x20components\x0a\x20\x20vec2\x20v0\x20=\x20mix(v00,\x20v10,\x20s);\x0a\x20\x20vec2\x20v1\x20=\x20mix(v01,\x20v11,\x20s);\x0a\x20\x20return\x20mix(v0,\x20v1,\x20t);\x0a}\x0a\x0avec2\x20lengthOfLonLat(vec2\x20lonLat)\x20{\x0a\x20\x20\x20\x20//\x20unit\x20conversion:\x20meters\x20->\x20longitude\x20latitude\x20degrees\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Geographic_coordinate_system#Length_of_a_degree\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20the\x20length\x20of\x20a\x20degree\x20of\x20latitude\x20and\x20longitude\x20in\x20meters\x0a\x20\x20float\x20latitude\x20=\x20radians(lonLat.y);\x0a\x0a\x20\x20float\x20term1\x20=\x20111132.92f;\x0a\x20\x20float\x20term2\x20=\x20559.82f\x20*\x20cos(2.0f\x20*\x20latitude);\x0a\x20\x20float\x20term3\x20=\x201.175f\x20*\x20cos(4.0f\x20*\x20latitude);\x0a\x20\x20float\x20term4\x20=\x200.0023f\x20*\x20cos(6.0f\x20*\x20latitude);\x0a\x20\x20float\x20latLength\x20=\x20term1\x20-\x20term2\x20+\x20term3\x20-\x20term4;\x0a\x0a\x20\x20float\x20term5\x20=\x20111412.84f\x20*\x20cos(latitude);\x0a\x20\x20float\x20term6\x20=\x2093.5f\x20*\x20cos(3.0f\x20*\x20latitude);\x0a\x20\x20float\x20term7\x20=\x200.118f\x20*\x20cos(5.0f\x20*\x20latitude);\x0a\x20\x20float\x20longLength\x20=\x20term5\x20-\x20term6\x20+\x20term7;\x0a\x0a\x20\x20return\x20vec2(longLength,\x20latLength);\x0a}\x0a\x0avec2\x20convertSpeedUnitToLonLat(vec2\x20lonLat,\x20vec2\x20speed)\x20{\x0a\x20\x20vec2\x20lonLatLength\x20=\x20lengthOfLonLat(lonLat);\x0a\x20\x20float\x20u\x20=\x20speed.x\x20/\x20lonLatLength.x;\x0a\x20\x20float\x20v\x20=\x20speed.y\x20/\x20lonLatLength.y;\x0a\x20\x20vec2\x20windVectorInLonLat\x20=\x20vec2(u,\x20v);\x0a\x0a\x20\x20return\x20windVectorInLonLat;\x0a}\x0a\x0avec2\x20calculateSpeedByRungeKutta2(vec2\x20lonLat)\x20{\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Runge%E2%80%93Kutta_methods#Second-order_methods_with_two_stages\x20for\x20detail\x0a\x20\x20const\x20float\x20h\x20=\x200.5f;\x0a\x0a\x20\x20vec2\x20y_n\x20=\x20lonLat;\x0a\x20\x20vec2\x20f_n\x20=\x20bilinearInterpolation(lonLat);\x0a\x20\x20vec2\x20midpoint\x20=\x20y_n\x20+\x200.5f\x20*\x20h\x20*\x20convertSpeedUnitToLonLat(y_n,\x20f_n)\x20*\x20speedScaleFactor;\x0a\x20\x20vec2\x20speed\x20=\x20h\x20*\x20bilinearInterpolation(midpoint)\x20*\x20speedScaleFactor;\x0a\x0a\x20\x20return\x20speed;\x0a}\x0a\x0avec2\x20calculateWindNorm(vec2\x20speed)\x20{\x0a\x20\x20float\x20speedLength\x20=\x20length(speed.xy);\x0a\x20\x20if(speedLength\x20==\x200.0f)\x20{\x0a\x20\x20\x20\x20return\x20vec2(0.0f);\x0a\x20\x20}\x0a\x0a\x20\x20\x20\x20//\x20Clamp\x20speedLength\x20to\x20range\x0a\x20\x20float\x20clampedSpeed\x20=\x20clamp(speedLength,\x20speedRange.x,\x20speedRange.y);\x0a\x20\x20float\x20normalizedSpeed\x20=\x20(clampedSpeed\x20-\x20speedRange.x)\x20/\x20(speedRange.y\x20-\x20speedRange.x);\x0a\x20\x20return\x20vec2(speedLength,\x20normalizedSpeed);\x0a}\x0a\x0aout\x20vec4\x20fragColor;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20vec2\x20lonLat\x20=\x20texture(currentParticlesPosition,\x20v_textureCoordinates).rg;\x0a\x20\x20vec2\x20speedOrigin\x20=\x20bilinearInterpolation(lonLat);\x0a\x20\x20vec2\x20speed\x20=\x20calculateSpeedByRungeKutta2(lonLat)\x20*\x20frameRateAdjustment;\x0a\x20\x20vec2\x20speedInLonLat\x20=\x20convertSpeedUnitToLonLat(lonLat,\x20speed);\x0a\x0a\x20\x20fragColor\x20=\x20vec4(speedInLonLat,\x20calculateWindNorm(speedOrigin));\x0a}\x0a',postProcessingPositionFragmentShader='#version\x20300\x20es\x0aprecision\x20highp\x20float;\x0a\x0auniform\x20sampler2D\x20nextParticlesPosition;\x0auniform\x20sampler2D\x20particlesSpeed;\x20//\x20(u,\x20v,\x20norm)\x0a\x0a//\x20range\x20(min,\x20max)\x0auniform\x20vec2\x20lonRange;\x0auniform\x20vec2\x20latRange;\x0a\x0a//\x20range\x20(min,\x20max)\x0auniform\x20vec2\x20dataLonRange;\x0auniform\x20vec2\x20dataLatRange;\x0a\x0auniform\x20float\x20randomCoefficient;\x0auniform\x20float\x20dropRate;\x0auniform\x20float\x20dropRateBump;\x0a\x0a//\x20添加新的\x20uniform\x20变量\x0auniform\x20bool\x20useViewerBounds;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0a//\x20pseudo-random\x20generator\x0aconst\x20vec3\x20randomConstants\x20=\x20vec3(12.9898f,\x2078.233f,\x204375.85453f);\x0aconst\x20vec2\x20normalRange\x20=\x20vec2(0.0f,\x201.0f);\x0afloat\x20rand(vec2\x20seed,\x20vec2\x20range)\x20{\x0a\x20\x20vec2\x20randomSeed\x20=\x20randomCoefficient\x20*\x20seed;\x0a\x20\x20float\x20temp\x20=\x20dot(randomConstants.xy,\x20randomSeed);\x0a\x20\x20temp\x20=\x20fract(sin(temp)\x20*\x20(randomConstants.z\x20+\x20temp));\x0a\x20\x20return\x20temp\x20*\x20(range.y\x20-\x20range.x)\x20+\x20range.x;\x0a}\x0a\x0avec2\x20generateRandomParticle(vec2\x20seed)\x20{\x0a\x20\x20vec2\x20range;\x0a\x20\x20float\x20randomLon,\x20randomLat;\x0a\x0a\x20\x20if(useViewerBounds)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20在当前视域范围内生成粒子\x0a\x20\x20\x20\x20randomLon\x20=\x20rand(seed,\x20lonRange);\x0a\x20\x20\x20\x20randomLat\x20=\x20rand(-seed,\x20latRange);\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20在数据范围内生成粒子\x0a\x20\x20\x20\x20randomLon\x20=\x20rand(seed,\x20dataLonRange);\x0a\x20\x20\x20\x20randomLat\x20=\x20rand(-seed,\x20dataLatRange);\x0a\x20\x20}\x0a\x0a\x20\x20return\x20vec2(randomLon,\x20randomLat);\x0a}\x0a\x0abool\x20particleOutbound(vec2\x20particle)\x20{\x0a\x20\x20return\x20particle.y\x20<\x20dataLatRange.x\x20||\x20particle.y\x20>\x20dataLatRange.y\x20||\x20particle.x\x20<\x20dataLonRange.x\x20||\x20particle.x\x20>\x20dataLonRange.y;\x0a}\x0a\x0aout\x20vec4\x20fragColor;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20vec2\x20nextParticle\x20=\x20texture(nextParticlesPosition,\x20v_textureCoordinates).rg;\x0a\x20\x20vec4\x20nextSpeed\x20=\x20texture(particlesSpeed,\x20v_textureCoordinates);\x0a\x20\x20float\x20speedNorm\x20=\x20nextSpeed.a;\x0a\x20\x20float\x20particleDropRate\x20=\x20dropRate\x20+\x20dropRateBump\x20*\x20speedNorm;\x0a\x0a\x20\x20vec2\x20seed1\x20=\x20nextParticle.xy\x20+\x20v_textureCoordinates;\x0a\x20\x20vec2\x20seed2\x20=\x20nextSpeed.rg\x20+\x20v_textureCoordinates;\x0a\x20\x20vec2\x20randomParticle\x20=\x20generateRandomParticle(seed1);\x0a\x20\x20float\x20randomNumber\x20=\x20rand(seed2,\x20normalRange);\x0a\x0a\x20\x20if(randomNumber\x20<\x20particleDropRate\x20||\x20particleOutbound(nextParticle))\x20{\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(randomParticle,\x200.0f,\x201.0f);\x20//\x201.0\x20means\x20this\x20is\x20a\x20random\x20particle\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(nextParticle,\x200.0f,\x200.0f);\x0a\x20\x20}\x0a}\x0a',renderParticlesFragmentShader='#version\x20300\x20es\x0aprecision\x20highp\x20float;\x0a\x0ain\x20vec4\x20speed;\x0ain\x20float\x20v_segmentPosition;\x0ain\x20vec2\x20textureCoordinate;\x0a\x0auniform\x20vec2\x20domain;\x0auniform\x20vec2\x20displayRange;\x0auniform\x20sampler2D\x20colorTable;\x0auniform\x20sampler2D\x20segmentsDepthTexture;\x0a\x0aout\x20vec4\x20fragColor;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20const\x20float\x20zero\x20=\x200.0f;\x0a\x20\x20if(speed.a\x20>\x20zero\x20&&\x20speed.b\x20>\x20displayRange.x\x20&&\x20speed.b\x20<\x20displayRange.y)\x20{\x0a\x20\x20\x20\x20float\x20speedLength\x20=\x20clamp(speed.b,\x20domain.x,\x20domain.y);\x0a\x20\x20\x20\x20float\x20normalizedSpeed\x20=\x20(speedLength\x20-\x20domain.x)\x20/\x20(domain.y\x20-\x20domain.x);\x0a\x20\x20\x20\x20vec4\x20baseColor\x20=\x20texture(colorTable,\x20vec2(normalizedSpeed,\x20zero));\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20使用更平滑的渐变效果\x0a\x20\x20\x20\x20float\x20alpha\x20=\x20smoothstep(0.0f,\x201.0f,\x20v_segmentPosition);\x0a\x20\x20\x20\x20alpha\x20=\x20pow(alpha,\x201.5f);\x20//\x20调整透明度渐变曲线\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20根据速度调整透明度\x0a\x20\x20\x20\x20float\x20speedAlpha\x20=\x20mix(0.3f,\x201.0f,\x20speed.a);\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20组合颜色和透明度\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(baseColor.rgb,\x20baseColor.a\x20*\x20alpha\x20*\x20speedAlpha);\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(zero);\x0a\x20\x20}\x0a\x0a\x20\x20float\x20segmentsDepth\x20=\x20texture(segmentsDepthTexture,\x20textureCoordinate).r;\x0a\x20\x20float\x20globeDepth\x20=\x20czm_unpackDepth(texture(czm_globeDepthTexture,\x20textureCoordinate));\x0a\x20\x20if(segmentsDepth\x20<\x20globeDepth)\x20{\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(zero);\x0a\x20\x20}\x0a}\x0a',renderParticlesVertexShader='#version\x20300\x20es\x0aprecision\x20highp\x20float;\x0a\x0ain\x20vec2\x20st;\x0ain\x20vec3\x20normal;\x0a\x0auniform\x20sampler2D\x20previousParticlesPosition;\x0auniform\x20sampler2D\x20currentParticlesPosition;\x0auniform\x20sampler2D\x20postProcessingPosition;\x0auniform\x20sampler2D\x20particlesSpeed;\x0a\x0auniform\x20float\x20frameRateAdjustment;\x0auniform\x20float\x20particleHeight;\x0auniform\x20float\x20aspect;\x0auniform\x20float\x20pixelSize;\x0auniform\x20vec2\x20lineWidth;\x0auniform\x20vec2\x20lineLength;\x0auniform\x20vec2\x20domain;\x0auniform\x20bool\x20is3D;\x0a\x0a//\x20添加输出变量传递给片元着色器\x0aout\x20vec4\x20speed;\x0aout\x20float\x20v_segmentPosition;\x0aout\x20vec2\x20textureCoordinate;\x0a\x0a//\x20添加结构体定义\x0astruct\x20adjacentPoints\x20{\x0a\x20\x20vec4\x20previous;\x0a\x20\x20vec4\x20current;\x0a\x20\x20vec4\x20next;\x0a};\x0a\x0avec3\x20convertCoordinate(vec2\x20lonLat)\x20{\x0a\x20\x20\x20\x20//\x20WGS84\x20(lon,\x20lat,\x20lev)\x20->\x20ECEF\x20(x,\x20y,\x20z)\x0a\x20\x20\x20\x20//\x20read\x20https://en.wikipedia.org/wiki/Geographic_coordinate_conversion#From_geodetic_to_ECEF_coordinates\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20WGS\x2084\x20geometric\x20constants\x0a\x20\x20float\x20a\x20=\x206378137.0f;\x20//\x20Semi-major\x20axis\x0a\x20\x20float\x20b\x20=\x206356752.3142f;\x20//\x20Semi-minor\x20axis\x0a\x20\x20float\x20e2\x20=\x206.69437999014e-3f;\x20//\x20First\x20eccentricity\x20squared\x0a\x0a\x20\x20float\x20latitude\x20=\x20radians(lonLat.y);\x0a\x20\x20float\x20longitude\x20=\x20radians(lonLat.x);\x0a\x0a\x20\x20float\x20cosLat\x20=\x20cos(latitude);\x0a\x20\x20float\x20sinLat\x20=\x20sin(latitude);\x0a\x20\x20float\x20cosLon\x20=\x20cos(longitude);\x0a\x20\x20float\x20sinLon\x20=\x20sin(longitude);\x0a\x0a\x20\x20float\x20N_Phi\x20=\x20a\x20/\x20sqrt(1.0f\x20-\x20e2\x20*\x20sinLat\x20*\x20sinLat);\x0a\x20\x20float\x20h\x20=\x20particleHeight;\x20//\x20it\x20should\x20be\x20high\x20enough\x20otherwise\x20the\x20particle\x20may\x20not\x20pass\x20the\x20terrain\x20depth\x20test\x0a\x20\x20vec3\x20cartesian\x20=\x20vec3(0.0f);\x0a\x20\x20cartesian.x\x20=\x20(N_Phi\x20+\x20h)\x20*\x20cosLat\x20*\x20cosLon;\x0a\x20\x20cartesian.y\x20=\x20(N_Phi\x20+\x20h)\x20*\x20cosLat\x20*\x20sinLon;\x0a\x20\x20cartesian.z\x20=\x20((b\x20*\x20b)\x20/\x20(a\x20*\x20a)\x20*\x20N_Phi\x20+\x20h)\x20*\x20sinLat;\x0a\x20\x20return\x20cartesian;\x0a}\x0a\x0avec4\x20calculateProjectedCoordinate(vec2\x20lonLat)\x20{\x0a\x20\x20if(is3D)\x20{\x0a\x20\x20\x20\x20vec3\x20particlePosition\x20=\x20convertCoordinate(lonLat);\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20使用\x20modelViewProjection\x20矩阵进行投影变换\x0a\x20\x20\x20\x20vec4\x20projectedPosition\x20=\x20czm_modelViewProjection\x20*\x20vec4(particlePosition,\x201.0f);\x0a\x20\x20\x20\x20return\x20projectedPosition;\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20vec3\x20position2D\x20=\x20vec3(radians(lonLat.x),\x20radians(lonLat.y),\x200.0f);\x0a\x20\x20\x20\x20return\x20czm_modelViewProjection\x20*\x20vec4(position2D,\x201.0f);\x0a\x20\x20}\x0a}\x0a\x0avec4\x20calculateOffsetOnNormalDirection(vec4\x20pointA,\x20vec4\x20pointB,\x20float\x20offsetSign,\x20float\x20widthFactor)\x20{\x0a\x20\x20vec2\x20aspectVec2\x20=\x20vec2(aspect,\x201.0f);\x0a\x20\x20vec2\x20pointA_XY\x20=\x20(pointA.xy\x20/\x20pointA.w)\x20*\x20aspectVec2;\x0a\x20\x20vec2\x20pointB_XY\x20=\x20(pointB.xy\x20/\x20pointB.w)\x20*\x20aspectVec2;\x0a\x0a\x20\x20\x20\x20//\x20计算方向向量\x0a\x20\x20vec2\x20direction\x20=\x20normalize(pointB_XY\x20-\x20pointA_XY);\x0a\x0a\x20\x20\x20\x20//\x20计算法向量\x0a\x20\x20vec2\x20normalVector\x20=\x20vec2(-direction.y,\x20direction.x);\x0a\x20\x20normalVector.x\x20=\x20normalVector.x\x20/\x20aspect;\x0a\x0a\x20\x20\x20\x20//\x20使用\x20widthFactor\x20调整宽度\x0a\x20\x20float\x20offsetLength\x20=\x20widthFactor\x20*\x20lineWidth.y;\x0a\x20\x20normalVector\x20=\x20offsetLength\x20*\x20normalVector;\x0a\x0a\x20\x20vec4\x20offset\x20=\x20vec4(offsetSign\x20*\x20normalVector,\x200.0f,\x200.0f);\x0a\x20\x20return\x20offset;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20翻转\x20Y\x20轴坐标\x0a\x20\x20vec2\x20flippedIndex\x20=\x20vec2(st.x,\x201.0f\x20-\x20st.y);\x0a\x0a\x20\x20vec2\x20particleIndex\x20=\x20flippedIndex;\x0a\x20\x20speed\x20=\x20texture(particlesSpeed,\x20particleIndex);\x0a\x0a\x20\x20vec2\x20previousPosition\x20=\x20texture(previousParticlesPosition,\x20particleIndex).rg;\x0a\x20\x20vec2\x20currentPosition\x20=\x20texture(currentParticlesPosition,\x20particleIndex).rg;\x0a\x20\x20vec2\x20nextPosition\x20=\x20texture(postProcessingPosition,\x20particleIndex).rg;\x0a\x0a\x20\x20float\x20isAnyRandomPointUsed\x20=\x20texture(postProcessingPosition,\x20particleIndex).a\x20+\x0a\x20\x20\x20\x20texture(currentParticlesPosition,\x20particleIndex).a\x20+\x0a\x20\x20\x20\x20texture(previousParticlesPosition,\x20particleIndex).a;\x0a\x0a\x20\x20adjacentPoints\x20projectedCoordinates;\x0a\x20\x20if(isAnyRandomPointUsed\x20>\x200.0f)\x20{\x0a\x20\x20\x20\x20projectedCoordinates.previous\x20=\x20calculateProjectedCoordinate(previousPosition);\x0a\x20\x20\x20\x20projectedCoordinates.current\x20=\x20projectedCoordinates.previous;\x0a\x20\x20\x20\x20projectedCoordinates.next\x20=\x20projectedCoordinates.previous;\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20projectedCoordinates.previous\x20=\x20calculateProjectedCoordinate(previousPosition);\x0a\x20\x20\x20\x20projectedCoordinates.current\x20=\x20calculateProjectedCoordinate(currentPosition);\x0a\x20\x20\x20\x20projectedCoordinates.next\x20=\x20calculateProjectedCoordinate(nextPosition);\x0a\x20\x20}\x0a\x0a\x20\x20int\x20pointToUse\x20=\x20int(normal.x);\x0a\x20\x20float\x20offsetSign\x20=\x20normal.y;\x0a\x20\x20vec4\x20offset\x20=\x20vec4(0.0f);\x0a\x0a\x20\x20\x20\x20//\x20计算速度相关的宽度和长度因子\x0a\x20\x20float\x20speedLength\x20=\x20clamp(speed.b,\x20domain.x,\x20domain.y);\x0a\x20\x20float\x20normalizedSpeed\x20=\x20(speedLength\x20-\x20domain.x)\x20/\x20(domain.y\x20-\x20domain.x);\x0a\x0a\x20\x20\x20\x20//\x20根据速度计算宽度\x0a\x20\x20float\x20widthFactor\x20=\x20mix(lineWidth.x,\x20lineWidth.y,\x20normalizedSpeed);\x0a\x20\x20widthFactor\x20*=\x20(pointToUse\x20<\x200\x20?\x201.0f\x20:\x200.5f);\x20//\x20头部更宽，尾部更窄\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20length\x20based\x20on\x20speed\x0a\x20\x20float\x20lengthFactor\x20=\x20mix(lineLength.x,\x20lineLength.y,\x20normalizedSpeed)\x20*\x20pixelSize;\x0a\x0a\x20\x20if(pointToUse\x20==\x201)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20头部位置\x0a\x20\x20\x20\x20offset\x20=\x20pixelSize\x20*\x20calculateOffsetOnNormalDirection(projectedCoordinates.previous,\x20projectedCoordinates.current,\x20offsetSign,\x20widthFactor);\x0a\x20\x20\x20\x20gl_Position\x20=\x20projectedCoordinates.previous\x20+\x20offset;\x0a\x20\x20\x20\x20v_segmentPosition\x20=\x200.0f;\x20//\x20头部\x0a\x20\x20}\x20else\x20if(pointToUse\x20==\x20-1)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20Get\x20direction\x20and\x20normalize\x20it\x20to\x20length\x201.0\x0a\x20\x20\x20\x20vec4\x20direction\x20=\x20normalize(projectedCoordinates.next\x20-\x20projectedCoordinates.current);\x0a\x20\x20\x20\x20vec4\x20extendedPosition\x20=\x20projectedCoordinates.current\x20+\x20direction\x20*\x20lengthFactor;\x0a\x0a\x20\x20\x20\x20offset\x20=\x20pixelSize\x20*\x20calculateOffsetOnNormalDirection(projectedCoordinates.current,\x20extendedPosition,\x20offsetSign,\x20widthFactor);\x0a\x20\x20\x20\x20gl_Position\x20=\x20extendedPosition\x20+\x20offset;\x0a\x20\x20\x20\x20v_segmentPosition\x20=\x201.0f;\x20//\x20尾部\x0a\x20\x20}\x0a\x0a\x20\x20textureCoordinate\x20=\x20st;\x0a}\x0a';const {ShaderSource:ShaderSource$1}=mars3d__namespace['Cesium'];class ShaderManager{static['getCalculateSpeedShader'](){const _0xe084ff={};function _0x19310d(_0x5febab,_0x114d48){return _0x130459(_0x5febab-0x6f,_0x114d48);}return _0xe084ff[_0x19310d(0x2dd,0x282)]=[calculateSpeedShader],new ShaderSource$1(_0xe084ff);}static['getUpdatePositionShader'](){const _0x2679a7={};function _0x29d1fc(_0x3467cb,_0x2e5a97){return _0x130459(_0x2e5a97-0x3a5,_0x3467cb);}return _0x2679a7[_0x29d1fc(0x5f8,0x613)]=[updatePositionShader],new ShaderSource$1(_0x2679a7);}static['getSegmentDrawVertexShader'](){const _0x1fe343={};return _0x1fe343['sources']=[renderParticlesVertexShader],new ShaderSource$1(_0x1fe343);}static['getSegmentDrawFragmentShader'](){const _0x13f50f={};return _0x13f50f['sources']=[renderParticlesFragmentShader],new ShaderSource$1(_0x13f50f);}static['getPostProcessingPositionShader'](){const _0x2799df={};_0x2799df[_0x3efeaf(0x4eb,0x48d)]=[postProcessingPositionFragmentShader];function _0x3efeaf(_0x4ab1c2,_0x508124){return _0x130459(_0x508124-0x21f,_0x4ab1c2);}return new ShaderSource$1(_0x2799df);}}const {BufferUsage:BufferUsage$1,ClearCommand:ClearCommand$1,Color:Color$2,ComputeCommand,DrawCommand,Geometry:Geometry$1,Matrix4,Pass:Pass$1,PrimitiveType:PrimitiveType$1,RenderState,ShaderProgram,ShaderSource,VertexArray:VertexArray$1,defined,destroyObject}=mars3d__namespace[_0x2c5470(0x59e,0x5d4)];class CustomPrimitive{constructor(_0x40adb2){this[_0x4c7458(0x2ee,0x2e7)]=_0x40adb2['commandType'],this[_0x2cb33b(0x33b,0x324)]=_0x40adb2[_0x4c7458(0x3ce,0x39d)],this['attributeLocations']=_0x40adb2['attributeLocations'],this['primitiveType']=_0x40adb2['primitiveType'];function _0x2cb33b(_0x17fdcf,_0xa053db){return _0x2c5470(_0xa053db- -0x329,_0x17fdcf);}this['uniformMap']=_0x40adb2['uniformMap']||{},this['vertexShaderSource']=_0x40adb2['vertexShaderSource'],this['fragmentShaderSource']=_0x40adb2['fragmentShaderSource'],this[_0x2cb33b(0x2d5,0x2f8)]=_0x40adb2[_0x2cb33b(0x2b2,0x2f8)],this['framebuffer']=_0x40adb2['framebuffer'],this['outputTexture']=_0x40adb2['outputTexture'],this[_0x2cb33b(0x25a,0x2db)]=_0x40adb2['autoClear']??![],this[_0x2cb33b(0x23c,0x289)]=_0x40adb2[_0x4c7458(0x333,0x30a)],this[_0x4c7458(0x348,0x39a)]=!![],this[_0x2cb33b(0x34a,0x2f9)]=undefined,this['clearCommand']=undefined;function _0x4c7458(_0x1e2643,_0x4d3fce){return _0x2c5470(_0x1e2643- -0x27f,_0x4d3fce);}this['isDynamic']=_0x40adb2['isDynamic']??(()=>!![]),this['autoClear']&&(this['clearCommand']=new ClearCommand$1({'color':new Color$2(0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':this[_0x4c7458(0x324,0x307)],'pass':Pass$1['OPAQUE']}));}['createCommand'](_0x26ef71){function _0x7b615(_0x4eb69c,_0x28c9c2){return _0x130459(_0x28c9c2-0x362,_0x4eb69c);}function _0x2769ce(_0x1da064,_0xc4ab2){return _0x2c5470(_0x1da064- -0x48,_0xc4ab2);}if(this['commandType']==='Draw'){const _0x5b256f={};_0x5b256f['context']=_0x26ef71,_0x5b256f['geometry']=this['geometry'],_0x5b256f['attributeLocations']=this[_0x7b615(0x66d,0x5fa)],_0x5b256f[_0x2769ce(0x51a,0x491)]=BufferUsage$1['STATIC_DRAW'];const _0x2e4896=VertexArray$1['fromGeometry'](_0x5b256f),_0x451e1e={};_0x451e1e[_0x2769ce(0x60f,0x591)]=_0x26ef71,_0x451e1e['vertexShaderSource']=this['vertexShaderSource'],_0x451e1e[_0x7b615(0x555,0x5c3)]=this['fragmentShaderSource'],_0x451e1e['attributeLocations']=this['attributeLocations'];const _0x468ac3=ShaderProgram[_0x7b615(0x553,0x538)](_0x451e1e),_0x2cf6a3=RenderState[_0x7b615(0x5be,0x538)](this[_0x7b615(0x600,0x5bf)]),_0x54bb35={};return _0x54bb35['owner']=this,_0x54bb35['vertexArray']=_0x2e4896,_0x54bb35[_0x7b615(0x526,0x59f)]=this['primitiveType'],_0x54bb35['modelMatrix']=Matrix4['IDENTITY'],_0x54bb35['renderState']=_0x2cf6a3,_0x54bb35['shaderProgram']=_0x468ac3,_0x54bb35[_0x2769ce(0x55b,0x597)]=this['framebuffer'],_0x54bb35['uniformMap']=this['uniformMap'],_0x54bb35[_0x2769ce(0x599,0x526)]=Pass$1['OPAQUE'],new DrawCommand(_0x54bb35);}else{if(this[_0x7b615(0x4d7,0x50b)]==='Compute'){const _0x1d17c5={};return _0x1d17c5['owner']=this,_0x1d17c5['fragmentShaderSource']=this[_0x2769ce(0x5dd,0x5b9)],_0x1d17c5['uniformMap']=this[_0x2769ce(0x5bd,0x591)],_0x1d17c5['outputTexture']=this[_0x2769ce(0x558,0x5c8)],_0x1d17c5['persists']=!![],new ComputeCommand(_0x1d17c5);}else throw new Error(_0x2769ce(0x518,0x568));}}[_0x2c5470(0x566,0x59f)](_0x3cbf73,_0x115175){this[_0x4902a4(0xec,0xfd)]=_0x115175;function _0x29424e(_0x3bf0e6,_0x1e6aa4){return _0x130459(_0x3bf0e6-0x246,_0x1e6aa4);}function _0x4902a4(_0x50025a,_0xaf48f7){return _0x2c5470(_0xaf48f7- -0x550,_0x50025a);}defined(this['commandToExecute'])&&(this['commandToExecute']['vertexArray']=VertexArray$1[_0x29424e(0x49c,0x41e)]({'context':_0x3cbf73,'geometry':this['geometry'],'attributeLocations':this['attributeLocations'],'bufferUsage':BufferUsage$1['STATIC_DRAW']}));}[_0x2c5470(0x611,0x614)](_0x2e1264){if(!this[_0x3d0d9e(0x268,0x23c)]())return;if(!this['show']||!defined(_0x2e1264))return;!defined(this['commandToExecute'])&&(this[_0x3d0d9e(0x307,0x2be)]=this['createCommand'](_0x2e1264['context']));function _0x1cd106(_0x5e892b,_0x3623cb){return _0x2c5470(_0x5e892b- -0x7a1,_0x3623cb);}defined(this['preExecute'])&&this['preExecute']();if(!_0x2e1264['commandList']){console[_0x3d0d9e(0x2f1,0x32e)]('frameState.commandList\x20is\x20undefined');return;}function _0x3d0d9e(_0x18b922,_0xafed18){return _0x130459(_0x18b922-0xa9,_0xafed18);}defined(this[_0x1cd106(-0x1e6,-0x21e)])&&_0x2e1264[_0x3d0d9e(0x2dc,0x27b)]['push'](this['clearCommand']),defined(this['commandToExecute'])&&_0x2e1264[_0x3d0d9e(0x2dc,0x2d8)]['push'](this['commandToExecute']);}[_0x2c5470(0x5ff,0x606)](){return![];}['destroy'](){function _0x51963d(_0x2ee2c5,_0x42898e){return _0x130459(_0x2ee2c5- -0x303,_0x42898e);}function _0x4c8e68(_0x2b5981,_0x43a26e){return _0x2c5470(_0x43a26e- -0x1f,_0x2b5981);}if(defined(this['commandToExecute'])){var _0x57116e;(_0x57116e=this[_0x51963d(-0xa5,-0xc9)][_0x4c8e68(0x539,0x57d)])===null||_0x57116e===void 0x0||_0x57116e['destroy'](),this['commandToExecute']['shaderProgram']=undefined;}return destroyObject(this);}}function deepMerge(_0x39d93c,_0x4b3db0){if(!_0x39d93c)return _0x4b3db0;if(!_0x4b3db0)return _0x39d93c;const _0x23176c={..._0x4b3db0};function _0x2c3814(_0x29f1a0,_0x3ccd38){return _0x2c5470(_0x3ccd38- -0x124,_0x29f1a0);}const _0x5c9a7f=_0x23176c;function _0x3b583b(_0x5368b7,_0x57518b){return _0x2c5470(_0x57518b- -0x544,_0x5368b7);}for(const _0xdddc37 in _0x39d93c){if(Object['prototype']['hasOwnProperty'][_0x3b583b(0x91,0x97)](_0x39d93c,_0xdddc37)){const _0x18d2e9=_0x39d93c[_0xdddc37],_0x144ba2=_0x4b3db0[_0xdddc37];if(Array['isArray'](_0x18d2e9)){_0x5c9a7f[_0xdddc37]=_0x18d2e9[_0x3b583b(0xfc,0xce)]();continue;}if(_0x18d2e9&&typeof _0x18d2e9==='object'){_0x5c9a7f[_0xdddc37]=deepMerge(_0x18d2e9,_0x144ba2||{});continue;}_0x18d2e9!==undefined&&(_0x5c9a7f[_0xdddc37]=_0x18d2e9);}}return _0x5c9a7f;}const {Cartesian2:Cartesian2$1,FrameRateMonitor,PixelDatatype:PixelDatatype$1,PixelFormat:PixelFormat$1,Sampler:Sampler$1,Texture:Texture$1,TextureMagnificationFilter:TextureMagnificationFilter$1,TextureMinificationFilter:TextureMinificationFilter$1}=mars3d__namespace['Cesium'];class WindParticlesComputing{constructor(_0x4d65d8,_0x56c734,_0xc849bb,_0x36d6de,_0x26b452){this[_0x4dc54a(0x4c6,0x4ae)]=_0x4d65d8,this['options']=_0xc849bb,this['viewerParameters']=_0x36d6de,this[_0x4dc54a(0x386,0x3b6)]=_0x56c734;function _0x30c1ef(_0x372485,_0x7b58de){return _0x2c5470(_0x7b58de- -0x43c,_0x372485);}this['frameRate']=0x3c,this['frameRateAdjustment']=0x1;const _0x10ed9b={};_0x10ed9b['scene']=_0x26b452,_0x10ed9b[_0x30c1ef(0x17e,0x1d1)]=0x1;function _0x4dc54a(_0x7be7e4,_0x586c2e){return _0x130459(_0x586c2e-0x21b,_0x7be7e4);}_0x10ed9b['quietPeriod']=0x0,this[_0x4dc54a(0x3db,0x3c8)]=new FrameRateMonitor(_0x10ed9b),this[_0x30c1ef(0x154,0x118)](),this['createWindTextures'](),this['createParticlesTextures'](),this['createComputingPrimitives']();}[_0x130459(0x190,0x1ed)](){const _0x67eafa=()=>{function _0x3a066a(_0x2781fc,_0x2ccbb4){return _0x449a(_0x2781fc- -0x2fa,_0x2ccbb4);}function _0xa6171a(_0x2e1d74,_0x461125){return _0x449a(_0x461125- -0x349,_0x2e1d74);}this['frameRateMonitor']['lastFramesPerSecond']>0x14&&(this[_0x3a066a(-0xa2,-0x117)]=this[_0x3a066a(-0x16f,-0x13e)][_0x3a066a(-0x81,-0x6e)],this['frameRateAdjustment']=0x3c/Math['max'](this['frameRate'],0x1));};_0x67eafa();const _0x4ab132=setInterval(_0x67eafa,0x3e8),_0x39b4a3=this['destroy'][_0x316fd4(0x545,0x5b6)](this);function _0x316fd4(_0x3a4ba3,_0x3caf7f){return _0x2c5470(_0x3caf7f- -0x94,_0x3a4ba3);}this['destroy']=()=>{clearInterval(_0x4ab132),_0x39b4a3();};}['createWindTextures'](){const _0x403921={};function _0x2b475b(_0x5e064b,_0x4a6021){return _0x130459(_0x5e064b- -0x1eb,_0x4a6021);}_0x403921['minificationFilter']=TextureMinificationFilter$1['LINEAR'],_0x403921['magnificationFilter']=TextureMagnificationFilter$1['LINEAR'];const _0x5e9a5b={'context':this['context'],'width':this['windData'][_0x30496f(0x1cf,0x220)],'height':this[_0x2b475b(-0x50,-0x43)]['height'],'pixelFormat':PixelFormat$1['RED'],'pixelDatatype':PixelDatatype$1['FLOAT'],'flipY':this[_0x30496f(0x1c0,0x1fe)]['flipY']??![],'sampler':new Sampler$1(_0x403921)};function _0x30496f(_0xea7851,_0x34a0a2){return _0x2c5470(_0xea7851- -0x44f,_0x34a0a2);}this['windTextures']={'U':new Texture$1({..._0x5e9a5b,'source':{'arrayBufferView':new Float32Array(this['windData']['u'][_0x2b475b(0x88,0x42)])}}),'V':new Texture$1({..._0x5e9a5b,'source':{'arrayBufferView':new Float32Array(this[_0x30496f(0x110,0xd0)]['v'][_0x2b475b(0x88,0x60)])}})};}['createParticlesTextures'](){function _0x185142(_0x4b4e4e,_0x67b8f6){return _0x130459(_0x67b8f6- -0x2c0,_0x4b4e4e);}const _0x476187={};_0x476187['minificationFilter']=TextureMinificationFilter$1['NEAREST'],_0x476187[_0x185142(-0x111,-0xef)]=TextureMagnificationFilter$1['NEAREST'];const _0x406c5f={'context':this['context'],'width':this['options']['particlesTextureSize'],'height':this['options']['particlesTextureSize'],'pixelFormat':PixelFormat$1[_0x185142(-0x7e,-0xd1)],'pixelDatatype':PixelDatatype$1['FLOAT'],'flipY':![],'source':{'arrayBufferView':new Float32Array(this['options']['particlesTextureSize']*this['options'][_0x185142(-0xba,-0xf2)]*0x4)['fill'](0x0)},'sampler':new Sampler$1(_0x476187)};function _0x29a615(_0x473980,_0x4bb7b1){return _0x130459(_0x4bb7b1- -0x68,_0x473980);}this[_0x29a615(0x16b,0x175)]={'previousParticlesPosition':new Texture$1(_0x406c5f),'currentParticlesPosition':new Texture$1(_0x406c5f),'nextParticlesPosition':new Texture$1(_0x406c5f),'postProcessingPosition':new Texture$1(_0x406c5f),'particlesSpeed':new Texture$1(_0x406c5f)};}['destroyParticlesTextures'](){Object['values'](this['particlesTextures'])['forEach'](_0x3f7330=>_0x3f7330['destroy']());}['createComputingPrimitives'](){function _0x2ec262(_0x38a3e3,_0x1363d1){return _0x2c5470(_0x38a3e3- -0x118,_0x1363d1);}function _0x1d0f4e(_0x282d1c,_0x20fea1){return _0x130459(_0x282d1c-0x194,_0x20fea1);}this['primitives']={'calculateSpeed':new CustomPrimitive({'commandType':'Compute','uniformMap':{'U':()=>this['windTextures']['U'],'V':()=>this['windTextures']['V'],'uRange':()=>new Cartesian2$1(this['windData']['u']['min'],this['windData']['u']['max']),'vRange':()=>new Cartesian2$1(this[_0x2ec262(0x447,0x411)]['v']['min'],this[_0x2ec262(0x447,0x409)]['v'][_0x2ec262(0x4b4,0x4d8)]),'speedRange':()=>new Cartesian2$1(this[_0x1d0f4e(0x32f,0x396)][_0x1d0f4e(0x3c4,0x3d8)][_0x1d0f4e(0x351,0x393)],this[_0x1d0f4e(0x32f,0x396)]['speed']['max']),'currentParticlesPosition':()=>this['particlesTextures'][_0x2ec262(0x537,0x5be)],'speedScaleFactor':()=>{function _0x52c189(_0x2d7785,_0x25e3d6){return _0x2ec262(_0x25e3d6- -0xa5,_0x2d7785);}function _0x36a02(_0x4cc86f,_0x25cde8){return _0x1d0f4e(_0x25cde8- -0xd9,_0x4cc86f);}return(this['viewerParameters']['pixelSize']+0x32)*this[_0x36a02(0x330,0x306)][_0x36a02(0x25c,0x2a8)];},'frameRateAdjustment':()=>this['frameRateAdjustment'],'dimension':()=>new Cartesian2$1(this['windData']['width'],this[_0x2ec262(0x447,0x44e)]['height']),'minimum':()=>new Cartesian2$1(this[_0x2ec262(0x447,0x42a)]['bounds']['west'],this[_0x2ec262(0x447,0x472)]['bounds'][_0x1d0f4e(0x400,0x45a)]),'maximum':()=>new Cartesian2$1(this['windData'][_0x2ec262(0x4c6,0x4fe)][_0x2ec262(0x475,0x4c7)],this[_0x2ec262(0x447,0x4ab)][_0x1d0f4e(0x3ae,0x338)][_0x2ec262(0x44b,0x403)])},'fragmentShaderSource':ShaderManager[_0x2ec262(0x4ce,0x45b)](),'outputTexture':this[_0x2ec262(0x489,0x46f)]['particlesSpeed'],'preExecute':()=>{const _0x2f5582=this['particlesTextures']['previousParticlesPosition'];function _0xd87953(_0x69ee8c,_0x510e96){return _0x2ec262(_0x510e96-0x82,_0x69ee8c);}this['particlesTextures'][_0xd87953(0x5a1,0x595)]=this[_0x598d43(0x4c3,0x515)][_0x598d43(0x637,0x5c3)],this['particlesTextures']['currentParticlesPosition']=this['particlesTextures'][_0x598d43(0x50e,0x559)],this['particlesTextures'][_0x598d43(0x55f,0x559)]=_0x2f5582;function _0x598d43(_0x4ffb54,_0x4e39a8){return _0x2ec262(_0x4e39a8-0x8c,_0x4ffb54);}this['primitives']['calculateSpeed']['commandToExecute']&&(this[_0x598d43(0x444,0x4c5)][_0x598d43(0x5fc,0x5c5)]['commandToExecute']['outputTexture']=this['particlesTextures'][_0x598d43(0x516,0x4ee)]);},'isDynamic':()=>this['options']['dynamic']}),'updatePosition':new CustomPrimitive({'commandType':_0x1d0f4e(0x3c2,0x421),'uniformMap':{'currentParticlesPosition':()=>this['particlesTextures']['currentParticlesPosition'],'particlesSpeed':()=>this[_0x2ec262(0x489,0x419)]['particlesSpeed']},'fragmentShaderSource':ShaderManager['getUpdatePositionShader'](),'outputTexture':this[_0x1d0f4e(0x371,0x3ee)]['nextParticlesPosition'],'preExecute':()=>{function _0xcbd2fa(_0x341184,_0x112adf){return _0x1d0f4e(_0x112adf- -0x1aa,_0x341184);}function _0x4ef28b(_0x5226ae,_0x417dc7){return _0x1d0f4e(_0x417dc7- -0x1f5,_0x5226ae);}this['primitives']['updatePosition'][_0xcbd2fa(0x24e,0x248)]&&(this[_0x4ef28b(0x18a,0x12c)]['updatePosition']['commandToExecute']['outputTexture']=this[_0xcbd2fa(0x21a,0x1c7)][_0x4ef28b(0x24a,0x214)]);},'isDynamic':()=>this['options']['dynamic']}),'postProcessingPosition':new CustomPrimitive({'commandType':_0x2ec262(0x4da,0x48c),'uniformMap':{'nextParticlesPosition':()=>this['particlesTextures'][_0x2ec262(0x521,0x4f8)],'particlesSpeed':()=>this['particlesTextures']['particlesSpeed'],'lonRange':()=>this['viewerParameters']['lonRange'],'latRange':()=>this[_0x2ec262(0x458,0x483)]['latRange'],'dataLonRange':()=>new Cartesian2$1(this['windData'][_0x1d0f4e(0x3ae,0x3cc)][_0x1d0f4e(0x3ec,0x3ea)],this['windData']['bounds']['east']),'dataLatRange':()=>new Cartesian2$1(this[_0x1d0f4e(0x32f,0x375)]['bounds'][_0x1d0f4e(0x400,0x44e)],this[_0x1d0f4e(0x32f,0x2b3)]['bounds']['north']),'randomCoefficient':function(){function _0x3283fd(_0x50979e,_0x86e113){return _0x1d0f4e(_0x50979e- -0x167,_0x86e113);}return Math[_0x3283fd(0x2a5,0x251)]();},'dropRate':()=>this[_0x2ec262(0x4f7,0x580)]['dropRate'],'dropRateBump':()=>this['options'][_0x1d0f4e(0x3c0,0x34c)],'useViewerBounds':()=>this['options']['useViewerBounds']},'fragmentShaderSource':ShaderManager[_0x2ec262(0x45b,0x46a)](),'outputTexture':this['particlesTextures']['postProcessingPosition'],'preExecute':()=>{function _0x34316b(_0x55523f,_0x351fe1){return _0x1d0f4e(_0x55523f- -0xf3,_0x351fe1);}function _0xf5f511(_0x35b176,_0x3ebe45){return _0x2ec262(_0x3ebe45- -0x328,_0x35b176);}this[_0x34316b(0x22e,0x29d)]['postProcessingPosition']['commandToExecute']&&(this['primitives'][_0xf5f511(0x18c,0x1a5)]['commandToExecute']['outputTexture']=this['particlesTextures']['postProcessingPosition']);},'isDynamic':()=>this['options'][_0x1d0f4e(0x425,0x429)]})};}['reCreateWindTextures'](){this['windTextures']['U']['destroy'](),this['windTextures']['V']['destroy'](),this['createWindTextures']();}['updateWindData'](_0x46eea9){function _0x25d064(_0x1e4746,_0x40cb8f){return _0x130459(_0x1e4746- -0xa,_0x40cb8f);}this['windData']=_0x46eea9,this[_0x25d064(0x1d7,0x1ed)]();}['updateOptions'](_0x5f5aa7){const _0x3870ef=_0x5f5aa7[_0x2aca61(0x40,-0x37)]!==undefined&&_0x5f5aa7['flipY']!==this['options'][_0x2aca61(-0xc1,-0x37)];function _0x2aca61(_0x1c3556,_0x439976){return _0x2c5470(_0x439976- -0x5b4,_0x1c3556);}function _0x1705f1(_0x27d7ba,_0x36e922){return _0x130459(_0x36e922- -0x292,_0x27d7ba);}this['options']=deepMerge(_0x5f5aa7,this[_0x2aca61(0x9a,0x5b)]),_0x3870ef&&this['reCreateWindTextures']();}[_0x2c5470(0x644,0x65a)](_0x29e9e0){function _0x5e4655(_0x45656d,_0x4cbad2){return _0x130459(_0x4cbad2-0x37e,_0x45656d);}function _0x1665ae(_0x4d6d77,_0x4371bb){return _0x2c5470(_0x4371bb- -0x504,_0x4d6d77);}const {array:_0x142f53}=_0x29e9e0;let {min:_0x4562ec,max:_0x17f4d8}=_0x29e9e0;const _0x5c9a59=new Float32Array(_0x142f53[_0x5e4655(0x554,0x560)]);_0x4562ec===undefined&&(console[_0x5e4655(0x57b,0x5c6)]('min\x20is\x20undefined,\x20calculate\x20min'),_0x4562ec=Math['min'](..._0x142f53));_0x17f4d8===undefined&&(console[_0x5e4655(0x5f2,0x5c6)](_0x5e4655(0x558,0x58c)),_0x17f4d8=Math['max'](..._0x142f53));const _0x4e0cd2=Math['max'](Math[_0x1665ae(0x153,0xca)](_0x4562ec),Math[_0x5e4655(0x532,0x588)](_0x17f4d8));for(let _0x4745bd=0x0;_0x4745bd<_0x142f53['length'];_0x4745bd++){const _0x5b6766=_0x142f53[_0x4745bd]/_0x4e0cd2;_0x5c9a59[_0x4745bd]=_0x5b6766;}return _0x5c9a59;}[_0x130459(0x205,0x1bd)](){Object[_0x2f1606(-0x1e0,-0x242)](this[_0x2f1606(-0x1a0,-0x210)])[_0x2f1606(-0x17e,-0x138)](_0x59b6ea=>_0x59b6ea['destroy']());function _0x2246b9(_0x39bc07,_0x20de0e){return _0x130459(_0x20de0e- -0x143,_0x39bc07);}Object[_0x2f1606(-0x1e0,-0x179)](this['particlesTextures'])['forEach'](_0x3af474=>_0x3af474[_0x2246b9(0xb4,0xc2)]());function _0x2f1606(_0x2ddecd,_0x184602){return _0x2c5470(_0x2ddecd- -0x76a,_0x184602);}Object['values'](this['primitives'])[_0x2246b9(0xbc,0xe5)](_0x4461fb=>_0x4461fb[_0x2f1606(-0x1a1,-0x1cf)]()),this['frameRateMonitor']['destroy']();}}const {Appearance,BufferUsage,Cartesian2,Color:Color$1,ComponentDatatype,Framebuffer,Geometry,GeometryAttribute,GeometryAttributes,PixelDatatype,PixelFormat,PrimitiveType,Sampler,SceneMode,Texture,TextureMagnificationFilter,TextureMinificationFilter,TextureWrap,VertexArray}=mars3d__namespace[_0x2c5470(0x59e,0x609)];class WindParticlesRendering{constructor(_0x2866b6,_0x458740,_0x2f89da,_0x4f56a5){this['context']=_0x2866b6,this['options']=_0x458740,this[_0x275da0(-0x197,-0x1ec)]=_0x2f89da,this[_0x275da0(-0xc8,-0x4b)]=_0x4f56a5;(typeof this[_0x275da0(-0xf8,-0x12e)]['particlesTextureSize']!=='number'||this[_0x2ecdb6(0x3f2,0x3d1)]['particlesTextureSize']<=0x0)&&(console[_0x275da0(-0xf3,-0x163)]('Invalid\x20particlesTextureSize.\x20Using\x20default\x20value\x20of\x20256.'),this['options']['particlesTextureSize']=0x100);this['colorTable']=this['createColorTableTexture'](),this['textures']=this['createRenderingTextures'](),this[_0x275da0(-0x15c,-0x10d)]=this['createRenderingFramebuffers']();function _0x275da0(_0x28cb9f,_0xed0647){return _0x130459(_0x28cb9f- -0x343,_0xed0647);}function _0x2ecdb6(_0x5442c0,_0x2889c9){return _0x2c5470(_0x2889c9- -0x23e,_0x5442c0);}this['primitives']=this[_0x275da0(-0x189,-0x1ca)]();}[_0x2c5470(0x647,0x67f)](){const _0x258b1a={};_0x258b1a['context']=this['context'],_0x258b1a[_0x7d7ea6(0x34f,0x37f)]=this['context']['drawingBufferWidth'],_0x258b1a['height']=this[_0x7d7ea6(0x388,0x31f)]['drawingBufferHeight'],_0x258b1a['pixelFormat']=PixelFormat['RGBA'];function _0x2e074e(_0x5bc9cd,_0xf0990b){return _0x2c5470(_0xf0990b- -0x613,_0x5bc9cd);}function _0x7d7ea6(_0x1a4454,_0x42ebdc){return _0x2c5470(_0x1a4454- -0x2cf,_0x42ebdc);}_0x258b1a['pixelDatatype']=PixelDatatype['UNSIGNED_BYTE'];const _0x3298e0=_0x258b1a,_0x3c53c6={};_0x3c53c6['context']=this['context'],_0x3c53c6['width']=this[_0x2e074e(0x93,0x44)]['drawingBufferWidth'],_0x3c53c6[_0x2e074e(0x54,0x1e)]=this[_0x7d7ea6(0x388,0x353)][_0x7d7ea6(0x301,0x354)],_0x3c53c6['pixelFormat']=PixelFormat['DEPTH_COMPONENT'],_0x3c53c6[_0x2e074e(0x36,-0xc)]=PixelDatatype['UNSIGNED_INT'];const _0x1bd35c=_0x3c53c6;return{'segmentsColor':new Texture(_0x3298e0),'segmentsDepth':new Texture(_0x1bd35c)};}['createRenderingFramebuffers'](){const _0xa93dec={};_0xa93dec['context']=this['context'],_0xa93dec['colorTextures']=[this['textures']['segmentsColor']],_0xa93dec['depthTexture']=this['textures'][_0x4c4ef1(0x7,-0x2a)];function _0x4c4ef1(_0x528ff7,_0x22b1df){return _0x130459(_0x528ff7- -0x1fb,_0x22b1df);}return{'segments':new Framebuffer(_0xa93dec)};}[_0x2c5470(0x5c8,0x54b)](){Object['values'](this['framebuffers'])['forEach'](_0x4151b4=>{_0x4151b4['destroy']();});}['createColorTableTexture'](){function _0x2a8f53(_0x556475,_0x4d0f7c){return _0x130459(_0x556475-0x179,_0x4d0f7c);}const _0x53249d=new Float32Array(this[_0x2a8f53(0x3c4,0x405)]['colors']['flatMap'](_0x56ca45=>{function _0x135c4b(_0x340cbc,_0x2eebd5){return _0x2a8f53(_0x2eebd5- -0x3e7,_0x340cbc);}function _0x5dac4(_0x2bcecc,_0x185452){return _0x2a8f53(_0x2bcecc-0x126,_0x185452);}const _0x287876=Color$1[_0x135c4b(-0xa8,-0x22)](_0x56ca45);return[_0x287876[_0x135c4b(-0x64,-0xd5)],_0x287876['green'],_0x287876[_0x135c4b(0x78,0x1c)],_0x287876['alpha']];})),_0x8ba239={};_0x8ba239[_0x242f19(0x57b,0x571)]=TextureMinificationFilter['LINEAR'],_0x8ba239['magnificationFilter']=TextureMagnificationFilter['LINEAR'];function _0x242f19(_0x2f4320,_0x313004){return _0x2c5470(_0x313004- -0x77,_0x2f4320);}return _0x8ba239[_0x242f19(0x5d0,0x563)]=TextureWrap['CLAMP_TO_EDGE'],_0x8ba239['wrapT']=TextureWrap['CLAMP_TO_EDGE'],new Texture({'context':this[_0x242f19(0x5a7,0x5e0)],'width':this['options']['colors']['length'],'height':0x1,'pixelFormat':PixelFormat['RGBA'],'pixelDatatype':PixelDatatype[_0x2a8f53(0x3d0,0x3d9)],'sampler':new Sampler(_0x8ba239),'source':{'width':this[_0x2a8f53(0x3c4,0x36d)]['colors']['length'],'height':0x1,'arrayBufferView':_0x53249d}});}['createSegmentsGeometry'](){const _0x3c1f1f=0x4,_0x1652b0=this[_0x1de407(0x17e,0x179)][_0x1fe5a1(0x3a1,0x37a)];let _0x504d5e=[];for(let _0x2883d0=0x0;_0x2883d0<_0x1652b0;_0x2883d0++){for(let _0x1be866=0x0;_0x1be866<_0x1652b0;_0x1be866++){for(let _0x26c3bc=0x0;_0x26c3bc<_0x3c1f1f;_0x26c3bc++){_0x504d5e[_0x1fe5a1(0x426,0x3b3)](_0x2883d0/_0x1652b0),_0x504d5e[_0x1fe5a1(0x426,0x3f1)](_0x1be866/_0x1652b0);}}}_0x504d5e=new Float32Array(_0x504d5e);const _0x3c64b7=this['options']['particlesTextureSize']**0x2;function _0x1de407(_0x129914,_0x4320eb){return _0x130459(_0x4320eb- -0xd2,_0x129914);}let _0x45c570=[];for(let _0xb37814=0x0;_0xb37814<_0x3c64b7;_0xb37814++){_0x45c570['push'](-0x1,-0x1,0x0,-0x1,0x1,0x0,0x1,-0x1,0x0,0x1,0x1,0x0);}_0x45c570=new Float32Array(_0x45c570);let _0x52556b=[];for(let _0xd91957=0x0,_0x3f5caf=0x0;_0xd91957<_0x3c64b7;_0xd91957++){_0x52556b['push'](_0x3f5caf+0x0,_0x3f5caf+0x1,_0x3f5caf+0x2,_0x3f5caf+0x2,_0x3f5caf+0x1,_0x3f5caf+0x3),_0x3f5caf+=_0x3c1f1f;}_0x52556b=new Uint32Array(_0x52556b);const _0x17f2d1={};function _0x1fe5a1(_0x42656d,_0x1a7e28){return _0x130459(_0x42656d-0x1d3,_0x1a7e28);}_0x17f2d1['componentDatatype']=ComponentDatatype['FLOAT'],_0x17f2d1['componentsPerAttribute']=0x2,_0x17f2d1[_0x1de407(0x119,0xf4)]=_0x504d5e;const _0xdeb7cf={};_0xdeb7cf[_0x1de407(0xf1,0x178)]=ComponentDatatype[_0x1de407(0x1ba,0x185)],_0xdeb7cf['componentsPerAttribute']=0x3,_0xdeb7cf[_0x1fe5a1(0x399,0x414)]=_0x45c570;const _0x5cab13=new Geometry({'attributes':new GeometryAttributes({'st':new GeometryAttribute(_0x17f2d1),'normal':new GeometryAttribute(_0xdeb7cf)}),'indices':_0x52556b});return _0x5cab13;}[_0x2c5470(0x5d3,0x5fc)](_0x51f81a){const _0x11f4bf={'viewport':undefined,'depthTest':undefined,'depthMask':undefined,'blending':undefined,..._0x51f81a};return Appearance['getDefaultRenderState'](!![],![],_0x11f4bf);}['createPrimitives'](){const _0x5cfd02={};_0x5cfd02['st']=0x0,_0x5cfd02[_0x4530d4(-0x174,-0x1c8)]=0x1;const _0x2f394c={};_0x2f394c[_0x35c67a(0x2c1,0x276)]=!![];const _0x463fe2={};function _0x4530d4(_0x2981f5,_0x42877f){return _0x130459(_0x2981f5- -0x33c,_0x42877f);}_0x463fe2['enabled']=!![],_0x463fe2['blendEquation']=WebGLRenderingContext[_0x35c67a(0x1ab,0x1eb)],_0x463fe2['blendFuncSource']=WebGLRenderingContext['SRC_ALPHA'],_0x463fe2['blendFuncDestination']=WebGLRenderingContext['ONE_MINUS_SRC_ALPHA'];const _0x53735c={};_0x53735c['viewport']=undefined,_0x53735c['depthTest']=_0x2f394c,_0x53735c['depthMask']=!![],_0x53735c['blending']=_0x463fe2;function _0x35c67a(_0x1dbafa,_0x13156b){return _0x130459(_0x13156b- -0x1,_0x1dbafa);}const _0x702259=new CustomPrimitive({'commandType':_0x4530d4(-0x9f,-0x6b),'attributeLocations':_0x5cfd02,'geometry':this['createSegmentsGeometry'](),'primitiveType':PrimitiveType[_0x35c67a(0x22d,0x1f8)],'uniformMap':{'previousParticlesPosition':()=>this[_0x35c67a(0x238,0x27a)]['particlesTextures']['previousParticlesPosition'],'currentParticlesPosition':()=>this['computing']['particlesTextures']['currentParticlesPosition'],'postProcessingPosition':()=>this['computing'][_0x4530d4(-0x15f,-0x1cf)]['postProcessingPosition'],'particlesSpeed':()=>this['computing']['particlesTextures'][_0x35c67a(0x236,0x1b5)],'frameRateAdjustment':()=>this['computing']['frameRateAdjustment'],'colorTable':()=>this['colorTable'],'domain':()=>{function _0x2694d8(_0x835c70,_0x53b3bd){return _0x4530d4(_0x53b3bd-0x5dd,_0x835c70);}var _0x5102f3,_0x7246e;const _0x48cc1a=new Cartesian2(((_0x5102f3=this['options']['domain'])===null||_0x5102f3===void 0x0?void 0x0:_0x5102f3[_0x2694d8(0x3fe,0x45e)])??this['computing']['windData'][_0x288e10(0x1ad,0x170)]['min'],((_0x7246e=this[_0x288e10(0x15c,0x18b)][_0x2694d8(0x4c9,0x463)])===null||_0x7246e===void 0x0?void 0x0:_0x7246e[_0x2694d8(0x470,0x4a9)])??this['computing'][_0x2694d8(0x400,0x43c)]['speed'][_0x2694d8(0x436,0x4a9)]);function _0x288e10(_0x598387,_0x4e94ea){return _0x35c67a(_0x598387,_0x4e94ea- -0xbf);}return _0x48cc1a;},'displayRange':()=>{var _0xfc34db,_0x75ec25;function _0x238285(_0x4b4a63,_0x392365){return _0x35c67a(_0x4b4a63,_0x392365- -0x22);}const _0x4a802a=new Cartesian2(((_0xfc34db=this[_0x374291(-0x1ad,-0x174)][_0x374291(-0x248,-0x23a)])===null||_0xfc34db===void 0x0?void 0x0:_0xfc34db['min'])??this['computing']['windData']['speed']['min'],((_0x75ec25=this['options'][_0x238285(0x138,0x18d)])===null||_0x75ec25===void 0x0?void 0x0:_0x75ec25['max'])??this[_0x238285(0x2b0,0x258)]['windData']['speed'][_0x374291(-0x1f0,-0x198)]);function _0x374291(_0x29f575,_0x404501){return _0x35c67a(_0x404501,_0x29f575- -0x3f7);}return _0x4a802a;},'particleHeight':()=>this['options'][_0x35c67a(0x2e8,0x26a)]||0x0,'aspect':()=>this['context']['drawingBufferWidth']/this['context'][_0x35c67a(0x222,0x20b)],'pixelSize':()=>this['viewerParameters']['pixelSize'],'lineWidth':()=>{const _0x3c0973={};_0x3c0973[_0x8c7870(0x129,0x165)]=0x1,_0x3c0973['max']=0x2;function _0x8c7870(_0x559d75,_0xb452dc){return _0x35c67a(_0x559d75,_0xb452dc- -0x57);}function _0x5481dc(_0x698a8e,_0x215939){return _0x35c67a(_0x698a8e,_0x215939-0x35c);}const _0x40aa8f=this[_0x5481dc(0x616,0x5a6)][_0x5481dc(0x5a3,0x5d9)]||_0x3c0973;return new Cartesian2(_0x40aa8f['min'],_0x40aa8f[_0x8c7870(0x19c,0x1b0)]);},'lineLength':()=>{const _0x22473f={};_0x22473f[_0x1b04ac(0x30f,0x28d)]=0x14,_0x22473f['max']=0x64;function _0x6a6898(_0x2fc32c,_0x2734d3){return _0x35c67a(_0x2734d3,_0x2fc32c-0x263);}function _0x1b04ac(_0xa4bde1,_0x382d4c){return _0x35c67a(_0xa4bde1,_0x382d4c-0xd1);}const _0x1ed5e6=this[_0x6a6898(0x4ad,0x4dd)]['lineLength']||_0x22473f;return new Cartesian2(_0x1ed5e6['min'],_0x1ed5e6[_0x1b04ac(0x2dd,0x2d8)]);},'is3D':()=>this['viewerParameters'][_0x4530d4(-0x14c,-0xe2)]===SceneMode[_0x4530d4(-0xcb,-0x144)],'segmentsDepthTexture':()=>this['textures'][_0x4530d4(-0x13a,-0x196)]},'vertexShaderSource':ShaderManager['getSegmentDrawVertexShader'](),'fragmentShaderSource':ShaderManager[_0x4530d4(-0xeb,-0xd0)](),'rawRenderState':this['createRawRenderState'](_0x53735c)}),_0x2a8968={};return _0x2a8968[_0x4530d4(-0x194,-0x1b0)]=_0x702259,_0x2a8968;}['onParticlesTextureSizeChange'](){const _0x1e48aa=this[_0x558e45(0x563,0x5b5)]();this['primitives']['segments']['geometry']=_0x1e48aa;const _0x1e7a15={};_0x1e7a15[_0x558e45(0x545,0x5c7)]=this[_0x9608d8(-0x8e,-0xc8)],_0x1e7a15[_0x9608d8(-0x4e,-0xd2)]=_0x1e48aa;function _0x9608d8(_0x563f95,_0x2807de){return _0x130459(_0x2807de- -0x35b,_0x563f95);}_0x1e7a15['attributeLocations']=this['primitives'][_0x9608d8(-0x19a,-0x1b3)][_0x9608d8(-0x125,-0xc3)];function _0x558e45(_0x1a3d80,_0x1cc98c){return _0x130459(_0x1cc98c-0x334,_0x1a3d80);}_0x1e7a15['bufferUsage']=BufferUsage['STATIC_DRAW'];const _0x151ef3=VertexArray['fromGeometry'](_0x1e7a15);this[_0x9608d8(-0x1ae,-0x1ce)][_0x9608d8(-0x15e,-0x1b3)]['commandToExecute']&&(this['primitives']['segments'][_0x558e45(0x535,0x592)][_0x9608d8(-0x1df,-0x197)]=_0x151ef3);}['onColorTableChange'](){this['colorTable']['destroy']();function _0x124bc6(_0x19642c,_0x53296c){return _0x130459(_0x53296c- -0x3fc,_0x19642c);}this['colorTable']=this[_0x124bc6(-0x1c1,-0x248)]();}[_0x2c5470(0x656,0x623)](_0x35c001){const _0x4a71b0=_0x35c001['colors']&&JSON['stringify'](_0x35c001[_0x324e1d(0x238,0x26c)])!==JSON['stringify'](this['options']['colors']);function _0x324e1d(_0x591fe2,_0x4ede16){return _0x130459(_0x591fe2-0xa3,_0x4ede16);}function _0x19e885(_0x397c59,_0x5311f0){return _0x2c5470(_0x397c59- -0x36b,_0x5311f0);}this[_0x19e885(0x2a4,0x319)]=deepMerge(_0x35c001,this[_0x324e1d(0x2ee,0x32b)]),_0x4a71b0&&this['onColorTableChange']();}[_0x2c5470(0x5c9,0x56d)](){Object['values'](this['framebuffers'])['forEach'](_0xcea8a5=>{_0xcea8a5['destroy']();});function _0x23252a(_0xc0a1be,_0x28ffb5){return _0x130459(_0x28ffb5-0x1d4,_0xc0a1be);}Object[_0x4af943(0x25b,0x2bb)](this[_0x23252a(0x37f,0x361)])['forEach'](_0x1ffca9=>{function _0x38734a(_0x31ee60,_0x244af7){return _0x4af943(_0x31ee60- -0x3a0,_0x244af7);}_0x1ffca9[_0x38734a(-0x106,-0x157)]();});function _0x4af943(_0x5e6fa1,_0xdc4129){return _0x130459(_0x5e6fa1-0x95,_0xdc4129);}this[_0x4af943(0x23b,0x203)][_0x4af943(0x29a,0x31a)]();}}const {ClearCommand,Color,Pass}=mars3d__namespace['Cesium'];class WindParticleSystem{constructor(_0x2cb5c1,_0x5c5aaf,_0x31fbbc,_0x57882c,_0xfb9e64){this['context']=_0x2cb5c1;function _0x559c5e(_0x5a1328,_0x179ab3){return _0x130459(_0x5a1328- -0x101,_0x179ab3);}function _0x252e1b(_0x1cd822,_0x482246){return _0x2c5470(_0x482246- -0x712,_0x1cd822);}this['options']=_0x31fbbc,this[_0x252e1b(-0x1ca,-0x1a2)]=_0x57882c,this['computing']=new WindParticlesComputing(_0x2cb5c1,_0x5c5aaf,_0x31fbbc,_0x57882c,_0xfb9e64),this[_0x559c5e(0x168,0x149)]=new WindParticlesRendering(_0x2cb5c1,_0x31fbbc,_0x57882c,this[_0x559c5e(0x17a,0x1f1)]),this['clearFramebuffers']();}['getPrimitives'](){function _0x2ee99e(_0x47e2e5,_0x15c3d2){return _0x130459(_0x47e2e5- -0x2a,_0x15c3d2);}const _0x55be07=[this['computing'][_0x2ee99e(0x163,0x196)][_0x2ee99e(0x263,0x21c)],this[_0x25ba16(0x228,0x29b)]['primitives']['updatePosition'],this[_0x2ee99e(0x251,0x23b)]['primitives'][_0x2ee99e(0x1f7,0x210)],this['rendering']['primitives']['segments']];function _0x25ba16(_0x52acd8,_0x1b7829){return _0x2c5470(_0x52acd8- -0x417,_0x1b7829);}return _0x55be07;}[_0x130459(0x22d,0x207)](){function _0x612b74(_0x4fdf50,_0x4e11af){return _0x130459(_0x4fdf50-0xfd,_0x4e11af);}const _0x12fa63=new ClearCommand({'color':new Color(0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':undefined,'pass':Pass[_0x38d276(-0x1b,-0x2)]});function _0x38d276(_0x464130,_0x3c2027){return _0x2c5470(_0x3c2027- -0x578,_0x464130);}Object[_0x612b74(0x360,0x31b)](this['rendering']['framebuffers'])[_0x38d276(0xb6,0x74)](_0x2f1b99=>{_0x12fa63['framebuffer']=this['rendering']['framebuffers'][_0x2f1b99];function _0x4fef54(_0x40fb45,_0x4f2c9c){return _0x38d276(_0x40fb45,_0x4f2c9c-0x4c);}_0x12fa63[_0x4fef54(0x6a,0xac)](this['context']);});}['changeOptions'](_0x3492db){let _0x211ff8=![];_0x3492db['particlesTextureSize']&&this['options']['particlesTextureSize']!==_0x3492db[_0x552661(0x7d,0xba)]&&(_0x211ff8=!![]);function _0x552661(_0x4c31f9,_0x4ca6f4){return _0x130459(_0x4c31f9- -0x151,_0x4ca6f4);}const _0x456e1b=deepMerge(_0x3492db,this['options']);if(_0x456e1b[_0x552661(0x7d,0x9)]<0x1)throw new Error(_0x4d32ca(-0x1d0,-0x196));this['options']=_0x456e1b,this[_0x4d32ca(-0x1ce,-0x193)][_0x552661(0x141,0x171)](_0x3492db);function _0x4d32ca(_0x1bdfb0,_0x74aa58){return _0x2c5470(_0x74aa58- -0x7c0,_0x1bdfb0);}this['computing']['updateOptions'](_0x3492db),_0x211ff8&&(this[_0x4d32ca(-0x1ea,-0x181)]['destroyParticlesTextures'](),this['computing']['createParticlesTextures'](),this['rendering']['onParticlesTextureSizeChange']());}['applyViewerParameters'](_0x2a18b5){this['viewerParameters']=_0x2a18b5,this['computing'][_0x1b01a1(0x5b6,0x538)]=_0x2a18b5;function _0x1b01a1(_0x1b2da6,_0x89b69c){return _0x2c5470(_0x89b69c- -0x38,_0x1b2da6);}function _0x4b1391(_0x410de9,_0x1890da){return _0x2c5470(_0x410de9- -0x6be,_0x1890da);}this[_0x4b1391(-0x91,-0x5c)][_0x1b01a1(0x569,0x538)]=_0x2a18b5;}[_0x2c5470(0x5c9,0x61e)](){this[_0x4ff8a6(0x420,0x467)]['destroy']();function _0x4ff8a6(_0x28754a,_0x139c31){return _0x2c5470(_0x28754a- -0x21f,_0x139c31);}this['rendering']['destroy']();}}const Cesium$1=mars3d__namespace['Cesium'],BaseLayer$1=mars3d__namespace['layer']['BaseLayer'],_0x1d0fd3={};function _0x3486(){const _0x4cffa3=['lineWidth','_onMapWhellEvent','processWindData','createSegmentsGeometry','rgb(206,255,255)','createRenderingTextures','_calcUV','now','bind','type','_removedHook','geometry','blue','currentParticlesPosition','canvasWidth','calculateSpeed','_bilinearInterpolation','default','round','dynamic','updateOptions','context','toDegrees','addEventListener','getUVByPoint','Cartesian2','attributeLocations','style','_addedHook','lastFramesPerSecond','rows','Draw','Rectangle','cartesianToCartographic','globe','811224nlkqkQ','updateViewerParameters','__esModule','primitives','mouseDown','WindUtil','initFrameRate','getUVByXY','xmax','mars3d-wind插件\x20注册成功','steps','colors','age','changeOptions','positionWC','red','_updateIng2','windData','Unknown\x20command\x20type','camera','bufferUsage','north','_mountedHook','particles','setGeometry','pointerEvents','off','_onMouseDownEvent','colorTable','visibility','segments','commandType','isInExtent','alt','viewerParameters','frameRateMonitor','getOwnPropertyDescriptor','getPostProcessingPositionShader','displayRange','_speedRate','OPAQUE','6fvXAOS','createColorTableTexture','create','particlesSpeed','zIndex','moveTo','flipY','createPrimitives','setData','9KlsgDs','min','globalCompositeOperation','isDynamic','globalAlpha','tlng','domain','particleSystem','vertexArray','getV','values','interpolated','normal','east','init','unbindEvent','ymin','none','particlesTextureSize','_createCanvas','frameTime','magnificationFilter','resize','ymax','mouse_down','_animateFrame','fromCache','wheel','shaderProgram','grid','Cesium','udata','outputTexture','particlesTextures','_calc_speedRate','framebuffer','requestAnimationFrame','reCreateWindTextures','length','removeEventListener','tlat','color','hidden','framebuffers','floor','2037133tLehDn','ColorRamp','_maxAge','FUNC_ADD','speedFactor','preExecute','RGBA','sceneMode','dropRate','getContext','strokeStyle','morphComplete','pointer-events','mouseMove','clearCommand','pow','TRIANGLES','BaseLayer','wind','_pointerEvents','DomUtil','original','applyViewerParameters','mouse_move','postMessage','segmentsDepth','show','destoryRenderingFramebuffers','destroy','windTextures','latitude','max','windField','abs','initWorker','drawingBufferHeight','speedRate','max\x20is\x20undefined,\x20calculate\x20max','createRawRenderState','clear','806296EbQiJC','clientWidth','requestRender','execute','SceneMode','wrapS','call','getPrimitives','4547175moOCmi','bounds','scene','lat','pass','setDate','toRadians','_colorRamp','postProcessingPosition','getCalculateSpeedShader','canvasContext','minificationFilter','ellipsoid','toWindowCoordinates','stroke','forEach','Cartesian3','Math','clientHeight','dropRateBump','clearFramebuffers','Compute','redraw','speed','CanvasWindLayer','maxAge','commandList','_randomParticle','lineTo','getParticles','xmin','getDataAtLonLat','vdata','mars3d-canvasWind','isDestroyed','_onMouseUpEvent','primitiveType','_showHook','lonRange','autoClear','uniformMap','reverse','pixelDatatype','440914UzkNBp','cancelAnimationFrame','0px','worker','warn','samplingWindow','componentDatatype','options','fromCssColorString','update','slice','_drawLines','error','getSegmentDrawFragmentShader','bindEvent','push','particlesNumber','beginPath','fromGeometry','FLOAT','west','sqrt','width','canvasHeight','canvasWind','rawRenderState','commandToExecute','192889zMyqSs','EventType','fragmentShaderSource','_tomap','keys','defineProperty','data','particlesTextureSize\x20must\x20be\x20greater\x20than\x200','previousParticlesPosition','_map','rendering','visible','fixedHeight','south','height','sources','2172075wuHWNQ','canvas','SCENE3D','EllipsoidalOccluder','array','_setOptionsHook','nextParticlesPosition','cols','enabled','random','MAX_VALUE','frameRate','computing','cos','lng'];_0x3486=function(){return _0x4cffa3;};return _0x3486();}_0x1d0fd3[_0x130459(0x1bd,0x1a4)]=0x1,_0x1d0fd3[_0x130459(0x208,0x1d4)]=0x2;const _0x5ab6f5={};_0x5ab6f5[_0x130459(0x1bd,0x170)]=0x14;function _0x2c5470(_0x56c524,_0x1be39e){return _0x449a(_0x56c524-0x3e6,_0x1be39e);}_0x5ab6f5['max']=0x64;const _0x5d988a={};_0x5d988a[_0x2c5470(0x592,0x5f0)]=0x64,_0x5d988a[_0x130459(0x26b,0x1f3)]=0x0,_0x5d988a[_0x130459(0x27e,0x23c)]=_0x1d0fd3,_0x5d988a['lineLength']=_0x5ab6f5,_0x5d988a['speedFactor']=0x1,_0x5d988a[_0x130459(0x1f1,0x1ee)]=0.003,_0x5d988a[_0x2c5470(0x5f0,0x5b0)]=0.001,_0x5d988a[_0x130459(0x195,0x211)]=[_0x2c5470(0x646,0x67f)],_0x5d988a['flipY']=![],_0x5d988a['dynamic']=!![];const DEF_OPTIONS=_0x5d988a;class WindLayer extends BaseLayer$1{constructor(_0xae12c3={}){_0xae12c3={...DEF_OPTIONS,..._0xae12c3},super(_0xae12c3);function _0x4f4696(_0x9552b0,_0x584848){return _0x2c5470(_0x9552b0- -0x26e,_0x584848);}this[_0x4f4696(0x3ca,0x3ea)](_0xae12c3,_0xae12c3);}get['layer'](){return this['primitives'];}get['data'](){function _0x403d21(_0x6231cc,_0x553377){return _0x130459(_0x6231cc-0x2ac,_0x553377);}return this[_0x403d21(0x4f7,0x4a7)]['data'];}set['data'](_0x33ea97){this['options']['data']=_0x33ea97,this['setData'](_0x33ea97);}get[_0x2c5470(0x559,0x559)](){return this['options']['colors'];}set['colors'](_0xfdb2e8){this['options']['colors']=_0xfdb2e8;const _0x4d31aa={};_0x4d31aa['colors']=_0xfdb2e8,this['_setOptionsHook'](this['options'],_0x4d31aa);}[_0x130459(0x23e,0x259)](_0x38e347){function _0x305e2a(_0x2785dd,_0x17e1e9){return _0x2c5470(_0x17e1e9- -0x416,_0x2785dd);}_0x38e347?this['_addedHook']():this[_0x305e2a(0x1ce,0x236)]();}[_0x2c5470(0x564,0x59d)](){}[_0x130459(0x29a,0x2ca)](){this['scene']=this[_0x3262c5(-0x13f,-0xe4)]['scene'],this[_0x156d07(-0x217,-0x299)]=this[_0x156d07(-0x14c,-0x1bc)]['camera'];this[_0x156d07(-0x169,-0x1da)][_0x156d07(-0x14f,-0x116)]&&this[_0x156d07(-0x1f9,-0x274)](this[_0x156d07(-0x169,-0x13f)]['data']);if(!this[_0x3262c5(-0x203,-0x1b1)]||!this[_0x3262c5(-0x11f,-0x149)])return;function _0x3262c5(_0x11b327,_0x19a6de){return _0x2c5470(_0x19a6de- -0x710,_0x11b327);}this['viewerParameters']={'lonRange':new Cesium$1['Cartesian2'](-0xb4,0xb4),'latRange':new Cesium$1['Cartesian2'](-0x5a,0x5a),'pixelSize':0x3e8,'sceneMode':this['scene']['mode']},this['updateViewerParameters'](),this[_0x3262c5(-0x204,-0x189)]=new WindParticleSystem(this['scene'][_0x156d07(-0x121,-0xf3)],this['windData'],this[_0x3262c5(-0x118,-0x101)],this['viewerParameters'],this['scene']);function _0x156d07(_0x47e1df,_0x3bd947){return _0x2c5470(_0x47e1df- -0x778,_0x3bd947);}this['primitives']=this['particleSystem'][_0x3262c5(-0x178,-0x134)](),this['primitives']['forEach'](_0x5de023=>{function _0x49e75b(_0x36c4e7,_0x28abe6){return _0x156d07(_0x28abe6-0x37d,_0x36c4e7);}this['scene'][_0x49e75b(0x154,0x156)]['add'](_0x5de023);}),this[_0x3262c5(-0x1ef,-0x1af)]['percentageChanged']=0.01,this['camera']['changed'][_0x3262c5(-0xab,-0xb7)](this[_0x3262c5(-0x1b8,-0x1c1)][_0x3262c5(-0x12d,-0xc6)](this)),this[_0x156d07(-0x199,-0x118)][_0x156d07(-0x1c0,-0x24a)]['addEventListener'](this['updateViewerParameters'][_0x3262c5(-0x7b,-0xc6)](this)),window['addEventListener']('resize',this[_0x3262c5(-0x147,-0x1c1)]['bind'](this));}['_removedHook'](){this['camera']['changed']['removeEventListener'](this['updateViewerParameters']['bind'](this)),this['scene']['morphComplete']['removeEventListener'](this['updateViewerParameters'][_0x92afc3(0x4a4,0x4a2)](this));function _0x369fec(_0x43cc25,_0x119623){return _0x130459(_0x119623-0x54,_0x43cc25);}window[_0x92afc3(0x401,0x3e7)](_0x92afc3(0x3f0,0x3f3),this['updateViewerParameters']['bind'](this));function _0x92afc3(_0x166d73,_0x6b7284){return _0x130459(_0x166d73-0x21e,_0x6b7284);}this[_0x92afc3(0x3ab,0x31f)]&&(this[_0x369fec(0x198,0x1e1)]['forEach'](_0x278f42=>{this['scene']['primitives']['remove'](_0x278f42);}),delete this['primitives']),this['particleSystem']&&(this['particleSystem'][_0x92afc3(0x423,0x464)](),delete this[_0x369fec(0x276,0x217)]);}['setData'](_0x1c412b,_0xd7242d){this['windData']=this['processWindData'](_0x1c412b);if(_0xd7242d){this[_0x49a4a1(0x1fd,0x289)](),this[_0x49a4a1(0x20f,0x292)]();return;}function _0x2982cc(_0x3d6ed3,_0xdc1c8){return _0x130459(_0xdc1c8-0x17f,_0x3d6ed3);}function _0x49a4a1(_0x86e4f7,_0x2c12d9){return _0x130459(_0x86e4f7- -0x8b,_0x2c12d9);}this['particleSystem']?(this['particleSystem']['computing']['updateWindData'](this[_0x2982cc(0x29e,0x31a)]),this['scene']['requestRender']()):this['_addedHook']();}['_setOptionsHook'](_0x5f32ef,_0x365dc1){function _0x24f66f(_0xff13d0,_0xd9340d){return _0x130459(_0xd9340d- -0x384,_0xff13d0);}function _0x113a1f(_0x2fdd7b,_0x4416fd){return _0x130459(_0x2fdd7b- -0x1b9,_0x4416fd);}this['particleSystem']&&(this['particleSystem'][_0x24f66f(-0x22e,-0x1ed)](_0x365dc1),this['scene'][_0x113a1f(0x5a,0xe)]());}[_0x2c5470(0x644,0x650)](_0xd0eac5){var _0x349d47,_0x3a9252;const _0x354c6c={..._0xd0eac5};function _0x117a89(_0x1097b7,_0x721ded){return _0x130459(_0x721ded-0x3a2,_0x1097b7);}const _0x2dad75=_0x354c6c;!_0x2dad75['height']&&_0x2dad75[_0x3dda7c(0x5a2,0x5ba)]&&(_0x2dad75[_0x3dda7c(0x573,0x5b5)]=_0x2dad75['rows']);!_0x2dad75['width']&&_0x2dad75['cols']&&(_0x2dad75[_0x117a89(0x605,0x5fc)]=_0x2dad75[_0x3dda7c(0x57c,0x5d9)]);function _0x3dda7c(_0xe32e25,_0x5af1e4){return _0x2c5470(_0xe32e25- -0xbe,_0x5af1e4);}!_0x2dad75[_0x3dda7c(0x520,0x516)]&&(_0x2dad75['bounds']={'west':_0x2dad75['xmin'],'south':_0x2dad75['ymin'],'east':_0x2dad75[_0x117a89(0x575,0x534)],'north':_0x2dad75['ymax']});if(!_0x2dad75['u']){const _0x55215b={};_0x55215b['array']=_0xd0eac5['udata'],_0x55215b[_0x3dda7c(0x4c3,0x4ba)]=_0xd0eac5['umin'],_0x55215b[_0x3dda7c(0x50e,0x48f)]=_0xd0eac5['umax'],_0x2dad75['u']=_0x55215b;}if(!_0x2dad75['v']){const _0x5eb975={};_0x5eb975['array']=_0xd0eac5['vdata'],_0x5eb975['min']=_0xd0eac5['vmin'],_0x5eb975[_0x117a89(0x5b3,0x5aa)]=_0xd0eac5['vmax'],_0x2dad75['v']=_0x5eb975;}if(((_0x349d47=_0x2dad75[_0x117a89(0x646,0x5d2)])===null||_0x349d47===void 0x0?void 0x0:_0x349d47[_0x3dda7c(0x4c3,0x546)])===undefined||((_0x3a9252=_0x2dad75[_0x117a89(0x5da,0x5d2)])===null||_0x3a9252===void 0x0?void 0x0:_0x3a9252[_0x117a89(0x593,0x5aa)])===undefined||_0x2dad75['speed']['array']===undefined){const _0x22d2fa={'array':new Float32Array(_0x2dad75['u'][_0x117a89(0x62e,0x615)]['length']),'min':Number[_0x117a89(0x651,0x61b)],'max':Number['MIN_VALUE']};for(let _0x6b14ab=0x0;_0x6b14ab<_0x2dad75['u'][_0x3dda7c(0x579,0x5e2)]['length'];_0x6b14ab++){_0x22d2fa['array'][_0x6b14ab]=Math['sqrt'](_0x2dad75['u']['array'][_0x6b14ab]*_0x2dad75['u']['array'][_0x6b14ab]+_0x2dad75['v'][_0x3dda7c(0x579,0x546)][_0x6b14ab]*_0x2dad75['v']['array'][_0x6b14ab]),_0x22d2fa['array'][_0x6b14ab]!==0x0&&(_0x22d2fa['min']=Math[_0x3dda7c(0x4c3,0x4f8)](_0x22d2fa[_0x117a89(0x57c,0x55f)],_0x22d2fa['array'][_0x6b14ab]),_0x22d2fa[_0x117a89(0x5a6,0x5aa)]=Math[_0x3dda7c(0x50e,0x492)](_0x22d2fa[_0x3dda7c(0x50e,0x4c2)],_0x22d2fa['array'][_0x6b14ab]));}_0x2dad75['speed']=_0x22d2fa;}return _0x2dad75;}['updateViewerParameters'](){var _0x14a7c1;const _0x252c5e=this[_0x4d264a(0x563,0x558)];if(!_0x252c5e)return;const _0x5741b7=_0x252c5e[_0x4d264a(0x5b8,0x5f0)],_0x1ecb3a={};_0x1ecb3a['x']=0x0,_0x1ecb3a['y']=0x0;const _0x107cc4={};_0x107cc4['x']=0x0,_0x107cc4['y']=_0x5741b7['clientHeight'];function _0x4d264a(_0x22822c,_0x401341){return _0x2c5470(_0x22822c- -0x7c,_0x401341);}const _0x13de00={};function _0x2f47f9(_0x57945e,_0x224860){return _0x130459(_0x224860- -0xb6,_0x57945e);}_0x13de00['x']=_0x5741b7['clientWidth'],_0x13de00['y']=0x0;const _0x3373f7={};_0x3373f7['x']=_0x5741b7['clientWidth'],_0x3373f7['y']=_0x5741b7['clientHeight'];const _0x1daf11=[_0x1ecb3a,_0x107cc4,_0x13de00,_0x3373f7];let _0x313c1f=0xb4,_0x11be10=-0xb4,_0x150808=0x5a,_0x10b980=-0x5a,_0x511db3=![];for(const _0x1b80e6 of _0x1daf11){const _0x4b07ef=_0x252c5e['camera']['pickEllipsoid'](new Cesium$1[(_0x2f47f9(0x17b,0x1e1))](_0x1b80e6['x'],_0x1b80e6['y']),_0x252c5e[_0x2f47f9(0x108,0xd3)][_0x2f47f9(0x167,0x16f)]);if(!_0x4b07ef){_0x511db3=!![];break;}const _0x1dca85=_0x252c5e['globe']['ellipsoid'][_0x4d264a(0x4d0,0x558)](_0x4b07ef),_0x283438=Cesium$1['Math'][_0x4d264a(0x5dc,0x60f)](_0x1dca85['longitude']),_0x18702e=Cesium$1[_0x4d264a(0x572,0x505)]['toDegrees'](_0x1dca85[_0x4d264a(0x54f,0x5ca)]);_0x313c1f=Math[_0x4d264a(0x505,0x4e3)](_0x313c1f,_0x283438),_0x11be10=Math[_0x4d264a(0x550,0x5a9)](_0x11be10,_0x283438),_0x150808=Math[_0x4d264a(0x505,0x47e)](_0x150808,_0x18702e),_0x10b980=Math[_0x4d264a(0x550,0x52c)](_0x10b980,_0x18702e);}if(!_0x511db3){const _0x332f60=new Cesium$1[(_0x4d264a(0x5df,0x59d))](Math['max'](this['windData']['bounds']['west'],_0x313c1f),Math[_0x2f47f9(0xe1,0x107)](this[_0x4d264a(0x4e3,0x4c0)]['bounds']['east'],_0x11be10)),_0x15c819=new Cesium$1[(_0x4d264a(0x5df,0x61b))](Math['max'](this[_0x4d264a(0x4e3,0x4c1)][_0x2f47f9(0x122,0x164)]['south'],_0x150808),Math['min'](this['windData'][_0x4d264a(0x562,0x58e)][_0x4d264a(0x4e7,0x529)],_0x10b980)),_0x3d44a5=(_0x332f60['y']-_0x332f60['x'])*0.05,_0x21fdd3=(_0x15c819['y']-_0x15c819['x'])*0.05;_0x332f60['x']=Math['max'](this['windData'][_0x2f47f9(0x1ba,0x164)][_0x2f47f9(0x1e5,0x1a2)],_0x332f60['x']-_0x3d44a5),_0x332f60['y']=Math['min'](this['windData']['bounds']['east'],_0x332f60['y']+_0x3d44a5),_0x15c819['x']=Math[_0x4d264a(0x550,0x534)](this[_0x2f47f9(0x5c,0xe5)]['bounds'][_0x4d264a(0x5b4,0x539)],_0x15c819['x']-_0x21fdd3),_0x15c819['y']=Math[_0x4d264a(0x505,0x493)](this['windData']['bounds']['north'],_0x15c819['y']+_0x21fdd3),this['viewerParameters'][_0x4d264a(0x587,0x535)]=_0x332f60,this['viewerParameters']['latRange']=_0x15c819;const _0x5d59e2=this['windData'][_0x2f47f9(0x195,0x164)]['east']-this['windData']['bounds'][_0x4d264a(0x5a0,0x5fb)],_0x25110c=this[_0x4d264a(0x4e3,0x501)][_0x4d264a(0x562,0x4ea)][_0x4d264a(0x4e7,0x4e2)]-this['windData']['bounds'][_0x4d264a(0x5b4,0x53d)],_0x26b8ac=(_0x332f60['y']-_0x332f60['x'])/_0x5d59e2,_0x4b6b71=(_0x15c819['y']-_0x15c819['x'])/_0x25110c,_0x3500d0=Math[_0x2f47f9(0xbe,0x107)](_0x26b8ac,_0x4b6b71),_0x55667c=0x3e8*_0x3500d0;_0x55667c>0x0&&(this['viewerParameters']['pixelSize']=Math['max'](0x0,Math['min'](0x3e8,_0x55667c)));}this['viewerParameters']['sceneMode']=this[_0x4d264a(0x563,0x514)]['mode'],(_0x14a7c1=this[_0x4d264a(0x50b,0x596)])===null||_0x14a7c1===void 0x0||_0x14a7c1[_0x4d264a(0x547,0x5c7)](this[_0x4d264a(0x4f4,0x4b0)]);}[_0x130459(0x238,0x278)](_0x207d86,_0x47f328){const {bounds:_0x3e630e,width:_0x3b4264,height:_0x974f6d,u:_0x379277,v:_0x229282,speed:_0x5f9330}=this[_0x509038(-0x22c,-0x2a7)],{flipY:_0x51a700}=this[_0x1ecf51(0x47b,0x4cd)];if(_0x207d86<_0x3e630e[_0x509038(-0x16f,-0x14c)]||_0x207d86>_0x3e630e[_0x509038(-0x1fe,-0x200)]||_0x47f328<_0x3e630e['south']||_0x47f328>_0x3e630e[_0x509038(-0x228,-0x204)])return null;const _0x37df0c=(_0x207d86-_0x3e630e['west'])/(_0x3e630e[_0x509038(-0x1fe,-0x1be)]-_0x3e630e['west'])*(_0x3b4264-0x1);let _0x442b9b=(_0x47f328-_0x3e630e['south'])/(_0x3e630e['north']-_0x3e630e[_0x1ecf51(0x54a,0x4ee)])*(_0x974f6d-0x1);_0x51a700&&(_0x442b9b=_0x974f6d-0x1-_0x442b9b);const _0x3bdcb1=Math['floor'](_0x37df0c),_0x372c32=Math['floor'](_0x442b9b),_0x311860=Math['floor'](_0x37df0c),_0x3c3d5b=Math[_0x1ecf51(0x441,0x43f)](_0x311860+0x1,_0x3b4264-0x1),_0x4d76db=Math['floor'](_0x442b9b),_0x2506b6=Math['min'](_0x4d76db+0x1,_0x974f6d-0x1),_0x4a7ae7=_0x37df0c-_0x311860,_0x46aa9c=_0x442b9b-_0x4d76db,_0x249340=_0x372c32*_0x3b4264+_0x3bdcb1,_0x2f5b54=_0x4d76db*_0x3b4264+_0x311860,_0x4ea97a=_0x4d76db*_0x3b4264+_0x3c3d5b,_0x3010ac=_0x2506b6*_0x3b4264+_0x311860,_0x2d4e19=_0x2506b6*_0x3b4264+_0x3c3d5b,_0x4d620a=_0x379277['array'][_0x2f5b54],_0x3c9794=_0x379277[_0x1ecf51(0x494,0x4f5)][_0x4ea97a],_0x141a2d=_0x379277[_0x1ecf51(0x4e2,0x4f5)][_0x3010ac],_0x38bb5f=_0x379277['array'][_0x2d4e19],_0x391297=(0x1-_0x4a7ae7)*(0x1-_0x46aa9c)*_0x4d620a+_0x4a7ae7*(0x1-_0x46aa9c)*_0x3c9794+(0x1-_0x4a7ae7)*_0x46aa9c*_0x141a2d+_0x4a7ae7*_0x46aa9c*_0x38bb5f,_0x439f64=_0x229282['array'][_0x2f5b54],_0x1a1e32=_0x229282[_0x1ecf51(0x578,0x4f5)][_0x4ea97a],_0x2aa4d1=_0x229282[_0x509038(-0x154,-0x19b)][_0x3010ac],_0x2d3ec8=_0x229282['array'][_0x2d4e19],_0x46e1b2=(0x1-_0x4a7ae7)*(0x1-_0x46aa9c)*_0x439f64+_0x4a7ae7*(0x1-_0x46aa9c)*_0x1a1e32+(0x1-_0x4a7ae7)*_0x46aa9c*_0x2aa4d1+_0x4a7ae7*_0x46aa9c*_0x2d3ec8;function _0x1ecf51(_0x50cb8a,_0x53ac8c){return _0x130459(_0x53ac8c-0x282,_0x50cb8a);}const _0x1d6374=Math['sqrt'](_0x391297*_0x391297+_0x46e1b2*_0x46e1b2),_0x1601c5={};_0x1601c5['u']=_0x379277['array'][_0x249340],_0x1601c5['v']=_0x229282['array'][_0x249340],_0x1601c5['speed']=_0x5f9330['array'][_0x249340];const _0x16f050={};function _0x509038(_0x41d95e,_0xdbe379){return _0x130459(_0x41d95e- -0x3c7,_0xdbe379);}_0x16f050['u']=_0x391297,_0x16f050['v']=_0x46e1b2,_0x16f050[_0x509038(-0x197,-0x180)]=_0x1d6374;const _0x1f7a1d={};return _0x1f7a1d[_0x1ecf51(0x462,0x480)]=_0x1601c5,_0x1f7a1d[_0x509038(-0x200,-0x188)]=_0x16f050,_0x1f7a1d;}}function _0x130459(_0x235f25,_0xd6f2b2){return _0x449a(_0x235f25-0x22,_0xd6f2b2);}mars3d__namespace['LayerUtil']['register'](_0x130459(0x1fb,0x284),WindLayer),mars3d__namespace['layer']['WindLayer']=WindLayer;class CanvasParticle{constructor(){this[_0x503914(0x281,0x285)]=null,this['lat']=null,this[_0x503914(0x1fb,0x1c9)]=null;function _0x1aef08(_0x196c83,_0x2ac8f6){return _0x130459(_0x2ac8f6- -0x9,_0x196c83);}function _0x503914(_0x4bf345,_0x4ed70a){return _0x130459(_0x4ed70a-0x8,_0x4bf345);}this['tlat']=null,this['age']=null,this['speed']=null;}['destroy'](){for(const _0xe4e5e3 in this){delete this[_0xe4e5e3];}}}class CanvasWindField{constructor(_0x29a306){this['setOptions'](_0x29a306);}get['speedRate'](){function _0x44df0d(_0x558bd4,_0x46a0c2){return _0x130459(_0x558bd4- -0xa4,_0x46a0c2);}return this[_0x44df0d(0x10d,0x165)];}set[_0x130459(0x20d,0x224)](_0x45b5f1){this[_0x4414da(-0x138,-0x173)]=(0x64-(_0x45b5f1>0x63?0x63:_0x45b5f1))*0x64;function _0x4414da(_0x42fd28,_0x3d500d){return _0x2c5470(_0x3d500d- -0x6e8,_0x42fd28);}function _0x240c10(_0x4a3604,_0x3f5b75){return _0x130459(_0x4a3604-0x171,_0x3f5b75);}this['_calc_speedRate']=[(this[_0x4414da(-0x1e0,-0x192)]-this[_0x4414da(-0x95,-0xed)])/this['_speedRate'],(this['ymax']-this[_0x4414da(-0x158,-0x158)])/this['_speedRate']];}get[_0x130459(0x232,0x1eb)](){function _0x3eb74d(_0xa58c97,_0x3a679a){return _0x130459(_0x3a679a- -0x1b2,_0xa58c97);}return this[_0x3eb74d(0x69,0x39)];}set['maxAge'](_0x405ba0){this['_maxAge']=_0x405ba0;}['setOptions'](_0x2ad42c){this[_0x2287c6(0x1b5,0x1cb)]=_0x2ad42c;function _0x147de8(_0x3afa36,_0xb9a007){return _0x130459(_0x3afa36-0xb6,_0xb9a007);}this['maxAge']=_0x2ad42c['maxAge']||0x78,this[_0x147de8(0x2c3,0x31d)]=_0x2ad42c[_0x2287c6(0x177,0x1d1)]||0x32;function _0x2287c6(_0x18bdd6,_0x2ca96b){return _0x2c5470(_0x18bdd6- -0x45a,_0x2ca96b);}this[_0x2287c6(0x10b,0x12d)]=[];const _0x384812=_0x2ad42c[_0x147de8(0x30a,0x2fd)]||0x1000;for(let _0x5a8582=0x0;_0x5a8582<_0x384812;_0x5a8582++){const _0x5d5a54=this[_0x2287c6(0x19e,0x1c6)](new CanvasParticle());this['particles']['push'](_0x5d5a54);}}['setDate'](_0x35ed41){this[_0x4647b6(0xbe,0xbb)]=_0x35ed41['rows'],this['cols']=_0x35ed41[_0x4647b6(0x98,0xd3)],this['xmin']=_0x35ed41['xmin'],this['xmax']=_0x35ed41['xmax'];function _0x331a08(_0x40b5a2,_0x2d24dd){return _0x130459(_0x40b5a2- -0x404,_0x2d24dd);}this['ymin']=_0x35ed41[_0x4647b6(-0x12,-0x31)];function _0x4647b6(_0x51e434,_0x52cfaf){return _0x2c5470(_0x51e434- -0x5a2,_0x52cfaf);}this[_0x4647b6(-0xb,0x25)]=_0x35ed41[_0x331a08(-0x231,-0x221)],this['grid']=[];const _0x2e24cc=_0x35ed41[_0x331a08(-0x229,-0x241)],_0x270879=_0x35ed41[_0x331a08(-0x1cb,-0x248)];let _0x2fe824=![];_0x2e24cc['length']===this['rows']&&_0x2e24cc[0x0][_0x4647b6(0x4,0x64)]===this[_0x4647b6(0x98,0x5e)]&&(_0x2fe824=!![]);let _0x1f9bb3=0x0,_0x447ef9=null,_0x32c9c1=null;for(let _0x275c41=0x0;_0x275c41<this['rows'];_0x275c41++){_0x447ef9=[];for(let _0x362b9a=0x0;_0x362b9a<this['cols'];_0x362b9a++,_0x1f9bb3++){_0x2fe824?_0x32c9c1=this['_calcUV'](_0x2e24cc[_0x275c41][_0x362b9a],_0x270879[_0x275c41][_0x362b9a]):_0x32c9c1=this[_0x331a08(-0x180,-0x10d)](_0x2e24cc[_0x1f9bb3],_0x270879[_0x1f9bb3]),_0x447ef9[_0x331a08(-0x1b1,-0x159)](_0x32c9c1);}this['grid']['push'](_0x447ef9);}!this[_0x331a08(-0x1b9,-0x1c6)][_0x331a08(-0x24b,-0x216)]&&this[_0x4647b6(-0x5,-0x48)][_0x4647b6(0x64,0x7e)]();}[_0x2c5470(0x5d4,0x59c)](){function _0x3e9652(_0x3b8a84,_0x1597c8){return _0x130459(_0x3b8a84-0x4,_0x1597c8);}delete this['rows'],delete this[_0x5f04af(0x513,0x548)],delete this[_0x3e9652(0x23b,0x1d3)],delete this[_0x3e9652(0x196,0x1a1)];function _0x5f04af(_0x4f0b8c,_0x43af2a){return _0x130459(_0x43af2a-0x2d2,_0x4f0b8c);}delete this[_0x3e9652(0x1d0,0x166)],delete this['ymax'],delete this['grid'],delete this[_0x3e9652(0x1a5,0x188)];}['toGridXY'](_0x517a3c,_0x477710){const _0x616cd6=(_0x517a3c-this['xmin'])/(this['xmax']-this['xmin'])*(this[_0x37d71f(-0x7b,-0xc8)]-0x1);function _0x37d71f(_0x221e31,_0x2851a7){return _0x130459(_0x2851a7- -0x33e,_0x221e31);}const _0x2b3092=(this['ymax']-_0x477710)/(this['ymax']-this['ymin'])*(this['rows']-0x1);return[_0x616cd6,_0x2b3092];}['getUVByXY'](_0x5e2c23,_0x1fa41a){if(_0x5e2c23<0x0||_0x5e2c23>=this['cols']||_0x1fa41a>=this['rows'])return[0x0,0x0,0x0];const _0x675d45=Math[_0x48912a(0x1a,0x8b)](_0x5e2c23),_0x818bf7=Math['floor'](_0x1fa41a);if(_0x675d45===_0x5e2c23&&_0x818bf7===_0x1fa41a)return this['grid'][_0x1fa41a][_0x5e2c23];const _0x91bae2=_0x675d45+0x1,_0x375dc7=_0x818bf7+0x1,_0x55ad89=this['getUVByXY'](_0x675d45,_0x818bf7),_0x23b7b0=this['getUVByXY'](_0x91bae2,_0x818bf7),_0x117a5f=this[_0x556fed(0x295,0x30d)](_0x675d45,_0x375dc7);function _0x556fed(_0xd849e4,_0x58edbf){return _0x2c5470(_0xd849e4- -0x2c0,_0x58edbf);}function _0x48912a(_0x32fc0d,_0x2bc44f){return _0x130459(_0x32fc0d- -0x1ce,_0x2bc44f);}const _0x5d5f49=this[_0x48912a(-0x3d,0xa)](_0x91bae2,_0x375dc7);let _0x45350e=null;try{_0x45350e=this['_bilinearInterpolation'](_0x5e2c23-_0x675d45,_0x1fa41a-_0x818bf7,_0x55ad89,_0x23b7b0,_0x117a5f,_0x5d5f49);}catch(_0x2dfaad){console['log'](_0x5e2c23,_0x1fa41a);}return _0x45350e;}[_0x130459(0x28e,0x30f)](_0x5810c1,_0xd5a0d6,_0x149fbd,_0x11c190,_0x4cf3b7,_0x11a1af){const _0x3c4df4=0x1-_0x5810c1,_0x2798b4=0x1-_0xd5a0d6,_0x41ef24=_0x3c4df4*_0x2798b4,_0x4f5b95=_0x5810c1*_0x2798b4,_0x379231=_0x3c4df4*_0xd5a0d6,_0x545a22=_0x5810c1*_0xd5a0d6,_0x197796=_0x149fbd[0x0]*_0x41ef24+_0x11c190[0x0]*_0x4f5b95+_0x4cf3b7[0x0]*_0x379231+_0x11a1af[0x0]*_0x545a22;function _0x40ab59(_0x5d3a4d,_0x1b074b){return _0x2c5470(_0x1b074b- -0x24d,_0x5d3a4d);}const _0x469881=_0x149fbd[0x1]*_0x41ef24+_0x11c190[0x1]*_0x4f5b95+_0x4cf3b7[0x1]*_0x379231+_0x11a1af[0x1]*_0x545a22;return this[_0x40ab59(0x461,0x3fb)](_0x197796,_0x469881);}[_0x2c5470(0x648,0x608)](_0x260bf3,_0x8b59dd){function _0x15af27(_0x3546f1,_0x4a1897){return _0x2c5470(_0x4a1897- -0x6ad,_0x3546f1);}return[+_0x260bf3,+_0x8b59dd,Math[_0x15af27(-0x70,-0x90)](_0x260bf3*_0x260bf3+_0x8b59dd*_0x8b59dd)];}['getUVByPoint'](_0x46488b,_0x1e27dd){function _0x3f27d3(_0x362209,_0x4e7e25){return _0x130459(_0x4e7e25- -0x32b,_0x362209);}if(!this[_0x2a718a(0x46f,0x4ad)](_0x46488b,_0x1e27dd))return null;function _0x2a718a(_0x2c42da,_0x5defa0){return _0x2c5470(_0x2c42da- -0xff,_0x5defa0);}const _0x54a57e=this['toGridXY'](_0x46488b,_0x1e27dd),_0x46ed64=this[_0x3f27d3(-0x120,-0x19a)](_0x54a57e[0x0],_0x54a57e[0x1]);return _0x46ed64;}['isInExtent'](_0x2113e7,_0x3f9c8c){function _0x4d8534(_0x19a61a,_0x59e570){return _0x130459(_0x59e570- -0x3ca,_0x19a61a);}function _0x441cec(_0x5407af,_0x3937f5){return _0x130459(_0x5407af- -0xea,_0x3937f5);}return _0x2113e7>=this['xmin']&&_0x2113e7<=this[_0x4d8534(-0x23b,-0x238)]&&_0x3f9c8c>=this['ymin']&&_0x3f9c8c<=this[_0x4d8534(-0x1b3,-0x1f7)]?!![]:![];}['getRandomLatLng'](){const _0x3618e4=fRandomByfloat(this['xmin'],this[_0x5c74cd(0x180,0x207)]);function _0x56f902(_0x48ddb8,_0x3d0d47){return _0x2c5470(_0x48ddb8- -0x597,_0x3d0d47);}const _0x5ca296=fRandomByfloat(this['ymin'],this[_0x56f902(0x0,0x32)]),_0x2616f0={};_0x2616f0['lat']=_0x5ca296,_0x2616f0['lng']=_0x3618e4;function _0x5c74cd(_0x173406,_0x4485a4){return _0x2c5470(_0x4485a4- -0x34f,_0x173406);}return _0x2616f0;}['getParticles'](){let _0x490b86,_0x4097b8,_0x30a89e;function _0x11470f(_0xdd127c,_0x4b5464){return _0x2c5470(_0x4b5464- -0xe9,_0xdd127c);}for(let _0x3b5154=0x0,_0x1e6bf9=this[_0x141a01(-0xfe,-0xf3)]['length'];_0x3b5154<_0x1e6bf9;_0x3b5154++){let _0x539f7e=this[_0x141a01(-0xa0,-0xf3)][_0x3b5154];_0x539f7e[_0x141a01(-0xba,-0xfe)]<=0x0&&(_0x539f7e=this['_randomParticle'](_0x539f7e));if(_0x539f7e['age']>0x0){const _0x24c1ac=_0x539f7e['tlng'],_0x2313c6=_0x539f7e['tlat'];_0x30a89e=this[_0x11470f(0x500,0x571)](_0x24c1ac,_0x2313c6),_0x30a89e?(_0x490b86=_0x24c1ac+this[_0x11470f(0x4ff,0x4b9)][0x0]*_0x30a89e[0x0],_0x4097b8=_0x2313c6+this[_0x11470f(0x4e7,0x4b9)][0x1]*_0x30a89e[0x1],_0x539f7e[_0x141a01(-0x7a,-0x17)]=_0x24c1ac,_0x539f7e['lat']=_0x2313c6,_0x539f7e['tlng']=_0x490b86,_0x539f7e['tlat']=_0x4097b8,_0x539f7e['speed']=_0x30a89e[0x2],_0x539f7e['age']--):_0x539f7e[_0x11470f(0x43e,0x471)]=0x0;}}function _0x141a01(_0x5bd3c8,_0x5b67fa){return _0x2c5470(_0x5b67fa- -0x658,_0x5bd3c8);}return this['particles'];}[_0x2c5470(0x5f8,0x65e)](_0x324ab0){let _0x514e77,_0x19d26c;for(let _0x19b80a=0x0;_0x19b80a<0x1e;_0x19b80a++){_0x514e77=this['getRandomLatLng'](),_0x19d26c=this['getUVByPoint'](_0x514e77['lng'],_0x514e77[_0x5a3487(-0x110,-0xb7)]);if(_0x19d26c&&_0x19d26c[0x2]>0x0)break;}if(!_0x19d26c)return _0x324ab0;const _0x15b12a=_0x514e77[_0x27e1c7(-0x1c,-0x4b)]+this[_0x27e1c7(-0xbb,-0x4d)][0x0]*_0x19d26c[0x0],_0x422871=_0x514e77['lat']+this[_0x5a3487(-0x14e,-0x142)][0x1]*_0x19d26c[0x1];function _0x5a3487(_0x395589,_0x29b38d){return _0x130459(_0x395589- -0x32c,_0x29b38d);}_0x324ab0[_0x5a3487(-0xaf,-0x5e)]=_0x514e77['lng'],_0x324ab0['lat']=_0x514e77['lat'],_0x324ab0[_0x27e1c7(-0xd8,-0xec)]=_0x15b12a,_0x324ab0[_0x27e1c7(-0xb5,-0x94)]=_0x422871,_0x324ab0[_0x5a3487(-0x196,-0x18a)]=Math[_0x5a3487(-0x9c,-0xf0)](0xa+Math[_0x5a3487(-0xb4,-0xbd)]()*this[_0x5a3487(-0xfa,-0x113)]);function _0x27e1c7(_0x305102,_0x105ff6){return _0x2c5470(_0x305102- -0x65d,_0x105ff6);}return _0x324ab0['speed']=_0x19d26c[0x2],_0x324ab0;}['destroy'](){for(const _0x5b2cb6 in this){delete this[_0x5b2cb6];}}}function fRandomByfloat(_0x86da6a,_0x27e22c){return _0x86da6a+Math['random']()*(_0x27e22c-_0x86da6a);}const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace['layer'][_0x130459(0x1fa,0x286)];class CanvasWindLayer extends BaseLayer{constructor(_0x2682c2={}){super(_0x2682c2);function _0x160b3f(_0x2b58cb,_0x2de2fc){return _0x130459(_0x2de2fc-0x38e,_0x2b58cb);}this['_setOptionsHook'](_0x2682c2);function _0x40582c(_0x11d53f,_0x566aa5){return _0x130459(_0x566aa5- -0xb6,_0x11d53f);}this[_0x40582c(0x245,0x1ba)]=null,_0x2682c2[_0x160b3f(0x518,0x523)]&&_0x2682c2[_0x40582c(0x14a,0xde)]&&(this[_0x160b3f(0x5a5,0x5ae)]=new mars3d__namespace[(_0x40582c(0xda,0x134))](_0x2682c2));}['_setOptionsHook'](_0x2af0bc,_0x3d78e9){this[_0x192075(0x3c9,0x3f0)]=0x3e8/(_0x2af0bc[_0x202493(-0xec,-0xef)]||0xa),this['_pointerEvents']=this[_0x202493(-0x11b,-0x13f)]['pointerEvents']??![],this['color']=_0x2af0bc[_0x202493(-0x181,-0x194)]||'#ffffff';function _0x202493(_0x59caef,_0x3d0667){return _0x2c5470(_0x59caef- -0x72a,_0x3d0667);}this['lineWidth']=_0x2af0bc['lineWidth']||0x1,this[_0x202493(-0xfb,-0x104)]=_0x2af0bc['fixedHeight']??0x0;function _0x192075(_0x333b73,_0x16ecb1){return _0x2c5470(_0x16ecb1- -0x1a4,_0x333b73);}this['flipY']=_0x2af0bc['flipY']??![],this[_0x202493(-0x15d,-0x17b)]&&this['windField']['setOptions'](_0x2af0bc);}get['layer'](){function _0xdd4a1c(_0x59f32b,_0x9639c3){return _0x2c5470(_0x9639c3- -0x2d1,_0x59f32b);}return this[_0xdd4a1c(0x2de,0x363)];}get['canvasWidth'](){return this['_map']['scene']['canvas']['clientWidth'];}get[_0x130459(0x25b,0x28d)](){return this['_map']['scene']['canvas']['clientHeight'];}get['pointerEvents'](){return this['_pointerEvents'];}set[_0x2c5470(0x567,0x501)](_0x3156de){function _0x1d01cc(_0x252593,_0x410a7a){return _0x2c5470(_0x410a7a- -0x7a6,_0x252593);}this[_0x1d01cc(-0x161,-0x1e6)]=_0x3156de;if(!this['canvas'])return;function _0x4a8a85(_0x1efb3c,_0x4fb0ba){return _0x130459(_0x1efb3c-0x237,_0x4fb0ba);}_0x3156de?this['canvas']['style'][_0x1d01cc(-0x205,-0x1ed)]='all':this['canvas']['style'][_0x1d01cc(-0x211,-0x1ed)]=_0x4a8a85(0x404,0x3de);}get[_0x130459(0x254,0x2d3)](){function _0x1d263b(_0x1ae304,_0x39fe09){return _0x130459(_0x1ae304- -0x32,_0x39fe09);}function _0x43cb64(_0x23ce50,_0xe3df64){return _0x130459(_0x23ce50- -0x139,_0xe3df64);}return this[_0x1d263b(0x219,0x2a5)][_0x1d263b(0x222,0x249)];}set['particlesNumber'](_0x1b162a){this[_0x5576c0(-0x4c,0x7)]['particlesNumber']=_0x1b162a,clearTimeout(this['_canrefresh']);function _0x5576c0(_0x18264f,_0x4fc262){return _0x2c5470(_0x18264f- -0x65b,_0x4fc262);}this['_canrefresh']=setTimeout(()=>{this['redraw']();},0x1f4);}get['speedRate'](){function _0xc250b8(_0x1c1edb,_0x22e3ee){return _0x2c5470(_0x22e3ee- -0x5ed,_0x1c1edb);}return this[_0xc250b8(0x8d,0x22)]['speedRate'];}set[_0x130459(0x20d,0x22d)](_0x1a318b){this[_0x2e85e5(0x20f,0x261)]['speedRate']=_0x1a318b;function _0x2e85e5(_0x57896e,_0x5bb040){return _0x2c5470(_0x5bb040- -0x3ae,_0x57896e);}this['windField']&&(this['windField']['speedRate']=_0x1a318b);}get[_0x2c5470(0x5f6,0x62a)](){return this['options']['maxAge'];}set['maxAge'](_0x55c584){function _0x1a50d2(_0xb0a378,_0x5ef2dd){return _0x130459(_0xb0a378- -0x47,_0x5ef2dd);}this[_0x1a50d2(0x204,0x1ab)][_0x53a532(-0x11f,-0x191)]=_0x55c584;function _0x53a532(_0x513ff3,_0x4b338b){return _0x2c5470(_0x4b338b- -0x787,_0x513ff3);}this[_0x53a532(-0x1ab,-0x1ba)]&&(this['windField']['maxAge']=_0x55c584);}get['data'](){function _0xa3fcc8(_0x1389ac,_0x387096){return _0x2c5470(_0x387096- -0x305,_0x1389ac);}return this[_0xa3fcc8(0x271,0x25a)];}set[_0x130459(0x265,0x2b6)](_0x5cac14){this['setData'](_0x5cac14);}get['rectangle'](){let _0x2a3352=this['windData']['xmin'],_0x2f2155=this[_0x5e9a98(-0x1ca,-0x16e)][_0x3aae41(0x4cd,0x4ff)],_0xf0d3b=this['windData']['ymin'],_0x39e47c=this['windData'][_0x3aae41(0x4ec,0x540)];_0x2f2155>=0x167&&_0x2a3352===0x0&&(_0x2a3352=-0xb4,_0x2f2155=0xb4);_0x2a3352=Math[_0x3aae41(0x4ee,0x575)](_0x2a3352,-0xb4);function _0x3aae41(_0x20e6b6,_0x2ee835){return _0x130459(_0x2ee835-0x36d,_0x20e6b6);}_0x2f2155=Math['min'](_0x2f2155,0xb4);function _0x5e9a98(_0x5df75e,_0x23a8b2){return _0x130459(_0x23a8b2- -0x309,_0x5df75e);}return _0xf0d3b=Math['max'](_0xf0d3b,-0x5a),_0x39e47c=Math[_0x5e9a98(-0x138,-0x14c)](_0x39e47c,0x5a),Cesium[_0x3aae41(0x5d9,0x60b)]['fromDegrees'](_0x2a3352,_0xf0d3b,_0x2f2155,_0x39e47c);}[_0x2c5470(0x602,0x5ec)](_0x4d75dc){function _0x2ff95f(_0x17d5ec,_0x2e7bc4){return _0x2c5470(_0x17d5ec- -0x11c,_0x2e7bc4);}function _0x1680bd(_0x84e2c8,_0x158619){return _0x130459(_0x84e2c8-0x37e,_0x158619);}_0x4d75dc?this['_addedHook']():(this['windData']&&(this['options'][_0x1680bd(0x5e3,0x629)]=this['windData']),this[_0x1680bd(0x606,0x651)]());}['_mountedHook'](){function _0x55c166(_0x2ee164,_0xfeac06){return _0x2c5470(_0x2ee164- -0x13c,_0xfeac06);}function _0x41fb97(_0x123779,_0x1e7c9a){return _0x2c5470(_0x123779- -0x5f9,_0x1e7c9a);}this['options'][_0x55c166(0x4cf,0x4d9)]?this['initWorker']():this['windField']=new CanvasWindField(this[_0x41fb97(0x16,0x0)]);}[_0x130459(0x29a,0x2e6)](){function _0x4b02f4(_0x24e489,_0x4a7177){return _0x130459(_0x24e489-0x70,_0x4a7177);}function _0x749fec(_0x2fbfe6,_0xdab764){return _0x130459(_0xdab764- -0x4c,_0x2fbfe6);}this[_0x749fec(0x2af,0x224)]=this[_0x4b02f4(0x23f,0x2b5)]();const _0x1eea0b={};_0x1eea0b['willReadFrequently']=!![],this[_0x749fec(0x1f2,0x1d7)]=this['canvas'][_0x749fec(0x159,0x1a6)]('2d',_0x1eea0b),this[_0x749fec(0x279,0x206)](),this['options'][_0x749fec(0x1a9,0x219)]&&this['setData'](this['options'][_0x749fec(0x1cc,0x219)]);}[_0x130459(0x288,0x216)](){this['clear'](),this['unbindEvent'](),this['canvas']&&(this['_map']['container']['removeChild'](this['canvas']),delete this['canvas']);}[_0x130459(0x1cf,0x1a3)](){const _0x1aefe0=mars3d__namespace[_0x7984b2(-0x267,-0x1ee)]['create']('canvas',_0x7984b2(-0x1a1,-0x1b1),this[_0x5ef606(0x60a,0x5cc)]['container']);_0x1aefe0[_0x5ef606(0x63b,0x686)]['position']='absolute',_0x1aefe0[_0x5ef606(0x63b,0x5be)]['top']=_0x5ef606(0x5e8,0x59d),_0x1aefe0[_0x7984b2(-0x1a3,-0x152)]['left']=_0x7984b2(-0x214,-0x1a5),_0x1aefe0['style'][_0x7984b2(-0x110,-0x191)]=this[_0x7984b2(-0x197,-0x183)]['scene']['canvas']['clientWidth']+'px',_0x1aefe0['style'][_0x5ef606(0x60f,0x5fe)]=this['_map']['scene']['canvas'][_0x7984b2(-0x1fa,-0x1c0)]+'px';function _0x5ef606(_0x4578e8,_0x4300fb){return _0x130459(_0x4578e8-0x3a2,_0x4300fb);}_0x1aefe0['style']['pointerEvents']=this[_0x5ef606(0x59e,0x530)]?'auto':'none';function _0x7984b2(_0x3fdc32,_0x582d6f){return _0x2c5470(_0x582d6f- -0x7af,_0x3fdc32);}return _0x1aefe0['style']['zIndex']=this[_0x5ef606(0x5ed,0x645)][_0x7984b2(-0x1fc,-0x234)]??0x9,_0x1aefe0['width']=this[_0x7984b2(-0x1d4,-0x183)]['scene'][_0x5ef606(0x612,0x5c4)]['clientWidth'],_0x1aefe0['height']=this[_0x7984b2(-0x101,-0x183)]['scene'][_0x5ef606(0x612,0x638)][_0x7984b2(-0x17b,-0x1c0)],_0x1aefe0;}['resize'](){function _0x59ec5d(_0x2e9e4d,_0x585144){return _0x130459(_0x2e9e4d- -0x305,_0x585144);}function _0x526348(_0x20e061,_0x2b1b18){return _0x130459(_0x2b1b18-0x16a,_0x20e061);}this['canvas']&&(this['canvas']['style'][_0x526348(0x376,0x3c4)]=this[_0x59ec5d(-0x9d,-0x71)][_0x526348(0x3aa,0x385)]['canvas'][_0x526348(0x39a,0x37c)]+'px',this['canvas']['style']['height']=this[_0x59ec5d(-0x9d,-0xa2)]['scene'][_0x59ec5d(-0x95,-0x103)][_0x526348(0x343,0x395)]+'px',this[_0x59ec5d(-0x95,-0x72)][_0x59ec5d(-0xab,-0x70)]=this['_map'][_0x526348(0x310,0x385)]['canvas']['clientWidth'],this['canvas']['height']=this[_0x59ec5d(-0x9d,-0xdd)]['scene']['canvas']['clientHeight']);}[_0x130459(0x252,0x281)](){function _0x4ef8f8(_0x4f5fb2,_0x215ae5){return _0x130459(_0x4f5fb2-0x26e,_0x215ae5);}const _0x557c6f=this;let _0x34826a=Date['now']();function _0x32adbc(_0x37a93e,_0x16f7ad){return _0x2c5470(_0x16f7ad- -0x448,_0x37a93e);}(function _0x1714ee(){if(_0x557c6f['isDestroy'])return;function _0x104b65(_0x471e1d,_0x489b63){return _0x449a(_0x489b63- -0x382,_0x471e1d);}_0x557c6f[_0x104b65(-0x187,-0x1cf)]=window[_0x104b65(-0x17a,-0x1c4)](_0x1714ee);function _0x50149b(_0x54e9e8,_0x166ce8){return _0x449a(_0x54e9e8-0x304,_0x166ce8);}if(_0x557c6f[_0x50149b(0x4e5,0x47d)]&&_0x557c6f['windField']){const _0x17488f=Date[_0x50149b(0x567,0x57a)](),_0x646e10=_0x17488f-_0x34826a;_0x646e10>_0x557c6f['frameTime']&&(_0x34826a=_0x17488f-_0x646e10%_0x557c6f['frameTime'],_0x557c6f[_0x50149b(0x52f,0x4eb)]());}}(),window[_0x32adbc(0x24d,0x211)](_0x32adbc(0x190,0x14e),this[_0x4ef8f8(0x440,0x4a0)]['bind'](this),![]),this['mouse_down']=![],this['mouse_move']=![],this[_0x32adbc(0x248,0x1c7)]['mouseHidden']&&(this['_map']['on'](mars3d__namespace['EventType']['wheel'],this['_onMapWhellEvent'],this),this['_map']['on'](mars3d__namespace[_0x4ef8f8(0x4ce,0x4c8)][_0x32adbc(0x8c,0x10a)],this['_onMouseDownEvent'],this),this['_map']['on'](mars3d__namespace['EventType']['mouseUp'],this['_onMouseUpEvent'],this)));}[_0x130459(0x1cb,0x1bf)](){window[_0x14e75c(0x1b0,0x1af)](this['_animateFrame']);function _0x14e75c(_0x413452,_0x2bc3d9){return _0x130459(_0x413452- -0x95,_0x2bc3d9);}delete this[_0x14e75c(0x140,0xfb)],window['removeEventListener'](_0x14e75c(0x13d,0x1ad),this['resize']);function _0x20a082(_0x49632c,_0x47c642){return _0x2c5470(_0x49632c- -0x476,_0x47c642);}this['options']['mouseHidden']&&(this[_0x14e75c(0x1d3,0x17c)]['off'](mars3d__namespace['EventType'][_0x20a082(0x125,0xf9)],this['_onMapWhellEvent'],this),this['_map']['off'](mars3d__namespace[_0x14e75c(0x1cb,0x214)][_0x20a082(0xdc,0x122)],this[_0x14e75c(0x110,0x140)],this),this[_0x14e75c(0x1d3,0x1b0)][_0x14e75c(0x10f,0x109)](mars3d__namespace[_0x14e75c(0x1cb,0x1f6)]['mouseUp'],this['_onMouseUpEvent'],this),this['_map']['off'](mars3d__namespace['EventType'][_0x20a082(0x144,0x135)],this['_onMouseMoveEvent'],this));}[_0x2c5470(0x643,0x655)](_0x281910){function _0x7639c6(_0x136f22,_0x37c288){return _0x130459(_0x136f22-0x388,_0x37c288);}clearTimeout(this['refreshTimer']);if(!this[_0x4fc540(0xac,0x28)]||!this[_0x7639c6(0x5f8,0x5dd)])return;function _0x4fc540(_0x57c7c1,_0x1dae4d){return _0x130459(_0x57c7c1- -0x157,_0x1dae4d);}this[_0x7639c6(0x5f8,0x5d5)][_0x4fc540(0x142,0x1ae)][_0x7639c6(0x52f,0x53e)]=_0x7639c6(0x56e,0x522),this['refreshTimer']=setTimeout(()=>{if(!this[_0x47deb3(-0x17e,-0x1c6)])return;function _0x47deb3(_0x2dcbeb,_0x5af781){return _0x7639c6(_0x5af781- -0x751,_0x2dcbeb);}this[_0x47deb3(-0x138,-0x19a)]();function _0x1fc5af(_0xe90026,_0x175ad4){return _0x4fc540(_0xe90026-0x24a,_0x175ad4);}this[_0x47deb3(-0xe0,-0x159)][_0x1fc5af(0x38c,0x385)][_0x47deb3(-0x199,-0x222)]='visible';},0xc8);}[_0x130459(0x1a5,0x1a3)](_0x55ca03){this[_0x728b96(0x384,0x38c)]=!![];function _0x1ac1ec(_0x1ec59d,_0x3140cd){return _0x2c5470(_0x1ec59d- -0x1b5,_0x3140cd);}this['_map'][_0x1ac1ec(0x3b3,0x43a)](mars3d__namespace['EventType'][_0x1ac1ec(0x405,0x427)],this['_onMouseMoveEvent'],this);function _0x728b96(_0x568a3b,_0x46aa74){return _0x2c5470(_0x46aa74- -0x20c,_0x568a3b);}this['_map']['on'](mars3d__namespace[_0x728b96(0x403,0x418)]['mouseMove'],this['_onMouseMoveEvent'],this);}['_onMouseMoveEvent'](_0x37fb81){if(!this[_0x4a3806(-0xa4,-0x1a)]||!this[_0x4a3806(-0x37,-0x20)])return;function _0x21cc51(_0x524bf2,_0xf90f75){return _0x130459(_0xf90f75-0x7d,_0x524bf2);}function _0x4a3806(_0x2f2312,_0x5e85e4){return _0x130459(_0x2f2312- -0x2a7,_0x5e85e4);}this['mouse_down']&&(this['canvas']['style'][_0x4a3806(-0x100,-0xf1)]='hidden',this['mouse_move']=!![]);}[_0x130459(0x23c,0x1b3)](_0x535b60){function _0x3e57fd(_0x6334b3,_0x398a0d){return _0x130459(_0x398a0d- -0x3c5,_0x6334b3);}if(!this[_0x3e57fd(-0x1f2,-0x1c2)]||!this[_0x29b344(0x5e2,0x5ba)])return;this['_map'][_0x29b344(0x516,0x586)](mars3d__namespace[_0x29b344(0x5d2,0x627)][_0x29b344(0x568,0x5f4)],this['_onMouseMoveEvent'],this);this['mouse_down']&&this[_0x29b344(0x572,0x5d7)]&&this[_0x29b344(0x5a1,0x547)]();function _0x29b344(_0x1962a6,_0x423cb){return _0x2c5470(_0x1962a6- -0x52,_0x423cb);}this['canvas']['style']['visibility']=_0x29b344(0x5dc,0x5eb),this['mouse_down']=![],this['mouse_move']=![];}['setData'](_0x8ae992){this['clear'](),this['windData']=_0x8ae992,this['windField']['setDate'](_0x8ae992),this['redraw']();}[_0x2c5470(0x5f3,0x5a7)](){if(!this['show'])return;this['windField']['setOptions'](this['options']),this['update']();}[_0x2c5470(0x611,0x636)](){if(this['_updateIng'])return;this['_updateIng']=!![];if(this['worker'])this['windField']['update']();else{const _0x1f24d6=this['windField'][_0x29761e(-0x3a,-0x1)]();this[_0x29761e(0x99,0x18)](_0x1f24d6);}function _0x29761e(_0x105cc3,_0x582346){return _0x2c5470(_0x582346- -0x5fb,_0x105cc3);}function _0x3c6bf8(_0x19e403,_0x3de874){return _0x130459(_0x3de874- -0x274,_0x19e403);}this['_updateIng']=![];}['_drawLines'](_0x2e5e0d){this['_canvasParticles']=_0x2e5e0d,this[_0x4c5abb(-0x1ce,-0x144)][_0x4c5abb(-0x233,-0x225)]='destination-in';function _0x33d70a(_0x5d6444,_0x54f90d){return _0x130459(_0x54f90d-0x5b,_0x5d6444);}this['canvasContext']['fillRect'](0x0,0x0,this['canvasWidth'],this['canvasHeight']),this[_0x4c5abb(-0x1ce,-0x1ea)][_0x4c5abb(-0x233,-0x204)]='lighter',this['canvasContext'][_0x33d70a(0x260,0x21b)]=0.9;function _0x4c5abb(_0x76b73b,_0x3ebc9){return _0x130459(_0x76b73b- -0x3f1,_0x3ebc9);}const _0x2c61e9=this['_map'][_0x4c5abb(-0x1d6,-0x1c7)]['mode']!==Cesium['SceneMode']['SCENE3D'],_0x283563=this[_0x33d70a(0x2e9,0x2e7)]*0.25;if(this['_colorRamp'])for(let _0x17db66=0x0,_0x1c5a46=_0x2e5e0d[_0x33d70a(0x2bb,0x23d)];_0x17db66<_0x1c5a46;_0x17db66++){const _0x11fcf5=_0x2e5e0d[_0x17db66],_0x2c3454=this['_tomap'](_0x11fcf5,_0x11fcf5['lng'],_0x11fcf5[_0x4c5abb(-0x1d5,-0x1d2)],_0x11fcf5['alt']),_0x42a85a=this[_0x4c5abb(-0x18f,-0x13c)](_0x11fcf5,_0x11fcf5[_0x4c5abb(-0x230,-0x23c)],_0x11fcf5['tlat'],_0x11fcf5['talt']);if(!_0x2c3454||!_0x42a85a)continue;if(_0x2c61e9&&Math['abs'](_0x2c3454[0x0]-_0x42a85a[0x0])>=_0x283563)continue;this['canvasContext'][_0x33d70a(0x2aa,0x2b0)](),this[_0x4c5abb(-0x1ce,-0x188)]['lineWidth']=this[_0x33d70a(0x306,0x2d9)],this[_0x4c5abb(-0x1ce,-0x16b)][_0x4c5abb(-0x1fe,-0x1d2)]=this[_0x4c5abb(-0x1d1,-0x207)]['getColor'](_0x11fcf5['speed']),this['canvasContext']['moveTo'](_0x2c3454[0x0],_0x2c3454[0x1]),this['canvasContext'][_0x33d70a(0x20f,0x290)](_0x42a85a[0x0],_0x42a85a[0x1]),this['canvasContext']['stroke']();}else{this['canvasContext'][_0x4c5abb(-0x19c,-0x1d5)](),this[_0x4c5abb(-0x1ce,-0x16b)][_0x33d70a(0x29b,0x2d9)]=this['lineWidth'],this[_0x33d70a(0x236,0x27e)]['strokeStyle']=this[_0x4c5abb(-0x20c,-0x262)];for(let _0xc71d9c=0x0,_0x35371b=_0x2e5e0d[_0x33d70a(0x231,0x23d)];_0xc71d9c<_0x35371b;_0xc71d9c++){const _0x4d6668=_0x2e5e0d[_0xc71d9c],_0xc36f17=this[_0x4c5abb(-0x18f,-0x182)](_0x4d6668,_0x4d6668['lng'],_0x4d6668['lat'],_0x4d6668[_0x4c5abb(-0x246,-0x23c)]),_0x4d60a8=this['_tomap'](_0x4d6668,_0x4d6668['tlng'],_0x4d6668['tlat'],_0x4d6668['talt']);if(!_0xc36f17||!_0x4d60a8)continue;if(_0x2c61e9&&Math[_0x33d70a(0x21c,0x265)](_0xc36f17[0x0]-_0x4d60a8[0x0])>=_0x283563)continue;this['canvasContext'][_0x33d70a(0x1ba,0x213)](_0xc36f17[0x0],_0xc36f17[0x1]),this['canvasContext']['lineTo'](_0x4d60a8[0x0],_0x4d60a8[0x1]);}this['canvasContext'][_0x4c5abb(-0x1ca,-0x218)]();}}['_tomap'](_0x1115ad,_0x44db65,_0x1033fe,_0x59e2b1){function _0x228aba(_0x217caa,_0x5c78b1){return _0x130459(_0x217caa-0x13b,_0x5c78b1);}const _0x5761ed=Cesium[_0x228aba(0x364,0x374)]['fromDegrees'](_0x44db65,_0x1033fe,_0x59e2b1??this[_0x228aba(0x3a6,0x404)]),_0x340798=this['_map']['scene'];if(_0x340798['mode']===Cesium[_0x228aba(0x350,0x334)][_0x228aba(0x3ac,0x426)]){const _0x51bfeb=new Cesium[(_0x2d26cd(0x259,0x290))](_0x340798['globe']['ellipsoid'],_0x340798[_0x2d26cd(0x184,0x10b)][_0x228aba(0x2d3,0x329)]),_0x8f5033=_0x51bfeb['isPointVisible'](_0x5761ed);if(!_0x8f5033)return _0x1115ad['age']=0x0,null;}function _0x2d26cd(_0x23080b,_0x28556c){return _0x130459(_0x23080b- -0x19,_0x28556c);}const _0x13ff5b=mars3d__namespace['PointTrans'][_0x228aba(0x361,0x3ce)](this['_map']['scene'],_0x5761ed);return _0x13ff5b?[_0x13ff5b['x'],_0x13ff5b['y']]:null;}['clear'](){function _0x571f54(_0x265494,_0x4e4932){return _0x2c5470(_0x4e4932- -0x4f9,_0x265494);}this['windField']['clear'](),delete this[_0x571f54(0x0,0x66)];}[_0x2c5470(0x5cf,0x643)](){this['worker']=new Worker(this['options']['worker']),this['worker']['onmessage']=_0x33e368=>{this['_drawLines'](_0x33e368['data'][_0x169705(-0x19c,-0x1eb)]);function _0x169705(_0x3d454a,_0x5d9855){return _0x449a(_0x3d454a- -0x31b,_0x5d9855);}this['_updateIng2']=![];},this[_0x1c4b77(-0x186,-0x140)]={'init':_0x237ff6=>{function _0x3c5f85(_0x546e7d,_0x1cc345){return _0x1c4b77(_0x546e7d,_0x1cc345-0x23d);}const _0x4c4906={};_0x4c4906[_0x3c5f85(0x106,0x17b)]=_0x2ce8ae(0x3a4,0x39a);function _0x2ce8ae(_0x2ee13e,_0x5d56b0){return _0x1c4b77(_0x5d56b0,_0x2ee13e-0x523);}_0x4c4906['options']=_0x237ff6,this[_0x3c5f85(0x190,0x13b)][_0x3c5f85(0x111,0xf5)](_0x4c4906);},'setOptions':_0x24a0e9=>{const _0x9aefd5={};function _0x450b87(_0x2ba7f5,_0x40fd50){return _0x1c4b77(_0x40fd50,_0x2ba7f5-0x5f7);}_0x9aefd5[_0x450b87(0x535,0x5a8)]='setOptions',_0x9aefd5['options']=_0x24a0e9,this['worker']['postMessage'](_0x9aefd5);},'setDate':_0x31bd=>{const _0x233d0e={};_0x233d0e[_0x18b869(0xb3,0xbd)]=_0x4f7d5d(0x5e0,0x57d);function _0x18b869(_0x51051a,_0x5a9629){return _0x1c4b77(_0x5a9629,_0x51051a-0x175);}_0x233d0e['data']=_0x31bd;function _0x4f7d5d(_0x5ec1fe,_0x65ce7){return _0x1c4b77(_0x65ce7,_0x5ec1fe-0x70b);}this['worker'][_0x4f7d5d(0x5c3,0x5e7)](_0x233d0e);},'update':()=>{function _0x15c2d8(_0x2981f8,_0x21ae66){return _0x1c4b77(_0x2981f8,_0x21ae66-0x3ac);}if(this['_updateIng2'])return;this[_0x1ead17(-0x261,-0x2e4)]=!![];const _0x392e5d={};_0x392e5d[_0x1ead17(-0x174,-0x158)]='update';function _0x1ead17(_0x5d1375,_0x47a174){return _0x1c4b77(_0x47a174,_0x5d1375- -0xb2);}this['worker'][_0x1ead17(-0x1fa,-0x1eb)](_0x392e5d);},'clear':()=>{const _0x14ec5b={};function _0x3637a6(_0x512d43,_0x2e3ae7){return _0x1c4b77(_0x512d43,_0x2e3ae7- -0x8a);}_0x14ec5b['type']='clear',this[_0x3637a6(-0x186,-0x18c)]['postMessage'](_0x14ec5b);}};function _0x1c4b77(_0x8a6489,_0x4c256d){return _0x130459(_0x4c256d- -0x349,_0x8a6489);}this['windField']['init'](this['options']);}}mars3d__namespace['LayerUtil']['register'](_0x130459(0x25c,0x1db),CanvasWindLayer),mars3d__namespace['layer'][_0x2c5470(0x5f5,0x675)]=CanvasWindLayer,mars3d__namespace['CanvasWindField']=CanvasWindField,mars3d__namespace['Log']['logInfo'](_0x2c5470(0x557,0x543)),mars3d__namespace['WindUtil']=WindUtil,exports['CanvasWindField']=CanvasWindField,exports['CanvasWindLayer']=CanvasWindLayer,exports['WindLayer']=WindLayer,exports[_0x2c5470(0x553,0x4dd)]=WindUtil;const _0x4883f2={};_0x4883f2['value']=!![],Object[_0x2c5470(0x628,0x5d5)](exports,_0x130459(0x18c,0x112),_0x4883f2);
}));
