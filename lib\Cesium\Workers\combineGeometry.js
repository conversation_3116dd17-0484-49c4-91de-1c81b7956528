/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.132
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as e}from"./chunk-IUDH6UEG.js";import{a as r}from"./chunk-HTCWAAZA.js";import"./chunk-W6HHAHPN.js";import"./chunk-D6462WKK.js";import"./chunk-6FDKAUDV.js";import"./chunk-HZRI4TWB.js";import"./chunk-3UI62PNL.js";import"./chunk-5HEB7B66.js";import"./chunk-GCGQM3V5.js";import"./chunk-DZ42UGGL.js";import"./chunk-UXDFNPCW.js";import"./chunk-QNF6DOIT.js";import"./chunk-RVXELCI5.js";import"./chunk-S2VOP6QV.js";import"./chunk-HEWRROTS.js";import"./chunk-W2IXI6NO.js";import"./chunk-ZO574IGQ.js";import"./chunk-YQWHD5N7.js";import"./chunk-4AYOP6XD.js";import"./chunk-F4R6NSH4.js";function n(o,t){let m=e.unpackCombineGeometryParameters(o),i=e.combineGeometry(m);return e.packCombineGeometryResults(i,t)}var c=r(n);export{c as default};
