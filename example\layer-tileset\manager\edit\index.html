<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0" />
    <meta name="author" content="火星科技 http://mars3d.cn " />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="x5-fullscreen" content="true" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />

    <!-- 标题及搜索关键字 -->
    <meta name="keywords" content="火星科技,cesium,3D,GIS,marsgis,三维,地球,地图,开发,框架,系统,示例,资料,模型,离线,外包,合肥,安徽,中国" />
    <meta
      name="description"
      content="火星科技 合肥火星 合肥火星科技 合肥火星科技有限公司 leaflet leaflet框架 leaflet开发 cesium cesium开发 cesium框架 三维 地球 模型  gis marsgis 地图离线 地图开发 地图框架 地图外包 框架 开发 外包  地图离线 二维地图 三维地图 全景漫游 地理信息系统 云GIS 三维GIS GIS平台 WebGIS"
    />

    <link rel="shortcut icon" type="image/x-icon" href="/img/favicon/favicon.ico" />
    <title>3dtiles模型位置及参数编辑 | Mars3D | 三维地图 | 火星科技 | 合肥火星科技有限公司</title>

    <!--第三方lib-->
    <script
      type="text/javascript"
      src="/lib/include-lib.js"
      libpath="/lib/"
      include="jquery,layer,toastr,font-awesome,web-icons,bootstrap,bootstrap-select,bootstrap-checkbox,bootstrap-slider,toastr,jstree,layer,haoutil,mars3d,localforage"
    ></script>

    <link href="/css/style.css" rel="stylesheet" />
  </head>

  <body class="dark">
    <div id="mars3dContainer" class="mars3d-container"></div>

    <!-- 面板 -->
    <div class="infoview infoview-left">
      <table class="mars-table">
        <tr>
          <td>模型URL：</td>
          <td colspan="4">
            <input id="txtModel" type="text" value="//data.mars3d.cn/3dtiles/max-fsdzm/tileset.json" class="form-control" style="width: 100%" />
          </td>

          <td>
            <div class="checkbox checkbox-primary checkbox-inline" title="解决跨域问题">
              <input id="chkProxy" class="styled" type="checkbox" />
              <label for="chkProxy"> 代理 </label>
            </div>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <input type="button" class="btn btn-primary" value="加载模型" onclick="resetInput();loadTilesModel();" />
          </td>
        </tr>
        <tr>
          <td class="transform">经度：</td>
          <td class="transform">
            <input
              id="txt_x"
              class="form-control"
              type="number"
              min="-180"
              max="180"
              step="0.000001"
              style="width: 120px"
              value="121.479394"
              onchange="changetilesObjectData()"
            />
          </td>
          <td class="transform">纬度：</td>
          <td class="transform">
            <input
              id="txt_y"
              class="form-control"
              type="number"
              min="-90"
              max="90"
              step="0.000001"
              style="width: 110px"
              value="29.791416"
              onchange="changetilesObjectData()"
            />
          </td>
          <td>高度：</td>
          <td>
            <input id="txt_z" class="form-control" type="number" step="0.1" style="width: 100px" value="0" onchange="changetilesObjectData()" />
            <div class="checkbox checkbox-primary checkbox-inline">
              <input id="chkTestTerrain" class="styled" type="checkbox" />
              <label for="chkTestTerrain"> 深度检测 </label>
            </div>
          </td>
        </tr>

        <tr class="transform">
          <td>绕X轴旋转模型：</td>
          <td title="绕x轴旋转模型">
            <input
              id="txt_rotation_x"
              class="form-control"
              type="number"
              style="width: 110px"
              min="-360"
              max="360"
              step="0.1"
              value="0"
              onchange="changetilesObjectData()"
            />
          </td>

          <td>绕Y轴旋转模型：</td>
          <td title="绕y轴旋转模型">
            <input
              id="txt_rotation_y"
              class="form-control"
              type="number"
              style="width: 100px"
              min="-360"
              max="360"
              step="0.1"
              value="0"
              onchange="changetilesObjectData()"
            />
          </td>

          <td>绕Z轴旋转模型：</td>
          <td title="绕z轴旋转模型">
            <input
              id="txt_rotation_z"
              class="form-control"
              type="number"
              style="width: 120px"
              min="-360"
              max="360"
              step="0.1"
              value="0"
              onchange="changetilesObjectData()"
            />
          </td>
        </tr>

        <tr class="transform">
          <td>变换垂直轴</td>
          <td>
            <select id="txt_axis" class="selectpicker form-control">
              <option value="" selected="selected">默认</option>
              <option value="Z_UP_TO_X_UP">Z轴 -&gt;X轴</option>
              <option value="Z_UP_TO_Y_UP">Z轴 -&gt;Y轴</option>
              <option value="X_UP_TO_Y_UP">X轴 -&gt;Y轴</option>
              <option value="X_UP_TO_Z_UP">X轴 -&gt;Z轴</option>
              <option value="Y_UP_TO_X_UP">Y轴 -&gt;X轴</option>
              <option value="Y_UP_TO_Z_UP">Y轴 -&gt;Z轴</option>
            </select>
          </td>

          <td colspan="2">
            &nbsp;&nbsp;&nbsp;&nbsp;
            <div class="checkbox checkbox-primary checkbox-inline transform">
              <input id="highlightEnable" class="styled" type="checkbox" />
              <label for="highlightEnable">
                <b>单击高亮构件</b>
              </label>
            </div>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <div class="checkbox checkbox-primary checkbox-inline transform">
              <input id="popupEnable" checked class="styled" type="checkbox" />
              <label for="popupEnable">
                <b>单击popup弹窗</b>
              </label>
            </div>
          </td>
        </tr>

        <tr>
          <td>缩放比例：</td>
          <td>
            <input
              id="txt_scale"
              class="form-control"
              type="number"
              style="width: 110px"
              min="0.1"
              max="100"
              step="0.1"
              value="1"
              onchange="changetilesObjectData()"
            />
          </td>
          <td>显示精度：</td>
          <td>
            <input id="txt_maximumScreenSpaceError" title="显示精度" />
          </td>
          <td>透明度：</td>
          <td>
            <input id="txt_opacity" title="透明度：" />
          </td>
        </tr>
      </table>

      <input type="button" class="btn btn-primary" value="视角定位至模型" onclick="locate()" />
      <input type="button" class="btn btn-primary transform" value="查看构件" onclick="updateSceneTree()" />

      <input type="button" class="btn btn-primary" value="保存参数" onclick="savemark()" />
    </div>

    <div id="viewReset" class="infoview" style="overflow: auto; left: 10px; top: 280px; width: 500px; height: calc(100% - 300px); display: none">
      <button id="btn_close" class="btn btn-default" style="margin: 5px 20px">关闭</button>
      <button id="btn_back" class="btn btn-primary" style="margin: 5px 20px; display: none">取消选中</button>
      <ul id="treeOverlays" style="padding: 0"></ul>
    </div>

    <script src="/js/common.js"></script>

    <script src="./map.js"></script>

    <script type="text/javascript">
      "use script" //开发环境建议开启严格模式

      let tilesObject

      eventTarget.on("tiles3dLayerLoad", function (event) {
        const tiles3dLayer = event.layer
        console.log("模型加载完成", tiles3dLayer)

        // 取模型中心点信息
        const locParams = tiles3dLayer.position

        if (locParams.alt < -1000 || locParams.alt > 10000) {
          locParams.alt = 0 // 高度异常数据，自动赋值高度为0
        }

        $("#txt_x").val(locParams.lng)
        $("#txt_y").val(locParams.lat)
        $("#txt_z").val(locParams.alt)

        $("#txt_maximumScreenSpaceError").slider("setValue", tiles3dLayer.maximumScreenSpaceError ?? 16)

        if (tiles3dLayer.transform) {
          $("#txt_rotation_z").val(tiles3dLayer.rotation.z)
          $("#txt_rotation_x").val(tiles3dLayer.rotation.x)
          $("#txt_rotation_y").val(tiles3dLayer.rotation.y)

          $("#txt_scale").val(tiles3dLayer.scale || 1)
          $("#txt_axis").val(tiles3dLayer.axis)
        } else {
          updateHeightForSurfaceTerrain(locParams)
        }
      })

      // 根据改变的位置触发不同的事件
      eventTarget.on("changePoition", function (event) {
        $("#txt_x").val(event.center.lng)
        $("#txt_y").val(event.center.lat)
        $("#txt_z").val(event.center.alt)

        $("#txt_rotation_z").val(event.rotation.z)
        $("#txt_rotation_x").val(event.rotation.x)
        $("#txt_rotation_y").val(event.rotation.y)
      })

      eventTarget.on("changeHeight", function (event) {
        $("#txt_z").val(event.alt)
      })

      eventTarget.on("historyUrl", function (event) {
        let url = event.url || "//data.mars3d.cn/3dtiles/max-fsdzm/tileset.json"
        $("#txtModel").val(url)
        showModel(url)
      })

      function initUI(options) {
        changetilesObjectData(false)

        $("#chkTestTerrain").change(function () {
          let val = $(this).is(":checked")

          updateDepthTest(val)
        })

        //单击高亮构件
        $("#highlightEnable").change(function () {
          let val = $(this).is(":checked")

          changetilesObjectData()
        })

        //单击popup弹窗
        $("#popupEnable").change(function () {
          let val = $(this).is(":checked")

          changetilesObjectData()
        })

        //滑动条
        $("#txt_maximumScreenSpaceError")
          .slider({ min: 1, max: 30.0, step: 1, value: 8.0 })
          .on("change", (e) => {
            changetilesObjectData()
          })

        $("#txt_opacity")
          .slider({ min: 0.1, max: 1.0, step: 0.1, value: 1.0 })
          .on("change", (e) => {
            changetilesObjectData()
          })

        //树控件
        initSceneTree()

        //加载数据
        // showModel($("#txtModel").val())
      }

      function loadTilesModel() {
        showModel($("#txtModel").val())
        changetilesObjectData(false) // 记录历史数据
      }

      function changetilesObjectData(upDate = true) {
        tilesObject = {
          name: "模型名称",
          type: "3dtiles",
          url: $("#txtModel").val(),
          maximumScreenSpaceError: $("#txt_maximumScreenSpaceError").val(), // 【重要】数值加大，能让最终成像模糊
          position: {
            lng: $("#txt_x").val(),
            lat: $("#txt_y").val(),
            alt: $("#txt_z").val()
          },
          rotation: {
            z: $("#txt_rotation_z").val(),
            x: $("#txt_rotation_x").val(),
            y: $("#txt_rotation_y").val()
          },
          scale: $("#txt_scale").val(),
          axis: $("#txt_axis").val() ? $("#txt_axis").val() : undefined,
          proxy: $("#chkProxy").is(":checked") ? "//server.mars3d.cn/proxy/" : undefined, // 代理
          opacity: $("#txt_opacity").val(),
          highlight: $("#highlightEnable").is(":checked")
            ? {
                type: "click",
                outlineEffect: true,
                color: "#00FF00"
              }
            : false,
          popup: $("#popupEnable").is(":checked") ? "all" : false,
          show: true
        }
        if (upDate) {
          setLayerOptions(tilesObject)
        }
      }

      function resetInput() {
        $("#txt_opacity").slider("setValue", 1.0)

        $("#highlightEnable").attr("checked", false)
        $("#popupEnable").attr("checked", true)
      }

      function savemark() {
        saveBookmark(tilesObject)
      }

      //==============显示构件树处理==============================
      function initSceneTree() {
        $("#btn_back").click(function (e) {
          tiles3dLayer.tileset.style = undefined
          $(this).hide()
        })
        $("#btn_close").click(function (e) {
          $("#viewReset").hide()
        })
        $("#treeOverlays").on("changed.jstree", function (e, data) {
          let node = data.node.original
          if (node && node.sphere) {
            compModelChange(node.eleid, node.sphere)

            $("#btn_back").show()
          }
        })
      }

      function updateSceneTree() {
        $("#viewReset").hide()
        let url = $("#txtModel").val()

        showCompTree(url)
      }

      eventTarget.on("compTree", function (event) {
        $("#treeOverlays").data("jstree", false).empty()

        $("#treeOverlays").jstree({
          core: {
            data: event.data,
            themes: {
              name: "default-dark",
              dots: true,
              icons: true
            }
          }
        })
        $("#viewReset").show()
      })
    </script>
  </body>
</html>
