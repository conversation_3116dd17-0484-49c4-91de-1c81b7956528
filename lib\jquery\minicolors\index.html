<!DOCTYPE html>
<html>
<head>
  <title>jQuery MiniColors</title>
  <meta charset="utf-8">

  <!-- jQuery -->
    <script src="../jquery-2.1.4.min.js"></script>

  <!-- Bootstrap 3 -->
  <link rel="stylesheet" href="../../bootstrap/bootstrap.css">
  <script src="../../bootstrap/bootstrap.min.js"></script>

  <!-- MiniColors -->
  <script src="jquery.minicolors.js"></script>
  <link rel="stylesheet" href="jquery.minicolors.css">

  <style>
    dl {
      margin: 20px 0;
    }
    dt {
      font-size: 120%;
    }
    dd {
      padding: 10px 20px 20px 20px;
    }
    dd:last-child {
      border-bottom: none;
    }
    code {
      color: black;
      border: none;
      background: rgba(128, 128, 128, .1);
    }
    [dir=rtl] code {
      direction: ltr;
    }
    pre {
      background: #f8f8f8;
      border: none;
      color: #333;
      padding: 20px;
    }
    [dir=rtl] pre {
      direction: ltr;
    }
    h2 {
      margin-top: 50px;
    }
    h3 {
      color: #aaa;
    }
    .jumbotron {
      padding: 40px;
    }
    .jumbotron h1 {
      margin-top: 0;
    }
    .jumbotron p:last-child {
      margin-bottom: 0;
    }
  </style>

  <script>
    $(document).ready( function() {

      $('.demo').each( function() {
        //
        // Dear reader, it's actually very easy to initialize MiniColors. For example:
        //
        //  $(selector).minicolors();
        //
        // The way I've done it below is just for the demo, so don't get confused
        // by it. Also, data- attributes aren't supported at this time...they're
        // only used for this demo.
        //
        $(this).minicolors({
          control: $(this).attr('data-control') || 'hue',
          defaultValue: $(this).attr('data-defaultValue') || '',
          format: $(this).attr('data-format') || 'hex',
          keywords: $(this).attr('data-keywords') || '',
          inline: $(this).attr('data-inline') === 'true',
          letterCase: $(this).attr('data-letterCase') || 'lowercase',
          opacity: $(this).attr('data-opacity'),
          position: $(this).attr('data-position') || 'bottom',
          swatches: $(this).attr('data-swatches') ? $(this).attr('data-swatches').split('|') : [],
          change: function(value, opacity) {
            if( !value ) return;
            if( opacity ) value += ', ' + opacity;
            if( typeof console === 'object' ) {
              console.log(value);
            }
          },
          theme: 'bootstrap'
        });

      });

    });
  </script>
</head>
<body>
  <div class="row" style="margin: 40px 40px;">
    <div class="col-12">

      <!-- Intro -->
      <div id="intro" class="jumbotron">
        <h1>jQuery MiniColors</h1>
        <p class="text-muted">
          Now with Bootstrap 3 support!
        </p>
        <p>
          A project by <a href="https://www.abeautifulsite.net/">A Beautiful Site</a>,
          originally developed for <a href="https://www.surrealcms.com/">Surreal CMS</a>.
        </p>
      </div>

      <!-- Contents -->
      <h2 id="contents">Contents</h2>
      <ul>
        <li><a href="#download">Download</a></li>
        <li><a href="#demos">Demos</a></li>
        <li><a href="#api">API</a>
          <ul>
            <li><a href="#instantiation">Instantiation</a></li>
            <li><a href="#settings">Settings</a></li>
            <li><a href="#methods">Methods</a></li>
            <li><a href="#events">Events</a></li>
          </ul>
        </li>
        <li><a href="#license">License</a></li>
      </ul>

      <!-- Download -->
      <h2 id="download">Download</h2>
      <p>
        This project is on GitHub. Feel free to post bug reports, feature requests, and code
        improvements on the official project page.
      </p>
      <p>
        <a href="https://github.com/claviska/jquery-minicolors" class="btn btn-success">Download on GitHub</a>
      </p>

      <!-- Demos -->
      <h2 id="demos">Demos</h2>
      <p>
        This is the main demo page, which uses <a href="https://getbootstrap.com/">Bootstrap 3</a>,
        but this plugin works without Bootstrap as well.
      </p>
      <p>
        <a href="without-bootstrap.html" class="btn btn-primary">View Demo Without Bootstrap</a>
      </p>
      <p>
        <a class="btn btn-primary" onclick="$(body).attr('dir')=='rtl'?$(body).removeAttr('dir'):$(body).attr('dir', 'rtl')">Toggle LTR/RTL</a>
      </p>

      <!-- Control types -->
      <h3>Control Types</h3>
      <div class="well">
        <div class="row">
          <div class="col-lg-4 col-sm-4 col-12">

            <div class="form-group">
              <label for="hue-demo">Hue (default)</label>
              <input type="text" id="hue-demo" class="form-control demo" data-control="hue" value="#ff6161">
            </div>
            <div class="form-group">
              <label for="saturation-demo">Saturation</label>
              <input type="text" id="saturation-demo" class="form-control demo" data-control="saturation" value="#0088cc">
            </div>
          </div>

          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="brightness-demo">Brightness</label>
              <input type="text" id="brightness-demo" class="form-control demo" data-control="brightness" value="#00ffff">
            </div>
            <div class="form-group">
              <label for="wheel-demo">Wheel</label>
              <input type="text" id="wheel-demo" class="form-control demo" data-control="wheel" value="#ff99ee">
            </div>
          </div>
        </div>
      </div>

      <!-- Input modes -->
      <h3>Input Modes</h3>
      <div class="well">
        <div class="row">
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="text-field">Text field</label>
              <br>
              <input type="text" id="text-field" class="form-control demo" value="#70c24a">
            </div>
            <div class="form-group">
              <label for="hidden-input">Hidden Input</label>
              <br>
              <input type="hidden" id="hidden-input" class="demo" value="#db913d">
            </div>
          </div>
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="inline">Inline</label>
              <br>
              <input type="text" id="inline" class="form-control demo" data-inline="true" value="#4fc8db">
            </div>
          </div>
        </div>
      </div>

      <!-- Positions -->
      <h3>Positions</h3>
      <div class="well">
        <p>
          Valid positions include <code>bottom left</code>, <code>bottom right</code>, <code>top
          left</code>, and <code>top right</code>.
        </p>
        <div class="row">
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="position-bottom-left">bottom left (default)</label>
              <input type="text" id="position-bottom-left" class="form-control demo" data-position="bottom left" value="#0088cc">
            </div>
            <div class="form-group">
              <label for="position-top-left">top left</label>
              <input type="text" id="position-top-left" class="form-control demo" data-position="top left" value="#0088cc">
            </div>
          </div>
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="position-bottom-right">bottom right</label>
              <input type="text" id="position-bottom-right" class="form-control demo" data-position="bottom right" value="#0088cc">
            </div>
            <div class="form-group">
              <label for="position-top-right">top right</label>
              <input type="text" id="position-top-right" class="form-control demo" data-position="top right" value="#0088cc">
            </div>
          </div>
          <div class="col-lg-4 col-sm-4 col-12">
          </div>
        </div>
      </div>

      <!-- RGB(A) -->
      <h3>RGB(A)</h3>
      <div class="well">
        <div class="row">
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="rgb">RGB</label>
              <br>
              <input type="text" id="rgb" class="form-control demo" data-format="rgb" value="rgb(33, 147, 58)">
              <span class="help-block">
                RGB input can be assigned by setting the <code>format</code> option
                to <code>rgb</code>.
              </span>
            </div>
          </div>
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="rgba">RGBA</label>
              <br>
              <input type="text" id="rgba" class="form-control demo" data-format="rgb" data-opacity=".5" value="rgba(52, 64, 158, 0.5)">
              <span class="help-block">
                RGBA input can be assigned by setting the <code>format</code>
                option to <code>rgb</code> and <code>opacity</code> option to
                <code>true</code>.
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- and more -->
      <h3>&hellip;and more!</h3>
      <div class="well">
        <div class="row">
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="opacity">Opacity</label>
              <br>
              <input type="text" id="opacity" class="form-control demo" data-opacity=".5" value="#766fa8">
              <span class="help-block">
                Opacity can be assigned by including the <code>data-opacity</code>
                attribute or by setting the <code>opacity</code> option to
                <code>true</code>.
              </span>
            </div>
          </div>
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="keywords">Keywords</label>
              <br>
              <input type="text" id="keywords" class="form-control demo" data-keywords="transparent, initial, inherit" value="transparent">
              <span class="help-block">
                CSS-wide keywords can be assigned by setting the <code>keywords</code>
                option to a comma-separated list of valid keywords: <code>transparent,
                initial, inherit</code>.
              </span>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="default-value">Default Value</label>
              <br>
              <input type="text" id="default-value" class="form-control demo" data-defaultValue="#ff6600">
              <span class="help-block">
                This field has a default value assigned to it, so it will never be empty.
              </span>
            </div>
          </div>
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="letter-case">Letter Case</label>
              <br>
              <input type="text" id="letter-case" class="form-control demo" data-letterCase="uppercase" value="#abcdef">
              <span class="help-block">
                This field will always be uppercase.
              </span>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="input-group">Input Groups</label>
              <div class="input-group">
                <input type="text" id="input-group" class="form-control demo" value="#ff0000"/>
                <span class="input-group-btn">
                  <button class="btn btn-default" type="button">Button</button>
                </span>
              </div>
              <span class="help-block">
                Example using Bootstrap's input groups.
              </span>
            </div>
          </div>
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="more-input-group">More Input Groups</label>
              <div class="input-group">
                <span class="input-group-addon">Color</span>
                <input type="text" id="more-input-group" class="form-control demo" value="#ff0000"/>
                <span class="input-group-btn">
                  <button class="btn btn-default" type="button">Button</button>
                </span>
              </div>
              <span class="help-block">
                Input group example with addon.
              </span>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="letter-case">Swatches</label>
              <br>
              <input type="text" id="swatches" class="form-control demo" data-swatches="#ef9a9a|#90caf9|#a5d6a7|#fff59d|#ffcc80|#bcaaa4|#eeeeee|#f44336|#2196f3|#4caf50|#ffeb3b|#ff9800|#795548|#9e9e9e" value="#abcdef">
              <span class="help-block">
                Example with swatches.
              </span>
            </div>
          </div>
          <div class="col-lg-4 col-sm-4 col-12">
            <div class="form-group">
              <label for="letter-case">Swatches and opacity</label>
              <br>
              <input type="text" id="swatches" class="form-control demo" data-format="rgb" data-opacity="1" data-swatches="#fff|#000|#f00|#0f0|#00f|#ff0|rgba(0,0,255,0.5)"  value="rgba(14, 206, 235, .5)">
              <span class="help-block">
                Example with swatches and opacity.
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- API -->
      <h2 id="api">API</h2>

      <!-- Instantiation -->
      <h3 id="instantiation">Instantiation</h3>
      <p>
        Instantiate like any other jQuery plugin:
      </p>
      <pre>$('INPUT.minicolors').minicolors(settings);</pre>

      <!-- Settings -->
      <h3 id="settings">Settings</h3>

      <p>
        Default settings are as follows:
      </p>
<pre>
$.minicolors = {
  defaults: {
    animationSpeed: 50,
    animationEasing: 'swing',
    change: null,
    changeDelay: 0,
    control: 'hue',
    defaultValue: '',
    format: 'hex',
    hide: null,
    hideSpeed: 100,
    inline: false,
    keywords: '',
    letterCase: 'lowercase',
    opacity: false,
    position: 'bottom left',
    show: null,
    showSpeed: 100,
    theme: 'default',
    swatches: []
  }
};
</pre>
      <p>
        For convenience, you can change default settings globally by assigning new values:
      </p>
<pre>
$.minicolors.defaults.changeDelay = 200;
</pre>
      <p>
        To change multiple properties at once, use <code>$.extend()</code>:
      </p>
<pre>
$.minicolors.defaults = $.extend($.minicolors.defaults, {
  changeDelay: 200,
  letterCase: 'uppercase',
  theme: 'bootstrap'
});
</pre>
      <p class="alert alert-warning">
        <strong>Note:</strong> Changing default settings will <em>not</em> affect controls that
        are already initialized.
      </p>

      <dl>
        <dt>animationSpeed</dt>
        <dd>
          <p>
            The animation speed of the sliders when the user taps or clicks a new color. Set to
            <code>0</code> for no animation.
          </p>
        </dd>

        <dt>animationEasing</dt>
        <dd>
          <p>
            The easing to use when animating the sliders.
          </p>
        </dd>

        <dt>changeDelay</dt>
        <dd>
          <p>
            The time, in milliseconds, to defer the <code>change</code> event from firing while
            the user makes a selection. This is useful for preventing the <code>change</code> event
            from firing frequently as the user drags the color picker around.
          </p>
          <p>
            The default value is <code>0</code> (no delay). If your <code>change</code> callback
            features something resource-intensive (such as an AJAX request), you’ll probably want
            to set this to at least <code>200</code>.
          </p>
        </dd>

        <dt>control</dt>
        <dd>
          <p>
            Determines the type of control. Valid options are <code>hue</code>, <code>brightness</code>,
            <code>saturation</code>, and <code>wheel</code>.
          </p>
        </dd>

        <dt>defaultValue</dt>
        <dd>
          <p>
            To force a default color, set this to a valid hex string. When the user clears the
            control, it will revert to this color.
          </p>
        </dd>

        <dt>format</dt>
        <dd>
          <p>
            The format miniColors should use. Valid options are <code>hex</code> and
            <code>rgb</code>.
          </p>
        </dd>

        <dt>hideSpeed &amp; showSpeed</dt>
        <dd>
          <p>
            The speed at which to hide and show the color picker.
          </p>
        </dd>

        <dt>inline</dt>
        <dd>
          <p>
            Set to <code>true</code> to force the color picker to appear inline.
          </p>
        </dd>

        <dt>keywords</dt>
        <dd>
          <p>
            A comma-separated list of keywords that the control should accept (e.g. inherit,
            transparent, initial). By default, no keywords are allowed.
          </p>
        </dd>

        <dt>letterCase</dt>
        <dd>
          <p>
            Determines the letter case of the hex code value. Valid options are <code>uppercase</code>
            or <code>lowercase</code>.
          </p>
        </dd>

        <dt>opacity</dt>
        <dd>
          <p>
            Set to <code>true</code> to enable the opacity slider. (Use the input element's
            <code>data-opacity</code> attribute to set a preset value.)
          </p>
        </dd>

        <dt>position</dt>
        <dd>
          <p>
            Sets the position of the dropdown. Valid options are <code>bottom left</code>,
            <code>bottom right</code>, <code>top left</code>, and <code>top right</code>.
          </p>
          <p class="alert alert-warning">
            The <code>swatchPosition</code> setting has been removed in version 2.1. The position
            of the swatch is now determined by <code>position</code>.
          </p>
        </dd>

        <dt>theme</dt>
        <dd>
          <p>
            A string containing the name of the custom theme to be applied. In your CSS, prefix
            your selectors like this:
          </p>
<pre>
.minicolors-theme-yourThemeName { ... }
</pre>
          <p>
            If you are using the default theme, you will probably need to adjust the swatch
            styles depending on your existing stylesheet rules. Version 2.1 removes as much
            styling on the <code>input</code> element as possible, which means it’s up to
            you to adjust your CSS to make sure the swatch aligns properly.
          </p>
          <p>
            To adjust the swatch, override these styles:
          </p>
<pre>
.minicolors-theme-default .minicolors-swatch {
  top: 5px;
  left: 5px;
  width: 18px;
  height: 18px;
}
.minicolors-theme-default.minicolors-position-right .minicolors-swatch {
  left: auto;
  right: 5px;
}
</pre>
          </div>
        </dd>

        <dt>swatches</dt>
        <dd>
          <p>
            An array containing one or more strings of hex colors that will show up under the main
            color grid.
          </p>
          <p>
            Alternatively, this can be an array of <code>{ name, color }</code> objects where
            <code>name</code> is a human-readable color name and <code>color</code> is a hex code.
            In this case, the name will be used to set the swatch's <code>title</code> attribute.
          </p>
        </dd>
      </dl>

      <!-- Methods -->
      <h3 id="methods">Methods</h3>
      <p>Use this syntax for calling methods:</p>
      <pre>$(<em>selector</em>).minicolors('method', <em>[data]</em>);</pre>
      <dl>
        <dt>create</dt>
        <dd>
          <p>
            Initializes the control for all items matching your selector. This is the default
            method, so <code>data</code> may be passed in as the only argument.
          </p>
          <p>
            To set a preset color value, populate the <code>value</code> attribute of the original
            input element.
          </p>
        </dd>

        <dt>destroy</dt>
        <dd>
          <p>
            Returns the <em>input</em> element to its original, uninitialized state.
          </p>
        </dd>

        <dt>hide</dt>
        <dd>
          <p>
            Hides the color picker.
          </p>
        </dd>

        <dt>show</dt>
        <dd>
          <p>
            Shows the color picker.
          </p>
        </dd>

        <dt>opacity</dt>
        <dd>
          <p>
            Gets or sets a control's opacity level. To use this method as a setter, pass data in
            as a value between 0 and 1. (You can also obtain this value by checking the input
            element's <code>data-opacity</code> attribute.)
          </p>
          <p>
            To set a preset opacity value, populate the <code>data-opacity</code> attribute of the
            original input element.
          </p>
        </dd>

        <dt>rgbObject</dt>
        <dd>
          <p>
            Returns an object containing red, green, blue, and alpha properties that correspond to
            the control's current value. Example:
          </p>
          <pre>{ r: 0, g: 82, b: 148, a: 0.75 }</pre>
        </dd>

        <dt>rgbString &amp; rgbaString</dt>
        <dd>
          <p>
            Returns an RGB or RGBA string suitable for use in your CSS. Examples:
          </p>
<pre>
rgb(0, 82, 148)
rgba(0, 82, 148, .75)
</pre>
        </dd>

        <dt>settings</dt>
        <dd>
          <p>
            Gets or sets a control's settings. If new settings are passed in, the control will
            destroy and re-initialize itself with any new settings overriding the old ones.
          </p>
        </dd>

        <dt>value</dt>
        <dd>
          <p>
            Gets or sets a control's color value. To use this method as a setter, pass
            in a color string or an object (ex: <code>{color: '#fc0', opacity: .5}</code>).
          </p>
        </dd>
      </dl>

      <!-- Events -->
      <h3 id="events">Events</h3>
      <dl>
        <dt>change</dt>
        <dd>
          <p>Fires when the value of the color picker changes. The <code>this</code> keyword will reference the original input element.
<pre>
$(<em>selector</em>).minicolors({
  change: function(value, opacity) {
    console.log(value + ' - ' + opacity);
  }
});
</pre>
          <p class="alert alert-warning">
            <strong>Warning!</strong> This event will fire a lot when the user drags the
            color picker around. Use the <code>changeDelay</code> setting to reduce its
            frequency.
          </p>
        </dd>

        <dt>hide</dt>
        <dd>
          <p>
            Fires when the color picker is hidden. The <code>this</code> keyword will reference
            the original input element.
          </p>
<pre>
$(<em>selector</em>).minicolors({
  hide: function() {
  console.log('Hide event triggered!');
  }
});
</pre>
        </dd>

        <dt>show</dt>
        <dd>
          <p>
            Fires when the color picker is shown. The <code>this</code> keyword will reference
            the original input element.
          </p>
<pre>
$(<em>selector</em>).minicolors({
  show: function() {
    console.log('Show event triggered!');
  }
});
</pre>
        </dd>
      </dl>

      <h2 id="license">License</h2>
      <p>
        Licensed under the <a href="https://opensource.org/licenses/MIT">MIT license</a>,
        same as <a href="https://jquery.org/license/">jQuery</a>.
      </p>
      <p>
        &copy;2013 <a href="https://www.abeautifulsite.net/">A Beautiful Site, LLC.</a>
      </p>
    </div>
  </div>
</body>
</html>
