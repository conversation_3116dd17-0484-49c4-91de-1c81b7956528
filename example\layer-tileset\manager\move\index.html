<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0" />
    <meta name="author" content="火星科技 http://mars3d.cn " />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="x5-fullscreen" content="true" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />

    <!-- 标题及搜索关键字 -->
    <meta name="keywords" content="火星科技,cesium,3D,GIS,marsgis,三维,地球,地图,开发,框架,系统,示例,资料,模型,离线,外包,合肥,安徽,中国" />
    <meta
      name="description"
      content="火星科技 合肥火星 合肥火星科技 合肥火星科技有限公司 leaflet leaflet框架 leaflet开发 cesium cesium开发 cesium框架 三维 地球 模型  gis marsgis 地图离线 地图开发 地图框架 地图外包 框架 开发 外包  地图离线 二维地图 三维地图 全景漫游 地理信息系统 云GIS 三维GIS GIS平台 WebGIS"
    />

    <link rel="shortcut icon" type="image/x-icon" href="/img/favicon/favicon.ico" />
    <title>模型位置XYZ平移(不贴球面) | Mars3D | 三维地图 | 火星科技 | 合肥火星科技有限公司</title>

    <!--第三方lib-->
    <script
      type="text/javascript"
      src="/lib/include-lib.js"
      libpath="/lib/"
      include="jquery,layer,toastr,font-awesome,bootstrap,bootstrap-checkbox,layer,haoutil,admui,mars3d,localforage"
    ></script>

    <link href="/css/style.css" rel="stylesheet" />
  </head>

  <body class="dark">


    <div id="mars3dContainer" class="mars3d-container"></div>

    <!-- 面板 -->
    <div class="infoview infoview-right">
      <div style="width: 100%; text-align: center; font-size: 16px">3dtile模型移动(只适合小范围内的偏移 笛卡尔坐标方向，非贴球面)</div>

      <table class="mars-table">
        <tr>
          <td>模型URL：</td>
          <td colspan="3">
            <input id="txtModel" type="text" value="https://data.mars3d.cn/3dtiles/qx-dyt/tileset.json" class="form-control" style="width: 450px" />
          </td>
          <td>
            <input type="button" class="btn btn-primary" value="加载模型" onclick="showMd()" />
          </td>
        </tr>
        <tr>
          <td>设置移动步长：</td>
          <td>
            <div class="btn-group" role="group">
              <input type="button" class="benStep btn btn-primary" value="0.1" />
              <input type="button" class="benStep btn btn-primary active" value="1" />
              <input type="button" class="benStep btn btn-primary" value="10" />
              <input type="button" class="benStep btn btn-primary" value="100" />
            </div>
          </td>
          <td>按步长移动：</td>
          <td>
            <input type="button" value="x+" onclick="change(0)" />
            <input type="button" value="x-" onclick="change(1)" />
            <input type="button" value="y+" onclick="change(2)" />
            <input type="button" value="y-" onclick="change(3)" />
            <input type="button" value="z+" onclick="change(4)" />
            <input type="button" value="z-" onclick="change(5)" />
          </td>
          <td></td>
        </tr>
      </table>

      <div class="col col-sm-12">
        <div class="checkbox checkbox-primary checkbox-inline">
          <input id="chkTestTerrain" class="styled" type="checkbox" />
          <label for="chkTestTerrain"> 深度检测 </label>
        </div>
        <div class="checkbox checkbox-primary checkbox-inline">
          <input id="chkHasTerrain" class="styled" type="checkbox" checked />
          <label for="chkHasTerrain"> 是否有地形 </label>
        </div>
        <div class="inline" style="margin-left: 50px">
          当前偏移量：
          <label id="lbl-result"></label>
        </div>
      </div>
    </div>

    <script src="/js/common.js"></script>
    <script src="./map.js"></script>
    <script type="text/javascript">
      "use script" //开发环境建议开启严格模式
      let url = "https://data.mars3d.cn/3dtiles/qx-dyt/tileset.json"
      function initUI(options) {
        setTimeout(() => {
          showModel(url)
        }, 1000)

        $("#chkHasTerrain").change(function () {
          let isStkTerrain = $(this).is(":checked")
          chkHasTerrain(isStkTerrain)
        })

        $("#chkTestTerrain").change(function () {
          let val = $(this).is(":checked")
          chkTestTerrain(val)
        })

        $(".benStep").click(function () {
          $(".active").removeClass("active")
          $(this).addClass("active")

          step = Number($(this).val())
        })

        // url传入模型地址
        const modelUrl = localStorage.getItem("3dtiles_move")
        if (modelUrl) {
          // 历史记录模型地址
          url = modelUrl
          $("#txtModel").val(url)
        } else {
          url = "https://data.mars3d.cn/3dtiles/qx-dyt/tileset.json"
          $("#txtModel").val(url)
        }
      }

      let x = 0
      let y = 0
      let z = 0
      let step = 1

      //当前页面业务相关
      function showMd() {
        url = $("#txtModel").val()
        localStorage.setItem("3dtiles_move", url)
        showModel(url)
      }

      function change(type) {
        switch (type) {
          default:
          case 0:
            x += step
            break
          case 1:
            x -= step
            break
          case 2:
            y += step
            break
          case 3:
            y -= step
            break
          case 4:
            z += step
            break
          case 5:
            z -= step
            break
        }
        $("#lbl-result").html("x:" + x.toFixed(1) + " y:" + y.toFixed(1) + " z:" + z.toFixed(1))

        //创建平移矩阵方法二

        setTranslation(x, y, z)
      }
    </script>
  </body>
</html>
