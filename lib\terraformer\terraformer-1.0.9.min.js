/*! Terraformer JS - 1.0.9 - 2018-06-08
*   https://github.com/esri/Terraformer
*   Copyright (c) 2013-2018 Environmental Systems Research Institute, Inc.
*   Licensed MIT */!function(a,b){"object"==typeof module&&"object"==typeof module.exports&&(exports=module.exports=b()),"object"==typeof window&&(a.Terraformer=b())}(this,function(){function a(a){return"[object Array]"===Object.prototype.toString.call(a)}function b(){var a=Array.prototype.slice.apply(arguments);void 0!==typeof console&&console.warn&&console.warn.apply(console,a)}function c(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c]);return a}function d(a){if(a.type)switch(a.type){case"Point":return[a.coordinates[0],a.coordinates[1],a.coordinates[0],a.coordinates[1]];case"MultiPoint":return g(a.coordinates);case"LineString":return g(a.coordinates);case"MultiLineString":return e(a.coordinates);case"Polygon":return e(a.coordinates);case"MultiPolygon":return f(a.coordinates);case"Feature":return a.geometry?d(a.geometry):null;case"FeatureCollection":return h(a);case"GeometryCollection":return i(a);default:throw new Error("Unknown type: "+a.type)}return null}function e(a){for(var b=null,c=null,d=null,e=null,f=0;f<a.length;f++)for(var g=a[f],h=0;h<g.length;h++){var i=g[h],j=i[0],k=i[1];null===b?b=j:j<b&&(b=j),null===c?c=j:j>c&&(c=j),null===d?d=k:k<d&&(d=k),null===e?e=k:k>e&&(e=k)}return[b,d,c,e]}function f(a){for(var b=null,c=null,d=null,e=null,f=0;f<a.length;f++)for(var g=a[f],h=0;h<g.length;h++)for(var i=g[h],j=0;j<i.length;j++){var k=i[j],l=k[0],m=k[1];null===b?b=l:l<b&&(b=l),null===c?c=l:l>c&&(c=l),null===d?d=m:m<d&&(d=m),null===e?e=m:m>e&&(e=m)}return[b,d,c,e]}function g(a){for(var b=null,c=null,d=null,e=null,f=0;f<a.length;f++){var g=a[f],h=g[0],i=g[1];null===b?b=h:h<b&&(b=h),null===c?c=h:h>c&&(c=h),null===d?d=i:i<d&&(d=i),null===e?e=i:i>e&&(e=i)}return[b,d,c,e]}function h(a){for(var b,c=[],e=a.features.length-1;e>=0;e--)b=d(a.features[e].geometry),c.push([b[0],b[1]]),c.push([b[2],b[3]]);return g(c)}function i(a){for(var b,c=[],e=a.geometries.length-1;e>=0;e--)b=d(a.geometries[e]),c.push([b[0],b[1]]),c.push([b[2],b[3]]);return g(c)}function k(a){var b=d(a);return{x:b[0],y:b[1],w:Math.abs(b[0]-b[2]),h:Math.abs(b[1]-b[3])}}function l(a){return a*W}function m(a){return a*X}function n(a,b){for(var c=0;c<a.length;c++)"number"==typeof a[c][0]&&(a[c]=b(a[c])),"object"==typeof a[c]&&(a[c]=n(a[c],b));return a}function o(a){var b=a[0],c=a[1];return[l(b/V)-360*Math.floor((l(b/V)+180)/360),l(Math.PI/2-2*Math.atan(Math.exp(-1*c/V)))]}function p(a){var b=a[0],c=Math.max(Math.min(a[1],89.99999),-89.99999);return[m(b)*V,V/2*Math.log((1+Math.sin(m(c)))/(1-Math.sin(m(c))))]}function q(a,b,c){if("Point"===a.type)a.coordinates=b(a.coordinates);else if("Feature"===a.type)a.geometry=q(a.geometry,b,!0);else if("FeatureCollection"===a.type)for(var d=0;d<a.features.length;d++)a.features[d]=q(a.features[d],b,!0);else if("GeometryCollection"===a.type)for(var e=0;e<a.geometries.length;e++)a.geometries[e]=q(a.geometries[e],b,!0);else a.coordinates=n(a.coordinates,b);return c||b===p&&(a.crs=Y),b===o&&delete a.crs,a}function r(a){return q(a,p)}function s(a){return q(a,o)}function t(a,b){return a<b?-1:a>b?1:0}function u(a,b){return a[0]>b[0]?-1:a[0]<b[0]?1:a[1]>b[1]?-1:a[1]<b[1]?1:0}function v(a,b,c){return t((b[0]-a[0])*(c[1]-a[1])-(c[0]-a[0])*(b[1]-a[1]),0)}function w(a,b){var c=b[0]-a[0],d=b[1]-a[1];return c*c+d*d}function x(a,b){var c=b;for(var d in a){var e=v(b,c,a[d]);(e===-1||0===e&&w(b,a[d])>w(b,c))&&(c=a[d])}return c}function y(a){if(0===a.length)return[];if(1===a.length)return a;for(var b=[a.sort(u)[0]],c=0;c<b.length;c++){var d=x(a,b[c]);d!==b[0]&&b.push(d)}return b}function z(a){for(var b,c=0;c<a.length-3;c++){var d=a[c],e=a[c+1],f=a[c+2],g=[e[0]-d[0],e[1]-d[1]],h=f[0]*g[1]-f[1]*g[0]+g[0]*d[1]-g[1]*d[0];if(0===c)b=h<0;else if(b&&h>0||!b&&h<0)return!1}return!0}function A(a,b){for(var c=!1,d=-1,e=a.length,f=e-1;++d<e;f=d)(a[d][1]<=b[1]&&b[1]<a[f][1]||a[f][1]<=b[1]&&b[1]<a[d][1])&&b[0]<(a[f][0]-a[d][0])*(b[1]-a[d][1])/(a[f][1]-a[d][1])+a[d][0]&&(c=!c);return c}function B(a,b){if(a&&a.length){if(1===a.length)return A(a[0],b);if(A(a[0],b)){for(var c=1;c<a.length;c++)if(A(a[c],b))return!1;return!0}return!1}return!1}function C(a,b,c,d){var e=(d[0]-c[0])*(a[1]-c[1])-(d[1]-c[1])*(a[0]-c[0]),f=(b[0]-a[0])*(a[1]-c[1])-(b[1]-a[1])*(a[0]-c[0]),g=(d[1]-c[1])*(b[0]-a[0])-(d[0]-c[0])*(b[1]-a[1]);if(0!==g){var h=e/g,i=f/g;if(0<=h&&h<=1&&0<=i&&i<=1)return!0}return!1}function D(a){return!isNaN(parseFloat(a))&&isFinite(a)}function E(a,b){if(D(a[0][0])){if(D(b[0][0])){for(var c=0;c<a.length-1;c++)for(var d=0;d<b.length-1;d++)if(C(a[c],a[c+1],b[d],b[d+1]))return!0}else for(var e=0;e<b.length;e++)if(E(a,b[e]))return!0}else for(var f=0;f<a.length;f++)if(E(a[f],b))return!0;return!1}function F(a){for(var b=[],c=0;c<a.length;c++){var d=a[c].slice();G(d[0],d[d.length-1])===!1&&d.push(d[0]),b.push(d)}return b}function G(a,b){for(var c=0;c<a.length;c++)if(a[c]!==b[c])return!1;return!0}function H(a,b){if(a.length!==b.length)return!1;for(var c=a.slice().sort(u),d=b.slice().sort(u),e=0;e<c.length;e++){if(c[e].length!==d[e].length)return!1;for(var f=0;f<c.length;f++)if(c[e][f]!==d[e][f])return!1}return!0}function I(a){if(a)switch(a.type){case"Point":return new J(a);case"MultiPoint":return new K(a);case"LineString":return new L(a);case"MultiLineString":return new M(a);case"Polygon":return new N(a);case"MultiPolygon":return new O(a);case"Feature":return new P(a);case"FeatureCollection":return new Q(a);case"GeometryCollection":return new R(a);default:throw new Error("Unknown type: "+a.type)}}function J(b){var d=Array.prototype.slice.call(arguments);if(b&&"Point"===b.type&&b.coordinates)c(this,b);else if(b&&a(b))this.coordinates=b;else{if(!(d.length>=2))throw"Terraformer: invalid input for Terraformer.Point";this.coordinates=d}this.type="Point"}function K(b){if(b&&"MultiPoint"===b.type&&b.coordinates)c(this,b);else{if(!a(b))throw"Terraformer: invalid input for Terraformer.MultiPoint";this.coordinates=b}this.type="MultiPoint"}function L(b){if(b&&"LineString"===b.type&&b.coordinates)c(this,b);else{if(!a(b))throw"Terraformer: invalid input for Terraformer.LineString";this.coordinates=b}this.type="LineString"}function M(b){if(b&&"MultiLineString"===b.type&&b.coordinates)c(this,b);else{if(!a(b))throw"Terraformer: invalid input for Terraformer.MultiLineString";this.coordinates=b}this.type="MultiLineString"}function N(b){if(b&&"Polygon"===b.type&&b.coordinates)c(this,b);else{if(!a(b))throw"Terraformer: invalid input for Terraformer.Polygon";this.coordinates=b}this.type="Polygon"}function O(b){if(b&&"MultiPolygon"===b.type&&b.coordinates)c(this,b);else{if(!a(b))throw"Terraformer: invalid input for Terraformer.MultiPolygon";this.coordinates=b}this.type="MultiPolygon"}function P(a){if(a&&"Feature"===a.type)c(this,a);else{if(!(a&&a.type&&a.coordinates))throw"Terraformer: invalid input for Terraformer.Feature";this.geometry=a}this.type="Feature"}function Q(b){if(b&&"FeatureCollection"===b.type&&b.features)c(this,b);else{if(!a(b))throw"Terraformer: invalid input for Terraformer.FeatureCollection";this.features=b}this.type="FeatureCollection"}function R(b){if(b&&"GeometryCollection"===b.type&&b.geometries)c(this,b);else if(a(b))this.geometries=b;else{if(!b.coordinates||!b.type)throw"Terraformer: invalid input for Terraformer.GeometryCollection";this.type="GeometryCollection",this.geometries=[b]}this.type="GeometryCollection"}function S(a,b,c){for(var d=p(a),e=c||64,f={type:"Polygon",coordinates:[[]]},g=1;g<=e;g++){var h=g*(360/e)*Math.PI/180;f.coordinates[0].push([d[0]+b*Math.cos(h),d[1]+b*Math.sin(h)])}return f.coordinates=F(f.coordinates),s(f)}function T(a,b,d){var e=d||64,f=b||250;if(!a||a.length<2||!f||!e)throw new Error("Terraformer: missing parameter for Terraformer.Circle");c(this,new P({type:"Feature",geometry:S(a,f,e),properties:{radius:f,center:a,steps:e}}))}var U={},V=6378137,W=57.29577951308232,X=.017453292519943,Y={type:"link",properties:{href:"http://spatialreference.org/ref/sr-org/6928/ogcwkt/",type:"ogcwkt"}},Z={type:"link",properties:{href:"http://spatialreference.org/ref/epsg/4326/ogcwkt/",type:"ogcwkt"}},$=["length"];return I.prototype.toMercator=function(){return r(this)},I.prototype.toGeographic=function(){return s(this)},I.prototype.envelope=function(){return k(this)},I.prototype.bbox=function(){return d(this)},I.prototype.convexHull=function(){var a,b,c=[];if("Point"===this.type)return null;if("LineString"===this.type||"MultiPoint"===this.type){if(!(this.coordinates&&this.coordinates.length>=3))return null;c=this.coordinates}else if("Polygon"===this.type||"MultiLineString"===this.type){if(!(this.coordinates&&this.coordinates.length>0))return null;for(a=0;a<this.coordinates.length;a++)c=c.concat(this.coordinates[a]);if(c.length<3)return null}else if("MultiPolygon"===this.type){if(!(this.coordinates&&this.coordinates.length>0))return null;for(a=0;a<this.coordinates.length;a++)for(b=0;b<this.coordinates[a].length;b++)c=c.concat(this.coordinates[a][b]);if(c.length<3)return null}else if("Feature"===this.type){var d=new I(this.geometry);return d.convexHull()}return new N({type:"Polygon",coordinates:F([y(c)])})},I.prototype.toJSON=function(){var a={};for(var b in this)this.hasOwnProperty(b)&&$.indexOf(b)===-1&&(a[b]=this[b]);return a.bbox=d(this),a},I.prototype.contains=function(a){return new I(a).within(this)},I.prototype.within=function(a){var b,c,d;if("Feature"===a.type&&(a=a.geometry),"Point"===a.type&&"Point"===this.type)return G(this.coordinates,a.coordinates);if("MultiLineString"===a.type&&"Point"===this.type)for(c=0;c<a.coordinates.length;c++){var e={type:"LineString",coordinates:a.coordinates[c]};if(this.within(e))return!0}if(("LineString"===a.type||"MultiPoint"===a.type)&&"Point"===this.type)for(c=0;c<a.coordinates.length;c++){if(this.coordinates.length!==a.coordinates[c].length)return!1;if(G(this.coordinates,a.coordinates[c]))return!0}if("Polygon"===a.type){if("Polygon"===this.type){if(a.coordinates.length===this.coordinates.length)for(c=0;c<this.coordinates.length;c++)if(H(this.coordinates[c],a.coordinates[c]))return!0;return!(!this.coordinates.length||!B(a.coordinates,this.coordinates[0][0]))&&!E(F(this.coordinates),F(a.coordinates))}if("Point"===this.type)return B(a.coordinates,this.coordinates);if("LineString"===this.type||"MultiPoint"===this.type){if(!this.coordinates||0===this.coordinates.length)return!1;for(c=0;c<this.coordinates.length;c++)if(B(a.coordinates,this.coordinates[c])===!1)return!1;return!0}if("MultiLineString"===this.type){for(c=0;c<this.coordinates.length;c++){var f=new L(this.coordinates[c]);if(f.within(a)===!1)return d++,!1}return!0}if("MultiPolygon"===this.type){for(c=0;c<this.coordinates.length;c++){var g=new I({type:"Polygon",coordinates:this.coordinates[c]});if(g.within(a)===!1)return!1}return!0}}if("MultiPolygon"===a.type){if("Point"===this.type){if(a.coordinates.length)for(c=0;c<a.coordinates.length;c++)if(b=a.coordinates[c],B(b,this.coordinates)&&E([this.coordinates],a.coordinates)===!1)return!0;return!1}if("Polygon"===this.type){for(c=0;c<this.coordinates.length;c++)if(a.coordinates[c].length===this.coordinates.length)for(j=0;j<this.coordinates.length;j++)if(H(this.coordinates[j],a.coordinates[c][j]))return!0;if(E(this.coordinates,a.coordinates)===!1&&a.coordinates.length){for(c=0;c<a.coordinates.length;c++)b=a.coordinates[c],d=B(b,this.coordinates[0][0])!==!1;return d}}else if("LineString"===this.type||"MultiPoint"===this.type)for(c=0;c<a.coordinates.length;c++){var h={type:"Polygon",coordinates:a.coordinates[c]};return!!this.within(h)}else{if("MultiLineString"===this.type){for(c=0;c<this.coordinates.length;c++){var i=new L(this.coordinates[c]);if(i.within(a)===!1)return!1}return!0}if("MultiPolygon"===this.type){for(c=0;c<a.coordinates.length;c++){var k={type:"Polygon",coordinates:a.coordinates[c]};if(this.within(k)===!1)return!1}return!0}}}return!1},I.prototype.intersects=function(a){"Feature"===a.type&&(a=a.geometry);var c=new I(a);if(this.within(a)||c.within(this))return!0;if("Point"!==this.type&&"MultiPoint"!==this.type&&"Point"!==a.type&&"MultiPoint"!==a.type)return E(this.coordinates,a.coordinates);if("Feature"===this.type){var d=new I(this.geometry);return d.intersects(a)}return b("Type "+this.type+" to "+a.type+" intersection is not supported by intersects"),!1},J.prototype=new I,J.prototype.constructor=J,K.prototype=new I,K.prototype.constructor=K,K.prototype.forEach=function(a){for(var b=0;b<this.coordinates.length;b++)a.apply(this,[this.coordinates[b],b,this.coordinates]);return this},K.prototype.addPoint=function(a){return this.coordinates.push(a),this},K.prototype.insertPoint=function(a,b){return this.coordinates.splice(b,0,a),this},K.prototype.removePoint=function(a){return"number"==typeof a?this.coordinates.splice(a,1):this.coordinates.splice(this.coordinates.indexOf(a),1),this},K.prototype.get=function(a){return new J(this.coordinates[a])},L.prototype=new I,L.prototype.constructor=L,L.prototype.addVertex=function(a){return this.coordinates.push(a),this},L.prototype.insertVertex=function(a,b){return this.coordinates.splice(b,0,a),this},L.prototype.removeVertex=function(a){return this.coordinates.splice(a,1),this},M.prototype=new I,M.prototype.constructor=M,M.prototype.forEach=function(a){for(var b=0;b<this.coordinates.length;b++)a.apply(this,[this.coordinates[b],b,this.coordinates])},M.prototype.get=function(a){return new L(this.coordinates[a])},N.prototype=new I,N.prototype.constructor=N,N.prototype.addVertex=function(a){return this.insertVertex(a,this.coordinates[0].length-1),this},N.prototype.insertVertex=function(a,b){return this.coordinates[0].splice(b,0,a),this},N.prototype.removeVertex=function(a){return this.coordinates[0].splice(a,1),this},N.prototype.close=function(){this.coordinates=F(this.coordinates)},N.prototype.hasHoles=function(){return this.coordinates.length>1},N.prototype.holes=function(){if(holes=[],this.hasHoles())for(var a=1;a<this.coordinates.length;a++)holes.push(new N([this.coordinates[a]]));return holes},O.prototype=new I,O.prototype.constructor=O,O.prototype.forEach=function(a){for(var b=0;b<this.coordinates.length;b++)a.apply(this,[this.coordinates[b],b,this.coordinates])},O.prototype.get=function(a){return new N(this.coordinates[a])},O.prototype.close=function(){var a=[];return this.forEach(function(b){a.push(F(b))}),this.coordinates=a,this},P.prototype=new I,P.prototype.constructor=P,Q.prototype=new I,Q.prototype.constructor=Q,Q.prototype.forEach=function(a){for(var b=0;b<this.features.length;b++)a.apply(this,[this.features[b],b,this.features])},Q.prototype.get=function(a){var b;return this.forEach(function(c){c.id===a&&(b=c)}),new P(b)},R.prototype=new I,R.prototype.constructor=R,R.prototype.forEach=function(a){for(var b=0;b<this.geometries.length;b++)a.apply(this,[this.geometries[b],b,this.geometries])},R.prototype.get=function(a){return new I(this.geometries[a])},T.prototype=new I,T.prototype.constructor=T,T.prototype.recalculate=function(){return this.geometry=S(this.properties.center,this.properties.radius,this.properties.steps),this},T.prototype.center=function(a){return a&&(this.properties.center=a,this.recalculate()),this.properties.center},T.prototype.radius=function(a){return a&&(this.properties.radius=a,this.recalculate()),this.properties.radius},T.prototype.steps=function(a){return a&&(this.properties.steps=a,this.recalculate()),this.properties.steps},T.prototype.toJSON=function(){var a=I.prototype.toJSON.call(this);return a},U.Primitive=I,U.Point=J,U.MultiPoint=K,U.LineString=L,U.MultiLineString=M,U.Polygon=N,U.MultiPolygon=O,U.Feature=P,U.FeatureCollection=Q,U.GeometryCollection=R,U.Circle=T,U.toMercator=r,U.toGeographic=s,U.Tools={},U.Tools.positionToMercator=p,U.Tools.positionToGeographic=o,U.Tools.applyConverter=q,U.Tools.toMercator=r,U.Tools.toGeographic=s,U.Tools.createCircle=S,U.Tools.calculateBounds=d,U.Tools.calculateEnvelope=k,U.Tools.coordinatesContainPoint=A,U.Tools.polygonContainsPoint=B,U.Tools.arraysIntersectArrays=E,U.Tools.coordinatesContainPoint=A,U.Tools.coordinatesEqual=H,U.Tools.convexHull=y,U.Tools.isConvex=z,U.MercatorCRS=Y,U.GeographicCRS=Z,U});