/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.132
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as r}from"./chunk-ZOLTURIP.js";import{a as L}from"./chunk-FS4UNHBA.js";import{a as P}from"./chunk-UTZVABQ2.js";import"./chunk-XGAIMGHL.js";import"./chunk-PT6HOSMO.js";import"./chunk-3UI62PNL.js";import"./chunk-5HEB7B66.js";import{a as X}from"./chunk-GCGQM3V5.js";import{a as W}from"./chunk-DZ42UGGL.js";import{b as K,c as Q,d as H}from"./chunk-UXDFNPCW.js";import{d as j}from"./chunk-QNF6DOIT.js";import"./chunk-RVXELCI5.js";import{a as v}from"./chunk-S2VOP6QV.js";import{a as A,d as w,f as J}from"./chunk-HEWRROTS.js";import{a as G}from"./chunk-W2IXI6NO.js";import"./chunk-ZO574IGQ.js";import"./chunk-YQWHD5N7.js";import{a as D}from"./chunk-4AYOP6XD.js";import{e as u}from"./chunk-F4R6NSH4.js";function Z(t,e,o,i,s,c,p){let g=P.numberOfPoints(t,e,s),f,n=o.red,m=o.green,d=o.blue,T=o.alpha,l=i.red,h=i.green,y=i.blue,S=i.alpha;if(r.equals(o,i)){for(f=0;f<g;f++)c[p++]=r.floatToByte(n),c[p++]=r.floatToByte(m),c[p++]=r.floatToByte(d),c[p++]=r.floatToByte(T);return p}let N=(l-n)/g,V=(h-m)/g,R=(y-d)/g,_=(S-T)/g,a=p;for(f=0;f<g;f++)c[a++]=r.floatToByte(n+f*N),c[a++]=r.floatToByte(m+f*V),c[a++]=r.floatToByte(d+f*R),c[a++]=r.floatToByte(T+f*_);return a}function O(t){t=t??J.EMPTY_OBJECT;let e=t.positions,o=t.colors,i=t.colorsPerVertex??!1;if(!u(e)||e.length<2)throw new D("At least two positions are required.");if(u(o)&&(i&&o.length<e.length||!i&&o.length<e.length-1))throw new D("colors has an invalid length.");this._positions=e,this._colors=o,this._colorsPerVertex=i,this._arcType=t.arcType??L.GEODESIC,this._granularity=t.granularity??G.RADIANS_PER_DEGREE,this._ellipsoid=t.ellipsoid??w.default,this._workerName="createSimplePolylineGeometry";let s=1+e.length*A.packedLength;s+=u(o)?1+o.length*r.packedLength:1,this.packedLength=s+w.packedLength+3}O.pack=function(t,e,o){if(!u(t))throw new D("value is required");if(!u(e))throw new D("array is required");o=o??0;let i,s=t._positions,c=s.length;for(e[o++]=c,i=0;i<c;++i,o+=A.packedLength)A.pack(s[i],e,o);let p=t._colors;for(c=u(p)?p.length:0,e[o++]=c,i=0;i<c;++i,o+=r.packedLength)r.pack(p[i],e,o);return w.pack(t._ellipsoid,e,o),o+=w.packedLength,e[o++]=t._colorsPerVertex?1:0,e[o++]=t._arcType,e[o]=t._granularity,e};O.unpack=function(t,e,o){if(!u(t))throw new D("array is required");e=e??0;let i,s=t[e++],c=new Array(s);for(i=0;i<s;++i,e+=A.packedLength)c[i]=A.unpack(t,e);s=t[e++];let p=s>0?new Array(s):void 0;for(i=0;i<s;++i,e+=r.packedLength)p[i]=r.unpack(t,e);let g=w.unpack(t,e);e+=w.packedLength;let f=t[e++]===1,n=t[e++],m=t[e];return u(o)?(o._positions=c,o._colors=p,o._ellipsoid=g,o._colorsPerVertex=f,o._arcType=n,o._granularity=m,o):new O({positions:c,colors:p,ellipsoid:g,colorsPerVertex:f,arcType:n,granularity:m})};var U=new Array(2),q=new Array(2),$={positions:U,height:q,ellipsoid:void 0,minDistance:void 0,granularity:void 0};O.createGeometry=function(t){let e=t._positions,o=t._colors,i=t._colorsPerVertex,s=t._arcType,c=t._granularity,p=t._ellipsoid,g=G.chordLength(c,p.maximumRadius),f=u(o)&&!i,n,m=e.length,d,T,l,h,y=0;if(s===L.GEODESIC||s===L.RHUMB){let _,a,k;s===L.GEODESIC?(_=G.chordLength(c,p.maximumRadius),a=P.numberOfPoints,k=P.generateArc):(_=c,a=P.numberOfPointsRhumbLine,k=P.generateRhumbArc);let z=P.extractHeights(e,p),B=$;if(s===L.GEODESIC?B.minDistance=g:B.granularity=c,B.ellipsoid=p,f){let b=0;for(n=0;n<m-1;n++)b+=a(e[n],e[n+1],_)+1;d=new Float64Array(b*3),l=new Uint8Array(b*4),B.positions=U,B.height=q;let E=0;for(n=0;n<m-1;++n){U[0]=e[n],U[1]=e[n+1],q[0]=z[n],q[1]=z[n+1];let C=k(B);if(u(o)){let M=C.length/3;h=o[n];for(let F=0;F<M;++F)l[E++]=r.floatToByte(h.red),l[E++]=r.floatToByte(h.green),l[E++]=r.floatToByte(h.blue),l[E++]=r.floatToByte(h.alpha)}d.set(C,y),y+=C.length}}else if(B.positions=e,B.height=z,d=new Float64Array(k(B)),u(o)){for(l=new Uint8Array(d.length/3*4),n=0;n<m-1;++n){let E=e[n],C=e[n+1],M=o[n],F=o[n+1];y=Z(E,C,M,F,g,l,y)}let b=o[m-1];l[y++]=r.floatToByte(b.red),l[y++]=r.floatToByte(b.green),l[y++]=r.floatToByte(b.blue),l[y++]=r.floatToByte(b.alpha)}}else{T=f?m*2-2:m,d=new Float64Array(T*3),l=u(o)?new Uint8Array(T*4):void 0;let _=0,a=0;for(n=0;n<m;++n){let k=e[n];if(f&&n>0&&(A.pack(k,d,_),_+=3,h=o[n-1],l[a++]=r.floatToByte(h.red),l[a++]=r.floatToByte(h.green),l[a++]=r.floatToByte(h.blue),l[a++]=r.floatToByte(h.alpha)),f&&n===m-1)break;A.pack(k,d,_),_+=3,u(o)&&(h=o[n],l[a++]=r.floatToByte(h.red),l[a++]=r.floatToByte(h.green),l[a++]=r.floatToByte(h.blue),l[a++]=r.floatToByte(h.alpha))}}let S=new W;S.position=new H({componentDatatype:v.DOUBLE,componentsPerAttribute:3,values:d}),u(o)&&(S.color=new H({componentDatatype:v.UNSIGNED_BYTE,componentsPerAttribute:4,values:l,normalize:!0})),T=d.length/3;let N=(T-1)*2,V=X.createTypedArray(T,N),R=0;for(n=0;n<T-1;++n)V[R++]=n,V[R++]=n+1;return new Q({attributes:S,indices:V,primitiveType:K.LINES,boundingSphere:j.fromPoints(e)})};var Y=O;function x(t,e){return u(e)&&(t=Y.unpack(t,e)),t._ellipsoid=w.clone(t._ellipsoid),Y.createGeometry(t)}var _e=x;export{_e as default};
