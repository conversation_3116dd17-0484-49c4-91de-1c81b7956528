/**
 * Mars3D三维可视化平台  mars3d
 *
 * 版本信息：v3.10.3
 * 编译日期：2025-08-17 12:10
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：火星科技免费公开版 ，2025-07-01
 */

/**地球容器div*/
.mars3d-container {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: relative;
}
.mars3d-container .cesium-widget-credits {
  display: none;
}
.mars3d-container .mars3d-vrButton {
  right: auto !important;
}
/**隐藏的div对象，如 DivBillboardEntity、HeatLayer 等*/
.mars3d-hideDiv {
  z-index: -99;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  pointer-events: none;
}
/**右键菜单*/
.mars3d-contextmenu {
  position: absolute;
  padding: 0;
  z-index: 20170825;
  display: none;
}
.mars3d-contextmenu-ul {
  background: rgba(43, 44, 47, 0.8);
  border: 1px solid #2b2c2f;
  border-radius: 2px;
  border-width: 1px;
  min-width: 80px;
  position: relative;
  list-style: none;
  margin: 0;
  padding: 0;
}
.mars3d-contextmenu-ul .mars3d-contextmenu-icon {
  position: absolute;
  left: 5px;
  width: 20px;
  height: 20px;
  text-align: center;
  overflow: hidden;
}
.mars3d-contextmenu-ul .mars3d-contextmenu-arrow {
  position: absolute;
  right: 0;
  width: 20px;
  height: 20px;
  text-align: center;
}
.mars3d-contextmenu-ul li + li {
  margin: 0;
  padding: 0;
  position: relative;
}
.mars3d-contextmenu-ul li + li:before {
  content: "";
  display: block;
  height: 1px;
  width: 100%;
  background: -webkit-linear-gradient(to left, transparent, rgba(255, 255, 255, 0.2), transparent);
  background: linear-gradient(to left, transparent, rgba(255, 255, 255, 0.2), transparent);
  position: absolute;
  top: 0;
  left: 0;
}
.mars3d-contextmenu-ul .mars3d-contextmenu-item ::marker {
  content: "";
}
.mars3d-contextmenu-ul .mars3d-contextmenu-line {
  position: absolute;
  left: 2%;
  height: 1px;
  width: 96%;
  background: #597086;
  border-radius: 2px;
  border-width: 1px;
}
.mars3d-contextmenu-ul > li > a {
  padding: 6px 10px 6px 30px;
  -webkit-transition: background-color 0.25s;
  -o-transition: background-color 0.25s;
  transition: background-color 0.25s;
  display: block;
  clear: both;
  line-height: 22px;
  color: #76838f;
  white-space: nowrap;
  color: #edffff;
  text-decoration: none;
}
.mars3d-contextmenu-ul > li > a:hover,
.mars3d-contextmenu-ul > li > a:focus,
.mars3d-contextmenu-ul > li > .active {
  color: #fff;
  background-color: #444d59;
  text-decoration: none;
}
.mars3d-contextmenu-ul > .active > a,
.mars3d-contextmenu-ul > .active > a:hover,
.mars3d-contextmenu-ul > .active > a:focus {
  color: #fff;
  background-color: var(--mars-hover-color, #3ea6ff);
  text-decoration: none;
}
.mars3d-sub-menu {
  position: absolute;
  background-color: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
  min-width: 100px;
  display: none;
}
.mars3d-sub-menu li {
  width: 100%;
  box-sizing: border-box;
  font-size: 14px;
  color: var(--mars-text-color, #ffffff);
  margin: 0;
  padding: 0;
}
.mars3d-sub-menu li:hover {
  background-color: var(--mars-hover-color, #3ea6ff);
}
.mars3d-smallTooltip {
  display: block;
  position: absolute;
  max-width: 200px;
  min-width: 100px;
  padding: 2px 5px;
  font-size: 11px;
  z-index: 1000;
  opacity: 0.8;
  -khtml-opacity: 0.8;
  -moz-opacity: 0.8;
  filter: alpha(opacity=80);
  pointer-events: none;
}
.mars3d-smallTooltip-inner {
  padding: 3px 5px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  text-align: left;
  max-width: 200px;
  text-decoration: none;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.mars3d-smallTooltip-inner p {
  margin: 0;
}
.mars3d-smallTooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  top: 50%;
}
.mars3d-smallTooltip-leftArrow {
  right: 0;
  margin-top: -5px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 5px solid #000000;
}
.mars3d-smallTooltip-rightArrow {
  left: 0;
  margin-top: -5px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-right: 5px solid #000000;
}
.mars3d-clockAnimate {
  z-index: 12;
  position: absolute;
  bottom: 0;
  left: 0;
  height: 27px;
  background: linear-gradient(to bottom, rgba(116, 117, 119, 0.8) 0%, rgba(58, 68, 82, 0.8) 11%, rgba(46, 50, 56, 0.8) 46%, rgba(53, 53, 53, 0.8) 81%, rgba(53, 53, 53, 0.8) 100%);
}
.mars3d-clockAnimate .time {
  margin: 0 4px;
  font-size: 13px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.mars3d-clockAnimate .mars3d-clockAnimate-btn {
  display: inline-block;
  vertical-align: middle;
  padding: 1px 8px;
  border: solid 1px #888;
  color: var(--mars-text-color, #ffffff);
  background-color: rgba(63, 72, 84, 0.7);
  border: none;
  cursor: pointer;
  width: 24px;
}
.mars3d-clockAnimate .mars3d-clockAnimate-btn svg {
  margin-top: 1px;
}
.mars3d-clockAnimate .mars3d-clockAnimate-speed {
  width: 80px;
  height: 24px;
  border: solid 1px rgba(136, 136, 136, 0.6);
  padding: 0 3px;
  margin: 0;
  color: var(--mars-text-color, #ffffff);
  background-color: rgba(63, 72, 84, 0.7);
}
.mars3d-clockAnimate .mars3d-clockAnimate-speed ::-webkit-outer-spin-button,
.mars3d-clockAnimate .mars3d-clockAnimate-speed input::-webkit-inner-spin-button {
  appearance: none !important;
  margin: 0;
}
.mars3d-compass {
  position: absolute;
  cursor: pointer;
  pointer-events: auto;
  user-select: none;
  width: 55px;
  height: 55px;
}
.mars3d-compass .mars3d-compass-outer {
  position: absolute;
  top: 0;
  left: 0;
  height: 55px;
  width: 55px;
  background-repeat: no-repeat;
  background-size: contain;
  fill: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
  border-radius: 50%;
}
.mars3d-compass .mars3d-compass-outer svg {
  height: 55px;
  width: 55px;
}
.mars3d-compass .mars3d-compass-inner {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  height: 25px;
  width: 25px;
  border-radius: 50%;
  display: block;
  margin: 0 auto;
  padding: 4px;
  box-sizing: border-box;
  background: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
  fill: var(--mars-text-color, #ffffff);
}
.mars3d-compass .mars3d-compass-inner:hover {
  background: #3385ff;
}
.mars3d-compass .mars3d-compass-rotation-arc {
  position: absolute;
  top: 2px;
  left: 2px;
  height: 51px;
  width: 51px;
  border-radius: 50%;
  background-repeat: no-repeat;
  background-size: contain;
}
.mars3d-cubeview {
  position: absolute;
  width: 100px;
  height: 100px;
}
.mars3d-cubeview-cube {
  width: 50%;
  height: 50%;
  top: 25%;
  transform-style: preserve-3d;
  margin: auto;
  position: relative;
  will-change: transform;
  font-family: monospace;
  font-weight: bold;
}
.mars3d-cubeview-cube > div {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  width: 100%;
  height: 100%;
  user-select: none;
  cursor: pointer;
  color: var(--mars-text-color, #ffffff);
  background-color: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
  border: 1px solid var(--mars-text-color, #ffffff);
}
.mars3d-cubeview-cube > div:hover {
  background: #3385ff;
}
.mars3d-cubeview-side-t {
  transform: rotatex(90deg) translateZ(25px);
}
.mars3d-cubeview-side-w {
  transform: rotateY(-90deg) translateZ(25px);
  border-right: 2px solid blue !important;
  border-bottom: 2px solid green !important;
}
.mars3d-cubeview-side-s {
  border-left: 2px solid blue !important;
  border-bottom: 2px solid red !important;
  transform: translateZ(25px);
}
.mars3d-cubeview-side-e {
  transform: rotateY(90deg) translateZ(25px);
}
.mars3d-cubeview-side-n {
  transform: rotateY(180deg) translateZ(25px);
}
.mars3d-cubeview-side-b {
  transform: rotateX(-90deg) translateZ(25px);
  border-top: 2px solid red !important;
  border-left: 2px solid green !important;
}
.mars3d-distance-legend {
  z-index: 11;
  position: absolute;
  width: 125px;
  height: 25px;
  pointer-events: none;
  user-select: none;
}
.mars3d-distance-legend .legend-label {
  font-size: 13px;
  color: #ffffff;
  text-align: center;
  width: 100%;
}
.mars3d-distance-legend .legend-scale-bar {
  position: absolute;
  height: 10px;
  top: 10px;
  border-left: 1px solid #ffffff;
  border-right: 1px solid #ffffff;
  border-bottom: 1px solid #ffffff;
}
/**左下角，鼠标经纬度提示*/
.mars3d-locationbar {
  z-index: 10;
  position: absolute;
  padding: 3px 10px;
  font-size: 13px;
  color: #e9e9e9;
  background-color: rgba(0, 0, 0, 0.4);
  min-height: 26px;
  pointer-events: none;
}
.mars3d-locationbar-content {
  float: right;
}
.mars3d-locationbar-content > div {
  float: left;
  margin-right: 20px;
}
.cesium-performanceDisplay-ms,
.cesium-performanceDisplay-fps {
  min-width: 65px;
}
/* 兼容屏幕大小 美观显示不同信息 */
@media screen and (max-width: 1200px) {
  .mars3d-locationbar-content > div {
    margin-right: 15px;
  }
}
@media screen and (max-width: 1000px) {
  .cesium-performanceDisplay-ms,
  .cesium-performanceDisplay-fps,
  .mars3d-locationbar-content > .hide1000 {
    display: none !important;
  }
  .mars3d-locationbar-content > div {
    margin-right: 10px;
  }
}
@media screen and (max-width: 700px) {
  .mars3d-locationbar-content > .hide700 {
    display: none !important;
  }
}
@media screen and (max-width: 600px) {
  .mars3d-locationbar {
    display: none !important;
  }
}
.mars3d-slider {
  position: absolute;
  left: 50%;
  top: 0px;
  background-color: #d3d3d3;
  width: 3px;
  height: 100%;
  z-index: 9999;
}
.mars3d-slider .slider-splitter {
  position: absolute;
  left: -15px;
  top: calc(50% - 15px);
  width: 30px;
  height: 30px;
  background: #f0eeee;
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  padding: 2px;
  border: 1px solid lightgrey;
  z-index: 99999;
}
.mars3d-slider .slider-splitter:hover {
  cursor: ew-resize;
}
.mars3d-mapCompare {
  position: absolute;
  right: 0px;
  top: 0px;
  bottom: 0px;
  width: 50%;
  height: 100%;
  margin: 0;
  padding: 0;
}
/**滚轮样式*/
.mars3d-mousedownview {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 40px;
  height: 40px;
  margin-top: -23px;
  /*图片高度的一半*/
  margin-left: -23px;
  pointer-events: none;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: visibility 0s 0.2s, opacity 0.2s ease-in;
  -moz-transition: visibility 0s 0.2s, opacity 0.2s ease-in;
  transition: visibility 0s 0.2s, opacity 0.2s ease-in;
}
.mars3d-mousedownview-img {
  width: 36px;
  height: 36px;
  background-image: url(./img/cursor.png);
  background-size: 100% 100%;
}
.mars3d-mousedownview-show {
  visibility: visible;
  opacity: 1;
  -webkit-transition: opacity 0.2s ease-out;
  -moz-transition: opacity 0.2s ease-out;
  transition: opacity 0.2s ease-out;
}
.mars3d-overviewMap {
  position: absolute;
  user-select: none;
  width: 200px;
  height: 150px;
  overflow: hidden;
  border: 1px solid orange;
  box-shadow: 2px 2px 3px #2b2b2b;
}
.mars3d-subtitles {
  position: absolute;
  user-select: none;
  overflow: hidden;
  padding: 10px;
  font-size: 30px;
  font-weight: bold;
  letter-spacing: 2px;
  text-align: center;
  z-index: 999;
}
.mar3d-toolButton img,
.mar3d-toolButton svg,
.mar3d-toolButton div {
  height: 100%;
  text-align: center;
}
.mars3d-divGraphic {
  position: absolute;
  left: 0;
  top: 0;
}
.mars3d-divGraphic:hover {
  z-index: 9999 !important;
}
.mars3d-divGraphic-edit {
  background-color: rgba(254, 87, 161, 0.1);
  border: 2px dashed rgba(172, 85, 59, 0.768);
  -webkit-border-radius: 2px;
  border-radius: 2px;
  margin: -2px;
  box-sizing: content-box;
}
/**内置的DivGraphic通用样式【文本动态边框】*/
.mars3d-divBoderLabel {
  position: absolute;
  left: 0px;
  bottom: 0px;
  cursor: pointer;
  --animation-name: mars3d-divBoderLabel-animation;
  --text-left-position: -75px;
}
@keyframes mars3d-divBoderLabel-animation {
  0%,
  100% {
    clip: rect(0px, var(--clip-width-1), 2px, 0px);
  }
  25% {
    clip: rect(0px, 2px, var(--clip-height-1), 0px);
  }
  50% {
    clip: rect(var(--clip-height-2), var(--clip-width-1), var(--clip-width-1), 0px);
  }
  75% {
    clip: rect(0px, var(--clip-width-1), var(--clip-height-1), var(--clip-width-2));
  }
}
.mars3d-divBoderLabel-boder {
  width: var(--boder-width);
  height: var(--boder-height);
  margin: auto;
  color: var(--border-color);
  box-shadow: inset 0 0 0 1px var(--box-shadow-color);
}
.mars3d-divBoderLabel-text {
  color: var(--text-color);
  font-size: var(--text-font-size);
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  font-weight: bolder;
  user-select: none;
  cursor: pointer;
}
.mars3d-divBoderLabel-boder,
.mars3d-divBoderLabel-boder::before,
.mars3d-divBoderLabel-boder::after {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.mars3d-divBoderLabel-boder::before,
.mars3d-divBoderLabel-boder::after {
  content: "";
  margin: -5%;
  box-shadow: inset 0 0 0 2px;
  animation: var(--animation-name) 8s linear infinite;
}
.mars3d-divBoderLabel-boder::before {
  animation-delay: -4s;
}
.mars3d-animation-point,
.mars3d-animation-point:after,
.mars3d-animation-point:before,
.mars3d-animation-point p,
.mars3d-animation-point p:after,
.mars3d-animation-point p:before {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}
.mars3d-animation-point {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 1px solid hsla(0, 0%, 100%, 0.5);
  cursor: pointer;
  color: #0ff;
  background: currentColor;
  z-index: 3;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  box-shadow: 0 0 2em currentColor, 0 0 0.5em currentColor;
  position: absolute;
}
.mars3d-animation-point .mars3d-animation-point-lbl {
  position: absolute;
  transform: translate(-50%, -120%);
  left: 50%;
  font-size: 16px;
  width: fit-content;
  white-space: nowrap;
}
.mars3d-animation-point p {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-animation: mars3d-animation-point-mapAni 2s ease infinite;
  -moz-animation: mars3d-animation-point-mapAni 2s ease infinite;
  -o-animation: mars3d-animation-point-mapAni 2s ease infinite;
  -ms-animation: mars3d-animation-point-mapAni 2s ease infinite;
  animation: mars3d-animation-point-mapAni 2s ease infinite;
}
.mars3d-animation-point .mapError {
  color: red;
}
.mars3d-animation-point .mapWarn {
  color: #b5a603;
}
.mars3d-animation-point .mapSuccess {
  color: #239233;
}
.mars3d-animation-point .mapOrange {
  color: #8c4d34;
}
.mars3d-animation-point:after,
.mars3d-animation-point:before,
.mars3d-animation-point p:after,
.mars3d-animation-point p:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  left: 50%;
  top: 50%;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.mars3d-animation-point:after,
.mars3d-animation-point:before {
  border: 1px solid;
  -webkit-animation: mars3d-animation-point-mapAni 1s ease infinite;
  -moz-animation: mars3d-animation-point-mapAni 1s ease infinite;
  -o-animation: mars3d-animation-point-mapAni 1s ease infinite;
  -ms-animation: mars3d-animation-point-mapAni 1s ease infinite;
  animation: mars3d-animation-point-mapAni 1s ease infinite;
}
.mars3d-animation-point p:before {
  border: 1px solid;
}
@-webkit-keyframes mars3d-animation-point-mapAni {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
    filter: alpha(opacity=1);
  }
  25% {
    width: 120%;
    height: 120%;
    opacity: 0.7;
    filter: alpha(opacity=70);
  }
  50% {
    width: 200%;
    height: 200%;
    opacity: 0.5;
    filter: alpha(opacity=50);
  }
  75% {
    width: 300%;
    height: 300%;
    opacity: 0.2;
    filter: alpha(opacity=20);
  }
  to {
    width: 400%;
    height: 400%;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@-moz-keyframes mars3d-animation-point-mapAni {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
    filter: alpha(opacity=1);
  }
  25% {
    width: 120%;
    height: 120%;
    opacity: 0.7;
    filter: alpha(opacity=70);
  }
  50% {
    width: 200%;
    height: 200%;
    opacity: 0.5;
    filter: alpha(opacity=50);
  }
  75% {
    width: 300%;
    height: 300%;
    opacity: 0.2;
    filter: alpha(opacity=20);
  }
  to {
    width: 400%;
    height: 400%;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@-o-keyframes mars3d-animation-point-mapAni {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
    filter: alpha(opacity=1);
  }
  25% {
    width: 120%;
    height: 120%;
    opacity: 0.7;
    filter: alpha(opacity=70);
  }
  50% {
    width: 200%;
    height: 200%;
    opacity: 0.5;
    filter: alpha(opacity=50);
  }
  75% {
    width: 300%;
    height: 300%;
    opacity: 0.2;
    filter: alpha(opacity=20);
  }
  to {
    width: 400%;
    height: 400%;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@-ms-keyframes mars3d-animation-point-mapAni {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
    filter: alpha(opacity=1);
  }
  25% {
    width: 120%;
    height: 120%;
    opacity: 0.7;
    filter: alpha(opacity=70);
  }
  50% {
    width: 200%;
    height: 200%;
    opacity: 0.5;
    filter: alpha(opacity=50);
  }
  75% {
    width: 300%;
    height: 300%;
    opacity: 0.2;
    filter: alpha(opacity=20);
  }
  to {
    width: 400%;
    height: 400%;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@keyframes mars3d-animation-point-mapAni {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
    filter: alpha(opacity=1);
  }
  25% {
    width: 120%;
    height: 120%;
    opacity: 0.7;
    filter: alpha(opacity=70);
  }
  50% {
    width: 200%;
    height: 200%;
    opacity: 0.5;
    filter: alpha(opacity=50);
  }
  75% {
    width: 300%;
    height: 300%;
    opacity: 0.2;
    filter: alpha(opacity=20);
  }
  to {
    width: 400%;
    height: 400%;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
.mars3d-divUpLabel {
  text-align: center;
  background: transparent;
  color: white;
  display: block;
  box-sizing: border-box;
  animation-duration: 1s;
  animation-fill-mode: both;
  animation-name: mars3d-divUpLabel-tinUpIn;
}
.mars3d-divUpLabel-text {
  writing-mode: vertical-lr;
  font-size: 16px;
  letter-spacing: 4px;
}
.mars3d-divUpLabel-line {
  display: block;
  height: 100px;
  width: 1.5px;
  margin-left: calc(50% - 1px);
  margin-top: 3px;
  background-color: white;
}
@keyframes mars3d-divUpLabel-tinUpIn {
  0% {
    opacity: 0;
    transform: scale(1, 1) translateY(-900%);
  }
  50%,
  70%,
  90% {
    opacity: 1;
    transform: scale(1.1, 1.1) translateY(0);
  }
  100%,
  60%,
  80% {
    opacity: 1;
    transform: scale(1, 1) translateY(0);
  }
}
/*****popup弹出框样式******/
.mars3d-popup {
  -webkit-user-select: auto;
  -moz-user-select: auto;
  -ms-user-select: auto;
  user-select: auto;
}
.mars3d-popup-close-button {
  position: absolute;
  top: 0;
  right: 0;
  padding: 4px 4px 0 0;
  text-align: center;
  width: 20px;
  height: 20px;
  font: 16px/14px Tahoma, Verdana, sans-serif;
  text-decoration: none;
  font-weight: bold;
  background: transparent;
  z-index: 20170825;
  cursor: pointer;
}
.mars3d-popup-content-wrapper {
  text-align: center;
  box-shadow: 0 3px 14px rgba(0, 0, 0, 0.4);
  padding: 1px;
  text-align: left;
  border-radius: 3px;
}
.mars3d-popup-content {
  margin: 10px;
  line-height: 1.4;
  font-size: 13px;
  max-width: 700px;
  min-width: 50px;
  max-height: 550px;
  overflow-y: auto;
}
.mars3d-popup-tip-container {
  margin: 0 auto;
  width: 40px;
  height: 17px;
  position: relative;
  overflow: hidden;
}
.mars3d-popup-tip {
  box-shadow: 0 3px 14px rgba(0, 0, 0, 0.4);
  width: 17px;
  height: 17px;
  padding: 1px;
  margin: -10px auto 0;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}
.mars3d-popup-scrolled {
  overflow: auto;
  border-bottom: 1px solid #ddd;
  border-top: 1px solid #ddd;
}
.mars3d-popup-color {
  color: var(--mars-text-color, #ffffff);
}
.mars3d-popup-background {
  background: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
}
.mars3d-popup-animation {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-name: mars3d-popup-swashIn;
}
@keyframes mars3d-popup-swashIn {
  0% {
    opacity: 0;
    transform-origin: 50% 50%;
    transform: scale(0, 0);
  }
  90% {
    opacity: 1;
    transform-origin: 50% 50%;
    transform: scale(0.9, 0.9);
  }
  100% {
    opacity: 1;
    transform-origin: 50% 50%;
    transform: scale(1, 1);
  }
}
.mars3d-popup-btn {
  padding: 3px 10px;
  border: 1px solid #209ffd;
  background: #209ffd1c;
}
/* all 中的html样式 */
.mars3d-template-title {
  height: 33px;
  line-height: 33px;
  padding: 0 10px 0 10px;
  font-size: 16px;
  color: var(--mars-text-color, #ffffff);
  border-bottom: 1px solid var(--mars-hover-color, #3ea6ff);
  overflow: hidden;
}
.mars3d-template-title a {
  color: var(--mars-msg-title-color, #479be0);
  text-decoration: none;
}
.mars3d-template-content {
  min-width: 150px;
  margin-top: 0px;
  padding: 10px;
  color: var(--mars-text-color, #ffffff);
  font-size: 14px;
  max-height: 490px;
  overflow-y: auto;
}
.mars3d-template-content > div {
  margin-top: 5px;
}
.mars3d-template-content label {
  margin: 0 10px;
  padding-right: 6px;
  min-width: 55px;
  float: left;
}
.mars3d-template-content input {
  color: var(--mars-text-color, #ffffff);
  background-color: transparent;
  padding: 4px 5px;
  border-width: 1px;
  border-style: solid;
}
.mars3d-template-content input::placeholder {
  color: #cdcdcd;
}
.mars3d-template-content textarea {
  color: var(--mars-text-color, #ffffff);
  background-color: transparent;
  padding: 4px 5px;
  border-width: 1px;
  border-style: solid;
  height: 60px;
  resize: none;
}
.mars3d-template-content textarea::placeholder {
  color: #cdcdcd;
}
/*****tooltip弹出框样式******/
.mars3d-tooltip {
  padding: 6px;
  white-space: nowrap;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  max-width: 700px;
  min-width: 50px;
  max-height: 550px;
  color: var(--mars-text-color, #ffffff);
  background: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
  border: 1px solid var(--mars-base-bg, rgba(63, 72, 84, 0.9));
}
.mars3d-tooltip-top:before,
.mars3d-tooltip-bottom:before,
.mars3d-tooltip-left:before,
.mars3d-tooltip-right:before {
  position: absolute;
  pointer-events: none;
  border: 6px solid transparent;
  background: transparent;
  content: "";
}
.mars3d-tooltip-top:before {
  border-top-color: var(--mars-base-bg, rgba(23, 49, 71, 0.8));
}
.mars3d-tooltip-bottom:before {
  border-bottom-color: var(--mars-base-bg, rgba(23, 49, 71, 0.8));
}
.mars3d-tooltip-left:before {
  border-left-color: var(--mars-base-bg, rgba(23, 49, 71, 0.8));
}
.mars3d-tooltip-right:before {
  border-right-color: var(--mars-base-bg, rgba(23, 49, 71, 0.8));
}
/* Directions */
.mars3d-tooltip-bottom {
  margin-top: 6px;
}
.mars3d-tooltip-top {
  margin-top: -6px;
}
.mars3d-tooltip-bottom:before,
.mars3d-tooltip-top:before {
  left: 50%;
  margin-left: -6px;
}
.mars3d-tooltip-top:before {
  bottom: 0;
  margin-bottom: -12px;
  border-top-color: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
}
.mars3d-tooltip-bottom:before {
  top: 0;
  margin-top: -6px;
  margin-left: -6px;
  border-bottom-color: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
}
.mars3d-tooltip-left {
  margin-left: -6px;
}
.mars3d-tooltip-right {
  margin-left: 6px;
}
.mars3d-tooltip-left:before,
.mars3d-tooltip-right:before {
  top: 50%;
  margin-top: -6px;
}
.mars3d-tooltip-left:before {
  right: 0;
  margin-right: -12px;
  border-left-color: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
}
.mars3d-tooltip-right:before {
  left: 0;
  margin-left: -6px;
  border-right-color: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
}
.mars3d-divlayer-camera {
  backface-visibility: hidden;
}
/** 覆盖cesium 控件相关样式 */
/**cesium info信息框*/
.cesium-infoBox {
  background: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
}
.cesium-infoBox-title {
  background-color: rgba(23, 49, 71, 0.8);
}
/**cesium 任务栏的FPS信息*/
.cesium-performanceDisplay-defaultContainer .cesium-performanceDisplay-ms,
.cesium-performanceDisplay-defaultContainer .cesium-performanceDisplay-fps {
  color: #fff;
}
/**cesium tileset调试信息面板*/
.cesium-cesiumInspector {
  background-color: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
}
/**animation时间面板*/
.cesium-viewer-animationContainer {
  z-index: 12;
}
/** 按钮工具栏 */
.cesium-viewer-toolbar {
  /** 按钮统一样式 */
  /**底图切换 弹出面板*/
  /**帮助 弹出面板*/
  /**POI查询输入框*/
}
.cesium-viewer-toolbar .cesium-button {
  background-color: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
  border-radius: 2px;
  border-width: 1px;
  color: var(--mars-text-color, #ffffff);
  fill: #e6e6e6;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
}
.cesium-viewer-toolbar .cesium-button:hover {
  background-color: #3385ff;
  box-shadow: none;
  border: none;
}
.cesium-viewer-toolbar .cesium-button img,
.cesium-viewer-toolbar .cesium-button svg,
.cesium-viewer-toolbar .cesium-button div,
.cesium-viewer-toolbar .cesium-button .cesium-svgPath-svg {
  scale: 0.8;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  text-align: center;
}
.cesium-viewer-toolbar .cesium-button img:hover,
.cesium-viewer-toolbar .cesium-button svg:hover,
.cesium-viewer-toolbar .cesium-button div:hover,
.cesium-viewer-toolbar .cesium-button .cesium-svgPath-svg:hover {
  scale: 0.9;
}
.cesium-viewer-toolbar .cesium-baseLayerPicker-dropDown {
  background-color: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
  max-height: 700px;
  z-index: 99;
}
.cesium-viewer-toolbar .cesium-navigationHelpButton-wrapper .cesium-navigation-help-instructions,
.cesium-viewer-toolbar .cesium-navigationHelpButton-wrapper .cesium-navigation-button {
  background: none;
}
.cesium-viewer-toolbar .cesium-navigationHelpButton-wrapper .cesium-navigation-help,
.cesium-viewer-toolbar .cesium-navigationHelpButton-wrapper .cesium-navigation-button-selected {
  background-color: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
}
.cesium-viewer-toolbar .cesium-navigationHelpButton-wrapper .cesium-navigation-button-unselected {
  background-color: rgba(105, 105, 105, 0.75);
}
.cesium-viewer-toolbar .cesium-navigationHelpButton-wrapper .cesium-navigation-button-unselected:hover {
  background-color: var(--mars-base-bg, rgba(76, 76, 76, 0.75));
}
.cesium-viewer-toolbar .cesium-viewer-geocoderContainer .cesium-geocoder-input {
  background-color: rgba(63, 72, 84, 0.7);
  border-width: 1px;
}
.cesium-viewer-toolbar .cesium-viewer-geocoderContainer .cesium-geocoder-input:focus {
  background-color: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
}
.cesium-viewer-toolbar .cesium-viewer-geocoderContainer .search-results {
  z-index: 99;
  background-color: rgba(23, 49, 71, 0.8);
}
.cesium-viewer-toolbar .cesium-viewer-geocoderContainer .cesium-geocoder-searchButton {
  background-color: var(--mars-base-bg, rgba(63, 72, 84, 0.9));
  border-radius: 2px;
  border-width: 1px;
  fill: #e6e6e6;
}
/** 修改定位："top-right"上侧靠右 */
/** 按钮工具栏修改定位："top-left"上侧靠左 */
.cesium-viewer-toolbar-top-left {
  top: 10px;
  bottom: auto;
  left: 10px;
  right: auto;
  /**帮助 弹出面板*/
  /**底图切换 弹出面板*/
}
.cesium-viewer-toolbar-top-left .cesium-navigationHelpButton-wrapper .cesium-navigation-help {
  left: 40px;
  transform-origin: left top;
}
.cesium-viewer-toolbar-top-left .cesium-baseLayerPicker-dropDown {
  left: 10px;
}
/** 修改定位："left-top"左侧靠上 */
.cesium-viewer-toolbar-left-top {
  top: 10px;
  bottom: auto;
  left: 10px;
  right: auto;
  /**底图切换 弹出面板*/
  /**帮助 弹出面板*/
  /**二维三维切换*/
  /**POI查询输入框*/
}
.cesium-viewer-toolbar-left-top > .cesium-toolbar-button,
.cesium-viewer-toolbar-left-top .cesium-navigationHelpButton-wrapper,
.cesium-viewer-toolbar-left-top .cesium-viewer-geocoderContainer {
  float: left;
  clear: both;
  margin-bottom: 5px;
}
.cesium-viewer-toolbar-left-top .cesium-baseLayerPicker-dropDown {
  left: 40px;
  margin-bottom: 5px;
}
.cesium-viewer-toolbar-left-top .cesium-navigationHelpButton-wrapper .cesium-navigation-help {
  top: 0px;
  left: 40px;
  transform-origin: left top;
}
.cesium-viewer-toolbar-left-top .cesium-sceneModePicker-wrapper {
  width: auto;
}
.cesium-viewer-toolbar-left-top .cesium-sceneModePicker-wrapper .cesium-sceneModePicker-dropDown-icon {
  float: right;
  margin: 0 3px;
}
.cesium-viewer-toolbar-left-top .cesium-viewer-geocoderContainer .search-results {
  left: 0;
  right: 40px;
  width: auto;
}
/** 修改定位："left-bottom"左侧靠下 */
.cesium-viewer-toolbar-left-bottom {
  top: auto;
  bottom: 60px;
  left: 10px;
  right: auto;
  /**底图切换 弹出面板*/
  /**帮助 弹出面板*/
  /**二维三维切换*/
  /**POI查询输入框*/
}
.cesium-viewer-toolbar-left-bottom > .cesium-toolbar-button,
.cesium-viewer-toolbar-left-bottom .cesium-navigationHelpButton-wrapper,
.cesium-viewer-toolbar-left-bottom .cesium-viewer-geocoderContainer {
  float: left;
  clear: both;
  margin-bottom: 5px;
}
.cesium-viewer-toolbar-left-bottom .cesium-baseLayerPicker-dropDown {
  bottom: 0;
  left: 40px;
  margin-bottom: 5px;
}
.cesium-viewer-toolbar-left-bottom .cesium-navigationHelpButton-wrapper .cesium-navigation-help {
  top: auto;
  bottom: 0;
  left: 40px;
  transform-origin: left bottom;
}
.cesium-viewer-toolbar-left-bottom .cesium-sceneModePicker-wrapper {
  width: auto;
}
.cesium-viewer-toolbar-left-bottom .cesium-sceneModePicker-wrapper .cesium-sceneModePicker-dropDown-icon {
  float: right;
  margin: 0 3px;
}
.cesium-viewer-toolbar-left-bottom .cesium-viewer-geocoderContainer .search-results {
  left: 0;
  right: 40px;
  width: auto;
}
/** 修改定位："right-top"右侧靠上 */
.cesium-viewer-toolbar-right-top {
  top: 10px;
  bottom: auto;
  right: 10px;
  left: auto;
  /**底图切换 弹出面板*/
  /**帮助 弹出面板*/
  /**二维三维切换*/
  /**POI查询输入框*/
}
.cesium-viewer-toolbar-right-top > .cesium-toolbar-button,
.cesium-viewer-toolbar-right-top .cesium-navigationHelpButton-wrapper,
.cesium-viewer-toolbar-right-top .cesium-viewer-geocoderContainer {
  float: right;
  clear: both;
  margin-bottom: 5px;
}
.cesium-viewer-toolbar-right-top .cesium-baseLayerPicker-dropDown {
  right: 40px;
  margin-bottom: 5px;
}
.cesium-viewer-toolbar-right-top .cesium-navigationHelpButton-wrapper .cesium-navigation-help {
  top: 0;
  bottom: auto;
  right: 40px;
  transform-origin: right top;
}
.cesium-viewer-toolbar-right-top .cesium-sceneModePicker-wrapper {
  width: auto;
}
.cesium-viewer-toolbar-right-top .cesium-sceneModePicker-wrapper .cesium-sceneModePicker-dropDown-icon {
  float: left;
  margin: 0 3px;
}
.cesium-viewer-toolbar-right-top .cesium-viewer-geocoderContainer .search-results {
  right: 0;
  left: 40px;
  width: auto;
}
/** 修改定位："right-bottom"右侧靠下 */
.cesium-viewer-toolbar-right-bottom {
  top: auto;
  bottom: 60px;
  right: 10px;
  left: auto;
  /**底图切换 弹出面板*/
  /**帮助 弹出面板*/
  /**二维三维切换*/
  /**POI查询输入框*/
}
.cesium-viewer-toolbar-right-bottom > .cesium-toolbar-button,
.cesium-viewer-toolbar-right-bottom .cesium-navigationHelpButton-wrapper,
.cesium-viewer-toolbar-right-bottom .cesium-viewer-geocoderContainer {
  float: right;
  clear: both;
  margin-bottom: 5px;
}
.cesium-viewer-toolbar-right-bottom .cesium-baseLayerPicker-dropDown {
  bottom: 0;
  right: 40px;
  margin-bottom: 5px;
}
.cesium-viewer-toolbar-right-bottom .cesium-navigationHelpButton-wrapper .cesium-navigation-help {
  top: auto;
  bottom: 0;
  right: 40px;
  transform-origin: right bottom;
}
.cesium-viewer-toolbar-right-bottom .cesium-sceneModePicker-wrapper {
  width: auto;
}
.cesium-viewer-toolbar-right-bottom .cesium-sceneModePicker-wrapper .cesium-sceneModePicker-dropDown-icon {
  float: left;
  margin: 0 3px;
}
.cesium-viewer-toolbar-right-bottom .cesium-viewer-geocoderContainer .search-results {
  right: 0;
  left: 40px;
  width: auto;
}
