# jQuery MiniColors: A tiny color picker built on jQuery

Developed by <PERSON> for A Beautiful Site, LLC

Licensed under the MIT license: http://opensource.org/licenses/MIT

## Demo & Documentation

http://labs.abeautifulsite.net/jquery-minicolors/

## Install via NPM

This is the official NPM version of MiniColors:

```
npm install --save @claviska/jquery-minicolors
```

**Note:** There is another version on NPM without the namespace that is out of date and not supported. I did not create it nor do I have control of it. Please use the official NPM version to ensure you have the latest updates.
