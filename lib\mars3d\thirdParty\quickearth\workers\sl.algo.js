/* eslint-disable */
const d2=E;(function(g,X){const d1=E,u=g();while(!![]){try{const f=parseInt(d1(0x9e))/0x1+-parseInt(d1(0x9f))/0x2+-parseInt(d1(0x8d))/0x3*(-parseInt(d1(0x97))/0x4)+parseInt(d1(0xa2))/0x5*(-parseInt(d1(0x8c))/0x6)+-parseInt(d1(0x8a))/0x7+-parseInt(d1(0xb2))/0x8*(parseInt(d1(0xb0))/0x9)+parseInt(d1(0xa0))/0xa;if(f===X)break;else u['push'](u['shift']());}catch(j){u['push'](u['shift']());}}}(d,0x591c4),importScripts(d2(0x85)));function d(){const d8=['vReso','scale','min','15894lKUwoF','无法识别的格点数据类型！','176cXkHHW','log','seeds','lineMinCount','length','init_','./proj4.js','stayLoop','dataType','front\x20interate\x20times\x20','xEnd','1465597fHLdIE','disableDensityControl','261978ryuzHF','83907TJTZLs','EPSG:4326','wScale','undef','ySize','xDelta','gridOptions','yStart','allLoop','point2D','28XSpjOz','xStart','data','zValues','zScale','abs','sqrt','367297byQXHt','44746fOeSvJ','2908270GChlOH','newPos','25NUhqJd','wAlgo','seed','algo','yDelta','hash','hReso','yEnd','push','xSize','offset'];d=function(){return d8;};return d();}function getGrid(g,X,u,f){const d3=d2;switch(g){case 0x1:return new Uint8Array(X,u,f);case 0x0:return new Int8Array(X,u,f);case 0x3:return new Uint16Array(X,u*0x2,f);case 0x2:return new Int16Array(X,u*0x2,f);case 0x5:return new Uint32Array(X,u*0x4,f);case 0x4:return new Int32Array(X,u*0x4,f);case 0x6:return new Float32Array(X,u*0x4,f);case 0x7:return new Float64Array(X,u*0x8,f);default:throw new Error(d3(0xb1));}}function getGridArray(g,X,u,f){const d4=d2,j=[];for(let I=0x0;I<f;I++){j[d4(0xaa)](getGrid(g,X,I*u,u));}return j;}function E(g,X){const u=d();return E=function(f,j){f=f-0x81;let I=u[f];return I;},E(g,X);}function getPos(g,X,u){return Math['floor']((u-g)/X);}function read2D(g,X,u,f,j,I){let V=g*u+X,e=f[V];if(e===j)return j;return I(e);}function interp2D(g,X,u,f,j,I){const d5=d2;let V=getPos(u[d5(0x98)],u[d5(0x92)],g),e=getPos(u[d5(0x94)],u['yDelta'],X);if(V<0x0)return undefined;if(e<0x0)return undefined;if(V>u[d5(0xab)]-0x1)return undefined;if(e>u[d5(0x91)]-0x1)return undefined;let D=read2D(e,V,u['xSize'],j,f,I);if(isNaN(D)||D===f)return[f,V,e];if(V===0x0||e===0x0||V===u['xSize']-0x1||e===u[d5(0x91)]-0x1)return[D,V,e];let S=u[d5(0x94)]+u[d5(0xa6)]*e,k=u[d5(0x98)]+u['xDelta']*V,F=S+u[d5(0xa6)],r=k+u[d5(0x92)],a=read2D(e,V+0x1,u[d5(0xab)],j,f,I);if(a===f)a=D;let q=read2D(e+0x1,V,u[d5(0xab)],j,f,I);if(q===f)q=D;let i=read2D(e+0x1,V+0x1,u['xSize'],j,f,I);if(i===f)i=D;let N=(g-k)/(r-k),o=(X-S)/(F-S),C=D*(0x1-N)+a*N,Z=q*(0x1-N)+i*N,O=C*(0x1-o)+Z*o;return[O,V,e];}function nextPos(g,X,f,j,I,V,e,D,S,k,F,r,a,q,i){const d6=d2;let N=0x0,o=0x0,C=0x1;if(g){if(X[0x2]>=f[j-0x1]||X[0x2]<=0x0)return undefined;else for(let s=j-0x2;s>=0x0;s--){if(X[0x2]>=f[s]){o=s,N=s+0x1;const l=f[s],J=f[s+0x1];C=0x1-(X[0x2]-l)/(J-l);break;}}}const Z=interp2D(X[0x0],X[0x1],F,r,I[N],D);if(Z===undefined||Z[0x0]===r)return undefined;const O=interp2D(X[0x0],X[0x1],F,r,V[N],S);if(O[0x0]===r)return undefined;const Q=e?interp2D(X[0x0],X[0x1],F,a,e[N],k):[0x0,0x0,0x0];if(Q[0x0]===a)return undefined;let y,T,L;if(g&&C!==0x1){const W=interp2D(X[0x0],X[0x1],F,r,I[o],D);if(W[0x0]===r)return undefined;const h=interp2D(X[0x0],X[0x1],F,r,V[o],S);if(h[0x0]===r)return undefined;const G=e?interp2D(X[0x0],X[0x1],F,a,e[o],k):[0x0,0x0,0x0];if(G[0x0]===a)return undefined;y=W[0x0]*C+Z[0x0]*(0x1-C),T=h[0x0]*C+O[0x0]*(0x1-C),L=G[0x0]===undefined?0x0:G[0x0]*C+Q[0x0]*(0x1-C);}else y=Z[0x0],T=O[0x0],L=Q[0x0];!i&&(y=-y,T=-T,L=-L);const p=Math[d6(0x9d)](y*y+T*T+L*L),x=q/p,K=y*x,Y=T*x,H=L*x,M=proj4(proj4(d6(0x8e)),proj4('EPSG:3857'),[X[0x0],X[0x1]]),c=[M[0x0]+K,M[0x1]+Y,X[0x2]+H],P=proj4(proj4('EPSG:3857'),proj4(d6(0x8e)),[c[0x0],c[0x1]]);if(X[0x0]>0xaa&&P[0x0]<0x0)P[0x0]+=0x168;else X[0x0]<-0xaa&&P[0x0]>0x0&&(P-=0x168);return{'newPos':[P[0x0],P[0x1],c[0x2]]};}function getTransformer(g,X,u){g=g||0x1,X=X||0x0;if(u===0x2)return f=>(f+X)*g;else return u===0x1?f=>f*g+X:f=>f;}function getHash(g,X,u,f){const j=getPos(X[0x0],X[0x1],g[0x0]),I=getPos(u[0x0],u[0x1],g[0x1]),V=getPos(f[0x0],f[0x1],g[0x2]);return j+'_'+I+'_'+V;}function sl(g){const d7=d2,X=g[d7(0x99)],u=X[d7(0x81)],f=X[d7(0x93)],j=f[d7(0xab)]*f[d7(0x91)],I=f[d7(0x9a)]['length'],V=getGridArray(X[d7(0x87)],X['u'],j,I),e=getTransformer(X[d7(0xae)],X[d7(0xac)],X[d7(0xa5)]),D=getGridArray(X[d7(0x87)],X['v'],j,I),S=getTransformer(X['scale'],X[d7(0xac)],X['algo']),k=X['w']&&getGridArray(X['wDataType'],X['w'],j,I),F=k&&getTransformer(X[d7(0x8f)],X['wOffset'],X[d7(0xa3)]),r=X[d7(0x90)],a=X['wUndef'],q=X['step']||f[d7(0x92)]*0xd6d8/0x2,N=f[d7(0x9a)][d7(0x83)]>0x1,o=0x3,C=u[d7(0x83)]/o,Z=f[d7(0x9a)],O=X[d7(0xa8)]||Math[d7(0x9c)](f['xDelta']),Q=X[d7(0xad)]||0x3e8,y=[Math[d7(0xaf)](f[d7(0x98)],f[d7(0x89)]),O],T=[Math[d7(0xaf)](f[d7(0x94)],f[d7(0xa9)]),O],L=[Math[d7(0xaf)](Z[0x0],Z[Z['length']-0x1]),Q],p=X['backward']===undefined?!![]:X['backward'],x=[],w={},K=X[d7(0x95)]||0xc350,Y=X[d7(0x86)]||0xa,H=X[d7(0x82)]||0xa,M=X[d7(0x9b)]||0x1,c=X[d7(0x96)]===undefined?![]:X[d7(0x96)],P=X[d7(0x8b)]===undefined?!![]:!X[d7(0x8b)];for(let z=0x0;z<C;z++){const J=o*z,v=[u[J],u[J+0x1],u[J+0x2]];let W=c?[[v[0x0],v[0x1]]]:[[v[0x0],v[0x1],v[0x2]*M]],h=[...v],G=![],R=d7(0x84)+z,m='',n=0x0,t=0x0;while(n<K){const A=nextPos(N,h,Z,I,V,D,k,e,S,F,f,r,a,q,!![]);h=A?.[d7(0xa1)];if(!h)break;n++;const b=getHash(h,y,T,L);m[d7(0x83)]===0x0&&(m=b);if(w[b]!==undefined){if(w[b][d7(0xa7)]===R&&w[b][d7(0xa4)]===z)t++;else{if(P){c?W[d7(0xaa)]([h[0x0],h[0x1]]):W[d7(0xaa)]([h[0x0],h[0x1],h[0x2]*M]);break;}}}else t=0x0;if(t>Y)break;R=b,w[b]={'hash':b,'seed':z},c?W[d7(0xaa)]([h[0x0],h[0x1]]):W[d7(0xaa)]([h[0x0],h[0x1],h[0x2]*M]);}W[d7(0x83)]>H&&x[d7(0xaa)](W),h=v;let B=[];R=m;n>K/0x2&&console[d7(0xb3)](d7(0x88)+n);n=0x0,t=0x0;if(p&&!G)while(n<K){const U=nextPos(N,h,Z,I,V,D,k,e,S,F,f,r,a,q,![]);h=U?.[d7(0xa1)];if(!h)break;n++;const d0=getHash(h,y,T,L);if(w[d0]!==undefined){if(w[d0][d7(0xa7)]===R&&w[d0][d7(0xa4)]===z)t++;else{if(P){c?B[d7(0xaa)]([h[0x0],h[0x1]]):B['push']([h[0x0],h[0x1],h[0x2]*M]);break;}}}else t=0x0;if(t>Y)break;R=d0,w[d0]={'hash':d0,'seed':z},c?B[d7(0xaa)]([h[0x0],h[0x1]]):B[d7(0xaa)]([h[0x0],h[0x1],h[0x2]*M]);}n>K/0x2&&console[d7(0xb3)]('back\x20interate\x20times\x20'+n),B[d7(0x83)]>H&&x[d7(0xaa)](B['reverse']());}const s={'streamlines':x,'u':X['u'],'v':X['v']},l=[X['u'],X['v']];return k&&(s['w']=X['w'],l[d7(0xaa)](X['w'])),{'message':s,'transfers':l};}
