/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.132
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as e}from"./chunk-S7SJV35K.js";import"./chunk-RKHPBM2Z.js";import"./chunk-RTWVL2BE.js";import"./chunk-GCGQM3V5.js";import"./chunk-DZ42UGGL.js";import"./chunk-UXDFNPCW.js";import"./chunk-QNF6DOIT.js";import"./chunk-RVXELCI5.js";import"./chunk-S2VOP6QV.js";import"./chunk-HEWRROTS.js";import"./chunk-W2IXI6NO.js";import"./chunk-ZO574IGQ.js";import"./chunk-YQWHD5N7.js";import"./chunk-4AYOP6XD.js";import{e as o}from"./chunk-F4R6NSH4.js";function i(r,t){return o(t)&&(r=e.unpack(r,t)),e.createGeometry(r)}var a=i;export{a as default};
