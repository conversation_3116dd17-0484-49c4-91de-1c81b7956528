var protomaps=(()=>{var xi=Object.create;var Te=Object.defineProperty;var gi=Object.getOwnPropertyDescriptor;var yi=Object.getOwnPropertyNames,Wt=Object.getOwnPropertySymbols,bi=Object.getPrototypeOf,Ht=Object.prototype.hasOwnProperty,wi=Object.prototype.propertyIsEnumerable;var Kt=(t,e,r)=>e in t?Te(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Jt=(t,e)=>{for(var r in e||(e={}))Ht.call(e,r)&&Kt(t,r,e[r]);if(Wt)for(var r of Wt(e))wi.call(e,r)&&Kt(t,r,e[r]);return t};var Gt=t=>Te(t,"__esModule",{value:!0});var W=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),vi=(t,e)=>{Gt(t);for(var r in e)Te(t,r,{get:e[r],enumerable:!0})},_i=(t,e,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of yi(e))!Ht.call(t,i)&&i!=="default"&&Te(t,i,{get:()=>e[i],enumerable:!(r=gi(e,i))||r.enumerable});return t},U=t=>_i(Gt(Te(t!=null?xi(bi(t)):{},"default",t&&t.__esModule&&"default"in t?{get:()=>t.default,enumerable:!0}:{value:t,enumerable:!0})),t);var O=(t,e,r)=>new Promise((i,n)=>{var a=l=>{try{o(r.next(l))}catch(u){n(u)}},s=l=>{try{o(r.throw(l))}catch(u){n(u)}},o=l=>l.done?i(l.value):Promise.resolve(l.value).then(a,s);o((r=r.apply(t,e)).next())});var ee=W((ca,Qt)=>{"use strict";Qt.exports=me;function me(t,e){this.x=t,this.y=e}me.prototype={clone:function(){return new me(this.x,this.y)},add:function(t){return this.clone()._add(t)},sub:function(t){return this.clone()._sub(t)},multByPoint:function(t){return this.clone()._multByPoint(t)},divByPoint:function(t){return this.clone()._divByPoint(t)},mult:function(t){return this.clone()._mult(t)},div:function(t){return this.clone()._div(t)},rotate:function(t){return this.clone()._rotate(t)},rotateAround:function(t,e){return this.clone()._rotateAround(t,e)},matMult:function(t){return this.clone()._matMult(t)},unit:function(){return this.clone()._unit()},perp:function(){return this.clone()._perp()},round:function(){return this.clone()._round()},mag:function(){return Math.sqrt(this.x*this.x+this.y*this.y)},equals:function(t){return this.x===t.x&&this.y===t.y},dist:function(t){return Math.sqrt(this.distSqr(t))},distSqr:function(t){var e=t.x-this.x,r=t.y-this.y;return e*e+r*r},angle:function(){return Math.atan2(this.y,this.x)},angleTo:function(t){return Math.atan2(this.y-t.y,this.x-t.x)},angleWith:function(t){return this.angleWithSep(t.x,t.y)},angleWithSep:function(t,e){return Math.atan2(this.x*e-this.y*t,this.x*t+this.y*e)},_matMult:function(t){var e=t[0]*this.x+t[1]*this.y,r=t[2]*this.x+t[3]*this.y;return this.x=e,this.y=r,this},_add:function(t){return this.x+=t.x,this.y+=t.y,this},_sub:function(t){return this.x-=t.x,this.y-=t.y,this},_mult:function(t){return this.x*=t,this.y*=t,this},_div:function(t){return this.x/=t,this.y/=t,this},_multByPoint:function(t){return this.x*=t.x,this.y*=t.y,this},_divByPoint:function(t){return this.x/=t.x,this.y/=t.y,this},_unit:function(){return this._div(this.mag()),this},_perp:function(){var t=this.y;return this.y=this.x,this.x=-t,this},_rotate:function(t){var e=Math.cos(t),r=Math.sin(t),i=e*this.x-r*this.y,n=r*this.x+e*this.y;return this.x=i,this.y=n,this},_rotateAround:function(t,e){var r=Math.cos(t),i=Math.sin(t),n=e.x+r*(this.x-e.x)-i*(this.y-e.y),a=e.y+i*(this.x-e.x)+r*(this.y-e.y);return this.x=n,this.y=a,this},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}};me.convert=function(t){return t instanceof me?t:Array.isArray(t)?new me(t[0],t[1]):t}});var mt=W((da,er)=>{"use strict";var zi=ee();er.exports=pe;function pe(t,e,r,i,n){this.properties={},this.extent=r,this.type=0,this._pbf=t,this._geometry=-1,this._keys=i,this._values=n,t.readFields(Si,this,e)}function Si(t,e,r){t==1?e.id=r.readVarint():t==2?Li(r,e):t==3?e.type=r.readVarint():t==4&&(e._geometry=r.pos)}function Li(t,e){for(var r=t.readVarint()+t.pos;t.pos<r;){var i=e._keys[t.readVarint()],n=e._values[t.readVarint()];e.properties[i]=n}}pe.types=["Unknown","Point","LineString","Polygon"];pe.prototype.loadGeometry=function(){var t=this._pbf;t.pos=this._geometry;for(var e=t.readVarint()+t.pos,r=1,i=0,n=0,a=0,s=[],o;t.pos<e;){if(i<=0){var l=t.readVarint();r=l&7,i=l>>3}if(i--,r===1||r===2)n+=t.readSVarint(),a+=t.readSVarint(),r===1&&(o&&s.push(o),o=[]),o.push(new zi(n,a));else if(r===7)o&&o.push(o[0].clone());else throw new Error("unknown command "+r)}return o&&s.push(o),s};pe.prototype.bbox=function(){var t=this._pbf;t.pos=this._geometry;for(var e=t.readVarint()+t.pos,r=1,i=0,n=0,a=0,s=1/0,o=-1/0,l=1/0,u=-1/0;t.pos<e;){if(i<=0){var f=t.readVarint();r=f&7,i=f>>3}if(i--,r===1||r===2)n+=t.readSVarint(),a+=t.readSVarint(),n<s&&(s=n),n>o&&(o=n),a<l&&(l=a),a>u&&(u=a);else if(r!==7)throw new Error("unknown command "+r)}return[s,l,o,u]};pe.prototype.toGeoJSON=function(t,e,r){var i=this.extent*Math.pow(2,r),n=this.extent*t,a=this.extent*e,s=this.loadGeometry(),o=pe.types[this.type],l,u;function f(g){for(var w=0;w<g.length;w++){var z=g[w],h=180-(z.y+a)*360/i;g[w]=[(z.x+n)*360/i-180,360/Math.PI*Math.atan(Math.exp(h*Math.PI/180))-90]}}switch(this.type){case 1:var c=[];for(l=0;l<s.length;l++)c[l]=s[l][0];s=c,f(s);break;case 2:for(l=0;l<s.length;l++)f(s[l]);break;case 3:for(s=Fi(s),l=0;l<s.length;l++)for(u=0;u<s[l].length;u++)f(s[l][u]);break}s.length===1?s=s[0]:o="Multi"+o;var d={type:"Feature",geometry:{type:o,coordinates:s},properties:this.properties};return"id"in this&&(d.id=this.id),d};function Fi(t){var e=t.length;if(e<=1)return[t];for(var r=[],i,n,a=0;a<e;a++){var s=ki(t[a]);s!==0&&(n===void 0&&(n=s<0),n===s<0?(i&&r.push(i),i=[t[a]]):i.push(t[a]))}return i&&r.push(i),r}function ki(t){for(var e=0,r=0,i=t.length,n=i-1,a,s;r<i;n=r++)a=t[r],s=t[n],e+=(s.x-a.x)*(a.y+s.y);return e}});var pt=W((ma,rr)=>{"use strict";var Mi=mt();rr.exports=tr;function tr(t,e){this.version=1,this.name=null,this.extent=4096,this.length=0,this._pbf=t,this._keys=[],this._values=[],this._features=[],t.readFields(Ci,this,e),this.length=this._features.length}function Ci(t,e,r){t===15?e.version=r.readVarint():t===1?e.name=r.readString():t===5?e.extent=r.readVarint():t===2?e._features.push(r.pos):t===3?e._keys.push(r.readString()):t===4&&e._values.push(Ti(r))}function Ti(t){for(var e=null,r=t.readVarint()+t.pos;t.pos<r;){var i=t.readVarint()>>3;e=i===1?t.readString():i===2?t.readFloat():i===3?t.readDouble():i===4?t.readVarint64():i===5?t.readVarint():i===6?t.readSVarint():i===7?t.readBoolean():null}return e}tr.prototype.feature=function(t){if(t<0||t>=this._features.length)throw new Error("feature index out of bounds");this._pbf.pos=this._features[t];var e=this._pbf.readVarint()+this._pbf.pos;return new Mi(this._pbf,e,this.extent,this._keys,this._values)}});var nr=W((pa,ir)=>{"use strict";var Pi=pt();ir.exports=Di;function Di(t,e){this.layers=t.readFields(Ai,{},e)}function Ai(t,e,r){if(t===3){var i=new Pi(r,r.readVarint()+r.pos);i.length&&(e[i.name]=i)}}});var ar=W((xa,Je)=>{Je.exports.VectorTile=nr();Je.exports.VectorTileFeature=mt();Je.exports.VectorTileLayer=pt()});var sr=W(xt=>{xt.read=function(t,e,r,i,n){var a,s,o=n*8-i-1,l=(1<<o)-1,u=l>>1,f=-7,c=r?n-1:0,d=r?-1:1,g=t[e+c];for(c+=d,a=g&(1<<-f)-1,g>>=-f,f+=o;f>0;a=a*256+t[e+c],c+=d,f-=8);for(s=a&(1<<-f)-1,a>>=-f,f+=i;f>0;s=s*256+t[e+c],c+=d,f-=8);if(a===0)a=1-u;else{if(a===l)return s?NaN:(g?-1:1)*(1/0);s=s+Math.pow(2,i),a=a-u}return(g?-1:1)*s*Math.pow(2,a-i)};xt.write=function(t,e,r,i,n,a){var s,o,l,u=a*8-n-1,f=(1<<u)-1,c=f>>1,d=n===23?Math.pow(2,-24)-Math.pow(2,-77):0,g=i?0:a-1,w=i?1:-1,z=e<0||e===0&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(o=isNaN(e)?1:0,s=f):(s=Math.floor(Math.log(e)/Math.LN2),e*(l=Math.pow(2,-s))<1&&(s--,l*=2),s+c>=1?e+=d/l:e+=d*Math.pow(2,1-c),e*l>=2&&(s++,l/=2),s+c>=f?(o=0,s=f):s+c>=1?(o=(e*l-1)*Math.pow(2,n),s=s+c):(o=e*Math.pow(2,c-1)*Math.pow(2,n),s=0));n>=8;t[r+g]=o&255,g+=w,o/=256,n-=8);for(s=s<<n|o,u+=n;u>0;t[r+g]=s&255,g+=w,s/=256,u-=8);t[r+g-w]|=z*128}});var cr=W((ya,fr)=>{"use strict";fr.exports=k;var Ge=sr();function k(t){this.buf=ArrayBuffer.isView&&ArrayBuffer.isView(t)?t:new Uint8Array(t||0),this.pos=0,this.type=0,this.length=this.buf.length}k.Varint=0;k.Fixed64=1;k.Bytes=2;k.Fixed32=5;var gt=(1<<16)*(1<<16),or=1/gt,Bi=12,lr=typeof TextDecoder=="undefined"?null:new TextDecoder("utf8");k.prototype={destroy:function(){this.buf=null},readFields:function(t,e,r){for(r=r||this.length;this.pos<r;){var i=this.readVarint(),n=i>>3,a=this.pos;this.type=i&7,t(n,e,this),this.pos===a&&this.skip(i)}return e},readMessage:function(t,e){return this.readFields(t,e,this.readVarint()+this.pos)},readFixed32:function(){var t=Qe(this.buf,this.pos);return this.pos+=4,t},readSFixed32:function(){var t=hr(this.buf,this.pos);return this.pos+=4,t},readFixed64:function(){var t=Qe(this.buf,this.pos)+Qe(this.buf,this.pos+4)*gt;return this.pos+=8,t},readSFixed64:function(){var t=Qe(this.buf,this.pos)+hr(this.buf,this.pos+4)*gt;return this.pos+=8,t},readFloat:function(){var t=Ge.read(this.buf,this.pos,!0,23,4);return this.pos+=4,t},readDouble:function(){var t=Ge.read(this.buf,this.pos,!0,52,8);return this.pos+=8,t},readVarint:function(t){var e=this.buf,r,i;return i=e[this.pos++],r=i&127,i<128||(i=e[this.pos++],r|=(i&127)<<7,i<128)||(i=e[this.pos++],r|=(i&127)<<14,i<128)||(i=e[this.pos++],r|=(i&127)<<21,i<128)?r:(i=e[this.pos],r|=(i&15)<<28,Ei(r,t,this))},readVarint64:function(){return this.readVarint(!0)},readSVarint:function(){var t=this.readVarint();return t%2==1?(t+1)/-2:t/2},readBoolean:function(){return Boolean(this.readVarint())},readString:function(){var t=this.readVarint()+this.pos,e=this.pos;return this.pos=t,t-e>=Bi&&lr?Hi(this.buf,e,t):Wi(this.buf,e,t)},readBytes:function(){var t=this.readVarint()+this.pos,e=this.buf.subarray(this.pos,t);return this.pos=t,e},readPackedVarint:function(t,e){if(this.type!==k.Bytes)return t.push(this.readVarint(e));var r=ne(this);for(t=t||[];this.pos<r;)t.push(this.readVarint(e));return t},readPackedSVarint:function(t){if(this.type!==k.Bytes)return t.push(this.readSVarint());var e=ne(this);for(t=t||[];this.pos<e;)t.push(this.readSVarint());return t},readPackedBoolean:function(t){if(this.type!==k.Bytes)return t.push(this.readBoolean());var e=ne(this);for(t=t||[];this.pos<e;)t.push(this.readBoolean());return t},readPackedFloat:function(t){if(this.type!==k.Bytes)return t.push(this.readFloat());var e=ne(this);for(t=t||[];this.pos<e;)t.push(this.readFloat());return t},readPackedDouble:function(t){if(this.type!==k.Bytes)return t.push(this.readDouble());var e=ne(this);for(t=t||[];this.pos<e;)t.push(this.readDouble());return t},readPackedFixed32:function(t){if(this.type!==k.Bytes)return t.push(this.readFixed32());var e=ne(this);for(t=t||[];this.pos<e;)t.push(this.readFixed32());return t},readPackedSFixed32:function(t){if(this.type!==k.Bytes)return t.push(this.readSFixed32());var e=ne(this);for(t=t||[];this.pos<e;)t.push(this.readSFixed32());return t},readPackedFixed64:function(t){if(this.type!==k.Bytes)return t.push(this.readFixed64());var e=ne(this);for(t=t||[];this.pos<e;)t.push(this.readFixed64());return t},readPackedSFixed64:function(t){if(this.type!==k.Bytes)return t.push(this.readSFixed64());var e=ne(this);for(t=t||[];this.pos<e;)t.push(this.readSFixed64());return t},skip:function(t){var e=t&7;if(e===k.Varint)for(;this.buf[this.pos++]>127;);else if(e===k.Bytes)this.pos=this.readVarint()+this.pos;else if(e===k.Fixed32)this.pos+=4;else if(e===k.Fixed64)this.pos+=8;else throw new Error("Unimplemented type: "+e)},writeTag:function(t,e){this.writeVarint(t<<3|e)},realloc:function(t){for(var e=this.length||16;e<this.pos+t;)e*=2;if(e!==this.length){var r=new Uint8Array(e);r.set(this.buf),this.buf=r,this.length=e}},finish:function(){return this.length=this.pos,this.pos=0,this.buf.subarray(0,this.length)},writeFixed32:function(t){this.realloc(4),ge(this.buf,t,this.pos),this.pos+=4},writeSFixed32:function(t){this.realloc(4),ge(this.buf,t,this.pos),this.pos+=4},writeFixed64:function(t){this.realloc(8),ge(this.buf,t&-1,this.pos),ge(this.buf,Math.floor(t*or),this.pos+4),this.pos+=8},writeSFixed64:function(t){this.realloc(8),ge(this.buf,t&-1,this.pos),ge(this.buf,Math.floor(t*or),this.pos+4),this.pos+=8},writeVarint:function(t){if(t=+t||0,t>268435455||t<0){Oi(t,this);return}this.realloc(4),this.buf[this.pos++]=t&127|(t>127?128:0),!(t<=127)&&(this.buf[this.pos++]=(t>>>=7)&127|(t>127?128:0),!(t<=127)&&(this.buf[this.pos++]=(t>>>=7)&127|(t>127?128:0),!(t<=127)&&(this.buf[this.pos++]=t>>>7&127)))},writeSVarint:function(t){this.writeVarint(t<0?-t*2-1:t*2)},writeBoolean:function(t){this.writeVarint(Boolean(t))},writeString:function(t){t=String(t),this.realloc(t.length*4),this.pos++;var e=this.pos;this.pos=Ki(this.buf,t,this.pos);var r=this.pos-e;r>=128&&ur(e,r,this),this.pos=e-1,this.writeVarint(r),this.pos+=r},writeFloat:function(t){this.realloc(4),Ge.write(this.buf,t,this.pos,!0,23,4),this.pos+=4},writeDouble:function(t){this.realloc(8),Ge.write(this.buf,t,this.pos,!0,52,8),this.pos+=8},writeBytes:function(t){var e=t.length;this.writeVarint(e),this.realloc(e);for(var r=0;r<e;r++)this.buf[this.pos++]=t[r]},writeRawMessage:function(t,e){this.pos++;var r=this.pos;t(e,this);var i=this.pos-r;i>=128&&ur(r,i,this),this.pos=r-1,this.writeVarint(i),this.pos+=i},writeMessage:function(t,e,r){this.writeTag(t,k.Bytes),this.writeRawMessage(e,r)},writePackedVarint:function(t,e){e.length&&this.writeMessage(t,Xi,e)},writePackedSVarint:function(t,e){e.length&&this.writeMessage(t,Vi,e)},writePackedBoolean:function(t,e){e.length&&this.writeMessage(t,ji,e)},writePackedFloat:function(t,e){e.length&&this.writeMessage(t,Yi,e)},writePackedDouble:function(t,e){e.length&&this.writeMessage(t,Ui,e)},writePackedFixed32:function(t,e){e.length&&this.writeMessage(t,Ni,e)},writePackedSFixed32:function(t,e){e.length&&this.writeMessage(t,qi,e)},writePackedFixed64:function(t,e){e.length&&this.writeMessage(t,Zi,e)},writePackedSFixed64:function(t,e){e.length&&this.writeMessage(t,$i,e)},writeBytesField:function(t,e){this.writeTag(t,k.Bytes),this.writeBytes(e)},writeFixed32Field:function(t,e){this.writeTag(t,k.Fixed32),this.writeFixed32(e)},writeSFixed32Field:function(t,e){this.writeTag(t,k.Fixed32),this.writeSFixed32(e)},writeFixed64Field:function(t,e){this.writeTag(t,k.Fixed64),this.writeFixed64(e)},writeSFixed64Field:function(t,e){this.writeTag(t,k.Fixed64),this.writeSFixed64(e)},writeVarintField:function(t,e){this.writeTag(t,k.Varint),this.writeVarint(e)},writeSVarintField:function(t,e){this.writeTag(t,k.Varint),this.writeSVarint(e)},writeStringField:function(t,e){this.writeTag(t,k.Bytes),this.writeString(e)},writeFloatField:function(t,e){this.writeTag(t,k.Fixed32),this.writeFloat(e)},writeDoubleField:function(t,e){this.writeTag(t,k.Fixed64),this.writeDouble(e)},writeBooleanField:function(t,e){this.writeVarintField(t,Boolean(e))}};function Ei(t,e,r){var i=r.buf,n,a;if(a=i[r.pos++],n=(a&112)>>4,a<128||(a=i[r.pos++],n|=(a&127)<<3,a<128)||(a=i[r.pos++],n|=(a&127)<<10,a<128)||(a=i[r.pos++],n|=(a&127)<<17,a<128)||(a=i[r.pos++],n|=(a&127)<<24,a<128)||(a=i[r.pos++],n|=(a&1)<<31,a<128))return xe(t,n,e);throw new Error("Expected varint not more than 10 bytes")}function ne(t){return t.type===k.Bytes?t.readVarint()+t.pos:t.pos+1}function xe(t,e,r){return r?e*4294967296+(t>>>0):(e>>>0)*4294967296+(t>>>0)}function Oi(t,e){var r,i;if(t>=0?(r=t%4294967296|0,i=t/4294967296|0):(r=~(-t%4294967296),i=~(-t/4294967296),r^4294967295?r=r+1|0:(r=0,i=i+1|0)),t>=18446744073709552e3||t<-18446744073709552e3)throw new Error("Given varint doesn't fit into 10 bytes");e.realloc(10),Ri(r,i,e),Ii(i,e)}function Ri(t,e,r){r.buf[r.pos++]=t&127|128,t>>>=7,r.buf[r.pos++]=t&127|128,t>>>=7,r.buf[r.pos++]=t&127|128,t>>>=7,r.buf[r.pos++]=t&127|128,t>>>=7,r.buf[r.pos]=t&127}function Ii(t,e){var r=(t&7)<<4;e.buf[e.pos++]|=r|((t>>>=3)?128:0),!!t&&(e.buf[e.pos++]=t&127|((t>>>=7)?128:0),!!t&&(e.buf[e.pos++]=t&127|((t>>>=7)?128:0),!!t&&(e.buf[e.pos++]=t&127|((t>>>=7)?128:0),!!t&&(e.buf[e.pos++]=t&127|((t>>>=7)?128:0),!!t&&(e.buf[e.pos++]=t&127)))))}function ur(t,e,r){var i=e<=16383?1:e<=2097151?2:e<=268435455?3:Math.floor(Math.log(e)/(Math.LN2*7));r.realloc(i);for(var n=r.pos-1;n>=t;n--)r.buf[n+i]=r.buf[n]}function Xi(t,e){for(var r=0;r<t.length;r++)e.writeVarint(t[r])}function Vi(t,e){for(var r=0;r<t.length;r++)e.writeSVarint(t[r])}function Yi(t,e){for(var r=0;r<t.length;r++)e.writeFloat(t[r])}function Ui(t,e){for(var r=0;r<t.length;r++)e.writeDouble(t[r])}function ji(t,e){for(var r=0;r<t.length;r++)e.writeBoolean(t[r])}function Ni(t,e){for(var r=0;r<t.length;r++)e.writeFixed32(t[r])}function qi(t,e){for(var r=0;r<t.length;r++)e.writeSFixed32(t[r])}function Zi(t,e){for(var r=0;r<t.length;r++)e.writeFixed64(t[r])}function $i(t,e){for(var r=0;r<t.length;r++)e.writeSFixed64(t[r])}function Qe(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16)+t[e+3]*16777216}function ge(t,e,r){t[r]=e,t[r+1]=e>>>8,t[r+2]=e>>>16,t[r+3]=e>>>24}function hr(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16)+(t[e+3]<<24)}function Wi(t,e,r){for(var i="",n=e;n<r;){var a=t[n],s=null,o=a>239?4:a>223?3:a>191?2:1;if(n+o>r)break;var l,u,f;o===1?a<128&&(s=a):o===2?(l=t[n+1],(l&192)==128&&(s=(a&31)<<6|l&63,s<=127&&(s=null))):o===3?(l=t[n+1],u=t[n+2],(l&192)==128&&(u&192)==128&&(s=(a&15)<<12|(l&63)<<6|u&63,(s<=2047||s>=55296&&s<=57343)&&(s=null))):o===4&&(l=t[n+1],u=t[n+2],f=t[n+3],(l&192)==128&&(u&192)==128&&(f&192)==128&&(s=(a&15)<<18|(l&63)<<12|(u&63)<<6|f&63,(s<=65535||s>=1114112)&&(s=null))),s===null?(s=65533,o=1):s>65535&&(s-=65536,i+=String.fromCharCode(s>>>10&1023|55296),s=56320|s&1023),i+=String.fromCharCode(s),n+=o}return i}function Hi(t,e,r){return lr.decode(t.subarray(e,r))}function Ki(t,e,r){for(var i=0,n,a;i<e.length;i++){if(n=e.charCodeAt(i),n>55295&&n<57344)if(a)if(n<56320){t[r++]=239,t[r++]=191,t[r++]=189,a=n;continue}else n=a-55296<<10|n-56320|65536,a=null;else{n>56319||i+1===e.length?(t[r++]=239,t[r++]=191,t[r++]=189):a=n;continue}else a&&(t[r++]=239,t[r++]=191,t[r++]=189,a=null);n<128?t[r++]=n:(n<2048?t[r++]=n>>6|192:(n<65536?t[r++]=n>>12|224:(t[r++]=n>>18|240,t[r++]=n>>12&63|128),t[r++]=n>>6&63|128),t[r++]=n&63|128)}return r}});var Vr=W((Ft,kt)=>{(function(t,e){typeof Ft=="object"&&typeof kt!="undefined"?kt.exports=e():typeof define=="function"&&define.amd?define(e):(t=t||self).RBush=e()})(Ft,function(){"use strict";function t(h,m,p,b,x){(function y(v,_,S,F,P){for(;F>S;){if(F-S>600){var C=F-S+1,B=_-S+1,q=Math.log(C),Z=.5*Math.exp(2*q/3),re=.5*Math.sqrt(q*Z*(C-Z)/C)*(B-C/2<0?-1:1),X=Math.max(S,Math.floor(_-B*Z/C+re)),ft=Math.min(F,Math.floor(_+(C-B)*Z/C+re));y(v,_,X,ft,P)}var de=v[_],ie=S,E=F;for(e(v,S,_),P(v[F],de)>0&&e(v,S,F);ie<E;){for(e(v,ie,E),ie++,E--;P(v[ie],de)<0;)ie++;for(;P(v[E],de)>0;)E--}P(v[S],de)===0?e(v,S,E):e(v,++E,F),E<=_&&(S=E+1),_<=E&&(F=E-1)}})(h,m,p||0,b||h.length-1,x||r)}function e(h,m,p){var b=h[m];h[m]=h[p],h[p]=b}function r(h,m){return h<m?-1:h>m?1:0}var i=function(h){h===void 0&&(h=9),this._maxEntries=Math.max(4,h),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),this.clear()};function n(h,m,p){if(!p)return m.indexOf(h);for(var b=0;b<m.length;b++)if(p(h,m[b]))return b;return-1}function a(h,m){s(h,0,h.children.length,m,h)}function s(h,m,p,b,x){x||(x=w(null)),x.minX=1/0,x.minY=1/0,x.maxX=-1/0,x.maxY=-1/0;for(var y=m;y<p;y++){var v=h.children[y];o(x,h.leaf?b(v):v)}return x}function o(h,m){return h.minX=Math.min(h.minX,m.minX),h.minY=Math.min(h.minY,m.minY),h.maxX=Math.max(h.maxX,m.maxX),h.maxY=Math.max(h.maxY,m.maxY),h}function l(h,m){return h.minX-m.minX}function u(h,m){return h.minY-m.minY}function f(h){return(h.maxX-h.minX)*(h.maxY-h.minY)}function c(h){return h.maxX-h.minX+(h.maxY-h.minY)}function d(h,m){return h.minX<=m.minX&&h.minY<=m.minY&&m.maxX<=h.maxX&&m.maxY<=h.maxY}function g(h,m){return m.minX<=h.maxX&&m.minY<=h.maxY&&m.maxX>=h.minX&&m.maxY>=h.minY}function w(h){return{children:h,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function z(h,m,p,b,x){for(var y=[m,p];y.length;)if(!((p=y.pop())-(m=y.pop())<=b)){var v=m+Math.ceil((p-m)/b/2)*b;t(h,v,m,p,x),y.push(m,v,v,p)}}return i.prototype.all=function(){return this._all(this.data,[])},i.prototype.search=function(h){var m=this.data,p=[];if(!g(h,m))return p;for(var b=this.toBBox,x=[];m;){for(var y=0;y<m.children.length;y++){var v=m.children[y],_=m.leaf?b(v):v;g(h,_)&&(m.leaf?p.push(v):d(h,_)?this._all(v,p):x.push(v))}m=x.pop()}return p},i.prototype.collides=function(h){var m=this.data;if(!g(h,m))return!1;for(var p=[];m;){for(var b=0;b<m.children.length;b++){var x=m.children[b],y=m.leaf?this.toBBox(x):x;if(g(h,y)){if(m.leaf||d(h,y))return!0;p.push(x)}}m=p.pop()}return!1},i.prototype.load=function(h){if(!h||!h.length)return this;if(h.length<this._minEntries){for(var m=0;m<h.length;m++)this.insert(h[m]);return this}var p=this._build(h.slice(),0,h.length-1,0);if(this.data.children.length)if(this.data.height===p.height)this._splitRoot(this.data,p);else{if(this.data.height<p.height){var b=this.data;this.data=p,p=b}this._insert(p,this.data.height-p.height-1,!0)}else this.data=p;return this},i.prototype.insert=function(h){return h&&this._insert(h,this.data.height-1),this},i.prototype.clear=function(){return this.data=w([]),this},i.prototype.remove=function(h,m){if(!h)return this;for(var p,b,x,y=this.data,v=this.toBBox(h),_=[],S=[];y||_.length;){if(y||(y=_.pop(),b=_[_.length-1],p=S.pop(),x=!0),y.leaf){var F=n(h,y.children,m);if(F!==-1)return y.children.splice(F,1),_.push(y),this._condense(_),this}x||y.leaf||!d(y,v)?b?(p++,y=b.children[p],x=!1):y=null:(_.push(y),S.push(p),p=0,b=y,y=y.children[0])}return this},i.prototype.toBBox=function(h){return h},i.prototype.compareMinX=function(h,m){return h.minX-m.minX},i.prototype.compareMinY=function(h,m){return h.minY-m.minY},i.prototype.toJSON=function(){return this.data},i.prototype.fromJSON=function(h){return this.data=h,this},i.prototype._all=function(h,m){for(var p=[];h;)h.leaf?m.push.apply(m,h.children):p.push.apply(p,h.children),h=p.pop();return m},i.prototype._build=function(h,m,p,b){var x,y=p-m+1,v=this._maxEntries;if(y<=v)return a(x=w(h.slice(m,p+1)),this.toBBox),x;b||(b=Math.ceil(Math.log(y)/Math.log(v)),v=Math.ceil(y/Math.pow(v,b-1))),(x=w([])).leaf=!1,x.height=b;var _=Math.ceil(y/v),S=_*Math.ceil(Math.sqrt(v));z(h,m,p,S,this.compareMinX);for(var F=m;F<=p;F+=S){var P=Math.min(F+S-1,p);z(h,F,P,_,this.compareMinY);for(var C=F;C<=P;C+=_){var B=Math.min(C+_-1,P);x.children.push(this._build(h,C,B,b-1))}}return a(x,this.toBBox),x},i.prototype._chooseSubtree=function(h,m,p,b){for(;b.push(m),!m.leaf&&b.length-1!==p;){for(var x=1/0,y=1/0,v=void 0,_=0;_<m.children.length;_++){var S=m.children[_],F=f(S),P=(C=h,B=S,(Math.max(B.maxX,C.maxX)-Math.min(B.minX,C.minX))*(Math.max(B.maxY,C.maxY)-Math.min(B.minY,C.minY))-F);P<y?(y=P,x=F<x?F:x,v=S):P===y&&F<x&&(x=F,v=S)}m=v||m.children[0]}var C,B;return m},i.prototype._insert=function(h,m,p){var b=p?h:this.toBBox(h),x=[],y=this._chooseSubtree(b,this.data,m,x);for(y.children.push(h),o(y,b);m>=0&&x[m].children.length>this._maxEntries;)this._split(x,m),m--;this._adjustParentBBoxes(b,x,m)},i.prototype._split=function(h,m){var p=h[m],b=p.children.length,x=this._minEntries;this._chooseSplitAxis(p,x,b);var y=this._chooseSplitIndex(p,x,b),v=w(p.children.splice(y,p.children.length-y));v.height=p.height,v.leaf=p.leaf,a(p,this.toBBox),a(v,this.toBBox),m?h[m-1].children.push(v):this._splitRoot(p,v)},i.prototype._splitRoot=function(h,m){this.data=w([h,m]),this.data.height=h.height+1,this.data.leaf=!1,a(this.data,this.toBBox)},i.prototype._chooseSplitIndex=function(h,m,p){for(var b,x,y,v,_,S,F,P=1/0,C=1/0,B=m;B<=p-m;B++){var q=s(h,0,B,this.toBBox),Z=s(h,B,p,this.toBBox),re=(x=q,y=Z,v=void 0,_=void 0,S=void 0,F=void 0,v=Math.max(x.minX,y.minX),_=Math.max(x.minY,y.minY),S=Math.min(x.maxX,y.maxX),F=Math.min(x.maxY,y.maxY),Math.max(0,S-v)*Math.max(0,F-_)),X=f(q)+f(Z);re<P?(P=re,b=B,C=X<C?X:C):re===P&&X<C&&(C=X,b=B)}return b||p-m},i.prototype._chooseSplitAxis=function(h,m,p){var b=h.leaf?this.compareMinX:l,x=h.leaf?this.compareMinY:u;this._allDistMargin(h,m,p,b)<this._allDistMargin(h,m,p,x)&&h.children.sort(b)},i.prototype._allDistMargin=function(h,m,p,b){h.children.sort(b);for(var x=this.toBBox,y=s(h,0,m,x),v=s(h,p-m,p,x),_=c(y)+c(v),S=m;S<p-m;S++){var F=h.children[S];o(y,h.leaf?x(F):F),_+=c(y)}for(var P=p-m-1;P>=m;P--){var C=h.children[P];o(v,h.leaf?x(C):C),_+=c(v)}return _},i.prototype._adjustParentBBoxes=function(h,m,p){for(var b=p;b>=0;b--)o(m[b],h)},i.prototype._condense=function(h){for(var m=h.length-1,p=void 0;m>=0;m--)h[m].children.length===0?m>0?(p=h[m-1].children).splice(p.indexOf(h[m]),1):this.clear():a(h[m],this.toBBox)},i})});var $r=W(()=>{});var Wr=W((Tt,Pt)=>{(function(t,e){typeof Tt=="object"&&typeof Pt!="undefined"?Pt.exports=e():typeof define=="function"&&define.amd?define(e):(t=t||self,t.TinyQueue=e())})(Tt,function(){"use strict";var t=function(i,n){if(i===void 0&&(i=[]),n===void 0&&(n=e),this.data=i,this.length=this.data.length,this.compare=n,this.length>0)for(var a=(this.length>>1)-1;a>=0;a--)this._down(a)};t.prototype.push=function(i){this.data.push(i),this.length++,this._up(this.length-1)},t.prototype.pop=function(){if(this.length!==0){var i=this.data[0],n=this.data.pop();return this.length--,this.length>0&&(this.data[0]=n,this._down(0)),i}},t.prototype.peek=function(){return this.data[0]},t.prototype._up=function(i){for(var n=this,a=n.data,s=n.compare,o=a[i];i>0;){var l=i-1>>1,u=a[l];if(s(o,u)>=0)break;a[i]=u,i=l}a[i]=o},t.prototype._down=function(i){for(var n=this,a=n.data,s=n.compare,o=this.length>>1,l=a[i];i<o;){var u=(i<<1)+1,f=a[u],c=u+1;if(c<this.length&&s(a[c],f)<0&&(u=c,f=a[c]),s(f,l)>=0)break;a[i]=f,i=u}a[i]=l};function e(r,i){return r<i?-1:r>i?1:0}return t})});var Kr=W((Ya,Dt)=>{"use strict";var st=Wr();st.default&&(st=st.default);Dt.exports=Hr;Dt.exports.default=Hr;function Hr(t,e,r){e=e||1;for(var i,n,a,s,o=0;o<t[0].length;o++){var l=t[0][o];(!o||l[0]<i)&&(i=l[0]),(!o||l[1]<n)&&(n=l[1]),(!o||l[0]>a)&&(a=l[0]),(!o||l[1]>s)&&(s=l[1])}var u=a-i,f=s-n,c=Math.min(u,f),d=c/2;if(c===0){var g=[i,n];return g.distance=0,g}for(var w=new st(void 0,Nn),z=i;z<a;z+=c)for(var h=n;h<s;h+=c)w.push(new ue(z+d,h+d,d,t));var m=Zn(t),p=new ue(i+u/2,n+f/2,0,t);p.d>m.d&&(m=p);for(var b=w.length;w.length;){var x=w.pop();x.d>m.d&&(m=x,r&&console.log("found best %d after %d probes",Math.round(1e4*x.d)/1e4,b)),!(x.max-m.d<=e)&&(d=x.h/2,w.push(new ue(x.x-d,x.y-d,d,t)),w.push(new ue(x.x+d,x.y-d,d,t)),w.push(new ue(x.x-d,x.y+d,d,t)),w.push(new ue(x.x+d,x.y+d,d,t)),b+=4)}r&&(console.log("num probes: "+b),console.log("best distance: "+m.d));var y=[m.x,m.y];return y.distance=m.d,y}function Nn(t,e){return e.max-t.max}function ue(t,e,r,i){this.x=t,this.y=e,this.h=r,this.d=qn(t,e,i),this.max=this.d+this.h*Math.SQRT2}function qn(t,e,r){for(var i=!1,n=1/0,a=0;a<r.length;a++)for(var s=r[a],o=0,l=s.length,u=l-1;o<l;u=o++){var f=s[o],c=s[u];f[1]>e!=c[1]>e&&t<(c[0]-f[0])*(e-f[1])/(c[1]-f[1])+f[0]&&(i=!i),n=Math.min(n,$n(t,e,f,c))}return n===0?0:(i?1:-1)*Math.sqrt(n)}function Zn(t){for(var e=0,r=0,i=0,n=t[0],a=0,s=n.length,o=s-1;a<s;o=a++){var l=n[a],u=n[o],f=l[0]*u[1]-u[0]*l[1];r+=(l[0]+u[0])*f,i+=(l[1]+u[1])*f,e+=f*3}return e===0?new ue(n[0][0],n[0][1],0,t):new ue(r/e,i/e,0,t)}function $n(t,e,r,i){var n=r[0],a=r[1],s=i[0]-n,o=i[1]-a;if(s!==0||o!==0){var l=((t-n)*s+(e-a)*o)/(s*s+o*o);l>1?(n=i[0],a=i[1]):l>0&&(n+=s*l,a+=o*l)}return s=t-n,o=e-a,s*s+o*o}});var ha={};vi(ha,{CenteredSymbolizer:()=>Ot,CenteredTextSymbolizer:()=>te,CircleSymbolizer:()=>oe,FlexSymbolizer:()=>Ze,Font:()=>oa,GeomType:()=>G,GroupSymbolizer:()=>$e,IconSymbolizer:()=>ai,Index:()=>Mt,Justify:()=>Q,Labeler:()=>Ve,Labelers:()=>Ye,LineLabelPlacement:()=>Se,LineLabelSymbolizer:()=>Le,LineSymbolizer:()=>R,OffsetSymbolizer:()=>Rt,OffsetTextSymbolizer:()=>ce,PMTiles:()=>tt,Padding:()=>oi,PmtilesSource:()=>Ee,PolygonLabelSymbolizer:()=>Fe,PolygonSymbolizer:()=>A,Sheet:()=>pi,ShieldSymbolizer:()=>ot,Static:()=>ci,TextPlacements:()=>T,TextSymbolizer:()=>We,TileCache:()=>at,View:()=>Lt,ZxySource:()=>rt,arr:()=>Jn,covering:()=>jr,createPattern:()=>Kn,cubicBezier:()=>ea,dark:()=>_e,exp:()=>Y,filterFn:()=>Ke,getFont:()=>Ut,getZoom:()=>It,isCCW:()=>Er,isInRing:()=>St,json_style:()=>ua,labelRules:()=>Me,leafletLayer:()=>sa,light:()=>ve,linear:()=>Qn,numberFn:()=>ht,numberOrFn:()=>Vt,paintRules:()=>ke,painter:()=>Xe,pointInPolygon:()=>Or,pointMinDistToLines:()=>Ir,pointMinDistToPoints:()=>Rr,sourcesToViews:()=>Ie,step:()=>Gn,toIndex:()=>se,transformGeom:()=>Oe,widthFn:()=>Yt,wrap:()=>Re});var le=U(ee());var fe=U(ee());var we=U(ee()),Tr=U(ar()),Pr=U(cr());var V=(t,e,r)=>new Promise((i,n)=>{var a=l=>{try{o(r.next(l))}catch(u){n(u)}},s=l=>{try{o(r.throw(l))}catch(u){n(u)}},o=l=>l.done?i(l.value):Promise.resolve(l.value).then(a,s);o((r=r.apply(t,e)).next())}),j=Uint8Array,he=Uint16Array,dr=Uint32Array,mr=new j([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),pr=new j([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),Ji=new j([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),xr=function(t,e){for(var r=new he(31),i=0;i<31;++i)r[i]=e+=1<<t[i-1];for(var n=new dr(r[30]),i=1;i<30;++i)for(var a=r[i];a<r[i+1];++a)n[a]=a-r[i]<<5|i;return[r,n]},gr=xr(mr,2),yr=gr[0],Gi=gr[1];yr[28]=258,Gi[258]=28;var br=xr(pr,0),Qi=br[0],ba=br[1],yt=new he(32768);for(M=0;M<32768;++M)ae=(M&43690)>>>1|(M&21845)<<1,ae=(ae&52428)>>>2|(ae&13107)<<2,ae=(ae&61680)>>>4|(ae&3855)<<4,yt[M]=((ae&65280)>>>8|(ae&255)<<8)>>>1;var ae,M,Pe=function(t,e,r){for(var i=t.length,n=0,a=new he(e);n<i;++n)t[n]&&++a[t[n]-1];var s=new he(e);for(n=0;n<e;++n)s[n]=s[n-1]+a[n-1]<<1;var o;if(r){o=new he(1<<e);var l=15-e;for(n=0;n<i;++n)if(t[n])for(var u=n<<4|t[n],f=e-t[n],c=s[t[n]-1]++<<f,d=c|(1<<f)-1;c<=d;++c)o[yt[c]>>>l]=u}else for(o=new he(i),n=0;n<i;++n)t[n]&&(o[n]=yt[s[t[n]-1]++]>>>15-t[n]);return o},De=new j(288);for(M=0;M<144;++M)De[M]=8;var M;for(M=144;M<256;++M)De[M]=9;var M;for(M=256;M<280;++M)De[M]=7;var M;for(M=280;M<288;++M)De[M]=8;var M,wr=new j(32);for(M=0;M<32;++M)wr[M]=5;var M,en=Pe(De,9,1),tn=Pe(wr,5,1),bt=function(t){for(var e=t[0],r=1;r<t.length;++r)t[r]>e&&(e=t[r]);return e},H=function(t,e,r){var i=e/8|0;return(t[i]|t[i+1]<<8)>>(e&7)&r},wt=function(t,e){var r=e/8|0;return(t[r]|t[r+1]<<8|t[r+2]<<16)>>(e&7)},rn=function(t){return(t+7)/8|0},nn=function(t,e,r){(e==null||e<0)&&(e=0),(r==null||r>t.length)&&(r=t.length);var i=new(t.BYTES_PER_ELEMENT==2?he:t.BYTES_PER_ELEMENT==4?dr:j)(r-e);return i.set(t.subarray(e,r)),i},an=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],K=function(t,e,r){var i=new Error(e||an[t]);if(i.code=t,Error.captureStackTrace&&Error.captureStackTrace(i,K),!r)throw i;return i},vt=function(t,e,r){var i=t.length;if(!i||r&&r.f&&!r.l)return e||new j(0);var n=!e||r,a=!r||r.i;r||(r={}),e||(e=new j(i*3));var s=function(qt){var Zt=e.length;if(qt>Zt){var $t=new j(Math.max(Zt*2,qt));$t.set(e),e=$t}},o=r.f||0,l=r.p||0,u=r.b||0,f=r.l,c=r.d,d=r.m,g=r.n,w=i*8;do{if(!f){o=H(t,l,1);var z=H(t,l+1,3);if(l+=3,z)if(z==1)f=en,c=tn,d=9,g=5;else if(z==2){var b=H(t,l,31)+257,x=H(t,l+10,15)+4,y=b+H(t,l+5,31)+1;l+=14;for(var v=new j(y),_=new j(19),S=0;S<x;++S)_[Ji[S]]=H(t,l+S*3,7);l+=x*3;for(var F=bt(_),P=(1<<F)-1,C=Pe(_,F,1),S=0;S<y;){var B=C[H(t,l,P)];l+=B&15;var h=B>>>4;if(h<16)v[S++]=h;else{var q=0,Z=0;for(h==16?(Z=3+H(t,l,3),l+=2,q=v[S-1]):h==17?(Z=3+H(t,l,7),l+=3):h==18&&(Z=11+H(t,l,127),l+=7);Z--;)v[S++]=q}}var re=v.subarray(0,b),X=v.subarray(b);d=bt(re),g=bt(X),f=Pe(re,d,1),c=Pe(X,g,1)}else K(1);else{var h=rn(l)+4,m=t[h-4]|t[h-3]<<8,p=h+m;if(p>i){a&&K(0);break}n&&s(u+m),e.set(t.subarray(h,p),u),r.b=u+=m,r.p=l=p*8,r.f=o;continue}if(l>w){a&&K(0);break}}n&&s(u+131072);for(var ft=(1<<d)-1,de=(1<<g)-1,ie=l;;ie=l){var q=f[wt(t,l)&ft],E=q>>>4;if(l+=q&15,l>w){a&&K(0);break}if(q||K(2),E<256)e[u++]=E;else if(E==256){ie=l,f=null;break}else{var jt=E-254;if(E>264){var S=E-257,Ce=mr[S];jt=H(t,l,(1<<Ce)-1)+yr[S],l+=Ce}var ct=c[wt(t,l)&de],dt=ct>>>4;ct||K(3),l+=ct&15;var X=Qi[dt];if(dt>3){var Ce=pr[dt];X+=wt(t,l)&(1<<Ce)-1,l+=Ce}if(l>w){a&&K(0);break}n&&s(u+131072);for(var Nt=u+jt;u<Nt;u+=4)e[u]=e[u-X],e[u+1]=e[u+1-X],e[u+2]=e[u+2-X],e[u+3]=e[u+3-X];u=Nt}}r.l=f,r.p=ie,r.b=u,r.f=o,f&&(o=1,r.m=d,r.d=c,r.n=g)}while(!o);return u==e.length?e:nn(e,0,u)},sn=new j(0),on=function(t){(t[0]!=31||t[1]!=139||t[2]!=8)&&K(6,"invalid gzip data");var e=t[3],r=10;e&4&&(r+=t[10]|(t[11]<<8)+2);for(var i=(e>>3&1)+(e>>4&1);i>0;i-=!t[r++]);return r+(e&2)},ln=function(t){var e=t.length;return(t[e-4]|t[e-3]<<8|t[e-2]<<16|t[e-1]<<24)>>>0},un=function(t){((t[0]&15)!=8||t[0]>>>4>7||(t[0]<<8|t[1])%31)&&K(6,"invalid zlib data"),t[1]&32&&K(6,"invalid zlib data: preset dictionaries not supported")};function hn(t,e){return vt(t,e)}function fn(t,e){return vt(t.subarray(on(t),-8),e||new j(ln(t)))}function cn(t,e){return vt((un(t),t.subarray(2,-4)),e)}function _t(t,e){return t[0]==31&&t[1]==139&&t[2]==8?fn(t,e):(t[0]&15)!=8||t[0]>>4>7||(t[0]<<8|t[1])%31?hn(t,e):cn(t,e)}var dn=typeof TextDecoder!="undefined"&&new TextDecoder,mn=0;try{dn.decode(sn,{stream:!0}),mn=1}catch(t){}var vr=(t,e)=>t*Math.pow(2,e),Ae=(t,e)=>Math.floor(t/Math.pow(2,e)),et=(t,e)=>vr(t.getUint16(e+1,!0),8)+t.getUint8(e),_r=(t,e)=>vr(t.getUint32(e+2,!0),16)+t.getUint16(e,!0),pn=(t,e,r,i,n)=>{if(t!=i.getUint8(n))return t-i.getUint8(n);let a=et(i,n+1);if(e!=a)return e-a;let s=et(i,n+4);return r!=s?r-s:0},xn=(t,e,r,i)=>{let n=Sr(t,e|128,r,i);return n?{z:e,x:r,y:i,offset:n[0],length:n[1],is_dir:!0}:null},zr=(t,e,r,i)=>{let n=Sr(t,e,r,i);return n?{z:e,x:r,y:i,offset:n[0],length:n[1],is_dir:!1}:null},Sr=(t,e,r,i)=>{let n=0,a=t.byteLength/17-1;for(;n<=a;){let s=a+n>>1,o=pn(e,r,i,t,s*17);if(o>0)n=s+1;else if(o<0)a=s-1;else return[_r(t,s*17+7),t.getUint32(s*17+13,!0)]}return null},gn=(t,e)=>t.is_dir&&!e.is_dir?1:!t.is_dir&&e.is_dir?-1:t.z!==e.z?t.z-e.z:t.x!==e.x?t.x-e.x:t.y-e.y,Lr=(t,e)=>{let r=t.getUint8(e*17);return{z:r&127,x:et(t,e*17+1),y:et(t,e*17+4),offset:_r(t,e*17+7),length:t.getUint32(e*17+13,!0),is_dir:r>>7==1}},Fr=t=>{let e=[],r=new DataView(t);for(let i=0;i<r.byteLength/17;i++)e.push(Lr(r,i));return yn(e)},yn=t=>{t.sort(gn);let e=new ArrayBuffer(17*t.length),r=new Uint8Array(e);for(let i=0;i<t.length;i++){let n=t[i],a=n.z;n.is_dir&&(a=a|128),r[i*17]=a,r[i*17+1]=n.x&255,r[i*17+2]=n.x>>8&255,r[i*17+3]=n.x>>16&255,r[i*17+4]=n.y&255,r[i*17+5]=n.y>>8&255,r[i*17+6]=n.y>>16&255,r[i*17+7]=n.offset&255,r[i*17+8]=Ae(n.offset,8)&255,r[i*17+9]=Ae(n.offset,16)&255,r[i*17+10]=Ae(n.offset,24)&255,r[i*17+11]=Ae(n.offset,32)&255,r[i*17+12]=Ae(n.offset,48)&255,r[i*17+13]=n.length&255,r[i*17+14]=n.length>>8&255,r[i*17+15]=n.length>>16&255,r[i*17+16]=n.length>>24&255}return e},bn=(t,e)=>{if(t.byteLength<17)return null;let r=t.byteLength/17,i=Lr(t,r-1);if(i.is_dir){let n=i.z,a=e.z-n,s=Math.trunc(e.x/(1<<a)),o=Math.trunc(e.y/(1<<a));return{z:n,x:s,y:o}}return null};function wn(t){return V(this,null,function*(){let e=yield t.getBytes(0,512e3),r=new DataView(e.data),i=r.getUint32(4,!0),n=r.getUint16(8,!0),a=new TextDecoder("utf-8"),s=JSON.parse(a.decode(new DataView(e.data,10,i))),o=0;s.compression==="gzip"&&(o=2);let l=0;"minzoom"in s&&(l=+s.minzoom);let u=0;"maxzoom"in s&&(u=+s.maxzoom);let f=0,c=0,d=0,g=-180,w=-85,z=180,h=85;if(s.bounds){let p=s.bounds.split(",");g=+p[0],w=+p[1],z=+p[2],h=+p[3]}if(s.center){let p=s.center.split(",");f=+p[0],c=+p[1],d=+p[2]}return{specVersion:r.getUint16(2,!0),rootDirectoryOffset:10+i,rootDirectoryLength:n*17,jsonMetadataOffset:10,jsonMetadataLength:i,leafDirectoryOffset:0,leafDirectoryLength:void 0,tileDataOffset:0,tileDataLength:void 0,numAddressedTiles:0,numTileEntries:0,numTileContents:0,clustered:!1,internalCompression:1,tileCompression:o,tileType:1,minZoom:l,maxZoom:u,minLon:g,minLat:w,maxLon:z,maxLat:h,centerZoom:d,centerLon:f,centerLat:c,etag:e.etag}})}function vn(t,e,r,i,n,a,s){return V(this,null,function*(){let o=yield r.getArrayBuffer(e,t.rootDirectoryOffset,t.rootDirectoryLength,t);t.specVersion===1&&(o=Fr(o));let l=zr(new DataView(o),i,n,a);if(l){let c=(yield e.getBytes(l.offset,l.length,s)).data,d=new DataView(c);return d.getUint8(0)==31&&d.getUint8(1)==139&&(c=_t(new Uint8Array(c))),{data:c}}let u=bn(new DataView(o),{z:i,x:n,y:a});if(u){let f=xn(new DataView(o),u.z,u.x,u.y);if(f){let c=yield r.getArrayBuffer(e,f.offset,f.length,t);t.specVersion===1&&(c=Fr(c));let d=zr(new DataView(c),i,n,a);if(d){let w=(yield e.getBytes(d.offset,d.length,s)).data,z=new DataView(w);return z.getUint8(0)==31&&z.getUint8(1)==139&&(w=_t(new Uint8Array(w))),{data:w}}}}})}var kr={getHeader:wn,getZxy:vn};function ye(t,e){return(e>>>0)*4294967296+(t>>>0)}function _n(t,e){let r=e.buf,i,n;if(n=r[e.pos++],i=(n&112)>>4,n<128||(n=r[e.pos++],i|=(n&127)<<3,n<128)||(n=r[e.pos++],i|=(n&127)<<10,n<128)||(n=r[e.pos++],i|=(n&127)<<17,n<128)||(n=r[e.pos++],i|=(n&127)<<24,n<128)||(n=r[e.pos++],i|=(n&1)<<31,n<128))return ye(t,i);throw new Error("Expected varint not more than 10 bytes")}function Be(t){let e=t.buf,r,i;return i=e[t.pos++],r=i&127,i<128||(i=e[t.pos++],r|=(i&127)<<7,i<128)||(i=e[t.pos++],r|=(i&127)<<14,i<128)||(i=e[t.pos++],r|=(i&127)<<21,i<128)?r:(i=e[t.pos],r|=(i&15)<<28,_n(r,t))}function zn(t,e,r,i){if(i==0){r==1&&(e[0]=t-1-e[0],e[1]=t-1-e[1]);let n=e[0];e[0]=e[1],e[1]=n}}function Sn(t,e,r){if(t>26)throw Error("Tile zoom level exceeds max safe number limit (26)");if(e>Math.pow(2,t)-1||r>Math.pow(2,t)-1)throw Error("tile x/y outside zoom level bounds");let i=0,n=0;for(;n<t;)i+=Math.pow(2,n)*Math.pow(2,n),n++;let a=Math.pow(2,t),s=0,o=0,l=0,u=[e,r],f=a/2;for(;f>0;)s=(u[0]&f)>0?1:0,o=(u[1]&f)>0?1:0,l+=f*f*(3*s^o),zn(f,u,s,o),f=f/2;return i+l}function Mr(t,e){return V(this,null,function*(){if(e===1||e===0)return t;if(e===2)return _t(new Uint8Array(t));throw Error("Compression method not supported")})}var Ln=127;function Fn(t,e){let r=0,i=t.length-1;for(;r<=i;){let n=i+r>>1,a=e-t[n].tileId;if(a>0)r=n+1;else if(a<0)i=n-1;else return t[n]}return i>=0&&(t[i].runLength===0||e-t[i].tileId<t[i].runLength)?t[i]:null}var kn=class{constructor(t){this.url=t}getKey(){return this.url}getBytes(t,e,r){return V(this,null,function*(){let i;r||(i=new AbortController,r=i.signal);let n=yield fetch(this.url,{signal:r,headers:{Range:"bytes="+t+"-"+(t+e-1)}});if(n.status===416&&t===0){let o=n.headers.get("Content-Range");if(!o||!o.startsWith("bytes */"))throw Error("Missing content-length on 416 response");let l=+o.substr(8);n=yield fetch(this.url,{signal:r,headers:{Range:"bytes=0-"+(l-1)}})}if(n.status>=300)throw Error("Bad response code: "+n.status);let a=n.headers.get("Content-Length");if(n.status===200&&(!a||+a>e))throw i&&i.abort(),Error("Server returned no content-length header or content-length exceeding request. Check that your storage backend supports HTTP Byte Serving.");return{data:yield n.arrayBuffer(),etag:n.headers.get("ETag")||void 0,cacheControl:n.headers.get("Cache-Control")||void 0,expires:n.headers.get("Expires")||void 0}})}};function J(t,e){let r=t.getUint32(e+4,!0),i=t.getUint32(e+0,!0);return r*Math.pow(2,32)+i}function Mn(t,e){let r=new DataView(t),i=r.getUint8(7);if(i>3)throw Error(`Archive is spec version ${i} but this library supports up to spec version 3`);return{specVersion:i,rootDirectoryOffset:J(r,8),rootDirectoryLength:J(r,16),jsonMetadataOffset:J(r,24),jsonMetadataLength:J(r,32),leafDirectoryOffset:J(r,40),leafDirectoryLength:J(r,48),tileDataOffset:J(r,56),tileDataLength:J(r,64),numAddressedTiles:J(r,72),numTileEntries:J(r,80),numTileContents:J(r,88),clustered:r.getUint8(96)===1,internalCompression:r.getUint8(97),tileCompression:r.getUint8(98),tileType:r.getUint8(99),minZoom:r.getUint8(100),maxZoom:r.getUint8(101),minLon:r.getInt32(102,!0)/1e7,minLat:r.getInt32(106,!0)/1e7,maxLon:r.getInt32(110,!0)/1e7,maxLat:r.getInt32(114,!0)/1e7,centerZoom:r.getUint8(118),centerLon:r.getInt32(119,!0)/1e7,centerLat:r.getInt32(123,!0)/1e7,etag:e}}function Cr(t){let e={buf:new Uint8Array(t),pos:0},r=Be(e),i=[],n=0;for(let a=0;a<r;a++){let s=Be(e);i.push({tileId:n+s,offset:0,length:0,runLength:1}),n+=s}for(let a=0;a<r;a++)i[a].runLength=Be(e);for(let a=0;a<r;a++)i[a].length=Be(e);for(let a=0;a<r;a++){let s=Be(e);s===0&&a>0?i[a].offset=i[a-1].offset+i[a-1].length:i[a].offset=s-1}return i}function Cn(t){let e=new DataView(t);return e.getUint16(2,!0)===2?(console.warn("PMTiles spec version 2 has been deprecated; please see github.com/protomaps/PMTiles for tools to upgrade"),2):e.getUint16(2,!0)===1?(console.warn("PMTiles spec version 1 has been deprecated; please see github.com/protomaps/PMTiles for tools to upgrade"),1):3}var be=class extends Error{};function Tn(t,e,r,i){return V(this,null,function*(){let n=yield t.getBytes(0,16384);if(new DataView(n.data).getUint16(0,!0)!==19792)throw new Error("Wrong magic number for PMTiles archive");if(Cn(n.data)<3)return[yield kr.getHeader(t)];let s=n.data.slice(0,Ln),o=n.etag;i&&n.etag!=i&&(console.warn("ETag conflict detected; your HTTP server might not support content-based ETag headers. ETags disabled for "+t.getKey()),o=void 0);let l=Mn(s,o);if(r){let u=n.data.slice(l.rootDirectoryOffset,l.rootDirectoryOffset+l.rootDirectoryLength),f=t.getKey()+"|"+(l.etag||"")+"|"+l.rootDirectoryOffset+"|"+l.rootDirectoryLength,c=Cr(yield e(u,l.internalCompression));return[l,[f,c.length,c]]}return[l,void 0]})}function Pn(t,e,r,i,n){return V(this,null,function*(){let a=yield t.getBytes(r,i);if(n.etag&&n.etag!==a.etag)throw new be(a.etag);let s=yield e(a.data,n.internalCompression),o=Cr(s);if(o.length===0)throw new Error("Empty directory is invalid");return o})}var Dn=class{constructor(t=100,e=!0,r=Mr){this.cache=new Map,this.maxCacheEntries=t,this.counter=1,this.prefetch=e,this.decompress=r}getHeader(t,e){return V(this,null,function*(){let r=t.getKey();if(this.cache.has(r))return this.cache.get(r).lastUsed=this.counter++,yield this.cache.get(r).data;let i=new Promise((n,a)=>{Tn(t,this.decompress,this.prefetch,e).then(s=>{s[1]&&this.cache.set(s[1][0],{lastUsed:this.counter++,data:Promise.resolve(s[1][2])}),n(s[0]),this.prune()}).catch(s=>{a(s)})});return this.cache.set(r,{lastUsed:this.counter++,data:i}),i})}getDirectory(t,e,r,i){return V(this,null,function*(){let n=t.getKey()+"|"+(i.etag||"")+"|"+e+"|"+r;if(this.cache.has(n))return this.cache.get(n).lastUsed=this.counter++,yield this.cache.get(n).data;let a=new Promise((s,o)=>{Pn(t,this.decompress,e,r,i).then(l=>{s(l),this.prune()}).catch(l=>{o(l)})});return this.cache.set(n,{lastUsed:this.counter++,data:a}),a})}getArrayBuffer(t,e,r,i){return V(this,null,function*(){let n=t.getKey()+"|"+(i.etag||"")+"|"+e+"|"+r;if(this.cache.has(n))return this.cache.get(n).lastUsed=this.counter++,yield this.cache.get(n).data;let a=new Promise((s,o)=>{t.getBytes(e,r).then(l=>{if(i.etag&&i.etag!==l.etag)throw new be(l.etag);s(l.data),this.cache.has(n),this.prune()}).catch(l=>{o(l)})});return this.cache.set(n,{lastUsed:this.counter++,data:a}),a})}prune(){if(this.cache.size>=this.maxCacheEntries){let t=1/0,e;this.cache.forEach((r,i)=>{r.lastUsed<t&&(t=r.lastUsed,e=i)}),e&&this.cache.delete(e)}}invalidate(t,e){return V(this,null,function*(){this.cache.delete(t.getKey()),yield this.getHeader(t,e)})}},tt=class{constructor(t,e,r){typeof t=="string"?this.source=new kn(t):this.source=t,r?this.decompress=r:this.decompress=Mr,e?this.cache=e:this.cache=new Dn}getHeader(){return V(this,null,function*(){return yield this.cache.getHeader(this.source)})}getZxyAttempt(t,e,r,i){return V(this,null,function*(){let n=Sn(t,e,r),a=yield this.cache.getHeader(this.source);if(a.specVersion<3)return kr.getZxy(a,this.source,this.cache,t,e,r,i);if(t<a.minZoom||t>a.maxZoom)return;let s=a.rootDirectoryOffset,o=a.rootDirectoryLength;for(let l=0;l<=3;l++){let u=yield this.cache.getDirectory(this.source,s,o,a),f=Fn(u,n);if(f)if(f.runLength>0){let c=yield this.source.getBytes(a.tileDataOffset+f.offset,f.length,i);if(a.etag&&a.etag!==c.etag)throw new be(c.etag);return{data:yield this.decompress(c.data,a.tileCompression),cacheControl:c.cacheControl,expires:c.expires}}else s=a.leafDirectoryOffset+f.offset,o=f.length;else return}throw Error("Maximum directory depth exceeded")})}getZxy(t,e,r,i){return V(this,null,function*(){try{return yield this.getZxyAttempt(t,e,r,i)}catch(n){if(n instanceof be)return this.cache.invalidate(this.source,n.message),yield this.getZxyAttempt(t,e,r,i);throw n}})}getMetadataAttempt(){return V(this,null,function*(){let t=yield this.cache.getHeader(this.source),e=yield this.source.getBytes(t.jsonMetadataOffset,t.jsonMetadataLength);if(t.etag&&t.etag!==e.etag)throw new be(e.etag);let r=yield this.decompress(e.data,t.internalCompression),i=new TextDecoder("utf-8");return JSON.parse(i.decode(r))})}getMetadata(){return V(this,null,function*(){try{return yield this.getMetadataAttempt()}catch(t){if(t instanceof be)return this.cache.invalidate(this.source,t.message),yield this.getMetadataAttempt();throw t}})}};var G;(function(i){i[i.Point=1]="Point",i[i.Line=2]="Line",i[i.Polygon=3]="Polygon"})(G||(G={}));function se(t){return t.x+":"+t.y+":"+t.z}var An=(t,e,r)=>{t.pos=e;for(var i=t.readVarint()+t.pos,n=1,a=0,s=0,o=0,l=1/0,u=-1/0,f=1/0,c=-1/0,d=[],g=[];t.pos<i;){if(a<=0){var w=t.readVarint();n=w&7,a=w>>3}if(a--,n===1||n===2)s+=t.readSVarint()*r,o+=t.readSVarint()*r,s<l&&(l=s),s>u&&(u=s),o<f&&(f=o),o>c&&(c=o),n===1&&(g.length>0&&d.push(g),g=[]),g.push(new we.default(s,o));else if(n===7)g&&g.push(g[0].clone());else throw new Error("unknown command "+n)}return g&&d.push(g),{geom:d,bbox:{minX:l,minY:f,maxX:u,maxY:c}}};function Dr(t,e){let r=new Tr.VectorTile(new Pr.default(t)),i=new Map;for(let[n,a]of Object.entries(r.layers)){let s=[],o=a;for(let l=0;l<o.length;l++){let u=An(o.feature(l)._pbf,o.feature(l)._geometry,e/o.extent),f=0;for(let c of u.geom)f+=c.length;s.push({id:o.feature(l).id,geomType:o.feature(l).type,geom:u.geom,numVertices:f,bbox:u.bbox,props:o.feature(l).properties})}i.set(n,s)}return i}var Ee=class{constructor(e,r){typeof e=="string"?this.p=new tt(e):this.p=e,this.controllers=[],this.shouldCancelZooms=r}get(e,r){return O(this,null,function*(){this.shouldCancelZooms&&(this.controllers=this.controllers.filter(s=>s[0]!=e.z?(s[1].abort(),!1):!0));let i=new AbortController;this.controllers.push([e.z,i]);let n=i.signal,a=yield this.p.getZxy(e.z,e.x,e.y,n);return a?Dr(a.data,r):new Map})}},rt=class{constructor(e,r){this.url=e,this.controllers=[],this.shouldCancelZooms=r}get(e,r){return O(this,null,function*(){this.shouldCancelZooms&&(this.controllers=this.controllers.filter(s=>s[0]!=e.z?(s[1].abort(),!1):!0));let i=this.url.replace("{z}",e.z.toString()).replace("{x}",e.x.toString()).replace("{y}",e.y.toString()),n=new AbortController;this.controllers.push([e.z,n]);let a=n.signal;return new Promise((s,o)=>{fetch(i,{signal:a}).then(l=>l.arrayBuffer()).then(l=>{let u=Dr(l,r);s(u)}).catch(l=>{o(l)})})})}},zt=6378137,Ar=85.0511287798,it=zt*Math.PI,Bn=t=>{let e=Math.PI/180,r=Math.max(Math.min(Ar,t[0]),-Ar),i=Math.sin(r*e);return new we.default(zt*t[1]*e,zt*Math.log((1+i)/(1-i))/2)};function Br(t){return t*t}function nt(t,e){return Br(t.x-e.x)+Br(t.y-e.y)}function En(t,e,r){var i=nt(e,r);if(i===0)return nt(t,e);var n=((t.x-e.x)*(r.x-e.x)+(t.y-e.y)*(r.y-e.y))/i;return n=Math.max(0,Math.min(1,n)),nt(t,new we.default(e.x+n*(r.x-e.x),e.y+n*(r.y-e.y)))}function St(t,e){for(var r=!1,i=0,n=e.length-1;i<e.length;n=i++){var a=e[i].x,s=e[i].y,o=e[n].x,l=e[n].y,u=s>t.y!=l>t.y&&t.x<(o-a)*(t.y-s)/(l-s)+a;u&&(r=!r)}return r}function Er(t){for(var e=0,r=0;r<t.length;r++){let i=(r+1)%t.length;e+=t[r].x*t[i].y,e-=t[i].x*t[r].y}return e<0}function Or(t,e){var r=!1;for(let i of e)if(Er(i))St(t,i)&&(r=!1);else{if(r)return!0;St(t,i)&&(r=!0)}return r}function Rr(t,e){let r=1/0;for(let i of e){let n=Math.sqrt(nt(t,i[0]));n<r&&(r=n)}return r}function Ir(t,e){let r=1/0;for(let n of e)for(var i=0;i<n.length-1;i++){let a=Math.sqrt(En(t,n[i],n[i+1]));a<r&&(r=a)}return r}var at=class{constructor(e,r){this.source=e,this.cache=new Map,this.inflight=new Map,this.tileSize=r}queryFeatures(e,r,i,n){let a=Bn([r,e]);var s=new we.default((a.x+it)/(it*2),1-(a.y+it)/(it*2));s.x>1&&(s.x=s.x-Math.floor(s.x));let o=s.mult(1<<i),l=Math.floor(o.x),u=Math.floor(o.y),f=se({z:i,x:l,y:u}),c=[],d=this.cache.get(f);if(d){let g=new we.default((o.x-l)*this.tileSize,(o.y-u)*this.tileSize);for(let[w,z]of d.data.entries())for(let h of z)h.geomType==1?Rr(g,h.geom)<n&&c.push({feature:h,layerName:w}):h.geomType==2?Ir(g,h.geom)<n&&c.push({feature:h,layerName:w}):Or(g,h.geom)&&c.push({feature:h,layerName:w})}return c}get(e){return O(this,null,function*(){let r=se(e);return new Promise((i,n)=>{let a=this.cache.get(r);if(a)a.used=performance.now(),i(a.data);else{let s=this.inflight.get(r);s?s.push([i,n]):(this.inflight.set(r,[]),this.source.get(e,this.tileSize).then(o=>{this.cache.set(r,{used:performance.now(),data:o});let l=this.inflight.get(r);if(l&&l.forEach(u=>u[0](o)),this.inflight.delete(r),i(o),this.cache.size>=64){let u=1/0,f;this.cache.forEach((c,d)=>{c.used<u&&(u=c.used,f=d)}),f&&this.cache.delete(f)}}).catch(o=>{let l=this.inflight.get(r);l&&l.forEach(u=>u[1](o)),this.inflight.delete(r),n(o)}))}})})}};var Oe=(t,e,r)=>{let i=[];for(let n of t){let a=[];for(let s of n)a.push(s.clone().mult(e).add(r));i.push(a)}return i},Re=(t,e)=>{let r=1<<e;return t<0&&(t=r+t),t>=r&&(t=t%r),t},Lt=class{constructor(e,r,i){this.tileCache=e,this.maxDataLevel=r,this.levelDiff=i}dataTilesForBounds(e,r){let i=Math.pow(2,e)/Math.pow(2,Math.ceil(e)),n=[];var a=1,s=this.tileCache.tileSize;if(e<this.levelDiff)a=1/(1<<this.levelDiff-e)*i,n.push({data_tile:{z:0,x:0,y:0},origin:new fe.default(0,0),scale:a,dim:s*a});else if(e<=this.levelDiff+this.maxDataLevel){let u=1<<this.levelDiff,f=256*i,c=Math.ceil(e)-this.levelDiff,d=Math.floor(r.minX/u/f),g=Math.floor(r.minY/u/f),w=Math.floor(r.maxX/u/f),z=Math.floor(r.maxY/u/f);for(var o=d;o<=w;o++)for(var l=g;l<=z;l++){let h=new fe.default(o*u*f,l*u*f);n.push({data_tile:{z:c,x:Re(o,c),y:Re(l,c)},origin:h,scale:i,dim:s*i})}}else{let u=1<<this.levelDiff;a=(1<<Math.ceil(e)-this.maxDataLevel-this.levelDiff)*i;let f=Math.floor(r.minX/u/256/a),c=Math.floor(r.minY/u/256/a),d=Math.floor(r.maxX/u/256/a),g=Math.floor(r.maxY/u/256/a);for(var o=f;o<=d;o++)for(var l=c;l<=g;l++){let h=new fe.default(o*u*256*a,l*u*256*a);n.push({data_tile:{z:this.maxDataLevel,x:Re(o,this.maxDataLevel),y:Re(l,this.maxDataLevel)},origin:h,scale:a,dim:s*a})}}return n}dataTileForDisplayTile(e){var r,i=1,n=this.tileCache.tileSize,a;if(e.z<this.levelDiff)r={z:0,x:0,y:0},i=1/(1<<this.levelDiff-e.z),a=new fe.default(0,0),n=n*i;else if(e.z<=this.levelDiff+this.maxDataLevel){let s=1<<this.levelDiff;r={z:e.z-this.levelDiff,x:Math.floor(e.x/s),y:Math.floor(e.y/s)},a=new fe.default(r.x*s*256,r.y*s*256)}else{i=1<<e.z-this.maxDataLevel-this.levelDiff;let s=1<<this.levelDiff;r={z:this.maxDataLevel,x:Math.floor(e.x/s/i),y:Math.floor(e.y/s/i)},a=new fe.default(r.x*s*i*256,r.y*s*i*256),n=n*i}return{data_tile:r,scale:i,origin:a,dim:n}}getBbox(e,r){return O(this,null,function*(){let i=this.dataTilesForBounds(e,r);return(yield Promise.all(i.map(a=>this.tileCache.get(a.data_tile)))).map((a,s)=>{let o=i[s];return{data:a,z:e,data_tile:o.data_tile,scale:o.scale,dim:o.dim,origin:o.origin}})})}getDisplayTile(e){return O(this,null,function*(){let r=this.dataTileForDisplayTile(e);return{data:yield this.tileCache.get(r.data_tile),z:e.z,data_tile:r.data_tile,scale:r.scale,origin:r.origin,dim:r.dim}})}queryFeatures(e,r,i){let n=Math.round(i),a=Math.min(n-this.levelDiff,this.maxDataLevel),s=16/(1<<n-a);return this.tileCache.queryFeatures(e,r,a,s)}},Ie=t=>{let e=i=>{let n=i.levelDiff===void 0?2:i.levelDiff,a=i.maxDataZoom||14,s;typeof i.url=="string"?i.url.endsWith(".pmtiles")?s=new Ee(i.url,!0):s=new rt(i.url,!0):s=new Ee(i.url,!0);let o=new at(s,256*1<<n);return new Lt(o,a,n)},r=new Map;if(t.sources)for(let i in t.sources)r.set(i,e(t.sources[i]));else r.set("",e(t));return r};var Xr=U(ee());function Xe(t,e,r,i,n,a,s,o,l){let u=performance.now();t.save(),t.miterLimit=2;for(var f of n){if(f.minzoom&&e<f.minzoom||f.maxzoom&&e>f.maxzoom)continue;let w=r.get(f.dataSource||"");if(!!w)for(let z of w){var c=z.data.get(f.dataLayer);if(c===void 0)continue;f.symbolizer.before&&f.symbolizer.before(t,z.z);let h=z.origin,m=z.dim,p=z.scale;if(t.save(),o){t.beginPath();let b=Math.max(h.x-s.x,a.minX-s.x),x=Math.max(h.y-s.y,a.minY-s.y),y=Math.min(h.x-s.x+m,a.maxX-s.x),v=Math.min(h.y-s.y+m,a.maxY-s.y);t.rect(b,x,y-b,v-x),t.clip()}t.translate(h.x-s.x,h.y-s.y);for(var d of c){let b=d.geom,x=d.bbox;x.maxX*p+h.x<a.minX||x.minX*p+h.x>a.maxX||x.minY*p+h.y>a.maxY||x.maxY*p+h.y<a.minY||f.filter&&!f.filter(z.z,d)||(p!=1&&(b=Oe(b,p,new Xr.default(0,0))),f.symbolizer.draw(t,b,z.z,d))}t.restore()}}if(o&&(t.beginPath(),t.rect(a.minX-s.x,a.minY-s.y,a.maxX-a.minX,a.maxY-a.minY),t.clip()),i){let w=i.searchBbox(a,1/0);for(var g of w)if(t.save(),t.translate(g.anchor.x-s.x,g.anchor.y-s.y),g.draw(t),t.restore(),l){t.lineWidth=.5,t.strokeStyle=l,t.fillStyle=l,t.globalAlpha=1,t.fillRect(g.anchor.x-s.x-2,g.anchor.y-s.y-2,4,4);for(let z of g.bboxes)t.strokeRect(z.minX-s.x,z.minY-s.y,z.maxX-z.minX,z.maxY-z.minY)}}return t.restore(),performance.now()-u}var Yr=U(ee()),Ur=U(Vr());var jr=(t,e,r)=>{let i=256,n=e/i,a=Math.floor(r.minX/i),s=Math.floor(r.minY/i),o=Math.floor(r.maxX/i),l=Math.floor(r.maxY/i),u=Math.log2(n),f=[];for(let c=a;c<=o;c++){let d=c%(1<<t);for(let g=s;g<=l;g++)f.push({display:se({z:t,x:d,y:g}),key:se({z:t-u,x:Math.floor(d/n),y:Math.floor(g/n)})})}return f},Mt=class{constructor(e,r){this.tree=new Ur.default,this.current=new Map,this.dim=e,this.maxLabeledTiles=r}hasPrefix(e){for(let r of this.current.keys())if(r.startsWith(e))return!0;return!1}has(e){return this.current.has(e)}size(){return this.current.size}keys(){return this.current.keys()}searchBbox(e,r){let i=new Set;for(let n of this.tree.search(e))n.indexed_label.order<=r&&i.add(n.indexed_label);return i}searchLabel(e,r){let i=new Set;for(let n of e.bboxes)for(let a of this.tree.search(n))a.indexed_label.order<=r&&i.add(a.indexed_label);return i}bboxCollides(e,r){for(let i of this.tree.search(e))if(i.indexed_label.order<=r)return!0;return!1}labelCollides(e,r){for(let i of e.bboxes)for(let n of this.tree.search(i))if(n.indexed_label.order<=r)return!0;return!1}deduplicationCollides(e){if(!e.deduplicationKey||!e.deduplicationDistance)return!1;let r=e.deduplicationDistance,i={minX:e.anchor.x-r,minY:e.anchor.y-r,maxX:e.anchor.x+r,maxY:e.anchor.y+r};for(let n of this.tree.search(i))if(n.indexed_label.deduplicationKey===e.deduplicationKey&&n.indexed_label.anchor.dist(e.anchor)<r)return!0;return!1}makeEntry(e){this.current.get(e)&&console.log("consistency error 1");let r=new Set;this.current.set(e,r)}insert(e,r,i){let n={anchor:e.anchor,bboxes:e.bboxes,draw:e.draw,order:r,tileKey:i,deduplicationKey:e.deduplicationKey,deduplicationDistance:e.deduplicationDistance},a=this.current.get(i);if(!a){let c=new Set;this.current.set(i,c),a=c}a.add(n);var s=!1,o=!1;for(let c of e.bboxes){var l=c;l.indexed_label=n,this.tree.insert(l),c.minX<0&&(s=!0),c.maxX>this.dim&&(o=!0)}if(s||o){var u=s?this.dim:-this.dim,f=[];for(let g of e.bboxes)f.push({minX:g.minX+u,minY:g.minY,maxX:g.maxX+u,maxY:g.maxY});let c={anchor:new Yr.default(e.anchor.x+u,e.anchor.y),bboxes:f,draw:e.draw,order:r,tileKey:i},d=this.current.get(i);d&&d.add(c);for(let g of f){var l=g;l.indexed_label=c,this.tree.insert(l)}}}pruneOrNoop(e){let r=e.split(":"),i,n=0,a=0;for(var s of this.current.keys()){let o=s.split(":");if(o[3]===r[3]){a++;let l=Math.sqrt(Math.pow(+o[0]-+r[0],2)+Math.pow(+o[1]-+r[1],2));l>n&&(n=l,i=s)}i&&a>this.maxLabeledTiles&&this.pruneKey(i)}}pruneKey(e){let r=this.current.get(e);if(!r)return;let i=[];for(let n of this.tree.all())r.has(n.indexed_label)&&i.push(n);i.forEach(n=>{this.tree.remove(n)}),this.current.delete(e)}removeLabel(e){let r=[];for(let n of this.tree.all())e==n.indexed_label&&r.push(n);r.forEach(n=>{this.tree.remove(n)});let i=this.current.get(e.tileKey);i&&i.delete(e)}},Ve=class{constructor(e,r,i,n,a){this.index=new Mt(256*1<<e,n),this.z=e,this.scratch=r,this.labelRules=i,this.callback=a}layout(e){let r=performance.now(),i=new Set;for(let[o,l]of e)for(let u of l){let f=se(u.data_tile)+":"+o;this.index.has(f)||(this.index.makeEntry(f),i.add(f))}let n=new Set;for(let[o,l]of this.labelRules.entries()){if(l.visible==!1||l.minzoom&&this.z<l.minzoom||l.maxzoom&&this.z>l.maxzoom)continue;let u=l.dataSource||"",f=e.get(u);if(!!f)for(let c of f){let d=se(c.data_tile)+":"+u;if(!i.has(d))continue;let g=c.data.get(l.dataLayer);if(g===void 0)continue;let w=g;l.sort&&w.sort((h,m)=>l.sort?l.sort(h.props,m.props):0);let z={index:this.index,zoom:this.z,scratch:this.scratch,order:o,overzoom:this.z-c.data_tile.z};for(let h of w){if(l.filter&&!l.filter(this.z,h))continue;let m=Oe(h.geom,c.scale,c.origin),p=l.symbolizer.place(z,m,h);if(!!p)for(let b of p){var a=!1;if(!(b.deduplicationKey&&this.index.deduplicationCollides(b))){if(this.index.labelCollides(b,1/0)){if(!this.index.labelCollides(b,o)){let x=this.index.searchLabel(b,1/0);for(let y of x){this.index.removeLabel(y);for(let v of y.bboxes)this.findInvalidatedTiles(n,c.dim,v,d)}this.index.insert(b,o,d),a=!0}}else this.index.insert(b,o,d),a=!0;if(a)for(let x of b.bboxes)(x.maxX>c.origin.x+c.dim||x.minX<c.origin.x||x.minY<c.origin.y||x.maxY>c.origin.y+c.dim)&&this.findInvalidatedTiles(n,c.dim,x,d)}}}}}for(var s of i)this.index.pruneOrNoop(s);return n.size>0&&this.callback&&this.callback(n),performance.now()-r}findInvalidatedTiles(e,r,i,n){let a=jr(this.z,r,i);for(let s of a)s.key!=n&&this.index.hasPrefix(s.key)&&e.add(s.display)}add(e){var r=!0;for(let[i,n]of e)for(let a of n)this.index.has(se(a.data_tile)+":"+i)||(r=!1);return r?0:this.layout(e)}},Ye=class{constructor(e,r,i,n){this.labelers=new Map,this.scratch=e,this.labelRules=r,this.maxLabeledTiles=i,this.callback=n}add(e,r){var i=this.labelers.get(e);return i||(i=new Ve(e,this.scratch,this.labelRules,this.maxLabeledTiles,this.callback),this.labelers.set(e,i)),i.add(r)}getIndex(e){let r=this.labelers.get(e);if(r)return r.index}};var ve={earth:"#FFFBF6",glacier:"#ffffff",residential:"#F4F4F8",hospital:"#FFF6F6",cemetery:"#EFF2EE",school:"#F7F6FF",industrial:"#FFF9EF",wood:"#F4F9EF",grass:"#EBF9E3",park:"#E5F9D5",water:"#B7DFF2",sand:"#ebebeb",buildings:"#F2EDE8",highwayCasing:"#FFC3C3",majorRoadCasing:"#FFB9B9",mediumRoadCasing:"#FFCE8E",minorRoadCasing:"#cccccc",highway:"#FFCEBB",majorRoad:"#FFE4B3",mediumRoad:"#FFF2C8",minorRoad:"#ffffff",boundaries:"#9e9e9e",mask:"#dddddd",countryLabel:"#aaaaaa",cityLabel:"#6C6C6C",stateLabel:"#999999",neighbourhoodLabel:"#888888",landuseLabel:"#898989",waterLabel:"#41ABDC",naturalLabel:"#4B8F14",roadsLabel:"#888888",poisLabel:"#606060"};var _e={earth:"#151515",glacier:"#1c1c1c",residential:"#252B2F",hospital:"#3E2C2C",cemetery:"#36483D",school:"#2C3440",industrial:"#33312C",wood:"#3A3E38",grass:"#4E604D",park:"#2C4034",water:"#4D5B73",sand:"#777777",buildings:"#464545",highwayCasing:"#000000",majorRoadCasing:"#1C1B1B",mediumRoadCasing:"#3E3E3E",minorRoadCasing:"#000000",highway:"#5B5B5B",majorRoad:"#595959",mediumRoad:"#4F4F4F",minorRoad:"#393939",boundaries:"#666666",mask:"#dddddd",countryLabel:"#ffffff",cityLabel:"#FFFFFF",stateLabel:"#ffffff",neighbourhoodLabel:"#FDFDFD",landuseLabel:"#DDDDDD",waterLabel:"#707E95",naturalLabel:"#4c4c4c",roadsLabel:"#C4C4C4",poisLabel:"#959393"};function Ue(t,e,r){return Math.min(Math.max(t,r),e)}var ze=class extends Error{constructor(e){super(`Failed to parse color: "${e}"`)}};function On(t){if(typeof t!="string")throw new ze(t);if(t.trim().toLowerCase()==="transparent")return[0,0,0,0];let e=t.trim();e=Un.test(t)?function(s){let o=s.toLowerCase().trim(),l=Rn[function(u){let f=5381,c=u.length;for(;c;)f=33*f^u.charCodeAt(--c);return(f>>>0)%2341}(o)];if(!l)throw new ze(s);return`#${l}`}(t):t;let r=In.exec(e);if(r){let s=Array.from(r).slice(1);return[...s.slice(0,3).map(o=>parseInt(je(o,2),16)),parseInt(je(s[3]||"f",2),16)/255]}let i=Xn.exec(e);if(i){let s=Array.from(i).slice(1);return[...s.slice(0,3).map(o=>parseInt(o,16)),parseInt(s[3]||"ff",16)/255]}let n=Vn.exec(e);if(n){let s=Array.from(n).slice(1);return[...s.slice(0,3).map(o=>parseInt(o,10)),parseFloat(s[3]||"1")]}let a=Yn.exec(e);if(a){let[s,o,l,u]=Array.from(a).slice(1).map(parseFloat);if(Ue(0,100,o)!==o)throw new ze(t);if(Ue(0,100,l)!==l)throw new ze(t);return[...jn(s,o,l),u||1]}throw new ze(t)}var Nr=t=>parseInt(t.replace(/_/g,""),36),Rn="1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm".split(" ").reduce((t,e)=>{let r=Nr(e.substring(0,3)),i=Nr(e.substring(3)).toString(16),n="";for(let a=0;a<6-i.length;a++)n+="0";return t[r]=`${n}${i}`,t},{}),je=(t,e)=>Array.from(Array(e)).map(()=>t).join(""),In=new RegExp(`^#${je("([a-f0-9])",3)}([a-f0-9])?$`,"i"),Xn=new RegExp(`^#${je("([a-f0-9]{2})",3)}([a-f0-9]{2})?$`,"i"),Vn=new RegExp(`^rgba?\\(\\s*(\\d+)\\s*${je(",\\s*(\\d+)\\s*",2)}(?:,\\s*([\\d.]+))?\\s*\\)$`,"i"),Yn=/^hsla?\(\s*([\d.]+)\s*,\s*([\d.]+)%\s*,\s*([\d.]+)%(?:\s*,\s*([\d.]+))?\s*\)$/i,Un=/^[a-z]+$/i,qr=t=>Math.round(255*t),jn=(t,e,r)=>{let i=r/100;if(e===0)return[i,i,i].map(qr);let n=(t%360+360)%360/60,a=(1-Math.abs(2*i-1))*(e/100),s=a*(1-Math.abs(n%2-1)),o=0,l=0,u=0;n>=0&&n<1?(o=a,l=s):n>=1&&n<2?(o=s,l=a):n>=2&&n<3?(l=a,u=s):n>=3&&n<4?(l=s,u=a):n>=4&&n<5?(o=s,u=a):n>=5&&n<6&&(o=a,u=s);let f=i-a/2;return[o+f,l+f,u+f].map(qr)};function Ct(t){let[e,r,i,n]=On(t).map((u,f)=>f===3?u:u/255),a=Math.max(e,r,i),s=Math.min(e,r,i),o=(a+s)/2;if(a===s)return[0,0,o,n];let l=a-s;return[60*(e===a?(r-i)/l+(r<i?6:0):r===a?(i-e)/l+2:(e-r)/l+4),o>.5?l/(2-a-s):l/(a+s),o,n]}function Zr(t,e,r,i){return`hsla(${(t%360).toFixed()}, ${Ue(0,100,100*e).toFixed()}%, ${Ue(0,100,100*r).toFixed()}%, ${parseFloat(Ue(0,1,i).toFixed(3))})`}var N=U(ee()),Qr=U($r()),ei=U(Kr());var I=class{constructor(e,r){this.str=e!=null?e:r,this.per_feature=typeof this.str=="function"&&this.str.length==2}get(e,r){return typeof this.str=="function"?this.str(e,r):this.str}},D=class{constructor(e,r=1){this.value=e!=null?e:r,this.per_feature=typeof this.value=="function"&&this.value.length==2}get(e,r){return typeof this.value=="function"?this.value(e,r):this.value}},Ne=class{constructor(e){var r;this.label_props=(r=e==null?void 0:e.label_props)!=null?r:["name"],this.textTransform=e==null?void 0:e.textTransform}get(e,r){let i,n;typeof this.label_props=="function"?n=this.label_props(e,r):n=this.label_props;for(let s of n)if(r.props.hasOwnProperty(s)&&typeof r.props[s]=="string"){i=r.props[s];break}let a;return typeof this.textTransform=="function"?a=this.textTransform(e,r):a=this.textTransform,i&&a==="uppercase"?i=i.toUpperCase():i&&a==="lowercase"?i=i.toLowerCase():i&&a==="capitalize"&&(i=i.toLowerCase().split(" ").map(l=>l[0].toUpperCase()+l.slice(1)).join(" ")),i}},qe=class{constructor(e){var r,i;(e==null?void 0:e.font)?this.font=e.font:(this.family=(r=e==null?void 0:e.fontFamily)!=null?r:"sans-serif",this.size=(i=e==null?void 0:e.fontSize)!=null?i:12,this.weight=e==null?void 0:e.fontWeight,this.style=e==null?void 0:e.fontStyle)}get(e,r){if(this.font)return typeof this.font=="function"?this.font(e,r):this.font;var i="";this.style&&(typeof this.style=="function"?i=this.style(e,r)+" ":i=this.style+" ");var n="";this.weight&&(typeof this.weight=="function"?n=this.weight(e,r)+" ":n=this.weight+" ");var a;typeof this.size=="function"?a=this.size(e,r):a=this.size;var s;return typeof this.family=="function"?s=this.family(e,r):s=this.family,`${i}${n}${a}px ${s}`}},At=class{constructor(e,r=[]){this.value=e!=null?e:r,this.per_feature=typeof this.value=="function"&&this.value.length==2}get(e,r){return typeof this.value=="function"?this.value(e,r):this.value}};var Bt=U(ee()),Wn=(t,e,r)=>{var i=[],n,a,s,o=0,l=0,u=0,f=0,c=0,d=0,g=0,w=0,z=0,h=0,m=0,p=0;if(t.length<2)return[];if(t.length===2)return u=Math.sqrt(Math.pow(t[1].x-t[0].x,2)+Math.pow(t[1].y-t[0].y,2)),[{length:u,beginIndex:0,beginDistance:0,endIndex:2,endDistance:u}];for(f=Math.sqrt(Math.pow(t[1].x-t[0].x,2)+Math.pow(t[1].y-t[0].y,2)),o=1,l=t.length-1;o<l;o++)n=t[o-1],a=t[o],s=t[o+1],d=a.x-n.x,g=a.y-n.y,w=s.x-a.x,z=s.y-a.y,c=Math.sqrt(w*w+z*z),u+=f,h=Math.acos((d*w+g*z)/(f*c)),(h>e||u-p>r)&&(i.push({length:u-p,beginDistance:p,beginIndex:m,endIndex:o+1,endDistance:u}),m=o,p=u),f=c;return o-m>0&&i.push({length:u-p+c,beginIndex:m,beginDistance:p,endIndex:o+1,endDistance:u+c}),i};function Jr(t,e,r,i){let n,a,s=0,o=[];var l=-1/0;for(let f of t){let c=Wn(f,Math.PI/45,e);for(let d of c)if(d.length>=e+i){let g=new Bt.default(f[d.beginIndex].x,f[d.beginIndex].y),w=f[d.endIndex-1],z=new Bt.default((w.x-g.x)/d.length,(w.y-g.y)/d.length);for(var u=i;u<d.length-e;u+=r)o.push({start:g.add(z.mult(u)),end:g.add(z.mult(u+e))})}}return o}function Gr(t,e,r,i){let n=e.x-t.x,a=e.y-t.y,s=Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2)),o=[];for(var l=0;l<r+i;l+=2*i){let u=l*1/s;o.push({x:t.x+u*n,y:t.y+u*a})}return o}function Et(t,e){if(t.length<=e)return[t];let r=e-1,i=t.lastIndexOf(" ",r),n=t.indexOf(" ",r);if(i==-1&&n==-1)return[t];let a,s;return n==-1||i>=0&&r-i<n-r?(a=t.substring(0,i),s=t.substring(i+1,t.length)):(a=t.substring(0,n),s=t.substring(n+1,t.length)),[a,...Et(s,e)]}var Hn="\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3220-\u3229\u3248-\u324F\u3251-\u325F\u3280-\u3289\u32B1-\u32BF\u3400-\u4DB5\u4E00-\u9FEA\uF900-\uFA6D\uFA70-\uFAD9\u2000",Na=new RegExp("^["+Hn+"]+$");var ti=5400,Q;(function(i){i[i.Left=1]="Left",i[i.Center=2]="Center",i[i.Right=3]="Right"})(Q||(Q={}));var T;(function(l){l[l.N=1]="N",l[l.NE=2]="NE",l[l.E=3]="E",l[l.SE=4]="SE",l[l.S=5]="S",l[l.SW=6]="SW",l[l.W=7]="W",l[l.NW=8]="NW"})(T||(T={}));var Kn=(t,e,r)=>{let i=document.createElement("canvas"),n=i.getContext("2d");return i.width=t,i.height=e,n!==null&&r(i,n),i},A=class{constructor(e){var r;this.pattern=e.pattern,this.fill=new I(e.fill,"black"),this.opacity=new D(e.opacity,1),this.stroke=new I(e.stroke,"black"),this.width=new D(e.width,0),this.per_feature=(r=this.fill.per_feature||this.opacity.per_feature||this.stroke.per_feature||this.width.per_feature||e.per_feature)!=null?r:!1,this.do_stroke=!1}before(e,r){if(!this.per_feature){e.globalAlpha=this.opacity.get(r),e.fillStyle=this.fill.get(r),e.strokeStyle=this.stroke.get(r);let i=this.width.get(r);i>0&&(this.do_stroke=!0),e.lineWidth=i}if(this.pattern){let i=e.createPattern(this.pattern,"repeat");i&&(e.fillStyle=i)}}draw(e,r,i,n){var a=!1;if(this.per_feature){e.globalAlpha=this.opacity.get(i,n),e.fillStyle=this.fill.get(i,n);var s=this.width.get(i,n);s&&(a=!0,e.strokeStyle=this.stroke.get(i,n),e.lineWidth=s)}let o=()=>{e.fill(),(a||this.do_stroke)&&e.stroke()};var l=0;e.beginPath();for(var u of r){l+u.length>ti&&(o(),l=0,e.beginPath());for(var f=0;f<u.length;f++){let c=u[f];f==0?e.moveTo(c.x,c.y):e.lineTo(c.x,c.y)}l+=u.length}l>0&&o()}};function Jn(t,e){return r=>{let i=r-t;return i>=0&&i<e.length?e[i]:0}}function ri(t,e){let r=0;for(;e[r+1][0]<t;)r++;return r}function ii(t,e,r){return t*(r-e)+e}function ni(t,e,r,i){let n=i[e+1][0]-i[e][0],a=t-i[e][0];return n===0?0:r===1?a/n:(Math.pow(r,a)-1)/(Math.pow(r,n)-1)}function Y(t,e){return r=>{if(e.length<1)return 0;if(r<=e[0][0])return e[0][1];if(r>=e[e.length-1][0])return e[e.length-1][1];let i=ri(r,e),n=ni(r,i,t,e);return ii(n,e[i][1],e[i+1][1])}}function Gn(t,e){return r=>{if(e.length<1)return 0;let i=t;for(let n=0;n<e.length;n++)r>=e[n][0]&&(i=e[n][1]);return i}}function Qn(t){return Y(1,t)}function ea(t,e,r,i,n){return a=>{if(n.length<1)return 0;let s=new Qr.default(t,e,r,i),o=ri(a,n),l=s.solve(ni(a,o,1,n));return ii(l,n[o][1],n[o+1][1])}}var R=class{constructor(e){var r;this.color=new I(e.color,"black"),this.width=new D(e.width),this.opacity=new D(e.opacity),this.dash=e.dash?new At(e.dash):null,this.dashColor=new I(e.dashColor,"black"),this.dashWidth=new D(e.dashWidth,1),this.lineCap=new I(e.lineCap,"butt"),this.lineJoin=new I(e.lineJoin,"miter"),this.skip=!1,this.per_feature=!!(((r=this.dash)==null?void 0:r.per_feature)||this.color.per_feature||this.opacity.per_feature||this.width.per_feature||this.lineCap.per_feature||this.lineJoin.per_feature||e.per_feature)}before(e,r){this.per_feature||(e.strokeStyle=this.color.get(r),e.lineWidth=this.width.get(r),e.globalAlpha=this.opacity.get(r),e.lineCap=this.lineCap.get(r),e.lineJoin=this.lineJoin.get(r))}draw(e,r,i,n){if(this.skip)return;let a=()=>{this.per_feature&&(e.globalAlpha=this.opacity.get(i,n),e.lineCap=this.lineCap.get(i,n),e.lineJoin=this.lineJoin.get(i,n)),this.dash?(e.save(),this.per_feature?(e.lineWidth=this.dashWidth.get(i,n),e.strokeStyle=this.dashColor.get(i,n),e.setLineDash(this.dash.get(i,n))):e.setLineDash(this.dash.get(i)),e.stroke(),e.restore()):(e.save(),this.per_feature&&(e.lineWidth=this.width.get(i,n),e.strokeStyle=this.color.get(i,n)),e.stroke(),e.restore())};var s=0;e.beginPath();for(var o of r){s+o.length>ti&&(a(),s=0,e.beginPath());for(var l=0;l<o.length;l++){let u=o[l];l==0?e.moveTo(u.x,u.y):e.lineTo(u.x,u.y)}s+=o.length}s>0&&a()}},ai=class{constructor(e){this.name=e.name,this.sheet=e.sheet,this.dpr=window.devicePixelRatio}place(e,r,i){let n=r[0],a=new N.default(r[0][0].x,r[0][0].y),s=this.sheet.get(this.name),o=s.w/this.dpr,l=s.h/this.dpr,u={minX:a.x-o/2,minY:a.y-l/2,maxX:a.x+o/2,maxY:a.y+l/2};return[{anchor:a,bboxes:[u],draw:c=>{c.globalAlpha=1,c.drawImage(this.sheet.canvas,s.x,s.y,s.w,s.h,-s.w/2/this.dpr,-s.h/2/this.dpr,s.w/2,s.h/2)}}]}},oe=class{constructor(e){this.radius=new D(e.radius,3),this.fill=new I(e.fill,"black"),this.stroke=new I(e.stroke,"white"),this.width=new D(e.width,0),this.opacity=new D(e.opacity)}draw(e,r,i,n){e.globalAlpha=this.opacity.get(i,n);let a=this.radius.get(i,n),s=this.width.get(i,n);s>0&&(e.strokeStyle=this.stroke.get(i,n),e.lineWidth=s,e.beginPath(),e.arc(r[0][0].x,r[0][0].y,a+s/2,0,2*Math.PI),e.stroke()),e.fillStyle=this.fill.get(i,n),e.beginPath(),e.arc(r[0][0].x,r[0][0].y,a,0,2*Math.PI),e.fill()}place(e,r,i){let n=r[0],a=new N.default(r[0][0].x,r[0][0].y),s=this.radius.get(e.zoom,i),o={minX:a.x-s,minY:a.y-s,maxX:a.x+s,maxY:a.y+s};return[{anchor:a,bboxes:[o],draw:u=>{this.draw(u,[[new N.default(0,0)]],e.zoom,i)}}]}},ot=class{constructor(e){this.font=new qe(e),this.text=new Ne(e),this.fill=new I(e.fill,"black"),this.background=new I(e.background,"white"),this.padding=new D(e.padding,0)}place(e,r,i){let n=this.text.get(e.zoom,i);if(!n)return;let a=this.font.get(e.zoom,i);e.scratch.font=a;let s=e.scratch.measureText(n),o=s.width,l=s.actualBoundingBoxAscent,u=s.actualBoundingBoxDescent,f=r[0],c=new N.default(r[0][0].x,r[0][0].y),d=this.padding.get(e.zoom,i),g={minX:c.x-o/2-d,minY:c.y-l-d,maxX:c.x+o/2+d,maxY:c.y+u+d};return[{anchor:c,bboxes:[g],draw:z=>{z.globalAlpha=1,z.fillStyle=this.background.get(e.zoom,i),z.fillRect(-o/2-d,-l-d,o+2*d,l+u+2*d),z.fillStyle=this.fill.get(e.zoom,i),z.font=a,z.fillText(n,-o/2,0)}}]}},Ze=class{constructor(e){this.list=e}place(e,r,i){var n=this.list[0].place(e,r,i);if(!n)return;var a=n[0];let s=a.anchor,o=a.bboxes[0],l=o.maxY-o.minY,u=[{draw:a.draw,translate:{x:0,y:0}}],f=[[new N.default(r[0][0].x,r[0][0].y+l)]];for(let d=1;d<this.list.length;d++)n=this.list[d].place(e,f,i),n&&(a=n[0],o=si(o,a.bboxes[0]),u.push({draw:a.draw,translate:{x:0,y:l}}));return[{anchor:s,bboxes:[o],draw:d=>{for(let g of u)d.save(),d.translate(g.translate.x,g.translate.y),g.draw(d),d.restore()}}]}},si=(t,e)=>({minX:Math.min(t.minX,e.minX),minY:Math.min(t.minY,e.minY),maxX:Math.max(t.maxX,e.maxX),maxY:Math.max(t.maxY,e.maxY)}),$e=class{constructor(e){this.list=e}place(e,r,i){let n=this.list[0];if(!n)return;var a=n.place(e,r,i);if(!a)return;var s=a[0];let o=s.anchor,l=s.bboxes[0],u=[s.draw];for(let c=1;c<this.list.length;c++){if(a=this.list[c].place(e,r,i),!a)return;s=a[0],l=si(l,s.bboxes[0]),u.push(s.draw)}return[{anchor:o,bboxes:[l],draw:c=>{u.forEach(d=>d(c))}}]}},Ot=class{constructor(e){this.symbolizer=e}place(e,r,i){let n=r[0][0],a=this.symbolizer.place(e,[[new N.default(0,0)]],i);if(!a||a.length==0)return;let s=a[0],o=s.bboxes[0],l=o.maxX-o.minX,u=o.maxY-o.minY,f={minX:n.x-l/2,maxX:n.x+l/2,minY:n.y-u/2,maxY:n.y+u/2};return[{anchor:n,bboxes:[f],draw:d=>{d.translate(-l/2,u/2-o.maxY),s.draw(d,{justify:2})}}]}},oi=class{constructor(e,r){this.padding=new D(e,0),this.symbolizer=r}place(e,r,i){let n=this.symbolizer.place(e,r,i);if(!n||n.length==0)return;let a=this.padding.get(e.zoom,i);for(var s of n)for(var o of s.bboxes)o.minX-=a,o.minY-=a,o.maxX+=a,o.maxY+=a;return n}},We=class{constructor(e){this.font=new qe(e),this.text=new Ne(e),this.fill=new I(e.fill,"black"),this.stroke=new I(e.stroke,"black"),this.width=new D(e.width,0),this.lineHeight=new D(e.lineHeight,1),this.letterSpacing=new D(e.letterSpacing,0),this.maxLineCodeUnits=new D(e.maxLineChars,15),this.justify=e.justify}place(e,r,i){let n=this.text.get(e.zoom,i);if(!n)return;let a=this.font.get(e.zoom,i);e.scratch.font=a;let s=this.letterSpacing.get(e.zoom,i),o=Et(n,this.maxLineCodeUnits.get(e.zoom,i));var l="",u=0;for(let p of o)p.length>u&&(u=p.length,l=p);let f=e.scratch.measureText(l),c=f.width+s*(u-1),d=f.actualBoundingBoxAscent,g=f.actualBoundingBoxDescent,w=(d+g)*this.lineHeight.get(e.zoom,i),z=new N.default(r[0][0].x,r[0][0].y),h={minX:z.x,minY:z.y-d,maxX:z.x+c,maxY:z.y+g+(o.length-1)*w};return[{anchor:z,bboxes:[h],draw:(p,b)=>{p.globalAlpha=1,p.font=a,p.fillStyle=this.fill.get(e.zoom,i);let x=this.width.get(e.zoom,i);var y=0;for(let F of o){var v=0;if(this.justify==2||b&&b.justify==2?v=(c-p.measureText(F).width)/2:(this.justify==3||b&&b.justify==3)&&(v=c-p.measureText(F).width),x)if(p.lineWidth=x*2,p.strokeStyle=this.stroke.get(e.zoom,i),s>0){var _=v;for(var S of F)p.strokeText(S,_,y),_+=p.measureText(S).width+s}else p.strokeText(F,v,y);if(s>0){var _=v;for(var S of F)p.fillText(S,_,y),_+=p.measureText(S).width+s}else p.fillText(F,v,y);y+=w}}}]}},te=class{constructor(e){this.centered=new Ot(new We(e))}place(e,r,i){return this.centered.place(e,r,i)}},Rt=class{constructor(e,r){var i,n,a;this.symbolizer=e,this.offsetX=new D(r.offsetX,0),this.offsetY=new D(r.offsetY,0),this.justify=(i=r.justify)!=null?i:void 0,this.placements=(n=r.placements)!=null?n:[2,6,8,4,1,3,5,7],this.ddValues=(a=r.ddValues)!=null?a:()=>({})}place(e,r,i){if(i.geomType!==G.Point)return;let n=r[0][0],a=this.symbolizer.place(e,[[new N.default(0,0)]],i);if(!a||a.length==0)return;let s=a[0],o=s.bboxes[0],l=this.offsetX,u=this.offsetY,f=this.justify,c=this.placements,{offsetX:d,offsetY:g,justify:w,placements:z}=this.ddValues(e.zoom,i)||{};d&&(l=new D(d,0)),g&&(u=new D(g,0)),w&&(f=w),z&&(c=z);let h=l.get(e.zoom,i),m=u.get(e.zoom,i),p=(_,S)=>({minX:_.x+S.x+o.minX,minY:_.y+S.y+o.minY,maxX:_.x+S.x+o.maxX,maxY:_.y+S.y+o.maxY});var b=new N.default(h,m),x;let y=_=>{_.translate(b.x,b.y),s.draw(_,{justify:x})},v=(_,S)=>{let F=p(_,S);if(!e.index.bboxCollides(F,e.order))return[{anchor:n,bboxes:[F],draw:y}]};for(let _ of c){let S=this.computeXAxisOffset(h,o,_),F=this.computeYAxisOffset(m,o,_);return x=this.computeJustify(f,_),b=new N.default(S,F),v(n,b)}}computeXAxisOffset(e,r,i){let n=r.maxX,a=n/2;return[1,5].includes(i)?e-a:[8,7,6].includes(i)?e-n:e}computeYAxisOffset(e,r,i){let n=Math.abs(r.minY),a=r.maxY,s=(r.minY+r.maxY)/2;return[3,7].includes(i)?e-s:[8,2,1].includes(i)?e-a:[6,4,5].includes(i)?e+n:e}computeJustify(e,r){return e||([1,5].includes(r)?2:[2,3,4].includes(r)?1:3)}},ce=class{constructor(e){this.symbolizer=new Rt(new We(e),e)}place(e,r,i){return this.symbolizer.place(e,r,i)}},Se;(function(i){i[i.Above=1]="Above",i[i.Center=2]="Center",i[i.Below=3]="Below"})(Se||(Se={}));var Le=class{constructor(e){var r;this.font=new qe(e),this.text=new Ne(e),this.fill=new I(e.fill,"black"),this.stroke=new I(e.stroke,"black"),this.width=new D(e.width,0),this.offset=new D(e.offset,0),this.position=(r=e.position)!=null?r:1,this.maxLabelCodeUnits=new D(e.maxLabelChars,40),this.repeatDistance=new D(e.repeatDistance,250)}place(e,r,i){let n=this.text.get(e.zoom,i);if(!n||n.length>this.maxLabelCodeUnits.get(e.zoom,i))return;let a=20,s=i.bbox;if(s.maxY-s.minY<a&&s.maxX-s.minX<a)return;let o=this.font.get(e.zoom,i);e.scratch.font=o;let l=e.scratch.measureText(n),u=l.width,f=l.actualBoundingBoxAscent+l.actualBoundingBoxDescent;var c=this.repeatDistance.get(e.zoom,i);e.overzoom>4&&(c*=1<<e.overzoom-4);let d=f*2,g=Jr(r,u,c,d);if(g.length==0)return;let w=[];for(let z of g){let h=z.end.x-z.start.x,m=z.end.y-z.start.y,b=Gr(z.start,z.end,u,d/2).map(y=>({minX:y.x-d/2,minY:y.y-d/2,maxX:y.x+d/2,maxY:y.y+d/2})),x=y=>{y.globalAlpha=1,y.rotate(Math.atan2(m,h)),h<0&&(y.scale(-1,-1),y.translate(-u,0));let v=0;this.position===3?v+=f:this.position===2&&(v+=f/2),y.translate(0,v-this.offset.get(e.zoom,i)),y.font=o;let _=this.width.get(e.zoom,i);_&&(y.lineWidth=_,y.strokeStyle=this.stroke.get(e.zoom,i),y.strokeText(n,0,0)),y.fillStyle=this.fill.get(e.zoom,i),y.fillText(n,0,0)};w.push({anchor:z.start,bboxes:b,draw:x,deduplicationKey:n,deduplicationDistance:c})}return w}},Fe=class{constructor(e){this.symbolizer=new We(e)}place(e,r,i){let n=i.bbox;if((n.maxY-n.minY)*(n.maxX-n.minX)<2e4)return;let s=this.symbolizer.place(e,[[new N.default(0,0)]],i);if(!s||s.length==0)return;let o=s[0],l=o.bboxes[0],u=r[0],f=(0,ei.default)([u.map(w=>[w.x,w.y])]),c=new N.default(f[0],f[1]),d={minX:c.x-(l.maxX-l.minX)/2,minY:c.y-(l.maxY-l.minY)/2,maxX:c.x+(l.maxX-l.minX)/2,maxY:c.y+(l.maxY-l.minY)/2};return[{anchor:c,bboxes:[d],draw:w=>{w.translate(o.anchor.x-(l.maxX-l.minX)/2,o.anchor.y),o.draw(w)}}]}};var li=(t,e)=>{let r=Ct(e),i=Jt({},t);for(let[n,a]of Object.entries(t)){let s=Ct(a);i[n]=Zr(r[0],r[1],s[2],s[3])}return i},ke=(t,e)=>(e&&(t=li(t,e)),[{dataLayer:"earth",symbolizer:new A({fill:t.earth})},{dataLayer:"natural",symbolizer:new A({fill:t.glacier}),filter:(r,i)=>i.props.natural=="glacier"},{dataLayer:"landuse",symbolizer:new A({fill:t.residential}),filter:(r,i)=>i.props.landuse=="residential"||i.props.place=="neighbourhood"},{dataLayer:"landuse",symbolizer:new A({fill:t.hospital}),filter:(r,i)=>i.props.amenity=="hospital"},{dataLayer:"landuse",symbolizer:new A({fill:t.cemetery}),filter:(r,i)=>i.props.landuse=="cemetery"},{dataLayer:"landuse",symbolizer:new A({fill:t.school}),filter:(r,i)=>i.props.amenity=="school"||i.props.amenity=="kindergarten"||i.props.amenity=="university"||i.props.amenity=="college"},{dataLayer:"landuse",symbolizer:new A({fill:t.industrial}),filter:(r,i)=>i.props.landuse=="industrial"},{dataLayer:"natural",symbolizer:new A({fill:t.wood}),filter:(r,i)=>i.props.natural=="wood"},{dataLayer:"landuse",symbolizer:new A({fill:t.grass}),filter:(r,i)=>i.props.landuse=="grass"},{dataLayer:"landuse",symbolizer:new A({fill:t.park}),filter:(r,i)=>i.props.leisure=="park"},{dataLayer:"water",symbolizer:new A({fill:t.water})},{dataLayer:"natural",symbolizer:new A({fill:t.sand}),filter:(r,i)=>i.props.natural=="sand"},{dataLayer:"buildings",symbolizer:new A({fill:t.buildings})},{dataLayer:"roads",symbolizer:new R({color:t.highwayCasing,width:Y(1.4,[[5,1.5],[11,4],[16,9],[20,40]])}),filter:(r,i)=>i.props["pmap:kind"]=="highway"},{dataLayer:"roads",symbolizer:new R({color:t.majorRoadCasing,width:Y(1.4,[[9,3],[12,4],[17,8],[20,22]])}),filter:(r,i)=>i.props["pmap:kind"]=="major_road"},{dataLayer:"roads",symbolizer:new R({color:t.mediumRoadCasing,width:Y(1.4,[[13,3],[17,6],[20,18]])}),filter:(r,i)=>i.props["pmap:kind"]=="medium_road"},{dataLayer:"roads",symbolizer:new R({color:t.minorRoadCasing,width:Y(1.4,[[14,2],[17,5],[20,15]])}),filter:(r,i)=>i.props["pmap:kind"]=="minor_road"},{dataLayer:"roads",symbolizer:new R({color:t.minorRoad,width:Y(1.4,[[14,1],[17,3],[20,13]])}),filter:(r,i)=>i.props["pmap:kind"]=="minor_road"},{dataLayer:"roads",symbolizer:new R({color:t.mediumRoad,width:Y(1.4,[[13,2],[17,4],[20,15]])}),filter:(r,i)=>i.props["pmap:kind"]=="medium_road"},{dataLayer:"roads",symbolizer:new R({color:t.majorRoad,width:Y(1.4,[[9,2],[12,3],[17,6],[20,20]])}),filter:(r,i)=>i.props["pmap:kind"]=="major_road"},{dataLayer:"roads",symbolizer:new R({color:t.highway,width:Y(1.4,[[5,.5],[11,2.5],[16,7],[20,30]])}),filter:(r,i)=>i.props["pmap:kind"]=="highway"},{dataLayer:"boundaries",symbolizer:new R({color:t.boundaries,width:2,opacity:.4})},{dataLayer:"mask",symbolizer:new A({fill:t.mask})}]),Me=(t,e,r,i)=>{e&&(t=li(t,e));var n=["name"];r&&(n=r);let a=(s,o)=>i?s instanceof ce?new Ze([s,new ce({fill:o,label_props:i})]):new Ze([s,new te({fill:o,label_props:i})]):s;return[{dataLayer:"places",symbolizer:a(new te({label_props:n,fill:t.countryLabel,lineHeight:1.5,font:(s,o)=>s<6?"200 14px sans-serif":"200 20px sans-serif",textTransform:"uppercase"}),t.countryLabel),filter:(s,o)=>o.props["pmap:kind"]=="country"},{dataLayer:"places",symbolizer:a(new te({label_props:n,fill:t.stateLabel,font:"300 16px sans-serif"}),t.stateLabel),filter:(s,o)=>o.props["pmap:kind"]=="state"},{id:"cities_high",dataLayer:"places",filter:(s,o)=>o.props["pmap:kind"]=="city",minzoom:7,symbolizer:a(new te({label_props:n,fill:t.cityLabel,font:(s,o)=>(o==null?void 0:o.props["pmap:rank"])===1?s>8?"600 20px sans-serif":"600 12px sans-serif":s>8?"600 16px sans-serif":"600 10px sans-serif"}),t.cityLabel),sort:(s,o)=>s["pmap:rank"]-o["pmap:rank"]},{id:"cities_low",dataLayer:"places",filter:(s,o)=>o.props["pmap:kind"]=="city",maxzoom:6,symbolizer:new $e([new oe({radius:2,fill:t.cityLabel}),a(new ce({label_props:n,fill:t.cityLabel,offsetX:2,offsetY:2,font:(s,o)=>(o==null?void 0:o.props["pmap:rank"])===1?s>8?"600 20px sans-serif":"600 12px sans-serif":s>8?"600 16px sans-serif":"600 10px sans-serif"}),t.cityLabel)]),sort:(s,o)=>s["pmap:rank"]-o["pmap:rank"]},{id:"neighbourhood",dataLayer:"places",symbolizer:a(new te({label_props:n,fill:t.neighbourhoodLabel,font:"500 10px sans-serif",textTransform:"uppercase"}),t.neighbourhoodLabel),filter:(s,o)=>o.props["pmap:kind"]=="neighbourhood"},{dataLayer:"landuse",symbolizer:a(new Fe({label_props:n,fill:t.landuseLabel,font:"300 12px sans-serif"}),t.landuseLabel)},{dataLayer:"water",symbolizer:a(new Fe({label_props:n,fill:t.waterLabel,font:"italic 600 12px sans-serif"}),t.waterLabel)},{dataLayer:"natural",symbolizer:a(new Fe({label_props:n,fill:t.naturalLabel,font:"italic 300 12px sans-serif"}),t.naturalLabel)},{dataLayer:"roads",symbolizer:a(new Le({label_props:n,fill:t.roadsLabel,font:"500 12px sans-serif"}),t.roadsLabel),minzoom:12},{dataLayer:"roads",symbolizer:new ot({label_props:["ref"],font:"600 9px sans-serif",background:t.highway,padding:2,fill:t.neighbourhoodLabel}),filter:(s,o)=>o.props["pmap:kind"]=="highway"},{dataLayer:"pois",symbolizer:new $e([new oe({radius:2,fill:t.poisLabel}),a(new ce({label_props:n,fill:t.poisLabel,offsetX:2,offsetY:2,font:"300 10px sans-serif"}),t.poisLabel)])}]};var ui=(t,e,r)=>[{dataSource:t,dataLayer:e,symbolizer:new oe({opacity:.2,fill:r,radius:4}),filter:(i,n)=>n.geomType==G.Point},{dataSource:t,dataLayer:e,symbolizer:new R({opacity:.2,color:r,width:2}),filter:(i,n)=>n.geomType==G.Line},{dataSource:t,dataLayer:e,symbolizer:new A({opacity:.2,fill:r,stroke:r,width:1}),filter:(i,n)=>n.geomType==G.Polygon}],lt=(t,e)=>{var r=[];for(var[i,n]of t)for(var a of n)for(var s of a.data.keys())i===e.dataSource&&s===e.dataLayer||(r=r.concat(ui(i,s,"steelblue")));return r=r.concat(ui(e.dataSource||"",e.dataLayer,"red")),r};var He=6378137,hi=85.0511287798,$=He*Math.PI,fi=t=>{let e=Math.PI/180,r=Math.max(Math.min(hi,t.y),-hi),i=Math.sin(r*e);return new le.default(He*t.x*e,He*Math.log((1+i)/(1-i))/2)},ta=t=>{var e=180/Math.PI;return{lat:(2*Math.atan(Math.exp(t.y/He))-Math.PI/2)*e,lng:t.x*e/He}},ra=(t,e)=>r=>{let i=fi(r);return new le.default((i.x+$)/($*2),1-(i.y+$)/($*2)).mult((1<<e)*256).sub(t)},ia=(t,e)=>r=>{let i=new le.default(r.x,r.y).add(t).div((1<<e)*256),n=new le.default(i.x*($*2)-$,(1-i.y)*($*2)-$);return ta(n)},It=(t,e)=>{let r=e*(360/t);return Math.log2(r/256)},ci=class{constructor(e){let r=e.dark?_e:ve;this.paint_rules=e.paint_rules||ke(r,e.shade),this.label_rules=e.label_rules||Me(r,e.shade,e.language1,e.language2),this.backgroundColor=e.backgroundColor,this.views=Ie(e),this.debug=e.debug||"",this.xray=e.xray}drawContext(e,r,i,n,a){return O(this,null,function*(){let s=fi(n),l=new le.default((s.x+$)/($*2),1-(s.y+$)/($*2)).clone().mult(Math.pow(2,a)*256).sub(new le.default(r/2,i/2)),u={minX:l.x,minY:l.y,maxX:l.x+r,maxY:l.y+i},f=[];for(let[p,b]of this.views){let x=b.getBbox(a,u);f.push({key:p,promise:x})}let c=yield Promise.all(f.map(p=>p.promise.then(b=>({status:"fulfilled",value:b,key:p.key}),b=>({status:"rejected",value:[],reason:b,key:p.key})))),d=new Map;for(let p of c)p.status==="fulfilled"&&d.set(p.key,p.value);let g=performance.now(),w=new Ve(a,e,this.label_rules,16,void 0),z=w.add(d);this.backgroundColor&&(e.save(),e.fillStyle=this.backgroundColor,e.fillRect(0,0,r,i),e.restore());let h=this.paint_rules;this.xray&&(h=lt(d,this.xray));let m=Xe(e,a,d,this.xray?null:w.index,h,u,l,!0,this.debug);if(this.debug){e.save(),e.translate(-l.x,-l.y),e.strokeStyle=this.debug,e.fillStyle=this.debug,e.font="12px sans-serif";let p=0;for(let[b,x]of d){for(let y of x){e.strokeRect(y.origin.x,y.origin.y,y.dim,y.dim);let v=y.data_tile;e.fillText(b+(b?" ":"")+v.z+" "+v.x+" "+v.y,y.origin.x+4,y.origin.y+14*(1+p))}p++}e.restore()}return{elapsed:performance.now()-g,project:ra(l,a),unproject:ia(l,a)}})}drawCanvas(a,s,o){return O(this,arguments,function*(e,r,i,n={}){let l=window.devicePixelRatio,u=e.clientWidth,f=e.clientHeight;e.width==u*l&&e.height==f*l||(e.width=u*l,e.height=f*l),n.lang&&(e.lang=n.lang);let c=e.getContext("2d");return c.setTransform(l,0,0,l,0,0),this.drawContext(c,u,f,r,i)})}drawContextBounds(e,r,i,n,a){return O(this,null,function*(){let s=i.x-r.x,o=new le.default((r.x+i.x)/2,(r.y+i.y)/2);return this.drawContext(e,n,a,o,It(s,n))})}drawCanvasBounds(s,o,l,u){return O(this,arguments,function*(e,r,i,n,a={}){let f=i.x-r.x,c=new le.default((r.x+i.x)/2,(r.y+i.y)/2);return this.drawCanvas(e,c,It(f,n),a)})}};var di=U(ee());var na=t=>new Promise((e,r)=>{setTimeout(()=>{e()},t)}),aa=t=>t.then(e=>({status:"fulfilled",value:e}),e=>({status:"rejected",reason:e})),sa=(t={})=>{class e extends L.GridLayer{constructor(i={}){i.noWrap&&!i.bounds&&(i.bounds=[[-90,-180],[90,180]]),i.attribution==null&&(i.attribution='<a href="https://protomaps.com">Protomaps</a> \xA9 <a href="https://openstreetmap.org/copyright">OpenStreetMap</a>'),super(i);let n=i.dark?_e:ve;this.paint_rules=i.paint_rules||ke(n,i.shade),this.label_rules=i.label_rules||Me(n,i.shade,i.language1,i.language2),this.backgroundColor=i.backgroundColor,this.lastRequestedZ=void 0,this.xray=i.xray,this.tasks=i.tasks||[],this.views=Ie(i),this.debug=i.debug;let a=document.createElement("canvas").getContext("2d");this.scratch=a,this.onTilesInvalidated=s=>{s.forEach(o=>{this.rerenderTile(o)})},this.labelers=new Ye(this.scratch,this.label_rules,16,this.onTilesInvalidated),this.tile_size=256*window.devicePixelRatio,this.tileDelay=i.tileDelay||3,this.lang=i.lang,this.inspector=this.inspect(this)}setDefaultStyle(i,n,a,s){let o=i?_e:ve;this.paint_rules=ke(o,n),this.label_rules=Me(o,n,a,s)}renderTile(i,n,a,s=()=>{}){return O(this,null,function*(){this.lastRequestedZ=i.z;let o=[];for(let[_,S]of this.views){let F=S.getDisplayTile(i);o.push({key:_,promise:F})}let l=yield Promise.all(o.map(_=>_.promise.then(S=>({status:"fulfilled",value:S,key:_.key}),S=>({status:"rejected",reason:S,key:_.key})))),u=new Map;for(let _ of l)_.status==="fulfilled"?u.set(_.key,[_.value]):_.reason.name==="AbortError"||console.error(_.reason);if(n.key!=a||this.lastRequestedZ!==i.z||(yield Promise.all(this.tasks.map(aa)),n.key!=a)||this.lastRequestedZ!==i.z)return;let f=this.labelers.add(i.z,u);if(n.key!=a||this.lastRequestedZ!==i.z)return;let c=this.labelers.getIndex(i.z);if(!this._map)return;let d=this._map.getCenter().wrap(),g=this._getTiledPixelBounds(d),w=this._pxBoundsToTileRange(g),z=w.getCenter(),h=i.distanceTo(z)*this.tileDelay;if(yield na(h),n.key!=a||this.lastRequestedZ!==i.z)return;let m=16,p={minX:256*i.x-m,minY:256*i.y-m,maxX:256*(i.x+1)+m,maxY:256*(i.y+1)+m},b=new di.default(256*i.x,256*i.y);n.width=this.tile_size,n.height=this.tile_size;let x=n.getContext("2d");x.setTransform(this.tile_size/256,0,0,this.tile_size/256,0,0),x.clearRect(0,0,256,256),this.backgroundColor&&(x.save(),x.fillStyle=this.backgroundColor,x.fillRect(0,0,256,256),x.restore());var y=0;let v=this.paint_rules;if(this.xray&&(v=lt(u,this.xray)),y=Xe(x,i.z,u,this.xray?null:c,v,p,b,!1,this.debug),this.debug){x.save(),x.fillStyle=this.debug,x.font="600 12px sans-serif",x.fillText(i.z+" "+i.x+" "+i.y,4,14),x.font="12px sans-serif";let _=28;for(let[S,F]of u){let P=F[0].data_tile;x.fillText(S+(S?" ":"")+P.z+" "+P.x+" "+P.y,4,_),_+=14}x.font="600 10px sans-serif",y>8&&(x.fillText(y.toFixed()+" ms paint",4,_),_+=14),f>8&&x.fillText(f.toFixed()+" ms layout",4,_),x.strokeStyle=this.debug,x.lineWidth=.5,x.beginPath(),x.moveTo(0,0),x.lineTo(0,256),x.stroke(),x.lineWidth=.5,x.beginPath(),x.moveTo(0,0),x.lineTo(256,0),x.stroke(),x.restore()}s()})}rerenderTile(i){for(let n in this._tiles){let a=this._wrapCoords(this._keyToTileCoords(n));i===this._tileCoordsToKey(a)&&this.renderTile(a,this._tiles[n].el,i)}}clearLayout(){this.labelers=new Ye(this.scratch,this.label_rules,16,this.onTilesInvalidated)}rerenderTiles(){for(let i in this._tiles){let n=this._wrapCoords(this._keyToTileCoords(i)),a=this._tileCoordsToKey(n);this.renderTile(n,this._tiles[i].el,a)}}createTile(i,n){let a=L.DomUtil.create("canvas","leaflet-tile");a.lang=this.lang;let s=this._tileCoordsToKey(i);return a.key=s,this.renderTile(i,a,s,()=>{n(null,a)}),a}_removeTile(i){let n=this._tiles[i];!n||(n.el.removed=!0,n.el.key=void 0,L.DomUtil.removeClass(n.el,"leaflet-tile-loaded"),n.el.width=n.el.height=0,L.DomUtil.remove(n.el),delete this._tiles[i],this.fire("tileunload",{tile:n.el,coords:this._keyToTileCoords(i)}))}queryFeatures(i,n){let a=new Map;for(var[s,o]of this.views)a.set(s,o.queryFeatures(i,n,this._map.getZoom()));return a}inspect(i){return n=>{let a=["\u25CE","\u27CD","\u25FB"],s=i._map.wrapLatLng(n.latlng),o=i.queryFeatures(s.lng,s.lat);var l="";let u=!0;for(var[f,c]of o)for(var d of c)if(!(this.xray&&this.xray!==!0&&!((this.xray.dataSource||"")===f&&this.xray.dataLayer===d.layerName))){l=l+`<div style="margin-top:${u?0:.5}em">${a[d.feature.geomType-1]} <b>${f} ${f?"/":""} ${d.layerName}</b> ${d.feature.id||""}</div>`;for(let g in d.feature.props)l=l+`<div style="font-size:0.9em">${g} = ${d.feature.props[g]}</div>`;u=!1}u&&(l="No features."),L.popup().setLatLng(n.latlng).setContent('<div style="max-height:400px;overflow-y:scroll;padding-right:8px">'+l+"</div>").openOn(i._map)}}addInspector(i){return i.on("click",this.inspector)}removeInspector(i){return i.off("click",this.inspector)}}return new e(t)};function Xt(t){let e=0,r=0;for(let o of t)e+=o.w*o.h,r=Math.max(r,o.w);t.sort((o,l)=>l.h-o.h);let i=Math.max(Math.ceil(Math.sqrt(e/.95)),r),n=[{x:0,y:0,w:i,h:1/0}],a=0,s=0;for(let o of t)for(let l=n.length-1;l>=0;l--){let u=n[l];if(!(o.w>u.w||o.h>u.h)){if(o.x=u.x,o.y=u.y,s=Math.max(s,o.y+o.h),a=Math.max(a,o.x+o.w),o.w===u.w&&o.h===u.h){let f=n.pop();l<n.length&&(n[l]=f)}else o.h===u.h?(u.x+=o.w,u.w-=o.w):o.w===u.w?(u.y+=o.h,u.h-=o.h):(n.push({x:u.x+o.w,y:u.y,w:u.w-o.w,h:o.h}),u.y+=o.h,u.h-=o.h);break}}return{w:a,h:s,fill:e/(a*s)||0}}var oa=(t,e,r)=>{let i=new FontFace(t,"url("+e+")",{weight:r});return document.fonts.add(i),i.load()},mi=t=>O(void 0,null,function*(){return new Promise((e,r)=>{let i=new Image;i.onload=()=>e(i),i.onerror=()=>r("Invalid SVG"),i.src=t})}),la=`
<svg width="20px" height="20px" viewBox="0 0 50 50" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <rect width="50" height="50" fill="#cccccc"/>
    <g transform="translate(5,5)">
        <path fill="none" stroke="#666666" stroke-width="7" d="m11,12a8.5,8 0 1,1 17,0q0,4-4,6t-4.5,4.5-.4,4v.2m0,3v7"/>
    </g>
</svg>
`,pi=class{constructor(e){this.src=e,this.canvas=document.createElement("canvas"),this.mapping=new Map,this.missingBox={x:0,y:0,w:0,h:0}}load(){return O(this,null,function*(){let e=this.src,r=window.devicePixelRatio;e.endsWith(".html")&&(e=yield(yield fetch(e)).text());let i=new window.DOMParser().parseFromString(e,"text/html"),n=Array.from(i.body.children),a=yield mi("data:image/svg+xml;base64,"+btoa(la)),s=[{w:a.width*r,h:a.height*r,img:a,id:""}],o=new XMLSerializer;for(let d of n){var l=btoa(o.serializeToString(d)),u="data:image/svg+xml;base64,"+l;let g=yield mi(u);s.push({w:g.width*r,h:g.height*r,img:g,id:d.id})}let f=Xt(s);this.canvas.width=f.w,this.canvas.height=f.h;let c=this.canvas.getContext("2d");if(c)for(let d of s)d.x!==void 0&&d.y!==void 0&&(c.drawImage(d.img,d.x,d.y,d.w,d.h),d.id?this.mapping.set(d.id,{x:d.x,y:d.y,w:d.w,h:d.h}):this.missingBox={x:d.x,y:d.y,w:d.w,h:d.h});return this})}get(e){let r=this.mapping.get(e);return r||(r=this.missingBox),r}};function ut(t,e){return typeof t=="number"?t:e}function Ke(t){if(t.includes("$type"))return e=>!0;if(t[0]=="==")return(e,r)=>r.props[t[1]]===t[2];if(t[0]=="!=")return(e,r)=>r.props[t[1]]!==t[2];if(t[0]=="!"){let e=Ke(t[1]);return(r,i)=>!e(r,i)}else{if(t[0]==="<")return(e,r)=>ut(r.props[t[1]],1/0)<t[2];if(t[0]==="<=")return(e,r)=>ut(r.props[t[1]],1/0)<=t[2];if(t[0]===">")return(e,r)=>ut(r.props[t[1]],-1/0)>t[2];if(t[0]===">=")return(e,r)=>ut(r.props[t[1]],-1/0)>=t[2];if(t[0]==="in")return(e,r)=>t.slice(2,t.length).includes(r.props[t[1]]);if(t[0]==="!in")return(e,r)=>!t.slice(2,t.length).includes(r.props[t[1]]);if(t[0]==="has")return(e,r)=>r.props.hasOwnProperty(t[1]);if(t[0]==="!has")return(e,r)=>!r.props.hasOwnProperty(t[1]);if(t[0]==="all"){let e=t.slice(1,t.length).map(r=>Ke(r));return(r,i)=>e.every(n=>n(r,i))}else if(t[0]==="any"){let e=t.slice(1,t.length).map(r=>Ke(r));return(r,i)=>e.some(n=>n(r,i))}else return console.log("Unimplemented filter: ",t[0]),e=>!1}}function ht(t){if(t.base&&t.stops)return r=>Y(t.base,t.stops)(r-1);if(t[0]=="interpolate"&&t[1][0]=="exponential"&&t[2]=="zoom"){let r=t.slice(3),i=[];for(var e=0;e<r.length;e+=2)i.push([r[e],r[e+1]]);return n=>Y(t[1][1],i)(n-1)}else if(t[0]=="step"&&t[1][0]=="get"){let r=t.slice(2),i=t[1][1];return(n,a)=>{let s=a==null?void 0:a.props[i];if(typeof s=="number"){if(s<r[1])return r[0];for(e=1;e<r.length;e+=2)if(s<=r[e])return r[e+1]}return r[r.length-1]}}else return console.log("Unimplemented numeric fn: ",t),r=>1}function Vt(t,e=0){return t?typeof t=="number"?t:(r,i)=>i?ht(t)(r,i):e:e}function Yt(t,e){let r=Vt(t,1),i=Vt(e);return(n,a)=>{let s=typeof r=="number"?r:r(n,a);return i?s+(typeof i=="number"?i:i(n,a)):s}}function Ut(t,e){let r=[];for(let o of t["text-font"])e.hasOwnProperty(o)&&r.push(e[o]);r.length===0&&r.push({face:"sans-serif"});let i=t["text-size"];var n="";r.length&&r[0].weight&&(n=r[0].weight+" ");var a="";if(r.length&&r[0].style&&(a=r[0].style+" "),typeof i=="number")return o=>`${a}${n}${i}px ${r.map(l=>l.face).join(", ")}`;if(i.stops){var s=1.4;i.base?s=i.base:i.base=s;let o=ht(i);return(l,u)=>`${a}${n}${o(l,u)}px ${r.map(f=>f.face).join(", ")}`}else if(i[0]=="step"){let o=ht(i);return(l,u)=>`${a}${n}${o(l,u)}px ${r.map(f=>f.face).join(", ")}`}else return console.log("Can't parse font: ",t),o=>"12px sans-serif"}function ua(t,e){let r=[],i=[],n=new Map;for(var a of t.layers){if(n.set(a.id,a),a.layout&&a.layout.visibility=="none")continue;if(a.ref){let u=n.get(a.ref);a.type=u.type,a.filter=u.filter,a.source=u.source,a["source-layer"]=u["source-layer"]}let l=a["source-layer"];var s,o=void 0;a.filter&&(o=Ke(a.filter)),a.type=="fill"?r.push({dataLayer:a["source-layer"],filter:o,symbolizer:new A({fill:a.paint["fill-color"],opacity:a.paint["fill-opacity"]})}):a.type=="fill-extrusion"?r.push({dataLayer:a["source-layer"],filter:o,symbolizer:new A({fill:a.paint["fill-extrusion-color"],opacity:a.paint["fill-extrusion-opacity"]})}):a.type=="line"?a.paint["line-dasharray"]?r.push({dataLayer:a["source-layer"],filter:o,symbolizer:new R({width:Yt(a.paint["line-width"],a.paint["line-gap-width"]),dash:a.paint["line-dasharray"],dashColor:a.paint["line-color"]})}):r.push({dataLayer:a["source-layer"],filter:o,symbolizer:new R({color:a.paint["line-color"],width:Yt(a.paint["line-width"],a.paint["line-gap-width"])})}):a.type=="symbol"?a.layout["symbol-placement"]=="line"?i.push({dataLayer:a["source-layer"],filter:o,symbolizer:new Le({font:Ut(a.layout,e),fill:a.paint["text-color"],width:a.paint["text-halo-width"],stroke:a.paint["text-halo-color"],textTransform:a.layout["text-transform"],label_props:a.layout["text-field"]?[a.layout["text-field"]]:void 0})}):i.push({dataLayer:a["source-layer"],filter:o,symbolizer:new te({font:Ut(a.layout,e),fill:a.paint["text-color"],stroke:a.paint["text-halo-color"],width:a.paint["text-halo-width"],textTransform:a.layout["text-transform"],label_props:a.layout["text-field"]?[a.layout["text-field"]]:void 0})}):a.type=="circle"&&r.push({dataLayer:a["source-layer"],filter:o,symbolizer:new oe({radius:a.paint["circle-radius"],fill:a.paint["circle-color"],stroke:a.paint["circle-stroke-color"],width:a.paint["circle-stroke-width"]})})}return i.reverse(),{paint_rules:r,label_rules:i,tasks:[]}}return ha;})();
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */