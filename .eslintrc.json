{
  "root": true,
  "env": {
    "es6": true,
    "node": false,
    "browser": true
  },
  "parser": "babel-eslint",
  "parserOptions": {
    "sourceType": "module"
  },
  "plugins": ["html", "prettier"],
  "extends": ["eslint:recommended", "plugin:prettier/recommended"],
  "globals": {
    "defineProps": "readonly",
    "defineEmits": "readonly",
    "defineExpose": "readonly",
    "withDefaults": "readonly",

    "currentPath": "readonly",
    "globalMsg": "readonly",
    "globalAlert": "readonly",
    "globalNotify": "readonly",
    "showLoading": "readonly",
    "hideLoading": "readonly",

    "Cesium": "readonly",
    "mars3d": "readonly",
    "es5widget": "readonly",
    "L": "readonly",
    "mars2d": "readonly",

    "localforage": "readonly",
    "turf": "readonly",
    "THREE": "readonly",
    "Ammo": "readonly",
    "echarts": "readonly",
    "Terraformer": "readonly",
    "AMap": "readonly",
    "BMap": "readonly",
    "kgUtil": "readonly",
    "shpUtil": "readonly",
    "netcdfjs": "readonly",
    "ol": "readonly",
    "olcs": "readonly",
    "mapv": "readonly",

    "map": "readonly",
    "$": false,
    "haoutil": false,
    "toastr": false,
    "layer": false,
    "JB": false,
    "jeDate": false,
    "kriging": false
  },
  "rules": {
    // mars3d-es5-example项目专用的配置(目的便于示例的开发调试) start
    "no-unused-vars": "off", // 不允许有声明后未被使用的变量或参数
    "prefer-regex-literals": "off", // 关闭只能使用正则字面量
    "comma-dangle": "off", // 强制在对象和数组文字中一致地使用尾随逗号
    "eqeqeq": "off", // 是否使用 === 替代 ==
    "no-var": "off", // @fixable 禁止使用 var
    "no-undef": "off",
    // mars3d-es5-example项目专用的配置(目的便于示例的开发调试) end

    "camelcase": "off", // 强制驼峰命名规则
    "indent": "off", // 强制一致的缩进风格
    "quotes": ["error", "double", { "avoidEscape": true, "allowTemplateLiterals": true }], // 使用双引号
    "curly": ["error", "all"], // 强制所有控制语句使用一致的括号风格  @fixable if 后面必须要有 {，除非是单行 if

    "padded-blocks": "off", // 块内行首行尾是否空行
    "global-require": "off", // require 必须在全局作用域下
    "valid-jsdoc": "off", // 强制使用有效的 JSDoc 注释
    "consistent-return": "off", // 要求 return 语句要么总是指定返回的值，要么不指定
    "default-case": "off", // switch 语句强制 default 分支，也可添加 // no default 注释取消此次警告
    "block-scoped-var": "error", // 将 var 定义的变量视为块作用域，禁止在块外使用
    "guard-for-in": "off", // for in 内部必须有 hasOwnProperty
    "constructor-super": "error", // constructor 中必须有 super
    "dot-location": ["off", "property"], // @fixable 链式调用的时候，点号必须放在第二行开头处，禁止放在第一行结尾处
    "comma-style": ["error", "last"], // 控制逗号在行尾出现还是在行首出现 (默认行尾)
    "space-before-function-paren": ["off", "always"], // 函数定义时括号前的空格
    "object-curly-newline": ["error", { "multiline": true, "consistent": true }], // @fixable 大括号内的首尾必须有换行
    "object-shorthand": "off", // position: position是否简写为position
    "computed-property-spacing": ["error", "never"], // "SwitchCase" (默认：0) 强制 switch 语句中的 case 子句的缩进水平

    "no-debugger": "warn", // 是否允许debugger
    "no-console": "off", // 不允许出现console语句
    "no-trailing-spaces": "off", // 一行最后不允许有空格
    "no-prototype-builtins": "off", // 是否允许使用Object.prototype
    "no-template-curly-in-string": "off", // 关闭${xxx}检测
    "no-mixed-spaces-and-tabs": "error", // 不允许混用tab和空格
    "no-new": "error", // 禁止在非赋值或条件语句中使用 new 操作符
    "no-new-wrappers": "error", // 禁止对 String，Number 和 Boolean 使用 new 操作符
    "no-self-assign": "error", // 禁止自我赋值
    "no-self-compare": "error", // 禁止自身比较
    "no-case-declarations": "error", // switch 的 case 内有变量定义的时候，必须使用大括号将 case 内变成一个代码块
    "no-extend-native": "error", // 禁止修改原生对象
    "no-extra-bind": "error", // @fixable 禁止出现没必要的 bind
    "no-extra-label": "error", // @fixable 禁止出现没必要的 label
    "no-fallthrough": "error", // switch 的 case 内必须有 break, return 或 throw
    "no-floating-decimal": "error", // @fixable 表示小数时，禁止省略 0，比如 .5
    "no-global-assign": "error", // 禁止对全局变量赋值
    "no-multi-str": "error", // 禁止使用 \ 来换行字符串
    "no-const-assign": "error", // 禁止对使用 const 定义的常量重新赋值
    "no-dupe-class-members": "error", // 禁止重复定义类
    "no-duplicate-imports": "off", // 禁止重复 import 模块
    "no-useless-constructor": "off", // 禁止出现没必要的 constructor，比如 constructor(value) { super(value) }
    "no-useless-escape": "off", // 可以使用\转义
    "no-callback-literal": "off", // 关闭eslint标准模式callback回调报错
    "no-dupe-keys": "error", // 禁止对象字面量中出现重复的 key
    "no-func-assign": "error", // 禁止对 function 声明重新赋值
    "no-nested-ternary": "error", // 禁用嵌套的三元表达式
    "no-multiple-empty-lines": ["error", { "max": 4 }], // 空行最多不能超过max行
    "new-cap": "off" // 关闭eslint fromDegrees方法报错
  }
}
