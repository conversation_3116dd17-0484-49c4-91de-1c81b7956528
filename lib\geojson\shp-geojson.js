!function(t,s){"object"==typeof exports&&"undefined"!=typeof module?s(exports):"function"==typeof define&&define.amd?define(["exports"],s):s((t="undefined"!=typeof globalThis?globalThis:t||self).shpUtil={})}(this,(function(t){"use strict";var s=6378137,i=.0066943799901413165,a=484813681109536e-20,e=Math.PI/2,h=1e-10,n=.017453292519943295,r=57.29577951308232,o=Math.PI/4,l=2*Math.PI,u=3.14159265359,c={greenwich:0,lisbon:-9.131906111111,paris:2.337229166667,bogota:-74.080916666667,madrid:-3.687938888889,rome:12.452333333333,bern:7.439583333333,jakarta:106.807719444444,ferro:-17.666666666667,brussels:4.367975,stockholm:18.058277777778,athens:23.7163375,oslo:10.722916666667},f={mm:{to_meter:.001},cm:{to_meter:.01},ft:{to_meter:.3048},"us-ft":{to_meter:1200/3937},fath:{to_meter:1.8288},kmi:{to_meter:1852},"us-ch":{to_meter:20.1168402336805},"us-mi":{to_meter:1609.34721869444},km:{to_meter:1e3},"ind-ft":{to_meter:.30479841},"ind-yd":{to_meter:.91439523},mi:{to_meter:1609.344},yd:{to_meter:.9144},ch:{to_meter:20.1168},link:{to_meter:.201168},dm:{to_meter:.01},in:{to_meter:.0254},"ind-ch":{to_meter:20.11669506},"us-in":{to_meter:.025400050800101},"us-yd":{to_meter:.914401828803658}},M=/[\s_\-\/\(\)]/g;function p(t,s){if(t[s])return t[s];for(var i,a=Object.keys(t),e=s.toLowerCase().replace(M,""),h=-1;++h<a.length;)if((i=a[h]).toLowerCase().replace(M,"")===e)return t[i]}function d(t){var s,i,a,e={},h=t.split("+").map((function(t){return t.trim()})).filter((function(t){return t})).reduce((function(t,s){var i=s.split("=");return i.push(!0),t[i[0].toLowerCase()]=i[1],t}),{}),r={proj:"projName",datum:"datumCode",rf:function(t){e.rf=parseFloat(t)},lat_0:function(t){e.lat0=t*n},lat_1:function(t){e.lat1=t*n},lat_2:function(t){e.lat2=t*n},lat_ts:function(t){e.lat_ts=t*n},lon_0:function(t){e.long0=t*n},lon_1:function(t){e.long1=t*n},lon_2:function(t){e.long2=t*n},alpha:function(t){e.alpha=parseFloat(t)*n},gamma:function(t){e.rectified_grid_angle=parseFloat(t)},lonc:function(t){e.longc=t*n},x_0:function(t){e.x0=parseFloat(t)},y_0:function(t){e.y0=parseFloat(t)},k_0:function(t){e.k0=parseFloat(t)},k:function(t){e.k0=parseFloat(t)},a:function(t){e.a=parseFloat(t)},b:function(t){e.b=parseFloat(t)},r:function(t){e.a=e.b=parseFloat(t)},r_a:function(){e.R_A=!0},zone:function(t){e.zone=parseInt(t,10)},south:function(){e.utmSouth=!0},towgs84:function(t){e.datum_params=t.split(",").map((function(t){return parseFloat(t)}))},to_meter:function(t){e.to_meter=parseFloat(t)},units:function(t){e.units=t;var s=p(f,t);s&&(e.to_meter=s.to_meter)},from_greenwich:function(t){e.from_greenwich=t*n},pm:function(t){var s=p(c,t);e.from_greenwich=(s||parseFloat(t))*n},nadgrids:function(t){"@null"===t?e.datumCode="none":e.nadgrids=t},axis:function(t){var s="ewnsud";3===t.length&&-1!==s.indexOf(t.substr(0,1))&&-1!==s.indexOf(t.substr(1,1))&&-1!==s.indexOf(t.substr(2,1))&&(e.axis=t)},approx:function(){e.approx=!0}};for(s in h)i=h[s],s in r?"function"==typeof(a=r[s])?a(i):e[a]=i:e[s]=i;return"string"==typeof e.datumCode&&"WGS84"!==e.datumCode&&(e.datumCode=e.datumCode.toLowerCase()),e}var _=/\s/,m=/[A-Za-z]/,y=/[A-Za-z84_]/,x=/[,\]]/,g=/[\d\.E\-\+]/;function v(t){if("string"!=typeof t)throw new Error("not a string");this.text=t.trim(),this.level=0,this.place=0,this.root=null,this.stack=[],this.currentObject=null,this.state=1}function b(t,s,i){Array.isArray(s)&&(i.unshift(s),s=null);var a=s?{}:t,e=i.reduce((function(t,s){return w(s,t),t}),a);s&&(t[s]=e)}function w(t,s){if(Array.isArray(t)){var i=t.shift();if("PARAMETER"===i&&(i=t.shift()),1===t.length)return Array.isArray(t[0])?(s[i]={},void w(t[0],s[i])):void(s[i]=t[0]);if(t.length)if("TOWGS84"!==i){if("AXIS"===i)return i in s||(s[i]=[]),void s[i].push(t);var a;switch(Array.isArray(i)||(s[i]={}),i){case"UNIT":case"PRIMEM":case"VERT_DATUM":return s[i]={name:t[0].toLowerCase(),convert:t[1]},void(3===t.length&&w(t[2],s[i]));case"SPHEROID":case"ELLIPSOID":return s[i]={name:t[0],a:t[1],rf:t[2]},void(4===t.length&&w(t[3],s[i]));case"EDATUM":case"ENGINEERINGDATUM":case"LOCAL_DATUM":case"DATUM":case"VERT_CS":case"VERTCRS":case"VERTICALCRS":return t[0]=["name",t[0]],void b(s,i,t);case"COMPD_CS":case"COMPOUNDCRS":case"FITTED_CS":case"PROJECTEDCRS":case"PROJCRS":case"GEOGCS":case"GEOCCS":case"PROJCS":case"LOCAL_CS":case"GEODCRS":case"GEODETICCRS":case"GEODETICDATUM":case"ENGCRS":case"ENGINEERINGCRS":return t[0]=["name",t[0]],b(s,i,t),void(s[i].type=i);default:for(a=-1;++a<t.length;)if(!Array.isArray(t[a]))return w(t,s[i]);return b(s,i,t)}}else s[i]=t;else s[i]=!0}else s[t]=!0}v.prototype.readCharicter=function(){var t=this.text[this.place++];if(4!==this.state)for(;_.test(t);){if(this.place>=this.text.length)return;t=this.text[this.place++]}switch(this.state){case 1:return this.neutral(t);case 2:return this.keyword(t);case 4:return this.quoted(t);case 5:return this.afterquote(t);case 3:return this.number(t);case-1:return}},v.prototype.afterquote=function(t){if('"'===t)return this.word+='"',void(this.state=4);if(x.test(t))return this.word=this.word.trim(),void this.afterItem(t);throw new Error("havn't handled \""+t+'" in afterquote yet, index '+this.place)},v.prototype.afterItem=function(t){return","===t?(null!==this.word&&this.currentObject.push(this.word),this.word=null,void(this.state=1)):"]"===t?(this.level--,null!==this.word&&(this.currentObject.push(this.word),this.word=null),this.state=1,this.currentObject=this.stack.pop(),void(this.currentObject||(this.state=-1))):void 0},v.prototype.number=function(t){if(!g.test(t)){if(x.test(t))return this.word=parseFloat(this.word),void this.afterItem(t);throw new Error("havn't handled \""+t+'" in number yet, index '+this.place)}this.word+=t},v.prototype.quoted=function(t){'"'!==t?this.word+=t:this.state=5},v.prototype.keyword=function(t){if(y.test(t))this.word+=t;else{if("["===t){var s=[];return s.push(this.word),this.level++,null===this.root?this.root=s:this.currentObject.push(s),this.stack.push(this.currentObject),this.currentObject=s,void(this.state=1)}if(!x.test(t))throw new Error("havn't handled \""+t+'" in keyword yet, index '+this.place);this.afterItem(t)}},v.prototype.neutral=function(t){if(m.test(t))return this.word=t,void(this.state=2);if('"'===t)return this.word="",void(this.state=4);if(g.test(t))return this.word=t,void(this.state=3);if(!x.test(t))throw new Error("havn't handled \""+t+'" in neutral yet, index '+this.place);this.afterItem(t)},v.prototype.output=function(){for(;this.place<this.text.length;)this.readCharicter();if(-1===this.state)return this.root;throw new Error('unable to parse string "'+this.text+'". State is '+this.state)};var S=["PROJECTEDCRS","PROJCRS","GEOGCS","GEOCCS","PROJCS","LOCAL_CS","GEODCRS","GEODETICCRS","GEODETICDATUM","ENGCRS","ENGINEERINGCRS"];function P(t){return.017453292519943295*t}function E(t){for(var s=Object.keys(t),i=0,a=s.length;i<a;++i){var e=s[i];-1!==S.indexOf(e)&&G(t[e]),"object"==typeof t[e]&&E(t[e])}}function G(t){if(t.AUTHORITY){var s=Object.keys(t.AUTHORITY)[0];s&&s in t.AUTHORITY&&(t.title=s+":"+t.AUTHORITY[s])}if("GEOGCS"===t.type?t.projName="longlat":"LOCAL_CS"===t.type?(t.projName="identity",t.local=!0):"object"==typeof t.PROJECTION?t.projName=Object.keys(t.PROJECTION)[0]:t.projName=t.PROJECTION,t.AXIS){for(var i="",a=0,e=t.AXIS.length;a<e;++a){var h=[t.AXIS[a][0].toLowerCase(),t.AXIS[a][1].toLowerCase()];-1!==h[0].indexOf("north")||("y"===h[0]||"lat"===h[0])&&"north"===h[1]?i+="n":-1!==h[0].indexOf("south")||("y"===h[0]||"lat"===h[0])&&"south"===h[1]?i+="s":-1!==h[0].indexOf("east")||("x"===h[0]||"lon"===h[0])&&"east"===h[1]?i+="e":-1===h[0].indexOf("west")&&("x"!==h[0]&&"lon"!==h[0]||"west"!==h[1])||(i+="w")}2===i.length&&(i+="u"),3===i.length&&(t.axis=i)}t.UNIT&&(t.units=t.UNIT.name.toLowerCase(),"metre"===t.units&&(t.units="meter"),t.UNIT.convert&&("GEOGCS"===t.type?t.DATUM&&t.DATUM.SPHEROID&&(t.to_meter=t.UNIT.convert*t.DATUM.SPHEROID.a):t.to_meter=t.UNIT.convert));var n=t.GEOGCS;function r(s){return s*(t.to_meter||1)}"GEOGCS"===t.type&&(n=t),n&&(n.DATUM?t.datumCode=n.DATUM.name.toLowerCase():t.datumCode=n.name.toLowerCase(),"d_"===t.datumCode.slice(0,2)&&(t.datumCode=t.datumCode.slice(2)),"new_zealand_1949"===t.datumCode&&(t.datumCode="nzgd49"),"wgs_1984"!==t.datumCode&&"world_geodetic_system_1984"!==t.datumCode||("Mercator_Auxiliary_Sphere"===t.PROJECTION&&(t.sphere=!0),t.datumCode="wgs84"),"belge_1972"===t.datumCode&&(t.datumCode="rnb72"),n.DATUM&&n.DATUM.SPHEROID&&(t.ellps=n.DATUM.SPHEROID.name.replace("_19","").replace(/[Cc]larke\_18/,"clrk"),"international"===t.ellps.toLowerCase().slice(0,13)&&(t.ellps="intl"),t.a=n.DATUM.SPHEROID.a,t.rf=parseFloat(n.DATUM.SPHEROID.rf,10)),n.DATUM&&n.DATUM.TOWGS84&&(t.datum_params=n.DATUM.TOWGS84),~t.datumCode.indexOf("osgb_1936")&&(t.datumCode="osgb36"),~t.datumCode.indexOf("osni_1952")&&(t.datumCode="osni52"),(~t.datumCode.indexOf("tm65")||~t.datumCode.indexOf("geodetic_datum_of_1965"))&&(t.datumCode="ire65"),"ch1903+"===t.datumCode&&(t.datumCode="ch1903"),~t.datumCode.indexOf("israel")&&(t.datumCode="isr93")),t.b&&!isFinite(t.b)&&(t.b=t.a);[["standard_parallel_1","Standard_Parallel_1"],["standard_parallel_1","Latitude of 1st standard parallel"],["standard_parallel_2","Standard_Parallel_2"],["standard_parallel_2","Latitude of 2nd standard parallel"],["false_easting","False_Easting"],["false_easting","False easting"],["false-easting","Easting at false origin"],["false_northing","False_Northing"],["false_northing","False northing"],["false_northing","Northing at false origin"],["central_meridian","Central_Meridian"],["central_meridian","Longitude of natural origin"],["central_meridian","Longitude of false origin"],["latitude_of_origin","Latitude_Of_Origin"],["latitude_of_origin","Central_Parallel"],["latitude_of_origin","Latitude of natural origin"],["latitude_of_origin","Latitude of false origin"],["scale_factor","Scale_Factor"],["k0","scale_factor"],["latitude_of_center","Latitude_Of_Center"],["latitude_of_center","Latitude_of_center"],["lat0","latitude_of_center",P],["longitude_of_center","Longitude_Of_Center"],["longitude_of_center","Longitude_of_center"],["longc","longitude_of_center",P],["x0","false_easting",r],["y0","false_northing",r],["long0","central_meridian",P],["lat0","latitude_of_origin",P],["lat0","standard_parallel_1",P],["lat1","standard_parallel_1",P],["lat2","standard_parallel_2",P],["azimuth","Azimuth"],["alpha","azimuth",P],["srsCode","name"]].forEach((function(s){return function(t,s){var i=s[0],a=s[1];!(i in t)&&a in t&&(t[i]=t[a],3===s.length&&(t[i]=s[2](t[i])))}(t,s)})),t.long0||!t.longc||"Albers_Conic_Equal_Area"!==t.projName&&"Lambert_Azimuthal_Equal_Area"!==t.projName||(t.long0=t.longc),t.lat_ts||!t.lat1||"Stereographic_South_Pole"!==t.projName&&"Polar Stereographic (variant B)"!==t.projName?!t.lat_ts&&t.lat0&&"Polar_Stereographic"===t.projName&&(t.lat_ts=t.lat0,t.lat0=P(t.lat0>0?90:-90)):(t.lat0=P(t.lat1>0?90:-90),t.lat_ts=t.lat1)}function C(t){var s=new v(t).output(),i=s[0],a={};return w(s,a),E(a),a[i]}function N(t){var s=this;if(2===arguments.length){var i=arguments[1];"string"==typeof i?"+"===i.charAt(0)?N[t]=d(arguments[1]):N[t]=C(arguments[1]):N[t]=i}else if(1===arguments.length){if(Array.isArray(t))return t.map((function(t){Array.isArray(t)?N.apply(s,t):N(t)}));if("string"==typeof t){if(t in N)return N[t]}else"EPSG"in t?N["EPSG:"+t.EPSG]=t:"ESRI"in t?N["ESRI:"+t.ESRI]=t:"IAU2000"in t?N["IAU2000:"+t.IAU2000]=t:console.log(t);return}}!function(t){t("EPSG:4326","+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees"),t("EPSG:4269","+title=NAD83 (long/lat) +proj=longlat +a=6378137.0 +b=6356752.31414036 +ellps=GRS80 +datum=NAD83 +units=degrees"),t("EPSG:3857","+title=WGS 84 / Pseudo-Mercator +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs");for(var s=1;s<=60;++s)t("EPSG:"+(32600+s),"+proj=utm +zone="+s+" +datum=WGS84 +units=m"),t("EPSG:"+(32700+s),"+proj=utm +zone="+s+" +south +datum=WGS84 +units=m");t.WGS84=t["EPSG:4326"],t["EPSG:3785"]=t["EPSG:3857"],t.GOOGLE=t["EPSG:3857"],t["EPSG:900913"]=t["EPSG:3857"],t["EPSG:102113"]=t["EPSG:3857"]}(N);var k=["PROJECTEDCRS","PROJCRS","GEOGCS","GEOCCS","PROJCS","LOCAL_CS","GEODCRS","GEODETICCRS","GEODETICDATUM","ENGCRS","ENGINEERINGCRS"];var j=["3857","900913","3785","102113"];function I(t){if(!function(t){return"string"==typeof t}(t))return t;if(function(t){return t in N}(t))return N[t];if(function(t){return k.some((function(s){return t.indexOf(s)>-1}))}(t)){var s=C(t);if(function(t){var s=p(t,"authority");if(s){var i=p(s,"epsg");return i&&j.indexOf(i)>-1}}(s))return N["EPSG:3857"];var i=function(t){var s=p(t,"extension");if(s)return p(s,"proj4")}(s);return i?d(i):s}return function(t){return"+"===t[0]}(t)?d(t):void 0}function O(t,s){var i,a;if(t=t||{},!s)return t;for(a in s)void 0!==(i=s[a])&&(t[a]=i);return t}function A(t,s,i){var a=t*s;return i/Math.sqrt(1-a*a)}function R(t){return t<0?-1:1}function q(t){return Math.abs(t)<=u?t:t-R(t)*l}function L(t,s,i){var a=t*i,h=.5*t;return a=Math.pow((1-a)/(1+a),h),Math.tan(.5*(e-s))/a}function T(t,s){for(var i,a,h=.5*t,n=e-2*Math.atan(s),r=0;r<=15;r++)if(i=t*Math.sin(n),n+=a=e-2*Math.atan(s*Math.pow((1-i)/(1+i),h))-n,Math.abs(a)<=1e-10)return n;return-9999}var z={init:function(){var t=this.b/this.a;this.es=1-t*t,"x0"in this||(this.x0=0),"y0"in this||(this.y0=0),this.e=Math.sqrt(this.es),this.lat_ts?this.sphere?this.k0=Math.cos(this.lat_ts):this.k0=A(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)):this.k0||(this.k?this.k0=this.k:this.k0=1)},forward:function(t){var s,i,a=t.x,n=t.y;if(n*r>90&&n*r<-90&&a*r>180&&a*r<-180)return null;if(Math.abs(Math.abs(n)-e)<=h)return null;if(this.sphere)s=this.x0+this.a*this.k0*q(a-this.long0),i=this.y0+this.a*this.k0*Math.log(Math.tan(o+.5*n));else{var l=Math.sin(n),u=L(this.e,n,l);s=this.x0+this.a*this.k0*q(a-this.long0),i=this.y0-this.a*this.k0*Math.log(u)}return t.x=s,t.y=i,t},inverse:function(t){var s,i,a=t.x-this.x0,h=t.y-this.y0;if(this.sphere)i=e-2*Math.atan(Math.exp(-h/(this.a*this.k0)));else{var n=Math.exp(-h/(this.a*this.k0));if(-9999===(i=T(this.e,n)))return null}return s=q(this.long0+a/(this.a*this.k0)),t.x=s,t.y=i,t},names:["Mercator","Popular Visualisation Pseudo Mercator","Mercator_1SP","Mercator_Auxiliary_Sphere","merc"]};function B(t){return t}var D=[z,{init:function(){},forward:B,inverse:B,names:["longlat","identity"]}],F={},U=[];function Q(t,s){var i=U.length;return t.names?(U[i]=t,t.names.forEach((function(t){F[t.toLowerCase()]=i})),this):(console.log(s),!0)}var W={start:function(){D.forEach(Q)},add:Q,get:function(t){if(!t)return!1;var s=t.toLowerCase();return void 0!==F[s]&&U[F[s]]?U[F[s]]:void 0}},H={MERIT:{a:6378137,rf:298.257,ellipseName:"MERIT 1983"},SGS85:{a:6378136,rf:298.257,ellipseName:"Soviet Geodetic System 85"},GRS80:{a:6378137,rf:298.*********,ellipseName:"GRS 1980(IUGG, 1980)"},IAU76:{a:6378140,rf:298.257,ellipseName:"IAU 1976"},airy:{a:6377563.396,b:6356256.91,ellipseName:"Airy 1830"},APL4:{a:6378137,rf:298.25,ellipseName:"Appl. Physics. 1965"},NWL9D:{a:6378145,rf:298.25,ellipseName:"Naval Weapons Lab., 1965"},mod_airy:{a:6377340.189,b:6356034.446,ellipseName:"Modified Airy"},andrae:{a:6377104.43,rf:300,ellipseName:"Andrae 1876 (Den., Iclnd.)"},aust_SA:{a:6378160,rf:298.25,ellipseName:"Australian Natl & S. Amer. 1969"},GRS67:{a:6378160,rf:298.*********,ellipseName:"GRS 67(IUGG 1967)"},bessel:{a:6377397.155,rf:299.1528128,ellipseName:"Bessel 1841"},bess_nam:{a:6377483.865,rf:299.1528128,ellipseName:"Bessel 1841 (Namibia)"},clrk66:{a:6378206.4,b:6356583.8,ellipseName:"Clarke 1866"},clrk80:{a:6378249.145,rf:293.4663,ellipseName:"Clarke 1880 mod."},clrk80ign:{a:6378249.2,b:6356515,rf:293.4660213,ellipseName:"Clarke 1880 (IGN)"},clrk58:{a:6378293.*********,rf:294.2606763692654,ellipseName:"Clarke 1858"},CPM:{a:6375738.7,rf:334.29,ellipseName:"Comm. des Poids et Mesures 1799"},delmbr:{a:6376428,rf:311.5,ellipseName:"Delambre 1810 (Belgium)"},engelis:{a:6378136.05,rf:298.2566,ellipseName:"Engelis 1985"},evrst30:{a:6377276.345,rf:300.8017,ellipseName:"Everest 1830"},evrst48:{a:6377304.063,rf:300.8017,ellipseName:"Everest 1948"},evrst56:{a:6377301.243,rf:300.8017,ellipseName:"Everest 1956"},evrst69:{a:6377295.664,rf:300.8017,ellipseName:"Everest 1969"},evrstSS:{a:6377298.556,rf:300.8017,ellipseName:"Everest (Sabah & Sarawak)"},fschr60:{a:6378166,rf:298.3,ellipseName:"Fischer (Mercury Datum) 1960"},fschr60m:{a:6378155,rf:298.3,ellipseName:"Fischer 1960"},fschr68:{a:6378150,rf:298.3,ellipseName:"Fischer 1968"},helmert:{a:6378200,rf:298.3,ellipseName:"Helmert 1906"},hough:{a:6378270,rf:297,ellipseName:"Hough"},intl:{a:6378388,rf:297,ellipseName:"International 1909 (Hayford)"},kaula:{a:6378163,rf:298.24,ellipseName:"Kaula 1961"},lerch:{a:6378139,rf:298.257,ellipseName:"Lerch 1979"},mprts:{a:6397300,rf:191,ellipseName:"Maupertius 1738"},new_intl:{a:6378157.5,b:6356772.2,ellipseName:"New International 1967"},plessis:{a:6376523,rf:6355863,ellipseName:"Plessis 1817 (France)"},krass:{a:6378245,rf:298.3,ellipseName:"Krassovsky, 1942"},SEasia:{a:6378155,b:6356773.3205,ellipseName:"Southeast Asia"},walbeck:{a:6376896,b:6355834.8467,ellipseName:"Walbeck"},WGS60:{a:6378165,rf:298.3,ellipseName:"WGS 60"},WGS66:{a:6378145,rf:298.25,ellipseName:"WGS 66"},WGS7:{a:6378135,rf:298.26,ellipseName:"WGS 72"}},Z=H.WGS84={a:6378137,rf:298.257223563,ellipseName:"WGS 84"};H.sphere={a:6370997,b:6370997,ellipseName:"Normal Sphere (r=6370997)"};var J={wgs84:{towgs84:"0,0,0",ellipse:"WGS84",datumName:"WGS84"},ch1903:{towgs84:"674.374,15.056,405.346",ellipse:"bessel",datumName:"swiss"},ggrs87:{towgs84:"-199.87,74.79,246.62",ellipse:"GRS80",datumName:"Greek_Geodetic_Reference_System_1987"},nad83:{towgs84:"0,0,0",ellipse:"GRS80",datumName:"North_American_Datum_1983"},nad27:{nadgrids:"@conus,@alaska,@ntv2_0.gsb,@ntv1_can.dat",ellipse:"clrk66",datumName:"North_American_Datum_1927"},potsdam:{towgs84:"598.1,73.7,418.2,0.202,0.045,-2.455,6.7",ellipse:"bessel",datumName:"Potsdam Rauenberg 1950 DHDN"},carthage:{towgs84:"-263.0,6.0,431.0",ellipse:"clark80",datumName:"Carthage 1934 Tunisia"},hermannskogel:{towgs84:"577.326,90.129,463.919,5.137,1.474,5.297,2.4232",ellipse:"bessel",datumName:"Hermannskogel"},mgi:{towgs84:"577.326,90.129,463.919,5.137,1.474,5.297,2.4232",ellipse:"bessel",datumName:"Militar-Geographische Institut"},osni52:{towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"airy",datumName:"Irish National"},ire65:{towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"mod_airy",datumName:"Ireland 1965"},rassadiran:{towgs84:"-133.63,-157.5,-158.62",ellipse:"intl",datumName:"Rassadiran"},nzgd49:{towgs84:"59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993",ellipse:"intl",datumName:"New Zealand Geodetic Datum 1949"},osgb36:{towgs84:"446.448,-125.157,542.060,0.1502,0.2470,0.8421,-20.4894",ellipse:"airy",datumName:"Ordnance Survey of Great Britain 1936"},s_jtsk:{towgs84:"589,76,480",ellipse:"bessel",datumName:"S-JTSK (Ferro)"},beduaram:{towgs84:"-106,-87,188",ellipse:"clrk80",datumName:"Beduaram"},gunung_segara:{towgs84:"-403,684,41",ellipse:"bessel",datumName:"Gunung Segara Jakarta"},rnb72:{towgs84:"106.869,-52.2978,103.724,-0.33657,0.456955,-1.84218,1",ellipse:"intl",datumName:"Reseau National Belge 1972"}};for(var V in J){var X=J[V];J[X.datumName]=X}var K={};function Y(t){if(0===t.length)return null;var s="@"===t[0];return s&&(t=t.slice(1)),"null"===t?{name:"null",mandatory:!s,grid:null,isNull:!0}:{name:t,mandatory:!s,grid:K[t]||null,isNull:!1}}function $(t){return t/3600*Math.PI/180}function tt(t,s,i){return String.fromCharCode.apply(null,new Uint8Array(t.buffer.slice(s,i)))}function st(t){return t.map((function(t){return[$(t.longitudeShift),$(t.latitudeShift)]}))}function it(t,s,i){return{name:tt(t,s+8,s+16).trim(),parent:tt(t,s+24,s+24+8).trim(),lowerLatitude:t.getFloat64(s+72,i),upperLatitude:t.getFloat64(s+88,i),lowerLongitude:t.getFloat64(s+104,i),upperLongitude:t.getFloat64(s+120,i),latitudeInterval:t.getFloat64(s+136,i),longitudeInterval:t.getFloat64(s+152,i),gridNodeCount:t.getInt32(s+168,i)}}function at(t,s,i,a){for(var e=s+176,h=[],n=0;n<i.gridNodeCount;n++){var r={latitudeShift:t.getFloat32(e+16*n,a),longitudeShift:t.getFloat32(e+16*n+4,a),latitudeAccuracy:t.getFloat32(e+16*n+8,a),longitudeAccuracy:t.getFloat32(e+16*n+12,a)};h.push(r)}return h}function et(t,s){if(!(this instanceof et))return new et(t);s=s||function(t){if(t)throw t};var i=I(t);if("object"==typeof i){var e=et.projections.get(i.projName);if(e){if(i.datumCode&&"none"!==i.datumCode){var n=p(J,i.datumCode);n&&(i.datum_params=i.datum_params||(n.towgs84?n.towgs84.split(","):null),i.ellps=n.ellipse,i.datumName=n.datumName?n.datumName:i.datumCode)}i.k0=i.k0||1,i.axis=i.axis||"enu",i.ellps=i.ellps||"wgs84",i.lat1=i.lat1||i.lat0;var r,o,l,u,c,f,M,d=function(t,s,i,a,e){if(!t){var n=p(H,a);n||(n=Z),t=n.a,s=n.b,i=n.rf}return i&&!s&&(s=(1-1/i)*t),(0===i||Math.abs(t-s)<h)&&(e=!0,s=t),{a:t,b:s,rf:i,sphere:e}}(i.a,i.b,i.rf,i.ellps,i.sphere),_=(r=d.a,o=d.b,d.rf,l=i.R_A,f=((u=r*r)-(c=o*o))/u,M=0,l?(u=(r*=1-f*(.16666666666666666+f*(.04722222222222222+.022156084656084655*f)))*r,f=0):M=Math.sqrt(f),{es:f,e:M,ep2:(u-c)/c}),m=function(t){return void 0===t?null:t.split(",").map(Y)}(i.nadgrids),y=i.datum||function(t,s,i,e,h,n,r){var o={};return o.datum_type=void 0===t||"none"===t?5:4,s&&(o.datum_params=s.map(parseFloat),0===o.datum_params[0]&&0===o.datum_params[1]&&0===o.datum_params[2]||(o.datum_type=1),o.datum_params.length>3&&(0===o.datum_params[3]&&0===o.datum_params[4]&&0===o.datum_params[5]&&0===o.datum_params[6]||(o.datum_type=2,o.datum_params[3]*=a,o.datum_params[4]*=a,o.datum_params[5]*=a,o.datum_params[6]=o.datum_params[6]/1e6+1))),r&&(o.datum_type=3,o.grids=r),o.a=i,o.b=e,o.es=h,o.ep2=n,o}(i.datumCode,i.datum_params,d.a,d.b,_.es,_.ep2,m);O(this,i),O(this,e),this.a=d.a,this.b=d.b,this.rf=d.rf,this.sphere=d.sphere,this.es=_.es,this.e=_.e,this.ep2=_.ep2,this.datum=y,this.init(),s(null,this)}else s("Could not get projection name from: "+t)}else s("Could not parse to valid json: "+t)}function ht(t,s,i){var a,h,n,r,o=t.x,l=t.y,u=t.z?t.z:0;if(l<-e&&l>-1.001*e)l=-e;else if(l>e&&l<1.001*e)l=e;else{if(l<-e)return{x:-1/0,y:-1/0,z:t.z};if(l>e)return{x:1/0,y:1/0,z:t.z}}return o>Math.PI&&(o-=2*Math.PI),h=Math.sin(l),r=Math.cos(l),n=h*h,{x:((a=i/Math.sqrt(1-s*n))+u)*r*Math.cos(o),y:(a+u)*r*Math.sin(o),z:(a*(1-s)+u)*h}}function nt(t,s,i,a){var e,h,n,r,o,l,u,c,f,M,p,d,_,m,y,x=1e-12,g=t.x,v=t.y,b=t.z?t.z:0;if(e=Math.sqrt(g*g+v*v),h=Math.sqrt(g*g+v*v+b*b),e/i<x){if(m=0,h/i<x)return y=-a,{x:t.x,y:t.y,z:t.z}}else m=Math.atan2(v,g);n=b/h,c=(r=e/h)*(1-s)*(o=1/Math.sqrt(1-s*(2-s)*r*r)),f=n*o,_=0;do{_++,l=s*(u=i/Math.sqrt(1-s*f*f))/(u+(y=e*c+b*f-u*(1-s*f*f))),d=(p=n*(o=1/Math.sqrt(1-l*(2-l)*r*r)))*c-(M=r*(1-l)*o)*f,c=M,f=p}while(d*d>1e-24&&_<30);return{x:m,y:Math.atan(p/Math.abs(M)),z:y}}function rt(t){return 1===t||2===t}function ot(t,a,e){if(function(t,s){return t.datum_type===s.datum_type&&!(t.a!==s.a||Math.abs(t.es-s.es)>5e-11)&&(1===t.datum_type?t.datum_params[0]===s.datum_params[0]&&t.datum_params[1]===s.datum_params[1]&&t.datum_params[2]===s.datum_params[2]:2!==t.datum_type||t.datum_params[0]===s.datum_params[0]&&t.datum_params[1]===s.datum_params[1]&&t.datum_params[2]===s.datum_params[2]&&t.datum_params[3]===s.datum_params[3]&&t.datum_params[4]===s.datum_params[4]&&t.datum_params[5]===s.datum_params[5]&&t.datum_params[6]===s.datum_params[6])}(t,a))return e;if(5===t.datum_type||5===a.datum_type)return e;var h=t.a,n=t.es;if(3===t.datum_type){if(0!==lt(t,!1,e))return;h=s,n=i}var r=a.a,o=a.b,l=a.es;if(3===a.datum_type&&(r=s,o=6356752.314,l=i),n===l&&h===r&&!rt(t.datum_type)&&!rt(a.datum_type))return e;if((e=ht(e,n,h),rt(t.datum_type)&&(e=function(t,s,i){if(1===s)return{x:t.x+i[0],y:t.y+i[1],z:t.z+i[2]};if(2===s){var a=i[0],e=i[1],h=i[2],n=i[3],r=i[4],o=i[5],l=i[6];return{x:l*(t.x-o*t.y+r*t.z)+a,y:l*(o*t.x+t.y-n*t.z)+e,z:l*(-r*t.x+n*t.y+t.z)+h}}}(e,t.datum_type,t.datum_params)),rt(a.datum_type)&&(e=function(t,s,i){if(1===s)return{x:t.x-i[0],y:t.y-i[1],z:t.z-i[2]};if(2===s){var a=i[0],e=i[1],h=i[2],n=i[3],r=i[4],o=i[5],l=i[6],u=(t.x-a)/l,c=(t.y-e)/l,f=(t.z-h)/l;return{x:u+o*c-r*f,y:-o*u+c+n*f,z:r*u-n*c+f}}}(e,a.datum_type,a.datum_params)),e=nt(e,l,r,o),3===a.datum_type)&&0!==lt(a,!0,e))return;return e}function lt(t,s,i){if(null===t.grids||0===t.grids.length)return console.log("Grid shift grids not found"),-1;var a={x:-i.x,y:i.y},e={x:Number.NaN,y:Number.NaN},h=[];t:for(var n=0;n<t.grids.length;n++){var o=t.grids[n];if(h.push(o.name),o.isNull){e=a;break}if(o.mandatory,null!==o.grid)for(var l=o.grid.subgrids,u=0,c=l.length;u<c;u++){var f=l[u],M=(Math.abs(f.del[1])+Math.abs(f.del[0]))/1e4,p=f.ll[0]-M,d=f.ll[1]-M,_=f.ll[0]+(f.lim[0]-1)*f.del[0]+M,m=f.ll[1]+(f.lim[1]-1)*f.del[1]+M;if(!(d>a.y||p>a.x||m<a.y||_<a.x)&&(e=ut(a,s,f),!isNaN(e.x)))break t}else if(o.mandatory)return console.log("Unable to find mandatory grid '"+o.name+"'"),-1}return isNaN(e.x)?(console.log("Failed to find a grid shift table for location '"+-a.x*r+" "+a.y*r+" tried: '"+h+"'"),-1):(i.x=-e.x,i.y=e.y,0)}function ut(t,s,i){var a={x:Number.NaN,y:Number.NaN};if(isNaN(t.x))return a;var e={x:t.x,y:t.y};e.x-=i.ll[0],e.y-=i.ll[1],e.x=q(e.x-Math.PI)+Math.PI;var h=ct(e,i);if(s){if(isNaN(h.x))return a;h.x=e.x-h.x,h.y=e.y-h.y;var n,r,o=9;do{if(r=ct(h,i),isNaN(r.x)){console.log("Inverse grid shift iteration failed, presumably at grid edge.  Using first approximation.");break}n={x:e.x-(r.x+h.x),y:e.y-(r.y+h.y)},h.x+=n.x,h.y+=n.y}while(o--&&Math.abs(n.x)>1e-12&&Math.abs(n.y)>1e-12);if(o<0)return console.log("Inverse grid shift iterator failed to converge."),a;a.x=q(h.x+i.ll[0]),a.y=h.y+i.ll[1]}else isNaN(h.x)||(a.x=t.x+h.x,a.y=t.y+h.y);return a}function ct(t,s){var i,a={x:t.x/s.del[0],y:t.y/s.del[1]},e=Math.floor(a.x),h=Math.floor(a.y),n=a.x-1*e,r=a.y-1*h,o={x:Number.NaN,y:Number.NaN};if(e<0||e>=s.lim[0])return o;if(h<0||h>=s.lim[1])return o;i=h*s.lim[0]+e;var l=s.cvs[i][0],u=s.cvs[i][1];i++;var c=s.cvs[i][0],f=s.cvs[i][1];i+=s.lim[0];var M=s.cvs[i][0],p=s.cvs[i][1];i--;var d=s.cvs[i][0],_=s.cvs[i][1],m=n*r,y=n*(1-r),x=(1-n)*(1-r),g=(1-n)*r;return o.x=x*l+y*c+g*d+m*M,o.y=x*u+y*f+g*_+m*p,o}function ft(t,s,i){var a,e,h,n=i.x,r=i.y,o=i.z||0,l={};for(h=0;h<3;h++)if(!s||2!==h||void 0!==i.z)switch(0===h?(a=n,e=-1!=="ew".indexOf(t.axis[h])?"x":"y"):1===h?(a=r,e=-1!=="ns".indexOf(t.axis[h])?"y":"x"):(a=o,e="z"),t.axis[h]){case"e":case"n":l[e]=a;break;case"w":case"s":l[e]=-a;break;case"u":void 0!==i[e]&&(l.z=a);break;case"d":void 0!==i[e]&&(l.z=-a);break;default:return null}return l}function Mt(t){var s={x:t[0],y:t[1]};return t.length>2&&(s.z=t[2]),t.length>3&&(s.m=t[3]),s}function pt(t){if("function"==typeof Number.isFinite){if(Number.isFinite(t))return;throw new TypeError("coordinates must be finite numbers")}if("number"!=typeof t||t!=t||!isFinite(t))throw new TypeError("coordinates must be finite numbers")}function dt(t,s,i,a){var e,h=void 0!==(i=Array.isArray(i)?Mt(i):{x:i.x,y:i.y,z:i.z,m:i.m}).z;if(function(t){pt(t.x),pt(t.y)}(i),t.datum&&s.datum&&function(t,s){return(1===t.datum.datum_type||2===t.datum.datum_type||3===t.datum.datum_type)&&"WGS84"!==s.datumCode||(1===s.datum.datum_type||2===s.datum.datum_type||3===s.datum.datum_type)&&"WGS84"!==t.datumCode}(t,s)&&(i=dt(t,e=new et("WGS84"),i,a),t=e),a&&"enu"!==t.axis&&(i=ft(t,!1,i)),"longlat"===t.projName)i={x:i.x*n,y:i.y*n,z:i.z||0};else if(t.to_meter&&(i={x:i.x*t.to_meter,y:i.y*t.to_meter,z:i.z||0}),!(i=t.inverse(i)))return;if(t.from_greenwich&&(i.x+=t.from_greenwich),i=ot(t.datum,s.datum,i))return s.from_greenwich&&(i={x:i.x-s.from_greenwich,y:i.y,z:i.z||0}),"longlat"===s.projName?i={x:i.x*r,y:i.y*r,z:i.z||0}:(i=s.forward(i),s.to_meter&&(i={x:i.x/s.to_meter,y:i.y/s.to_meter,z:i.z||0})),a&&"enu"!==s.axis?ft(s,!0,i):(i&&!h&&delete i.z,i)}et.projections=W,et.projections.start();var _t=et("WGS84");function mt(t,s,i,a){var e,h,n;return Array.isArray(i)?(e=dt(t,s,i,a)||{x:NaN,y:NaN},i.length>2?void 0!==t.name&&"geocent"===t.name||void 0!==s.name&&"geocent"===s.name?"number"==typeof e.z?[e.x,e.y,e.z].concat(i.slice(3)):[e.x,e.y,i[2]].concat(i.slice(3)):[e.x,e.y].concat(i.slice(2)):[e.x,e.y]):(h=dt(t,s,i,a),2===(n=Object.keys(i)).length||n.forEach((function(a){if(void 0!==t.name&&"geocent"===t.name||void 0!==s.name&&"geocent"===s.name){if("x"===a||"y"===a||"z"===a)return}else if("x"===a||"y"===a)return;h[a]=i[a]})),h)}function yt(t){return t instanceof et?t:t.oProj?t.oProj:et(t)}function xt(t,s,i){t=yt(t);var a,e=!1;return void 0===s?(s=t,t=_t,e=!0):(void 0!==s.x||Array.isArray(s))&&(i=s,s=t,t=_t,e=!0),s=yt(s),i?mt(t,s,i):(a={forward:function(i,a){return mt(t,s,i,a)},inverse:function(i,a){return mt(s,t,i,a)}},e&&(a.oProj=s),a)}var gt="AJSAJS",vt="AFAFAF",bt=65,wt=73,St=79,Pt=86,Et=90,Gt={forward:Ct,inverse:function(t){var s=It(Rt(t.toUpperCase()));if(s.lat&&s.lon)return[s.lon,s.lat,s.lon,s.lat];return[s.left,s.bottom,s.right,s.top]},toPoint:Nt};function Ct(t,s){return s=s||5,function(t,s){var i="00000"+t.easting,a="00000"+t.northing;return t.zoneNumber+t.zoneLetter+(M=t.easting,p=t.northing,d=t.zoneNumber,_=At(d),m=Math.floor(M/1e5),y=Math.floor(p/1e5)%20,e=m,h=y,n=_,r=n-1,o=gt.charCodeAt(r),l=vt.charCodeAt(r),u=o+e-1,c=l+h,f=!1,u>Et&&(u=u-Et+bt-1,f=!0),(u===wt||o<wt&&u>wt||(u>wt||o<wt)&&f)&&u++,(u===St||o<St&&u>St||(u>St||o<St)&&f)&&++u===wt&&u++,u>Et&&(u=u-Et+bt-1),c>Pt?(c=c-Pt+bt-1,f=!0):f=!1,(c===wt||l<wt&&c>wt||(c>wt||l<wt)&&f)&&c++,(c===St||l<St&&c>St||(c>St||l<St)&&f)&&++c===wt&&c++,c>Pt&&(c=c-Pt+bt-1),String.fromCharCode(u)+String.fromCharCode(c))+i.substr(i.length-5,s)+a.substr(a.length-5,s);var e,h,n,r,o,l,u,c,f;var M,p,d,_,m,y}(function(t){var s,i,a,e,h,n,r,o,l=t.lat,u=t.lon,c=6378137,f=.00669438,M=.9996,p=kt(l),d=kt(u);o=Math.floor((u+180)/6)+1,180===u&&(o=60);l>=56&&l<64&&u>=3&&u<12&&(o=32);l>=72&&l<84&&(u>=0&&u<9?o=31:u>=9&&u<21?o=33:u>=21&&u<33?o=35:u>=33&&u<42&&(o=37));r=kt(6*(o-1)-180+3),s=f/(1-f),i=c/Math.sqrt(1-f*Math.sin(p)*Math.sin(p)),a=Math.tan(p)*Math.tan(p),e=s*Math.cos(p)*Math.cos(p),h=Math.cos(p)*(d-r),n=c*((1-f/4-3*f*f/64-5*f*f*f/256)*p-(3*f/8+3*f*f/32+45*f*f*f/1024)*Math.sin(2*p)+(15*f*f/256+45*f*f*f/1024)*Math.sin(4*p)-35*f*f*f/3072*Math.sin(6*p));var _=M*i*(h+(1-a+e)*h*h*h/6+(5-18*a+a*a+72*e-58*s)*h*h*h*h*h/120)+5e5,m=M*(n+i*Math.tan(p)*(h*h/2+(5-a+9*e+4*e*e)*h*h*h*h/24+(61-58*a+a*a+600*e-330*s)*h*h*h*h*h*h/720));l<0&&(m+=1e7);return{northing:Math.round(m),easting:Math.round(_),zoneNumber:o,zoneLetter:Ot(l)}}({lat:t[1],lon:t[0]}),s)}function Nt(t){var s=It(Rt(t.toUpperCase()));return s.lat&&s.lon?[s.lon,s.lat]:[(s.left+s.right)/2,(s.top+s.bottom)/2]}function kt(t){return t*(Math.PI/180)}function jt(t){return t/Math.PI*180}function It(t){var s=t.northing,i=t.easting,a=t.zoneLetter,e=t.zoneNumber;if(e<0||e>60)return null;var h,n,r,o,l,u,c,f,M,p=.9996,d=6378137,_=.00669438,m=(1-Math.sqrt(.99330562))/(1+Math.sqrt(.99330562)),y=i-5e5,x=s;a<"N"&&(x-=1e7),c=6*(e-1)-180+3,h=.006739496752268451,M=(f=x/p/6367449.145945056)+(3*m/2-27*m*m*m/32)*Math.sin(2*f)+(21*m*m/16-55*m*m*m*m/32)*Math.sin(4*f)+151*m*m*m/96*Math.sin(6*f),n=d/Math.sqrt(1-_*Math.sin(M)*Math.sin(M)),r=Math.tan(M)*Math.tan(M),o=h*Math.cos(M)*Math.cos(M),l=.99330562*d/Math.pow(1-_*Math.sin(M)*Math.sin(M),1.5),u=y/(n*p);var g=M-n*Math.tan(M)/l*(u*u/2-(5+3*r+10*o-4*o*o-9*h)*u*u*u*u/24+(61+90*r+298*o+45*r*r-1.6983531815716497-3*o*o)*u*u*u*u*u*u/720);g=jt(g);var v,b=(u-(1+2*r+o)*u*u*u/6+(5-2*o+28*r-3*o*o+8*h+24*r*r)*u*u*u*u*u/120)/Math.cos(M);if(b=c+jt(b),t.accuracy){var w=It({northing:t.northing+t.accuracy,easting:t.easting+t.accuracy,zoneLetter:t.zoneLetter,zoneNumber:t.zoneNumber});v={top:w.lat,right:w.lon,bottom:g,left:b}}else v={lat:g,lon:b};return v}function Ot(t){var s="Z";return 84>=t&&t>=72?s="X":72>t&&t>=64?s="W":64>t&&t>=56?s="V":56>t&&t>=48?s="U":48>t&&t>=40?s="T":40>t&&t>=32?s="S":32>t&&t>=24?s="R":24>t&&t>=16?s="Q":16>t&&t>=8?s="P":8>t&&t>=0?s="N":0>t&&t>=-8?s="M":-8>t&&t>=-16?s="L":-16>t&&t>=-24?s="K":-24>t&&t>=-32?s="J":-32>t&&t>=-40?s="H":-40>t&&t>=-48?s="G":-48>t&&t>=-56?s="F":-56>t&&t>=-64?s="E":-64>t&&t>=-72?s="D":-72>t&&t>=-80&&(s="C"),s}function At(t){var s=t%6;return 0===s&&(s=6),s}function Rt(t){if(t&&0===t.length)throw"MGRSPoint coverting from nothing";for(var s,i=t.length,a=null,e="",h=0;!/[A-Z]/.test(s=t.charAt(h));){if(h>=2)throw"MGRSPoint bad conversion from: "+t;e+=s,h++}var n=parseInt(e,10);if(0===h||h+3>i)throw"MGRSPoint bad conversion from: "+t;var r=t.charAt(h++);if(r<="A"||"B"===r||"Y"===r||r>="Z"||"I"===r||"O"===r)throw"MGRSPoint zone letter "+r+" not handled: "+t;a=t.substring(h,h+=2);for(var o=At(n),l=function(t,s){var i=gt.charCodeAt(s-1),a=1e5,e=!1;for(;i!==t.charCodeAt(0);){if(++i===wt&&i++,i===St&&i++,i>Et){if(e)throw"Bad character: "+t;i=bt,e=!0}a+=1e5}return a}(a.charAt(0),o),u=function(t,s){if(t>"V")throw"MGRSPoint given invalid Northing "+t;var i=vt.charCodeAt(s-1),a=0,e=!1;for(;i!==t.charCodeAt(0);){if(++i===wt&&i++,i===St&&i++,i>Pt){if(e)throw"Bad character: "+t;i=bt,e=!0}a+=1e5}return a}(a.charAt(1),o);u<qt(r);)u+=2e6;var c=i-h;if(c%2!=0)throw"MGRSPoint has to have an even number \nof digits after the zone letter and two 100km letters - front \nhalf for easting meters, second half for \nnorthing meters"+t;var f,M,p,d=c/2,_=0,m=0;return d>0&&(f=1e5/Math.pow(10,d),M=t.substring(h,h+d),_=parseFloat(M)*f,p=t.substring(h+d),m=parseFloat(p)*f),{easting:_+l,northing:m+u,zoneLetter:r,zoneNumber:n,accuracy:f}}function qt(t){var s;switch(t){case"C":s=11e5;break;case"D":s=2e6;break;case"E":s=28e5;break;case"F":s=37e5;break;case"G":s=46e5;break;case"H":s=55e5;break;case"J":s=64e5;break;case"K":s=73e5;break;case"L":s=82e5;break;case"M":s=91e5;break;case"N":s=0;break;case"P":s=8e5;break;case"Q":s=17e5;break;case"R":s=26e5;break;case"S":s=35e5;break;case"T":s=44e5;break;case"U":s=53e5;break;case"V":s=62e5;break;case"W":s=7e6;break;case"X":s=79e5;break;default:s=-1}if(s>=0)return s;throw"Invalid zone letter: "+t}function Lt(t,s,i){if(!(this instanceof Lt))return new Lt(t,s,i);if(Array.isArray(t))this.x=t[0],this.y=t[1],this.z=t[2]||0;else if("object"==typeof t)this.x=t.x,this.y=t.y,this.z=t.z||0;else if("string"==typeof t&&void 0===s){var a=t.split(",");this.x=parseFloat(a[0],10),this.y=parseFloat(a[1],10),this.z=parseFloat(a[2],10)||0}else this.x=t,this.y=s,this.z=i||0;console.warn("proj4.Point will be removed in version 3, use proj4.toPoint")}Lt.fromMGRS=function(t){return new Lt(Nt(t))},Lt.prototype.toMGRS=function(t){return Ct([this.x,this.y],t)};var Tt=.046875,zt=.01953125,Bt=.01068115234375;function Dt(t){var s=[];s[0]=1-t*(.25+t*(Tt+t*(zt+t*Bt))),s[1]=t*(.75-t*(Tt+t*(zt+t*Bt)));var i=t*t;return s[2]=i*(.46875-t*(.013020833333333334+.007120768229166667*t)),i*=t,s[3]=i*(.3645833333333333-.005696614583333333*t),s[4]=i*t*.3076171875,s}function Ft(t,s,i,a){return i*=s,s*=s,a[0]*t-i*(a[1]+s*(a[2]+s*(a[3]+s*a[4])))}function Ut(t,s,i){for(var a=1/(1-s),e=t,n=20;n;--n){var r=Math.sin(e),o=1-s*r*r;if(e-=o=(Ft(e,r,Math.cos(e),i)-t)*(o*Math.sqrt(o))*a,Math.abs(o)<h)return e}return e}var Qt={init:function(){this.x0=void 0!==this.x0?this.x0:0,this.y0=void 0!==this.y0?this.y0:0,this.long0=void 0!==this.long0?this.long0:0,this.lat0=void 0!==this.lat0?this.lat0:0,this.es&&(this.en=Dt(this.es),this.ml0=Ft(this.lat0,Math.sin(this.lat0),Math.cos(this.lat0),this.en))},forward:function(t){var s,i,a,e=t.x,n=t.y,r=q(e-this.long0),o=Math.sin(n),l=Math.cos(n);if(this.es){var u=l*r,c=Math.pow(u,2),f=this.ep2*Math.pow(l,2),M=Math.pow(f,2),p=Math.abs(l)>h?Math.tan(n):0,d=Math.pow(p,2),_=Math.pow(d,2);s=1-this.es*Math.pow(o,2),u/=Math.sqrt(s);var m=Ft(n,o,l,this.en);i=this.a*(this.k0*u*(1+c/6*(1-d+f+c/20*(5-18*d+_+14*f-58*d*f+c/42*(61+179*_-_*d-479*d)))))+this.x0,a=this.a*(this.k0*(m-this.ml0+o*r*u/2*(1+c/12*(5-d+9*f+4*M+c/30*(61+_-58*d+270*f-330*d*f+c/56*(1385+543*_-_*d-3111*d))))))+this.y0}else{var y=l*Math.sin(r);if(Math.abs(Math.abs(y)-1)<h)return 93;if(i=.5*this.a*this.k0*Math.log((1+y)/(1-y))+this.x0,a=l*Math.cos(r)/Math.sqrt(1-Math.pow(y,2)),(y=Math.abs(a))>=1){if(y-1>h)return 93;a=0}else a=Math.acos(a);n<0&&(a=-a),a=this.a*this.k0*(a-this.lat0)+this.y0}return t.x=i,t.y=a,t},inverse:function(t){var s,i,a,n,r=(t.x-this.x0)*(1/this.a),o=(t.y-this.y0)*(1/this.a);if(this.es)if(i=Ut(s=this.ml0+o/this.k0,this.es,this.en),Math.abs(i)<e){var l=Math.sin(i),u=Math.cos(i),c=Math.abs(u)>h?Math.tan(i):0,f=this.ep2*Math.pow(u,2),M=Math.pow(f,2),p=Math.pow(c,2),d=Math.pow(p,2);s=1-this.es*Math.pow(l,2);var _=r*Math.sqrt(s)/this.k0,m=Math.pow(_,2);a=i-(s*=c)*m/(1-this.es)*.5*(1-m/12*(5+3*p-9*f*p+f-4*M-m/30*(61+90*p-252*f*p+45*d+46*f-m/56*(1385+3633*p+4095*d+1574*d*p)))),n=q(this.long0+_*(1-m/6*(1+2*p+f-m/20*(5+28*p+24*d+8*f*p+6*f-m/42*(61+662*p+1320*d+720*d*p))))/u)}else a=e*R(o),n=0;else{var y=Math.exp(r/this.k0),x=.5*(y-1/y),g=this.lat0+o/this.k0,v=Math.cos(g);s=Math.sqrt((1-Math.pow(v,2))/(1+Math.pow(x,2))),a=Math.asin(s),o<0&&(a=-a),n=0===x&&0===v?0:q(Math.atan2(x,v)+this.long0)}return t.x=n,t.y=a,t},names:["Fast_Transverse_Mercator","Fast Transverse Mercator"]};function Wt(t){var s=Math.exp(t);return s=(s-1/s)/2}function Ht(t,s){t=Math.abs(t),s=Math.abs(s);var i=Math.max(t,s),a=Math.min(t,s)/(i||1);return i*Math.sqrt(1+Math.pow(a,2))}function Zt(t){var s=Math.abs(t);return s=function(t){var s=1+t,i=s-1;return 0===i?t:t*Math.log(s)/i}(s*(1+s/(Ht(1,s)+1))),t<0?-s:s}function Jt(t,s){for(var i,a=2*Math.cos(2*s),e=t.length-1,h=t[e],n=0;--e>=0;)i=a*h-n+t[e],n=h,h=i;return s+i*Math.sin(2*s)}function Vt(t,s,i){for(var a,e,h=Math.sin(s),n=Math.cos(s),r=Wt(i),o=function(t){var s=Math.exp(t);return(s+1/s)/2}(i),l=2*n*o,u=-2*h*r,c=t.length-1,f=t[c],M=0,p=0,d=0;--c>=0;)a=p,e=M,f=l*(p=f)-a-u*(M=d)+t[c],d=u*p-e+l*M;return[(l=h*o)*f-(u=n*r)*d,l*d+u*f]}var Xt={init:function(){if(!this.approx&&(isNaN(this.es)||this.es<=0))throw new Error('Incorrect elliptical usage. Try using the +approx option in the proj string, or PROJECTION["Fast_Transverse_Mercator"] in the WKT.');this.approx&&(Qt.init.apply(this),this.forward=Qt.forward,this.inverse=Qt.inverse),this.x0=void 0!==this.x0?this.x0:0,this.y0=void 0!==this.y0?this.y0:0,this.long0=void 0!==this.long0?this.long0:0,this.lat0=void 0!==this.lat0?this.lat0:0,this.cgb=[],this.cbg=[],this.utg=[],this.gtu=[];var t=this.es/(1+Math.sqrt(1-this.es)),s=t/(2-t),i=s;this.cgb[0]=s*(2+s*(-2/3+s*(s*(116/45+s*(26/45+s*(-2854/675)))-2))),this.cbg[0]=s*(s*(2/3+s*(4/3+s*(-82/45+s*(32/45+s*(4642/4725)))))-2),i*=s,this.cgb[1]=i*(7/3+s*(s*(-227/45+s*(2704/315+s*(2323/945)))-1.6)),this.cbg[1]=i*(5/3+s*(-16/15+s*(-13/9+s*(904/315+s*(-1522/945))))),i*=s,this.cgb[2]=i*(56/15+s*(-136/35+s*(-1262/105+s*(73814/2835)))),this.cbg[2]=i*(-26/15+s*(34/21+s*(1.6+s*(-12686/2835)))),i*=s,this.cgb[3]=i*(4279/630+s*(-332/35+s*(-399572/14175))),this.cbg[3]=i*(1237/630+s*(s*(-24832/14175)-2.4)),i*=s,this.cgb[4]=i*(4174/315+s*(-144838/6237)),this.cbg[4]=i*(-734/315+s*(109598/31185)),i*=s,this.cgb[5]=i*(601676/22275),this.cbg[5]=i*(444337/155925),i=Math.pow(s,2),this.Qn=this.k0/(1+s)*(1+i*(1/4+i*(1/64+i/256))),this.utg[0]=s*(s*(2/3+s*(-37/96+s*(1/360+s*(81/512+s*(-96199/604800)))))-.5),this.gtu[0]=s*(.5+s*(-2/3+s*(5/16+s*(41/180+s*(-127/288+s*(7891/37800)))))),this.utg[1]=i*(-1/48+s*(-1/15+s*(437/1440+s*(-46/105+s*(1118711/3870720))))),this.gtu[1]=i*(13/48+s*(s*(557/1440+s*(281/630+s*(-1983433/1935360)))-.6)),i*=s,this.utg[2]=i*(-17/480+s*(37/840+s*(209/4480+s*(-5569/90720)))),this.gtu[2]=i*(61/240+s*(-103/140+s*(15061/26880+s*(167603/181440)))),i*=s,this.utg[3]=i*(-4397/161280+s*(11/504+s*(830251/7257600))),this.gtu[3]=i*(49561/161280+s*(-179/168+s*(6601661/7257600))),i*=s,this.utg[4]=i*(-4583/161280+s*(108847/3991680)),this.gtu[4]=i*(34729/80640+s*(-3418889/1995840)),i*=s,this.utg[5]=i*(-20648693/638668800),this.gtu[5]=.6650675310896665*i;var a=Jt(this.cbg,this.lat0);this.Zb=-this.Qn*(a+function(t,s){for(var i,a=2*Math.cos(s),e=t.length-1,h=t[e],n=0;--e>=0;)i=a*h-n+t[e],n=h,h=i;return Math.sin(s)*i}(this.gtu,2*a))},forward:function(t){var s=q(t.x-this.long0),i=t.y;i=Jt(this.cbg,i);var a=Math.sin(i),e=Math.cos(i),h=Math.sin(s),n=Math.cos(s);i=Math.atan2(a,n*e),s=Math.atan2(h*e,Ht(a,e*n)),s=Zt(Math.tan(s));var r,o,l=Vt(this.gtu,2*i,2*s);return i+=l[0],s+=l[1],Math.abs(s)<=2.623395162778?(r=this.a*(this.Qn*s)+this.x0,o=this.a*(this.Qn*i+this.Zb)+this.y0):(r=1/0,o=1/0),t.x=r,t.y=o,t},inverse:function(t){var s,i,a=(t.x-this.x0)*(1/this.a),e=(t.y-this.y0)*(1/this.a);if(e=(e-this.Zb)/this.Qn,a/=this.Qn,Math.abs(a)<=2.623395162778){var h=Vt(this.utg,2*e,2*a);e+=h[0],a+=h[1],a=Math.atan(Wt(a));var n=Math.sin(e),r=Math.cos(e),o=Math.sin(a),l=Math.cos(a);e=Math.atan2(n*l,Ht(o,l*r)),s=q((a=Math.atan2(o,l*r))+this.long0),i=Jt(this.cgb,e)}else s=1/0,i=1/0;return t.x=s,t.y=i,t},names:["Extended_Transverse_Mercator","Extended Transverse Mercator","etmerc","Transverse_Mercator","Transverse Mercator","Gauss Kruger","Gauss_Kruger","tmerc"]};var Kt={init:function(){var t=function(t,s){if(void 0===t){if((t=Math.floor(30*(q(s)+Math.PI)/Math.PI)+1)<0)return 0;if(t>60)return 60}return t}(this.zone,this.long0);if(void 0===t)throw new Error("unknown utm zone");this.lat0=0,this.long0=(6*Math.abs(t)-183)*n,this.x0=5e5,this.y0=this.utmSouth?1e7:0,this.k0=.9996,Xt.init.apply(this),this.forward=Xt.forward,this.inverse=Xt.inverse},names:["Universal Transverse Mercator System","utm"],dependsOn:"etmerc"};function Yt(t,s){return Math.pow((1-t)/(1+t),s)}var $t={init:function(){var t=Math.sin(this.lat0),s=Math.cos(this.lat0);s*=s,this.rc=Math.sqrt(1-this.es)/(1-this.es*t*t),this.C=Math.sqrt(1+this.es*s*s/(1-this.es)),this.phic0=Math.asin(t/this.C),this.ratexp=.5*this.C*this.e,this.K=Math.tan(.5*this.phic0+o)/(Math.pow(Math.tan(.5*this.lat0+o),this.C)*Yt(this.e*t,this.ratexp))},forward:function(t){var s=t.x,i=t.y;return t.y=2*Math.atan(this.K*Math.pow(Math.tan(.5*i+o),this.C)*Yt(this.e*Math.sin(i),this.ratexp))-e,t.x=this.C*s,t},inverse:function(t){for(var s=t.x/this.C,i=t.y,a=Math.pow(Math.tan(.5*i+o)/this.K,1/this.C),h=20;h>0&&(i=2*Math.atan(a*Yt(this.e*Math.sin(t.y),-.5*this.e))-e,!(Math.abs(i-t.y)<1e-14));--h)t.y=i;return h?(t.x=s,t.y=i,t):null},names:["gauss"]};var ts={init:function(){$t.init.apply(this),this.rc&&(this.sinc0=Math.sin(this.phic0),this.cosc0=Math.cos(this.phic0),this.R2=2*this.rc,this.title||(this.title="Oblique Stereographic Alternative"))},forward:function(t){var s,i,a,e;return t.x=q(t.x-this.long0),$t.forward.apply(this,[t]),s=Math.sin(t.y),i=Math.cos(t.y),a=Math.cos(t.x),e=this.k0*this.R2/(1+this.sinc0*s+this.cosc0*i*a),t.x=e*i*Math.sin(t.x),t.y=e*(this.cosc0*s-this.sinc0*i*a),t.x=this.a*t.x+this.x0,t.y=this.a*t.y+this.y0,t},inverse:function(t){var s,i,a,e,h;if(t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,h=Ht(t.x,t.y)){var n=2*Math.atan2(h,this.R2);s=Math.sin(n),i=Math.cos(n),e=Math.asin(i*this.sinc0+t.y*s*this.cosc0/h),a=Math.atan2(t.x*s,h*this.cosc0*i-t.y*this.sinc0*s)}else e=this.phic0,a=0;return t.x=a,t.y=e,$t.inverse.apply(this,[t]),t.x=q(t.x+this.long0),t},names:["Stereographic_North_Pole","Oblique_Stereographic","sterea","Oblique Stereographic Alternative","Double_Stereographic"]};var ss={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.coslat0=Math.cos(this.lat0),this.sinlat0=Math.sin(this.lat0),this.sphere?1===this.k0&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=h&&(this.k0=.5*(1+R(this.lat0)*Math.sin(this.lat_ts))):(Math.abs(this.coslat0)<=h&&(this.lat0>0?this.con=1:this.con=-1),this.cons=Math.sqrt(Math.pow(1+this.e,1+this.e)*Math.pow(1-this.e,1-this.e)),1===this.k0&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=h&&Math.abs(Math.cos(this.lat_ts))>h&&(this.k0=.5*this.cons*A(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts))/L(this.e,this.con*this.lat_ts,this.con*Math.sin(this.lat_ts))),this.ms1=A(this.e,this.sinlat0,this.coslat0),this.X0=2*Math.atan(this.ssfn_(this.lat0,this.sinlat0,this.e))-e,this.cosX0=Math.cos(this.X0),this.sinX0=Math.sin(this.X0))},forward:function(t){var s,i,a,n,r,o,l=t.x,u=t.y,c=Math.sin(u),f=Math.cos(u),M=q(l-this.long0);return Math.abs(Math.abs(l-this.long0)-Math.PI)<=h&&Math.abs(u+this.lat0)<=h?(t.x=NaN,t.y=NaN,t):this.sphere?(s=2*this.k0/(1+this.sinlat0*c+this.coslat0*f*Math.cos(M)),t.x=this.a*s*f*Math.sin(M)+this.x0,t.y=this.a*s*(this.coslat0*c-this.sinlat0*f*Math.cos(M))+this.y0,t):(i=2*Math.atan(this.ssfn_(u,c,this.e))-e,n=Math.cos(i),a=Math.sin(i),Math.abs(this.coslat0)<=h?(r=L(this.e,u*this.con,this.con*c),o=2*this.a*this.k0*r/this.cons,t.x=this.x0+o*Math.sin(l-this.long0),t.y=this.y0-this.con*o*Math.cos(l-this.long0),t):(Math.abs(this.sinlat0)<h?(s=2*this.a*this.k0/(1+n*Math.cos(M)),t.y=s*a):(s=2*this.a*this.k0*this.ms1/(this.cosX0*(1+this.sinX0*a+this.cosX0*n*Math.cos(M))),t.y=s*(this.cosX0*a-this.sinX0*n*Math.cos(M))+this.y0),t.x=s*n*Math.sin(M)+this.x0,t))},inverse:function(t){var s,i,a,n,r;t.x-=this.x0,t.y-=this.y0;var o=Math.sqrt(t.x*t.x+t.y*t.y);if(this.sphere){var l=2*Math.atan(o/(2*this.a*this.k0));return s=this.long0,i=this.lat0,o<=h?(t.x=s,t.y=i,t):(i=Math.asin(Math.cos(l)*this.sinlat0+t.y*Math.sin(l)*this.coslat0/o),s=Math.abs(this.coslat0)<h?this.lat0>0?q(this.long0+Math.atan2(t.x,-1*t.y)):q(this.long0+Math.atan2(t.x,t.y)):q(this.long0+Math.atan2(t.x*Math.sin(l),o*this.coslat0*Math.cos(l)-t.y*this.sinlat0*Math.sin(l))),t.x=s,t.y=i,t)}if(Math.abs(this.coslat0)<=h){if(o<=h)return i=this.lat0,s=this.long0,t.x=s,t.y=i,t;t.x*=this.con,t.y*=this.con,a=o*this.cons/(2*this.a*this.k0),i=this.con*T(this.e,a),s=this.con*q(this.con*this.long0+Math.atan2(t.x,-1*t.y))}else n=2*Math.atan(o*this.cosX0/(2*this.a*this.k0*this.ms1)),s=this.long0,o<=h?r=this.X0:(r=Math.asin(Math.cos(n)*this.sinX0+t.y*Math.sin(n)*this.cosX0/o),s=q(this.long0+Math.atan2(t.x*Math.sin(n),o*this.cosX0*Math.cos(n)-t.y*this.sinX0*Math.sin(n)))),i=-1*T(this.e,Math.tan(.5*(e+r)));return t.x=s,t.y=i,t},names:["stere","Stereographic_South_Pole","Polar Stereographic (variant B)","Polar_Stereographic"],ssfn_:function(t,s,i){return s*=i,Math.tan(.5*(e+t))*Math.pow((1-s)/(1+s),.5*i)}};var is={init:function(){var t=this.lat0;this.lambda0=this.long0;var s=Math.sin(t),i=this.a,a=1/this.rf,e=2*a-Math.pow(a,2),h=this.e=Math.sqrt(e);this.R=this.k0*i*Math.sqrt(1-e)/(1-e*Math.pow(s,2)),this.alpha=Math.sqrt(1+e/(1-e)*Math.pow(Math.cos(t),4)),this.b0=Math.asin(s/this.alpha);var n=Math.log(Math.tan(Math.PI/4+this.b0/2)),r=Math.log(Math.tan(Math.PI/4+t/2)),o=Math.log((1+h*s)/(1-h*s));this.K=n-this.alpha*r+this.alpha*h/2*o},forward:function(t){var s=Math.log(Math.tan(Math.PI/4-t.y/2)),i=this.e/2*Math.log((1+this.e*Math.sin(t.y))/(1-this.e*Math.sin(t.y))),a=-this.alpha*(s+i)+this.K,e=2*(Math.atan(Math.exp(a))-Math.PI/4),h=this.alpha*(t.x-this.lambda0),n=Math.atan(Math.sin(h)/(Math.sin(this.b0)*Math.tan(e)+Math.cos(this.b0)*Math.cos(h))),r=Math.asin(Math.cos(this.b0)*Math.sin(e)-Math.sin(this.b0)*Math.cos(e)*Math.cos(h));return t.y=this.R/2*Math.log((1+Math.sin(r))/(1-Math.sin(r)))+this.y0,t.x=this.R*n+this.x0,t},inverse:function(t){for(var s=t.x-this.x0,i=t.y-this.y0,a=s/this.R,e=2*(Math.atan(Math.exp(i/this.R))-Math.PI/4),h=Math.asin(Math.cos(this.b0)*Math.sin(e)+Math.sin(this.b0)*Math.cos(e)*Math.cos(a)),n=Math.atan(Math.sin(a)/(Math.cos(this.b0)*Math.cos(a)-Math.sin(this.b0)*Math.tan(e))),r=this.lambda0+n/this.alpha,o=0,l=h,u=-1e3,c=0;Math.abs(l-u)>1e-7;){if(++c>20)return;o=1/this.alpha*(Math.log(Math.tan(Math.PI/4+h/2))-this.K)+this.e*Math.log(Math.tan(Math.PI/4+Math.asin(this.e*Math.sin(l))/2)),u=l,l=2*Math.atan(Math.exp(o))-Math.PI/2}return t.x=r,t.y=l,t},names:["somerc"]},as=1e-7;var es={init:function(){var t,s,i,a,r,u,c,f,M,p,d,_,m,y=0,x=0,g=0,v=0,b=0,w=0,S=0;this.no_off=(m="object"==typeof(_=this).PROJECTION?Object.keys(_.PROJECTION)[0]:_.PROJECTION,"no_uoff"in _||"no_off"in _||-1!==["Hotine_Oblique_Mercator","Hotine_Oblique_Mercator_Azimuth_Natural_Origin"].indexOf(m)),this.no_rot="no_rot"in this;var P=!1;"alpha"in this&&(P=!0);var E=!1;if("rectified_grid_angle"in this&&(E=!0),P&&(S=this.alpha),E&&(y=this.rectified_grid_angle*n),P||E)x=this.longc;else if(g=this.long1,b=this.lat1,v=this.long2,w=this.lat2,Math.abs(b-w)<=as||(t=Math.abs(b))<=as||Math.abs(t-e)<=as||Math.abs(Math.abs(this.lat0)-e)<=as||Math.abs(Math.abs(w)-e)<=as)throw new Error;var G=1-this.es;s=Math.sqrt(G),Math.abs(this.lat0)>h?(f=Math.sin(this.lat0),i=Math.cos(this.lat0),t=1-this.es*f*f,this.B=i*i,this.B=Math.sqrt(1+this.es*this.B*this.B/G),this.A=this.B*this.k0*s/t,(r=(a=this.B*s/(i*Math.sqrt(t)))*a-1)<=0?r=0:(r=Math.sqrt(r),this.lat0<0&&(r=-r)),this.E=r+=a,this.E*=Math.pow(L(this.e,this.lat0,f),this.B)):(this.B=1/s,this.A=this.k0,this.E=a=r=1),P||E?(P?(d=Math.asin(Math.sin(S)/a),E||(y=S)):(d=y,S=Math.asin(a*Math.sin(d))),this.lam0=x-Math.asin(.5*(r-1/r)*Math.tan(d))/this.B):(u=Math.pow(L(this.e,b,Math.sin(b)),this.B),c=Math.pow(L(this.e,w,Math.sin(w)),this.B),r=this.E/u,M=(c-u)/(c+u),p=((p=this.E*this.E)-c*u)/(p+c*u),(t=g-v)<-Math.pi?v-=l:t>Math.pi&&(v+=l),this.lam0=q(.5*(g+v)-Math.atan(p*Math.tan(.5*this.B*(g-v))/M)/this.B),d=Math.atan(2*Math.sin(this.B*q(g-this.lam0))/(r-1/r)),y=S=Math.asin(a*Math.sin(d))),this.singam=Math.sin(d),this.cosgam=Math.cos(d),this.sinrot=Math.sin(y),this.cosrot=Math.cos(y),this.rB=1/this.B,this.ArB=this.A*this.rB,this.BrA=1/this.ArB,this.A,this.B,this.no_off?this.u_0=0:(this.u_0=Math.abs(this.ArB*Math.atan(Math.sqrt(a*a-1)/Math.cos(S))),this.lat0<0&&(this.u_0=-this.u_0)),r=.5*d,this.v_pole_n=this.ArB*Math.log(Math.tan(o-r)),this.v_pole_s=this.ArB*Math.log(Math.tan(o+r))},forward:function(t){var s,i,a,n,r,o,l,u,c={};if(t.x=t.x-this.lam0,Math.abs(Math.abs(t.y)-e)>h){if(s=.5*((r=this.E/Math.pow(L(this.e,t.y,Math.sin(t.y)),this.B))-(o=1/r)),i=.5*(r+o),n=Math.sin(this.B*t.x),a=(s*this.singam-n*this.cosgam)/i,Math.abs(Math.abs(a)-1)<h)throw new Error;u=.5*this.ArB*Math.log((1-a)/(1+a)),o=Math.cos(this.B*t.x),l=Math.abs(o)<as?this.A*t.x:this.ArB*Math.atan2(s*this.cosgam+n*this.singam,o)}else u=t.y>0?this.v_pole_n:this.v_pole_s,l=this.ArB*t.y;return this.no_rot?(c.x=l,c.y=u):(l-=this.u_0,c.x=u*this.cosrot+l*this.sinrot,c.y=l*this.cosrot-u*this.sinrot),c.x=this.a*c.x+this.x0,c.y=this.a*c.y+this.y0,c},inverse:function(t){var s,i,a,n,r,o,l,u={};if(t.x=(t.x-this.x0)*(1/this.a),t.y=(t.y-this.y0)*(1/this.a),this.no_rot?(i=t.y,s=t.x):(i=t.x*this.cosrot-t.y*this.sinrot,s=t.y*this.cosrot+t.x*this.sinrot+this.u_0),n=.5*((a=Math.exp(-this.BrA*i))-1/a),r=.5*(a+1/a),l=((o=Math.sin(this.BrA*s))*this.cosgam+n*this.singam)/r,Math.abs(Math.abs(l)-1)<h)u.x=0,u.y=l<0?-e:e;else{if(u.y=this.E/Math.sqrt((1+l)/(1-l)),u.y=T(this.e,Math.pow(u.y,1/this.B)),u.y===1/0)throw new Error;u.x=-this.rB*Math.atan2(n*this.cosgam-o*this.singam,Math.cos(this.BrA*s))}return u.x+=this.lam0,u},names:["Hotine_Oblique_Mercator","Hotine Oblique Mercator","Hotine_Oblique_Mercator_Azimuth_Natural_Origin","Hotine_Oblique_Mercator_Two_Point_Natural_Origin","Hotine_Oblique_Mercator_Azimuth_Center","Oblique_Mercator","omerc"]};var hs={init:function(){if(this.lat2||(this.lat2=this.lat1),this.k0||(this.k0=1),this.x0=this.x0||0,this.y0=this.y0||0,!(Math.abs(this.lat1+this.lat2)<h)){var t=this.b/this.a;this.e=Math.sqrt(1-t*t);var s=Math.sin(this.lat1),i=Math.cos(this.lat1),a=A(this.e,s,i),e=L(this.e,this.lat1,s),n=Math.sin(this.lat2),r=Math.cos(this.lat2),o=A(this.e,n,r),l=L(this.e,this.lat2,n),u=L(this.e,this.lat0,Math.sin(this.lat0));Math.abs(this.lat1-this.lat2)>h?this.ns=Math.log(a/o)/Math.log(e/l):this.ns=s,isNaN(this.ns)&&(this.ns=s),this.f0=a/(this.ns*Math.pow(e,this.ns)),this.rh=this.a*this.f0*Math.pow(u,this.ns),this.title||(this.title="Lambert Conformal Conic")}},forward:function(t){var s=t.x,i=t.y;Math.abs(2*Math.abs(i)-Math.PI)<=h&&(i=R(i)*(e-2e-10));var a,n,r=Math.abs(Math.abs(i)-e);if(r>h)a=L(this.e,i,Math.sin(i)),n=this.a*this.f0*Math.pow(a,this.ns);else{if((r=i*this.ns)<=0)return null;n=0}var o=this.ns*q(s-this.long0);return t.x=this.k0*(n*Math.sin(o))+this.x0,t.y=this.k0*(this.rh-n*Math.cos(o))+this.y0,t},inverse:function(t){var s,i,a,h,n,r=(t.x-this.x0)/this.k0,o=this.rh-(t.y-this.y0)/this.k0;this.ns>0?(s=Math.sqrt(r*r+o*o),i=1):(s=-Math.sqrt(r*r+o*o),i=-1);var l=0;if(0!==s&&(l=Math.atan2(i*r,i*o)),0!==s||this.ns>0){if(i=1/this.ns,a=Math.pow(s/(this.a*this.f0),i),-9999===(h=T(this.e,a)))return null}else h=-e;return n=q(l/this.ns+this.long0),t.x=n,t.y=h,t},names:["Lambert Tangential Conformal Conic Projection","Lambert_Conformal_Conic","Lambert_Conformal_Conic_1SP","Lambert_Conformal_Conic_2SP","lcc","Lambert Conic Conformal (1SP)","Lambert Conic Conformal (2SP)"]};var ns={init:function(){this.a=6377397.155,this.es=.006674372230614,this.e=Math.sqrt(this.es),this.lat0||(this.lat0=.863937979737193),this.long0||(this.long0=.4334234309119251),this.k0||(this.k0=.9999),this.s45=.785398163397448,this.s90=2*this.s45,this.fi0=this.lat0,this.e2=this.es,this.e=Math.sqrt(this.e2),this.alfa=Math.sqrt(1+this.e2*Math.pow(Math.cos(this.fi0),4)/(1-this.e2)),this.uq=1.04216856380474,this.u0=Math.asin(Math.sin(this.fi0)/this.alfa),this.g=Math.pow((1+this.e*Math.sin(this.fi0))/(1-this.e*Math.sin(this.fi0)),this.alfa*this.e/2),this.k=Math.tan(this.u0/2+this.s45)/Math.pow(Math.tan(this.fi0/2+this.s45),this.alfa)*this.g,this.k1=this.k0,this.n0=this.a*Math.sqrt(1-this.e2)/(1-this.e2*Math.pow(Math.sin(this.fi0),2)),this.s0=1.37008346281555,this.n=Math.sin(this.s0),this.ro0=this.k1*this.n0/Math.tan(this.s0),this.ad=this.s90-this.uq},forward:function(t){var s,i,a,e,h,n,r,o=t.x,l=t.y,u=q(o-this.long0);return s=Math.pow((1+this.e*Math.sin(l))/(1-this.e*Math.sin(l)),this.alfa*this.e/2),i=2*(Math.atan(this.k*Math.pow(Math.tan(l/2+this.s45),this.alfa)/s)-this.s45),a=-u*this.alfa,e=Math.asin(Math.cos(this.ad)*Math.sin(i)+Math.sin(this.ad)*Math.cos(i)*Math.cos(a)),h=Math.asin(Math.cos(i)*Math.sin(a)/Math.cos(e)),n=this.n*h,r=this.ro0*Math.pow(Math.tan(this.s0/2+this.s45),this.n)/Math.pow(Math.tan(e/2+this.s45),this.n),t.y=r*Math.cos(n)/1,t.x=r*Math.sin(n)/1,this.czech||(t.y*=-1,t.x*=-1),t},inverse:function(t){var s,i,a,e,h,n,r,o=t.x;t.x=t.y,t.y=o,this.czech||(t.y*=-1,t.x*=-1),h=Math.sqrt(t.x*t.x+t.y*t.y),e=Math.atan2(t.y,t.x)/Math.sin(this.s0),a=2*(Math.atan(Math.pow(this.ro0/h,1/this.n)*Math.tan(this.s0/2+this.s45))-this.s45),s=Math.asin(Math.cos(this.ad)*Math.sin(a)-Math.sin(this.ad)*Math.cos(a)*Math.cos(e)),i=Math.asin(Math.cos(a)*Math.sin(e)/Math.cos(s)),t.x=this.long0-i/this.alfa,n=s,r=0;var l=0;do{t.y=2*(Math.atan(Math.pow(this.k,-1/this.alfa)*Math.pow(Math.tan(s/2+this.s45),1/this.alfa)*Math.pow((1+this.e*Math.sin(n))/(1-this.e*Math.sin(n)),this.e/2))-this.s45),Math.abs(n-t.y)<1e-10&&(r=1),n=t.y,l+=1}while(0===r&&l<15);return l>=15?null:t},names:["Krovak","krovak"]};function rs(t,s,i,a,e){return t*e-s*Math.sin(2*e)+i*Math.sin(4*e)-a*Math.sin(6*e)}function os(t){return 1-.25*t*(1+t/16*(3+1.25*t))}function ls(t){return.375*t*(1+.25*t*(1+.46875*t))}function us(t){return.05859375*t*t*(1+.75*t)}function cs(t){return t*t*t*(35/3072)}function fs(t,s,i){var a=s*i;return t/Math.sqrt(1-a*a)}function Ms(t){return Math.abs(t)<e?t:t-R(t)*Math.PI}function ps(t,s,i,a,e){var h,n;h=t/s;for(var r=0;r<15;r++)if(h+=n=(t-(s*h-i*Math.sin(2*h)+a*Math.sin(4*h)-e*Math.sin(6*h)))/(s-2*i*Math.cos(2*h)+4*a*Math.cos(4*h)-6*e*Math.cos(6*h)),Math.abs(n)<=1e-10)return h;return NaN}var ds={init:function(){this.sphere||(this.e0=os(this.es),this.e1=ls(this.es),this.e2=us(this.es),this.e3=cs(this.es),this.ml0=this.a*rs(this.e0,this.e1,this.e2,this.e3,this.lat0))},forward:function(t){var s,i,a=t.x,e=t.y;if(a=q(a-this.long0),this.sphere)s=this.a*Math.asin(Math.cos(e)*Math.sin(a)),i=this.a*(Math.atan2(Math.tan(e),Math.cos(a))-this.lat0);else{var h=Math.sin(e),n=Math.cos(e),r=fs(this.a,this.e,h),o=Math.tan(e)*Math.tan(e),l=a*Math.cos(e),u=l*l,c=this.es*n*n/(1-this.es);s=r*l*(1-u*o*(1/6-(8-o+8*c)*u/120)),i=this.a*rs(this.e0,this.e1,this.e2,this.e3,e)-this.ml0+r*h/n*u*(.5+(5-o+6*c)*u/24)}return t.x=s+this.x0,t.y=i+this.y0,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var s,i,a=t.x/this.a,n=t.y/this.a;if(this.sphere){var r=n+this.lat0;s=Math.asin(Math.sin(r)*Math.cos(a)),i=Math.atan2(Math.tan(a),Math.cos(r))}else{var o=ps(this.ml0/this.a+n,this.e0,this.e1,this.e2,this.e3);if(Math.abs(Math.abs(o)-e)<=h)return t.x=this.long0,t.y=e,n<0&&(t.y*=-1),t;var l=fs(this.a,this.e,Math.sin(o)),u=l*l*l/this.a/this.a*(1-this.es),c=Math.pow(Math.tan(o),2),f=a*this.a/l,M=f*f;s=o-l*Math.tan(o)/u*f*f*(.5-(1+3*c)*f*f/24),i=f*(1-M*(c/3+(1+3*c)*c*M/15))/Math.cos(o)}return t.x=q(i+this.long0),t.y=Ms(s),t},names:["Cassini","Cassini_Soldner","cass"]};function _s(t,s){var i;return t>1e-7?(1-t*t)*(s/(1-(i=t*s)*i)-.5/t*Math.log((1-i)/(1+i))):2*s}var ms=.3333333333333333,ys=.17222222222222222,xs=.10257936507936508,gs=.06388888888888888,vs=.0664021164021164,bs=.016415012942191543;var ws={init:function(){var t,s=Math.abs(this.lat0);if(Math.abs(s-e)<h?this.mode=this.lat0<0?this.S_POLE:this.N_POLE:Math.abs(s)<h?this.mode=this.EQUIT:this.mode=this.OBLIQ,this.es>0)switch(this.qp=_s(this.e,1),this.mmf=.5/(1-this.es),this.apa=function(t){var s,i=[];return i[0]=t*ms,s=t*t,i[0]+=s*ys,i[1]=s*gs,s*=t,i[0]+=s*xs,i[1]+=s*vs,i[2]=s*bs,i}(this.es),this.mode){case this.N_POLE:case this.S_POLE:this.dd=1;break;case this.EQUIT:this.rq=Math.sqrt(.5*this.qp),this.dd=1/this.rq,this.xmf=1,this.ymf=.5*this.qp;break;case this.OBLIQ:this.rq=Math.sqrt(.5*this.qp),t=Math.sin(this.lat0),this.sinb1=_s(this.e,t)/this.qp,this.cosb1=Math.sqrt(1-this.sinb1*this.sinb1),this.dd=Math.cos(this.lat0)/(Math.sqrt(1-this.es*t*t)*this.rq*this.cosb1),this.ymf=(this.xmf=this.rq)/this.dd,this.xmf*=this.dd}else this.mode===this.OBLIQ&&(this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0))},forward:function(t){var s,i,a,n,r,l,u,c,f,M,p=t.x,d=t.y;if(p=q(p-this.long0),this.sphere){if(r=Math.sin(d),M=Math.cos(d),a=Math.cos(p),this.mode===this.OBLIQ||this.mode===this.EQUIT){if((i=this.mode===this.EQUIT?1+M*a:1+this.sinph0*r+this.cosph0*M*a)<=h)return null;s=(i=Math.sqrt(2/i))*M*Math.sin(p),i*=this.mode===this.EQUIT?r:this.cosph0*r-this.sinph0*M*a}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(a=-a),Math.abs(d+this.lat0)<h)return null;i=o-.5*d,s=(i=2*(this.mode===this.S_POLE?Math.cos(i):Math.sin(i)))*Math.sin(p),i*=a}}else{switch(u=0,c=0,f=0,a=Math.cos(p),n=Math.sin(p),r=Math.sin(d),l=_s(this.e,r),this.mode!==this.OBLIQ&&this.mode!==this.EQUIT||(u=l/this.qp,c=Math.sqrt(1-u*u)),this.mode){case this.OBLIQ:f=1+this.sinb1*u+this.cosb1*c*a;break;case this.EQUIT:f=1+c*a;break;case this.N_POLE:f=e+d,l=this.qp-l;break;case this.S_POLE:f=d-e,l=this.qp+l}if(Math.abs(f)<h)return null;switch(this.mode){case this.OBLIQ:case this.EQUIT:f=Math.sqrt(2/f),i=this.mode===this.OBLIQ?this.ymf*f*(this.cosb1*u-this.sinb1*c*a):(f=Math.sqrt(2/(1+c*a)))*u*this.ymf,s=this.xmf*f*c*n;break;case this.N_POLE:case this.S_POLE:l>=0?(s=(f=Math.sqrt(l))*n,i=a*(this.mode===this.S_POLE?f:-f)):s=i=0}}return t.x=this.a*s+this.x0,t.y=this.a*i+this.y0,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var s,i,a,n,r,o,l,u,c,f,M=t.x/this.a,p=t.y/this.a;if(this.sphere){var d,_=0,m=0;if((i=.5*(d=Math.sqrt(M*M+p*p)))>1)return null;switch(i=2*Math.asin(i),this.mode!==this.OBLIQ&&this.mode!==this.EQUIT||(m=Math.sin(i),_=Math.cos(i)),this.mode){case this.EQUIT:i=Math.abs(d)<=h?0:Math.asin(p*m/d),M*=m,p=_*d;break;case this.OBLIQ:i=Math.abs(d)<=h?this.lat0:Math.asin(_*this.sinph0+p*m*this.cosph0/d),M*=m*this.cosph0,p=(_-Math.sin(i)*this.sinph0)*d;break;case this.N_POLE:p=-p,i=e-i;break;case this.S_POLE:i-=e}s=0!==p||this.mode!==this.EQUIT&&this.mode!==this.OBLIQ?Math.atan2(M,p):0}else{if(l=0,this.mode===this.OBLIQ||this.mode===this.EQUIT){if(M/=this.dd,p*=this.dd,(o=Math.sqrt(M*M+p*p))<h)return t.x=this.long0,t.y=this.lat0,t;n=2*Math.asin(.5*o/this.rq),a=Math.cos(n),M*=n=Math.sin(n),this.mode===this.OBLIQ?(l=a*this.sinb1+p*n*this.cosb1/o,r=this.qp*l,p=o*this.cosb1*a-p*this.sinb1*n):(l=p*n/o,r=this.qp*l,p=o*a)}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(p=-p),!(r=M*M+p*p))return t.x=this.long0,t.y=this.lat0,t;l=1-r/this.qp,this.mode===this.S_POLE&&(l=-l)}s=Math.atan2(M,p),u=Math.asin(l),c=this.apa,f=u+u,i=u+c[0]*Math.sin(f)+c[1]*Math.sin(f+f)+c[2]*Math.sin(f+f+f)}return t.x=q(this.long0+s),t.y=i,t},names:["Lambert Azimuthal Equal Area","Lambert_Azimuthal_Equal_Area","laea"],S_POLE:1,N_POLE:2,EQUIT:3,OBLIQ:4};function Ss(t){return Math.abs(t)>1&&(t=t>1?1:-1),Math.asin(t)}var Ps={init:function(){Math.abs(this.lat1+this.lat2)<h||(this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e3=Math.sqrt(this.es),this.sin_po=Math.sin(this.lat1),this.cos_po=Math.cos(this.lat1),this.t1=this.sin_po,this.con=this.sin_po,this.ms1=A(this.e3,this.sin_po,this.cos_po),this.qs1=_s(this.e3,this.sin_po),this.sin_po=Math.sin(this.lat2),this.cos_po=Math.cos(this.lat2),this.t2=this.sin_po,this.ms2=A(this.e3,this.sin_po,this.cos_po),this.qs2=_s(this.e3,this.sin_po),this.sin_po=Math.sin(this.lat0),this.cos_po=Math.cos(this.lat0),this.t3=this.sin_po,this.qs0=_s(this.e3,this.sin_po),Math.abs(this.lat1-this.lat2)>h?this.ns0=(this.ms1*this.ms1-this.ms2*this.ms2)/(this.qs2-this.qs1):this.ns0=this.con,this.c=this.ms1*this.ms1+this.ns0*this.qs1,this.rh=this.a*Math.sqrt(this.c-this.ns0*this.qs0)/this.ns0)},forward:function(t){var s=t.x,i=t.y;this.sin_phi=Math.sin(i),this.cos_phi=Math.cos(i);var a=_s(this.e3,this.sin_phi),e=this.a*Math.sqrt(this.c-this.ns0*a)/this.ns0,h=this.ns0*q(s-this.long0),n=e*Math.sin(h)+this.x0,r=this.rh-e*Math.cos(h)+this.y0;return t.x=n,t.y=r,t},inverse:function(t){var s,i,a,e,h,n;return t.x-=this.x0,t.y=this.rh-t.y+this.y0,this.ns0>=0?(s=Math.sqrt(t.x*t.x+t.y*t.y),a=1):(s=-Math.sqrt(t.x*t.x+t.y*t.y),a=-1),e=0,0!==s&&(e=Math.atan2(a*t.x,a*t.y)),a=s*this.ns0/this.a,this.sphere?n=Math.asin((this.c-a*a)/(2*this.ns0)):(i=(this.c-a*a)/this.ns0,n=this.phi1z(this.e3,i)),h=q(e/this.ns0+this.long0),t.x=h,t.y=n,t},names:["Albers_Conic_Equal_Area","Albers","aea"],phi1z:function(t,s){var i,a,e,n,r=Ss(.5*s);if(t<h)return r;for(var o=t*t,l=1;l<=25;l++)if(r+=n=.5*(e=1-(a=t*(i=Math.sin(r)))*a)*e/Math.cos(r)*(s/(1-o)-i/e+.5/t*Math.log((1-a)/(1+a))),Math.abs(n)<=1e-7)return r;return null}};var Es={init:function(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0),this.infinity_dist=1e3*this.a,this.rc=1},forward:function(t){var s,i,a,e,n,r,o,l=t.x,u=t.y;return a=q(l-this.long0),s=Math.sin(u),i=Math.cos(u),e=Math.cos(a),(n=this.sin_p14*s+this.cos_p14*i*e)>0||Math.abs(n)<=h?(r=this.x0+1*this.a*i*Math.sin(a)/n,o=this.y0+1*this.a*(this.cos_p14*s-this.sin_p14*i*e)/n):(r=this.x0+this.infinity_dist*i*Math.sin(a),o=this.y0+this.infinity_dist*(this.cos_p14*s-this.sin_p14*i*e)),t.x=r,t.y=o,t},inverse:function(t){var s,i,a,e,h,n;return t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,(s=Math.sqrt(t.x*t.x+t.y*t.y))?(e=Math.atan2(s,this.rc),i=Math.sin(e),n=Ss((a=Math.cos(e))*this.sin_p14+t.y*i*this.cos_p14/s),h=Math.atan2(t.x*i,s*this.cos_p14*a-t.y*this.sin_p14*i),h=q(this.long0+h)):(n=this.phic0,h=0),t.x=h,t.y=n,t},names:["gnom"]};var Gs={init:function(){this.sphere||(this.k0=A(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)))},forward:function(t){var s,i,a=t.x,e=t.y,h=q(a-this.long0);if(this.sphere)s=this.x0+this.a*h*Math.cos(this.lat_ts),i=this.y0+this.a*Math.sin(e)/Math.cos(this.lat_ts);else{var n=_s(this.e,Math.sin(e));s=this.x0+this.a*this.k0*h,i=this.y0+this.a*n*.5/this.k0}return t.x=s,t.y=i,t},inverse:function(t){var s,i;return t.x-=this.x0,t.y-=this.y0,this.sphere?(s=q(this.long0+t.x/this.a/Math.cos(this.lat_ts)),i=Math.asin(t.y/this.a*Math.cos(this.lat_ts))):(i=function(t,s){var i=1-(1-t*t)/(2*t)*Math.log((1-t)/(1+t));if(Math.abs(Math.abs(s)-i)<1e-6)return s<0?-1*e:e;for(var a,h,n,r,o=Math.asin(.5*s),l=0;l<30;l++)if(h=Math.sin(o),n=Math.cos(o),r=t*h,o+=a=Math.pow(1-r*r,2)/(2*n)*(s/(1-t*t)-h/(1-r*r)+.5/t*Math.log((1-r)/(1+r))),Math.abs(a)<=1e-10)return o;return NaN}(this.e,2*t.y*this.k0/this.a),s=q(this.long0+t.x/(this.a*this.k0))),t.x=s,t.y=i,t},names:["cea"]};var Cs={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Equidistant Cylindrical (Plate Carre)",this.rc=Math.cos(this.lat_ts)},forward:function(t){var s=t.x,i=t.y,a=q(s-this.long0),e=Ms(i-this.lat0);return t.x=this.x0+this.a*a*this.rc,t.y=this.y0+this.a*e,t},inverse:function(t){var s=t.x,i=t.y;return t.x=q(this.long0+(s-this.x0)/(this.a*this.rc)),t.y=Ms(this.lat0+(i-this.y0)/this.a),t},names:["Equirectangular","Equidistant_Cylindrical","eqc"]};var Ns={init:function(){this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=os(this.es),this.e1=ls(this.es),this.e2=us(this.es),this.e3=cs(this.es),this.ml0=this.a*rs(this.e0,this.e1,this.e2,this.e3,this.lat0)},forward:function(t){var s,i,a,e=t.x,n=t.y,r=q(e-this.long0);if(a=r*Math.sin(n),this.sphere)Math.abs(n)<=h?(s=this.a*r,i=-1*this.a*this.lat0):(s=this.a*Math.sin(a)/Math.tan(n),i=this.a*(Ms(n-this.lat0)+(1-Math.cos(a))/Math.tan(n)));else if(Math.abs(n)<=h)s=this.a*r,i=-1*this.ml0;else{var o=fs(this.a,this.e,Math.sin(n))/Math.tan(n);s=o*Math.sin(a),i=this.a*rs(this.e0,this.e1,this.e2,this.e3,n)-this.ml0+o*(1-Math.cos(a))}return t.x=s+this.x0,t.y=i+this.y0,t},inverse:function(t){var s,i,a,e,n,r,o,l,u;if(a=t.x-this.x0,e=t.y-this.y0,this.sphere)if(Math.abs(e+this.a*this.lat0)<=h)s=q(a/this.a+this.long0),i=0;else{var c;for(r=this.lat0+e/this.a,o=a*a/this.a/this.a+r*r,l=r,n=20;n;--n)if(l+=u=-1*(r*(l*(c=Math.tan(l))+1)-l-.5*(l*l+o)*c)/((l-r)/c-1),Math.abs(u)<=h){i=l;break}s=q(this.long0+Math.asin(a*Math.tan(l)/this.a)/Math.sin(i))}else if(Math.abs(e+this.ml0)<=h)i=0,s=q(this.long0+a/this.a);else{var f,M,p,d,_;for(r=(this.ml0+e)/this.a,o=a*a/this.a/this.a+r*r,l=r,n=20;n;--n)if(_=this.e*Math.sin(l),f=Math.sqrt(1-_*_)*Math.tan(l),M=this.a*rs(this.e0,this.e1,this.e2,this.e3,l),p=this.e0-2*this.e1*Math.cos(2*l)+4*this.e2*Math.cos(4*l)-6*this.e3*Math.cos(6*l),l-=u=(r*(f*(d=M/this.a)+1)-d-.5*f*(d*d+o))/(this.es*Math.sin(2*l)*(d*d+o-2*r*d)/(4*f)+(r-d)*(f*p-2/Math.sin(2*l))-p),Math.abs(u)<=h){i=l;break}f=Math.sqrt(1-this.es*Math.pow(Math.sin(i),2))*Math.tan(i),s=q(this.long0+Math.asin(a*f/this.a)/Math.sin(i))}return t.x=s,t.y=i,t},names:["Polyconic","poly"]};var ks={init:function(){this.A=[],this.A[1]=.6399175073,this.A[2]=-.1358797613,this.A[3]=.063294409,this.A[4]=-.02526853,this.A[5]=.0117879,this.A[6]=-.0055161,this.A[7]=.0026906,this.A[8]=-.001333,this.A[9]=67e-5,this.A[10]=-34e-5,this.B_re=[],this.B_im=[],this.B_re[1]=.7557853228,this.B_im[1]=0,this.B_re[2]=.249204646,this.B_im[2]=.003371507,this.B_re[3]=-.001541739,this.B_im[3]=.04105856,this.B_re[4]=-.10162907,this.B_im[4]=.01727609,this.B_re[5]=-.26623489,this.B_im[5]=-.36249218,this.B_re[6]=-.6870983,this.B_im[6]=-1.1651967,this.C_re=[],this.C_im=[],this.C_re[1]=1.3231270439,this.C_im[1]=0,this.C_re[2]=-.577245789,this.C_im[2]=-.007809598,this.C_re[3]=.508307513,this.C_im[3]=-.112208952,this.C_re[4]=-.15094762,this.C_im[4]=.18200602,this.C_re[5]=1.01418179,this.C_im[5]=1.64497696,this.C_re[6]=1.9660549,this.C_im[6]=2.5127645,this.D=[],this.D[1]=1.5627014243,this.D[2]=.5185406398,this.D[3]=-.03333098,this.D[4]=-.1052906,this.D[5]=-.0368594,this.D[6]=.007317,this.D[7]=.0122,this.D[8]=.00394,this.D[9]=-.0013},forward:function(t){var s,i=t.x,e=t.y-this.lat0,h=i-this.long0,n=e/a*1e-5,r=h,o=1,l=0;for(s=1;s<=10;s++)o*=n,l+=this.A[s]*o;var u,c=l,f=r,M=1,p=0,d=0,_=0;for(s=1;s<=6;s++)u=p*c+M*f,M=M*c-p*f,p=u,d=d+this.B_re[s]*M-this.B_im[s]*p,_=_+this.B_im[s]*M+this.B_re[s]*p;return t.x=_*this.a+this.x0,t.y=d*this.a+this.y0,t},inverse:function(t){var s,i,e=t.x,h=t.y,n=e-this.x0,r=(h-this.y0)/this.a,o=n/this.a,l=1,u=0,c=0,f=0;for(s=1;s<=6;s++)i=u*r+l*o,l=l*r-u*o,u=i,c=c+this.C_re[s]*l-this.C_im[s]*u,f=f+this.C_im[s]*l+this.C_re[s]*u;for(var M=0;M<this.iterations;M++){var p,d=c,_=f,m=r,y=o;for(s=2;s<=6;s++)p=_*c+d*f,d=d*c-_*f,_=p,m+=(s-1)*(this.B_re[s]*d-this.B_im[s]*_),y+=(s-1)*(this.B_im[s]*d+this.B_re[s]*_);d=1,_=0;var x=this.B_re[1],g=this.B_im[1];for(s=2;s<=6;s++)p=_*c+d*f,d=d*c-_*f,_=p,x+=s*(this.B_re[s]*d-this.B_im[s]*_),g+=s*(this.B_im[s]*d+this.B_re[s]*_);var v=x*x+g*g;c=(m*x+y*g)/v,f=(y*x-m*g)/v}var b=c,w=f,S=1,P=0;for(s=1;s<=9;s++)S*=b,P+=this.D[s]*S;var E=this.lat0+P*a*1e5,G=this.long0+w;return t.x=G,t.y=E,t},names:["New_Zealand_Map_Grid","nzmg"]};var js={init:function(){},forward:function(t){var s=t.x,i=t.y,a=q(s-this.long0),e=this.x0+this.a*a,h=this.y0+this.a*Math.log(Math.tan(Math.PI/4+i/2.5))*1.25;return t.x=e,t.y=h,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var s=q(this.long0+t.x/this.a),i=2.5*(Math.atan(Math.exp(.8*t.y/this.a))-Math.PI/4);return t.x=s,t.y=i,t},names:["Miller_Cylindrical","mill"]};var Is={init:function(){this.sphere?(this.n=1,this.m=0,this.es=0,this.C_y=Math.sqrt((this.m+1)/this.n),this.C_x=this.C_y/(this.m+1)):this.en=Dt(this.es)},forward:function(t){var s,i,a=t.x,e=t.y;if(a=q(a-this.long0),this.sphere){if(this.m)for(var n=this.n*Math.sin(e),r=20;r;--r){var o=(this.m*e+Math.sin(e)-n)/(this.m+Math.cos(e));if(e-=o,Math.abs(o)<h)break}else e=1!==this.n?Math.asin(this.n*Math.sin(e)):e;s=this.a*this.C_x*a*(this.m+Math.cos(e)),i=this.a*this.C_y*e}else{var l=Math.sin(e),u=Math.cos(e);i=this.a*Ft(e,l,u,this.en),s=this.a*a*u/Math.sqrt(1-this.es*l*l)}return t.x=s,t.y=i,t},inverse:function(t){var s,i,a;return t.x-=this.x0,i=t.x/this.a,t.y-=this.y0,s=t.y/this.a,this.sphere?(s/=this.C_y,i/=this.C_x*(this.m+Math.cos(s)),this.m?s=Ss((this.m*s+Math.sin(s))/this.n):1!==this.n&&(s=Ss(Math.sin(s)/this.n)),i=q(i+this.long0),s=Ms(s)):(s=Ut(t.y/this.a,this.es,this.en),(a=Math.abs(s))<e?(a=Math.sin(s),i=q(this.long0+t.x*Math.sqrt(1-this.es*a*a)/(this.a*Math.cos(s)))):a-h<e&&(i=this.long0)),t.x=i,t.y=s,t},names:["Sinusoidal","sinu"]};var Os={init:function(){},forward:function(t){for(var s=t.x,i=t.y,a=q(s-this.long0),e=i,n=Math.PI*Math.sin(i);;){var r=-(e+Math.sin(e)-n)/(1+Math.cos(e));if(e+=r,Math.abs(r)<h)break}e/=2,Math.PI/2-Math.abs(i)<h&&(a=0);var o=.900316316158*this.a*a*Math.cos(e)+this.x0,l=1.4142135623731*this.a*Math.sin(e)+this.y0;return t.x=o,t.y=l,t},inverse:function(t){var s,i;t.x-=this.x0,t.y-=this.y0,i=t.y/(1.4142135623731*this.a),Math.abs(i)>.999999999999&&(i=.999999999999),s=Math.asin(i);var a=q(this.long0+t.x/(.900316316158*this.a*Math.cos(s)));a<-Math.PI&&(a=-Math.PI),a>Math.PI&&(a=Math.PI),i=(2*s+Math.sin(2*s))/Math.PI,Math.abs(i)>1&&(i=1);var e=Math.asin(i);return t.x=a,t.y=e,t},names:["Mollweide","moll"]};var As={init:function(){Math.abs(this.lat1+this.lat2)<h||(this.lat2=this.lat2||this.lat1,this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=os(this.es),this.e1=ls(this.es),this.e2=us(this.es),this.e3=cs(this.es),this.sinphi=Math.sin(this.lat1),this.cosphi=Math.cos(this.lat1),this.ms1=A(this.e,this.sinphi,this.cosphi),this.ml1=rs(this.e0,this.e1,this.e2,this.e3,this.lat1),Math.abs(this.lat1-this.lat2)<h?this.ns=this.sinphi:(this.sinphi=Math.sin(this.lat2),this.cosphi=Math.cos(this.lat2),this.ms2=A(this.e,this.sinphi,this.cosphi),this.ml2=rs(this.e0,this.e1,this.e2,this.e3,this.lat2),this.ns=(this.ms1-this.ms2)/(this.ml2-this.ml1)),this.g=this.ml1+this.ms1/this.ns,this.ml0=rs(this.e0,this.e1,this.e2,this.e3,this.lat0),this.rh=this.a*(this.g-this.ml0))},forward:function(t){var s,i=t.x,a=t.y;if(this.sphere)s=this.a*(this.g-a);else{var e=rs(this.e0,this.e1,this.e2,this.e3,a);s=this.a*(this.g-e)}var h=this.ns*q(i-this.long0),n=this.x0+s*Math.sin(h),r=this.y0+this.rh-s*Math.cos(h);return t.x=n,t.y=r,t},inverse:function(t){var s,i,a,e;t.x-=this.x0,t.y=this.rh-t.y+this.y0,this.ns>=0?(i=Math.sqrt(t.x*t.x+t.y*t.y),s=1):(i=-Math.sqrt(t.x*t.x+t.y*t.y),s=-1);var h=0;return 0!==i&&(h=Math.atan2(s*t.x,s*t.y)),this.sphere?(e=q(this.long0+h/this.ns),a=Ms(this.g-i/this.a),t.x=e,t.y=a,t):(a=ps(this.g-i/this.a,this.e0,this.e1,this.e2,this.e3),e=q(this.long0+h/this.ns),t.x=e,t.y=a,t)},names:["Equidistant_Conic","eqdc"]};var Rs={init:function(){this.R=this.a},forward:function(t){var s,i,a=t.x,n=t.y,r=q(a-this.long0);Math.abs(n)<=h&&(s=this.x0+this.R*r,i=this.y0);var o=Ss(2*Math.abs(n/Math.PI));(Math.abs(r)<=h||Math.abs(Math.abs(n)-e)<=h)&&(s=this.x0,i=n>=0?this.y0+Math.PI*this.R*Math.tan(.5*o):this.y0+Math.PI*this.R*-Math.tan(.5*o));var l=.5*Math.abs(Math.PI/r-r/Math.PI),u=l*l,c=Math.sin(o),f=Math.cos(o),M=f/(c+f-1),p=M*M,d=M*(2/c-1),_=d*d,m=Math.PI*this.R*(l*(M-_)+Math.sqrt(u*(M-_)*(M-_)-(_+u)*(p-_)))/(_+u);r<0&&(m=-m),s=this.x0+m;var y=u+M;return m=Math.PI*this.R*(d*y-l*Math.sqrt((_+u)*(u+1)-y*y))/(_+u),i=n>=0?this.y0+m:this.y0-m,t.x=s,t.y=i,t},inverse:function(t){var s,i,a,e,n,r,o,l,u,c,f,M;return t.x-=this.x0,t.y-=this.y0,f=Math.PI*this.R,n=(a=t.x/f)*a+(e=t.y/f)*e,f=3*(e*e/(l=-2*(r=-Math.abs(e)*(1+n))+1+2*e*e+n*n)+(2*(o=r-2*e*e+a*a)*o*o/l/l/l-9*r*o/l/l)/27)/(u=(r-o*o/3/l)/l)/(c=2*Math.sqrt(-u/3)),Math.abs(f)>1&&(f=f>=0?1:-1),M=Math.acos(f)/3,i=t.y>=0?(-c*Math.cos(M+Math.PI/3)-o/3/l)*Math.PI:-(-c*Math.cos(M+Math.PI/3)-o/3/l)*Math.PI,s=Math.abs(a)<h?this.long0:q(this.long0+Math.PI*(n-1+Math.sqrt(1+2*(a*a-e*e)+n*n))/2/a),t.x=s,t.y=i,t},names:["Van_der_Grinten_I","VanDerGrinten","vandg"]};var qs={init:function(){this.sin_p12=Math.sin(this.lat0),this.cos_p12=Math.cos(this.lat0)},forward:function(t){var s,i,a,n,r,o,l,u,c,f,M,p,d,_,m,y,x,g,v,b,w,S,P=t.x,E=t.y,G=Math.sin(t.y),C=Math.cos(t.y),N=q(P-this.long0);return this.sphere?Math.abs(this.sin_p12-1)<=h?(t.x=this.x0+this.a*(e-E)*Math.sin(N),t.y=this.y0-this.a*(e-E)*Math.cos(N),t):Math.abs(this.sin_p12+1)<=h?(t.x=this.x0+this.a*(e+E)*Math.sin(N),t.y=this.y0+this.a*(e+E)*Math.cos(N),t):(g=this.sin_p12*G+this.cos_p12*C*Math.cos(N),x=(y=Math.acos(g))?y/Math.sin(y):1,t.x=this.x0+this.a*x*C*Math.sin(N),t.y=this.y0+this.a*x*(this.cos_p12*G-this.sin_p12*C*Math.cos(N)),t):(s=os(this.es),i=ls(this.es),a=us(this.es),n=cs(this.es),Math.abs(this.sin_p12-1)<=h?(r=this.a*rs(s,i,a,n,e),o=this.a*rs(s,i,a,n,E),t.x=this.x0+(r-o)*Math.sin(N),t.y=this.y0-(r-o)*Math.cos(N),t):Math.abs(this.sin_p12+1)<=h?(r=this.a*rs(s,i,a,n,e),o=this.a*rs(s,i,a,n,E),t.x=this.x0+(r+o)*Math.sin(N),t.y=this.y0+(r+o)*Math.cos(N),t):(l=G/C,u=fs(this.a,this.e,this.sin_p12),c=fs(this.a,this.e,G),f=Math.atan((1-this.es)*l+this.es*u*this.sin_p12/(c*C)),v=0===(M=Math.atan2(Math.sin(N),this.cos_p12*Math.tan(f)-this.sin_p12*Math.cos(N)))?Math.asin(this.cos_p12*Math.sin(f)-this.sin_p12*Math.cos(f)):Math.abs(Math.abs(M)-Math.PI)<=h?-Math.asin(this.cos_p12*Math.sin(f)-this.sin_p12*Math.cos(f)):Math.asin(Math.sin(N)*Math.cos(f)/Math.sin(M)),p=this.e*this.sin_p12/Math.sqrt(1-this.es),y=u*v*(1-(b=v*v)*(m=(d=this.e*this.cos_p12*Math.cos(M)/Math.sqrt(1-this.es))*d)*(1-m)/6+(w=b*v)/8*(_=p*d)*(1-2*m)+(S=w*v)/120*(m*(4-7*m)-3*p*p*(1-7*m))-S*v/48*_),t.x=this.x0+y*Math.sin(M),t.y=this.y0+y*Math.cos(M),t))},inverse:function(t){var s,i,a,n,r,o,l,u,c,f,M,p,d,_,m,y,x,g,v,b,w,S,P;if(t.x-=this.x0,t.y-=this.y0,this.sphere){if((s=Math.sqrt(t.x*t.x+t.y*t.y))>2*e*this.a)return;return i=s/this.a,a=Math.sin(i),n=Math.cos(i),r=this.long0,Math.abs(s)<=h?o=this.lat0:(o=Ss(n*this.sin_p12+t.y*a*this.cos_p12/s),l=Math.abs(this.lat0)-e,r=Math.abs(l)<=h?this.lat0>=0?q(this.long0+Math.atan2(t.x,-t.y)):q(this.long0-Math.atan2(-t.x,t.y)):q(this.long0+Math.atan2(t.x*a,s*this.cos_p12*n-t.y*this.sin_p12*a))),t.x=r,t.y=o,t}return u=os(this.es),c=ls(this.es),f=us(this.es),M=cs(this.es),Math.abs(this.sin_p12-1)<=h?(o=ps(((p=this.a*rs(u,c,f,M,e))-(s=Math.sqrt(t.x*t.x+t.y*t.y)))/this.a,u,c,f,M),r=q(this.long0+Math.atan2(t.x,-1*t.y)),t.x=r,t.y=o,t):Math.abs(this.sin_p12+1)<=h?(p=this.a*rs(u,c,f,M,e),o=ps(((s=Math.sqrt(t.x*t.x+t.y*t.y))-p)/this.a,u,c,f,M),r=q(this.long0+Math.atan2(t.x,t.y)),t.x=r,t.y=o,t):(s=Math.sqrt(t.x*t.x+t.y*t.y),m=Math.atan2(t.x,t.y),d=fs(this.a,this.e,this.sin_p12),y=Math.cos(m),g=-(x=this.e*this.cos_p12*y)*x/(1-this.es),v=3*this.es*(1-g)*this.sin_p12*this.cos_p12*y/(1-this.es),S=1-g*(w=(b=s/d)-g*(1+g)*Math.pow(b,3)/6-v*(1+3*g)*Math.pow(b,4)/24)*w/2-b*w*w*w/6,_=Math.asin(this.sin_p12*Math.cos(w)+this.cos_p12*Math.sin(w)*y),r=q(this.long0+Math.asin(Math.sin(m)*Math.sin(w)/Math.cos(_))),P=Math.sin(_),o=Math.atan2((P-this.es*S*this.sin_p12)*Math.tan(_),P*(1-this.es)),t.x=r,t.y=o,t)},names:["Azimuthal_Equidistant","aeqd"]};var Ls={init:function(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0)},forward:function(t){var s,i,a,e,n,r,o,l=t.x,u=t.y;return a=q(l-this.long0),s=Math.sin(u),i=Math.cos(u),e=Math.cos(a),((n=this.sin_p14*s+this.cos_p14*i*e)>0||Math.abs(n)<=h)&&(r=1*this.a*i*Math.sin(a),o=this.y0+1*this.a*(this.cos_p14*s-this.sin_p14*i*e)),t.x=r,t.y=o,t},inverse:function(t){var s,i,a,n,r,o,l;return t.x-=this.x0,t.y-=this.y0,i=Ss((s=Math.sqrt(t.x*t.x+t.y*t.y))/this.a),a=Math.sin(i),n=Math.cos(i),o=this.long0,Math.abs(s)<=h?(l=this.lat0,t.x=o,t.y=l,t):(l=Ss(n*this.sin_p14+t.y*a*this.cos_p14/s),r=Math.abs(this.lat0)-e,Math.abs(r)<=h?(o=this.lat0>=0?q(this.long0+Math.atan2(t.x,-t.y)):q(this.long0-Math.atan2(-t.x,t.y)),t.x=o,t.y=l,t):(o=q(this.long0+Math.atan2(t.x*a,s*this.cos_p14*n-t.y*this.sin_p14*a)),t.x=o,t.y=l,t))},names:["ortho"]},Ts=1,zs=2,Bs=3,Ds=4,Fs=5,Us=6,Qs=1,Ws=2,Hs=3,Zs=4;function Js(t,s,i,a){var n;return t<h?(a.value=Qs,n=0):(n=Math.atan2(s,i),Math.abs(n)<=o?a.value=Qs:n>o&&n<=e+o?(a.value=Ws,n-=e):n>e+o||n<=-(e+o)?(a.value=Hs,n=n>=0?n-u:n+u):(a.value=Zs,n+=e)),n}function Vs(t,s){var i=t+s;return i<-u?i+=l:i>+u&&(i-=l),i}var Xs={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Quadrilateralized Spherical Cube",this.lat0>=e-o/2?this.face=Fs:this.lat0<=-(e-o/2)?this.face=Us:Math.abs(this.long0)<=o?this.face=Ts:Math.abs(this.long0)<=e+o?this.face=this.long0>0?zs:Ds:this.face=Bs,0!==this.es&&(this.one_minus_f=1-(this.a-this.b)/this.a,this.one_minus_f_squared=this.one_minus_f*this.one_minus_f)},forward:function(t){var s,i,a,h,n,r,l={x:0,y:0},c={value:0};if(t.x-=this.long0,s=0!==this.es?Math.atan(this.one_minus_f_squared*Math.tan(t.y)):t.y,i=t.x,this.face===Fs)h=e-s,i>=o&&i<=e+o?(c.value=Qs,a=i-e):i>e+o||i<=-(e+o)?(c.value=Ws,a=i>0?i-u:i+u):i>-(e+o)&&i<=-o?(c.value=Hs,a=i+e):(c.value=Zs,a=i);else if(this.face===Us)h=e+s,i>=o&&i<=e+o?(c.value=Qs,a=-i+e):i<o&&i>=-o?(c.value=Ws,a=-i):i<-o&&i>=-(e+o)?(c.value=Hs,a=-i-e):(c.value=Zs,a=i>0?-i+u:-i-u);else{var f,M,p,d,_,m;this.face===zs?i=Vs(i,+e):this.face===Bs?i=Vs(i,+u):this.face===Ds&&(i=Vs(i,-e)),d=Math.sin(s),_=Math.cos(s),m=Math.sin(i),f=_*Math.cos(i),M=_*m,p=d,this.face===Ts?a=Js(h=Math.acos(f),p,M,c):this.face===zs?a=Js(h=Math.acos(M),p,-f,c):this.face===Bs?a=Js(h=Math.acos(-f),p,-M,c):this.face===Ds?a=Js(h=Math.acos(-M),p,f,c):(h=a=0,c.value=Qs)}return r=Math.atan(12/u*(a+Math.acos(Math.sin(a)*Math.cos(o))-e)),n=Math.sqrt((1-Math.cos(h))/(Math.cos(r)*Math.cos(r))/(1-Math.cos(Math.atan(1/Math.cos(a))))),c.value===Ws?r+=e:c.value===Hs?r+=u:c.value===Zs&&(r+=1.5*u),l.x=n*Math.cos(r),l.y=n*Math.sin(r),l.x=l.x*this.a+this.x0,l.y=l.y*this.a+this.y0,t.x=l.x,t.y=l.y,t},inverse:function(t){var s,i,a,h,n,r,o,l,c,f,M,p,d={lam:0,phi:0},_={value:0};if(t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,i=Math.atan(Math.sqrt(t.x*t.x+t.y*t.y)),s=Math.atan2(t.y,t.x),t.x>=0&&t.x>=Math.abs(t.y)?_.value=Qs:t.y>=0&&t.y>=Math.abs(t.x)?(_.value=Ws,s-=e):t.x<0&&-t.x>=Math.abs(t.y)?(_.value=Hs,s=s<0?s+u:s-u):(_.value=Zs,s+=e),c=u/12*Math.tan(s),n=Math.sin(c)/(Math.cos(c)-1/Math.sqrt(2)),r=Math.atan(n),(o=1-(a=Math.cos(s))*a*(h=Math.tan(i))*h*(1-Math.cos(Math.atan(1/Math.cos(r)))))<-1?o=-1:o>1&&(o=1),this.face===Fs)l=Math.acos(o),d.phi=e-l,_.value===Qs?d.lam=r+e:_.value===Ws?d.lam=r<0?r+u:r-u:_.value===Hs?d.lam=r-e:d.lam=r;else if(this.face===Us)l=Math.acos(o),d.phi=l-e,_.value===Qs?d.lam=-r+e:_.value===Ws?d.lam=-r:_.value===Hs?d.lam=-r-e:d.lam=r<0?-r-u:-r+u;else{var m,y,x;c=(m=o)*m,y=(c+=(x=c>=1?0:Math.sqrt(1-c)*Math.sin(r))*x)>=1?0:Math.sqrt(1-c),_.value===Ws?(c=y,y=-x,x=c):_.value===Hs?(y=-y,x=-x):_.value===Zs&&(c=y,y=x,x=-c),this.face===zs?(c=m,m=-y,y=c):this.face===Bs?(m=-m,y=-y):this.face===Ds&&(c=m,m=y,y=-c),d.phi=Math.acos(-x)-e,d.lam=Math.atan2(y,m),this.face===zs?d.lam=Vs(d.lam,-e):this.face===Bs?d.lam=Vs(d.lam,-u):this.face===Ds&&(d.lam=Vs(d.lam,+e))}return 0!==this.es&&(f=d.phi<0?1:0,M=Math.tan(d.phi),p=this.b/Math.sqrt(M*M+this.one_minus_f_squared),d.phi=Math.atan(Math.sqrt(this.a*this.a-p*p)/(this.one_minus_f*p)),f&&(d.phi=-d.phi)),d.lam+=this.long0,t.x=d.lam,t.y=d.phi,t},names:["Quadrilateralized Spherical Cube","Quadrilateralized_Spherical_Cube","qsc"]},Ks=[[1,22199e-21,-715515e-10,31103e-10],[.9986,-482243e-9,-24897e-9,-13309e-10],[.9954,-83103e-8,-448605e-10,-9.86701e-7],[.99,-.00135364,-59661e-9,36777e-10],[.9822,-.00167442,-449547e-11,-572411e-11],[.973,-.00214868,-903571e-10,1.8736e-8],[.96,-.00305085,-900761e-10,164917e-11],[.9427,-.00382792,-653386e-10,-26154e-10],[.9216,-.00467746,-10457e-8,481243e-11],[.8962,-.00536223,-323831e-10,-543432e-11],[.8679,-.00609363,-113898e-9,332484e-11],[.835,-.00698325,-640253e-10,9.34959e-7],[.7986,-.00755338,-500009e-10,9.35324e-7],[.7597,-.00798324,-35971e-9,-227626e-11],[.7186,-.00851367,-701149e-10,-86303e-10],[.6732,-.00986209,-199569e-9,191974e-10],[.6213,-.010418,883923e-10,624051e-11],[.5722,-.00906601,182e-6,624051e-11],[.5322,-.00677797,275608e-9,624051e-11]],Ys=[[-520417e-23,.0124,121431e-23,-845284e-16],[.062,.0124,-1.26793e-9,4.22642e-10],[.124,.0124,5.07171e-9,-1.60604e-9],[.186,.0123999,-1.90189e-8,6.00152e-9],[.248,.0124002,7.10039e-8,-2.24e-8],[.31,.0123992,-2.64997e-7,8.35986e-8],[.372,.0124029,9.88983e-7,-3.11994e-7],[.434,.0123893,-369093e-11,-4.35621e-7],[.4958,.0123198,-102252e-10,-3.45523e-7],[.5571,.0121916,-154081e-10,-5.82288e-7],[.6176,.0119938,-241424e-10,-5.25327e-7],[.6769,.011713,-320223e-10,-5.16405e-7],[.7346,.0113541,-397684e-10,-6.09052e-7],[.7903,.0109107,-489042e-10,-104739e-11],[.8435,.0103431,-64615e-9,-1.40374e-9],[.8936,.00969686,-64636e-9,-8547e-9],[.9394,.00840947,-192841e-9,-42106e-10],[.9761,.00616527,-256e-6,-42106e-10],[1,.00328947,-319159e-9,-42106e-10]],$s=.8487,ti=1.3523,si=r/5,ii=1/si,ai=18,ei=function(t,s){return t[0]+s*(t[1]+s*(t[2]+s*t[3]))};var hi={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.long0=this.long0||0,this.es=0,this.title=this.title||"Robinson"},forward:function(t){var s=q(t.x-this.long0),i=Math.abs(t.y),a=Math.floor(i*si);a<0?a=0:a>=ai&&(a=17);var e={x:ei(Ks[a],i=r*(i-ii*a))*s,y:ei(Ys[a],i)};return t.y<0&&(e.y=-e.y),e.x=e.x*this.a*$s+this.x0,e.y=e.y*this.a*ti+this.y0,e},inverse:function(t){var s={x:(t.x-this.x0)/(this.a*$s),y:Math.abs(t.y-this.y0)/(this.a*ti)};if(s.y>=1)s.x/=Ks[18][0],s.y=t.y<0?-e:e;else{var i=Math.floor(s.y*ai);for(i<0?i=0:i>=ai&&(i=17);;)if(Ys[i][0]>s.y)--i;else{if(!(Ys[i+1][0]<=s.y))break;++i}var a=Ys[i],r=5*(s.y-a[0])/(Ys[i+1][0]-a[0]);r=function(t,s,i,a){for(var e=s;a;--a){var h=t(e);if(e-=h,Math.abs(h)<i)break}return e}((function(t){return(ei(a,t)-s.y)/function(t,s){return t[1]+s*(2*t[2]+3*s*t[3])}(a,t)}),r,h,100),s.x/=ei(Ks[i],r),s.y=(5*i+r)*n,t.y<0&&(s.y=-s.y)}return s.x=q(s.x+this.long0),s},names:["Robinson","robin"]};var ni={init:function(){this.name="geocent"},forward:function(t){return ht(t,this.es,this.a)},inverse:function(t){return nt(t,this.es,this.a,this.b)},names:["Geocentric","geocentric","geocent","Geocent"]},ri=0,oi=1,li=2,ui=3,ci={h:{def:1e5,num:!0},azi:{def:0,num:!0,degrees:!0},tilt:{def:0,num:!0,degrees:!0},long0:{def:0,num:!0},lat0:{def:0,num:!0}};var fi={init:function(){if(Object.keys(ci).forEach(function(t){if(void 0===this[t])this[t]=ci[t].def;else{if(ci[t].num&&isNaN(this[t]))throw new Error("Invalid parameter value, must be numeric "+t+" = "+this[t]);ci[t].num&&(this[t]=parseFloat(this[t]))}ci[t].degrees&&(this[t]=this[t]*n)}.bind(this)),Math.abs(Math.abs(this.lat0)-e)<h?this.mode=this.lat0<0?oi:ri:Math.abs(this.lat0)<h?this.mode=li:(this.mode=ui,this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0)),this.pn1=this.h/this.a,this.pn1<=0||this.pn1>1e10)throw new Error("Invalid height");this.p=1+this.pn1,this.rp=1/this.p,this.h1=1/this.pn1,this.pfact=(this.p+1)*this.h1,this.es=0;var t=this.tilt,s=this.azi;this.cg=Math.cos(s),this.sg=Math.sin(s),this.cw=Math.cos(t),this.sw=Math.sin(t)},forward:function(t){t.x-=this.long0;var s,i,a,e,h=Math.sin(t.y),n=Math.cos(t.y),r=Math.cos(t.x);switch(this.mode){case ui:i=this.sinph0*h+this.cosph0*n*r;break;case li:i=n*r;break;case oi:i=-h;break;case ri:i=h}switch(s=(i=this.pn1/(this.p-i))*n*Math.sin(t.x),this.mode){case ui:i*=this.cosph0*h-this.sinph0*n*r;break;case li:i*=h;break;case ri:i*=-n*r;break;case oi:i*=n*r}return e=1/((a=i*this.cg+s*this.sg)*this.sw*this.h1+this.cw),s=(s*this.cg-i*this.sg)*this.cw*e,i=a*e,t.x=s*this.a,t.y=i*this.a,t},inverse:function(t){t.x/=this.a,t.y/=this.a;var s,i,a,e={x:t.x,y:t.y};a=1/(this.pn1-t.y*this.sw),s=this.pn1*t.x*a,i=this.pn1*t.y*this.cw*a,t.x=s*this.cg+i*this.sg,t.y=i*this.cg-s*this.sg;var n=Ht(t.x,t.y);if(Math.abs(n)<h)e.x=0,e.y=t.y;else{var r,o;switch(o=1-n*n*this.pfact,o=(this.p-Math.sqrt(o))/(this.pn1/n+n/this.pn1),r=Math.sqrt(1-o*o),this.mode){case ui:e.y=Math.asin(r*this.sinph0+t.y*o*this.cosph0/n),t.y=(r-this.sinph0*Math.sin(e.y))*n,t.x*=o*this.cosph0;break;case li:e.y=Math.asin(t.y*o/n),t.y=r*n,t.x*=o;break;case ri:e.y=Math.asin(r),t.y=-t.y;break;case oi:e.y=-Math.asin(r)}e.x=Math.atan2(t.x,t.y)}return t.x=e.x+this.long0,t.y=e.y,t},names:["Tilted_Perspective","tpers"]};var Mi={init:function(){if(this.flip_axis="x"===this.sweep?1:0,this.h=Number(this.h),this.radius_g_1=this.h/this.a,this.radius_g_1<=0||this.radius_g_1>1e10)throw new Error;if(this.radius_g=1+this.radius_g_1,this.C=this.radius_g*this.radius_g-1,0!==this.es){var t=1-this.es,s=1/t;this.radius_p=Math.sqrt(t),this.radius_p2=t,this.radius_p_inv2=s,this.shape="ellipse"}else this.radius_p=1,this.radius_p2=1,this.radius_p_inv2=1,this.shape="sphere";this.title||(this.title="Geostationary Satellite View")},forward:function(t){var s,i,a,e,h=t.x,n=t.y;if(h-=this.long0,"ellipse"===this.shape){n=Math.atan(this.radius_p2*Math.tan(n));var r=this.radius_p/Ht(this.radius_p*Math.cos(n),Math.sin(n));if(i=r*Math.cos(h)*Math.cos(n),a=r*Math.sin(h)*Math.cos(n),e=r*Math.sin(n),(this.radius_g-i)*i-a*a-e*e*this.radius_p_inv2<0)return t.x=Number.NaN,t.y=Number.NaN,t;s=this.radius_g-i,this.flip_axis?(t.x=this.radius_g_1*Math.atan(a/Ht(e,s)),t.y=this.radius_g_1*Math.atan(e/s)):(t.x=this.radius_g_1*Math.atan(a/s),t.y=this.radius_g_1*Math.atan(e/Ht(a,s)))}else"sphere"===this.shape&&(s=Math.cos(n),i=Math.cos(h)*s,a=Math.sin(h)*s,e=Math.sin(n),s=this.radius_g-i,this.flip_axis?(t.x=this.radius_g_1*Math.atan(a/Ht(e,s)),t.y=this.radius_g_1*Math.atan(e/s)):(t.x=this.radius_g_1*Math.atan(a/s),t.y=this.radius_g_1*Math.atan(e/Ht(a,s))));return t.x=t.x*this.a,t.y=t.y*this.a,t},inverse:function(t){var s,i,a,e,h=-1,n=0,r=0;if(t.x=t.x/this.a,t.y=t.y/this.a,"ellipse"===this.shape){this.flip_axis?(r=Math.tan(t.y/this.radius_g_1),n=Math.tan(t.x/this.radius_g_1)*Ht(1,r)):(n=Math.tan(t.x/this.radius_g_1),r=Math.tan(t.y/this.radius_g_1)*Ht(1,n));var o=r/this.radius_p;if(s=n*n+o*o+h*h,(a=(i=2*this.radius_g*h)*i-4*s*this.C)<0)return t.x=Number.NaN,t.y=Number.NaN,t;e=(-i-Math.sqrt(a))/(2*s),h=this.radius_g+e*h,n*=e,r*=e,t.x=Math.atan2(n,h),t.y=Math.atan(r*Math.cos(t.x)/h),t.y=Math.atan(this.radius_p_inv2*Math.tan(t.y))}else if("sphere"===this.shape){if(this.flip_axis?(r=Math.tan(t.y/this.radius_g_1),n=Math.tan(t.x/this.radius_g_1)*Math.sqrt(1+r*r)):(n=Math.tan(t.x/this.radius_g_1),r=Math.tan(t.y/this.radius_g_1)*Math.sqrt(1+n*n)),s=n*n+r*r+h*h,(a=(i=2*this.radius_g*h)*i-4*s*this.C)<0)return t.x=Number.NaN,t.y=Number.NaN,t;e=(-i-Math.sqrt(a))/(2*s),h=this.radius_g+e*h,n*=e,r*=e,t.x=Math.atan2(n,h),t.y=Math.atan(r*Math.cos(t.x)/h)}return t.x=t.x+this.long0,t},names:["Geostationary Satellite View","Geostationary_Satellite","geos"]},pi=1.340264,di=-.081106,_i=893e-6,mi=.003796,yi=Math.sqrt(3)/2;var xi={init:function(){this.es=0,this.long0=void 0!==this.long0?this.long0:0},forward:function(t){var s=q(t.x-this.long0),i=t.y,a=Math.asin(yi*Math.sin(i)),e=a*a,h=e*e*e;return t.x=s*Math.cos(a)/(yi*(pi+3*di*e+h*(7*_i+9*mi*e))),t.y=a*(pi+di*e+h*(_i+mi*e)),t.x=this.a*t.x+this.x0,t.y=this.a*t.y+this.y0,t},inverse:function(t){t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a;var s,i,a,e,h=t.y;for(e=0;e<12&&(h-=a=(h*(pi+di*(s=h*h)+(i=s*s*s)*(_i+mi*s))-t.y)/(pi+3*di*s+i*(7*_i+9*mi*s)),!(Math.abs(a)<1e-9));++e);return i=(s=h*h)*s*s,t.x=yi*t.x*(pi+3*di*s+i*(7*_i+9*mi*s))/Math.cos(h),t.y=Math.asin(Math.sin(h)/yi),t.x=q(t.x+this.long0),t},names:["eqearth","Equal Earth","Equal_Earth"]},gi=1e-10;function vi(t){var s,i,a,e=q(t.x-(this.long0||0)),h=t.y;return s=this.am1+this.m1-Ft(h,i=Math.sin(h),a=Math.cos(h),this.en),i=a*e/(s*Math.sqrt(1-this.es*i*i)),t.x=s*Math.sin(i),t.y=this.am1-s*Math.cos(i),t.x=this.a*t.x+(this.x0||0),t.y=this.a*t.y+(this.y0||0),t}function bi(t){var s,i,a,h;if(t.x=(t.x-(this.x0||0))/this.a,t.y=(t.y-(this.y0||0))/this.a,i=Ht(t.x,t.y=this.am1-t.y),h=Ut(this.am1+this.m1-i,this.es,this.en),(s=Math.abs(h))<e)s=Math.sin(h),a=i*Math.atan2(t.x,t.y)*Math.sqrt(1-this.es*s*s)/Math.cos(h);else{if(!(Math.abs(s-e)<=gi))throw new Error;a=0}return t.x=q(a+(this.long0||0)),t.y=Ms(h),t}function wi(t){var s,i,a=q(t.x-(this.long0||0)),e=t.y;return i=this.cphi1+this.phi1-e,Math.abs(i)>gi?(t.x=i*Math.sin(s=a*Math.cos(e)/i),t.y=this.cphi1-i*Math.cos(s)):t.x=t.y=0,t.x=this.a*t.x+(this.x0||0),t.y=this.a*t.y+(this.y0||0),t}function Si(t){var s,i;t.x=(t.x-(this.x0||0))/this.a,t.y=(t.y-(this.y0||0))/this.a;var a=Ht(t.x,t.y=this.cphi1-t.y);if(i=this.cphi1+this.phi1-a,Math.abs(i)>e)throw new Error;return s=Math.abs(Math.abs(i)-e)<=gi?0:a*Math.atan2(t.x,t.y)/Math.cos(i),t.x=q(s+(this.long0||0)),t.y=Ms(i),t}var Pi={init:function(){var t;if(this.phi1=this.lat1,Math.abs(this.phi1)<gi)throw new Error;this.es?(this.en=Dt(this.es),this.m1=Ft(this.phi1,this.am1=Math.sin(this.phi1),t=Math.cos(this.phi1),this.en),this.am1=t/(Math.sqrt(1-this.es*this.am1*this.am1)*this.am1),this.inverse=bi,this.forward=vi):(Math.abs(this.phi1)+gi>=e?this.cphi1=0:this.cphi1=1/Math.tan(this.phi1),this.inverse=Si,this.forward=wi)},names:["bonne","Bonne (Werner lat_1=90)"]};xt.defaultDatum="WGS84",xt.Proj=et,xt.WGS84=new xt.Proj("WGS84"),xt.Point=Lt,xt.toPoint=Mt,xt.defs=N,xt.nadgrid=function(t,s){var i=new DataView(s),a=function(t){var s=t.getInt32(8,!1);if(11===s)return!1;s=t.getInt32(8,!0),11!==s&&console.warn("Failed to detect nadgrid endian-ness, defaulting to little-endian");return!0}(i),e=function(t,s){return{nFields:t.getInt32(8,s),nSubgridFields:t.getInt32(24,s),nSubgrids:t.getInt32(40,s),shiftType:tt(t,56,64).trim(),fromSemiMajorAxis:t.getFloat64(120,s),fromSemiMinorAxis:t.getFloat64(136,s),toSemiMajorAxis:t.getFloat64(152,s),toSemiMinorAxis:t.getFloat64(168,s)}}(i,a),h=function(t,s,i){for(var a=176,e=[],h=0;h<s.nSubgrids;h++){var n=it(t,a,i),r=at(t,a,n,i),o=Math.round(1+(n.upperLongitude-n.lowerLongitude)/n.longitudeInterval),l=Math.round(1+(n.upperLatitude-n.lowerLatitude)/n.latitudeInterval);e.push({ll:[$(n.lowerLongitude),$(n.lowerLatitude)],del:[$(n.longitudeInterval),$(n.latitudeInterval)],lim:[o,l],count:n.gridNodeCount,cvs:st(r)}),a+=176+16*n.gridNodeCount}return e}(i,e,a),n={header:e,subgrids:h};return K[t]=n,n},xt.transform=dt,xt.mgrs=Gt,xt.version="__VERSION__",function(t){t.Proj.projections.add(Qt),t.Proj.projections.add(Xt),t.Proj.projections.add(Kt),t.Proj.projections.add(ts),t.Proj.projections.add(ss),t.Proj.projections.add(is),t.Proj.projections.add(es),t.Proj.projections.add(hs),t.Proj.projections.add(ns),t.Proj.projections.add(ds),t.Proj.projections.add(ws),t.Proj.projections.add(Ps),t.Proj.projections.add(Es),t.Proj.projections.add(Gs),t.Proj.projections.add(Cs),t.Proj.projections.add(Ns),t.Proj.projections.add(ks),t.Proj.projections.add(js),t.Proj.projections.add(Is),t.Proj.projections.add(Os),t.Proj.projections.add(As),t.Proj.projections.add(Rs),t.Proj.projections.add(qs),t.Proj.projections.add(Ls),t.Proj.projections.add(Xs),t.Proj.projections.add(hi),t.Proj.projections.add(ni),t.Proj.projections.add(fi),t.Proj.projections.add(Mi),t.Proj.projections.add(xi),t.Proj.projections.add(Pi)}(xt);var Ei=/^(?:ANSI\s)?(\d+)$/m;function Gi(t,s){if(!t)return a;try{new TextDecoder(t.trim())}catch(e){var i=Ei.exec(t);return i&&!s?Gi("windows-"+i[1],!0):(t=void 0,a)}return a;function a(s){var i=new TextDecoder(t||void 0);return(i.decode(s,{stream:!0})+i.decode()).replace(/\0/g,"").trim()}}function Ci(t,s,i,a,e){var h=e(new Uint8Array(t.buffer.slice(t.byteOffset+s,t.byteOffset+s+i)));switch(a){case"N":case"F":case"O":return parseFloat(h,10);case"D":return new Date(h.slice(0,4),parseInt(h.slice(4,6),10)-1,h.slice(6,8));case"L":return"y"===h.toLowerCase()||"t"===h.toLowerCase();default:return h}}function Ni(t,s,i,a){for(var e,h,n={},r=0,o=i.length;r<o;)e=Ci(t,s,(h=i[r]).len,h.dataType,a),s+=h.len,void 0!==e&&(n[h.name]=e),r++;return n}function ki(t,s){for(var i=Gi(s),a=function(t){var s={};return s.lastUpdated=new Date(t.getUint8(1)+1900,t.getUint8(2),t.getUint8(3)),s.records=t.getUint32(4,!0),s.headerLen=t.getUint16(8,!0),s.recLen=t.getUint16(10,!0),s}(t),e=function(t,s,i){for(var a=[],e=32;e<s&&(a.push({name:i(new Uint8Array(t.buffer.slice(t.byteOffset+e,t.byteOffset+e+11))),dataType:String.fromCharCode(t.getUint8(e+11)),len:t.getUint8(e+16),decimal:t.getUint8(e+17)}),13!==t.getUint8(e+32));)e+=32;return a}(t,a.headerLen-1,i),h=2+(e.length+1<<5),n=a.recLen,r=a.records,o=[];r;)o.push(Ni(t,h,e,i)),h+=n,r--;return o}var ji,Ii="deflate-raw",Oi=self.DecompressionStream;try{new Oi(Ii),ji=async t=>{let s=new Oi(Ii),i=s.writable.getWriter(),a=s.readable.getReader();i.write(t),i.close();let e,h,n=[],r=0,o=0;for(;!(h=await a.read()).done;)e=h.value,n.push(e),r+=e.length;return n.length-1?(e=new Uint8Array(r),n.map((t=>{e.set(t,o),o+=t.length})),e):n[0]}}catch{}var Ai=new TextDecoder,Ri=t=>{throw new Error("but-unzip~"+t)},qi=t=>Ai.decode(t);function*Li(t,s=ji){let i=(t=>{let s=t.length-20,i=Math.max(s-65516,2);for(;-1!==(s=t.lastIndexOf(80,s-1))&&(75!==t[s+1]||5!==t[s+2]||6!==t[s+3])&&s>i;);return s})(t);-1===i&&Ri(2);let a=(s,a)=>t.subarray(i+=s,i+=a),e=new DataView(t.buffer,t.byteOffset),h=t=>e.getUint16(t+i,!0),n=t=>e.getUint32(t+i,!0),r=h(10);for(r!==h(8)&&Ri(3),i=n(16);r--;){let t,e=h(10),r=h(28),o=h(30),l=h(32),u=n(20),c=n(42),f=qi(a(46,r)),M=qi(a(o,l)),p=i;i=c,t=a(30+h(26)+h(28),u),yield{filename:f,comment:M,read:()=>8&e?s(t):e?Ri(1):t},i=p}}const Ti=/.+\.(shp|dbf|json|prj|cpg)$/i;async function zi(t,s){const i=function(t,s){if(!s)return t;const i=new URL(t);return i.pathname=`${i.pathname}.${s}`,i.href}(t,s),a="prj"===s||"cpg"===s;try{const t=await fetch(i);if(t.status>399)throw new Error(t.statusText);if(a)return t.text();const s=await t.arrayBuffer();return new DataView(s)}catch(t){if(a||"dbf"===s)return!1;throw t}}function Bi(t){let s=0,i=1;const a=t.length;let e,h;const n=[t[0][0],t[0][1],t[0][0],t[0][1]];for(;i<a;)e=h||t[0],h=t[i],s+=(h[0]-e[0])*(h[1]+e[1]),i++,h[0]<n[0]&&(n[0]=h[0]),h[1]<n[1]&&(n[1]=h[1]),h[0]>n[2]&&(n[2]=h[0]),h[1]>n[3]&&(n[3]=h[1]);return{ring:t,clockWise:s>0,bbox:n,children:[]}}function Di(t,s){return!(t.bbox[0]>s.bbox[0])&&(!(t.bbox[1]>s.bbox[1])&&(!(t.bbox[2]<s.bbox[2])&&!(t.bbox[3]<s.bbox[3])))}Ui.prototype.parsePoint=function(t){return{type:"Point",coordinates:this.parseCoord(t,0)}},Ui.prototype.parseZPoint=function(t){const s=this.parsePoint(t);return s.coordinates.push(t.getFloat64(16,!0)),s},Ui.prototype.parsePointArray=function(t,s,i){const a=[];let e=0;for(;e<i;)a.push(this.parseCoord(t,s)),s+=16,e++;return a},Ui.prototype.parseZPointArray=function(t,s,i,a){let e=0;for(;e<i;)a[e].push(t.getFloat64(s,!0)),e++,s+=8;return a},Ui.prototype.parseArrayGroup=function(t,s,i,a,e){const h=[];let n,r,o=0,l=0;for(;o<a;)o++,i+=4,n=l,l=o===a?e:t.getInt32(i,!0),r=l-n,r&&(h.push(this.parsePointArray(t,s,r)),s+=r<<4);return h},Ui.prototype.parseZArrayGroup=function(t,s,i,a){let e=0;for(;e<i;)a[e]=this.parseZPointArray(t,s,a[e].length,a[e]),s+=a[e].length<<3,e++;return a},Ui.prototype.parseMultiPoint=function(t){const s={},i=t.getInt32(32,!0);if(!i)return null;const a=this.parseCoord(t,0),e=this.parseCoord(t,16);s.bbox=[a[0],a[1],e[0],e[1]];return 1===i?(s.type="Point",s.coordinates=this.parseCoord(t,36)):(s.type="MultiPoint",s.coordinates=this.parsePointArray(t,36,i)),s},Ui.prototype.parseZMultiPoint=function(t){const s=this.parseMultiPoint(t);if(!s)return null;let i;if("Point"===s.type)return s.coordinates.push(t.getFloat64(72,!0)),s;i=s.coordinates.length;const a=52+(i<<4);return s.coordinates=this.parseZPointArray(t,a,i,s.coordinates),s},Ui.prototype.parsePolyline=function(t){const s={},i=t.getInt32(32,!0);if(!i)return null;const a=this.parseCoord(t,0),e=this.parseCoord(t,16);s.bbox=[a[0],a[1],e[0],e[1]];const h=t.getInt32(36,!0);let n,r;return 1===i?(s.type="LineString",n=44,s.coordinates=this.parsePointArray(t,n,h)):(s.type="MultiLineString",n=40+(i<<2),r=40,s.coordinates=this.parseArrayGroup(t,n,40,i,h)),s},Ui.prototype.parseZPolyline=function(t){const s=this.parsePolyline(t);if(!s)return null;const i=s.coordinates.length;let a;if("LineString"===s.type)return a=60+(i<<4),s.coordinates=this.parseZPointArray(t,a,i,s.coordinates),s;return a=56+(s.coordinates.reduce((function(t,s){return t+s.length}),0)<<4)+(i<<2),s.coordinates=this.parseZArrayGroup(t,a,i,s.coordinates),s},Ui.prototype.polyFuncs=function(t){return t?"LineString"===t.type?(t.type="Polygon",t.coordinates=[t.coordinates],t):(t.coordinates=function(t){const s=[],i=[];for(const a of t){const t=Bi(a);t.clockWise?s.push(t):i.push(t)}for(const t of i)for(const i of s)if(Di(i,t)){i.children.push(t.ring);break}const a=[];for(const t of s)a.push([t.ring].concat(t.children));return a}(t.coordinates),1===t.coordinates.length?(t.type="Polygon",t.coordinates=t.coordinates[0],t):(t.type="MultiPolygon",t)):t},Ui.prototype.parsePolygon=function(t){return this.polyFuncs(this.parsePolyline(t))},Ui.prototype.parseZPolygon=function(t){return this.polyFuncs(this.parseZPolyline(t))};const Fi={1:"parsePoint",3:"parsePolyline",5:"parsePolygon",8:"parseMultiPoint",11:"parseZPoint",13:"parseZPolyline",15:"parseZPolygon",18:"parseZMultiPoint"};function Ui(t,s){if(!(this instanceof Ui))return new Ui(t,s);this.buffer=t,this.headers=this.parseHeader(),this.shpFuncs(s),this.rows=this.getRows()}function Qi(t,s){return new Ui(t,s).rows}function Wi(t){return window.proj4?window.proj4(t):window.mars3d&&window.mars3d.proj4?window.mars3d.proj4(t):xt(t)}Ui.prototype.shpFuncs=function(t){let s=this.headers.shpCode;if(s>20&&(s-=20),!(s in Fi))throw new Error(`I don't know shp type "${s}"`);var i;this.parseFunc=this[Fi[s]],this.parseCoord=(i=t)?function(t,s){const a=[t.getFloat64(s,!0),t.getFloat64(s+8,!0)];return i.inverse(a)}:function(t,s){return[t.getFloat64(s,!0),t.getFloat64(s+8,!0)]}},Ui.prototype.getShpCode=function(){return this.parseHeader().shpCode},Ui.prototype.parseHeader=function(){const t=this.buffer;return{length:t.getInt32(24)<<1,version:t.getInt32(28,!0),shpCode:t.getInt32(32,!0),bbox:[t.getFloat64(36,!0),t.getFloat64(44,!0),t.getFloat64(52,!0),t.getFloat64(60,!0)]}},Ui.prototype.getRows=function(){let t=100;const s=this.buffer.byteLength-8,i=[];let a;for(;t<=s&&(a=this.getRow(t),a);)t+=8,t+=a.len,a.type?i.push(this.parseFunc(a.data)):i.push(null);return i},Ui.prototype.getRow=function(t){const s=this.buffer.getInt32(t),i=this.buffer.getInt32(t+4)<<1;return 0===i?{id:s,len:i,type:0}:t+i+8>this.buffer.byteLength?void 0:{id:s,len:i,data:new DataView(this.buffer.buffer,this.buffer.byteOffset+t+12,i-4),type:this.buffer.getInt32(t+8,!0)}},xt.defs("EPSG:4490","+proj=longlat +ellps=GRS80 +no_defs"),xt.defs("EPSG:4491","+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=13500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4492","+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=14500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4493","+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=15500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4494","+proj=tmerc +lat_0=0 +lon_0=93 +k=1 +x_0=16500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4495","+proj=tmerc +lat_0=0 +lon_0=99 +k=1 +x_0=17500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4496","+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=18500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4497","+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=19500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4498","+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=20500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4499","+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=21500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4500","+proj=tmerc +lat_0=0 +lon_0=129 +k=1 +x_0=22500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4501","+proj=tmerc +lat_0=0 +lon_0=135 +k=1 +x_0=23500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4513","+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=25500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4514","+proj=tmerc +lat_0=0 +lon_0=78 +k=1 +x_0=26500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4515","+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=27500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4516","+proj=tmerc +lat_0=0 +lon_0=84 +k=1 +x_0=28500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4517","+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=29500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4518","+proj=tmerc +lat_0=0 +lon_0=90 +k=1 +x_0=30500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4519","+proj=tmerc +lat_0=0 +lon_0=93 +k=1 +x_0=31500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4520","+proj=tmerc +lat_0=0 +lon_0=96 +k=1 +x_0=32500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4521","+proj=tmerc +lat_0=0 +lon_0=99 +k=1 +x_0=33500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4522","+proj=tmerc +lat_0=0 +lon_0=102 +k=1 +x_0=34500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4523","+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=35500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4524","+proj=tmerc +lat_0=0 +lon_0=108 +k=1 +x_0=36500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4525","+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=37500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4526","+proj=tmerc +lat_0=0 +lon_0=114 +k=1 +x_0=38500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4527","+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=39500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4528","+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=40500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4529","+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=41500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4530","+proj=tmerc +lat_0=0 +lon_0=126 +k=1 +x_0=42500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4531","+proj=tmerc +lat_0=0 +lon_0=129 +k=1 +x_0=43500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4532","+proj=tmerc +lat_0=0 +lon_0=132 +k=1 +x_0=44500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4533","+proj=tmerc +lat_0=0 +lon_0=135 +k=1 +x_0=45500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4502","+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4503","+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4504","+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4505","+proj=tmerc +lat_0=0 +lon_0=93 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4506","+proj=tmerc +lat_0=0 +lon_0=99 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4507","+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4508","+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4509","+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4510","+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4511","+proj=tmerc +lat_0=0 +lon_0=129 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4512","+proj=tmerc +lat_0=0 +lon_0=135 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4534","+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4535","+proj=tmerc +lat_0=0 +lon_0=78 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4536","+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4537","+proj=tmerc +lat_0=0 +lon_0=84 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4538","+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4539","+proj=tmerc +lat_0=0 +lon_0=90 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4540","+proj=tmerc +lat_0=0 +lon_0=93 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4541","+proj=tmerc +lat_0=0 +lon_0=96 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4542","+proj=tmerc +lat_0=0 +lon_0=99 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4543","+proj=tmerc +lat_0=0 +lon_0=102 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4544","+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4545","+proj=tmerc +lat_0=0 +lon_0=108 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4546","+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4547","+proj=tmerc +lat_0=0 +lon_0=114 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4548","+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4549","+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4550","+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4551","+proj=tmerc +lat_0=0 +lon_0=126 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4552","+proj=tmerc +lat_0=0 +lon_0=129 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4553","+proj=tmerc +lat_0=0 +lon_0=132 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),xt.defs("EPSG:4554","+proj=tmerc +lat_0=0 +lon_0=135 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs");const Hi=globalThis.URL,Zi=new TextDecoder,Ji=t=>{if(t)return"string"==typeof t?t:Xi(t)||ArrayBuffer.isView(t)||Ki(t)?Zi.decode(t):void 0},Vi=t=>{if(!t)throw new Error("forgot to pass buffer");if(Ki(t))return t;if(Xi(t))return new DataView(t);if(Xi(t.buffer))return new DataView(t.buffer,t.byteOffset,t.byteLength);throw new Error("invalid buffer like object")};function Xi(t){return t instanceof globalThis.ArrayBuffer||"[object ArrayBuffer]"===Object.prototype.toString.call(t)}function Ki(t){return t instanceof globalThis.DataView||"[object DataView]"===Object.prototype.toString.call(t)}const Yi=function([t,s]){const i={type:"FeatureCollection",features:[]};let a=0;const e=t.length;for(s||(s=[]);a<e;)i.features.push({type:"Feature",geometry:t[a],properties:s[a]||{}}),a++;return i},$i=async function(t,s,i,a){let e;t=(t=>{if(!t)throw new Error("forgot to pass buffer");if(Xi(t))return new Uint8Array(t);if(Xi(t.buffer))return 1===t.BYTES_PER_ELEMENT?t:new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("invalid buffer like object")})(t);const h=await async function(t){const s={},i=[],a=Li(t);for(const t of a)Ti.test(t.filename)&&i.push(Promise.resolve(t.read()).then((i=>s[t.filename]=i)));await Promise.all(i);const e={},h=new TextDecoder;for(const[t,i]of Object.entries(s))"shp"===t.slice(-3).toLowerCase()||"dbf"===t.slice(-3).toLowerCase()?e[t]=new DataView(i.buffer,i.byteOffset,i.byteLength):e[t]=h.decode(i);return e}(t),n=[];for(e in s=s||[],h)if(-1===e.indexOf("__MACOSX"))if(".shp"===e.slice(-4).toLowerCase())n.push(e.slice(0,-4)),h[e.slice(0,-3)+e.slice(-3).toLowerCase()]=h[e];else if(".prj"===e.slice(-4).toLowerCase()){let t;try{t=Wi(a||h[e])}catch(s){console.error("该坐标系proj4未解析,将原样转出",s),t=Wi("EPSG:4326")}h[e.slice(0,-3)+e.slice(-3).toLowerCase()]=xt(t)}else".json"===e.slice(-5).toLowerCase()||s.indexOf(e.split(".").pop())>-1?n.push(e.slice(0,-3)+e.slice(-3).toLowerCase()):".dbf"!==e.slice(-4).toLowerCase()&&".cpg"!==e.slice(-4).toLowerCase()||(h[e.slice(0,-3)+e.slice(-3).toLowerCase()]=h[e]);if(!n.length)throw new Error("no layers founds");const r=n.map((function(t){let a,e;const n=t.lastIndexOf(".");return n>-1&&t.slice(n).indexOf("json")>-1?(a=JSON.parse(h[t]),a.fileName=t.slice(0,n)):s.indexOf(t.slice(n+1))>-1?(a=h[t],a.fileName=t):(h[t+".dbf"]&&(e=ki(h[t+".dbf"],i||h[t+".cpg"])),a=Yi([Qi(h[t+".shp"],h[t+".prj"]),e]),a.fileName=t),a}));return 1===r.length?r[0]:r};const ta=async t=>{const s=await Promise.all([zi(t,"shp"),zi(t,"prj")]);let i=!1;try{s[1]&&(i=xt(s[1]))}catch(t){i=!1}return Qi(s[0],i)},sa=async t=>{const[s,i]=await Promise.all([zi(t,"dbf"),zi(t,"cpg")]);if(s)return ki(s,i)},ia=(t,s)=>new Hi(t,globalThis?.document?.location).pathname.slice(-4).toLowerCase()===s,aa=async function(t,s,i,a){if("string"!=typeof t){if(Xi(t)||ArrayBuffer.isView(t)||Ki(t))return $i(t);if(t.shp)return(({shp:t,dbf:s,cpg:i,prj:a})=>{const e=[ha(t,a)];return s&&e.push(na(s,i)),Yi(e)})(t);throw new TypeError("must be a string, some sort of Buffer, or an object with at least a .shp property")}if(ia(t,".zip"))return async function(t,s,i,a){const e=await zi(t);return $i(e,s,i,a)}(t,s,i,a);ia(t,".shp")&&(t=t.slice(0,-4));const e=await Promise.all([ta(t),sa(t)]);return Yi(e)},ea=aa,ha=function(t,s){if(t=Vi(t),"string"==typeof(s=Ji(s)))try{s=xt(s)}catch(t){s=!1}return Qi(t,s)},na=function(t,s){return ki(t=Vi(t),s=Ji(s))};t.combine=Yi,t.getShapefile=aa,t.parseDbf=na,t.parseShp=ha,t.parseZip=$i,t.toGeoJSON=ea}));
