/* eslint-disable */
const c=E;function d(){const z=['1973336kSCkAk','push','array','keys','BufferGeometryUtils:\x20.mergeBufferAttributes()\x20failed.\x20InterleavedBufferAttributes\x20are\x20not\x20supported.','position','translate','492GVDIuY','32JCdcnu','isInterleavedBufferAttribute','normal','set','9DyPjWR','395787rzPZlB','BufferGeometryUtils:\x20.mergeBufferAttributes()\x20failed.\x20BufferAttribute.normalized\x20must\x20be\x20consistent\x20across\x20matching\x20attributes.','has','index','13625bgjwne','36378EPBiNV','1583672rEuocf','morphTargetsRelative','userData','ConeGeometry','./three.min.js','data','mergedUserData','.\x20\x20.morphAttributes\x20must\x20be\x20consistent\x20throughout\x20all\x20geometries.','attributes','vertices','\x20attribute.','itemSize','rotateX','1946385GenGGu','constructor','length','count','getX','BYTES_PER_ELEMENT','buffer','setAttribute','morphAttributes','normalized','size','setIndex','normals','BufferGeometryUtils:\x20.mergeBufferAttributes()\x20failed.\x20BufferAttribute.itemSize\x20must\x20be\x20consistent\x20across\x20matching\x20attributes.','\x20morphAttribute.','.\x20The\x20geometry\x20must\x20have\x20either\x20an\x20index\x20or\x20a\x20position\x20attribute','error','BufferGeometryUtils:\x20.mergeBufferGeometries()\x20failed\x20with\x20geometry\x20at\x20index\x20','CylinderGeometry','BufferGeometryUtils:\x20.mergeBufferGeometries()\x20failed\x20while\x20trying\x20to\x20merge\x20the\x20','13716970rNhmzg','BufferAttribute'];d=function(){return z;};return d();}function E(g,X){const u=d();return E=function(f,j){f=f-0x179;let I=u[f];return I;},E(g,X);}(function(g,X){const M=E,u=g();while(!![]){try{const f=-parseInt(M(0x18a))/0x1*(parseInt(M(0x194))/0x2)+-parseInt(M(0x18f))/0x3+-parseInt(M(0x182))/0x4+-parseInt(M(0x193))/0x5*(-parseInt(M(0x189))/0x6)+-parseInt(M(0x1a2))/0x7+parseInt(M(0x195))/0x8*(parseInt(M(0x18e))/0x9)+parseInt(M(0x180))/0xa;if(f===X)break;else u['push'](u['shift']());}catch(j){u['push'](u['shift']());}}}(d,0x4b21c),importScripts(c(0x199)));function mergeBufferGeometries(g,X=![]){const P=c,u=g[0x0][P(0x192)]!==null,f=new Set(Object[P(0x185)](g[0x0][P(0x19d)])),I=new Set(Object[P(0x185)](g[0x0][P(0x1aa)])),V={},e={},D=g[0x0][P(0x196)],S=new THREE['BufferGeometry']();let k=0x0;for(let F=0x0;F<g[P(0x1a4)];++F){const r=g[F];let a=0x0;if(u!==(r['index']!==null))return console[P(0x17c)](P(0x17d)+F+'.\x20All\x20geometries\x20must\x20have\x20compatible\x20attributes;\x20make\x20sure\x20index\x20attribute\x20exists\x20among\x20all\x20geometries,\x20or\x20in\x20none\x20of\x20them.'),null;for(const q in r[P(0x19d)]){if(!f[P(0x191)](q))return console['error'](P(0x17d)+F+'.\x20All\x20geometries\x20must\x20have\x20compatible\x20attributes;\x20make\x20sure\x20\x22'+q+'\x22\x20attribute\x20exists\x20among\x20all\x20geometries,\x20or\x20in\x20none\x20of\x20them.'),null;if(V[q]===undefined)V[q]=[];V[q][P(0x183)](r[P(0x19d)][q]),a++;}if(a!==f[P(0x1ac)])return console['error'](P(0x17d)+F+'.\x20Make\x20sure\x20all\x20geometries\x20have\x20the\x20same\x20number\x20of\x20attributes.'),null;if(D!==r[P(0x196)])return console[P(0x17c)](P(0x17d)+F+'.\x20.morphTargetsRelative\x20must\x20be\x20consistent\x20throughout\x20all\x20geometries.'),null;for(const N in r[P(0x1aa)]){if(!I[P(0x191)](N))return console[P(0x17c)](P(0x17d)+F+P(0x19c)),null;if(e[N]===undefined)e[N]=[];e[N][P(0x183)](r[P(0x1aa)][N]);}S[P(0x197)][P(0x19b)]=S['userData'][P(0x19b)]||[],S['userData'][P(0x19b)][P(0x183)](r[P(0x197)]);if(X){let o;if(u)o=r[P(0x192)][P(0x1a5)];else{if(r[P(0x19d)][P(0x187)]!==undefined)o=r[P(0x19d)][P(0x187)][P(0x1a5)];else return console['error'](P(0x17d)+F+P(0x17b)),null;}S['addGroup'](k,o,F),k+=o;}}if(u){let C=0x0;const Z=[];for(let O=0x0;O<g[P(0x1a4)];++O){const Q=g[O]['index'];for(let y=0x0;y<Q['count'];++y){Z[P(0x183)](Q[P(0x1a6)](y)+C);}C+=g[O]['attributes'][P(0x187)]['count'];}S[P(0x1ad)](Z);}for(const T in V){const L=mergeBufferAttributes(V[T]);if(!L)return console['error'](P(0x17f)+T+P(0x19f)),null;S[P(0x1a9)](T,L);}for(const p in e){const x=e[p][0x0][P(0x1a4)];if(x===0x0)break;S[P(0x1aa)]=S['morphAttributes']||{},S[P(0x1aa)][p]=[];for(let w=0x0;w<x;++w){const K=[];for(let H=0x0;H<e[p][P(0x1a4)];++H){K['push'](e[p][H][w]);}const Y=mergeBufferAttributes(K);if(!Y)return console[P(0x17c)]('BufferGeometryUtils:\x20.mergeBufferGeometries()\x20failed\x20while\x20trying\x20to\x20merge\x20the\x20'+p+P(0x17a)),null;S[P(0x1aa)][p][P(0x183)](Y);}}return S;}function mergeBufferAttributes(g){const s=c;let X,u,f,j=0x0;for(let e=0x0;e<g[s(0x1a4)];++e){const D=g[e];if(D[s(0x18b)])return console[s(0x17c)](s(0x186)),null;if(X===undefined)X=D[s(0x184)][s(0x1a3)];if(X!==D['array'][s(0x1a3)])return console[s(0x17c)]('BufferGeometryUtils:\x20.mergeBufferAttributes()\x20failed.\x20BufferAttribute.array\x20must\x20be\x20of\x20consistent\x20array\x20types\x20across\x20matching\x20attributes.'),null;if(u===undefined)u=D['itemSize'];if(u!==D[s(0x1a0)])return console[s(0x17c)](s(0x179)),null;if(f===undefined)f=D[s(0x1ab)];if(f!==D['normalized'])return console['error'](s(0x190)),null;j+=D[s(0x184)][s(0x1a4)];}const I=new X(j);let V=0x0;for(let S=0x0;S<g[s(0x1a4)];++S){I[s(0x18d)](g[S][s(0x184)],V),V+=g[S][s(0x184)][s(0x1a4)];}return new THREE[(s(0x181))](I,u,f);}onmessage=g=>{const l=c,{headRadius:X,headLength:u,headSegments:f,bodyTopRadius:j,bodyBottomRadius:I,bodyLength:V,bodySegments:e,flipArrow:D}=g[l(0x19a)],S=new THREE[(l(0x198))](X,u,f);S[l(0x188)](0x0,V/0x2,0x0),D&&S[l(0x1a1)](Math['PI']);const k=new THREE[(l(0x17e))](j,I,V,e);D&&k[l(0x1a1)](Math['PI']);const F=mergeBufferGeometries([S,k],![]),r={'vertices':F[l(0x19d)]['position'][l(0x184)]['buffer'],'indices':F[l(0x192)][l(0x184)][l(0x1a8)],'normals':F[l(0x19d)][l(0x18c)][l(0x184)][l(0x1a8)],'indiceByteLen':F[l(0x192)][l(0x184)][l(0x1a7)]},a=[r[l(0x19e)],r['indices'],r[l(0x1ae)]];postMessage(r,a);};
