var MarsgisEditor=function(e,t){"use strict";var i=Object.defineProperty,n=(e,t,n)=>((e,t,n)=>t in e?i(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n)(e,"symbol"!=typeof t?t+"":t,n);function r(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e)for(const i in e)if("default"!==i){const n=Object.getOwnPropertyDescriptor(e,i);Object.defineProperty(t,i,n.get?n:{enumerable:!0,get:()=>e[i]})}return t.default=e,Object.freeze(t)}const s=r(t),o=class e{static setConfig(t){e.apiConf=t,e.sessionName=t.packageName+"-homepage-config"}static getConfig(){return e.apiConf}static getPackageName(){return e.apiConf.packageName}static getExampleList(){if("online"===e.apiConf.configFetchType)return e.fetchExampleList();try{const t=JSON.parse(sessionStorage.getItem(e.sessionName)||"");if(t)return e.saveCompConfig(t),Promise.resolve(t);throw new Error("无缓存数据")}catch(t){return e.fetchExampleList()}}static async fetchExampleList(){const t=await fetch(e.apiConf.configSourceUrl),i=await t.json();return e.saveCompConfig(i),"local"===e.apiConf.configFetchType&&sessionStorage.setItem(e.sessionName,JSON.stringify(i)),i}static async getCompConfig(t,i){try{const n=e.mars3dExampleConfig,r=e.mars3dExampleConfigKeymap;if(i&&r&&r[i]&&r[i].main===t)return r[i];if(n)return n[t];throw new Error("无缓存数据")}catch(n){return await e.getExampleList(),e.getCompConfig(t,i)}}static saveCompConfig(t){const i={},n={};e.totalCount=0,t.forEach((t,r)=>{t.id=`ex_${r}`,t.children&&(t.count=0,t.children.forEach((r,s)=>{r.id=`${t.id}_${s}`,r.children&&(r.count=r.children.length,r.children.forEach((s,o)=>{t.count++,s.id=`${r.id}_${o}`,s.fullName=`${t.name}  -  ${r.name}  -  ${s.name}`,s.thumbnail=`${e.apiConf.thumbnailPublicPath||"/"}${s.thumbnail||"thumbnail.jpg"}`,i[s.main]=s,n[s.id]=s,s.hidden||e.totalCount++}))}))}),e.mars3dExampleConfig=i,e.mars3dExampleConfigKeymap=n}static getResourcesByLibs(t){t&&0!==t.length||(t=[e.apiConf.packageName]);let i=[];const n={};for(let r=0,s=t.length;r<s;r++){const s=t[r];if(n[s])continue;n[s]=!0;const o=e.apiConf.configLibs[s];o&&(i=i.concat(o.map(t=>t.startsWith("http")||t.startsWith("//:")?t:e.apiConf.libPublicPath+t)))}return i}static getLibs(t,i=[],n=[]){return e.getResourcesByLibs(i).concat(n).map(i=>{let n=i;return i.startsWith(".")||i.startsWith("http")||i.startsWith("//:")?n=i:i.startsWith("/")?"/"!==e.apiConf.baseUrl&&(n=e.apiConf.baseUrl+i):n=`${e.apiConf.resourcePublicPath}/${t}/${i}`,{name:n,url:n}})}static getQueryString(e){return new URL(window.location.href).searchParams.get(e)}static LoadSource(t){const i=n=>{if(t.length){const r=t.shift();let s;s=r.endsWith(".css")?e.loadLink(r):e.loadScript(r),s.then(()=>{i(n)})}else n(!0)};return new Promise(e=>{i(e)})}static loadScript(e,t=!0){const i=document.createElement("script");return i.async=t,i.src=e,document.body.appendChild(i),new Promise(e=>{i.onload=()=>{e(!0)}})}static loadLink(e){const t=document.createElement("link");return t.rel="stylesheet",t.href=e,document.head.appendChild(t),new Promise(e=>{t.onload=()=>{e(!0)}})}static scriptFilter(t){return t=(t=(t=t.replace(/const /gm,"var ")).replace(/let /gm,"var ")).replace(/export /gm,""),t=(t="mars3d"===e.apiConf.packageName?(t=t.replace('import * as mars3d from "mars3d"',"")).replace("const Cesium = mars3d.Cesium",""):(t=t.replace('import * as mars2d from "mars2d"',"")).replace("const L = mars3d.L","")).replace(/import /gm,"// import ")}};n(o,"apiConf"),n(o,"sessionName"),n(o,"totalCount",0),n(o,"mars3dExampleConfig"),n(o,"mars3dExampleConfigKeymap");let a=o;function c(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function l(e){if(Object.prototype.hasOwnProperty.call(e,"__esModule"))return e;var t=e.default;if("function"==typeof t){var i=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};i.prototype=t.prototype}else i={};return Object.defineProperty(i,"__esModule",{value:!0}),Object.keys(e).forEach(function(t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(i,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}),i}var A={};const h=l(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var d,p={};const u="3.1.10";var m;var g=(m||(m=1,function(e){
/**
       * @file Embedded JavaScript templating engine. {@link http://ejs.co}
       * <AUTHOR> Eernisse <<EMAIL>>
       * <AUTHOR> "Timothy" Gu <<EMAIL>>
       * @project EJS
       * @license {@link http://www.apache.org/licenses/LICENSE-2.0 Apache License, Version 2.0}
       */
var t=h,i=h,n=(d||(d=1,function(e){var t=/[|\\{}()[\]^$+*?.]/g,i=Object.prototype.hasOwnProperty,n=function(e,t){return i.apply(e,[t])};e.escapeRegExpChars=function(e){return e?String(e).replace(t,"\\$&"):""};var r={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&#34;","'":"&#39;"},s=/[&<>'"]/g;function o(e){return r[e]||e}function a(){return Function.prototype.toString.call(this)+';\nvar _ENCODE_HTML_RULES = {\n      "&": "&amp;"\n    , "<": "&lt;"\n    , ">": "&gt;"\n    , \'"\': "&#34;"\n    , "\'": "&#39;"\n    }\n  , _MATCH_HTML = /[&<>\'"]/g;\nfunction encode_char(c) {\n  return _ENCODE_HTML_RULES[c] || c;\n};\n'}e.escapeXML=function(e){return null==e?"":String(e).replace(s,o)};try{"function"==typeof Object.defineProperty?Object.defineProperty(e.escapeXML,"toString",{value:a}):e.escapeXML.toString=a}catch(c){console.warn("Unable to set escapeXML.toString (is the Function prototype frozen?)")}e.shallowCopy=function(e,t){if(t=t||{},null!=e)for(var i in t)n(t,i)&&"__proto__"!==i&&"constructor"!==i&&(e[i]=t[i]);return e},e.shallowCopyFromList=function(e,t,i){if(i=i||[],t=t||{},null!=e)for(var r=0;r<i.length;r++){var s=i[r];if(void 0!==t[s]){if(!n(t,s))continue;if("__proto__"===s||"constructor"===s)continue;e[s]=t[s]}}return e},e.cache={_data:{},set:function(e,t){this._data[e]=t},get:function(e){return this._data[e]},remove:function(e){delete this._data[e]},reset:function(){this._data={}}},e.hyphenToCamel=function(e){return e.replace(/-[a-z]/g,function(e){return e[1].toUpperCase()})},e.createNullProtoObjWherePossible="function"==typeof Object.create?function(){return Object.create(null)}:{__proto__:null}instanceof Object?function(){return{}}:function(){return{__proto__:null}},e.hasOwnOnlyObject=function(t){var i=e.createNullProtoObjWherePossible();for(var r in t)n(t,r)&&(i[r]=t[r]);return i}}(p)),p),r=!1,s=u,o="locals",a=["delimiter","scope","context","debug","compileDebug","client","_with","rmWhitespace","strict","filename","async"],c=a.concat("cache"),l=/^\uFEFF/,A=/^[a-zA-Z_$][0-9a-zA-Z_$]*$/;function m(i,n){var r;if(n.some(function(n){return r=e.resolveInclude(i,n,!0),t.existsSync(r)}))return r}function g(t,i){var n,r=t.filename,s=arguments.length>1;if(t.cache){if(!r)throw new Error("cache option requires a filename");if(n=e.cache.get(r))return n;s||(i=f(r).toString().replace(l,""))}else if(!s){if(!r)throw new Error("Internal EJS error: no file name or template provided");i=f(r).toString().replace(l,"")}return n=e.compile(i,t),t.cache&&e.cache.set(r,n),n}function f(t){return e.fileLoader(t)}function k(i,r){var s=n.shallowCopy(n.createNullProtoObjWherePossible(),r);if(s.filename=function(i,n){var r,s,o=n.views,a=/^[A-Za-z]+:\\|^\//.exec(i);if(a&&a.length)i=i.replace(/^\/*/,""),r=Array.isArray(n.root)?m(i,n.root):e.resolveInclude(i,n.root||"/",!0);else if(n.filename&&(s=e.resolveInclude(i,n.filename),t.existsSync(s)&&(r=s)),!r&&Array.isArray(o)&&(r=m(i,o)),!r&&"function"!=typeof n.includer)throw new Error('Could not find the include file "'+n.escapeFunction(i)+'"');return r}(i,s),"function"==typeof r.includer){var o=r.includer(i,s.filename);if(o&&(o.filename&&(s.filename=o.filename),o.template))return g(s,o.template)}return g(s)}function w(e,t,i,n,r){var s=t.split("\n"),o=Math.max(n-3,0),a=Math.min(s.length,n+3),c=r(i),l=s.slice(o,a).map(function(e,t){var i=t+o+1;return(i==n?" >> ":"    ")+i+"| "+e}).join("\n");throw e.path=c,e.message=(c||"ejs")+":"+n+"\n"+l+"\n\n"+e.message,e}function C(e){return e.replace(/;(\s*$)/,"$1")}function v(t,i){var r=n.hasOwnOnlyObject(i),s=n.createNullProtoObjWherePossible();this.templateText=t,this.mode=null,this.truncate=!1,this.currentLine=1,this.source="",s.client=r.client||!1,s.escapeFunction=r.escape||r.escapeFunction||n.escapeXML,s.compileDebug=!1!==r.compileDebug,s.debug=!!r.debug,s.filename=r.filename,s.openDelimiter=r.openDelimiter||e.openDelimiter||"<",s.closeDelimiter=r.closeDelimiter||e.closeDelimiter||">",s.delimiter=r.delimiter||e.delimiter||"%",s.strict=r.strict||!1,s.context=r.context,s.cache=r.cache||!1,s.rmWhitespace=r.rmWhitespace,s.root=r.root,s.includer=r.includer,s.outputFunctionName=r.outputFunctionName,s.localsName=r.localsName||e.localsName||o,s.views=r.views,s.async=r.async,s.destructuredLocals=r.destructuredLocals,s.legacyInclude=void 0===r.legacyInclude||!!r.legacyInclude,s.strict?s._with=!1:s._with=void 0===r._with||r._with,this.opts=s,this.regex=this.createRegex()}e.cache=n.cache,e.fileLoader=t.readFileSync,e.localsName=o,e.promiseImpl=new Function("return this;")().Promise,e.resolveInclude=function(e,t,n){var r=i.dirname,s=i.extname,o=(0,i.resolve)(n?t:r(t),e);return s(e)||(o+=".ejs"),o},e.compile=function(e,t){return t&&t.scope&&(r||(console.warn("`scope` option is deprecated and will be removed in EJS 3"),r=!0),t.context||(t.context=t.scope),delete t.scope),new v(e,t).compile()},e.render=function(e,t,i){var r=t||n.createNullProtoObjWherePossible(),s=i||n.createNullProtoObjWherePossible();return 2==arguments.length&&n.shallowCopyFromList(s,r,a),g(s,e)(r)},e.renderFile=function(){var t,i,r,s=Array.prototype.slice.call(arguments),o=s.shift(),a={filename:o};return"function"==typeof arguments[arguments.length-1]&&(t=s.pop()),s.length?(i=s.shift(),s.length?n.shallowCopy(a,s.pop()):(i.settings&&(i.settings.views&&(a.views=i.settings.views),i.settings["view cache"]&&(a.cache=!0),(r=i.settings["view options"])&&n.shallowCopy(a,r)),n.shallowCopyFromList(a,i,c)),a.filename=o):i=n.createNullProtoObjWherePossible(),function(t,i,n){var r;if(!n){if("function"==typeof e.promiseImpl)return new e.promiseImpl(function(e,n){try{e(r=g(t)(i))}catch(s){n(s)}});throw new Error("Please provide a callback function")}try{r=g(t)(i)}catch(s){return n(s)}n(null,r)}(a,i,t)},e.Template=v,e.clearCache=function(){e.cache.reset()},v.modes={EVAL:"eval",ESCAPED:"escaped",RAW:"raw",COMMENT:"comment",LITERAL:"literal"},v.prototype={createRegex:function(){var e="(<%%|%%>|<%=|<%-|<%_|<%#|<%|%>|-%>|_%>)",t=n.escapeRegExpChars(this.opts.delimiter),i=n.escapeRegExpChars(this.opts.openDelimiter),r=n.escapeRegExpChars(this.opts.closeDelimiter);return e=e.replace(/%/g,t).replace(/</g,i).replace(/>/g,r),new RegExp(e)},compile:function(){var e,t,r,s=this.opts,o="",a="",c=s.escapeFunction,l=s.filename?JSON.stringify(s.filename):"undefined";if(!this.source){if(this.generateSource(),o+='  var __output = "";\n  function __append(s) { if (s !== undefined && s !== null) __output += s }\n',s.outputFunctionName){if(!A.test(s.outputFunctionName))throw new Error("outputFunctionName is not a valid JS identifier.");o+="  var "+s.outputFunctionName+" = __append;\n"}if(s.localsName&&!A.test(s.localsName))throw new Error("localsName is not a valid JS identifier.");if(s.destructuredLocals&&s.destructuredLocals.length){for(var h="  var __locals = ("+s.localsName+" || {}),\n",d=0;d<s.destructuredLocals.length;d++){var p=s.destructuredLocals[d];if(!A.test(p))throw new Error("destructuredLocals["+d+"] is not a valid JS identifier.");d>0&&(h+=",\n  "),h+=p+" = __locals."+p}o+=h+";\n"}!1!==s._with&&(o+="  with ("+s.localsName+" || {}) {\n",a+="  }\n"),a+="  return __output;\n",this.source=o+this.source+a}e=s.compileDebug?"var __line = 1\n  , __lines = "+JSON.stringify(this.templateText)+"\n  , __filename = "+l+";\ntry {\n"+this.source+"} catch (e) {\n  rethrow(e, __lines, __filename, __line, escapeFn);\n}\n":this.source,s.client&&(e="escapeFn = escapeFn || "+c.toString()+";\n"+e,s.compileDebug&&(e="rethrow = rethrow || "+w.toString()+";\n"+e)),s.strict&&(e='"use strict";\n'+e),s.debug&&console.log(e),s.compileDebug&&s.filename&&(e=e+"\n//# sourceURL="+l+"\n");try{if(s.async)try{r=new Function("return (async function(){}).constructor;")()}catch(f){throw f instanceof SyntaxError?new Error("This environment does not support async/await"):f}else r=Function;t=new r(s.localsName+", escapeFn, include, rethrow",e)}catch(f){throw f instanceof SyntaxError&&(s.filename&&(f.message+=" in "+s.filename),f.message+=" while compiling ejs\n\n",f.message+="If the above error is not helpful, you may want to try EJS-Lint:\n",f.message+="https://github.com/RyanZim/EJS-Lint",s.async||(f.message+="\n",f.message+="Or, if you meant to create an async function, pass `async: true` as an option.")),f}var u=s.client?t:function(e){return t.apply(s.context,[e||n.createNullProtoObjWherePossible(),c,function(t,i){var r=n.shallowCopy(n.createNullProtoObjWherePossible(),e);return i&&(r=n.shallowCopy(r,i)),k(t,s)(r)},w])};if(s.filename&&"function"==typeof Object.defineProperty){var m=s.filename,g=i.basename(m,i.extname(m));try{Object.defineProperty(u,"name",{value:g,writable:!1,enumerable:!1,configurable:!0})}catch(f){}}return u},generateSource:function(){this.opts.rmWhitespace&&(this.templateText=this.templateText.replace(/[\r\n]+/g,"\n").replace(/^\s+|\s+$/gm,"")),this.templateText=this.templateText.replace(/[ \t]*<%_/gm,"<%_").replace(/_%>[ \t]*/gm,"_%>");var e=this,t=this.parseTemplateText(),i=this.opts.delimiter,n=this.opts.openDelimiter,r=this.opts.closeDelimiter;t&&t.length&&t.forEach(function(s,o){var a;if(0===s.indexOf(n+i)&&0!==s.indexOf(n+i+i)&&(a=t[o+2])!=i+r&&a!="-"+i+r&&a!="_"+i+r)throw new Error('Could not find matching close tag for "'+s+'".');e.scanLine(s)})},parseTemplateText:function(){for(var e,t=this.templateText,i=this.regex,n=i.exec(t),r=[];n;)0!==(e=n.index)&&(r.push(t.substring(0,e)),t=t.slice(e)),r.push(n[0]),t=t.slice(n[0].length),n=i.exec(t);return t&&r.push(t),r},_addOutput:function(e){if(this.truncate&&(e=e.replace(/^(?:\r\n|\r|\n)/,""),this.truncate=!1),!e)return e;e=(e=(e=(e=e.replace(/\\/g,"\\\\")).replace(/\n/g,"\\n")).replace(/\r/g,"\\r")).replace(/"/g,'\\"'),this.source+='    ; __append("'+e+'")\n'},scanLine:function(e){var t,i=this.opts.delimiter,n=this.opts.openDelimiter,r=this.opts.closeDelimiter;switch(t=e.split("\n").length-1,e){case n+i:case n+i+"_":this.mode=v.modes.EVAL;break;case n+i+"=":this.mode=v.modes.ESCAPED;break;case n+i+"-":this.mode=v.modes.RAW;break;case n+i+"#":this.mode=v.modes.COMMENT;break;case n+i+i:this.mode=v.modes.LITERAL,this.source+='    ; __append("'+e.replace(n+i+i,n+i)+'")\n';break;case i+i+r:this.mode=v.modes.LITERAL,this.source+='    ; __append("'+e.replace(i+i+r,i+r)+'")\n';break;case i+r:case"-"+i+r:case"_"+i+r:this.mode==v.modes.LITERAL&&this._addOutput(e),this.mode=null,this.truncate=0===e.indexOf("-")||0===e.indexOf("_");break;default:if(this.mode){switch(this.mode){case v.modes.EVAL:case v.modes.ESCAPED:case v.modes.RAW:e.lastIndexOf("//")>e.lastIndexOf("\n")&&(e+="\n")}switch(this.mode){case v.modes.EVAL:this.source+="    ; "+e+"\n";break;case v.modes.ESCAPED:this.source+="    ; __append(escapeFn("+C(e)+"))\n";break;case v.modes.RAW:this.source+="    ; __append("+C(e)+")\n";break;case v.modes.COMMENT:break;case v.modes.LITERAL:this._addOutput(e)}}else this._addOutput(e)}this.opts.compileDebug&&t&&(this.currentLine+=t,this.source+="    ; __line = "+this.currentLine+"\n")}},e.escapeXML=n.escapeXML,e.__express=e.renderFile,e.VERSION=s,e.name="ejs","undefined"!=typeof window&&(window.ejs=e)}(A)),A);const f=c(g);var k="undefined"!=typeof window?window:null,w=null===k,C=w?void 0:k.document,v="addEventListener",y="removeEventListener",E="getBoundingClientRect",S="_a",I="_b",x="_c",b="horizontal",L=function(){return!1},B=w?"calc":["","-webkit-","-moz-","-o-"].filter(function(e){var t=C.createElement("div");return t.style.cssText="width:"+e+"calc(9px)",!!t.style.length}).shift()+"calc",P=function(e){return"string"==typeof e||e instanceof String},j=function(e){if(P(e)){var t=C.querySelector(e);if(!t)throw new Error("Selector "+e+" did not match a DOM element");return t}return e},Q=function(e,t,i){var n=e[t];return void 0!==n?n:i},T=function(e,t,i,n){if(t){if("end"===n)return 0;if("center"===n)return e/2}else if(i){if("start"===n)return 0;if("center"===n)return e/2}return e},J=function(e,t){var i=C.createElement("div");return i.className="gutter gutter-"+t,i},D=function(e,t,i){var n={};return P(t)?n[e]=t:n[e]=B+"("+t+"% - "+i+"px)",n},F=function(e,t){var i;return(i={})[e]=t+"px",i},N=function(e,t){if(void 0===t&&(t={}),w)return{};var i,n,r,s,o,a,c=e;Array.from&&(c=Array.from(c));var l=j(c[0]).parentNode,A=getComputedStyle?getComputedStyle(l):null,h=A?A.flexDirection:null,d=Q(t,"sizes")||c.map(function(){return 100/c.length}),p=Q(t,"minSize",100),u=Array.isArray(p)?p:c.map(function(){return p}),m=Q(t,"maxSize",1/0),g=Array.isArray(m)?m:c.map(function(){return m}),f=Q(t,"expandToMin",!1),B=Q(t,"gutterSize",10),P=Q(t,"gutterAlign","center"),N=Q(t,"snapOffset",30),V=Array.isArray(N)?N:c.map(function(){return N}),O=Q(t,"dragInterval",1),Y=Q(t,"direction",b),M=Q(t,"cursor",Y===b?"col-resize":"row-resize"),K=Q(t,"gutter",J),z=Q(t,"elementStyle",D),Z=Q(t,"gutterStyle",F);function U(e,t,n,r){var s=z(i,t,n,r);Object.keys(s).forEach(function(t){e.style[t]=s[t]})}function X(){return a.map(function(e){return e.size})}function R(e){return"touches"in e?e.touches[0][n]:e[n]}function q(e){var t=a[this.a],i=a[this.b],n=t.size+i.size;t.size=e/this.size*n,i.size=n-e/this.size*n,U(t.element,t.size,this[I],t.i),U(i.element,i.size,this[x],i.i)}function H(e){var i,n=a[this.a],r=a[this.b];this.dragging&&(i=R(e)-this.start+(this[I]-this.dragOffset),O>1&&(i=Math.round(i/O)*O),i<=n.minSize+n.snapOffset+this[I]?i=n.minSize+this[I]:i>=this.size-(r.minSize+r.snapOffset+this[x])&&(i=this.size-(r.minSize+this[x])),i>=n.maxSize-n.snapOffset+this[I]?i=n.maxSize+this[I]:i<=this.size-(r.maxSize-r.snapOffset+this[x])&&(i=this.size-(r.maxSize+this[x])),q.call(this,i),Q(t,"onDrag",L)(X()))}function W(){var e=a[this.a].element,t=a[this.b].element,n=e[E](),o=t[E]();this.size=n[i]+o[i]+this[I]+this[x],this.start=n[r],this.end=n[s]}function G(e){var t=function(e){if(!getComputedStyle)return null;var t=getComputedStyle(e);if(!t)return null;var i=e[o];return 0===i?null:i-=Y===b?parseFloat(t.paddingLeft)+parseFloat(t.paddingRight):parseFloat(t.paddingTop)+parseFloat(t.paddingBottom)}(l);if(null===t)return e;if(u.reduce(function(e,t){return e+t},0)>t)return e;var i=0,n=[],r=e.map(function(r,s){var o=t*r/100,a=T(B,0===s,s===e.length-1,P),c=u[s]+a;return o<c?(i+=c-o,n.push(0),c):(n.push(o-c),o)});return 0===i?e:r.map(function(e,r){var s=e;if(i>0&&n[r]-i>0){var o=Math.min(i,n[r]-i);i-=o,s=e-o}return s/t*100})}function _(){var e=this,i=a[e.a].element,n=a[e.b].element;e.dragging&&Q(t,"onDragEnd",L)(X()),e.dragging=!1,k[y]("mouseup",e.stop),k[y]("touchend",e.stop),k[y]("touchcancel",e.stop),k[y]("mousemove",e.move),k[y]("touchmove",e.move),e.stop=null,e.move=null,i[y]("selectstart",L),i[y]("dragstart",L),n[y]("selectstart",L),n[y]("dragstart",L),i.style.userSelect="",i.style.webkitUserSelect="",i.style.MozUserSelect="",i.style.pointerEvents="",n.style.userSelect="",n.style.webkitUserSelect="",n.style.MozUserSelect="",n.style.pointerEvents="",e.gutter.style.cursor="",e.parent.style.cursor="",C.body.style.cursor=""}function $(e){if(!("button"in e)||0===e.button){var i=this,n=a[i.a].element,r=a[i.b].element;i.dragging||Q(t,"onDragStart",L)(X()),e.preventDefault(),i.dragging=!0,i.move=H.bind(i),i.stop=_.bind(i),k[v]("mouseup",i.stop),k[v]("touchend",i.stop),k[v]("touchcancel",i.stop),k[v]("mousemove",i.move),k[v]("touchmove",i.move),n[v]("selectstart",L),n[v]("dragstart",L),r[v]("selectstart",L),r[v]("dragstart",L),n.style.userSelect="none",n.style.webkitUserSelect="none",n.style.MozUserSelect="none",n.style.pointerEvents="none",r.style.userSelect="none",r.style.webkitUserSelect="none",r.style.MozUserSelect="none",r.style.pointerEvents="none",i.gutter.style.cursor=M,i.parent.style.cursor=M,C.body.style.cursor=M,W.call(i),i.dragOffset=R(e)-i.end}}Y===b?(i="width",n="clientX",r="left",s="right",o="clientWidth"):"vertical"===Y&&(i="height",n="clientY",r="top",s="bottom",o="clientHeight"),d=G(d);var ee=[];function te(e){var t=e.i===ee.length,i=t?ee[e.i-1]:ee[e.i];W.call(i);var n=t?i.size-e.minSize-i[x]:e.minSize+i[I];q.call(i,n)}return(a=c.map(function(e,t){var n,r={element:j(e),size:d[t],minSize:u[t],maxSize:g[t],snapOffset:V[t],i:t};if(t>0&&((n={a:t-1,b:t,dragging:!1,direction:Y,parent:l})[I]=T(B,t-1==0,!1,P),n[x]=T(B,!1,t===c.length-1,P),"row-reverse"===h||"column-reverse"===h)){var s=n.a;n.a=n.b,n.b=s}if(t>0){var o=K(t,Y,r.element);!function(e,t,n){var r=Z(i,t,n);Object.keys(r).forEach(function(t){e.style[t]=r[t]})}(o,B,t),n[S]=$.bind(n),o[v]("mousedown",n[S]),o[v]("touchstart",n[S]),l.insertBefore(o,r.element),n.gutter=o}return U(r.element,r.size,T(B,0===t,t===c.length-1,P),t),t>0&&ee.push(n),r})).forEach(function(e){var t=e.element[E]()[i];t<e.minSize&&(f?te(e):e.minSize=t)}),{setSizes:function(e){var t=G(e);t.forEach(function(e,i){if(i>0){var n=ee[i-1],r=a[n.a],s=a[n.b];r.size=t[i-1],s.size=e,U(r.element,r.size,n[I],r.i),U(s.element,s.size,n[x],s.i)}})},getSizes:X,collapse:function(e){te(a[e])},destroy:function(e,t){ee.forEach(function(n){if(!0!==t?n.parent.removeChild(n.gutter):(n.gutter[y]("mousedown",n[S]),n.gutter[y]("touchstart",n[S])),!0!==e){var r=z(i,n.a.size,n[I]);Object.keys(r).forEach(function(e){a[n.a].element.style[e]="",a[n.b].element.style[e]=""})}})},parent:l,pairs:ee}};class V{constructor(){n(this,"_cache",{})}on(e,t){const i=this._cache[e]=this._cache[e]||[];return-1===i.indexOf(t)&&i.push(t),this}emit(e,...t){const i=this._cache[e];return Array.isArray(i)&&i.forEach(e=>{e(...t)}),this}off(e,t){const i=this._cache[e];if(Array.isArray(i))if(t){const e=i.indexOf(t);-1!==e&&i.splice(e,1)}else i.length=0;return this}}class O extends V{constructor(e,t,i){super(),n(this,"split"),n(this,"leftId"),n(this,"rightId"),this.leftId=e,this.rightId=t,this.split=N([`#${e}`,`#${t}`],{sizes:i?[35,65]:[0,100],minSize:[0,800],gutterSize:5,gutter(e,t){const n=document.createElement("div");return i||(n.style.display="none"),n.className=`gutter gutter-${t}`,n.id="split-gutter",n},onDrag:()=>{this.getSize(0)<5?this.collapse():(document.getElementById("split-gutter").style.display="block",this.emit("layout"))}}),i||(document.getElementById(this.rightId).style.width="100%")}getSize(e){return this.split.getSizes()[e]}expand(){this.split.setSizes([35,65]),document.getElementById("split-gutter").style.display="block",this.emit("layout")}collapse(){this.split.collapse(0),document.getElementById("split-gutter").style.display="none",document.getElementById(this.rightId).style.width="100%",this.emit("layout")}setLeft(e){const t=document.getElementById(this.leftId);t&&(t.innerHTML=e)}setRight(e){const t=document.getElementById(this.rightId);t&&(t.innerHTML=e)}}const Y={theme:"vs-dark",formatOnPaste:!0,fontSize:14,scrollbar:{verticalScrollbarSize:2}};class M extends V{constructor(e){super(),n(this,"editor"),n(this,"uiEditor"),n(this,"resetBtn"),n(this,"runBtn"),n(this,"jsContainerId","js-editor"),n(this,"originScript"),n(this,"config"),n(this,"showJsBtn"),n(this,"showUIBtn"),n(this,"jsEditorContainer"),n(this,"uiEditorContainer"),n(this,"fileNameEle"),this.config=e,this.editor=s.editor.create(document.getElementById(this.jsContainerId),{language:"javascript",value:"",...Y}),this.fetchScript(),this.config.hasPannel&&this.fetchUISource().then(e=>{this.uiEditor=s.editor.create(document.getElementById("ui-editor"),{language:a.apiConf.UIFileLanguage,readOnly:!0,...Y}),this.uiEditor.setValue(e)}),this.bindEvent()}async fetchScript(){const e=await fetch(`${a.apiConf.resourcePublicPath}/${this.config.main}/map.js`);this.originScript=await e.text(),this.editor.setValue(this.originScript),this.emit("run",this.originScript)}async fetchUISource(e){let t;if(a.apiConf.fetchUICode){t=await a.apiConf.fetchUICode(this.config.main,e)}else if(e){const i=await fetch(e.url.replaceAll("{main}",`${a.apiConf.resourcePublicPath}/${this.config.main}`));t=await i.text()}else{const e=await fetch(a.apiConf.UIFile(this.config.main));t=await e.text()}return t}resize(){this.editor&&this.editor.layout(),this.uiEditor&&this.uiEditor.layout()}bindEvent(){this.jsEditorContainer||(this.jsEditorContainer=document.getElementById("js-editor")),this.uiEditorContainer||(this.uiEditorContainer=document.getElementById("ui-editor")),this.resetBtn||(this.resetBtn=document.getElementById("jsreset")),this.runBtn||(this.runBtn=document.getElementById("jsrun")),this.fileNameEle||(this.fileNameEle=document.getElementById("filename")),this.config.hasPannel&&(this.showJsBtn||(this.showJsBtn=document.getElementById("showjs-button")),this.showUIBtn||(this.showUIBtn=document.getElementById("showui-button")),this.showJsBtn.addEventListener("click",()=>{this.jsEditorContainer.style.display="block",this.uiEditorContainer.style.display="none",this.fileNameEle.innerHTML="JS代码",this.resize()}),this.showUIBtn.addEventListener("click",()=>{this.uiEditorContainer.style.display="block",this.jsEditorContainer.style.display="none",this.fileNameEle.innerHTML="UI面板代码（只读）",this.resize()}),document.querySelectorAll(".uidep-item").forEach(e=>{e.addEventListener("click",async e=>{const t=e.currentTarget;try{const e=JSON.parse(t.dataset.file);console.log(e);const i=await this.fetchUISource(e);console.log(i),this.uiEditor.setValue(i),this.uiEditorContainer.style.display="block",this.jsEditorContainer.style.display="none",this.fileNameEle.innerHTML=`${e.name}（只读）`,this.resize()}catch(i){throw i}})})),this.resetBtn.addEventListener("click",()=>{this.editor.setValue(this.originScript),this.emit("run",this.scriptFilter(this.originScript))}),this.runBtn.addEventListener("click",()=>{this.emit("run",this.scriptFilter(this.editor.getValue()))}),window.addEventListener("resize",()=>{this.resize()}),document.addEventListener("keydown",e=>{if(!e.ctrlKey)return;const t=String.fromCharCode(e.keyCode).toLowerCase();"s"===t?(e.preventDefault(),this.emit("run",this.scriptFilter(this.editor.getValue()))):"r"===t&&(e.preventDefault(),this.emit("run",this.scriptFilter(this.originScript)))})}scriptFilter(e){return e=(e=(e=e.replace(/export let /gm,"var ")).replace(/export const /gm,"var ")).replace(/export /gm,""),e=(e="mars3d"===a.apiConf.packageName?(e=e.replace('import * as mars3d from "mars3d"',"")).replace("const Cesium = mars3d.Cesium",""):(e=e.replace('import * as mars2d from "mars2d"',"")).replace("const L = mars3d.L","")).replace(/import /gm,"// import ")}}class K extends V{constructor(e){super(),n(this,"sanboxWapper"),n(this,"scriptDoc"),n(this,"htmlTemp"),n(this,"resourcePublicPath"),n(this,"exampleConfig"),this.exampleConfig=e,this.sanboxWapper=document.getElementById("sanbox"),this.resourcePublicPath=a.apiConf.resourcePublicPath||"",this.getTemp()}writeScriptDoc(e){this.scriptDoc=a.scriptFilter(e),this.loadFramePage()}async loadFramePage(){const{main:e}=this.exampleConfig;if(this.scriptDoc&&this.htmlTemp){const t=this.createFrame().contentWindow.document,i=`\n        <script type="text/javascript">\n        "use script"; // 开发环境建议开启严格模式\n        window.currentPath = "${this.resourcePublicPath}/${e}/"; // 当前示例的配置\n        ${this.scriptDoc}\n        <\/script> `,n=`\n      <script type="text/javascript">\n        window.currentPath = "${this.resourcePublicPath}/${e}/"; // 当前示例的配置\n      <\/script>\n      <script type="text/javascript" src="${this.resourcePublicPath}/${e}/map.js"><\/script> `,r=this.htmlTemp.replace("\x3c!-- script-output --\x3e",a.apiConf.alwaysUseOrigin?n:i);t&&(t.open(),t.write(r),t.close())}}async getTemp(){const{temp:e="template"}=this.exampleConfig;console.log(`${a.apiConf.baseUrl}temp/${e}.html`);const t=await fetch(`${a.apiConf.baseUrl}temp/${e}.html`);this.htmlTemp=this.mixTemp(await t.text()),this.loadFramePage()}mixTemp(e){const{libs:t=[],resources:i=[],main:n}=this.exampleConfig;let r="";return a.getLibs(n,t,i).forEach(({url:e})=>{/\.css/.test(e)?r+='<link href="'+e+'"  rel="stylesheet" >':r+='<script src="'+e+'"  type="text/javascript" ><\/script>'}),e.replace("\x3c!-- resource-output --\x3e",r)}createFrame(){const e=document.createElement("iframe");return this.sanboxWapper.innerHTML="",e.setAttribute("id","marsgis-mappage"),e.setAttribute("name","marsgis-mappage"),this.sanboxWapper.append(e),e}}class z extends V{constructor(e){super(),n(this,"config",{}),this.mergeConfig(e)}mergeConfig(e){const t={configFetchType:"online",code:"1",fullScreen:"0",packageName:"mars3d",framework:"es5",baseUrl:"/",resourcePublicPath:"example",configLibs:{},libPublicPath:"/",thumbnailPublicPath:"/config/thumbnail/",homepage:"http://mars3d.cn/example.html",configSourceUrl:"config/example.json",expandBtnText:"查看源代码",collapseBtnText:"收起源代码",UIFileLanguage:"html",...e};if("1"===t.fullScreen&&(t.code="0"),t.code="1"!==t.code?"0":"1","function"!=typeof t.UIFile){const e=t.UIFile||"{main}/index.html";t.UIFile=i=>`${t.resourcePublicPath}/${e.replaceAll("{main}",i)}`}this.config=t,a.setConfig(t)}getLinks(e){let t=this.config,i=e.dev?`${e.dev}.html`:`guide/project/example-${t.framework}.html`,n=e.api||"Map.html";return[{id:"btn-jump-api",title:"API",description:"查阅API文档",url:`http://${t.packageName}.cn/api/${n}`,icon:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='24'%20height='24'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3crect%20width='48'%20height='48'%20fill='white'%20fill-opacity='0.01'/%3e%3cpath%20d='M37%2022.0001L34%2025.0001L23%2014.0001L26%2011.0001C27.5%209.50002%2033%207.00005%2037%2011.0001C41%2015.0001%2038.5%2020.5%2037%2022.0001Z'%20fill='none'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M42%206L37%2011'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M11%2025.9999L14%2022.9999L25%2033.9999L22%2036.9999C20.5%2038.5%2015%2041%2011%2036.9999C7%2032.9999%209.5%2027.5%2011%2025.9999Z'%20fill='none'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M23%2032L27%2028'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M6%2042L11%2037'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M16%2025L20%2021'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e"},{id:"btn-jump-dev",title:"教程",description:"学习开发教程",url:`http://${t.packageName}.cn/dev/${i}`,icon:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='24'%20height='24'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M7%2037C7%2029.2967%207%2011%207%2011C7%207.68629%209.68629%205%2013%205H35V31C35%2031%2018.2326%2031%2013%2031C9.7%2031%207%2033.6842%207%2037Z'%20fill='none'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linejoin='round'/%3e%3cpath%20d='M35%2031C35%2031%2014.1537%2031%2013%2031C9.68629%2031%207%2033.6863%207%2037C7%2040.3137%209.68629%2043%2013%2043C15.2091%2043%2025.8758%2043%2041%2043V7'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M14%2037H34'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e"},{id:"btn-jump-github",title:"下载",description:"下载源代码",url:`https://gitee.com/marsgis/${t.packageName}-${t.framework}-example`,icon:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='24'%20height='24'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3crect%20width='48'%20height='48'%20fill='white'%20fill-opacity='0.01'/%3e%3cpath%20d='M29.3444%2030.4767C31.7481%2029.9771%2033.9292%2029.1109%2035.6247%2027.8393C38.5202%2025.6677%2040%2022.3137%2040%2019C40%2016.6754%2039.1187%2014.5051%2037.5929%2012.6669C36.7427%2011.6426%2039.2295%204.00001%2037.02%205.02931C34.8105%206.05861%2031.5708%208.33691%2029.8726%207.8341C28.0545%207.29577%2026.0733%207.00001%2024%207.00001C22.1992%207.00001%2020.4679%207.22313%2018.8526%207.63452C16.5046%208.23249%2014.2591%206.00001%2012%205.02931C9.74086%204.05861%2010.9736%2011.9633%2010.3026%2012.7946C8.84119%2014.6052%208%2016.7289%208%2019C8%2022.3137%209.79086%2025.6677%2012.6863%2027.8393C14.6151%2029.2858%2017.034%2030.2077%2019.7401%2030.6621'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'/%3e%3cpath%20d='M19.7402%2030.662C18.5817%2031.9372%2018.0024%2033.148%2018.0024%2034.2946C18.0024%2035.4411%2018.0024%2038.3465%2018.0024%2043.0108'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'/%3e%3cpath%20d='M29.3443%2030.4767C30.4421%2031.9175%2030.991%2033.2112%2030.991%2034.3577C30.991%2035.5043%2030.991%2038.3886%2030.991%2043.0108'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'/%3e%3cpath%20d='M6%2031.2156C6.89887%2031.3255%207.56554%2031.7388%208%2032.4555C8.65169%2033.5304%2011.0742%2037.5181%2013.8251%2037.5181C15.6591%2037.5181%2017.0515%2037.5181%2018.0024%2037.5181'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'/%3e%3c/svg%3e",children:[{id:"btn-jump-child-github",title:"Gihub开源",target:"_blank",url:`https://github.com/marsgis/${t.packageName}-${t.framework}-example`},{id:"btn-jump-child-gitee",title:"Gitee开源",target:"_blank",url:`https://gitee.com/marsgis/${t.packageName}-${t.framework}-example`}]},{id:"btn-jump-stack",title:"技术栈",description:"切换技术栈",icon:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='24'%20height='24'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%3e%3crect%20width='48'%20height='48'%20fill='white'%20fill-opacity='0.01'/%3e%3cg%3e%3cpath%20d='M10%208V16H38L42%2012L38%208L10%208Z'%20fill='none'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linejoin='round'/%3e%3cpath%20d='M38%2023V31H10L6%2027L10%2023L38%2023Z'%20fill='none'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linejoin='round'/%3e%3cpath%20d='M24%2031V44'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M24%2016V23'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M24%204V8'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M19%2044H29'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e",children:(()=>{const e=[];return"es5"!==t.framework&&e.push({id:"btn-jump-es5",title:"原生JS版",target:"_self",url:"javascript: toOtherFramework('es5');"}),"vue"!==t.framework&&e.push({id:"btn-jump-vue",title:"Vue版",target:"_self",url:"javascript: toOtherFramework('vue');"}),e})()}]}}const Z={theme:"vs-dark",formatOnPaste:!0,fontSize:14,scrollbar:{verticalScrollbarSize:2}};class U extends V{constructor(e){super(),n(this,"editor"),n(this,"resetBtn"),n(this,"runBtn"),n(this,"jsContainerId","js-editor"),n(this,"originScript"),n(this,"config"),n(this,"showJsBtn"),n(this,"showUIBtn"),n(this,"jsEditorContainer"),n(this,"uiEditorContainer"),n(this,"fileNameEle"),this.config=e,this.editor=s.editor.create(document.getElementById(this.jsContainerId),{language:"html",value:"",...Z}),this.fetchScript(),this.bindEvent()}async fetchScript(){const e=await fetch(`${a.apiConf.resourcePublicPath}/${this.config.main}/index.html`),t=await fetch(`${a.apiConf.resourcePublicPath}/${this.config.main}/map.js`);this.originScript=this.mixinTemp(await e.text(),await t.text()),this.editor.setValue(this.originScript),this.emit("run",this.originScript)}mixinTemp(e,t){let i=`${a.apiConf.resourcePublicPath}/${this.config.main}`;const n=`\n    <script type="text/javascript">\n      "use script"; // 开发环境建议开启严格模式\n      window.currentPath = "${i}/";// 当前示例的目录\n      ${t=t.split("\n").join("\n      ")}\n    <\/script>`;let r=e.replace(new RegExp(/[\s]*<meta[^>]*?\/?>/,"gm"),"").replace('<script src="./map.js"><\/script>',n).replace(new RegExp(/\<link href="\./,"gm"),`<link href="${i}`).replace(new RegExp(/\<script src="\./,"gm"),`<script src="${i}`);return a.scriptFilter(r)}resize(){this.editor&&this.editor.layout()}bindEvent(){this.resetBtn||(this.resetBtn=document.getElementById("jsreset")),this.runBtn||(this.runBtn=document.getElementById("jsrun")),this.resetBtn.addEventListener("click",()=>{this.editor.setValue(this.originScript),this.emit("run",a.scriptFilter(this.originScript))}),this.runBtn.addEventListener("click",()=>{this.emit("run",a.scriptFilter(this.editor.getValue()))}),window.addEventListener("resize",()=>{this.resize()}),document.addEventListener("keydown",e=>{if(!e.ctrlKey)return;const t=String.fromCharCode(e.keyCode).toLowerCase();"s"===t?(e.preventDefault(),this.emit("run",a.scriptFilter(this.editor.getValue()))):"r"===t&&(e.preventDefault(),this.emit("run",a.scriptFilter(this.originScript)))})}}class X extends V{constructor(){super(),n(this,"sanboxWapper"),n(this,"innerHtml"),this.sanboxWapper=document.getElementById("sanbox")}writeHtml(e){this.innerHtml=e,this.loadFramePage()}async loadFramePage(){const e=this.createFrame().contentWindow.document;e&&(e.open(),e.write(this.innerHtml),e.close())}createFrame(){const e=document.createElement("iframe");return this.sanboxWapper.innerHTML="",e.setAttribute("id","marsgis-mappage"),e.setAttribute("name","marsgis-mappage"),this.sanboxWapper.append(e),e}}return e.Editor=class extends z{constructor(){super(...arguments),n(this,"container"),n(this,"fullName"),n(this,"leftContainerId","split-left"),n(this,"rightContainerId","split-right"),n(this,"toogleBtn"),n(this,"split"),n(this,"jsEditor"),n(this,"htmlEditor"),n(this,"sanBox"),n(this,"htmlRunner"),n(this,"mapWork")}async renderContainer({container:e,exampleId:t,exampleKey:i,onlyHtml:n=!1}){if(!e)throw new Error("container 不能为空");if(!t)throw alert("id不能为空"),new Error("id不能为空");t=t.replace(/\\/gm,"/"),this.container=e,this.container.innerHTML=f.render('<div class="marsgis-editor__container">\r\n  <div id="split-left"></div>\r\n  <div id="split-right"></div>\r\n</div>\r\n');const r="1"===this.config.code;this.split=new O(this.leftContainerId,this.rightContainerId,r);let s=await a.getCompConfig(t,i);if(!s){const e="没有查询到当前id对应的配置";window.$message?window.$message(e):alert(e),s={id:t,main:t,fullName:"临时测试页面",name:"临时测试页面",hasPannel:"1"===a.getQueryString("hasPannel")}}this.fullName=s.fullName;var o="localhost"===window.location.hostname?`//${a.apiConf.packageName}.cn`:"";return window.toOtherFramework=function(e){let t=`${o}/editor-${e}.html?id=${encodeURI(s.main)}`;new URL(window.location.href).searchParams.forEach((e,i)=>{"id"!==i&&"code"!==i&&(t+=`&${i}=${e}`)}),window.open(t)},this.split.setLeft(f.render('<div class="editor-container">\r\n  <div class="editor-header" >\r\n    <div class="header-left">\r\n      <% if(!onlyHtml) { %>\r\n        <div id="showjs-button" class="left-handles showjs" title="查看JS代码">\r\n          <img src="<%= javascriptIcon %>" alt="js" />\r\n        </div>\r\n        <% if (hasPannel) { %>\r\n          <div class="mars-dropdown">\r\n            <div id="showui-button" class="left-handles showui">\r\n              <img src="<%= uiIcon %>" alt="react" />\r\n            </div>\r\n            <div class="mars-dropdown-content" id="uilist-container"></div>\r\n          </div>\r\n          <% } %>\r\n            <% }%>\r\n              <div class="mars-dropdown">\r\n                <div class="left-handles">\r\n                  <img src="<%= fileIcon %>" alt="依赖" />\r\n                </div>\r\n                <div class="mars-dropdown-content" id="deplist-container"></div>\r\n              </div>\r\n    </div>\r\n    <span class="fileTip" id="filename">\r\n      <% if(!onlyHtml) { %>JS代码<% } else {%>源代码<% } %>\r\n    </span>\r\n    <div class="header-right">\r\n      <span id="jsreset" class="refresh mars-link-btn-primary" title="还原为初始代码，快捷键：Ctrl + R">\r\n        <img class="editor-icon" src="<%= refreshIcon %>" />\r\n        <span>重置</span>\r\n      </span>\r\n      <span id="jsrun" class="mars-link-btn-primary" title="运行源码，快捷键：Ctrl + S">\r\n        <img class="editor-icon" src="<%= runIcon %>" />\r\n        <span>运行</span>\r\n      </span>\r\n    </div>\r\n  </div>\r\n\r\n\r\n  <div class="editor-code" id="js-editor"></div>\r\n  <div class="editor-code" id="ui-editor"></div>\r\n</div>',{onlyHtml:n,javascriptIcon:"data:image/png;base64,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",uiIcon:"data:image/png;base64,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",fileIcon:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='24'%20height='24'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%3e%3cpath%20d='M0%200H48V48H0V0Z'%20fill='white'%20fill-opacity='0.01'/%3e%3cg%3e%3cg%3e%3crect%20width='48'%20height='48'%20fill='white'%20fill-opacity='0.01'/%3e%3cpath%20d='M12%209.92704V7C12%205.34315%2013.3431%204%2015%204H41C42.6569%204%2044%205.34315%2044%207V33C44%2034.6569%2042.6569%2036%2041%2036H38.0174'%20stroke='%231890ff'%20stroke-width='4'/%3e%3crect%20x='4'%20y='10'%20width='34'%20height='34'%20rx='3'%20fill='none'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linejoin='round'/%3e%3c/g%3e%3cg%3e%3cg%3e%3cpath%20d='M18.4396%2023.1098L23.7321%2017.6003C25.1838%2016.1486%2027.5693%2016.1806%2029.0604%2017.6717C30.5515%2019.1628%2030.5835%2021.5483%2029.1319%2023L27.2218%2025.0228'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M13.4661%2028.7469C12.9558%2029.2573%2011.9006%2030.2762%2011.9006%2030.2762C10.4489%2031.7279%2010.4095%2034.3152%2011.9006%2035.8063C13.3917%2037.2974%2015.7772%2037.3294%2017.2289%2035.8777L22.3931%2031.1894'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M18.6631%2028.3283C17.9705%2027.6357%2017.5927%2026.7501%2017.5321%2025.8547C17.4624%2024.8225%2017.8143%2023.7774%2018.5916%2023'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M22.3218%2025.8611C23.8129%2027.3522%2023.8449%2029.7377%2022.3932%2031.1894'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",hasPannel:s.hasPannel,refreshIcon:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='24'%20height='24'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M42%208V24'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M6%2024L6%2040'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M6%2024C6%2033.9411%2014.0589%2042%2024%2042C28.8556%2042%2033.2622%2040.0774%2036.5%2036.9519'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M42.0007%2024C42.0007%2014.0589%2033.9418%206%2024.0007%206C18.9152%206%2014.3223%208.10896%2011.0488%2011.5'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e",runIcon:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='24'%20height='24'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3crect%20width='48'%20height='48'%20fill='white'%20fill-opacity='0.01'/%3e%3cpath%20d='M24%2044C35.0457%2044%2044%2035.0457%2044%2024C44%2012.9543%2035.0457%204%2024%204C12.9543%204%204%2012.9543%204%2024C4%2035.0457%2012.9543%2044%2024%2044Z'%20fill='none'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linejoin='round'/%3e%3cpath%20d='M20%2024V17.0718L26%2020.5359L32%2024L26%2027.4641L20%2030.9282V24Z'%20fill='none'%20stroke='%231890ff'%20stroke-width='4'%20stroke-linejoin='round'/%3e%3c/svg%3e"})),this.split.setRight(f.render('<div class="layout-right">\r\n  <div class="layout-right__header mars-fadeInDown <%= fullScreen ? \'hidden\' : \'\' %>">\r\n    <span class="mars-link-btn-primary" id="toogle-editor">\r\n      <img class="editor-icon" id="expand-icon" style="display: none;" src="<%= expandIcon %>" />\r\n      <img class="editor-icon" id="collapse-icon" src="<%= collapseIcon %>" />\r\n      <span id="toogle-text">\r\n        <%= toogleText %>\r\n      </span>\r\n    </span>\r\n    <p class="fullname" id="fullname">标题</p>\r\n    <div id="other-intro">\r\n      <% links.forEach(function(item){ %>\r\n\r\n        <% if (item.children) { %>\r\n          <div id="<%= item.id %>" class="mars-dropdown" title="<%= item.description %>">\r\n            <div class="left-handles">\r\n              <span class="mars-link-btn-primary">\r\n                <img class="editor-icon" src="<%= item.icon %>" />\r\n                <span>\r\n                  <%= item.title %>\r\n                </span>\r\n              </span>\r\n            </div>\r\n            <div class="mars-dropdown-content display-left">\r\n              <div class="mars-dropdown-title"><%= item.description %></div>\r\n              <% item.children.forEach(function(it){ %>\r\n                <a  id="<%= it.id %>" rel="noreferrer" class="mars-link-btn-primary mars-dropdown-item" target="<%= it.target %>" href="<%= it.url %>">\r\n                  <%= it.title %>\r\n                </a>\r\n                <% }); %>\r\n            </div>\r\n          </div>\r\n          <% } %>\r\n            <% if (!item.children) { %>\r\n              <a id="<%= item.id %>" class="mars-link-btn-primary" target="_blank" rel="noreferrer"  title="<%= item.description %>"\r\n                href="<%= item.url %>">\r\n                <img class="editor-icon" src="<%= item.icon %>" />\r\n                <span>\r\n                  <%= item.title %>\r\n                </span>\r\n              </a>\r\n              <% } %>\r\n                <% }); %>\r\n    </div>\r\n  </div>\r\n\r\n  <div class="layout-right__content mars-main-view <%= fullScreen?\'layout-right__fillcontent\':\'\' %>" id="mars-main-view">\r\n    <div class="mars-ui-root" id="mars-ui-root"></div>\r\n    <div class="sanbox" id="sanbox"></div>\r\n  </div>\r\n</div>',{toogleText:"1"===this.config.code?this.config.collapseBtnText:this.config.expandBtnText,links:this.getLinks(s),fullScreen:"1"===this.config.fullScreen,expandIcon:"data:image/png;base64,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",collapseIcon:"data:image/png;base64,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"})),document.getElementById("fullname").innerText=s.fullName,document.getElementById("fullname").title=s.fullName,this.renderDepList(s),this.bindEvent(),s}async render({container:e,exampleId:t,exampleKey:i}){const n=await this.renderContainer({container:e,exampleId:t,exampleKey:i});Object.defineProperty(window,"mapWork",{get:()=>this.mapWork,set:e=>{this.mapWork=e,this.emit("loaded",n),n.hasPannel||this.useLifecycle()}}),n.hasPannel&&this.renderUiDepList(n),this.sanBox=new K(n),this.jsEditor=new M(n),this.jsEditor.on("run",e=>{this.sanBox&&this.sanBox.writeScriptDoc(e)}),this.split.on("layout",()=>{this.jsEditor.resize()})}renderDepList({main:e,libs:t,resources:i}){const n=a.getLibs(e,t,i);document.getElementById("deplist-container").innerHTML=f.render('<div class="mars-dropdown-title">当前示例依赖的资源文件 (请注意顺序)</div>\r\n<% depSources.forEach(function(dep){ %>\r\n<div class="mars-dropdown-item">\r\n  <a rel="noreferrer" class="mars-link-btn-primary" target="_blank" href="<%= dep.url %>"> <%= dep.name %> </a>\r\n</div>\r\n<% }); %>\r\n',{depSources:n})}renderUiDepList({pannelFiles:e}){e&&e[this.config.framework]&&(document.getElementById("uilist-container").innerHTML=f.render('<div class="mars-dropdown-title">查看UI面板代码,代码只读</div>\r\n<% pannelFiles.forEach(function(dep){ %>\r\n  <div class="mars-dropdown-item uidep-item" data-file="<%= JSON.stringify(dep) %>">\r\n    <span class="mars-link-btn-primary deplist-item">\r\n      <%= dep.name %>\r\n    </span>\r\n  </div>\r\n  <% }); %>',{pannelFiles:e[this.config.framework]}))}async renderHTML({container:e,exampleId:t,exampleKey:i}){const n=await this.renderContainer({container:e,exampleId:t,exampleKey:i,onlyHtml:!0});this.htmlEditor=new U(n),this.htmlRunner=new X,this.htmlEditor.on("run",e=>{this.htmlRunner&&this.htmlRunner.writeHtml(e)}),this.split.on("layout",()=>{this.htmlEditor.resize()})}useLifecycle(){const e=window._mapInstance;this.mapWork&&(!window.mars3d&&this.mapWork.mars3d&&(window.mars3d=this.mapWork.mars3d),!window.mars2d&&this.mapWork.mars2d&&(window.mars2d=this.mapWork.mars2d),this.mapWork.onMounted&&this.mapWork.onMounted(e))}bindEvent(){this.toogleBtn||(this.toogleBtn=document.getElementById("toogle-editor")),this.toogleBtn.addEventListener("click",()=>{const e=document.getElementById("expand-icon"),t=document.getElementById("collapse-icon"),i=document.getElementById("toogle-text");"0"===a.apiConf.code?(this.split.expand(),a.apiConf.code="1",i.innerText=this.config.collapseBtnText,e.style.display="none",t.style.display="inline-block"):(this.split.collapse(),a.apiConf.code="0",i.innerText=this.config.expandBtnText,t.style.display="none",e.style.display="inline-block")})}},e.Util=a,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e}({},monaco);