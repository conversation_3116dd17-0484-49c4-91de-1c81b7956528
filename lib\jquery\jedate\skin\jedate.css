/**
 @Name : jeDate V6.5.0 日期控件
 @Author: <PERSON><PERSON> <PERSON><PERSON><PERSON>
 @QQ群：516754269
 @官网：http://www.jemui.com/ 或 https://github.com/singod/jeDate
 */
@font-face {font-family: "jedatefont";
    src: url('jedatefont.eot?t=1510763148800'); /* IE9*/
    src: url('jedatefont.eot?t=1510763148800#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('jedatefont.woff?t=1510763148800') format('woff'),
    url('jedatefont.ttf?t=1510763148800') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
    url('jedatefont.svg?t=1510763148800#jedatefont') format('svg'); /* iOS 4.1- */
}
  
.jedatefont {font-family:"jedatefont" !important;font-style:normal;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing: grayscale;}
.jedate {
    height: auto;
    font-family: 'PingFangSC-Light','PingFang SC','Segoe UI','Lucida Grande','NotoSansHans-Light','Microsoft YaHei', '\5FAE\8F6F\96C5\9ED1', STHeiti, 'WenQuanYi Micro Hei', SimSun, sans-serif;
    font-size: 12px;
    cursor: default;
    margin: 0;
    padding: 0;
    overflow: hidden;
    position: relative;
    border-radius: 4px;
    display: inline-block;
    border: 1px solid #e2e2e2;
    box-shadow: 0 1px 6px rgba(0,0,0,.15);
    background: rgba(30,36,50,0.9);
}
.jedate *{margin: 0;padding: 0;list-style-type:none;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;font-style:normal;font-family:'PingFangSC-Light','PingFang SC','Segoe UI','Lucida Grande','NotoSansHans-Light','Microsoft YaHei', '\5FAE\8F6F\96C5\9ED1', STHeiti, 'WenQuanYi Micro Hei', SimSun, sans-serif;}

.jedate table thead,.jedate table td{border: 1px #fff solid;}
.jedate ul,.jedate ol,.jedate li,.jedate dl{list-style-type:none;font-style:normal;font-weight: 300;}

.jedate .yearprev{left:0;font-size: 14px;}
.jedate .monthprev{left:25px;font-size: 14px;}
.jedate .yearnext{right:0;font-size: 14px;}
.jedate .monthnext{right:25px;font-size: 14px;}
.jedate .jedate-tips{position: absolute; top: 40%; left: 50%;z-index: 800; width: 200px; margin-left: -100px; line-height: 20px; padding: 15px; text-align: center; font-size: 12px; color: #ff0000;background-color: #FFFEF4;border: 1px rgb(247, 206, 57) solid;display: none;}
.jedate .timecontent ul::-webkit-scrollbar,.jedate-menu::-webkit-scrollbar{height:6px;width:6px;margin-right:5px;background-color: #f5f5f5;transition:all 0.3s ease-in-out;border-radius:0px}
.jedate .timecontent ul::-webkit-scrollbar-track,.jedate-menu::-webkit-scrollbar-track { -webkit-border-radius: 0px;border-radius: 0px;}
.jedate .timecontent ul::-webkit-scrollbar-thumb,.jedate-menu::-webkit-scrollbar-thumb{-webkit-border-radius: 0px;border-radius: 0px;background: rgba(0,0,0,0.5); }
.jedate .timecontent ul::-webkit-scrollbar-thumb:hover,.jedate-menu::-webkit-scrollbar-thumb:hover{background:rgba(0,0,0,0.6)}
.jedate .timecontent ul::-webkit-scrollbar-thumb:active,.jedate-menu::-webkit-scrollbar-thumb:active{background:rgba(0,0,0,0.8)}
.jedate .timecontent ul::-webkit-scrollbar-thumb:window-inactive,.jedate-menu::-webkit-scrollbar-thumb:window-inactive {background: rgba(0,0,0,0.4);}
.jedate .jedate-hmsmask{width:100%;display: block;background-color: rgba(0,0,0,.7);background-color:rgba(30,36,50,0.9);position: absolute;top: 0;left:0;right:0;bottom: 36px;z-index: 100;}
.jedatetipscon{color:#333; float:left; overflow:hidden;background-color: #FFFEF4; line-height:22px;padding:6px;border: 1px rgb(247, 206, 57) solid;font-style:normal;font-family: Arial, "\5b8b\4f53", 'sans-serif';font-size:12px;font-weight: 300;}
.jedatetipscon p{padding: 0;margin: 0;font-size:12px;}
.jedatetipscon p.red{color: #ff0000;}

.jedate.leftmenu{padding-left: 90px;}
.jedate .jedate-menu{width:90px;position: absolute;top: 0;left:0;bottom: 0;z-index: 10;background: #f2f2f2;border-right: 1px solid #efefef;border-radius: 4px 0 0 4px;overflow:auto;display: block;padding:4px 0;}
.jedate .jedate-menu p{height: 30px;line-height: 30px;padding-left: 8px;overflow:hidden;font-size: 12px;cursor: pointer;}
.jedate .jedate-menu p:hover{background-color: #00A680;color:#FFFFFF;}
.jedate .jedate-wrap{min-width:230px;background: rgba(30,36,50,0.6);overflow: hidden;}
.jedate .jedate-pane{width: 230px;float: left;overflow: hidden;}
.jedate .jedate-header{width:100%;height:36px;line-height: 36px;float: left;background-color: #f2f2f2;text-align: center;font-size: 14px;padding: 0 50px;position: relative;}
.jedate .jedate-header em{width:25px;height:36px;line-height: 36px;position:absolute;color: #666;top:0;background-repeat: no-repeat;background-position: center center;cursor: pointer;}
.jedate .jedate-header .ymbtn{padding: 8px;border-radius: 4px;cursor:pointer;font-size: 14px;}
/* .jedate .jedate-header em:hover,.jedate .jedate-header .ymbtn:hover{color: #00A680;} */
.jedate .jedate-content{width:100%;height: 220px;float: left;padding: 5px;overflow: hidden;}
.jedate .jedate-content.bordge{border-left: 1px #e9e9e9 solid;}
.jedate .jedate-content .yeartable,.jedate .jedate-content .monthtable{width: 100%;border-collapse: collapse;border-spacing: 0;border: 1px solid #fff;}
.jedate .jedate-content .yeartable td,.jedate .jedate-content .monthtable td{width:73px;height: 51px;line-height: 51px;text-align:center; position:relative; overflow:hidden;font-size: 14px;}
.jedate .jedate-content .yeartable td span,.jedate .jedate-content .monthtable td span{padding: 8px 10px;border: 1px solid #fff;}
.jedate .jedate-content .yeartable td.action span,.jedate .jedate-content .monthtable td.action span,
.jedate .jedate-content .yeartable td.action span:hover,.jedate .jedate-content .monthtable td.action span:hover{background-color:#00A680;border:1px #00A680 solid;color:#fff;}
.jedate .jedate-content .yeartable td span:hover,.jedate .jedate-content .monthtable td span:hover{background-color:#f2f2f2;border: 1px #f2f2f2 solid;}
.jedate .jedate-content .yeartable td.disabled span,.jedate .jedate-content .monthtable td.disabled span,
.jedate .jedate-content .yeartable td.disabled span:hover,.jedate .jedate-content .monthtable td.disabled span:hover{color:#bbb;background-color:rgba(30,36,50,0.9);border: 1px solid #fff;}
.jedate .jedate-content .yeartable td.contain span,.jedate .jedate-content .monthtable td.contain span,
.jedate .jedate-content .yeartable td.contain span:hover,.jedate .jedate-content .monthtable td.contain span:hover{background-color: #D0F0E3;border:1px #D0F0E3 solid;}

.jedate.grid .daystable thead,.jedate.grid .daystable td{border: 1px #f2f2f2 solid;}
.jedate .jedate-content .daystable{width: 100%;border-collapse: collapse;border-spacing: 0;1px solid #135b91}
.jedate .jedate-content .daystable thead{background: rgba(30,36,50,0.9);}
.jedate .jedate-content .daystable th{width:31px;height:27px;  text-align:center; position:relative; overflow:hidden;font-size: 12px;font-weight: 400;}
.jedate .jedate-content .daystable td{width:31px;height:30px;  text-align:center; position:relative; overflow:hidden;font-size: 14px;font-family: Arial, "\5b8b\4f53", 'sans-serif';}
.jedate .jedate-content .daystable td .nolunar{line-height:29px;font-size: 14px;font-family: Arial, "\5b8b\4f53", 'sans-serif';}
.jedate .jedate-content .daystable td .solar{height:14px;line-height:14px;font-size: 14px;padding-top: 2px;display: block;font-family: Arial, "\5b8b\4f53", 'sans-serif';}
.jedate .jedate-content .daystable td .lunar{height:15px;line-height:15px;font-size: 12px;overflow:hidden;display: block;font-family: Arial, "\5b8b\4f53", 'sans-serif';color: #888;transform: scale(.95);}
.jedate .jedate-content .daystable td.action,.jedate .jedate-content .daystable td.action:hover,
.jedate .jedate-content .daystable td.action .lunar{background-color: #00A680;color:#fff;}
.jedate .jedate-content .daystable td.other,.jedate .jedate-content .daystable td.other .nolunar,.jedate .jedate-content .daystable td.other .lunar{color:#00DDAA;}
.jedate .jedate-content .daystable td.disabled,.jedate .jedate-content .daystable td.disabled .nolunar,.jedate .jedate-content .daystable td.disabled .lunar{ color:#bbb;}
.jedate .jedate-content .daystable td.contain,.jedate .jedate-content .daystable td.contain:hover{background-color: #00DDAA;color:#fff;}
.jedate .jedate-content .daystable td.disabled:hover{background-color:rgba(30,36,50,0.9);}
.jedate .jedate-content .daystable td:hover{background-color:#f2f2f2;}
.jedate .jedate-content .daystable td.red{ color:#ff0000;}
.jedate .jedate-content .daystable td .marks{ width:5px; height:5px; background-color:#ff0000; -webkit-border-radius:50%;border-radius:50%; position:absolute; right:2px; top:4px;}
.jedate .jedate-content .daystable td.action .marks{ width:5px; height:5px; background-color:rgba(30,36,50,0.9); -webkit-border-radius:50%;border-radius:50%; position:absolute; right:2px; top:4px;}

.jedate .jedate-time{overflow: hidden;padding-bottom: 4px; background-color:rgba(30,36,50,0.9);position: absolute;top:0;right: 0;z-index: 150;}
.jedate .jedate-time .timepane{width:230px;float:left;}
.jedate .jedate-time .timeheader{width: 100%;float:left;height: 36px;line-height: 36px;background-color: #f2f2f2;text-align: center;font-size: 14px;position: relative;}
.jedate .jedate-time .timecontent{width: 100%;float:left;}
.jedate .jedate-time .hmstitle{width: 211px;margin: 0 auto;overflow: hidden;padding-top: 4px;text-align: center;}
.jedate .jedate-time .hmstitle p{width: 33.33%;float:left;height: 30px;line-height: 30px;font-size: 13px;}
.jedate .jedate-time .hmslist{width: 211px;margin: 0 auto 6px auto;border: 1px solid #ddd;border-right: none;overflow: hidden;}
.jedate .jedate-time .hmslist .hmsauto{height: 100%;margin: 0;text-align: center;}
.jedate .jedate-time .hmslist ul {width: 70px;height: 174px;float: left;border-right: 1px solid #ddd;overflow: hidden;}
.jedate .jedate-time .hmslist .hmsauto:hover ul {overflow-y: auto}
.jedate .jedate-time .hmslist ul li {width: 130%;padding-left:26px;text-align: left;height: 25px;line-height: 25px;font-size: 14px;font-family: Arial, "\5b8b\4f53", 'sans-serif';}
.jedate .jedate-time .hmslist ul li:hover{background-color: rgba(35, 68, 117, 0.85);}
.jedate .jedate-time .hmslist ul li.action,.jedate-time .hmslist ul li.action:hover{background-color: #00A680;color:#fff;}
.jedate .jedate-time .hmslist ul li.disabled{ background-color: #fbfbfb;color:#ccc;}
.jedate .jedate-time .hmslist ul li.disabled.action{ background-color: #00A680;color:#FFFFFF;filter:Alpha(opacity=30);opacity:.3; }

.jedate .jedate-footbtn{height: 36px;padding: 0 6px;border-top: 1px #e9e9e9 solid;overflow: hidden;}
.jedate .jedate-footbtn .timecon{line-height:28px;padding:0 5px;background-color:#00A680;color:#fff;display:block;float: left;font-size: 12px;margin-top:4px;border-radius:4px;overflow: hidden;}
.jedate .jedate-footbtn .btnscon{line-height:28px;margin-top:4px;display:block;float: right;font-size: 12px;border-radius:4px;overflow: hidden;}
.jedate .jedate-footbtn .btnscon span{float:left; padding:0 5px;border-right: 1px #fff solid;background-color:#00A680;color:#fff;display:block;height:28px;line-height:28px;text-align:center;overflow:hidden;}
.jedate .jedate-footbtn .btnscon span:last-child{border-right:none;}