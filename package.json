{"name": "mars3d-es5-example", "version": "3.10.0", "description": "Mars3D平台,原生JS版本功能示例", "main": "index.js", "scripts": {"serve": "http-server", "build": "gulp build", "serve:dist": "http-server ./dist", "lint": "eslint ./example*/**/*.{js,html} --fix", "build-read": "node build/build.js"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/runtime": "^7.27.6", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-html": "^6.2.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-promise": "^4.3.1", "fs-extra": "^10.1.0", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-cheerio": "^0.6.3", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-header": "^2.0.9", "gulp-htmlmin": "^5.0.1", "gulp-plumber": "^1.2.1", "gulp-uglify": "^3.0.2", "gulp-utf8-convert": "0.0.7", "gulp-util": "^3.0.8", "http-server": "^14.1.1", "mars3d": "~3.10.0", "prettier": "^2.8.8", "uglify-js": "^3.19.3"}, "repository": {"type": "git", "url": "https://github.com/marsgis/mars3d-es5-example.git"}, "bugs": {"url": "https://github.com/marsgis/mars3d-es5-example/issues", "email": "<EMAIL>"}, "keywords": ["marsgis", "mars3d", "cesium", "webgl", "gis", "3dgis", "webgis"], "author": "火星科技", "license": "Apache-2.0", "homepage": "http://mars3d.cn"}