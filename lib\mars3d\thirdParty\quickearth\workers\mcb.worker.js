/* eslint-disable */
(function(g, X) { const O = E; const u = g(); while ([]) { try { const f = parseInt(O(0x1b4)) / 0x1 + parseInt(O(0x1c0)) / 0x2 + -parseInt(O(0x1b0)) / 0x3 + parseInt(O(0x1b8)) / 0x4 * (parseInt(O(0x1c1)) / 0x5) + -parseInt(O(0x1b9)) / 0x6 + parseInt(O(0x1bc)) / 0x7 + -parseInt(O(0x1a5)) / 0x8 * (parseInt(O(0x1b5)) / 0x9); if (f === X) { break } else { u.push(u.shift()) } } catch (j) { u.push(u.shift()) } } }(d, 0x42cb2)); function d() { const K = ["buffer", "659414sWPSVl", "offset", "vertices", "length", "854482KCAikX", "295KRnwDj", "raw", "toFixed", "hasOuter", "3832OVfSKi", "bitValue", "not\x20supported\x20anaType", "anaType\x20not\x20set,use\x20default\x20:>", "values", "outerOffset", "push", "not\x20supported\x20grid\x20data\x20type\x20", "ySize", "levels", "anaType", "451815pZIejZ", "noShared", "outerScale", "type", "161613uBJqNl", "7407tswtki", "scale", "dataType", "26876IEDJrS", "1566414tEKjVt", "abs"]; d = function() { return K }; return d() } function E(g, X) { const u = d(); return E = function(f, j) { f = f - 0x1a4; const I = u[f]; return I }, E(g, X) } const triTable = [[-0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x8, 0x3, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x1, 0x9, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x8, 0x3, 0x9, 0x8, 0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x2, 0xa, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x8, 0x3, 0x1, 0x2, 0xa, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x2, 0xa, 0x0, 0x2, 0x9, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x2, 0x8, 0x3, 0x2, 0xa, 0x8, 0xa, 0x9, 0x8, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0xb, 0x2, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0xb, 0x2, 0x8, 0xb, 0x0, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x9, 0x0, 0x2, 0x3, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0xb, 0x2, 0x1, 0x9, 0xb, 0x9, 0x8, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0xa, 0x1, 0xb, 0xa, 0x3, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0xa, 0x1, 0x0, 0x8, 0xa, 0x8, 0xb, 0xa, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0x9, 0x0, 0x3, 0xb, 0x9, 0xb, 0xa, 0x9, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x8, 0xa, 0xa, 0x8, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0x7, 0x8, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0x3, 0x0, 0x7, 0x3, 0x4, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x1, 0x9, 0x8, 0x4, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0x1, 0x9, 0x4, 0x7, 0x1, 0x7, 0x3, 0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x2, 0xa, 0x8, 0x4, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0x4, 0x7, 0x3, 0x0, 0x4, 0x1, 0x2, 0xa, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x2, 0xa, 0x9, 0x0, 0x2, 0x8, 0x4, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x2, 0xa, 0x9, 0x2, 0x9, 0x7, 0x2, 0x7, 0x3, 0x7, 0x9, 0x4, -0x1, -0x1, -0x1, -0x1], [0x8, 0x4, 0x7, 0x3, 0xb, 0x2, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xb, 0x4, 0x7, 0xb, 0x2, 0x4, 0x2, 0x0, 0x4, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x0, 0x1, 0x8, 0x4, 0x7, 0x2, 0x3, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0x7, 0xb, 0x9, 0x4, 0xb, 0x9, 0xb, 0x2, 0x9, 0x2, 0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0xa, 0x1, 0x3, 0xb, 0xa, 0x7, 0x8, 0x4, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0xb, 0xa, 0x1, 0x4, 0xb, 0x1, 0x0, 0x4, 0x7, 0xb, 0x4, -0x1, -0x1, -0x1, -0x1], [0x4, 0x7, 0x8, 0x9, 0x0, 0xb, 0x9, 0xb, 0xa, 0xb, 0x0, 0x3, -0x1, -0x1, -0x1, -0x1], [0x4, 0x7, 0xb, 0x4, 0xb, 0x9, 0x9, 0xb, 0xa, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x5, 0x4, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x5, 0x4, 0x0, 0x8, 0x3, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x5, 0x4, 0x1, 0x5, 0x0, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x8, 0x5, 0x4, 0x8, 0x3, 0x5, 0x3, 0x1, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x2, 0xa, 0x9, 0x5, 0x4, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0x0, 0x8, 0x1, 0x2, 0xa, 0x4, 0x9, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x5, 0x2, 0xa, 0x5, 0x4, 0x2, 0x4, 0x0, 0x2, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x2, 0xa, 0x5, 0x3, 0x2, 0x5, 0x3, 0x5, 0x4, 0x3, 0x4, 0x8, -0x1, -0x1, -0x1, -0x1], [0x9, 0x5, 0x4, 0x2, 0x3, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0xb, 0x2, 0x0, 0x8, 0xb, 0x4, 0x9, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x5, 0x4, 0x0, 0x1, 0x5, 0x2, 0x3, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x2, 0x1, 0x5, 0x2, 0x5, 0x8, 0x2, 0x8, 0xb, 0x4, 0x8, 0x5, -0x1, -0x1, -0x1, -0x1], [0xa, 0x3, 0xb, 0xa, 0x1, 0x3, 0x9, 0x5, 0x4, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0x9, 0x5, 0x0, 0x8, 0x1, 0x8, 0xa, 0x1, 0x8, 0xb, 0xa, -0x1, -0x1, -0x1, -0x1], [0x5, 0x4, 0x0, 0x5, 0x0, 0xb, 0x5, 0xb, 0xa, 0xb, 0x0, 0x3, -0x1, -0x1, -0x1, -0x1], [0x5, 0x4, 0x8, 0x5, 0x8, 0xa, 0xa, 0x8, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x7, 0x8, 0x5, 0x7, 0x9, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x3, 0x0, 0x9, 0x5, 0x3, 0x5, 0x7, 0x3, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x7, 0x8, 0x0, 0x1, 0x7, 0x1, 0x5, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x5, 0x3, 0x3, 0x5, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x7, 0x8, 0x9, 0x5, 0x7, 0xa, 0x1, 0x2, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xa, 0x1, 0x2, 0x9, 0x5, 0x0, 0x5, 0x3, 0x0, 0x5, 0x7, 0x3, -0x1, -0x1, -0x1, -0x1], [0x8, 0x0, 0x2, 0x8, 0x2, 0x5, 0x8, 0x5, 0x7, 0xa, 0x5, 0x2, -0x1, -0x1, -0x1, -0x1], [0x2, 0xa, 0x5, 0x2, 0x5, 0x3, 0x3, 0x5, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x7, 0x9, 0x5, 0x7, 0x8, 0x9, 0x3, 0xb, 0x2, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x5, 0x7, 0x9, 0x7, 0x2, 0x9, 0x2, 0x0, 0x2, 0x7, 0xb, -0x1, -0x1, -0x1, -0x1], [0x2, 0x3, 0xb, 0x0, 0x1, 0x8, 0x1, 0x7, 0x8, 0x1, 0x5, 0x7, -0x1, -0x1, -0x1, -0x1], [0xb, 0x2, 0x1, 0xb, 0x1, 0x7, 0x7, 0x1, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x5, 0x8, 0x8, 0x5, 0x7, 0xa, 0x1, 0x3, 0xa, 0x3, 0xb, -0x1, -0x1, -0x1, -0x1], [0x5, 0x7, 0x0, 0x5, 0x0, 0x9, 0x7, 0xb, 0x0, 0x1, 0x0, 0xa, 0xb, 0xa, 0x0, -0x1], [0xb, 0xa, 0x0, 0xb, 0x0, 0x3, 0xa, 0x5, 0x0, 0x8, 0x0, 0x7, 0x5, 0x7, 0x0, -0x1], [0xb, 0xa, 0x5, 0x7, 0xb, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xa, 0x6, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x8, 0x3, 0x5, 0xa, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x0, 0x1, 0x5, 0xa, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x8, 0x3, 0x1, 0x9, 0x8, 0x5, 0xa, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x6, 0x5, 0x2, 0x6, 0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x6, 0x5, 0x1, 0x2, 0x6, 0x3, 0x0, 0x8, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x6, 0x5, 0x9, 0x0, 0x6, 0x0, 0x2, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x5, 0x9, 0x8, 0x5, 0x8, 0x2, 0x5, 0x2, 0x6, 0x3, 0x2, 0x8, -0x1, -0x1, -0x1, -0x1], [0x2, 0x3, 0xb, 0xa, 0x6, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xb, 0x0, 0x8, 0xb, 0x2, 0x0, 0xa, 0x6, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x1, 0x9, 0x2, 0x3, 0xb, 0x5, 0xa, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x5, 0xa, 0x6, 0x1, 0x9, 0x2, 0x9, 0xb, 0x2, 0x9, 0x8, 0xb, -0x1, -0x1, -0x1, -0x1], [0x6, 0x3, 0xb, 0x6, 0x5, 0x3, 0x5, 0x1, 0x3, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x8, 0xb, 0x0, 0xb, 0x5, 0x0, 0x5, 0x1, 0x5, 0xb, 0x6, -0x1, -0x1, -0x1, -0x1], [0x3, 0xb, 0x6, 0x0, 0x3, 0x6, 0x0, 0x6, 0x5, 0x0, 0x5, 0x9, -0x1, -0x1, -0x1, -0x1], [0x6, 0x5, 0x9, 0x6, 0x9, 0xb, 0xb, 0x9, 0x8, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x5, 0xa, 0x6, 0x4, 0x7, 0x8, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0x3, 0x0, 0x4, 0x7, 0x3, 0x6, 0x5, 0xa, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x9, 0x0, 0x5, 0xa, 0x6, 0x8, 0x4, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xa, 0x6, 0x5, 0x1, 0x9, 0x7, 0x1, 0x7, 0x3, 0x7, 0x9, 0x4, -0x1, -0x1, -0x1, -0x1], [0x6, 0x1, 0x2, 0x6, 0x5, 0x1, 0x4, 0x7, 0x8, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x2, 0x5, 0x5, 0x2, 0x6, 0x3, 0x0, 0x4, 0x3, 0x4, 0x7, -0x1, -0x1, -0x1, -0x1], [0x8, 0x4, 0x7, 0x9, 0x0, 0x5, 0x0, 0x6, 0x5, 0x0, 0x2, 0x6, -0x1, -0x1, -0x1, -0x1], [0x7, 0x3, 0x9, 0x7, 0x9, 0x4, 0x3, 0x2, 0x9, 0x5, 0x9, 0x6, 0x2, 0x6, 0x9, -0x1], [0x3, 0xb, 0x2, 0x7, 0x8, 0x4, 0xa, 0x6, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x5, 0xa, 0x6, 0x4, 0x7, 0x2, 0x4, 0x2, 0x0, 0x2, 0x7, 0xb, -0x1, -0x1, -0x1, -0x1], [0x0, 0x1, 0x9, 0x4, 0x7, 0x8, 0x2, 0x3, 0xb, 0x5, 0xa, 0x6, -0x1, -0x1, -0x1, -0x1], [0x9, 0x2, 0x1, 0x9, 0xb, 0x2, 0x9, 0x4, 0xb, 0x7, 0xb, 0x4, 0x5, 0xa, 0x6, -0x1], [0x8, 0x4, 0x7, 0x3, 0xb, 0x5, 0x3, 0x5, 0x1, 0x5, 0xb, 0x6, -0x1, -0x1, -0x1, -0x1], [0x5, 0x1, 0xb, 0x5, 0xb, 0x6, 0x1, 0x0, 0xb, 0x7, 0xb, 0x4, 0x0, 0x4, 0xb, -0x1], [0x0, 0x5, 0x9, 0x0, 0x6, 0x5, 0x0, 0x3, 0x6, 0xb, 0x6, 0x3, 0x8, 0x4, 0x7, -0x1], [0x6, 0x5, 0x9, 0x6, 0x9, 0xb, 0x4, 0x7, 0x9, 0x7, 0xb, 0x9, -0x1, -0x1, -0x1, -0x1], [0xa, 0x4, 0x9, 0x6, 0x4, 0xa, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0xa, 0x6, 0x4, 0x9, 0xa, 0x0, 0x8, 0x3, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xa, 0x0, 0x1, 0xa, 0x6, 0x0, 0x6, 0x4, 0x0, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x8, 0x3, 0x1, 0x8, 0x1, 0x6, 0x8, 0x6, 0x4, 0x6, 0x1, 0xa, -0x1, -0x1, -0x1, -0x1], [0x1, 0x4, 0x9, 0x1, 0x2, 0x4, 0x2, 0x6, 0x4, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0x0, 0x8, 0x1, 0x2, 0x9, 0x2, 0x4, 0x9, 0x2, 0x6, 0x4, -0x1, -0x1, -0x1, -0x1], [0x0, 0x2, 0x4, 0x4, 0x2, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x8, 0x3, 0x2, 0x8, 0x2, 0x4, 0x4, 0x2, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xa, 0x4, 0x9, 0xa, 0x6, 0x4, 0xb, 0x2, 0x3, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x8, 0x2, 0x2, 0x8, 0xb, 0x4, 0x9, 0xa, 0x4, 0xa, 0x6, -0x1, -0x1, -0x1, -0x1], [0x3, 0xb, 0x2, 0x0, 0x1, 0x6, 0x0, 0x6, 0x4, 0x6, 0x1, 0xa, -0x1, -0x1, -0x1, -0x1], [0x6, 0x4, 0x1, 0x6, 0x1, 0xa, 0x4, 0x8, 0x1, 0x2, 0x1, 0xb, 0x8, 0xb, 0x1, -0x1], [0x9, 0x6, 0x4, 0x9, 0x3, 0x6, 0x9, 0x1, 0x3, 0xb, 0x6, 0x3, -0x1, -0x1, -0x1, -0x1], [0x8, 0xb, 0x1, 0x8, 0x1, 0x0, 0xb, 0x6, 0x1, 0x9, 0x1, 0x4, 0x6, 0x4, 0x1, -0x1], [0x3, 0xb, 0x6, 0x3, 0x6, 0x0, 0x0, 0x6, 0x4, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x6, 0x4, 0x8, 0xb, 0x6, 0x8, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x7, 0xa, 0x6, 0x7, 0x8, 0xa, 0x8, 0x9, 0xa, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x7, 0x3, 0x0, 0xa, 0x7, 0x0, 0x9, 0xa, 0x6, 0x7, 0xa, -0x1, -0x1, -0x1, -0x1], [0xa, 0x6, 0x7, 0x1, 0xa, 0x7, 0x1, 0x7, 0x8, 0x1, 0x8, 0x0, -0x1, -0x1, -0x1, -0x1], [0xa, 0x6, 0x7, 0xa, 0x7, 0x1, 0x1, 0x7, 0x3, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x2, 0x6, 0x1, 0x6, 0x8, 0x1, 0x8, 0x9, 0x8, 0x6, 0x7, -0x1, -0x1, -0x1, -0x1], [0x2, 0x6, 0x9, 0x2, 0x9, 0x1, 0x6, 0x7, 0x9, 0x0, 0x9, 0x3, 0x7, 0x3, 0x9, -0x1], [0x7, 0x8, 0x0, 0x7, 0x0, 0x6, 0x6, 0x0, 0x2, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x7, 0x3, 0x2, 0x6, 0x7, 0x2, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x2, 0x3, 0xb, 0xa, 0x6, 0x8, 0xa, 0x8, 0x9, 0x8, 0x6, 0x7, -0x1, -0x1, -0x1, -0x1], [0x2, 0x0, 0x7, 0x2, 0x7, 0xb, 0x0, 0x9, 0x7, 0x6, 0x7, 0xa, 0x9, 0xa, 0x7, -0x1], [0x1, 0x8, 0x0, 0x1, 0x7, 0x8, 0x1, 0xa, 0x7, 0x6, 0x7, 0xa, 0x2, 0x3, 0xb, -0x1], [0xb, 0x2, 0x1, 0xb, 0x1, 0x7, 0xa, 0x6, 0x1, 0x6, 0x7, 0x1, -0x1, -0x1, -0x1, -0x1], [0x8, 0x9, 0x6, 0x8, 0x6, 0x7, 0x9, 0x1, 0x6, 0xb, 0x6, 0x3, 0x1, 0x3, 0x6, -0x1], [0x0, 0x9, 0x1, 0xb, 0x6, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x7, 0x8, 0x0, 0x7, 0x0, 0x6, 0x3, 0xb, 0x0, 0xb, 0x6, 0x0, -0x1, -0x1, -0x1, -0x1], [0x7, 0xb, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x7, 0x6, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0x0, 0x8, 0xb, 0x7, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x1, 0x9, 0xb, 0x7, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x8, 0x1, 0x9, 0x8, 0x3, 0x1, 0xb, 0x7, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xa, 0x1, 0x2, 0x6, 0xb, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x2, 0xa, 0x3, 0x0, 0x8, 0x6, 0xb, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x2, 0x9, 0x0, 0x2, 0xa, 0x9, 0x6, 0xb, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x6, 0xb, 0x7, 0x2, 0xa, 0x3, 0xa, 0x8, 0x3, 0xa, 0x9, 0x8, -0x1, -0x1, -0x1, -0x1], [0x7, 0x2, 0x3, 0x6, 0x2, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x7, 0x0, 0x8, 0x7, 0x6, 0x0, 0x6, 0x2, 0x0, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x2, 0x7, 0x6, 0x2, 0x3, 0x7, 0x0, 0x1, 0x9, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x6, 0x2, 0x1, 0x8, 0x6, 0x1, 0x9, 0x8, 0x8, 0x7, 0x6, -0x1, -0x1, -0x1, -0x1], [0xa, 0x7, 0x6, 0xa, 0x1, 0x7, 0x1, 0x3, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xa, 0x7, 0x6, 0x1, 0x7, 0xa, 0x1, 0x8, 0x7, 0x1, 0x0, 0x8, -0x1, -0x1, -0x1, -0x1], [0x0, 0x3, 0x7, 0x0, 0x7, 0xa, 0x0, 0xa, 0x9, 0x6, 0xa, 0x7, -0x1, -0x1, -0x1, -0x1], [0x7, 0x6, 0xa, 0x7, 0xa, 0x8, 0x8, 0xa, 0x9, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x6, 0x8, 0x4, 0xb, 0x8, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0x6, 0xb, 0x3, 0x0, 0x6, 0x0, 0x4, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x8, 0x6, 0xb, 0x8, 0x4, 0x6, 0x9, 0x0, 0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x4, 0x6, 0x9, 0x6, 0x3, 0x9, 0x3, 0x1, 0xb, 0x3, 0x6, -0x1, -0x1, -0x1, -0x1], [0x6, 0x8, 0x4, 0x6, 0xb, 0x8, 0x2, 0xa, 0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x2, 0xa, 0x3, 0x0, 0xb, 0x0, 0x6, 0xb, 0x0, 0x4, 0x6, -0x1, -0x1, -0x1, -0x1], [0x4, 0xb, 0x8, 0x4, 0x6, 0xb, 0x0, 0x2, 0x9, 0x2, 0xa, 0x9, -0x1, -0x1, -0x1, -0x1], [0xa, 0x9, 0x3, 0xa, 0x3, 0x2, 0x9, 0x4, 0x3, 0xb, 0x3, 0x6, 0x4, 0x6, 0x3, -0x1], [0x8, 0x2, 0x3, 0x8, 0x4, 0x2, 0x4, 0x6, 0x2, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x4, 0x2, 0x4, 0x6, 0x2, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x9, 0x0, 0x2, 0x3, 0x4, 0x2, 0x4, 0x6, 0x4, 0x3, 0x8, -0x1, -0x1, -0x1, -0x1], [0x1, 0x9, 0x4, 0x1, 0x4, 0x2, 0x2, 0x4, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x8, 0x1, 0x3, 0x8, 0x6, 0x1, 0x8, 0x4, 0x6, 0x6, 0xa, 0x1, -0x1, -0x1, -0x1, -0x1], [0xa, 0x1, 0x0, 0xa, 0x0, 0x6, 0x6, 0x0, 0x4, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0x6, 0x3, 0x4, 0x3, 0x8, 0x6, 0xa, 0x3, 0x0, 0x3, 0x9, 0xa, 0x9, 0x3, -0x1], [0xa, 0x9, 0x4, 0x6, 0xa, 0x4, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0x9, 0x5, 0x7, 0x6, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x8, 0x3, 0x4, 0x9, 0x5, 0xb, 0x7, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x5, 0x0, 0x1, 0x5, 0x4, 0x0, 0x7, 0x6, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xb, 0x7, 0x6, 0x8, 0x3, 0x4, 0x3, 0x5, 0x4, 0x3, 0x1, 0x5, -0x1, -0x1, -0x1, -0x1], [0x9, 0x5, 0x4, 0xa, 0x1, 0x2, 0x7, 0x6, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x6, 0xb, 0x7, 0x1, 0x2, 0xa, 0x0, 0x8, 0x3, 0x4, 0x9, 0x5, -0x1, -0x1, -0x1, -0x1], [0x7, 0x6, 0xb, 0x5, 0x4, 0xa, 0x4, 0x2, 0xa, 0x4, 0x0, 0x2, -0x1, -0x1, -0x1, -0x1], [0x3, 0x4, 0x8, 0x3, 0x5, 0x4, 0x3, 0x2, 0x5, 0xa, 0x5, 0x2, 0xb, 0x7, 0x6, -0x1], [0x7, 0x2, 0x3, 0x7, 0x6, 0x2, 0x5, 0x4, 0x9, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x5, 0x4, 0x0, 0x8, 0x6, 0x0, 0x6, 0x2, 0x6, 0x8, 0x7, -0x1, -0x1, -0x1, -0x1], [0x3, 0x6, 0x2, 0x3, 0x7, 0x6, 0x1, 0x5, 0x0, 0x5, 0x4, 0x0, -0x1, -0x1, -0x1, -0x1], [0x6, 0x2, 0x8, 0x6, 0x8, 0x7, 0x2, 0x1, 0x8, 0x4, 0x8, 0x5, 0x1, 0x5, 0x8, -0x1], [0x9, 0x5, 0x4, 0xa, 0x1, 0x6, 0x1, 0x7, 0x6, 0x1, 0x3, 0x7, -0x1, -0x1, -0x1, -0x1], [0x1, 0x6, 0xa, 0x1, 0x7, 0x6, 0x1, 0x0, 0x7, 0x8, 0x7, 0x0, 0x9, 0x5, 0x4, -0x1], [0x4, 0x0, 0xa, 0x4, 0xa, 0x5, 0x0, 0x3, 0xa, 0x6, 0xa, 0x7, 0x3, 0x7, 0xa, -0x1], [0x7, 0x6, 0xa, 0x7, 0xa, 0x8, 0x5, 0x4, 0xa, 0x4, 0x8, 0xa, -0x1, -0x1, -0x1, -0x1], [0x6, 0x9, 0x5, 0x6, 0xb, 0x9, 0xb, 0x8, 0x9, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0x6, 0xb, 0x0, 0x6, 0x3, 0x0, 0x5, 0x6, 0x0, 0x9, 0x5, -0x1, -0x1, -0x1, -0x1], [0x0, 0xb, 0x8, 0x0, 0x5, 0xb, 0x0, 0x1, 0x5, 0x5, 0x6, 0xb, -0x1, -0x1, -0x1, -0x1], [0x6, 0xb, 0x3, 0x6, 0x3, 0x5, 0x5, 0x3, 0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x2, 0xa, 0x9, 0x5, 0xb, 0x9, 0xb, 0x8, 0xb, 0x5, 0x6, -0x1, -0x1, -0x1, -0x1], [0x0, 0xb, 0x3, 0x0, 0x6, 0xb, 0x0, 0x9, 0x6, 0x5, 0x6, 0x9, 0x1, 0x2, 0xa, -0x1], [0xb, 0x8, 0x5, 0xb, 0x5, 0x6, 0x8, 0x0, 0x5, 0xa, 0x5, 0x2, 0x0, 0x2, 0x5, -0x1], [0x6, 0xb, 0x3, 0x6, 0x3, 0x5, 0x2, 0xa, 0x3, 0xa, 0x5, 0x3, -0x1, -0x1, -0x1, -0x1], [0x5, 0x8, 0x9, 0x5, 0x2, 0x8, 0x5, 0x6, 0x2, 0x3, 0x8, 0x2, -0x1, -0x1, -0x1, -0x1], [0x9, 0x5, 0x6, 0x9, 0x6, 0x0, 0x0, 0x6, 0x2, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x5, 0x8, 0x1, 0x8, 0x0, 0x5, 0x6, 0x8, 0x3, 0x8, 0x2, 0x6, 0x2, 0x8, -0x1], [0x1, 0x5, 0x6, 0x2, 0x1, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x3, 0x6, 0x1, 0x6, 0xa, 0x3, 0x8, 0x6, 0x5, 0x6, 0x9, 0x8, 0x9, 0x6, -0x1], [0xa, 0x1, 0x0, 0xa, 0x0, 0x6, 0x9, 0x5, 0x0, 0x5, 0x6, 0x0, -0x1, -0x1, -0x1, -0x1], [0x0, 0x3, 0x8, 0x5, 0x6, 0xa, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xa, 0x5, 0x6, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xb, 0x5, 0xa, 0x7, 0x5, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xb, 0x5, 0xa, 0xb, 0x7, 0x5, 0x8, 0x3, 0x0, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x5, 0xb, 0x7, 0x5, 0xa, 0xb, 0x1, 0x9, 0x0, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xa, 0x7, 0x5, 0xa, 0xb, 0x7, 0x9, 0x8, 0x1, 0x8, 0x3, 0x1, -0x1, -0x1, -0x1, -0x1], [0xb, 0x1, 0x2, 0xb, 0x7, 0x1, 0x7, 0x5, 0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x8, 0x3, 0x1, 0x2, 0x7, 0x1, 0x7, 0x5, 0x7, 0x2, 0xb, -0x1, -0x1, -0x1, -0x1], [0x9, 0x7, 0x5, 0x9, 0x2, 0x7, 0x9, 0x0, 0x2, 0x2, 0xb, 0x7, -0x1, -0x1, -0x1, -0x1], [0x7, 0x5, 0x2, 0x7, 0x2, 0xb, 0x5, 0x9, 0x2, 0x3, 0x2, 0x8, 0x9, 0x8, 0x2, -0x1], [0x2, 0x5, 0xa, 0x2, 0x3, 0x5, 0x3, 0x7, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x8, 0x2, 0x0, 0x8, 0x5, 0x2, 0x8, 0x7, 0x5, 0xa, 0x2, 0x5, -0x1, -0x1, -0x1, -0x1], [0x9, 0x0, 0x1, 0x5, 0xa, 0x3, 0x5, 0x3, 0x7, 0x3, 0xa, 0x2, -0x1, -0x1, -0x1, -0x1], [0x9, 0x8, 0x2, 0x9, 0x2, 0x1, 0x8, 0x7, 0x2, 0xa, 0x2, 0x5, 0x7, 0x5, 0x2, -0x1], [0x1, 0x3, 0x5, 0x3, 0x7, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x8, 0x7, 0x0, 0x7, 0x1, 0x1, 0x7, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x0, 0x3, 0x9, 0x3, 0x5, 0x5, 0x3, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0x8, 0x7, 0x5, 0x9, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x5, 0x8, 0x4, 0x5, 0xa, 0x8, 0xa, 0xb, 0x8, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x5, 0x0, 0x4, 0x5, 0xb, 0x0, 0x5, 0xa, 0xb, 0xb, 0x3, 0x0, -0x1, -0x1, -0x1, -0x1], [0x0, 0x1, 0x9, 0x8, 0x4, 0xa, 0x8, 0xa, 0xb, 0xa, 0x4, 0x5, -0x1, -0x1, -0x1, -0x1], [0xa, 0xb, 0x4, 0xa, 0x4, 0x5, 0xb, 0x3, 0x4, 0x9, 0x4, 0x1, 0x3, 0x1, 0x4, -0x1], [0x2, 0x5, 0x1, 0x2, 0x8, 0x5, 0x2, 0xb, 0x8, 0x4, 0x5, 0x8, -0x1, -0x1, -0x1, -0x1], [0x0, 0x4, 0xb, 0x0, 0xb, 0x3, 0x4, 0x5, 0xb, 0x2, 0xb, 0x1, 0x5, 0x1, 0xb, -0x1], [0x0, 0x2, 0x5, 0x0, 0x5, 0x9, 0x2, 0xb, 0x5, 0x4, 0x5, 0x8, 0xb, 0x8, 0x5, -0x1], [0x9, 0x4, 0x5, 0x2, 0xb, 0x3, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x2, 0x5, 0xa, 0x3, 0x5, 0x2, 0x3, 0x4, 0x5, 0x3, 0x8, 0x4, -0x1, -0x1, -0x1, -0x1], [0x5, 0xa, 0x2, 0x5, 0x2, 0x4, 0x4, 0x2, 0x0, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0xa, 0x2, 0x3, 0x5, 0xa, 0x3, 0x8, 0x5, 0x4, 0x5, 0x8, 0x0, 0x1, 0x9, -0x1], [0x5, 0xa, 0x2, 0x5, 0x2, 0x4, 0x1, 0x9, 0x2, 0x9, 0x4, 0x2, -0x1, -0x1, -0x1, -0x1], [0x8, 0x4, 0x5, 0x8, 0x5, 0x3, 0x3, 0x5, 0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x4, 0x5, 0x1, 0x0, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x8, 0x4, 0x5, 0x8, 0x5, 0x3, 0x9, 0x0, 0x5, 0x0, 0x3, 0x5, -0x1, -0x1, -0x1, -0x1], [0x9, 0x4, 0x5, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0xb, 0x7, 0x4, 0x9, 0xb, 0x9, 0xa, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x8, 0x3, 0x4, 0x9, 0x7, 0x9, 0xb, 0x7, 0x9, 0xa, 0xb, -0x1, -0x1, -0x1, -0x1], [0x1, 0xa, 0xb, 0x1, 0xb, 0x4, 0x1, 0x4, 0x0, 0x7, 0x4, 0xb, -0x1, -0x1, -0x1, -0x1], [0x3, 0x1, 0x4, 0x3, 0x4, 0x8, 0x1, 0xa, 0x4, 0x7, 0x4, 0xb, 0xa, 0xb, 0x4, -0x1], [0x4, 0xb, 0x7, 0x9, 0xb, 0x4, 0x9, 0x2, 0xb, 0x9, 0x1, 0x2, -0x1, -0x1, -0x1, -0x1], [0x9, 0x7, 0x4, 0x9, 0xb, 0x7, 0x9, 0x1, 0xb, 0x2, 0xb, 0x1, 0x0, 0x8, 0x3, -0x1], [0xb, 0x7, 0x4, 0xb, 0x4, 0x2, 0x2, 0x4, 0x0, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0xb, 0x7, 0x4, 0xb, 0x4, 0x2, 0x8, 0x3, 0x4, 0x3, 0x2, 0x4, -0x1, -0x1, -0x1, -0x1], [0x2, 0x9, 0xa, 0x2, 0x7, 0x9, 0x2, 0x3, 0x7, 0x7, 0x4, 0x9, -0x1, -0x1, -0x1, -0x1], [0x9, 0xa, 0x7, 0x9, 0x7, 0x4, 0xa, 0x2, 0x7, 0x8, 0x7, 0x0, 0x2, 0x0, 0x7, -0x1], [0x3, 0x7, 0xa, 0x3, 0xa, 0x2, 0x7, 0x4, 0xa, 0x1, 0xa, 0x0, 0x4, 0x0, 0xa, -0x1], [0x1, 0xa, 0x2, 0x8, 0x7, 0x4, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0x9, 0x1, 0x4, 0x1, 0x7, 0x7, 0x1, 0x3, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0x9, 0x1, 0x4, 0x1, 0x7, 0x0, 0x8, 0x1, 0x8, 0x7, 0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0x0, 0x3, 0x7, 0x4, 0x3, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x4, 0x8, 0x7, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0xa, 0x8, 0xa, 0xb, 0x8, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0x0, 0x9, 0x3, 0x9, 0xb, 0xb, 0x9, 0xa, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x1, 0xa, 0x0, 0xa, 0x8, 0x8, 0xa, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0x1, 0xa, 0xb, 0x3, 0xa, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x2, 0xb, 0x1, 0xb, 0x9, 0x9, 0xb, 0x8, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0x0, 0x9, 0x3, 0x9, 0xb, 0x1, 0x2, 0x9, 0x2, 0xb, 0x9, -0x1, -0x1, -0x1, -0x1], [0x0, 0x2, 0xb, 0x8, 0x0, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x3, 0x2, 0xb, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x2, 0x3, 0x8, 0x2, 0x8, 0xa, 0xa, 0x8, 0x9, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x9, 0xa, 0x2, 0x0, 0x9, 0x2, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x2, 0x3, 0x8, 0x2, 0x8, 0xa, 0x0, 0x1, 0x8, 0x1, 0xa, 0x8, -0x1, -0x1, -0x1, -0x1], [0x1, 0xa, 0x2, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x1, 0x3, 0x8, 0x9, 0x1, 0x8, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x9, 0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [0x0, 0x3, 0x8, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1], [-0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1, -0x1]]; const VULF = 0x1 << 0x0; const VULB = 0x1 << 0x1; const VLLB = 0x1 << 0x2; const VLLF = 0x1 << 0x3; const VURF = 0x1 << 0x4; const VURB = 0x1 << 0x5; const VLRB = 0x1 << 0x6; const VLRF = 0x1 << 0x7; const ptIdentifers = [VULF, VULB, VLLB, VLLF, VURF, VURB, VLRB, VLRF]; const vertexIdentifersOfEdge = [[0x0, 0x1], [0x1, 0x2], [0x2, 0x3], [0x3, 0x0], [0x4, 0x5], [0x5, 0x6], [0x6, 0x7], [0x7, 0x4], [0x0, 0x4], [0x1, 0x5], [0x2, 0x6], [0x3, 0x7]]; const postionToVertices = [[0x0, 0x1, 0x1], [0x0, 0x1, 0x0], [0x0, 0x0, 0x0], [0x0, 0x0, 0x1], [0x1, 0x1, 0x1], [0x1, 0x1, 0x0], [0x1, 0x0, 0x0], [0x1, 0x0, 0x1]]; let horizontalSize; let insideFunc; let anaValue; let scaleFirst; let scale; let offset; let undef; let outerScale; let outerOffset; let readData; function readDataNoOuter(g) { return scaleFirst ? g = g * scale + offset : g = (g + offset) * scale, g } function readDataWidthOuter(g) { scaleFirst ? g = (g * outerScale + outerOffset) * scale + offset : g = ((g + outerOffset) * outerScale + offset) * scale } function Cube(g, X, u, f, j) { const Q = E; this.z = u, this.y = X, this.x = g, this[Q(0x1be)] = new Array(0x8), this[Q(0x1a9)] = new Array(0x8), this[Q(0x1a6)] = 0x0; for (let I = 0x0; I < 0x8; I++) { const V = postionToVertices[I]; const e = [V[0x0] + g, V[0x1] + X, V[0x2] + u]; this[Q(0x1be)][I] = e; const D = e[0x2] * horizontalSize + e[0x1] * j + e[0x0]; let S = f[D]; isNaN(S) || S === undef ? this[Q(0x1a9)][I] = undef : (S = readData(S), insideFunc(S) && (this[Q(0x1a6)] |= ptIdentifers[I]), this[Q(0x1a9)][I] = S) } } function getPointPostionOnAxis(g, X, u) { if (g === X) { return g } return g + (X - g) * u } function getEdgeVertices(g, X) { const y = E; const u = vertexIdentifersOfEdge[X]; const f = g[y(0x1be)][u[0x0]]; const j = g[y(0x1be)][u[0x1]]; const I = g[y(0x1a9)][u[0x0]]; const V = g.values[u[0x1]]; if (Math[y(0x1ba)](I - anaValue) < 0.00001) { return f } if (Math.abs(V - anaValue) < 0.00001) { return j } if (Math[y(0x1ba)](I - V) < 0.00001) { return f } let e; if (I === undef) { return j } else { if (V === undef) { return f } else { e = parseFloat(((anaValue - I) / (V - I))[y(0x1c3)](0x6)) } } const D = getPointPostionOnAxis(f[0x0], j[0x0], e); const S = getPointPostionOnAxis(f[0x1], j[0x1], e); const k = getPointPostionOnAxis(f[0x2], j[0x2], e); return [D, S, k] } function createPointId(g) { const T = E; return g[0x0][T(0x1c3)](0x2) + "_" + g[0x1][T(0x1c3)](0x2) + "_" + g[0x2].toFixed(0x2) } function updatePointVertices(g, X, u) { const L = E; const f = createPointId(g); let j = u[f]; return j === undefined && (j = X[L(0x1bf)], u[f] = j, X[L(0x1ab)](g[0x0], g[0x1], g[0x2])), j } function updateFacesOfCube(g, X, u, f) { const p = E; const j = triTable[g[p(0x1a6)]]; if (j[0x0] === -0x1) { return undefined } for (let I = 0x0; I < j[p(0x1bf)]; I += 0x3) { const V = j[I]; if (V === -0x1) { break } const e = j[I + 0x1]; const D = j[I + 0x2]; const S = getEdgeVertices(g, V); const k = updatePointVertices(S, u, f); const F = getEdgeVertices(g, e); const r = updatePointVertices(F, u, f); const a = getEdgeVertices(g, D); const q = updatePointVertices(a, u, f); X[p(0x1ab)](k / 0x3, r / 0x3, q / 0x3) } } const getArr = function(g, X) { const x = E; switch (X) { case 0x1:return new Uint8Array(g); case 0x0:return new Int8Array(g); case 0x3:return new Uint16Array(g); case 0x2:return new Int16Array(g); case 0x5:return new Uint32Array(g); case 0x4:return new Int32Array(g); case 0x6:return new Float32Array(g); case 0x7:return new Float64Array(g); default:throw new Error(x(0x1ac) + this[x(0x1b7)]) } }; onmessage = g => { const w = E; const X = g.data; const u = getArr(X[w(0x1c2)], X.dataType); const f = X[w(0x1ae)]; const I = X.xSize; const V = X[w(0x1ad)]; horizontalSize = V * I, scale = X[w(0x1b6)] === undefined ? 0x1 : X[w(0x1b6)], offset = X[w(0x1bd)] === undefined ? 0x0 : X[w(0x1bd)], scaleFirst = X.scaleFirst === undefined ? !![] : X.scaleFirst, outerScale = X[w(0x1b2)] === undefined ? 0x1 : X[w(0x1b2)], outerOffset = X[w(0x1aa)] === undefined ? 0x0 : X[w(0x1aa)]; const e = X[w(0x1a4)]; e ? readData = readDataWidthOuter : readData = readDataNoOuter; undef = X.undef, anaValue = X.anaValue; X[w(0x1af)] === undefined && (X[w(0x1b3)] = ">", console.warn(w(0x1a8))); if (X[w(0x1af)] === ">") { insideFunc = function(Z) { return Z > anaValue } } else { if (X.anaType === ">=") { insideFunc = function(Z) { return Z >= anaValue } } else { if (X[w(0x1af)] === "<") { insideFunc = function(Z) { return Z < anaValue } } else { if (X[w(0x1af)] === "<=") { insideFunc = function(Z) { return Z <= anaValue } } else { throw new Error(w(0x1a7)) } } } } const D = []; const S = []; const F = {}; for (let r = 0x0; r < f; r++) { for (let a = 0x0; a < V; a++) { for (let q = 0x0; q < I; q++) { const N = new Cube(q, a, r, u, I); updateFacesOfCube(N, D, S, F) } } } const o = new Uint32Array(D); const C = new Float32Array(S); X[w(0x1b1)] ? postMessage({ indices: o[w(0x1bb)], vertices: C[w(0x1bb)], source: X.raw }, [o.buffer, C.buffer, X[w(0x1c2)]]) : postMessage({ indices: o[w(0x1bb)], vertices: C.buffer }, [o[w(0x1bb)], C[w(0x1bb)]]) }
