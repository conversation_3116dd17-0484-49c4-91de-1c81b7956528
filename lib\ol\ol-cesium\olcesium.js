var olcs_unused_var;(()=>{"use strict";var e={n:t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return e.d(i,{a:i}),i},d:(t,i)=>{for(var n in i)e.o(i,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:i[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{default:()=>xe});ol.geom.Point;const i=ol.proj,n={};function s(e,t,i){return e.on(t,i)}n.obj=function(e){return e},n.supportsImageRenderingPixelatedResult_=void 0,n.imageRenderingValueResult_=void 0,n.supportsImageRenderingPixelated=function(){if(void 0===n.supportsImageRenderingPixelatedResult_){const e=document.createElement("canvas");e.setAttribute("style","image-rendering: -moz-crisp-edges; image-rendering: pixelated;");const t=e.style.imageRendering;n.supportsImageRenderingPixelatedResult_=!!t,n.supportsImageRenderingPixelatedResult_&&(n.imageRenderingValueResult_=t)}return n.supportsImageRenderingPixelatedResult_},n.imageRenderingValue=function(){return n.supportsImageRenderingPixelated(),n.imageRenderingValueResult_||""},n.getSourceProjection=function(e){return e.get("olcs.projection")||e.getProjection()};let o=0;function r(e){return e.olcs_uid||(e.olcs_uid=++o)}function a(e){const t=Cesium.GroundPolylinePrimitive;return t&&t.isSupported(e)}const l=n,c=ol.easing,u=ol.layer.Tile;var h=e.n(u);const m=ol.layer.Image;var d=e.n(m);const g=ol.source.ImageStatic;var C=e.n(g);const p=ol.source.ImageWMS;var y=e.n(p);const f=ol.source.TileImage;var _=e.n(f);const v=ol.source.TileWMS;var w=e.n(v);const S=ol.source.VectorTile;var L=e.n(S);const P=ol.source.Image,T=ol.source,b=function(){const e=new T.Tile({projection:"EPSG:3857",wrapX:!0}).getTileCoordForTileUrlFunction([6,-31,22]);return e&&33===e[1]&&22===e[2]}();class R{constructor(e,t,i){this.source_=t,this.projection_=null,this.fallbackProj_=i||null,this.ready_=!1,this.tilingScheme_=null,this.rectangle_=null,this.map_=e,this.shouldRequestNextLevel=!1;const n=this.source_.get("olcs.proxy");n&&("function"==typeof n?this.proxy_={getURL:n}:"string"==typeof n&&(this.proxy_=new Cesium.DefaultProxy(n))),this.errorEvent_=new Cesium.Event,this.emptyCanvas_=document.createElement("canvas"),this.emptyCanvas_.width=1,this.emptyCanvas_.height=1,this.source_.on("change",(e=>{this.handleSourceChanged_()})),this.handleSourceChanged_()}handleSourceChanged_(e){if(!this.ready_&&"ready"==this.source_.getState()){this.projection_=l.getSourceProjection(this.source_)||this.fallbackProj_;const e={numberOfLevelZeroTilesX:1,numberOfLevelZeroTilesY:1};if(null!==this.source_.tileGrid&&this.source_.tileGrid.forEachTileCoord(this.projection_.getExtent(),0,(([t,i,n])=>{e.numberOfLevelZeroTilesX=i+1,e.numberOfLevelZeroTilesY=n+1})),this.projection_==(0,i.get)("EPSG:4326"))this.shouldRequestNextLevel=1===e.numberOfLevelZeroTilesX&&1===e.numberOfLevelZeroTilesY,this.tilingScheme_=new Cesium.GeographicTilingScheme(e);else{if(this.projection_!=(0,i.get)("EPSG:3857"))return;this.shouldRequestNextLevel=!1,this.tilingScheme_=new Cesium.WebMercatorTilingScheme(e)}this.rectangle_=this.tilingScheme_.rectangle,this.ready_=!0}}getTileCredits(e,t,i){const n=this.source_.getAttributions();if(!n)return[];const s=this.map_.getView().calculateExtent(this.map_.getSize()),o=this.map_.getView().getCenter();return Z(n,this.shouldRequestNextLevel?i+1:i,o,s)}requestImage(e,t,i){const n=this.source_.getTileUrlFunction();if(n&&this.projection_){const s=this.shouldRequestNextLevel?i+1:i;let o=t;b||(o=-t-1);let r=n.call(this.source_,[s,e,o],1,this.projection_);return this.proxy_&&(r=this.proxy_.getURL(r)),r?Cesium.ImageryProvider.loadImage(this,r):this.emptyCanvas_}return this.emptyCanvas_}}Object.defineProperties(R.prototype,{ready:{get:function(){return this.ready_}},rectangle:{get:function(){return this.rectangle_}},tileWidth:{get:function(){const e=this.source_.getTileGrid();return e?Array.isArray(e.getTileSize(0))?e.getTileSize(0)[0]:e.getTileSize(0):256}},tileHeight:{get:function(){const e=this.source_.getTileGrid();return e?Array.isArray(e.getTileSize(0))?e.getTileSize(0)[1]:e.getTileSize(0):256}},maximumLevel:{get:function(){const e=this.source_.getTileGrid();return e?e.getMaxZoom():18}},minimumLevel:{get:function(){return 0}},tilingScheme:{get:function(){return this.tilingScheme_}},tileDiscardPolicy:{get:function(){}},errorEvent:{get:function(){return this.errorEvent_}},proxy:{get:function(){return this.proxy_}},hasAlphaChannel:{get:function(){return!0}},pickFeatures:{get:function(){}}});const E=R,x=ol.format.MVT;var O=e.n(x);const G=ol.style.Style;var F=e.n(G);const A=ol.style.Stroke;var M=e.n(A);const I=ol.render,k=ol.util,V=ol.structs.LRUCache;var j=e.n(V);const N=ol.tilegrid,D=ol.tileurlfunction,z=new(O()),B=[new(F())({stroke:new(M())({color:"blue",width:2})})];class W{constructor(e){this.urls=e.urls,this.ready=!0,this.readyPromise=Promise.resolve(!0),this.tileWidth=256,this.tileHeight=256,this.maximumLevel=e.maximumLevel||20,this.minimumLevel=e.minimumLevel||0,this.tilingScheme=new Cesium.WebMercatorTilingScheme,this.rectangle=e.rectangle||this.tilingScheme.rectangle,this.errorEvent=new Cesium.Event,this.credit=e.credit,this.hasAlphaChannel=!0,this.styleFunction_=e.styleFunction||(()=>B),this.projection_=(0,i.get)("EPSG:3857"),this.emptyCanvas_=document.createElement("canvas"),this.emptyCanvas_.width=1,this.emptyCanvas_.height=1,this.tileRectangle_=new Cesium.Rectangle;const t=void 0!==e.cacheSize?e.cacheSize:50;this.tileCache=new(j())(t),this.featureCache=e.featureCache||new(j())(t);const n=(0,N.getForProjection)(this.projection_);this.tileFunction_=(0,D.createFromTemplates)(this.urls,n)}getTileCredits(){return[]}pickFeatures(){}getTileFeatures(e,t,i){const n=this.getCacheKey_(e,t,i);let s;if(this.featureCache.containsKey(n)&&(s=this.featureCache.get(n)),!s){const o=this.getUrl_(e,t,i);if(s=fetch(o).then((e=>e.ok?e:Promise.reject(e))).then((e=>e.arrayBuffer())).then((e=>this.readFeaturesFromBuffer(e))),this.featureCache.set(n,s),this.featureCache.getCount()>2*this.featureCache.highWaterMark)for(;this.featureCache.canExpireCache();)this.featureCache.pop()}return s}readFeaturesFromBuffer(e){let t;k.VERSION<="6.4.4"&&(t={extent:[0,0,4096,4096],dataProjection:z.dataProjection,featureProjection:z.dataProjection});const i=z.readFeatures(e,t),n=this.tileWidth/4096;return i.forEach((e=>{const t=e.getFlatCoordinates();let i=!1;for(let e=0;e<t.length;++e)t[e]*=n,i&&(t[e]=this.tileWidth-t[e]),k.VERSION<="6.4.4"&&(i=!i)})),i}getUrl_(e,t,i){return this.tileFunction_([e,t,i])}getCacheKey_(e,t,i){return`${e}_${t}_${i}`}requestImage(e,t,i,n){if(i<this.minimumLevel)return this.emptyCanvas_;try{const n=this.getCacheKey_(i,e,t);let s;if(this.tileCache.containsKey(n)&&(s=this.tileCache.get(n)),!s&&(s=this.getTileFeatures(i,e,t).then((n=>{this.tilingScheme.tileXYToNativeRectangle(e,t,i,this.tileRectangle_);const s=(this.tileRectangle_.east-this.tileRectangle_.west)/this.tileWidth;return this.rasterizeFeatures(n,this.styleFunction_,s)})),this.tileCache.set(n,s),this.tileCache.getCount()>2*this.tileCache.highWaterMark))for(;this.tileCache.canExpireCache();)this.tileCache.pop();return s}catch(e){this.raiseEvent("could not render pbf to tile",e)}}rasterizeFeatures(e,t,i){const n=document.createElement("canvas"),s=(0,I.toContext)(n.getContext("2d"),{size:[this.tileWidth,this.tileHeight]});return e.forEach((e=>{const n=t(e,i);n&&n.forEach((t=>{s.setStyle(t),s.drawGeometry(e)}))})),n}}const H=ol.layer.VectorTile;var K=e.n(H);const U=ol.extent,q={};function Z(e,t,i,n){if(!e)return[];let s=e({viewState:{zoom:t,center:i},extent:n});return Array.isArray(s)||(s=[s]),s.map((e=>new Cesium.Credit(e,!0)))}q.computePixelSizeAtCoordinate=function(e,t){const i=e.camera,n=e.canvas,s=i.frustum,o=Cesium.Cartesian3.magnitude(Cesium.Cartesian3.subtract(i.position,t,new Cesium.Cartesian3));return s.getPixelDimensions(n.clientWidth,n.clientHeight,o,e.pixelRatio,new Cesium.Cartesian2)},q.computeBoundingBoxAtTarget=function(e,t,i){const n=q.computePixelSizeAtCoordinate(e,t),s=Cesium.Transforms.eastNorthUpToFixedFrame(t),o=Cesium.Matrix4.multiplyByPoint(s,new Cesium.Cartesian3(-n.x*i,-n.y*i,0),new Cesium.Cartesian3),r=Cesium.Matrix4.multiplyByPoint(s,new Cesium.Cartesian3(n.x*i,n.y*i,0),new Cesium.Cartesian3);return Cesium.Ellipsoid.WGS84.cartesianArrayToCartographicArray([o,r])},q.applyHeightOffsetToGeometry=function(e,t){e.applyTransform(((e,i,n)=>{if(void 0!==n&&n>=3)for(let e=0;e<i.length;e+=n)i[e+2]=i[e+2]+t;return i}))},q.createMatrixAtCoordinates=function(e,t=0,i=Cesium.Cartesian3.ZERO,n=new Cesium.Cartesian3(1,1,1)){const s=q.ol4326CoordinateToCesiumCartesian(e),o=Cesium.Transforms.eastNorthUpToFixedFrame(s),r=Cesium.Quaternion.fromAxisAngle(Cesium.Cartesian3.UNIT_Z,-t),a=Cesium.Matrix4.fromTranslationQuaternionRotationScale(i,r,n);return Cesium.Matrix4.multiply(o,a,new Cesium.Matrix4)},q.rotateAroundAxis=function(e,t,i,n,s){const o=Cesium.Math.clamp,r=Cesium.defaultValue,a=s||{},l=r(a.duration,500),u=r(a.easing,c.linear),h=a.callback;let m=0;const d=new Cesium.Matrix4,g=Date.now(),C=function(){const s=Date.now(),r=u(o((s-g)/l,0,1));e.transform.clone(d);const a=(r-m)*t;m=r,e.lookAtTransform(n),e.rotate(i,a),e.lookAtTransform(d),r<1?window.requestAnimationFrame(C):h&&h()};window.requestAnimationFrame(C)},q.setHeadingUsingBottomCenter=function(e,t,i,n){const s=e.camera,o=q.computeAngleToZenith(e,i),r=s.right,a=Cesium.Quaternion.fromAxisAngle(r,o),l=Cesium.Matrix3.fromQuaternion(a),c=new Cesium.Cartesian3;Cesium.Cartesian3.subtract(s.position,i,c);const u=new Cesium.Cartesian3;Cesium.Matrix3.multiplyByVector(l,c,u),Cesium.Cartesian3.add(u,i,u);const h=Cesium.Matrix4.fromTranslation(u);(0,q.rotateAroundAxis)(s,t,u,h,n)},q.pickOnTerrainOrEllipsoid=function(e,t){const i=e.camera.getPickRay(t);return e.globe.pick(i,e)||e.camera.pickEllipsoid(t)},q.pickBottomPoint=function(e){const t=e.canvas,i=new Cesium.Cartesian2(t.clientWidth/2,t.clientHeight);return q.pickOnTerrainOrEllipsoid(e,i)},q.pickCenterPoint=function(e){const t=e.canvas,i=new Cesium.Cartesian2(t.clientWidth/2,t.clientHeight/2);return q.pickOnTerrainOrEllipsoid(e,i)},q.computeSignedTiltAngleOnGlobe=function(e){const t=e.camera,i=new Cesium.Ray(t.position,t.direction);let n=e.globe.pick(i,e);if(!n){const e=Cesium.Ellipsoid.WGS84,t=Cesium.IntersectionTests.rayEllipsoid(i,e);t&&(n=Cesium.Ray.getPoint(i,t.start))}if(!n)return;const s=new Cesium.Cartesian3;Cesium.Ellipsoid.WGS84.geocentricSurfaceNormal(n,s);const o=(0,q.signedAngleBetween)(t.direction,s,t.right)-Math.PI;return Cesium.Math.convertLongitudeRange(o)},q.bottomFovRay=function(e){const t=e.camera,i=t.frustum.fovy/2,n=t.direction,s=Cesium.Quaternion.fromAxisAngle(t.right,i),o=Cesium.Matrix3.fromQuaternion(s),r=new Cesium.Cartesian3;return Cesium.Matrix3.multiplyByVector(o,n,r),new Cesium.Ray(t.position,r)},q.signedAngleBetween=function(e,t,i){const n=new Cesium.Cartesian3,s=new Cesium.Cartesian3,o=new Cesium.Cartesian3;Cesium.Cartesian3.normalize(e,n),Cesium.Cartesian3.normalize(t,s),Cesium.Cartesian3.cross(n,s,o);const r=Cesium.Cartesian3.dot(n,s),a=Cesium.Cartesian3.magnitude(o),l=Cesium.Cartesian3.dot(i,o),c=Math.atan2(a,r);return l>=0?c:-c},q.computeAngleToZenith=function(e,t){const i=e.camera,n=i.frustum.fovy/2,s=q.bottomFovRay(e),o=Cesium.Cartesian3.clone(s.direction);Cesium.Cartesian3.negate(o,o);const r=new Cesium.Cartesian3;Cesium.Ellipsoid.WGS84.geocentricSurfaceNormal(t,r);const a=new Cesium.Cartesian3;Cesium.Cartesian3.negate(i.right,a);return q.signedAngleBetween(r,o,a)+n},q.extentToRectangle=function(e,t){if(e&&t){const n=(0,i.transformExtent)(e,t,"EPSG:4326");return Cesium.Rectangle.fromDegrees(n[0],n[1],n[2],n[3])}return null},q.sourceToImageryProvider=function(e,t,i,n){const s=t.get("olcs_skip");if(s)return null;let o=null;if(t instanceof y()&&t.getUrl()&&t.getImageLoadFunction()===P.defaultImageLoadFunction){const e={"olcs.proxy":t.get("olcs.proxy"),"olcs.extent":t.get("olcs.extent"),"olcs.projection":t.get("olcs.projection"),"olcs.imagesource":t};(t=new(w())({url:t.getUrl(),attributions:t.getAttributions(),projection:t.getProjection(),params:t.getParams()})).setProperties(e)}if(t instanceof _()){let n=l.getSourceProjection(t);if(n||(n=i),!q.isCesiumProjection(n))return null;o=new E(e,t,i)}else{if(!(t instanceof C())){if(t instanceof L()){let e=l.getSourceProjection(t);if(e||(e=i),!1===s){const i=e.getCode().split(":")[1],s=t.urls.map((e=>e.replace(i,"3857"))),r=n.getExtent(),a=q.extentToRectangle(r,e),l=t.get("olcs_minimumLevel"),c=t.getAttributions(),u=n.getStyleFunction();let h;if(r&&c){h=Z(c,0,(0,U.getCenter)(r),r)[0]}return o=new W({credit:h,rectangle:a,minimumLevel:l,styleFunction:u,urls:s}),o}return null}return null}{let e=l.getSourceProjection(t);if(e||(e=i),!q.isCesiumProjection(e))return null;o=new Cesium.SingleTileImageryProvider({url:t.getUrl(),rectangle:new Cesium.Rectangle.fromDegrees(t.getImageExtent()[0],t.getImageExtent()[1],t.getImageExtent()[2],t.getImageExtent()[3])})}}return o},q.tileLayerToImageryLayer=function(e,t,i){if(!(t instanceof h()||t instanceof d()||t instanceof K()))return null;const n=t.getSource();if(!n)return null;let s=n.get("olcs_provider");if(s||(s=this.sourceToImageryProvider(e,n,i,t)),!s)return null;const o={},r=t.get("olcs.extent")||t.getExtent();r&&(o.rectangle=q.extentToRectangle(r,i));return new Cesium.ImageryLayer(s,o)},q.updateCesiumLayerProperties=function(e,t){let i=1,n=!0;[e.layer].concat(e.parents).forEach((e=>{const t=e.getOpacity();void 0!==t&&(i*=t);const s=e.getVisible();void 0!==s&&(n&=s)})),t.alpha=i,t.show=n},q.ol4326CoordinateToCesiumCartesian=function(e){const t=e;return t.length>2?Cesium.Cartesian3.fromDegrees(t[0],t[1],t[2]):Cesium.Cartesian3.fromDegrees(t[0],t[1])},q.ol4326CoordinateArrayToCsCartesians=function(e){const t=q.ol4326CoordinateToCesiumCartesian,i=[];for(let n=0;n<e.length;++n)i.push(t(e[n]));return i},q.olGeometryCloneTo4326=function(e,t){const n=(0,i.get)("EPSG:4326"),s=(0,i.get)(t);if(s!==n){const t=e.getProperties();(e=e.clone()).transform(s,n),e.setProperties(t)}return e},q.convertColorToCesium=function(e){if(e=e||"black",Array.isArray(e))return new Cesium.Color(Cesium.Color.byteToFloat(e[0]),Cesium.Color.byteToFloat(e[1]),Cesium.Color.byteToFloat(e[2]),e[3]);if("string"==typeof e)return Cesium.Color.fromCssColorString(e);if(e instanceof CanvasPattern||e instanceof CanvasGradient){const t=document.createElement("canvas"),i=t.getContext("2d");return t.width=t.height=256,i.fillStyle=e,i.fillRect(0,0,t.width,t.height),new Cesium.ImageMaterialProperty({image:t})}},q.convertUrlToCesium=function(e){let t="";const i=/\{(\d|[a-z])-(\d|[a-z])\}/,n=i.exec(e);if(n){e=e.replace(i,"{s}");const s=n[1].charCodeAt(0),o=n[2].charCodeAt(0);let r;for(r=s;r<=o;++r)t+=String.fromCharCode(r)}return{url:e,subdomains:t}},q.resetToNorthZenith=function(e,t){return new Promise(((i,n)=>{const s=t.camera,o=q.pickBottomPoint(t);if(!o)return void n("Could not get bottom pivot");const r=e.getView().getRotation();if(void 0===r)return void n("The view is not initialized");const a=q.computeAngleToZenith(t,o);q.setHeadingUsingBottomCenter(t,r,o);const l=Cesium.Matrix4.fromTranslation(o),c=s.right,u={callback:()=>{const t=e.getView();q.normalizeView(t),i()}};q.rotateAroundAxis(s,-a,c,l,u)}))},q.rotateAroundBottomCenter=function(e,t){return new Promise(((i,n)=>{const s=e.camera,o=q.pickBottomPoint(e);if(!o)return void n("could not get bottom pivot");const r={callback:i},a=Cesium.Matrix4.fromTranslation(o),l=s.right;(0,q.rotateAroundAxis)(s,-t,l,a,r)}))},q.normalizeView=function(e,t=0){const i=e.getResolution();e.setRotation(t),e.constrainResolution?e.setResolution(e.constrainResolution(i)):e.setResolution(e.getConstrainedResolution(i))},q.isCesiumProjection=function(e){const t=e===(0,i.get)("EPSG:3857"),n=e===(0,i.get)("EPSG:4326");return t||n};const Y=q;const X=class{constructor(e){this.ol3d=e,this.scene_=e.getCesiumScene(),this.canvas_=this.scene_.canvas,this._boundNotifyRepaintRequired=this.notifyRepaintRequired.bind(this),this.repaintEventNames_=["mousemove","mousedown","mouseup","touchstart","touchend","touchmove","pointerdown","pointerup","pointermove","wheel"],this.enable()}enable(){this.scene_.requestRenderMode=!0,this.scene_.maximumRenderTimeChange=1e3;for(const e of this.repaintEventNames_)this.canvas_.addEventListener(e,this._boundNotifyRepaintRequired,!1);window.addEventListener("resize",this._boundNotifyRepaintRequired,!1),this.ol3d.getOlMap().getLayerGroup().on("change",this._boundNotifyRepaintRequired)}disable(){for(const e of this.repaintEventNames_)this.canvas_.removeEventListener(e,this._boundNotifyRepaintRequired,!1);window.removeEventListener("resize",this._boundNotifyRepaintRequired,!1),this.ol3d.getOlMap().getLayerGroup().un("change",this._boundNotifyRepaintRequired),this.scene_.requestRenderMode=!1}restartRenderLoop(){this.notifyRepaintRequired()}notifyRepaintRequired(){this.scene_.requestRender()}},Q=ol.Observable;var $=e.n(Q);function J(e){return 180*e/Math.PI}function ee(e){return e*Math.PI/180}class te{constructor(e,t){this.scene_=e,this.cam_=e.camera,this.map_=t,this.view_=null,this.viewListenKey_=null,this.toLonLat_=te.identityProjection,this.fromLonLat_=te.identityProjection,this.tilt_=0,this.distance_=0,this.lastCameraViewMatrix_=null,this.viewUpdateInProgress_=!1,this.map_.on("change:view",(e=>{this.setView_(this.map_.getView())})),this.setView_(this.map_.getView())}static identityProjection(e,t,i){const n=i||e.length;if(t)for(let i=0;i<n;++i)t[i]=e[i];return e}setView_(e){if(this.view_&&((0,Q.unByKey)(this.viewListenKey_),this.viewListenKey_=null),this.view_=e,e){const t=(0,i.getTransform)(e.getProjection(),"EPSG:4326"),n=(0,i.getTransform)("EPSG:4326",e.getProjection());this.toLonLat_=t,this.fromLonLat_=n,this.viewListenKey_=e.on("propertychange",(e=>this.handleViewEvent_(e))),this.readFromView()}else this.toLonLat_=te.identityProjection,this.fromLonLat_=te.identityProjection}handleViewEvent_(e){this.viewUpdateInProgress_||this.readFromView()}setHeading(e){this.view_&&this.view_.setRotation(e)}getHeading(){if(!this.view_)return;return this.view_.getRotation()||0}setTilt(e){this.tilt_=e,this.updateCamera_()}getTilt(){return this.tilt_}setDistance(e){this.distance_=e,this.updateCamera_(),this.updateView()}getDistance(){return this.distance_}setCenter(e){this.view_&&this.view_.setCenter(e)}getCenter(){if(this.view_)return this.view_.getCenter()}setPosition(e){if(!this.toLonLat_)return;const t=this.toLonLat_(e),i=new Cesium.Cartographic(ee(t[0]),ee(t[1]),this.getAltitude());this.cam_.setView({destination:Cesium.Ellipsoid.WGS84.cartographicToCartesian(i)}),this.updateView()}getPosition(){if(!this.fromLonLat_)return;const e=Cesium.Ellipsoid.WGS84.cartesianToCartographic(this.cam_.position);return this.fromLonLat_([J(e.longitude),J(e.latitude)])}setAltitude(e){const t=Cesium.Ellipsoid.WGS84.cartesianToCartographic(this.cam_.position);t.height=e,this.cam_.position=Cesium.Ellipsoid.WGS84.cartographicToCartesian(t),this.updateView()}getAltitude(){return Cesium.Ellipsoid.WGS84.cartesianToCartographic(this.cam_.position).height}updateCamera_(){if(!this.view_||!this.toLonLat_)return;const e=this.view_.getCenter();if(!e)return;const t=this.toLonLat_(e),i=new Cesium.Cartographic(ee(t[0]),ee(t[1]));if(this.scene_.globe){const e=this.scene_.globe.getHeight(i);i.height=e||0}const n=Cesium.Ellipsoid.WGS84.cartographicToCartesian(i),s={pitch:this.tilt_-Cesium.Math.PI_OVER_TWO,heading:-this.view_.getRotation(),roll:void 0};this.cam_.setView({destination:n,orientation:s}),this.cam_.moveBackward(this.distance_),this.checkCameraChange(!0)}readFromView(){if(!this.view_||!this.toLonLat_)return;const e=this.view_.getCenter();if(null==e)return;const t=this.toLonLat_(e),i=this.view_.getResolution();this.distance_=this.calcDistanceForResolution(i||0,ee(t[1])),this.updateCamera_()}updateView(){if(!this.view_||!this.fromLonLat_)return;this.viewUpdateInProgress_=!0;const e=Cesium.Ellipsoid.WGS84,t=this.scene_,i=Y.pickCenterPoint(t);let n=i;if(!n){const e=t.globe,i=this.cam_.positionCartographic.clone(),s=e.getHeight(i);i.height=s||0,n=Cesium.Ellipsoid.WGS84.cartographicToCartesian(i)}this.distance_=Cesium.Cartesian3.distance(n,this.cam_.position);const s=e.cartesianToCartographic(n);if(this.view_.setCenter(this.fromLonLat_([J(s.longitude),J(s.latitude)])),this.view_.setResolution(this.calcResolutionForDistance(this.distance_,s?s.latitude:0)),i){const t=this.cam_.position,n=new Cesium.Cartesian3;e.geocentricSurfaceNormal(i,n);const s=new Cesium.Cartesian3;Cesium.Cartesian3.subtract(t,i,s),Cesium.Cartesian3.normalize(s,s);const o=this.cam_.up,r=this.cam_.right,a=new Cesium.Cartesian3(-i.y,i.x,0),l=Cesium.Cartesian3.angleBetween(r,a),c=Cesium.Cartesian3.cross(i,o,new Cesium.Cartesian3).z;this.view_.setRotation(c<0?l:-l);const u=Math.acos(Cesium.Cartesian3.dot(n,s));this.tilt_=isNaN(u)?0:u}else this.view_.setRotation(this.cam_.heading),this.tilt_=-this.cam_.pitch+Math.PI/2;this.viewUpdateInProgress_=!1}checkCameraChange(e){const t=this.lastCameraViewMatrix_,i=this.cam_.viewMatrix;t&&Cesium.Matrix4.equalsEpsilon(t,i,1e-5)||(this.lastCameraViewMatrix_=i.clone(),!0!==e&&this.updateView())}calcDistanceForResolution(e,t){return function(e,t,i,n){const s=i.canvas,o=i.camera.frustum.fovy,r=n.getMetersPerUnit();return e*s.clientHeight*r*Math.cos(Math.abs(t))/2/Math.tan(o/2)}(e,t,this.scene_,this.view_.getProjection())}calcResolutionForDistance(e,t){return function(e,t,i,n){const s=i.canvas,o=i.camera.frustum.fovy,r=n.getMetersPerUnit();return 2*e*Math.tan(o/2)/r/Math.cos(Math.abs(t))/s.clientHeight}(e,t,this.scene_,this.view_.getProjection())}}const ie=te,ne=ol.layer.Group;var se=e.n(ne);const oe=class{constructor(e,t){this.map=e,this.view=e.getView(),this.scene=t,this.olLayers=e.getLayerGroup().getLayers(),this.mapLayerGroup=e.getLayerGroup(),this.layerMap={},this.olLayerListenKeys={},this.olGroupListenKeys_={}}synchronize(){this.destroyAll(),this.addLayers_(this.mapLayerGroup)}orderLayers(){}addLayers_(e){const t=[{layer:e,parents:[]}];for(;t.length>0;){const e=t.splice(0,1)[0],i=e.layer,n=r(i).toString();this.olLayerListenKeys[n]=[];let o=null;if(i instanceof se())this.listenForGroupChanges_(i),i!==this.mapLayerGroup&&(o=this.createSingleLayerCounterparts(e)),o||i.getLayers().forEach((n=>{if(n){const s={layer:n,parents:i===this.mapLayerGroup?[]:[e.layer].concat(e.parents)};t.push(s)}}));else if(o=this.createSingleLayerCounterparts(e),!o){const t=n,i=e,o=e=>{const n=this.createSingleLayerCounterparts(i);n&&(i.layer.un("change",o),this.addCesiumObjects_(n,t,i.layer),this.orderLayers())};this.olLayerListenKeys[n].push(s(i.layer,"change",o))}o&&this.addCesiumObjects_(o,n,i)}this.orderLayers()}addCesiumObjects_(e,t,i){this.layerMap[t]=e,this.olLayerListenKeys[t].push(s(i,"change:zIndex",(()=>this.orderLayers()))),e.forEach((e=>{this.addCesiumObject(e)}))}removeAndDestroySingleLayer_(e){const t=r(e).toString(),i=this.layerMap[t];return i&&(i.forEach((e=>{this.removeSingleCesiumObject(e,!1),this.destroyCesiumObject(e)})),this.olLayerListenKeys[t].forEach(Q.unByKey),delete this.olLayerListenKeys[t]),delete this.layerMap[t],!!i}unlistenSingleGroup_(e){if(e===this.mapLayerGroup)return;const t=r(e).toString();this.olGroupListenKeys_[t].forEach((e=>{(0,Q.unByKey)(e)})),delete this.olGroupListenKeys_[t],delete this.layerMap[t]}removeLayer_(e){if(e){const t=[e];for(;t.length>0;){const e=t.splice(0,1)[0],i=this.removeAndDestroySingleLayer_(e);e instanceof se()&&(this.unlistenSingleGroup_(e),i||e.getLayers().forEach((e=>{t.push(e)})))}}}listenForGroupChanges_(e){const t=r(e).toString(),i=[];this.olGroupListenKeys_[t]=i;let n=[];const s=function(){const t=e.getLayers();t&&(n=[t.on("add",(e=>{this.addLayers_(e.element)})),t.on("remove",(e=>{this.removeLayer_(e.element)}))],i.push(...n))}.bind(this);s(),i.push(e.on("change:layers",(e=>{n.forEach((e=>{const t=i.indexOf(e);t>=0&&i.splice(t,1),(0,Q.unByKey)(e)})),s()})))}destroyAll(){let e;for(e in this.removeAllCesiumObjects(!0),this.olGroupListenKeys_){this.olGroupListenKeys_[e].forEach(Q.unByKey)}for(e in this.olLayerListenKeys)this.olLayerListenKeys[e].forEach(Q.unByKey);this.olGroupListenKeys_={},this.olLayerListenKeys={},this.layerMap={}}addCesiumObject(e){}destroyCesiumObject(e){}removeSingleCesiumObject(e,t){}removeAllCesiumObjects(e){}createSingleLayerCounterparts(e){}};const re=class extends oe{constructor(e,t){super(e,t),this.cesiumLayers_=t.imageryLayers,this.ourLayers_=new Cesium.ImageryLayerCollection}addCesiumObject(e){this.cesiumLayers_.add(e),this.ourLayers_.add(e)}destroyCesiumObject(e){e.destroy()}removeSingleCesiumObject(e,t){this.cesiumLayers_.remove(e,t),this.ourLayers_.remove(e,!1)}removeAllCesiumObjects(e){for(let t=0;t<this.ourLayers_.length;++t)this.cesiumLayers_.remove(this.ourLayers_.get(t),e);this.ourLayers_.removeAll(!1)}convertLayerToCesiumImageries(e,t){const i=Y.tileLayerToImageryLayer(this.map,e,t);return i?[i]:null}createSingleLayerCounterparts(e){const t=e.layer,i=r(t).toString(),n=this.view.getProjection(),s=this.convertLayerToCesiumImageries(t,n);if(s){const n=[];if([e.layer].concat(e.parents).forEach((t=>{n.push(t.on(["change:opacity","change:visible"],(()=>{for(let t=0;t<s.length;++t)Y.updateCesiumLayerProperties(e,s[t])})))})),t.getStyleFunction){let e=t.getStyleFunction();n.push(t.on("change",(()=>{const i=t.getStyleFunction();if(e!==i){e=i;for(let e=0;e<s.length;++e){const t=s[e];t._imageryCache&&t.imageryProvider.cache_&&(t._imageryCache={},t.imageryProvider.cache_={},t.imageryProvider.styleFunction_=i)}this.scene.requestRender()}})))}for(let t=0;t<s.length;++t)Y.updateCesiumLayerProperties(e,s[t]);n.push(t.on("change:extent",(e=>{for(let e=0;e<s.length;++e)this.cesiumLayers_.remove(s[e],!0),this.ourLayers_.remove(s[e],!1);delete this.layerMap[r(t)],this.synchronize()}))),n.push(t.on("change",(e=>{for(let e=0;e<s.length;++e){const t=this.cesiumLayers_.indexOf(s[e]);t>=0&&(this.cesiumLayers_.remove(s[e],!1),this.cesiumLayers_.add(s[e],t))}}))),this.olLayerListenKeys[i].push(...n)}return Array.isArray(s)?s:null}orderLayers(){const e=[],t={},i=[this.mapLayerGroup];for(;i.length>0;){const n=i.splice(0,1)[0];if(e.push(n),t[r(n)]=n.getZIndex()||0,n instanceof se()){const e=n.getLayers();e&&i.unshift(...e.getArray())}}!function(e,t){const i=e.length,n=Array(e.length);for(let t=0;t<i;t++)n[t]={index:t,value:e[t]};n.sort(((e,i)=>t(e.value,i.value)||e.index-i.index));for(let t=0;t<e.length;t++)e[t]=n[t].value}(e,((e,i)=>t[r(e)]-t[r(i)])),e.forEach((e=>{const t=r(e).toString(),i=this.layerMap[t];i&&i.forEach((e=>{this.raiseToTop(e)}))}))}raiseToTop(e){this.cesiumLayers_.raiseToTop(e)}};ol.source.Vector;ol.layer.Layer;const ae=ol.source.Cluster;var le=e.n(ae);const ce=ol.layer.Vector;var ue=e.n(ce);const he=ol.geom.Geometry;var me=e.n(he);const de=ol.style.Icon;var ge=e.n(de);const Ce=ol.geom.Polygon,pe=ol.geom.SimpleGeometry;var ye=e.n(pe);const fe=class{constructor(e,t){const i=new Cesium.BillboardCollection({scene:t}),n=new Cesium.PrimitiveCollection;this.olListenKeys=[],this.rootCollection_=new Cesium.PrimitiveCollection,this.context={projection:e,billboards:i,featureToCesiumMap:{},primitives:n},this.rootCollection_.add(i),this.rootCollection_.add(n)}destroy(){this.olListenKeys.forEach(Q.unByKey),this.olListenKeys.length=0}getRootPrimitive(){return this.rootCollection_}};const _e=class{constructor(e){this.scene=e,this.boundOnRemoveOrClearFeatureListener_=this.onRemoveOrClearFeature_.bind(this),this.defaultBillboardEyeOffset_=new Cesium.Cartesian3(0,0,10)}onRemoveOrClearFeature_(e){const t=e.target,i=l.obj(t).olcs_cancellers;if(i){const n=e.feature;if(n){const e=r(n),t=i[e];t&&(t(),delete i[e])}else{for(const e in i)i.hasOwnProperty(e)&&i[e]();l.obj(t).olcs_cancellers={}}}}setReferenceForPicking(e,t,i){i.olLayer=e,i.olFeature=t}createColoredPrimitive(e,t,i,n,s,o){const r={flat:!0,renderState:{depthTest:{enabled:!0}}};void 0!==o&&(r.renderState||(r.renderState={}),r.renderState.lineWidth=o);const a=function(e,t){const i=new Cesium.GeometryInstance({geometry:e});return!t||t instanceof Cesium.ImageMaterialProperty||(i.attributes={color:Cesium.ColorGeometryInstanceAttribute.fromColor(t)}),i}(n,s);let l;if(this.getHeightReference(e,t,i)===Cesium.HeightReference.CLAMP_TO_GROUND){const e=a.geometry.constructor;if(e&&!e.createShadowVolume)return null;l=new Cesium.GroundPrimitive({geometryInstances:a})}else l=new Cesium.Primitive({geometryInstances:a});if(s instanceof Cesium.ImageMaterialProperty){const e=s.image.getValue().toDataURL();l.appearance=new Cesium.MaterialAppearance({flat:!0,renderState:{depthTest:{enabled:!0}},material:new Cesium.Material({fabric:{type:"Image",uniforms:{image:e}}})})}else l.appearance=new Cesium.PerInstanceColorAppearance(r);return this.setReferenceForPicking(e,t,l),l}extractColorFromOlStyle(e,t){const i=e.getFill()?e.getFill().getColor():null,n=e.getStroke()?e.getStroke().getColor():null;let s="black";return n&&t?s=n:i&&(s=i),Y.convertColorToCesium(s)}extractLineWidthFromOlStyle(e){const t=e.getStroke()?e.getStroke().getWidth():void 0;return void 0!==t?t:1}wrapFillAndOutlineGeometries(e,t,i,n,s,o){const r=this.extractColorFromOlStyle(o,!1),a=this.extractColorFromOlStyle(o,!0),l=new Cesium.PrimitiveCollection;if(o.getFill()){const s=this.createColoredPrimitive(e,t,i,n,r);l.add(s)}if(o.getStroke()&&s){const n=this.extractLineWidthFromOlStyle(o),r=this.createColoredPrimitive(e,t,i,s,a,n);r&&l.add(r)}return l}addTextStyle(e,t,i,n,s){let o;if(s instanceof Cesium.PrimitiveCollection?o=s:(o=new Cesium.PrimitiveCollection,o.add(s)),!n.getText())return o;const r=n.getText(),a=this.olGeometry4326TextPartToCesium(e,t,i,r);return a&&o.add(a),o}csAddBillboard(e,t,i,n,s,o){t.eyeOffset||(t.eyeOffset=this.defaultBillboardEyeOffset_);const r=e.add(t);return this.setReferenceForPicking(i,n,r),r}olCircleGeometryToCesium(e,t,i,n,s){let o=(i=Y.olGeometryCloneTo4326(i,n)).getCenter();const r=3==o.length?o[2]:0;let l=o.slice();l[0]+=i.getRadius(),o=Y.ol4326CoordinateToCesiumCartesian(o),l=Y.ol4326CoordinateToCesiumCartesian(l);const c=Cesium.Cartesian3.distance(o,l),u=new Cesium.CircleGeometry({center:o,radius:c,height:r});let h,m;if(this.getHeightReference(e,t,i)===Cesium.HeightReference.CLAMP_TO_GROUND){const n=this.extractLineWidthFromOlStyle(s);if(n){const o=(0,Ce.circular)(i.getCenter(),c),r=Y.ol4326CoordinateArrayToCsCartesians(o.getLinearRing(0).getCoordinates());if(a(this.scene))h=new Cesium.GroundPolylinePrimitive({geometryInstances:new Cesium.GeometryInstance({geometry:new Cesium.GroundPolylineGeometry({positions:r,width:n})}),appearance:new Cesium.PolylineMaterialAppearance({material:this.olStyleToCesium(t,s,!0)}),classificationType:Cesium.ClassificationType.TERRAIN}),h.readyPromise.then((()=>{this.setReferenceForPicking(e,t,h._primitive)}));else{const i=this.extractColorFromOlStyle(s,!0);h=this.createStackedGroundCorridors(e,t,n,i,r)}}}else m=new Cesium.CircleOutlineGeometry({center:o,radius:c,extrudedHeight:r,height:r});const d=this.wrapFillAndOutlineGeometries(e,t,i,u,m,s);return h&&d.add(h),this.addTextStyle(e,t,i,s,d)}createStackedGroundCorridors(e,t,i,n,s){Array.isArray(s[0])||(s=[s]),i=Math.max(3,i);const o=[];let r=0;for(const e of[1e3,4e3,16e3,64e3,254e3,1e6,1e7]){const t={width:i*=2.14,vertexFormat:Cesium.VertexFormat.POSITION_ONLY};for(const i of s)t.positions=i,o.push(new Cesium.GeometryInstance({geometry:new Cesium.CorridorGeometry(t),attributes:{color:Cesium.ColorGeometryInstanceAttribute.fromColor(n),distanceDisplayCondition:new Cesium.DistanceDisplayConditionGeometryInstanceAttribute(r,e-1)}}));r=e}return new Cesium.GroundPrimitive({geometryInstances:o})}olLineStringGeometryToCesium(e,t,i,n,s){i=Y.olGeometryCloneTo4326(i,n);const o=Y.ol4326CoordinateArrayToCsCartesians(i.getCoordinates()),r=this.extractLineWidthFromOlStyle(s);let l;const c=this.getHeightReference(e,t,i);if(c!==Cesium.HeightReference.CLAMP_TO_GROUND||a(this.scene)){const i=new Cesium.PolylineMaterialAppearance({material:this.olStyleToCesium(t,s,!0)}),n={positions:o,width:r},a={appearance:i};if(c===Cesium.HeightReference.CLAMP_TO_GROUND){const i=new Cesium.GroundPolylineGeometry(n);a.geometryInstances=new Cesium.GeometryInstance({geometry:i}),l=new Cesium.GroundPolylinePrimitive(a),l.readyPromise.then((()=>{this.setReferenceForPicking(e,t,l._primitive)}))}else{n.vertexFormat=i.vertexFormat;const e=new Cesium.PolylineGeometry(n);a.geometryInstances=new Cesium.GeometryInstance({geometry:e}),l=new Cesium.Primitive(a)}}else{const i=this.extractColorFromOlStyle(s,!0);l=this.createStackedGroundCorridors(e,t,r,i,o)}return this.setReferenceForPicking(e,t,l),this.addTextStyle(e,t,i,s,l)}olPolygonGeometryToCesium(e,t,i,n,s){i=Y.olGeometryCloneTo4326(i,n);const o=this.getHeightReference(e,t,i);let r,l,c;if(5==i.getCoordinates()[0].length&&"rectangle"===t.getGeometry().get("olcs.polygon_kind")){const e=i.getCoordinates()[0],t=(0,U.boundingExtent)(e),n=Cesium.Rectangle.fromDegrees(t[0],t[1],t[2],t[3]);let s=0;if(3==e[0].length)for(let t=0;t<e.length;t++)s=Math.max(s,e[t][2]);r=new Cesium.RectangleGeometry({ellipsoid:Cesium.Ellipsoid.WGS84,rectangle:n,height:s}),l=new Cesium.RectangleOutlineGeometry({ellipsoid:Cesium.Ellipsoid.WGS84,rectangle:n,height:s})}else{const n=i.getLinearRings(),u={},h=u;for(let e=0;e<n.length;++e){const t=n[e].getCoordinates(),i=Y.ol4326CoordinateArrayToCsCartesians(t);0==e?u.positions=i:(u.holes||(u.holes=[]),u.holes.push({positions:i}))}if(r=new Cesium.PolygonGeometry({polygonHierarchy:h,perPositionHeight:!0}),o===Cesium.HeightReference.CLAMP_TO_GROUND){const i=this.extractLineWidthFromOlStyle(s);if(i>0){const n=[u.positions];if(u.holes)for(let e=0;e<u.holes.length;++e)n.push(u.holes[e].positions);if(a(this.scene)){const o=new Cesium.PolylineMaterialAppearance({material:this.olStyleToCesium(t,s,!0)}),r=[];for(const e of n){const t=new Cesium.GroundPolylineGeometry({positions:e,width:i});r.push(new Cesium.GeometryInstance({geometry:t}))}const a={appearance:o,geometryInstances:r};c=new Cesium.GroundPolylinePrimitive(a),c.readyPromise.then((()=>{this.setReferenceForPicking(e,t,c._primitive)}))}else{const o=this.extractColorFromOlStyle(s,!0);c=this.createStackedGroundCorridors(e,t,i,o,n)}}}else l=new Cesium.PolygonOutlineGeometry({polygonHierarchy:u,perPositionHeight:!0})}const u=this.wrapFillAndOutlineGeometries(e,t,i,r,l,s);return c&&u.add(c),this.addTextStyle(e,t,i,s,u)}getHeightReference(e,t,i){let n=i.get("altitudeMode");void 0===n&&(n=t.get("altitudeMode")),void 0===n&&(n=e.get("altitudeMode"));let s=Cesium.HeightReference.NONE;return"clampToGround"===n?s=Cesium.HeightReference.CLAMP_TO_GROUND:"relativeToGround"===n&&(s=Cesium.HeightReference.RELATIVE_TO_GROUND),s}createBillboardFromImage(e,t,i,n,s,o,a,c){o instanceof ge()&&o.load();const u=o.getImage(1),h=function(){if(!u)return;if(!(u instanceof HTMLCanvasElement||u instanceof Image||u instanceof HTMLImageElement))return;const n=i.getCoordinates(),r=Y.ol4326CoordinateToCesiumCartesian(n);let l;const h=o.getOpacity();void 0!==h&&(l=new Cesium.Color(1,1,1,h));const m=o.getScale(),d=this.getHeightReference(e,t,i),g={image:u,color:l,scale:m,heightReference:d,position:r};if(Object.assign(g,t.get("cesiumOptions")),o instanceof ge()){const e=o.getAnchor();e&&(g.pixelOffset=new Cesium.Cartesian2((u.width/2-e[0])*m,(u.height/2-e[1])*m))}const C=this.csAddBillboard(a,g,e,t,i,s);c&&c(C)}.bind(this);if(u instanceof Image&&!function(e){return""!=e.src&&0!=e.naturalHeight&&0!=e.naturalWidth&&e.complete}(u)){let i=!1;const n=e.getSource(),s=function(){i=!0};n.on(["removefeature","clear"],this.boundOnRemoveOrClearFeatureListener_);let o=l.obj(n).olcs_cancellers;o||(o=l.obj(n).olcs_cancellers={});const c=r(t);o[c]&&o[c](),o[c]=s;const m=function(){u.removeEventListener("load",m),a.isDestroyed()||i||h()};u.addEventListener("load",m)}else h()}olPointGeometryToCesium(e,t,i,n,s,o,r){i=Y.olGeometryCloneTo4326(i,n);let a=null;const l=s.getImage();if(l){const c=i.get("olcs_model")||t.get("olcs_model");if(c){const e=c(),t=Object.assign({},{scene:this.scene},e.cesiumOptions),i=Cesium.Model.fromGltf(t);a=new Cesium.PrimitiveCollection,a.add(i),e.debugModelMatrix&&a.add(new Cesium.DebugModelMatrixPrimitive({modelMatrix:e.debugModelMatrix}))}else this.createBillboardFromImage(e,t,i,n,s,l,o,r)}return s.getText()?this.addTextStyle(e,t,i,s,a||new Cesium.Primitive):a}olMultiGeometryToCesium(e,t,i,n,s,o,r){const a=function(i,o){const r=new Cesium.PrimitiveCollection;return i.forEach((i=>{r.add(o(e,t,i,n,s))})),r};let l;switch(i.getType()){case"MultiPoint":if(l=(i=i).getPoints(),s.getText()){const i=new Cesium.PrimitiveCollection;return l.forEach((a=>{const l=this.olPointGeometryToCesium(e,t,a,n,s,o,r);l&&i.add(l)})),i}return l.forEach((i=>{this.olPointGeometryToCesium(e,t,i,n,s,o,r)})),null;case"MultiLineString":return l=(i=i).getLineStrings(),a(l,this.olLineStringGeometryToCesium.bind(this));case"MultiPolygon":return l=(i=i).getPolygons(),a(l,this.olPolygonGeometryToCesium.bind(this))}}olGeometry4326TextPartToCesium(e,t,i,n){const s=n.getText();if(!s)return null;const o=new Cesium.LabelCollection({scene:this.scene}),r=(0,U.getCenter)(i.getExtent());if(i instanceof ye()){const e=i.getFirstCoordinate();r[2]=3==e.length?e[2]:0}const a={};a.position=Y.ol4326CoordinateToCesiumCartesian(r),a.text=s,a.heightReference=this.getHeightReference(e,t,i);const l=n.getOffsetX(),c=n.getOffsetY();if(0!=l&&0!=c){const e=new Cesium.Cartesian2(l,c);a.pixelOffset=e}let u,h;switch(a.font=n.getFont()||"10px sans-serif",n.getFill()&&(a.fillColor=this.extractColorFromOlStyle(n,!1),u=Cesium.LabelStyle.FILL),n.getStroke()&&(a.outlineWidth=this.extractLineWidthFromOlStyle(n),a.outlineColor=this.extractColorFromOlStyle(n,!0),u=Cesium.LabelStyle.OUTLINE),n.getFill()&&n.getStroke()&&(u=Cesium.LabelStyle.FILL_AND_OUTLINE),a.style=u,n.getTextAlign()){case"left":h=Cesium.HorizontalOrigin.LEFT;break;case"right":h=Cesium.HorizontalOrigin.RIGHT;break;default:h=Cesium.HorizontalOrigin.CENTER}if(a.horizontalOrigin=h,n.getTextBaseline()){let e;switch(n.getTextBaseline()){case"top":case"alphabetic":e=Cesium.VerticalOrigin.TOP;break;case"middle":e=Cesium.VerticalOrigin.CENTER;break;case"bottom":case"hanging":e=Cesium.VerticalOrigin.BOTTOM}a.verticalOrigin=e}const m=o.add(a);return this.setReferenceForPicking(e,t,m),o}olStyleToCesium(e,t,i){const n=t.getFill(),s=t.getStroke();if(i&&!s||!i&&!n)return null;let o=i?s.getColor():n.getColor();return o=Y.convertColorToCesium(o),i&&s.getLineDash()?Cesium.Material.fromType("Stripe",{horizontal:!1,repeat:500,evenColor:o,oddColor:new Cesium.Color(0,0,0,0)}):Cesium.Material.fromType("Color",{color:o})}computePlainStyle(e,t,i,n){const s=t.getStyleFunction();let o=null;return s&&(o=s(t,n)),!o&&i&&(o=i(t,n)),o?Array.isArray(o)?o:[o]:null}getGeometryFromFeature(e,t,i){if(i)return i;const n=e.get("olcs.3d_geometry");if(n&&n instanceof me())return n;if(t){const i=t.getGeometryFunction()(e);if(i instanceof me())return i}return e.getGeometry()}olFeatureToCesium(e,t,i,n,s){let o=this.getGeometryFromFeature(t,i,s);if(!o)return null;const a=n.projection,l=function(e){const i=n.featureToCesiumMap[r(t)];i instanceof Array?i.push(e):n.featureToCesiumMap[r(t)]=[e]};switch(o.getType()){case"GeometryCollection":const s=new Cesium.PrimitiveCollection;return o.getGeometries().forEach((o=>{if(o){const r=this.olFeatureToCesium(e,t,i,n,o);r&&s.add(r)}})),s;case"Point":o=o;const r=n.billboards,c=this.olPointGeometryToCesium(e,t,o,a,i,r,l);return c||null;case"Circle":return o=o,this.olCircleGeometryToCesium(e,t,o,a,i);case"LineString":return o=o,this.olLineStringGeometryToCesium(e,t,o,a,i);case"Polygon":return o=o,this.olPolygonGeometryToCesium(e,t,o,a,i);case"MultiPoint":case"MultiLineString":case"MultiPolygon":const u=this.olMultiGeometryToCesium(e,t,o,a,i,n.billboards,l);return u||null;case"LinearRing":throw new Error("LinearRing should only be part of polygon.");default:throw new Error(`Ol geom type not handled : ${o.getType()}`)}}olVectorLayerToCesium(e,t,i){const n=t.getProjection(),s=t.getResolution();if(void 0===s||!n)throw new Error("View not ready");let o=e.getSource();o instanceof le()&&(o=o.getSource());const a=o.getFeatures(),l=new fe(n,this.scene),c=l.context;for(let t=0;t<a.length;++t){const n=a[t];if(!n)continue;const o=e.getStyleFunction(),u=this.computePlainStyle(e,n,o,s);if(!u||!u.length)continue;let h=null;for(let t=0;t<u.length;t++){const i=this.olFeatureToCesium(e,n,u[t],c);if(i)if(h){if(i){let e,t=0;for(;e=i.get(t);)h.add(e),t++}}else h=i}h&&(i[r(n)]=h,l.getRootPrimitive().add(h))}return l}convert(e,t,i,n){const s=t.getProjection(),o=t.getResolution();if(null==o||!s)return null;const r=e.getStyleFunction(),a=this.computePlainStyle(e,i,r,o);if(!a||!a.length)return null;n.projection=s;let l=null;for(let t=0;t<a.length;t++){const s=this.olFeatureToCesium(e,i,a[t],n);if(l){if(s){let e,t=0;for(;e=s.get(t);)l.add(e),t++}}else l=s}return l}};const ve=class extends oe{constructor(e,t,i){super(e,t),this.converter=i||new _e(t),this.csAllPrimitives_=new Cesium.PrimitiveCollection,t.primitives.add(this.csAllPrimitives_),this.csAllPrimitives_.destroyPrimitives=!1}addCesiumObject(e){e.getRootPrimitive().counterpart=e,this.csAllPrimitives_.add(e.getRootPrimitive())}destroyCesiumObject(e){e.getRootPrimitive().destroy()}removeSingleCesiumObject(e,t){e.destroy(),this.csAllPrimitives_.destroyPrimitives=t,this.csAllPrimitives_.remove(e.getRootPrimitive()),this.csAllPrimitives_.destroyPrimitives=!1}removeAllCesiumObjects(e){if(this.csAllPrimitives_.destroyPrimitives=e,e)for(let e=0;e<this.csAllPrimitives_.length;++e)this.csAllPrimitives_.get(e).counterpart.destroy();this.csAllPrimitives_.removeAll(),this.csAllPrimitives_.destroyPrimitives=!1}updateLayerVisibility(e,t){let i=!0;[e.layer].concat(e.parents).forEach((e=>{const t=e.getVisible();void 0!==t?i&=t:i=!1})),t.show=i}createSingleLayerCounterparts(e){const t=e.layer;if(!(t instanceof ue())||t instanceof K())return null;let i=t.getSource();if(i instanceof le()&&(i=i.getSource()),!i)return null;const n=this.view,o={},a=this.converter.olVectorLayerToCesium(t,n,o),l=a.getRootPrimitive(),c=a.olListenKeys;[e.layer].concat(e.parents).forEach((t=>{c.push(s(t,"change:visible",(()=>{this.updateLayerVisibility(e,l)})))})),this.updateLayerVisibility(e,l);const u=function(e){const i=a.context,s=this.converter.convert(t,n,e,i);s&&(o[r(e)]=s,l.add(s))}.bind(this),h=function(e){const t=r(e),i=a.context,n=i.featureToCesiumMap[t];n&&(delete i.featureToCesiumMap[t],n.forEach((e=>{e instanceof Cesium.Billboard&&i.billboards.remove(e)})));const s=o[t];delete o[t],s&&l.remove(s)}.bind(this);return c.push(s(i,"addfeature",(e=>{u(e.feature)}))),c.push(s(i,"removefeature",(e=>{h(e.feature)}))),c.push(s(i,"changefeature",(e=>{const t=e.feature;h(t),u(t)}))),a?[a]:null}},we=ol.Overlay;var Se=e.n(we);class Le extends(Se()){constructor(e){const t=e.parent;super(t.getOptions()),this.scenePostRenderListenerRemover_=null,this.scene_=e.scene,this.synchronizer_=e.synchronizer,this.parent_=t,this.positionWGS84_=void 0,this.observer_=new MutationObserver(this.handleElementChanged.bind(this)),this.attributeObserver_=[],this.listenerKeys_=[];const i=e=>this.setPropertyFromEvent_(e);this.listenerKeys_.push(this.parent_.on("change:position",i)),this.listenerKeys_.push(this.parent_.on("change:element",i)),this.listenerKeys_.push(this.parent_.on("change:offset",i)),this.listenerKeys_.push(this.parent_.on("change:position",i)),this.listenerKeys_.push(this.parent_.on("change:positioning",i)),this.setProperties(this.parent_.getProperties()),this.handleMapChanged(),this.handleElementChanged()}observeTarget_(e){if(this.observer_){this.observer_.disconnect(),this.observer_.observe(e,{attributes:!1,childList:!0,characterData:!0,subtree:!0}),this.attributeObserver_.forEach((e=>{e.disconnect()})),this.attributeObserver_.length=0;for(let t=0;t<e.childNodes.length;t++){const i=e.childNodes[t];if(1===i.nodeType){const e=new MutationObserver(this.handleElementChanged.bind(this));e.observe(i,{attributes:!0,subtree:!0}),this.attributeObserver_.push(e)}}}}setPropertyFromEvent_(e){e.target&&e.key&&this.set(e.key,e.target.get(e.key))}getScene(){return this.scene_}handleMapChanged(){var e;this.scenePostRenderListenerRemover_&&(this.scenePostRenderListenerRemover_(),(e=this.element)&&e.parentNode&&e.parentNode.removeChild(e)),this.scenePostRenderListenerRemover_=null;const t=this.getScene();if(t){this.scenePostRenderListenerRemover_=t.postRender.addEventListener(this.updatePixelPosition.bind(this)),this.updatePixelPosition();const e=this.stopEvent?this.synchronizer_.getOverlayContainerStopEvent():this.synchronizer_.getOverlayContainer();this.insertFirst?e.insertBefore(this.element,e.childNodes[0]||null):e.appendChild(this.element)}}handlePositionChanged(){const e=this.getPosition();if(e){const t=this.parent_.getMap().getView().getProjection();this.positionWGS84_=(0,i.transform)(e,t,"EPSG:4326")}else this.positionWGS84_=void 0;this.updatePixelPosition()}handleElementChanged(){function e(t,i){const n=t.cloneNode();if("CANVAS"===t.nodeName){n.getContext("2d").drawImage(t,0,0)}i&&i.appendChild(n),t.nodeType!=Node.TEXT_NODE&&n.addEventListener("click",(e=>{t.dispatchEvent(new MouseEvent("click",e)),e.stopPropagation()}));const s=t.childNodes;for(let t=0;t<s.length;t++)s[t]&&e(s[t],n);return n}!function(e){for(;e.lastChild;)e.removeChild(e.lastChild)}(this.element);const t=this.getElement();if(t&&t.parentNode&&t.parentNode.childNodes)for(const i of t.parentNode.childNodes){const t=e(i,null);this.element.appendChild(t)}t.parentNode&&this.observeTarget_(t.parentNode)}updatePixelPosition(){const e=this.positionWGS84_;if(!this.scene_||!e)return void this.setVisible(!1);let t=0;if(2===e.length){const i=this.scene_.globe.getHeight(Cesium.Cartographic.fromDegrees(e[0],e[1]));i&&this.scene_.globe.tilesLoaded&&(e[2]=i),i&&(t=i)}else t=e[2];const i=Cesium.Cartesian3.fromDegrees(e[0],e[1],t),n=this.scene_.camera,s=new Cesium.BoundingSphere(new Cesium.Cartesian3,6356752);if(!new Cesium.Occluder(s,n.position).isPointVisible(i))return void this.setVisible(!1);if(1!==n.frustum.computeCullingVolume(n.position,n.direction,n.up).computeVisibility(new Cesium.BoundingSphere(i)))return void this.setVisible(!1);this.setVisible(!0);const o=this.scene_.cartesianToCanvasCoordinates(i),r=[o.x,o.y],a=[this.scene_.canvas.width,this.scene_.canvas.height];this.updateRenderedPosition(r,a)}destroy(){this.scenePostRenderListenerRemover_&&this.scenePostRenderListenerRemover_(),this.observer_&&this.observer_.disconnect(),(0,Q.unByKey)(this.listenerKeys_),this.listenerKeys_.splice(0),this.element.removeNode?this.element.removeNode(!0):this.element.remove(),this.element=null}}const Pe=Le;const Te=class{constructor(e,t){this.map=e,this.overlays_=this.map.getOverlays(),this.scene=t,this.overlayContainerStopEvent_=document.createElement("DIV"),this.overlayContainerStopEvent_.className="ol-overlaycontainer-stopevent";["click","dblclick","mousedown","touchstart","MSPointerDown","pointerdown","mousewheel","wheel"].forEach((e=>{this.overlayContainerStopEvent_.addEventListener(e,(e=>e.stopPropagation()))})),this.scene.canvas.parentElement.appendChild(this.overlayContainerStopEvent_),this.overlayContainer_=document.createElement("DIV"),this.overlayContainer_.className="ol-overlaycontainer",this.scene.canvas.parentElement.appendChild(this.overlayContainer_),this.overlayMap_={}}getOverlayContainerStopEvent(){return this.overlayContainerStopEvent_}getOverlayContainer(){return this.overlayContainer_}synchronize(){this.destroyAll(),this.addOverlays(),this.overlays_.on("add",this.addOverlayFromEvent_.bind(this)),this.overlays_.on("remove",this.removeOverlayFromEvent_.bind(this))}addOverlayFromEvent_(e){const t=e.element;this.addOverlay(t)}addOverlays(){this.overlays_.forEach((e=>{this.addOverlay(e)}))}addOverlay(e){if(!e)return;const t=new Pe({scene:this.scene,synchronizer:this,parent:e}),i=r(e).toString();this.overlayMap_[i]=t}removeOverlayFromEvent_(e){const t=e.element;this.removeOverlay(t)}removeOverlay(e){const t=r(e).toString(),i=this.overlayMap_[t];i&&(i.destroy(),delete this.overlayMap_[t])}destroyAll(){Object.keys(this.overlayMap_).forEach((e=>{this.overlayMap_[e].destroy(),delete this.overlayMap_[e]}))}};const be=class{constructor(e){this.autoRenderLoop_=null,this.map_=e.map,this.time_=e.time||function(){return Cesium.JulianDate.now()},this.to4326Transform_=(0,i.getTransform)(this.map_.getView().getProjection(),"EPSG:4326"),this.resolutionScale_=1,this.canvasClientWidth_=0,this.canvasClientHeight_=0,this.resolutionScaleChanged_=!0;this.container_=document.createElement("DIV");const t=document.createAttribute("style");t.value="position:absolute;top:0;left:0;width:100%;height:100%;visibility:hidden;",this.container_.setAttributeNode(t);let n=e.viewer;n.container.appendChild(this.container_),this.enabled_=!1,this.pausedInteractions_=[],this.hiddenRootGroup_=null,this.scene_=n.scene,this.camera_=new ie(this.scene_,this.map_),this.globe_=n.scene.globe,this.dataSourceCollection_=n.dataSources,this.dataSourceDisplay_=n.dataSourceDisplay;const s=e.createSynchronizers?e.createSynchronizers(this.map_,this.scene_,this.dataSourceCollection_):[new re(this.map_,this.scene_),new ve(this.map_,this.scene_),new Te(this.map_,this.scene_)];for(let e=s.length-1;e>=0;--e)s[e].synchronize();this.trackedFeature_=null,this.trackedEntity_=null,n.scene.postUpdate.addEventListener(this.onAnimationFrame_,this)}onAnimationFrame_(e){this.camera_.checkCameraChange()}getCamera(){return this.camera_}getOlMap(){return this.map_}getOlView(){return this.map_.getView()}getCesiumScene(){return this.scene_}getDataSources(){return this.dataSourceCollection_}getDataSourceDisplay(){return this.dataSourceDisplay_}getEnabled(){return this.enabled_}setEnabled(e){this.enabled_!==e&&(this.enabled_=e,this.enabled_?this.camera_.readFromView():this.camera_.updateView())}warmUp(e,t){if(this.enabled_)return;this.throwOnUnitializedMap_(),this.camera_.readFromView();const i=this.globe_.ellipsoid,n=this.scene_.camera,s=i.cartesianToCartographic(n.position);s.height<e&&(s.height=e,n.position=i.cartographicToCartesian(s))}enableAutoRenderLoop(){this.autoRenderLoop_||(this.autoRenderLoop_=new X(this))}getAutoRenderLoop(){return this.autoRenderLoop_}setResolutionScale(e){(e=Math.max(0,e))!==this.resolutionScale_&&(this.resolutionScale_=Math.max(0,e),this.resolutionScaleChanged_=!0,this.autoRenderLoop_&&this.autoRenderLoop_.restartRenderLoop())}throwOnUnitializedMap_(){const e=this.map_.getView(),t=e.getCenter();if(!e.isDef()||isNaN(t[0])||isNaN(t[1]))throw new Error(`The OpenLayers map is not properly initialized: ${t} / ${e.getResolution()}`)}get trackedFeature(){return this.trackedFeature_}set trackedFeature(e){if(this.trackedFeature_!==e){const t=this.scene_;if(!e||!e.getGeometry())return t.screenSpaceCameraController.enableTilt=!0,this.trackedEntity_&&this.dataSourceDisplay_.defaultDataSource.entities.remove(this.trackedEntity_),this.trackedEntity_=null,this.trackedFeature_=null,this.entityView_=null,void t.camera.lookAtTransform(Cesium.Matrix4.IDENTITY);this.trackedFeature_=e;const i=this.to4326Transform_,n=function(){const t=e.getGeometry().getCoordinates(),n=i(t,void 0,t.length);return Y.ol4326CoordinateToCesiumCartesian(n)},s={position:new Cesium.CallbackProperty(((e,t)=>n()),!1),point:{pixelSize:1,color:Cesium.Color.TRANSPARENT}};this.trackedEntity_=this.dataSourceDisplay_.defaultDataSource.entities.add(s)}}};class Re{constructor(e){this.promise,this.url_=e}load(){return this.promise||(this.promise=new Promise(((e,t)=>{const i=document.createElement("script");i.onload=()=>e(),i.onerror=()=>t(),document.head.appendChild(i),i.src=this.url_}))),this.promise}}const Ee=class extends($()){constructor(e,{map:t,cameraExtentInRadians:i,cesiumIonDefaultAccessToken:n}={}){super(),this.cesiumUrl_=e,this.map=t,this.cameraExtentInRadians=i||null,this.boundingSphere_,this.blockLimiter_=!1,this.promise_,this.cesiumIonDefaultAccessToken_=n,this.ol3d,this.cesiumInitialTilt_=ee(50),this.fogDensity=1e-4,this.fogSSEFactor=25,this.minimumZoomDistance=2,this.maximumZoomDistance=1e7,this.limitCameraToBoundingSphereRatio=e=>e>3e3?9:3}load(){if(!this.promise_){const e=new Re(this.cesiumUrl_);this.promise_=e.load().then((()=>this.onCesiumLoaded()))}return this.promise_}onCesiumLoaded(){if(this.cameraExtentInRadians){const e=new Cesium.Rectangle(...this.cameraExtentInRadians);Cesium.Camera.DEFAULT_VIEW_RECTANGLE=e,this.boundingSphere_=Cesium.BoundingSphere.fromRectangle3D(e,Cesium.Ellipsoid.WGS84,300)}this.cesiumIonDefaultAccessToken_&&(Cesium.Ion.defaultAccessToken=this.cesiumIonDefaultAccessToken_),this.ol3d=this.instantiateOLCesium();const e=this.ol3d.getCesiumScene();return this.configureForUsability(e),this.configureForPerformance(e),this.dispatchEvent("load"),this.ol3d}instantiateOLCesium(){const e=new be({map:this.map}),t=e.getCesiumScene(),i=Cesium.createWorldTerrain();return t.terrainProvider=i,e}configureForPerformance(e){const t=e.fog;t.enabled=!0,t.density=this.fogDensity,t.screenSpaceErrorFactor=this.fogSSEFactor}configureForUsability(e){const t=e.screenSpaceCameraController;t.minimumZoomDistance=this.minimumZoomDistance,t.maximumZoomDistance=this.maximumZoomDistance,e.globe.depthTestAgainstTerrain=!0,e.globe.baseColor=Cesium.Color.WHITE,e.backgroundColor=Cesium.Color.WHITE,this.boundingSphere_&&e.postRender.addEventListener(this.limitCameraToBoundingSphere.bind(this),e),this.ol3d.enableAutoRenderLoop()}limitCameraToBoundingSphere(){if(this.boundingSphere_&&!this.blockLimiter_){const e=this.ol3d.getCesiumScene().camera,t=e.position,i=Cesium.Cartographic.fromCartesian(t),n=this.limitCameraToBoundingSphereRatio(i.height);if(Cesium.Cartesian3.distance(this.boundingSphere_.center,t)>this.boundingSphere_.radius*n){if(!0===e.flying)return;{this.blockLimiter_=!0;const t=()=>this.blockLimiter_=!1;e.flyToBoundingSphere(this.boundingSphere_,{complete:t,cancel:t})}}}}toggle3d(){return this.load().then((e=>{const t=e.getEnabled(),i=e.getCesiumScene();return t?Y.resetToNorthZenith(this.map,i).then((()=>{e.setEnabled(!1),this.dispatchEvent("toggle")})):(e.setEnabled(!0),this.dispatchEvent("toggle"),Y.rotateAroundBottomCenter(i,this.cesiumInitialTilt_))}))}set3dWithView(e,t,i,n,s){return this.load().then((o=>{const r=o.getEnabled(),a=o.getCesiumScene().camera,l=Cesium.Cartesian3.fromDegrees(e,t,i),c={heading:Cesium.Math.toRadians(n),pitch:Cesium.Math.toRadians(s),roll:0};r||(o.setEnabled(!0),this.dispatchEvent("toggle")),a.setView({destination:l,orientation:c})}))}is3dEnabled(){return!!this.ol3d&&this.ol3d.getEnabled()}getHeading(){return this.map&&this.map.getView().getRotation()||0}getTiltOnGlobe(){const e=this.ol3d.getCesiumScene();return-Y.computeSignedTiltAngleOnGlobe(e)}setHeading(e){const t=this.ol3d.getCesiumScene(),i=Y.pickBottomPoint(t);i&&Y.setHeadingUsingBottomCenter(t,e,i)}getOl3d(){return this.ol3d}getOlView(){return this.map.getView()}getCesiumViewMatrix(){return this.ol3d.getCesiumScene().camera.viewMatrix}getCesiumScene(){return this.ol3d.getCesiumScene()}flyToRectangle(e,t=0){const i=this.getCesiumScene().camera,n=i.getRectangleCameraCoordinates(e),s=Cesium.Cartesian3.magnitude(n)+t;return Cesium.Cartesian3.normalize(n,n),Cesium.Cartesian3.multiplyByScalar(n,s,n),new Promise(((e,t)=>{this.cameraExtentInRadians?i.flyTo({destination:n,complete:()=>e(),cancel:()=>t(),endTransform:Cesium.Matrix4.IDENTITY}):t()}))}getCameraExtentRectangle(){if(this.cameraExtentInRadians)return new Cesium.Rectangle(...this.cameraExtentInRadians)}},xe=be;var Oe=window.olcs={};Oe.OLCesium=be,Oe.AbstractSynchronizer=oe,Oe.RasterSynchronizer=re,Oe.VectorSynchronizer=ve,Oe.core=Y,Oe.core.OLImageryProvider=E,Oe.core.VectorLayerCounterpart=fe,Oe.contrib={},Oe.contrib.LazyLoader=Re,Oe.contrib.Manager=Ee,olcs_unused_var=t})();