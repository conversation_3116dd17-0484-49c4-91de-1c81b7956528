/**
 * Mars3D平台插件,结合heatmap可视化功能插件  mars3d-heatmap
 *
 * 版本信息：v3.10.3
 * 编译日期：2025-08-17 12:11
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：火星科技免费公开版 ，2025-07-01
 */
(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
	typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-heatmap"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';(function(_0x4c2619,_0x4fa411){var _0x41ee3b={_0x1d0983:0x2ab,_0x161a56:0x27e,_0x27a9a4:0x38b,_0x36b630:0x22f,_0x244b79:0x21e,_0x5943cb:0x26f,_0x3edae7:0x2e0,_0x105330:0x2a7,_0x5744d5:0x3c5,_0x425252:0x3a8};function _0xdc7294(_0x1254c0,_0x49d117){return _0x44a8(_0x1254c0-0xff,_0x49d117);}function _0x3ad70e(_0x1dc48a,_0x3fc287){return _0x44a8(_0x1dc48a-0x1ef,_0x3fc287);}var _0x8fb854=_0x4c2619();while(!![]){try{var _0x4e5d72=-parseInt(_0xdc7294(_0x41ee3b._0x1d0983,_0x41ee3b._0x161a56))/0x1*(-parseInt(_0xdc7294(0x2aa,0x28d))/0x2)+-parseInt(_0x3ad70e(0x36b,_0x41ee3b._0x27a9a4))/0x3*(parseInt(_0xdc7294(_0x41ee3b._0x36b630,_0x41ee3b._0x244b79))/0x4)+-parseInt(_0xdc7294(0x25e,0x25c))/0x5+-parseInt(_0xdc7294(0x27c,0x236))/0x6+parseInt(_0xdc7294(0x2c4,0x2f4))/0x7+parseInt(_0xdc7294(0x2c3,_0x41ee3b._0x5943cb))/0x8+parseInt(_0xdc7294(_0x41ee3b._0x3edae7,_0x41ee3b._0x105330))/0x9*(parseInt(_0x3ad70e(_0x41ee3b._0x5744d5,_0x41ee3b._0x425252))/0xa);if(_0x4e5d72===_0x4fa411)break;else _0x8fb854['push'](_0x8fb854['shift']());}catch(_0x2c2720){_0x8fb854['push'](_0x8fb854['shift']());}}}(_0xdc04,0xd56cd));function _interopNamespace(_0x10a3c4){var _0x484cc3={_0x4e2ba4:0x23a};function _0x2b8860(_0x370f73,_0x8e7048){return _0x44a8(_0x370f73-0xb5,_0x8e7048);}if(_0x10a3c4&&_0x10a3c4[_0x2b8860(_0x484cc3._0x4e2ba4,0x259)])return _0x10a3c4;var _0x2fa7d2=Object['create'](null);return _0x10a3c4&&Object['keys'](_0x10a3c4)['forEach'](function(_0x57a2e5){if(_0x57a2e5!=='default'){var _0x565a3b=Object['getOwnPropertyDescriptor'](_0x10a3c4,_0x57a2e5);Object['defineProperty'](_0x2fa7d2,_0x57a2e5,_0x565a3b['get']?_0x565a3b:{'enumerable':!![],'get':function(){return _0x10a3c4[_0x57a2e5];}});}}),_0x2fa7d2['default']=_0x10a3c4,_0x2fa7d2;}function _mergeNamespaces(_0x359a76,_0x528cba){var _0x1477e9={_0x23e0e7:0x199,_0x2311c8:0x130};return _0x528cba['forEach'](function(_0xc5b633){function _0x3b808a(_0x3be8a8,_0x8c3191){return _0x44a8(_0x8c3191- -0x5f,_0x3be8a8);}function _0x3c19d3(_0x5f13ae,_0x206c4c){return _0x44a8(_0x5f13ae- -0x17f,_0x206c4c);}_0xc5b633&&typeof _0xc5b633!==_0x3b808a(_0x1477e9._0x23e0e7,0x18f)&&!Array[_0x3b808a(_0x1477e9._0x2311c8,0x117)](_0xc5b633)&&Object['keys'](_0xc5b633)['forEach'](function(_0x304380){if(_0x304380!=='default'&&!(_0x304380 in _0x359a76)){var _0xd38a52=Object['getOwnPropertyDescriptor'](_0xc5b633,_0x304380);Object['defineProperty'](_0x359a76,_0x304380,_0xd38a52['get']?_0xd38a52:{'enumerable':!![],'get':function(){return _0xc5b633[_0x304380];}});}});}),_0x359a76;}var mars3d__namespace=_interopNamespace(mars3d),commonjsGlobal=typeof globalThis!=='undefined'?globalThis:typeof window!==_0x13a5eb(0x2e0,0x2ee)?window:typeof global!=='undefined'?global:typeof self!=='undefined'?self:{},_0x3a6b1a={};_0x3a6b1a[_0xdbdd05(-0x222,-0x27c)]={};var heatmap$1=_0x3a6b1a;(function(_0x1b1894){var _0x11322b={_0x1e1b92:0x307,_0x7119c6:0x2f0,_0x4fd7c0:0x2c5,_0x355e99:0x2e1,_0x4fee80:0x2e6,_0x3c1b04:0x322},_0x55d222={_0x5c7d7a:0x1a1},_0x3ae8ed={_0xa0dce7:0x48c};function _0x1294ac(_0x147476,_0x19319c){return _0x13a5eb(_0x19319c,_0x147476- -0x3a8);}(function(_0x5ac84c,_0x4539ef,_0x1814b1){var _0x482eea={_0x7a0cc3:0x329};function _0x3b6f50(_0x43ee4b,_0x53119f){return _0x44a8(_0x53119f-_0x482eea._0x7a0cc3,_0x43ee4b);}_0x1b1894[_0x3b6f50(0x469,_0x3ae8ed._0xa0dce7)]?_0x1b1894['exports']=_0x1814b1():_0x4539ef[_0x5ac84c]=_0x1814b1();}(_0x1294ac(-0x68,-0x61),commonjsGlobal,function(){var _0x1c88b9={_0x5cc914:0x380},_0x232c4f={_0x30b95c:0x80,_0x5455dc:0xe0},_0x5b25f2={_0x548988:0x62},_0x39b898={_0x5e5c88:0x68},_0x5c4a5f={_0x1ddd23:0x441,_0x597cfe:0x15,_0x4db8c1:0x46b,_0x3e9b5a:0x4c8,_0x43baa1:0x22,_0x4c19f4:0x82,_0xffeda3:0x19,_0x54d2b5:0x49e},_0x157eb4={_0x1ac6a8:0x42a,_0x59c42c:0x2f2,_0x8da177:0x321,_0x4400e7:0x2e0,_0x24e051:0x3d2,_0x120330:0x319,_0x2bd697:0x3f7,_0x2c7cb9:0x360,_0x482158:0x424},_0x23a773={_0x47fb7a:0x371,_0x593b53:0x114,_0x1cfdcb:0xc2,_0x534ada:0x58,_0x18971d:0xa4},_0x2a9100={_0x22d383:0x148,_0x477950:0x1a4},_0x557127={_0x1a7d13:0xe0},_0x5ea531={_0x1da52d:0x497,_0x1dcc09:0x541,_0x40fbdb:0x1a7,_0x4c5059:0x495,_0x4748c3:0x453,_0x232117:0x18b,_0x18c948:0x1ab,_0x3fbe81:0x151,_0x2c76a7:0x1b1,_0x1208ad:0x15e,_0x34d41f:0x48d,_0x921841:0x4b6,_0x261fa9:0x538,_0x5d9ad3:0x57a,_0xbfb2e:0x49c,_0x580407:0x4de,_0x19aadc:0x527,_0x1d1e8a:0x525},_0x59775d={_0x14037d:0x238,_0x1e3c14:0x4aa,_0x15be33:0x467,_0x197872:0x3fd},_0x5b2074={_0x20fca7:0x576,_0x39a169:0x400,_0x1fb008:0x458,_0x44f382:0x584},_0x1295c5={_0x2ac50a:0x20f,_0x35f916:0x23c,_0x5d0157:0x288},_0x15f27c={_0x3b148c:0x2c5,_0x20e7d1:0x2d7,_0x3db0c2:0x243,_0x21d8e7:0x27e,_0x48b738:0x255,_0x310926:0x29c,_0x2665cb:0x241,_0x344d79:0x2c1,_0x3c7716:0x2e2},_0x5120a9={_0x22cdf8:0x162,_0x30b608:0x1f},_0x1b611d={};_0x1b611d['0.25']=_0x2bb87f(0x291,0x2e7);function _0x10ad77(_0x59df71,_0x2bbe93){return _0x1294ac(_0x2bbe93-0x44f,_0x59df71);}_0x1b611d['0.55']='rgb(0,255,0)',_0x1b611d['0.85']='yellow',_0x1b611d['1']='rgb(255,0,0)';var _0x133f47={};_0x133f47[_0x2bb87f(_0x11322b._0x1e1b92,0x368)]=0x28,_0x133f47['defaultRenderer']='canvas2d',_0x133f47[_0x10ad77(0x354,0x33c)]=_0x1b611d,_0x133f47[_0x2bb87f(0x28e,_0x11322b._0x7119c6)]=0x1,_0x133f47[_0x2bb87f(_0x11322b._0x4fd7c0,_0x11322b._0x355e99)]=0x0,_0x133f47['defaultBlur']=0.85,_0x133f47['defaultXField']='x',_0x133f47[_0x10ad77(0x3fb,0x3e3)]='y',_0x133f47[_0x2bb87f(0x2cc,0x2ce)]=_0x2bb87f(_0x11322b._0x4fee80,_0x11322b._0x3c1b04),_0x133f47['plugins']={};var _0x4bfacc=_0x133f47,_0x28c36e=function _0x52b17b(){var _0x539f11={_0x4cc01c:0xbc},_0x540afc={_0x10188e:0x4d6,_0x108e43:0x544,_0x5cf71e:0x4e5},_0x167919={_0x20da3f:0x48d,_0x460663:0x4b5},_0x1fb5d4={_0x3d8b01:0x2fe},_0x3caad5={_0x5d492b:0x22d,_0x4f5d84:0x1df,_0x3121ef:0x234,_0x2d0460:0x24e,_0x320dc4:0x250,_0x3f4869:0x24d,_0x246205:0x29d,_0x5f37fa:0x5b5},_0x21f69c={_0x53949b:0x128},_0x2e8b68={_0x414fd9:0x220,_0x279174:0x200},_0x48f05e={_0x114198:0x38b,_0xe49463:0x364,_0x23eacd:0x338},_0x52c301={_0x40dae3:0x1f2},_0x58747c={_0x544dd0:0x153},_0x494bf3={_0x4313df:0x285,_0xbac37d:0xed,_0x212163:0x335},_0x2b127f=function _0xe8381b(_0x442a71){var _0x396b77={_0x40f978:0x115};this['_coordinator']={},this[_0xfcc7d1(0x29b,_0x494bf3._0x4313df)]=[];function _0xfcc7d1(_0x337c3f,_0x10a714){return _0x44a8(_0x10a714-_0x396b77._0x40f978,_0x337c3f);}this[_0xfcc7d1(0x2a0,0x262)]=[],this['_min']=0xa,this[_0x2041f9(_0x494bf3._0xbac37d,0x105)]=0x1;function _0x2041f9(_0x35bd4c,_0x5bd28b){return _0x44a8(_0x5bd28b- -0x8a,_0x35bd4c);}this['_xField']=_0x442a71['xField']||_0x442a71['defaultXField'],this['_yField']=_0x442a71[_0xfcc7d1(_0x494bf3._0x212163,0x2df)]||_0x442a71[_0xfcc7d1(0x313,0x2fc)],this[_0xfcc7d1(0x291,0x2d1)]=_0x442a71['valueField']||_0x442a71['defaultValueField'],_0x442a71['radius']&&(this['_cfgRadius']=_0x442a71['radius']);},_0x6931d3=_0x4bfacc['defaultRadius'];return _0x2b127f['prototype']={'_organiseData':function(_0x4499d6,_0x11f267){var _0x40392d=_0x4499d6[this[_0x2e3384(0xd3,0xcc)]];function _0x23a716(_0x3f6224,_0x3bdb09){return _0x44a8(_0x3bdb09- -_0x58747c._0x544dd0,_0x3f6224);}var _0x38f200=_0x4499d6[this['_yField']],_0x547043=this['_radi'];function _0x2e3384(_0x392d6a,_0x3e9b87){return _0x44a8(_0x3e9b87- -0x89,_0x392d6a);}var _0x5f311c=this[_0x2e3384(0xcb,0xe7)],_0x18294c=this[_0x2e3384(_0x5120a9._0x22cdf8,0x106)],_0x57d663=this['_min'],_0x56164b=_0x4499d6[this['_valueField']]||0x1,_0x4f80d7=_0x4499d6['radius']||this['_cfgRadius']||_0x6931d3;!_0x5f311c[_0x40392d]&&(_0x5f311c[_0x40392d]=[],_0x547043[_0x40392d]=[]);!_0x5f311c[_0x40392d][_0x38f200]?(_0x5f311c[_0x40392d][_0x38f200]=_0x56164b,_0x547043[_0x40392d][_0x38f200]=_0x4f80d7):_0x5f311c[_0x40392d][_0x38f200]+=_0x56164b;var _0xfefef2=_0x5f311c[_0x40392d][_0x38f200];if(_0xfefef2>_0x18294c)return!_0x11f267?this['_max']=_0xfefef2:this['setDataMax'](_0xfefef2),![];else{if(_0xfefef2<_0x57d663)return!_0x11f267?this[_0x2e3384(0xe9,0xdd)]=_0xfefef2:this['setDataMin'](_0xfefef2),![];else{var _0x3f66d7={};return _0x3f66d7['x']=_0x40392d,_0x3f66d7['y']=_0x38f200,_0x3f66d7[_0x2e3384(0xd6,0x130)]=_0x56164b,_0x3f66d7['radius']=_0x4f80d7,_0x3f66d7[_0x23a716(-0x5,_0x5120a9._0x30b608)]=_0x57d663,_0x3f66d7['max']=_0x18294c,_0x3f66d7;}}},'_unOrganizeData':function(){var _0x4fb34c=[];function _0x36a8dd(_0x2c73b2,_0x1f636d){return _0x44a8(_0x1f636d-_0x52c301._0x40dae3,_0x2c73b2);}function _0x309933(_0x3befaa,_0x3340bf){return _0x44a8(_0x3340bf-0x155,_0x3befaa);}var _0x47f9a7=this['_data'],_0x28e238=this[_0x36a8dd(0x361,0x33f)];for(var _0x29396c in _0x47f9a7){for(var _0x362b9f in _0x47f9a7[_0x29396c]){var _0x5ad62a={};_0x5ad62a['x']=_0x29396c,_0x5ad62a['y']=_0x362b9f,_0x5ad62a['radius']=_0x28e238[_0x29396c][_0x362b9f],_0x5ad62a['value']=_0x47f9a7[_0x29396c][_0x362b9f],_0x4fb34c['push'](_0x5ad62a);}}var _0xde1388={};return _0xde1388[_0x36a8dd(_0x48f05e._0x114198,_0x48f05e._0xe49463)]=this['_min'],_0xde1388['max']=this['_max'],_0xde1388[_0x36a8dd(0x378,_0x48f05e._0x23eacd)]=_0x4fb34c,_0xde1388;},'_onExtremaChange':function(){var _0x52d648={_0x1700ba:0x49};function _0x1c425d(_0x287ab5,_0x2def36){return _0x44a8(_0x2def36-_0x52d648._0x1700ba,_0x287ab5);}this[_0x1c425d(_0x2e8b68._0x414fd9,_0x2e8b68._0x279174)]['emit']('extremachange',{'min':this['_min'],'max':this['_max']});},'addData':function(){if(arguments[0x0][_0x155680(_0x15f27c._0x3b148c,0x2b5)]>0x0){var _0x75b478=arguments[0x0],_0x578148=_0x75b478[_0x355886(_0x15f27c._0x20e7d1,0x308)];while(_0x578148--){this[_0x355886(_0x15f27c._0x3db0c2,0x29d)][_0x155680(0x2c2,_0x15f27c._0x21d8e7)](this,_0x75b478[_0x578148]);}}else{var _0x57a70e=this['_organiseData'](arguments[0x0],!![]);_0x57a70e&&(this[_0x155680(_0x15f27c._0x48b738,0x28d)]['length']===0x0&&(this['_min']=this['_max']=_0x57a70e['value']),this[_0x155680(_0x15f27c._0x310926,_0x15f27c._0x2665cb)][_0x155680(_0x15f27c._0x344d79,0x31c)](_0x355886(0x2a5,_0x15f27c._0x3c7716),{'min':this['_min'],'max':this['_max'],'data':[_0x57a70e]}));}function _0x155680(_0x570c70,_0x1af8ea){return _0x44a8(_0x570c70-0xe5,_0x1af8ea);}function _0x355886(_0x5bba5f,_0x3e17cc){return _0x44a8(_0x3e17cc-_0x21f69c._0x53949b,_0x5bba5f);}return this;},'setData':function(_0x1ca339){var _0x15c2d4=_0x1ca339[_0x12554c(_0x3caad5._0x5d492b,_0x3caad5._0x4f5d84)],_0x25898d=_0x15c2d4['length'];this['_data']=[],this[_0x12554c(_0x3caad5._0x3121ef,_0x3caad5._0x2d0460)]=[];for(var _0x2d62bd=0x0;_0x2d62bd<_0x25898d;_0x2d62bd++){this['_organiseData'](_0x15c2d4[_0x2d62bd],![]);}this[_0x12554c(0x276,_0x3caad5._0x320dc4)]=_0x1ca339['max'],this[_0x12554c(_0x3caad5._0x3f4869,0x220)]=_0x1ca339[_0x12554c(0x259,_0x3caad5._0x246205)]||0x0;function _0x4b1bed(_0x3cadfb,_0x1744be){return _0x44a8(_0x3cadfb-0x3cf,_0x1744be);}this[_0x12554c(0x291,0x29f)]();function _0x12554c(_0x3f3d1d,_0x125bbd){return _0x44a8(_0x3f3d1d-0xe7,_0x125bbd);}return this['_coordinator']['emit']('renderall',this[_0x4b1bed(_0x3caad5._0x5f37fa,0x5ff)]()),this;},'removeData':function(){},'setDataMax':function(_0x5cdfad){this[_0xcfcefe(_0x167919._0x20da3f,0x494)]=_0x5cdfad;function _0xcfcefe(_0x45532f,_0x21b909){return _0x44a8(_0x45532f-_0x1fb5d4._0x3d8b01,_0x21b909);}function _0x22f0d9(_0x5bb981,_0x113708){return _0x44a8(_0x5bb981- -0x253,_0x113708);}return this[_0xcfcefe(0x4a8,0x48b)](),this[_0xcfcefe(_0x167919._0x460663,0x474)]['emit']('renderall',this[_0xcfcefe(0x4e4,0x530)]()),this;},'setDataMin':function(_0x326423){function _0x5077ed(_0x465930,_0x7969b6){return _0x44a8(_0x465930- -0x1a9,_0x7969b6);}function _0x33d145(_0x581323,_0x1e5c87){return _0x44a8(_0x581323- -0x3b9,_0x1e5c87);}return this['_min']=_0x326423,this[_0x33d145(-_0x1295c5._0x2ac50a,-_0x1295c5._0x35f916)](),this['_coordinator']['emit'](_0x33d145(-_0x1295c5._0x5d0157,-0x288),this[_0x33d145(-0x1d3,-0x221)]()),this;},'setCoordinator':function(_0x1c4dc3){this['_coordinator']=_0x1c4dc3;},'_getInternalData':function(){var _0x5d45d9={_0x3b4cd0:0x39d};function _0x1b6f06(_0x5ae2d3,_0x4f05e8){return _0x44a8(_0x4f05e8-0x39f,_0x5ae2d3);}var _0x308069={};_0x308069[_0x5787cd(0x54a,0x568)]=this['_max'],_0x308069['min']=this[_0x5787cd(_0x540afc._0x10188e,0x503)],_0x308069[_0x1b6f06(_0x540afc._0x108e43,_0x540afc._0x5cf71e)]=this['_data'],_0x308069['radi']=this['_radi'];function _0x5787cd(_0x3d2b00,_0x216731){return _0x44a8(_0x216731-_0x5d45d9._0x3b4cd0,_0x3d2b00);}return _0x308069;},'getData':function(){function _0x2ccf89(_0x489dc4,_0x37420c){return _0x44a8(_0x37420c-_0x539f11._0x4cc01c,_0x489dc4);}return this[_0x2ccf89(0x237,0x213)]();}},_0x2b127f;}(),_0x3cb2db=function _0x280246(){var _0x5c0fe5={_0x22e939:0x41a},_0x1e6213={_0x41c21e:0x2b3},_0x3975ca={_0x13e558:0x311},_0x43fab3={_0x55b4e7:0x1ab,_0x4872bd:0x247,_0x3b52d0:0x247},_0x4b570a={_0x30b226:0x41,_0x46867e:0x24f},_0x3fc291={_0x45b264:0x6},_0x2b543a={_0x142858:0x350},_0x3cf0db={_0x5d59a8:0x359},_0x3e848d={_0x1de57e:0x190,_0x1e1d8f:0x176,_0x198e72:0x174,_0x122640:0x196,_0x6d597:0x17b,_0x41b513:0x123,_0x43c34a:0x1b1,_0x446884:0x89},_0x271f95=function(_0x3d29ff){var _0x51dce3={_0x2d6633:0x26e},_0x4bd80f=_0x3d29ff['gradient']||_0x3d29ff['defaultGradient'],_0x424e82=document['createElement'](_0x2a6b4e(0x59d,_0x5b2074._0x20fca7)),_0x75eb75={};_0x75eb75[_0xc1c431(0x417,_0x5b2074._0x39a169)]=!![];var _0x1c765c=_0x424e82['getContext']('2d',_0x75eb75);_0x424e82['width']=0x100;function _0xc1c431(_0x3dd52d,_0xd045b0){return _0x44a8(_0x3dd52d-_0x51dce3._0x2d6633,_0xd045b0);}_0x424e82['height']=0x1;var _0x373e01=_0x1c765c['createLinearGradient'](0x0,0x0,0x100,0x1);for(var _0x1cc6aa in _0x4bd80f){_0x373e01['addColorStop'](_0x1cc6aa,_0x4bd80f[_0x1cc6aa]);}_0x1c765c[_0xc1c431(_0x5b2074._0x1fb008,0x400)]=_0x373e01;function _0x2a6b4e(_0xeb4b38,_0x1fe993){return _0x44a8(_0x1fe993-0x3e6,_0xeb4b38);}return _0x1c765c['fillRect'](0x0,0x0,0x100,0x1),_0x1c765c['getImageData'](0x0,0x0,0x100,0x1)[_0x2a6b4e(_0x5b2074._0x44f382,0x52c)];},_0x5aafda=function(_0x1c9b16,_0x1a401a){function _0x21dd38(_0x550d13,_0x46a67d){return _0x44a8(_0x46a67d-0x4c,_0x550d13);}function _0x287401(_0x3d8610,_0x53366d){return _0x44a8(_0x53366d- -0x273,_0x3d8610);}var _0x520b12=document[_0x287401(-0x16c,-0x122)]('canvas'),_0x5ce6e1={};_0x5ce6e1[_0x21dd38(0x1c1,0x1f5)]=!![];var _0x1791ea=_0x520b12[_0x287401(-_0x3e848d._0x1de57e,-0x13f)]('2d',_0x5ce6e1),_0x398b3c=_0x1c9b16,_0x5a3003=_0x1c9b16;_0x520b12[_0x21dd38(_0x3e848d._0x1e1d8f,0x188)]=_0x520b12[_0x287401(-0xcc,-0x111)]=_0x1c9b16*0x2;if(_0x1a401a==0x1)_0x1791ea[_0x21dd38(0x253,0x200)](),_0x1791ea['arc'](_0x398b3c,_0x5a3003,_0x1c9b16,0x0,0x2*Math['PI'],![]),_0x1791ea['fillStyle']=_0x21dd38(_0x3e848d._0x198e72,_0x3e848d._0x122640),_0x1791ea['fill']();else{var _0x200a02=_0x1791ea[_0x287401(-_0x3e848d._0x6d597,-_0x3e848d._0x41b513)](_0x398b3c,_0x5a3003,_0x1c9b16*_0x1a401a,_0x398b3c,_0x5a3003,_0x1c9b16);_0x200a02['addColorStop'](0x0,'rgba(0,0,0,1)'),_0x200a02['addColorStop'](0x1,_0x21dd38(0x1ca,_0x3e848d._0x43c34a)),_0x1791ea[_0x287401(-0x67,-_0x3e848d._0x446884)]=_0x200a02,_0x1791ea[_0x287401(-0xd5,-0xbd)](0x0,0x0,0x2*_0x1c9b16,0x2*_0x1c9b16);}return _0x520b12;},_0xf84b6f=function(_0x159044){var _0x2477a4=[],_0x1b6297=_0x159044[_0x17caf6(0x249,0x1fb)],_0x3ec8d3=_0x159044['max'],_0x260885=_0x159044['radi'],_0x159044=_0x159044[_0x1ccf97(0x3f5,0x396)],_0x26b516=Object[_0x17caf6(0x1f8,_0x59775d._0x14037d)](_0x159044),_0x724fc2=_0x26b516[_0x17caf6(0x251,0x269)];function _0x1ccf97(_0x4395b6,_0x470866){return _0x44a8(_0x4395b6-0x2af,_0x470866);}function _0x17caf6(_0x2ca0a1,_0xe03d56){return _0x44a8(_0xe03d56-0x89,_0x2ca0a1);}while(_0x724fc2--){var _0x86455=_0x26b516[_0x724fc2],_0x3cc4c8=Object[_0x1ccf97(0x45e,_0x59775d._0x1e3c14)](_0x159044[_0x86455]),_0xf31d2c=_0x3cc4c8['length'];while(_0xf31d2c--){var _0x3525d8=_0x3cc4c8[_0xf31d2c],_0x3da24d=_0x159044[_0x86455][_0x3525d8],_0x3f94b7=_0x260885[_0x86455][_0x3525d8],_0x4920cd={};_0x4920cd['x']=_0x86455,_0x4920cd['y']=_0x3525d8,_0x4920cd[_0x1ccf97(0x468,_0x59775d._0x15be33)]=_0x3da24d,_0x4920cd[_0x1ccf97(_0x59775d._0x197872,0x3c7)]=_0x3f94b7,_0x2477a4['push'](_0x4920cd);}}var _0x1b14a5={};return _0x1b14a5['min']=_0x1b6297,_0x1b14a5['max']=_0x3ec8d3,_0x1b14a5['data']=_0x2477a4,_0x1b14a5;};function _0xee56d5(_0x339ece){var _0x53a1cf=_0x339ece['container'],_0x2d8302=this[_0x18b797(_0x5ea531._0x1da52d,0x494)]=document['createElement'](_0x18b797(0x4e9,0x4e1));function _0x18b797(_0x1c9bf4,_0x12c2df){return _0x44a8(_0x1c9bf4-_0x3cf0db._0x5d59a8,_0x12c2df);}var _0xac7c18=this['canvas']=_0x339ece[_0x206000(0x222,0x1df)]||document['createElement']('canvas');this['_renderBoundaries']=[0x2710,0x2710,0x0,0x0];var _0x5b956c=getComputedStyle(_0x339ece['container'])||{};_0xac7c18[_0x18b797(_0x5ea531._0x1dcc09,0x547)]=_0x206000(0x1bd,_0x5ea531._0x40fbdb),this[_0x206000(0x1a3,0x1dd)]=_0xac7c18['width']=_0x2d8302[_0x18b797(_0x5ea531._0x4c5059,_0x5ea531._0x4748c3)]=_0x339ece[_0x206000(0x18c,_0x5ea531._0x232117)]||+_0x5b956c['width'][_0x206000(0x209,_0x5ea531._0x18c948)](/px/,''),this['_height']=_0xac7c18[_0x206000(0x191,0x1b1)]=_0x2d8302['height']=_0x339ece[_0x206000(_0x5ea531._0x3fbe81,_0x5ea531._0x2c76a7)]||+_0x5b956c[_0x206000(0x203,0x1b1)][_0x206000(_0x5ea531._0x1208ad,0x1ab)](/px/,'');var _0x2993dd={};function _0x206000(_0x222d96,_0x1b68f7){return _0x44a8(_0x1b68f7-0x4f,_0x222d96);}_0x2993dd[_0x206000(0x1d5,0x1f8)]=!![],this['shadowCtx']=_0x2d8302['getContext']('2d',_0x2993dd);var _0x5a3cdc={};_0x5a3cdc['willReadFrequently']=!![],this[_0x206000(0x18b,0x1dc)]=_0xac7c18[_0x18b797(_0x5ea531._0x34d41f,_0x5ea531._0x921841)]('2d',_0x5a3cdc),_0xac7c18[_0x18b797(_0x5ea531._0x261fa9,_0x5ea531._0x5d9ad3)][_0x18b797(_0x5ea531._0xbfb2e,0x493)]=_0x2d8302[_0x18b797(_0x5ea531._0x261fa9,0x4ff)]['cssText']='position:absolute;left:0;top:0;',_0x53a1cf[_0x18b797(_0x5ea531._0x261fa9,_0x5ea531._0x580407)][_0x18b797(_0x5ea531._0x19aadc,0x57d)]=_0x18b797(_0x5ea531._0x1d1e8a,0x553),_0x53a1cf['appendChild'](_0xac7c18),this['_palette']=_0x271f95(_0x339ece),this['_templates']={},this[_0x206000(0x196,0x1f7)](_0x339ece);}return _0xee56d5['prototype']={'renderPartial':function(_0x28a24e){function _0x3c2073(_0x2e896f,_0x9d4bcd){return _0x44a8(_0x9d4bcd- -_0x2b543a._0x142858,_0x2e896f);}function _0x9723aa(_0x2fc4f7,_0x58ed39){return _0x44a8(_0x58ed39- -0x1cd,_0x2fc4f7);}_0x28a24e[_0x3c2073(-0x1ab,-0x20a)][_0x9723aa(0x6,0x13)]>0x0&&(this['_drawAlpha'](_0x28a24e),this['_colorize']());},'renderAll':function(_0x3379ec){this['_clear']();function _0x11e583(_0x4a6963,_0x26f2b7){return _0x44a8(_0x4a6963- -0x2c1,_0x26f2b7);}_0x3379ec['data'][_0x11e583(-0xe1,-_0x557127._0x1a7d13)]>0x0&&(this['_drawAlpha'](_0xf84b6f(_0x3379ec)),this['_colorize']());},'_updateGradient':function(_0x1fa3e1){this['_palette']=_0x271f95(_0x1fa3e1);},'updateConfig':function(_0x1e551b){function _0x32c41e(_0x24f74e,_0x43370c){return _0x44a8(_0x43370c-_0x3fc291._0x45b264,_0x24f74e);}_0x1e551b['gradient']&&this[_0x32c41e(_0x2a9100._0x22d383,_0x2a9100._0x477950)](_0x1e551b),this['_setStyles'](_0x1e551b);},'setDimensions':function(_0x56ce46,_0x983198){var _0x5418f7={_0x4c7ecf:0x111};this['_width']=_0x56ce46,this[_0x427502(0x2d0,0x2c4)]=_0x983198;function _0x41d9dd(_0x3179cd,_0x31bdda){return _0x44a8(_0x31bdda- -0xd7,_0x3179cd);}function _0x427502(_0x1b0308,_0x4257a6){return _0x44a8(_0x1b0308-_0x5418f7._0x4c7ecf,_0x4257a6);}this['canvas'][_0x41d9dd(_0x4b570a._0x30b226,0x65)]=this['shadowCanvas']['width']=_0x56ce46,this['canvas']['height']=this[_0x427502(_0x4b570a._0x46867e,0x20a)][_0x41d9dd(0xac,0x8b)]=_0x983198;},'_clear':function(){var _0x77e74d={_0x50335a:0x1d7};function _0x4767c4(_0x352ea9,_0x18da70){return _0x44a8(_0x18da70- -0x263,_0x352ea9);}function _0x52ac9b(_0x348606,_0x4609e9){return _0x44a8(_0x348606-_0x77e74d._0x50335a,_0x4609e9);}this[_0x52ac9b(_0x23a773._0x47fb7a,0x310)]['clearRect'](0x0,0x0,this['_width'],this['_height']),this['ctx'][_0x4767c4(-0xfe,-_0x23a773._0x593b53)](0x0,0x0,this[_0x4767c4(-_0x23a773._0x1cfdcb,-0xd5)],this[_0x4767c4(-_0x23a773._0x534ada,-_0x23a773._0x18971d)]);},'_setStyles':function(_0x44fc31){var _0x3f4717={_0x58b4c1:0x296};function _0x4ae2a4(_0x63ab9c,_0x15acf2){return _0x44a8(_0x15acf2-_0x3f4717._0x58b4c1,_0x63ab9c);}this['_blur']=_0x44fc31[_0x4ae2a4(0x445,0x42a)]==0x0?0x0:_0x44fc31[_0x4ae2a4(0x447,_0x157eb4._0x1ac6a8)]||_0x44fc31['defaultBlur'];_0x44fc31[_0x6d2345(_0x157eb4._0x59c42c,0x2cf)]&&(this[_0x6d2345(_0x157eb4._0x8da177,0x31b)][_0x4ae2a4(0x415,0x475)]['backgroundColor']=_0x44fc31['backgroundColor']);this[_0x6d2345(_0x157eb4._0x4400e7,0x319)]=this['canvas'][_0x4ae2a4(0x423,_0x157eb4._0x24e051)]=this['shadowCanvas']['width']=_0x44fc31['width']||this[_0x6d2345(0x327,_0x157eb4._0x120330)];function _0x6d2345(_0x11537e,_0x5b5b1c){return _0x44a8(_0x5b5b1c-0x18b,_0x11537e);}this[_0x6d2345(0x34a,0x34a)]=this['canvas']['height']=this['shadowCanvas']['height']=_0x44fc31['height']||this['_height'],this[_0x4ae2a4(0x402,0x45f)]=(_0x44fc31[_0x6d2345(0x37a,0x331)]||0x0)*0xff,this['_maxOpacity']=(_0x44fc31[_0x6d2345(0x319,0x35b)]||_0x44fc31[_0x4ae2a4(0x406,_0x157eb4._0x2bd697)])*0xff,this[_0x6d2345(0x325,_0x157eb4._0x2c7cb9)]=(_0x44fc31['minOpacity']||_0x44fc31['defaultMinOpacity'])*0xff,this[_0x4ae2a4(_0x157eb4._0x482158,0x44e)]=!!_0x44fc31['useGradientOpacity'];},'_drawAlpha':function(_0x770cbd){var _0x349735=this[_0x50c8e6(0x253,0x25a)]=_0x770cbd['min'],_0x413d65=this['_max']=_0x770cbd['max'],_0x770cbd=_0x770cbd[_0x50c8e6(0x233,0x224)]||[],_0x4ad1aa=_0x770cbd['length'];function _0x4a1993(_0x478de6,_0x3a8b9c){return _0x44a8(_0x3a8b9c- -0x2c0,_0x478de6);}var _0x52ddeb=0x1-this['_blur'];function _0x50c8e6(_0x3acae6,_0x4e8ca4){return _0x44a8(_0x3acae6-0xed,_0x4e8ca4);}while(_0x4ad1aa--){var _0x4d3c7e=_0x770cbd[_0x4ad1aa],_0x2a5608=_0x4d3c7e['x'],_0x7d9dd2=_0x4d3c7e['y'],_0x1215bf=_0x4d3c7e[_0x4a1993(-_0x43fab3._0x55b4e7,-0x172)],_0x161c52=Math['min'](_0x4d3c7e['value'],_0x413d65),_0x29f24b=_0x2a5608-_0x1215bf,_0xc2fc69=_0x7d9dd2-_0x1215bf,_0x9b7420=this[_0x4a1993(-0xec,-0x126)],_0x261c33;!this['_templates'][_0x1215bf]?this['_templates'][_0x1215bf]=_0x261c33=_0x5aafda(_0x1215bf,_0x52ddeb):_0x261c33=this['_templates'][_0x1215bf];var _0x18cee5=(_0x161c52-_0x349735)/(_0x413d65-_0x349735);_0x9b7420['globalAlpha']=_0x18cee5<0.01?0.01:_0x18cee5,_0x9b7420['drawImage'](_0x261c33,_0x29f24b,_0xc2fc69),_0x29f24b<this['_renderBoundaries'][0x0]&&(this['_renderBoundaries'][0x0]=_0x29f24b),_0xc2fc69<this['_renderBoundaries'][0x1]&&(this['_renderBoundaries'][0x1]=_0xc2fc69),_0x29f24b+0x2*_0x1215bf>this[_0x50c8e6(_0x43fab3._0x4872bd,0x26e)][0x2]&&(this[_0x50c8e6(_0x43fab3._0x4872bd,0x1e8)][0x2]=_0x29f24b+0x2*_0x1215bf),_0xc2fc69+0x2*_0x1215bf>this['_renderBoundaries'][0x3]&&(this[_0x50c8e6(_0x43fab3._0x3b52d0,0x274)][0x3]=_0xc2fc69+0x2*_0x1215bf);}},'_colorize':function(){var _0x56452a=this[_0x3efa36(0x46b,_0x5c4a5f._0x1ddd23)][0x0],_0x209419=this[_0x37f313(0x24,_0x5c4a5f._0x597cfe)][0x1],_0x3f1b9b=this[_0x3efa36(_0x5c4a5f._0x4db8c1,0x482)][0x2]-_0x56452a,_0x17c223=this[_0x3efa36(_0x5c4a5f._0x4db8c1,_0x5c4a5f._0x3e9b5a)][0x3]-_0x209419;function _0x3efa36(_0x3efdca,_0xe28491){return _0x44a8(_0x3efdca-_0x3975ca._0x13e558,_0xe28491);}var _0x4a1845=this[_0x37f313(0x58,_0x5c4a5f._0x43baa1)],_0x5f5414=this['_height'],_0x4d1df9=this[_0x37f313(0x93,0x79)],_0x20139e=this['_maxOpacity'],_0x2b4efe=this['_minOpacity'],_0x32df48=this[_0x37f313(_0x5c4a5f._0x4c19f4,0x2a)];_0x56452a<0x0&&(_0x56452a=0x0);_0x209419<0x0&&(_0x209419=0x0);_0x56452a+_0x3f1b9b>_0x4a1845&&(_0x3f1b9b=_0x4a1845-_0x56452a);_0x209419+_0x17c223>_0x5f5414&&(_0x17c223=_0x5f5414-_0x209419);var _0x2f0d0f=this['shadowCtx'][_0x3efa36(0x4ea,0x538)](_0x56452a,_0x209419,_0x3f1b9b,_0x17c223),_0x4e6f70=_0x2f0d0f[_0x3efa36(0x457,0x4a2)],_0x1aeffc=_0x4e6f70['length'],_0x11a01b=this[_0x37f313(0x38,_0x5c4a5f._0xffeda3)];for(var _0xb21a39=0x3;_0xb21a39<_0x1aeffc;_0xb21a39+=0x4){var _0x2ca04e=_0x4e6f70[_0xb21a39],_0x2016eb=_0x2ca04e*0x4;if(!_0x2016eb)continue;var _0x11bd05;_0x4d1df9>0x0?_0x11bd05=_0x4d1df9:_0x2ca04e<_0x20139e?_0x2ca04e<_0x2b4efe?_0x11bd05=_0x2b4efe:_0x11bd05=_0x2ca04e:_0x11bd05=_0x20139e,_0x4e6f70[_0xb21a39-0x3]=_0x11a01b[_0x2016eb],_0x4e6f70[_0xb21a39-0x2]=_0x11a01b[_0x2016eb+0x1],_0x4e6f70[_0xb21a39-0x1]=_0x11a01b[_0x2016eb+0x2],_0x4e6f70[_0xb21a39]=_0x32df48?_0x11a01b[_0x2016eb+0x3]:_0x11bd05;}this[_0x3efa36(_0x5c4a5f._0x54d2b5,0x4b3)][_0x3efa36(0x501,0x4d3)](_0x2f0d0f,_0x56452a,_0x209419);function _0x37f313(_0x5d6f56,_0x397b7b){return _0x44a8(_0x5d6f56- -0x136,_0x397b7b);}this['_renderBoundaries']=[0x3e8,0x3e8,0x0,0x0];},'getValueAt':function(_0xbfeb3f){var _0x35c31d;function _0x577b00(_0x57bd95,_0x4c135e){return _0x44a8(_0x4c135e-_0x1e6213._0x41c21e,_0x57bd95);}var _0x3722da=this['shadowCtx'],_0x322cda=_0x3722da['getImageData'](_0xbfeb3f['x'],_0xbfeb3f['y'],0x1,0x1),_0xe4f848=_0x322cda['data'][0x3],_0x573c99=this[_0x577b00(_0x5c0fe5._0x22e939,0x442)],_0x50e8f6=this['_min'];return _0x35c31d=Math['abs'](_0x573c99-_0x50e8f6)*(_0xe4f848/0xff)>>0x0,_0x35c31d;},'getDataURL':function(){return this['canvas']['toDataURL']();}},_0xee56d5;}(),_0x4d913a=function _0x5a2270(){var _0xf9899a=![];_0x4bfacc['defaultRenderer']===_0x552041(0x16f,_0x55d222._0x5c7d7a)&&(_0xf9899a=_0x3cb2db);function _0x552041(_0x97a4ad,_0x33e63f){return _0x10ad77(_0x97a4ad,_0x33e63f- -0x1cf);}return _0xf9899a;}(),_0x5d215f={};_0x5d215f['merge']=function(){var _0x5427ea={},_0xaa2e6=arguments['length'];for(var _0x4f477a=0x0;_0x4f477a<_0xaa2e6;_0x4f477a++){var _0x10a755=arguments[_0x4f477a];for(var _0x26bd3c in _0x10a755){_0x5427ea[_0x26bd3c]=_0x10a755[_0x26bd3c];}}return _0x5427ea;};var _0x359043=_0x5d215f,_0x218747=function _0x5328e6(){var _0x3f7ec7={_0x1c054e:0x40c},_0x2d64c1={_0x4d0223:0x183},_0x53db7f={_0x713b49:0x3f1},_0x21cde4={_0x1b6bcc:0xf2},_0x19e2e0={_0x2e60bf:0x17},_0x33af4c={_0x99678c:0xc0,_0x1fc6f6:0x22},_0x3ffff5={_0x27c441:0x340},_0xb1e7b7={_0x428248:0x3e8},_0x1d959a={_0x5af7ae:0x227,_0x300507:0xb},_0x2e2fd2={_0x5917a4:0xb7};function _0xdd0c14(_0x3466d2,_0x122ec8){return _0x2bb87f(_0x3466d2-0xca,_0x122ec8);}var _0x891996=function _0x3f93dd(){var _0x353423={_0x52764d:0x351},_0x17d200={_0xb4fe26:0x21f};function _0x2c2050(){function _0x58894a(_0x3ec714,_0x5ee767){return _0x44a8(_0x3ec714- -_0x17d200._0xb4fe26,_0x5ee767);}this[_0x58894a(-0xa1,-_0x2e2fd2._0x5917a4)]={};}return _0x2c2050['prototype']={'on':function(_0x2a9f95,_0x8316af,_0x4cfa63){function _0x2d1364(_0x6f3f2b,_0x29bc04){return _0x44a8(_0x6f3f2b-_0x353423._0x52764d,_0x29bc04);}var _0x27df07=this[_0x2d1364(0x4cf,0x4ca)];!_0x27df07[_0x2a9f95]&&(_0x27df07[_0x2a9f95]=[]),_0x27df07[_0x2a9f95]['push'](function(_0x4157ab){return _0x8316af['call'](_0x4cfa63,_0x4157ab);});},'emit':function(_0x39916a,_0x2f70a6){var _0x588e00=this['cStore'];if(_0x588e00[_0x39916a]){var _0x4fc80e=_0x588e00[_0x39916a]['length'];for(var _0x268a0a=0x0;_0x268a0a<_0x4fc80e;_0x268a0a++){var _0x335912=_0x588e00[_0x39916a][_0x268a0a];_0x335912(_0x2f70a6);}}}},_0x2c2050;}(),_0x5bb2e=function(_0x416de8){var _0x494543=_0x416de8['_renderer'];function _0x1cefad(_0x6597d6,_0x15cbfb){return _0x44a8(_0x15cbfb-0x240,_0x6597d6);}var _0x53bde3=_0x416de8['_coordinator'],_0x3b0366=_0x416de8['_store'];_0x53bde3['on']('renderpartial',_0x494543['renderPartial'],_0x494543),_0x53bde3['on']('renderall',_0x494543[_0x1cefad(_0xb1e7b7._0x428248,0x407)],_0x494543),_0x53bde3['on']('extremachange',function(_0x2e6355){function _0x42c171(_0x847be1,_0x53efc0){return _0x1cefad(_0x53efc0,_0x847be1- -0x1e4);}function _0x512b1a(_0x12de92,_0x49d69b){return _0x1cefad(_0x49d69b,_0x12de92- -0x3ac);}_0x416de8[_0x512b1a(0x14,0x5c)]['onExtremaChange']&&_0x416de8[_0x512b1a(0x14,0x3d)]['onExtremaChange']({'min':_0x2e6355['min'],'max':_0x2e6355[_0x42c171(_0x1d959a._0x5af7ae,0x21a)],'gradient':_0x416de8['_config'][_0x512b1a(_0x1d959a._0x300507,0x9)]||_0x416de8['_config']['defaultGradient']});}),_0x3b0366['setCoordinator'](_0x53bde3);};function _0xe848b1(){var _0x3dea40=this['_config']=_0x359043[_0x451fef(0x3e4,0x39d)](_0x4bfacc,arguments[0x0]||{});this['_coordinator']=new _0x891996();if(_0x3dea40['plugin']){var _0x51b23c=_0x3dea40[_0x451fef(0x3c4,0x3d3)];if(!_0x4bfacc['plugins'][_0x51b23c])throw new Error('Plugin\x20\x27'+_0x51b23c+_0x3077e4(0xe,-0xc));else{var _0x5c0cb1=_0x4bfacc['plugins'][_0x51b23c];this['_renderer']=new _0x5c0cb1[(_0x451fef(0x362,0x393))](_0x3dea40),this['_store']=new _0x5c0cb1['store'](_0x3dea40);}}else this[_0x451fef(0x38b,_0x3ffff5._0x27c441)]=new _0x4d913a(_0x3dea40),this['_store']=new _0x28c36e(_0x3dea40);function _0x451fef(_0x2b3461,_0x27efc8){return _0x44a8(_0x27efc8-0x201,_0x2b3461);}function _0x3077e4(_0x37b097,_0x1ca27c){return _0x44a8(_0x1ca27c- -0x147,_0x37b097);}_0x5bb2e(this);}return _0xe848b1[_0xdd0c14(0x3a5,0x3d2)]={'addData':function(){function _0x5e3145(_0x182a2f,_0x4ebbdf){return _0xdd0c14(_0x4ebbdf- -0x2ac,_0x182a2f);}function _0xf7f913(_0x8910ab,_0x2cf266){return _0xdd0c14(_0x2cf266- -0x366,_0x8910ab);}return this['_store'][_0x5e3145(0xc2,_0x33af4c._0x99678c)]['apply'](this[_0xf7f913(-0x12,_0x33af4c._0x1fc6f6)],arguments),this;},'removeData':function(){function _0x5ab729(_0x25f3d0,_0x40d4bb){return _0xdd0c14(_0x40d4bb- -0x2b1,_0x25f3d0);}function _0x3ba469(_0x1491d0,_0x43a551){return _0xdd0c14(_0x43a551- -0x3db,_0x1491d0);}return this['_store'][_0x3ba469(-0x27,-_0x19e2e0._0x2e60bf)]&&this['_store'][_0x3ba469(-0x40,-_0x19e2e0._0x2e60bf)][_0x3ba469(-0x49,-0x90)](this['_store'],arguments),this;},'setData':function(){return this['_store']['setData']['apply'](this['_store'],arguments),this;},'setDataMax':function(){return this['_store']['setDataMax']['apply'](this['_store'],arguments),this;},'setDataMin':function(){this['_store']['setDataMin']['apply'](this[_0x46e369(-0xb9,-_0x21cde4._0x1b6bcc)],arguments);function _0x46e369(_0x595ccc,_0x451e94){return _0xdd0c14(_0x595ccc- -0x441,_0x451e94);}return this;},'configure':function(_0x553183){this['_config']=_0x359043[_0x3c3670(0x168,0x17f)](this['_config'],_0x553183),this['_renderer']['updateConfig'](this['_config']);function _0x25bc41(_0x8b9c75,_0x333133){return _0xdd0c14(_0x8b9c75- -_0x53db7f._0x713b49,_0x333133);}function _0x3c3670(_0x1fb479,_0xb74cdd){return _0xdd0c14(_0x1fb479- -0x22b,_0xb74cdd);}return this[_0x3c3670(_0x2d64c1._0x4d0223,0x173)]['emit']('renderall',this['_store'][_0x3c3670(0x1b2,0x1c2)]()),this;},'repaint':function(){var _0x284c8f={_0x11c84a:0x335};function _0x590062(_0x54bb89,_0x317079){return _0xdd0c14(_0x317079- -_0x284c8f._0x11c84a,_0x54bb89);}this['_coordinator'][_0x590062(_0x39b898._0x5e5c88,0x9e)]('renderall',this[_0x590062(0x7,0x53)][_0x590062(0xe1,0xa8)]());function _0x115cff(_0x62b33c,_0xf64092){return _0xdd0c14(_0x62b33c-0x187,_0xf64092);}return this;},'getData':function(){function _0x53e690(_0x1837f5,_0x232891){return _0xdd0c14(_0x1837f5- -_0x3f7ec7._0x1c054e,_0x232891);}function _0x3ea67b(_0x2b9656,_0x152ccd){return _0xdd0c14(_0x2b9656- -0x301,_0x152ccd);}return this[_0x53e690(-0x84,-0xc8)][_0x3ea67b(_0x5b25f2._0x548988,0xa7)]();},'getDataURL':function(){return this['_renderer']['getDataURL']();},'getValueAt':function(_0xaaecde){function _0x3b003b(_0x3e5c5c,_0x971b13){return _0xdd0c14(_0x971b13- -0x416,_0x3e5c5c);}function _0x5dda0c(_0x3c9c30,_0x3f4969){return _0xdd0c14(_0x3f4969- -0x59c,_0x3c9c30);}if(this['_store']['getValueAt'])return this['_store'][_0x3b003b(-0xcf,-0xc2)](_0xaaecde);else return this['_renderer']['getValueAt']?this[_0x3b003b(-_0x232c4f._0x30b95c,-_0x232c4f._0x5455dc)]['getValueAt'](_0xaaecde):null;}},_0xe848b1;}(),_0x406a79={'create':function(_0x2c2e0f){return new _0x218747(_0x2c2e0f);},'register':function(_0x202fb7,_0x1d5e22){_0x4bfacc['plugins'][_0x202fb7]=_0x1d5e22;}};function _0x2bb87f(_0x69fc9d,_0x1033df){return _0x1294ac(_0x69fc9d-_0x1c88b9._0x5cc914,_0x1033df);}return _0x406a79;}));}(heatmap$1));var heatmap=heatmap$1['exports'],_0x40d8d8={};_0x40d8d8['__proto__']=null,_0x40d8d8[_0x13a5eb(0x31d,0x312)]=heatmap;var h337=_mergeNamespaces(_0x40d8d8,[heatmap$1['exports']]),HeatMaterial=_0x13a5eb(0x233,0x28e);if(!heatmap$1[_0x13a5eb(0x262,0x2b8)][_0x13a5eb(0x23c,0x28f)])throw new Error(_0x13a5eb(0x29f,0x29d));const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace[_0xdbdd05(-0x1e8,-0x217)]['BaseLayer'];var _0x5726e3={};_0x5726e3['0.4']='blue',_0x5726e3['0.6']=_0xdbdd05(-0x1d4,-0x216),_0x5726e3[_0xdbdd05(-0x1fe,-0x1e4)]='yellow',_0x5726e3['0.9']='red';var _0x3e43ee={};function _0x44a8(_0x474a59,_0x1f8a97){var _0xdc04c5=_0xdc04();return _0x44a8=function(_0x44a83c,_0x4ecfa9){_0x44a83c=_0x44a83c-0x12f;var _0x128684=_0xdc04c5[_0x44a83c];if(_0x44a8['zmHfoP']===undefined){var _0x51d2a6=function(_0x3a6b1a){var _0x12b2b1='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';var _0x5a3241='',_0x47bfbd='';for(var _0x5f0c7e=0x0,_0x17d25e,_0x42de9d,_0x9fbea=0x0;_0x42de9d=_0x3a6b1a['charAt'](_0x9fbea++);~_0x42de9d&&(_0x17d25e=_0x5f0c7e%0x4?_0x17d25e*0x40+_0x42de9d:_0x42de9d,_0x5f0c7e++%0x4)?_0x5a3241+=String['fromCharCode'](0xff&_0x17d25e>>(-0x2*_0x5f0c7e&0x6)):0x0){_0x42de9d=_0x12b2b1['indexOf'](_0x42de9d);}for(var _0x225d12=0x0,_0x559ce9=_0x5a3241['length'];_0x225d12<_0x559ce9;_0x225d12++){_0x47bfbd+='%'+('00'+_0x5a3241['charCodeAt'](_0x225d12)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0x47bfbd);};_0x44a8['KDPEGN']=_0x51d2a6,_0x474a59=arguments,_0x44a8['zmHfoP']=!![];}var _0xef9402=_0xdc04c5[0x0],_0x5f2ca9=_0x44a83c+_0xef9402,_0x70e9d2=_0x474a59[_0x5f2ca9];return!_0x70e9d2?(_0x128684=_0x44a8['KDPEGN'](_0x128684),_0x474a59[_0x5f2ca9]=_0x128684):_0x128684=_0x70e9d2,_0x128684;},_0x44a8(_0x474a59,_0x1f8a97);}_0x3e43ee['maxOpacity']=0.8,_0x3e43ee['minOpacity']=0.1,_0x3e43ee[_0x13a5eb(0x2db,0x2e9)]=0.85,_0x3e43ee['radius']=0x19,_0x3e43ee[_0xdbdd05(-0x20e,-0x200)]=_0x5726e3;const DEF_HEATSTYLE=_0x3e43ee;var _0xf77335={};_0xf77335[_0x13a5eb(0x282,0x2a1)]=1.5,_0xf77335['arcBlurScale']=1.5;function _0x13a5eb(_0xe6bb62,_0x5ba440){return _0x44a8(_0x5ba440-0x155,_0xe6bb62);}function _0xdc04(){var _0x31ef52=['zw1PDa','y2fSBa','yM9KEq','C3r5Bgu','BgvUz3rO','mZKWmJK0zePZqNj0','DMfSDwvdB2X1Bw4','zgLMzLG','ChG7zgLZCgXHEtPUB25LoW','CMDIkde0mcWXndaSmtqWkq','x2DLDeLUDgvYBMfSrgf0yq','zgvMyxvSDfLgAwvSza','y2XHC3noyw1L','Aw1Hz2u','zMLSBfn0EwXL','AdmZnW','x2DYyxbOAwm','quXxqvLt','C3rYAw5N','zgLMzKHLAwDODa','Chv0sw1Hz2veyxrH','Cg9ZAxrPB25Z','zgLMzLK','ntmZmJiYogfHrNLwAq','CMvUzgvYywXS','yxr0CG','mc44nq','z2v0q29UDgv4Da','Eg1HEa','x3nOB3DiB29R','x2nYzwf0zufYy0DYyxbOAwm','CMvTB3zL','Dw5PzM9YBsbZyw1WBgvYmKqGAw1Hz2u7cGPJEM1FBwf0zxjPywWGy3PTx2DLDe1HDgvYAwfSkgn6Bv9TyxrLCMLHBeLUChv0ig1HDgvYAwfSsw5WDxqPihSkicbJEM1FBwf0zxjPywWGBwf0zxjPywWGpsbJEM1Fz2v0rgvMyxvSDe1HDgvYAwfSkg1HDgvYAwfSsw5WDxqPoWOGihzLyZiGC3qGpsbTyxrLCMLHBeLUChv0lNn0oWOGihzLyZqGy29SB3jjBwfNzsa9ihrLEhr1CMuOAw1Hz2uSihn0ktSkicbPzIHJB2XVCKLTywDLlNjNyIa9psb2zwmZkdeUmcKGFhWGy29SB3jjBwfNzs5Yz2iGpt0GDMvJmYGWlJaPksb7cIaGicbKAxnJyxjKoWOGih0kicbTyxrLCMLHBc5KAwzMDxnLid0Gy29SB3jjBwfNzs5Yz2i7cIaGBwf0zxjPywWUywXWAgeGpsbJB2XVCKLTywDLlMe7cIaGCMv0DxjUig1HDgvYAwfSoWP9cG','y3jLyxrL','jYbUB3qGzM91BMqUie1HEwjLigL0ihDHCYbUB3qGCMvNAxn0zxjLzc4','D2LKDgG','zMX5vg8','C2HHzg93q2fUDMfZ','x3jLBMrLCMvY','zgvMyxvSDeDYywrPzw50','x21HCa','ywrKr3jHCgHPyW','y3nZvgv4Da','yMfJA2DYB3vUzenVBg9Y','y2XPzw50v2LKDgG','zgf0yq','y2fTzxjHtw92zuvUza','6k+35BYv5ywLigHLyxrTyxaUANmG5BQtia','quXqsefFqKXftKq','CMDIysGWldaSmcWXkq','ChjPDMf0zq','yxjJuMfKAxvZu2nHBgu','x3jHzgK','CMfKAxvZ','y2XLyxjszwn0','y3jLyxrLuMfKAwfSr3jHzgLLBNq','y3jLyxrLrwXLBwvUDa','tg5Ntgf0ug9PBNq','vxrPBa','yxbWBhK','x3HgAwvSza','CMvKCMf3uMf0Aw8','x3vUt3jNyw5PEMveyxrH','AgvHDg1HCc1Jyw52yxm','x2jVDw5KCW','x3jLBMrLCKjVDw5KyxjPzxm','Dg9bCNjHEq','CMvWBgfJzq','z2v0vMfSDwvbDa','CMvJDgfUz2XL','ndq1otK1nw91D1Lnyq','x3bVC2L0Aw9UCW','zgvMyxvSDe1HEe9WywnPDhK','AgvPz2H0','zxHWB3j0CW','CMDIkdaSmcWYntuP','CMDIysGWldaSmcWWkq','x21PBG','AxngB3jTyxq','DMvYDgv4rM9YBwf0','z2XVyMu','twf0zxjPywXuExbL','B3b0Aw9UCW','z2v0rgf0yq','DxbKyxrLuMfKAxvZ','x3bHBgv0Dgu','Bg5N','x2rHDge','Ew1HEa','BwLU','zgvMAw5LuhjVCgvYDhK','y2fUDMfZmMq','ywrKrgf0yq','AxnbCNjHEq','z3jHzgLLBNq','B2zM','s0vfua','z3jHCgHPyW','y2XPzw50sgvPz2H0','m1rOAe1eqW','nZC4ndu2ogjbrfjUCG','y1n0B3jL','yNvTCe1HCa','x2nVBMzPzW','q29SB3i','yxjJrgLYzwn0Aw9U','u3rLBMnPBez1BMn0Aw9U','x2DLDeHLyxrdyw52yxm','x19LC01VzhvSzq','x2HLyxq','mc44','rwXSAxbZB2LKu3vYzMfJzufWCgvHCMfUy2u','AgvHDfn0EwXL','uMvJDgfUz2XLuhjPBwL0AxzL','y2fUDMfZu2L6zq','z3jHBNvSyxjPDhK','y3r4','x3DPzhrO','x21HEa','y2fUDMfZ','x3n0B3jL','CMvUzgvYzxi','x3jLy3rHBMDSzq','yMX1CG','x29Uq2fTzxjHtw92zuvUza','rxzLBNruExbL','zgL2','zgvMyxvSDe1PBK9WywnPDhK','Dw5KzwzPBMvK','C2HHzg93q3r4','u3rLBMnPBe9WzxjHDgLVBG','BwvYz2u','Bgf5zxi','x3vWzgf0zuDYywrPzw50','zgvMyxvSDfzHBhvLrMLLBgq','x2XHEwvY','CMvJDgfUz2XLugfKzgLUzW','x3vWzgf0zuDYyxbOAwm','zM9YBwf0tNvT','CgfYC2u','x2DYyxbOAwmY','B3bHy2L0Eq','y29UzMLNDxjL','x3nLDfn0EwXLCW','D2LSBfjLywrgCMvXDwvUDgX5','x29UrxH0CMvTyunOyw5Nzq','mtm1ntbPwwPJyNm','mtyXCMftwgPs','twf0zxjPywW','ChjVDg90ExbL','A2v5CW','y2XLyxi','z3jLzw4','twf0zxjPywXvDgLS','Bgf0','yMvNAw5qyxrO','CMvNAxn0zxi','zMLSBfjLy3q','x2nVB3jKAw5HDg9Y','x3vZzuDYywrPzw50t3bHy2L0Eq','DMfSDwu','CMvUzgvYCgfYDgLHBa','y29UDgfPBMvY','x3zHBhvLrMLLBgq','zgvMyxvSDa','C2nLBMu','x2HLAwDODa','zgvMAw5Lza','zw5HyMXLza','sgvHDeXHEwvY','qMXLBMrPBMDtDgf0zq','mta5mZi2odbOBuLAvfK','mJm0mZm0mwTeyNrsvq','x2nVBNrHAw5LCG','CMvUzgvYqwXS','twvHC3vYzvv0AwW','x29WywnPDhK','EuzPzwXK','Bwf4','CMvSyxrPDMu','CMvTB3zLrgf0yq','Cg9ZAxrPB24','zM9YrwfJAa','Bwf4t3bHy2L0Eq','x2DLDefYy0HLyxrdyw52yxm','CgX1z2LU','Dw5PzM9YBxm','Eg1PBG','x21PBK9WywnPDhK','mZCWEgfrywfW','rg9TvxrPBa','zNjVBurLz3jLzxm','z2v0sw1Hz2veyxrH','zgvMyxvSDfjHzgL1CW','CgLJA0vSBgLWC29Pza'];_0xdc04=function(){return _0x31ef52;};return _0xdc04();}_0xf77335[_0xdbdd05(-0x21d,-0x1cd)]=Cesium['EllipsoidSurfaceAppearance']['VERTEX_FORMAT'];const DEF_STYLE=_0xf77335;class HeatLayer extends BaseLayer{constructor(_0x527e47={}){var _0x581a5e={_0x40967c:0x1e1,_0x1ae0d6:0x1be,_0x40e37a:0x1e4};super(_0x527e47);function _0x490404(_0x3e0c94,_0x3098d2){return _0xdbdd05(_0x3e0c94-0x410,_0x3098d2);}this['options'][_0x490404(_0x581a5e._0x40967c,_0x581a5e._0x1ae0d6)]=this[_0x4f9583(_0x581a5e._0x40e37a,0x246)]['redrawRatio']||0x1,this['options'][_0x4f9583(0x253,0x264)]={...DEF_HEATSTYLE,...this[_0x4f9583(0x20a,0x246)]['heatStyle']};function _0x4f9583(_0x28f0b5,_0x465f16){return _0xdbdd05(_0x465f16-0x460,_0x28f0b5);}this['options']['style']={...DEF_STYLE,...this['options']['style']};}get[_0x13a5eb(0x33d,0x2f2)](){return this['_layer'];}get['heatStyle'](){var _0x2bb353={_0x4f77ce:0x109};function _0xf35f25(_0x185480,_0xa3250a){return _0xdbdd05(_0xa3250a-0x358,_0x185480);}return this['options'][_0xf35f25(_0x2bb353._0x4f77ce,0x15c)];}set[_0xdbdd05(-0x1fc,-0x1c1)](_0x5339f7){var _0x5d4920={_0x8dd6fe:0x4b6,_0x22b083:0x4e4,_0x1a3ce1:0x4b3,_0x4f14c5:0x1bb,_0x49c704:0x1cb,_0x496405:0x217,_0xf2642:0x261},_0x462c19={_0x510c03:0x1d8};function _0x2891c0(_0x3624b4,_0x3de896){return _0x13a5eb(_0x3624b4,_0x3de896-_0x462c19._0x510c03);}this[_0x2891c0(0x4e4,0x498)]['heatStyle']=mars3d__namespace['Util']['merge'](this[_0x2891c0(0x493,0x498)][_0x2891c0(0x4ea,_0x5d4920._0x8dd6fe)],_0x5339f7);function _0x4ace47(_0x5ad95f,_0x47ebb1){return _0xdbdd05(_0x47ebb1- -0x1b,_0x5ad95f);}if(this[_0x2891c0(0x50b,0x4b3)]){this[_0x2891c0(_0x5d4920._0x22b083,_0x5d4920._0x1a3ce1)][_0x4ace47(-_0x5d4920._0x4f14c5,-0x1f9)](this['options'][_0x4ace47(-_0x5d4920._0x49c704,-_0x5d4920._0x496405)]);const _0x13e36f=getCanvas(this['_heat'][_0x4ace47(-0x268,-_0x5d4920._0xf2642)][_0x4ace47(-0x20e,-0x210)]);this['_updateGraphic'](_0x13e36f),_0x5339f7['radius']&&this['updateRadius'](_0x5339f7['radius']);}}get['style'](){return this['options']['style'];}set[_0xdbdd05(-0x1a6,-0x17f)](_0x51aa72){var _0x5b6cfb={_0x5eae0d:0x156},_0x437b95={_0x4d9fc2:0x1a9};function _0x3715cb(_0x1ccf59,_0x3799b6){return _0x13a5eb(_0x3799b6,_0x1ccf59-_0x437b95._0x4d9fc2);}function _0x48e54b(_0x522959,_0x4f537e){return _0xdbdd05(_0x522959-0xac,_0x4f537e);}this[_0x3715cb(0x469,0x482)][_0x3715cb(0x4dd,0x538)]=mars3d__namespace[_0x48e54b(-0x186,-_0x5b6cfb._0x5eae0d)]['merge'](this[_0x3715cb(0x469,0x40f)]['style'],_0x51aa72);}get['positions'](){return this['_positions'];}set['positions'](_0x194b77){this['setPositions'](_0x194b77);}get['coordinates'](){const _0x36d281=[];return this['points']['forEach'](_0x24e0d9=>{function _0x4fd7ae(_0x513c08,_0x122e3a){return _0x44a8(_0x513c08- -0xf7,_0x122e3a);}_0x36d281['push'](_0x24e0d9[_0x4fd7ae(0x64,0xb9)]());}),_0x36d281;}get[_0xdbdd05(-0x227,-0x1e6)](){return this['_rectangle'];}['_setOptionsHook'](_0x3dad0b,_0x4d8301){var _0x27a3e4={_0x52a32d:0x97,_0x4eac8c:0x221,_0x3b4b76:0xb4,_0x1e55fd:0xd2,_0x46f67c:0xae,_0x905f6d:0xba,_0x1a163f:0x19d,_0x188373:0x8b},_0x4c1b96={_0x42a011:0x511};if(this[_0x4ffaf9(-0x9a,-0xe7)]){_0x4d8301['heatStyle']&&(_0x3dad0b[_0x46dc84(-0x254,-0x233)]=mars3d__namespace['Util'][_0x4ffaf9(-0x84,-0x54)](_0x3dad0b['heatStyle'],_0x4d8301[_0x4ffaf9(-_0x27a3e4._0x52a32d,-0xc5)]),this['_heat'][_0x46dc84(-0x22f,-0x215)](_0x3dad0b[_0x46dc84(-_0x27a3e4._0x4eac8c,-0x233)]));if(!_0x4d8301['positions']){var _0x52376e;const _0x4d9fea=this['_heat'][_0x4ffaf9(-_0x27a3e4._0x3b4b76,-0x7a)]();if((_0x52376e=_0x4d8301['heatStyle'])!==null&&_0x52376e!==void 0x0&&_0x52376e[_0x4ffaf9(-0xd2,-0x115)]){const _0x226dc6=_0x4d8301['heatStyle']['radius'];if(_0x4d9fea!==null&&_0x4d9fea!==void 0x0&&_0x4d9fea[_0x4ffaf9(-0xda,-0xc3)])for(const _0x5c19e1 in _0x4d9fea[_0x4ffaf9(-0xda,-_0x27a3e4._0x1e55fd)]){const _0xedfbe3=_0x4d9fea['data'][_0x5c19e1];_0xedfbe3[_0x46dc84(-0x258,-0x26e)]=_0x226dc6;}}Cesium['defined'](_0x4d8301['min'])&&(_0x4d9fea['min']=_0x4d8301[_0x4ffaf9(-_0x27a3e4._0x46f67c,-0xa1)]);Cesium[_0x4ffaf9(-0x60,-_0x27a3e4._0x905f6d)](_0x4d8301[_0x46dc84(-_0x27a3e4._0x1a163f,-0x1f1)])&&(_0x4d9fea['max']=_0x4d8301[_0x4ffaf9(-0x55,-0x76)]);this['_heat']['setData'](_0x4d9fea);const _0x249b4e=getCanvas(this['_heat']['_renderer'][_0x4ffaf9(-0x90,-_0x27a3e4._0x188373)]);this[_0x4ffaf9(-0x7e,-0x64)](_0x249b4e);}}function _0x46dc84(_0x3b07f9,_0x306948){return _0x13a5eb(_0x3b07f9,_0x306948- -_0x4c1b96._0x42a011);}function _0x4ffaf9(_0x87bb97,_0x307cfa){return _0xdbdd05(_0x87bb97-0x165,_0x307cfa);}_0x4d8301[_0x46dc84(-0x194,-0x1cb)]&&(this['positions']=_0x4d8301['positions']);}['_mountedHook'](){var _0x48dba6={_0x5f5801:0x3f7,_0x5c7746:0x3de,_0x37fb2a:0x418},_0x42fc1e={_0x4c4eaf:0x59d};function _0x32858a(_0x392542,_0x28c33c){return _0xdbdd05(_0x392542-_0x42fc1e._0x4c4eaf,_0x28c33c);}function _0x31fce6(_0xa56370,_0xd0d17c){return _0x13a5eb(_0xd0d17c,_0xa56370-0x13e);}if(this[_0x32858a(_0x48dba6._0x5f5801,0x44b)]['type']==='image'){var _0x349087={};_0x349087['crs']='EPSG:3857',_0x349087['private']=!![],this['_layer']=new mars3d__namespace['layer']['ImageLayer'](_0x349087);}else{var _0x25d288={};_0x25d288[_0x31fce6(_0x48dba6._0x5c7746,0x427)]=!![],this['_layer']=new mars3d__namespace[(_0x31fce6(0x430,_0x48dba6._0x37fb2a))]['GraphicLayer'](_0x25d288);}}['_addedHook'](){var _0x1c162a={_0x516d3a:0x476,_0x2bf362:0x3ea,_0x5e43b4:0x2fe,_0x2e3834:0x47c,_0x27558e:0x442,_0x3deac4:0x4a1,_0x40c1c1:0x405,_0xfdfb98:0x2d8,_0xc927f7:0x3ed},_0x664d2c={_0x46a675:0x635},_0x4d830a={_0x51f110:0x4c8};this['_map']['addLayer'](this['_layer']);function _0x518b17(_0x586da5,_0x5e086c){return _0xdbdd05(_0x586da5-_0x4d830a._0x51f110,_0x5e086c);}this[_0x39d534(0x429,_0x1c162a._0x516d3a)]=mars3d__namespace['DomUtil'][_0x39d534(0x393,_0x1c162a._0x2bf362)](_0x518b17(0x2da,0x2a3),'mars3d-heatmap\x20mars3d-hideDiv',this[_0x518b17(0x284,0x2b4)][_0x518b17(_0x1c162a._0x5e43b4,0x345)]);function _0x39d534(_0x2655ab,_0x5820fe){return _0xdbdd05(_0x5820fe-_0x664d2c._0x46a675,_0x2655ab);}this[_0x39d534(_0x1c162a._0x2e3834,0x41b)][_0x39d534(_0x1c162a._0x27558e,_0x1c162a._0x3deac4)]&&(this[_0x518b17(0x334,0x2d8)]=this['options']['positions']),this[_0x39d534(_0x1c162a._0x40c1c1,0x41b)]['redrawZoom']&&(this[_0x518b17(0x284,0x237)]['on'](mars3d__namespace['EventType']['cameraMoveEnd'],this[_0x518b17(_0x1c162a._0xfdfb98,0x28b)],this),this['_onCameraMoveEnd']()),this['options']['flyTo']&&this[_0x39d534(0x3da,_0x1c162a._0xc927f7)]();}['_removedHook'](){var _0x5ab912={_0x5e31e5:0xbc,_0x25a423:0x57,_0x41a3fc:0x44,_0x2fbbed:0xdf,_0x59af2a:0x255,_0x3805a2:0x2a6,_0x76df78:0x21e,_0x37f6bb:0x265},_0x346373={_0x14e425:0x245};function _0x1dab4b(_0x538fbe,_0x136657){return _0x13a5eb(_0x136657,_0x538fbe- -_0x346373._0x14e425);}this[_0x3ec4de(0x1e9,0x1c9)]['redrawZoom']&&this['_map'][_0x1dab4b(0x88,_0x5ab912._0x5e31e5)](mars3d__namespace[_0x3ec4de(0x214,0x204)][_0x1dab4b(_0x5ab912._0x25a423,_0x5ab912._0x41a3fc)],this[_0x1dab4b(0xa5,_0x5ab912._0x2fbbed)],this);this[_0x3ec4de(0x244,0x21a)]&&(mars3d__namespace[_0x3ec4de(_0x5ab912._0x59af2a,_0x5ab912._0x3805a2)][_0x1dab4b(0x48,0x3)](this[_0x3ec4de(0x244,0x25d)]),delete this['_container']);function _0x3ec4de(_0x3a6a83,_0x524950){return _0x13a5eb(_0x524950,_0x3a6a83- -0xd7);}this['clear'](),this['_map']['removeLayer'](this[_0x3ec4de(_0x5ab912._0x76df78,_0x5ab912._0x37f6bb)]);}[_0xdbdd05(-0x24f,-0x206)](_0x481f2e){_0x481f2e&&this['_updatePositionsHook']();}['addPosition'](_0x279267){this['_positions']=this['_positions']||[],this['_positions']['push'](_0x279267),this['_updatePositionsHook']();}['setPositions'](_0x232241){var _0x2181a8={_0x71d863:0x219},_0xe6ca61={_0x251577:0x4ea};this[_0x162eca(-_0x2181a8._0x71d863,-0x235)]=_0x232241;function _0x162eca(_0x11f7e8,_0x4eb967){return _0x13a5eb(_0x11f7e8,_0x4eb967- -_0xe6ca61._0x251577);}this['_updatePositionsHook']();}[_0xdbdd05(-0x1d5,-0x223)](){var _0x5a58c3={_0x39e74a:0x84};this['_graphic']&&(this[_0x210c0d(-0xab,-0x4e)]['removeGraphic'](this['_graphic'],!![]),delete this[_0x98a0d6(0x3a7,0x395)]);function _0x98a0d6(_0x26e930,_0x284ba6){return _0x13a5eb(_0x284ba6,_0x26e930-0x66);}function _0x210c0d(_0x16989b,_0x1ab4d3){return _0x13a5eb(_0x16989b,_0x1ab4d3- -0x343);}this['_graphic2']&&(this[_0x210c0d(-0x77,-0x4e)]['removeGraphic'](this[_0x210c0d(-_0x5a58c3._0x39e74a,-0x49)],!![]),delete this['_graphic2']);}['_updatePositionsHook'](){var _0xba26da={_0x3e2bdf:0x2fb};if(!this['show']||!this['_map']||!this[_0x188926(0x2e0,0x319)]||this['positions']['length']===0x0)return this;function _0x188926(_0x58be36,_0x4f73e0){return _0x13a5eb(_0x58be36,_0x4f73e0- -0x2d);}const _0x5255b2=this[_0x5eea3a(0x1,-0x22)]();this['_updateGraphic'](_0x5255b2);function _0x5eea3a(_0x477ad6,_0x56ea20){return _0x13a5eb(_0x477ad6,_0x56ea20- -_0xba26da._0x3e2bdf);}return this;}['getRectangle'](_0x3834b5){var _0xadb737={_0x1a7f66:0x109,_0x112dda:0x122,_0x351a36:0x10a},_0x394587={_0x306f17:0x2fb};function _0x30ac96(_0x5fed72,_0x4fe21d){return _0x13a5eb(_0x4fe21d,_0x5fed72- -0x1be);}function _0x3b9ad6(_0x5d7110,_0x427896){return _0xdbdd05(_0x5d7110-_0x394587._0x306f17,_0x427896);}return _0x3834b5!==null&&_0x3834b5!==void 0x0&&_0x3834b5[_0x30ac96(0xfe,0xd2)]&&this[_0x3b9ad6(_0xadb737._0x1a7f66,_0xadb737._0x112dda)]?mars3d__namespace['PolyUtil']['formatRectangle'](this[_0x3b9ad6(0x109,_0xadb737._0x351a36)]):this[_0x30ac96(0x12a,0x11b)];}['_onCameraMoveEnd'](){var _0x2b6377={_0x1f85cf:0x3b5,_0x49af97:0x3e5,_0x288efe:0x1d1,_0x1e5453:0x326};if(!this[_0x2fc457(0x38f,_0x2b6377._0x1f85cf)]||!this['show']||!this['_map'])return;function _0x2fc457(_0x18871f,_0x32ed60){return _0xdbdd05(_0x32ed60-0x5b4,_0x18871f);}let _0x47d1fc;const _0x4e3fc8=getSurfaceDistance(this['_map'][_0x2fc457(_0x2b6377._0x49af97,0x3ed)])/0x2;function _0x331f85(_0x23d45e,_0xa9a3c4){return _0x13a5eb(_0xa9a3c4,_0x23d45e- -0x47f);}if(_0x4e3fc8&&_0x4e3fc8<this[_0x331f85(-_0x2b6377._0x288efe,-0x1d4)]['radius']){const _0x1f652c=this[_0x331f85(-0x1bf,-0x1b4)]['redrawRatio']*_0x4e3fc8/this['_bounds'][_0x2fc457(_0x2b6377._0x1e5453,0x37d)];_0x47d1fc=this['heatStyle']['radius']*_0x1f652c,_0x47d1fc=Math['max'](_0x47d1fc,0x2);}else _0x47d1fc=this[_0x2fc457(0x3dc,0x3b8)]['radius'];_0x47d1fc&&this['updateRadius'](_0x47d1fc);}['_getBounds'](_0x1e82d7){var _0x15c213={_0x53da5b:0x148,_0x4f3022:0x2cc,_0x41f950:0x283,_0x5e8c35:0x134,_0x10293c:0x141,_0x576b50:0x1b3,_0x4d7168:0x16e,_0x3857e9:0x11c,_0x57e746:0x225,_0x482717:0xea,_0x402242:0x27f,_0x4d1659:0xff,_0x4b657b:0x1a8,_0x585f2a:0x2ad,_0x5964cf:0x278},_0x3e1af1={_0x2565f8:0x355},_0x2940d2={_0x3568a5:0x25,_0x1464cc:0x19,_0x253d0f:0x1f,_0x4c98a1:0x3,_0x18eda8:0x6d};let _0x4e4185,_0x127fe2,_0x4ec64d,_0x52779f;this[_0x42a3ed(0xe9,0x13b)]['rectangle']?(_0x4e4185=this['options']['rectangle']['xmin'],_0x127fe2=this['options'][_0x42a3ed(_0x15c213._0x53da5b,0x12e)][_0x42a3ed(0xe5,0x105)],_0x4ec64d=this['options'][_0x23a45f(-_0x15c213._0x4f3022,-_0x15c213._0x41f950)]['ymin'],_0x52779f=this[_0x42a3ed(0xfd,0x13b)][_0x42a3ed(_0x15c213._0x5e8c35,0x12e)][_0x42a3ed(0x115,_0x15c213._0x10293c)]):_0x1e82d7[_0x23a45f(-_0x15c213._0x576b50,-0x212)]((_0x500bed,_0x102539)=>{var _0x359dbf={_0x113400:0x282};function _0x48bb9f(_0x492b52,_0x266b90){return _0x42a3ed(_0x266b90,_0x492b52- -0x15e);}function _0x344cd9(_0x529b6c,_0x3b7848){return _0x42a3ed(_0x529b6c,_0x3b7848-_0x359dbf._0x113400);}_0x102539===0x0?(_0x4e4185=_0x500bed[_0x48bb9f(-0x1f,0x12)],_0x127fe2=_0x500bed['lng'],_0x4ec64d=_0x500bed['lat'],_0x52779f=_0x500bed[_0x48bb9f(_0x2940d2._0x3568a5,-_0x2940d2._0x1464cc)]):(_0x4e4185=Math['min'](_0x4e4185,_0x500bed['lng']),_0x127fe2=Math['max'](_0x127fe2,_0x500bed[_0x48bb9f(-_0x2940d2._0x253d0f,0x43)]),_0x4ec64d=Math[_0x48bb9f(-0x1c,-_0x2940d2._0x4c98a1)](_0x4ec64d,_0x500bed['lat']),_0x52779f=Math[_0x48bb9f(0x3d,_0x2940d2._0x18eda8)](_0x52779f,_0x500bed['lat']));});let _0x4c1d89=_0x127fe2-_0x4e4185,_0x3e273c=_0x52779f-_0x4ec64d;_0x4c1d89===0x0&&(_0x4c1d89=0x1);_0x3e273c===0x0&&(_0x3e273c=0x1);const _0x33e92d=this['options'][_0x42a3ed(_0x15c213._0x4d7168,0x171)]??0.2;!this[_0x23a45f(-0x254,-0x276)]['rectangle']&&(_0x4e4185-=_0x4c1d89*_0x33e92d,_0x4ec64d-=_0x3e273c*_0x33e92d,_0x127fe2+=_0x4c1d89*_0x33e92d,_0x52779f+=_0x3e273c*_0x33e92d);_0x4e4185=Math['max'](_0x4e4185,-0xb4),_0x127fe2=Math['min'](_0x127fe2,0xb4),_0x4ec64d=Math['max'](_0x4ec64d,-0x5a),_0x52779f=Math[_0x42a3ed(_0x15c213._0x3857e9,0x142)](_0x52779f,0x5a);var _0x2b7432={};_0x2b7432[_0x23a45f(-_0x15c213._0x57e746,-0x20d)]=_0x4e4185,_0x2b7432[_0x42a3ed(_0x15c213._0x482717,0x105)]=_0x127fe2,_0x2b7432['ymin']=_0x4ec64d,_0x2b7432[_0x23a45f(-_0x15c213._0x402242,-0x270)]=_0x52779f;const _0x415fac=_0x2b7432;_0x415fac[_0x42a3ed(0x1c4,_0x15c213._0x576b50)]=_0x127fe2-_0x4e4185,_0x415fac[_0x42a3ed(0x129,_0x15c213._0x4d1659)]=_0x52779f-_0x4ec64d;function _0x23a45f(_0x4bb827,_0xe1cb50){return _0xdbdd05(_0xe1cb50- -0x5c,_0x4bb827);}_0x415fac['rectangle']=Cesium['Rectangle'][_0x42a3ed(0x187,_0x15c213._0x4b657b)](_0x4e4185,_0x4ec64d,_0x127fe2,_0x52779f);const _0xfe6839=Math['max'](_0x415fac['rectangle'][_0x23a45f(-_0x15c213._0x585f2a,-0x27f)],_0x415fac['rectangle']['width']);function _0x42a3ed(_0x480561,_0x198d9e){return _0xdbdd05(_0x198d9e-_0x3e1af1._0x2565f8,_0x480561);}return _0x415fac[_0x23a45f(-0x257,-0x255)]=_0xfe6839,_0x415fac['radius']=Cesium['Math']['chordLength'](_0xfe6839,this['_map']['scene'][_0x23a45f(-0x29a,-_0x15c213._0x5964cf)]['ellipsoid']['maximumRadius'])/(0x1+0x2*_0x33e92d),_0x415fac;}[_0x13a5eb(0x294,0x2d9)](){var _0x141e9a={_0x3e22f2:0x285,_0x172e09:0x43d,_0x1b49c8:0x435,_0x15190b:0x3af,_0x24d540:0x3bf,_0x22b2eb:0x3f1,_0x15a132:0x3bc,_0x5e59dc:0x3b9,_0x2c7104:0x28c,_0x5babe8:0x41b,_0x4e3a3b:0x2b6,_0x437cd1:0x207,_0x1bc1fc:0x2a8,_0x3ae11f:0x292,_0x3e9a23:0x206,_0x4d0c6c:0x2a2,_0x272a9c:0x1c9,_0x58f7d4:0x3ef,_0x392986:0x42c,_0x488791:0x3a5,_0x1bb553:0x243,_0x10d317:0x25f,_0x355733:0x3ec,_0x171d11:0x45e,_0x32d022:0x298,_0x196f14:0x3f6,_0x556167:0x3ac,_0x42d48f:0x2a0},_0x2b5647={_0x5e8660:0x99,_0xffb08c:0x3aa,_0x144b9a:0x172,_0x27d0d2:0x123,_0x18afc0:0x380,_0x260a2e:0x10b,_0x219c6d:0x422,_0xad7562:0x3c3},_0x3cd32c={_0x1926e1:0x325},_0x203ca5={_0x725ab1:0x8d,_0x4019a9:0x6d,_0x2b599a:0x92,_0x58dc4f:0x1f3,_0x14166c:0x190,_0x54b445:0x1c9,_0x11adde:0x1de,_0x17597c:0x54,_0x4c7dfb:0x1de,_0x185815:0x1ac};const _0x50212e=this[_0x5c42f9(-0x246,-_0x141e9a._0x3e22f2)],_0x32a3da=[];_0x50212e[_0x17e371(_0x141e9a._0x172e09,_0x141e9a._0x1b49c8)](_0x4d3e09=>{let _0x5093bd;function _0x28dd7c(_0x1ac13d,_0x796952){return _0x17e371(_0x796952,_0x1ac13d- -0x241);}function _0x57ff0a(_0x165ace,_0x3f5244){return _0x17e371(_0x3f5244,_0x165ace- -0x425);}if(_0x4d3e09['position']&&_0x4d3e09[_0x57ff0a(-_0x203ca5._0x725ab1,-0x55)]){_0x5093bd=mars3d__namespace[_0x57ff0a(-_0x203ca5._0x4019a9,-_0x203ca5._0x2b599a)]['parse'](_0x4d3e09[_0x28dd7c(_0x203ca5._0x58dc4f,0x227)]);if(!_0x5093bd)return;_0x5093bd['value']=Number(_0x4d3e09[_0x28dd7c(0x157,0x192)][this[_0x28dd7c(_0x203ca5._0x14166c,0x184)][_0x28dd7c(0x207,0x23f)]||_0x57ff0a(-0x6,-0x51)]);}else{_0x5093bd=mars3d__namespace['LngLatPoint'][_0x28dd7c(_0x203ca5._0x54b445,0x224)](_0x4d3e09);if(!_0x5093bd)return;_0x5093bd[_0x28dd7c(_0x203ca5._0x11adde,0x1a3)]=Number(_0x4d3e09[this[_0x57ff0a(-_0x203ca5._0x17597c,-0x5b)]['valueColumn']||_0x28dd7c(_0x203ca5._0x4c7dfb,0x1dc)]);}(!_0x5093bd[_0x28dd7c(0x1de,_0x203ca5._0x185815)]||isNaN(_0x5093bd['value']))&&(_0x5093bd['value']=0x1),_0x32a3da['push'](_0x5093bd);}),this['_bounds']=this['_getBounds'](_0x32a3da),this['_rectangle']=this['_bounds']['rectangle'];let _0x605412,_0x29102d;this[_0x17e371(_0x141e9a._0x15190b,_0x141e9a._0x24d540)]['diffX']>this[_0x17e371(0x38c,0x3bf)]['diffY']?(_0x605412=this['options'][_0x17e371(0x442,_0x141e9a._0x22b2eb)]??document['body']['clientWidth'],_0x29102d=mars3d__namespace[_0x17e371(_0x141e9a._0x15a132,_0x141e9a._0x5e59dc)]['formatNum'](_0x605412/this[_0x5c42f9(-0x253,-_0x141e9a._0x2c7104)][_0x17e371(_0x141e9a._0x5babe8,0x449)]*this['_bounds'][_0x5c42f9(-0x2d7,-_0x141e9a._0x4e3a3b)])):(_0x29102d=this['options']['canvasSize']??document[_0x5c42f9(-0x1db,-_0x141e9a._0x437cd1)]['clientHeight'],_0x605412=mars3d__namespace[_0x5c42f9(-_0x141e9a._0x1bc1fc,-_0x141e9a._0x3ae11f)][_0x5c42f9(-0x269,-0x242)](_0x29102d/this['_bounds']['diffY']*this['_bounds']['diffX']));this['_canvasWidth']=_0x605412,this['_canvasHeight']=_0x29102d,this['_container'][_0x5c42f9(-0x233,-_0x141e9a._0x3e9a23)][_0x5c42f9(-0x2a8,-_0x141e9a._0x4d0c6c)]='width:'+_0x605412+'px;height:'+_0x29102d+_0x5c42f9(-_0x141e9a._0x272a9c,-0x201);var _0x5b7cde={...this[_0x17e371(0x3bf,_0x141e9a._0x58f7d4)]};_0x5b7cde['container']=this[_0x17e371(0x3df,_0x141e9a._0x392986)];const _0x54ebd7=_0x5b7cde;if(this['_heat']){this['_heat'][_0x5c42f9(-0x1e3,-0x218)]();var _0x46fbad={};_0x46fbad['width']=_0x605412,_0x46fbad['height']=_0x29102d,this['_heat'][_0x17e371(0x3a2,_0x141e9a._0x488791)]['updateConfig'](_0x46fbad),this[_0x5c42f9(-_0x141e9a._0x1bb553,-_0x141e9a._0x10d317)]['configure'](_0x54ebd7);}else this[_0x17e371(0x43b,_0x141e9a._0x355733)]=heatmap$1['exports']['create'](_0x54ebd7);let _0x331f5c=_0x32a3da[0x0]['value']??0x1,_0x40d891=_0x32a3da[0x0][_0x17e371(_0x141e9a._0x171d11,0x41f)]??0x0;function _0x5c42f9(_0x3a1774,_0x589633){return _0xdbdd05(_0x589633- -0x60,_0x3a1774);}const _0x52101b=[];_0x32a3da[_0x5c42f9(-0x205,-0x216)](_0x41b1d8=>{const _0x29abcf=Math['round']((_0x41b1d8[_0x5a55e0(0x3f0,0x3c0)]-this[_0x201b72(0xe2,_0x2b5647._0x5e8660)]['xmin'])/this[_0x5a55e0(0x38d,_0x2b5647._0xffb08c)][_0x201b72(_0x2b5647._0x144b9a,_0x2b5647._0x27d0d2)]*_0x605412),_0x7c348a=Math['round']((this['_bounds']['ymax']-_0x41b1d8['lat'])/this[_0x201b72(0xec,0x99)][_0x5a55e0(_0x2b5647._0x18afc0,0x380)]*_0x29102d),_0x5c2c8a=_0x41b1d8['value']||0x1;_0x331f5c=Math[_0x201b72(0x167,_0x2b5647._0x260a2e)](_0x331f5c,_0x5c2c8a);function _0x201b72(_0x4207af,_0x174ba0){return _0x5c42f9(_0x4207af,_0x174ba0-_0x3cd32c._0x1926e1);}_0x40d891=Math[_0x5a55e0(_0x2b5647._0x219c6d,_0x2b5647._0xad7562)](_0x40d891,_0x5c2c8a);var _0x54d217={};_0x54d217['x']=_0x29abcf,_0x54d217['y']=_0x7c348a,_0x54d217['value']=_0x5c2c8a;function _0x5a55e0(_0x298666,_0x3179d3){return _0x5c42f9(_0x298666,_0x3179d3-0x636);}_0x52101b['push'](_0x54d217);});var _0x313abd={};_0x313abd['min']=this['options']['min']??_0x40d891,_0x313abd['max']=this[_0x5c42f9(-_0x141e9a._0x32d022,-0x27a)]['max']??_0x331f5c,_0x313abd[_0x17e371(_0x141e9a._0x196f14,_0x141e9a._0x556167)]=_0x52101b;function _0x17e371(_0x29455c,_0x459b1d){return _0xdbdd05(_0x459b1d-0x5eb,_0x29455c);}const _0x329afe=_0x313abd;return this[_0x5c42f9(-_0x141e9a._0x42d48f,-_0x141e9a._0x10d317)]['setData'](_0x329afe),getCanvas(this['_heat']['_renderer']['canvas']);}['_getArcHeatCanvas'](){var _0x29224f={_0xc173e9:0x5e4,_0x4ce0aa:0x5b8,_0x41e81b:0x506,_0x57f8d9:0x551,_0x2b690f:0x559,_0x107f74:0x585},_0x557fb2={_0x3f57ab:0x758},_0x158a4={};function _0x453574(_0x188abd,_0x1d7d6c){return _0x13a5eb(_0x1d7d6c,_0x188abd- -0x3bf);}_0x158a4['0.25']='rgb(0,0,0)';function _0x27f03a(_0x49c556,_0x9db608){return _0xdbdd05(_0x9db608-_0x557fb2._0x3f57ab,_0x49c556);}_0x158a4['0.55']=_0x27f03a(_0x29224f._0xc173e9,_0x29224f._0x4ce0aa),_0x158a4[_0x27f03a(0x516,_0x29224f._0x41e81b)]='rgb(216,216,216)',_0x158a4['1']='rgb(255,255,255)',this['_heat']['configure']({'radius':this['heatStyle']['radius']*this['style'][_0x453574(-0x11e,-0x159)],'blur':this['heatStyle']['blur']*this['style']['arcBlurScale'],'gradient':this[_0x453574(-0xe1,-0xff)]['gradientArc']||_0x158a4});const _0x30979e=getCanvas(this[_0x27f03a(_0x29224f._0x57f8d9,_0x29224f._0x2b690f)]['_renderer']['canvas']);return this['_heat']['configure'](this[_0x27f03a(_0x29224f._0x107f74,0x53e)]['heatStyle']),_0x30979e;}[_0x13a5eb(0x310,0x2c2)](_0x5e1402){var _0x163226={_0x11eaa3:0x2b,_0x2a7043:0x6f,_0x5f5568:0x15,_0x2ae66a:0xa,_0x39db1e:0x3b0,_0xd9988:0x3c0,_0x5efbfa:0x382,_0x367fac:0x35,_0x8beeb5:0x47,_0x222ec7:0x40},_0x3f29d7={_0x490a85:0x5bf};function _0x577541(_0x772819,_0x168fb5){return _0xdbdd05(_0x168fb5-_0x3f29d7._0x490a85,_0x772819);}const _0x4dabcb=this[_0xf985df(_0x163226._0x11eaa3,_0x163226._0x2a7043)]['getData']();if(_0x4dabcb!==null&&_0x4dabcb!==void 0x0&&_0x4dabcb['data'])for(const _0x1f286a in _0x4dabcb[_0xf985df(-_0x163226._0x5f5568,_0x163226._0x2ae66a)]){const _0xa79451=_0x4dabcb['data'][_0x1f286a];_0xa79451['radius']=_0x5e1402;}this[_0x577541(_0x163226._0x39db1e,_0x163226._0xd9988)]['setData'](_0x4dabcb);const _0x2a7e49=getCanvas(this[_0x577541(_0x163226._0x5efbfa,0x3c0)][_0xf985df(-0x1c,-0x10)][_0xf985df(_0x163226._0x367fac,-0x2c)]);function _0xf985df(_0x2d9511,_0x3b05e9){return _0xdbdd05(_0x2d9511-0x22a,_0x3b05e9);}this[_0xf985df(_0x163226._0x8beeb5,_0x163226._0x222ec7)](_0x2a7e49);}['getPointData'](_0x100894){var _0x26f01f={_0xd26e1a:0x444,_0x3482bb:0x234,_0x4d4581:0x238,_0x47074c:0x439,_0x188642:0x456,_0x1ff294:0x404,_0x3f71d2:0x3df,_0x9d4b7d:0x434,_0x225400:0x49b,_0x3b1283:0x43f,_0x1cbf3d:0x427,_0x21cf36:0x26d,_0x3efe39:0x438,_0xc4c7b6:0x367,_0x3ec1aa:0x2d5};const _0x28d3b3=mars3d__namespace['LngLatPoint'][_0x2dfa34(0x42a,_0x26f01f._0xd26e1a)](_0x100894);if(!_0x28d3b3||!this['_bounds'])return{};if(_0x28d3b3['lng']<this[_0x3bb423(_0x26f01f._0x3482bb,0x255)]['xmin']||_0x28d3b3['lng']>this[_0x3bb423(_0x26f01f._0x4d4581,0x255)]['xmax']||_0x28d3b3['lat']<this['_bounds']['ymin']||_0x28d3b3[_0x2dfa34(_0x26f01f._0x47074c,_0x26f01f._0x188642)]>this['_bounds'][_0x2dfa34(0x3f7,_0x26f01f._0x1ff294)])return{};const _0x1dce83=(_0x28d3b3['lng']-this[_0x2dfa34(_0x26f01f._0x3f71d2,_0x26f01f._0x9d4b7d)][_0x2dfa34(0x45a,_0x26f01f._0x225400)])/(this[_0x2dfa34(0x3df,_0x26f01f._0x3b1283)]['xmax']-this['_bounds']['xmin'])*this['_canvasWidth'],_0x5cde6c=(this[_0x2dfa34(0x3df,_0x26f01f._0x1cbf3d)]['ymax']-_0x28d3b3['lat'])/(this['_bounds'][_0x3bb423(0x2ad,_0x26f01f._0x21cf36)]-this[_0x3bb423(0x20c,0x255)]['ymin'])*this['_canvasHeight'];function _0x3bb423(_0x197647,_0x2f41fa){return _0xdbdd05(_0x2f41fa-0x481,_0x197647);}function _0x2dfa34(_0x467f10,_0x3e499c){return _0xdbdd05(_0x467f10-0x60b,_0x3e499c);}var _0x2da760={};_0x2da760['x']=_0x1dce83,_0x2da760['y']=_0x5cde6c;const _0x12dcaf=this['_heat']['getValueAt'](_0x2da760),_0x7c7ab4=this[_0x2dfa34(0x40c,_0x26f01f._0x3efe39)][_0x2dfa34(0x3c5,_0x26f01f._0xc4c7b6)]['ctx'][_0x3bb423(0x323,_0x26f01f._0x3ec1aa)](_0x1dce83-0x1,_0x5cde6c-0x1,0x1,0x1)['data'];var _0x2eade9={};return _0x2eade9['x']=_0x1dce83,_0x2eade9['y']=_0x5cde6c,_0x2eade9['value']=_0x12dcaf,_0x2eade9['color']='rgba('+_0x7c7ab4[0x0]+','+_0x7c7ab4[0x1]+','+_0x7c7ab4[0x2]+','+_0x7c7ab4[0x3]+')',_0x2eade9;}[_0xdbdd05(-0x1e3,-0x1ee)](_0x2e2f40){var _0x13b1f8={_0x3f1437:0x95,_0x5e5fd5:0x53,_0x5a459c:0x18f,_0x1c4d87:0xd4,_0x133c13:0xb7,_0xa36d3f:0xc6,_0x580fa0:0x9a,_0x493b7:0x71,_0x2301d9:0x198,_0x233923:0x252};function _0x310ddf(_0x34228f,_0xb2fcb3){return _0xdbdd05(_0x34228f-0x27a,_0xb2fcb3);}function _0x162c45(_0x4cb4f0,_0x23d167){return _0x13a5eb(_0x4cb4f0,_0x23d167- -0x4b1);}if(this['style']['type']==='image')_0x2e2f40 instanceof HTMLCanvasElement&&(_0x2e2f40=_0x2e2f40['toDataURL']('image/png',0x1)),this[_0x310ddf(_0x13b1f8._0x3f1437,_0x13b1f8._0x5e5fd5)]['setOptions']({'url':_0x2e2f40,'rectangle':this[_0x162c45(-_0x13b1f8._0x5a459c,-0x1c9)],'opacity':this[_0x310ddf(_0x13b1f8._0x1c4d87,0xef)]['opacity']});else this['style']['arc']?this[_0x162c45(-0x15f,-0x170)]&&this['_graphic']['rectangle']['equals'](this[_0x310ddf(0x88,0x9d)])?(this['_graphic'][_0x310ddf(0xc8,0xa1)][_0x310ddf(0xde,_0x13b1f8._0x133c13)]=_0x2e2f40,this['_graphic'][_0x310ddf(0xc8,0x116)][_0x310ddf(0x74,0xa8)]=this[_0x310ddf(_0x13b1f8._0xa36d3f,0x10f)](),this['_graphic2']&&(this[_0x310ddf(_0x13b1f8._0x580fa0,_0x13b1f8._0x493b7)][_0x310ddf(0xc8,0x92)]['image']=_0x2e2f40,this['_graphic2'][_0x162c45(-0x14d,-0x189)]['bumpMap']=this[_0x162c45(-_0x13b1f8._0x2301d9,-0x170)]['uniforms']['bumpMap'])):this[_0x310ddf(0x2c,0x24)](_0x2e2f40):this['_graphic']&&this['_graphic'][_0x162c45(-_0x13b1f8._0x233923,-0x1fe)]['equals'](this['_rectangle'])?this['_graphic']['uniforms']['image']=_0x2e2f40:this['_createGraphic'](_0x2e2f40);}['_createGraphic'](_0x5059c0){var _0xc50678={_0x4424e0:0x12a,_0xf26195:0x130,_0x453e66:0x16d,_0x24931b:0x10b,_0x11e19e:0x155};this['clear']();var _0xc32251={};function _0x1f812d(_0x522948,_0x15a09c){return _0x13a5eb(_0x522948,_0x15a09c- -0x1a0);}_0xc32251[_0x1c6410(0x1b5,0x16a)]=_0x5059c0;const _0x4ba1d8={...this['options'],'private':!![],'flyTo':![],'rectangle':this['_rectangle'],'appearance':new Cesium['EllipsoidSurfaceAppearance']({'material':mars3d__namespace[_0x1c6410(0x105,0x133)]['createMaterial'](mars3d__namespace[_0x1f812d(_0xc50678._0x4424e0,0x11f)]['Image2'],_0xc32251),'flat':!![]})};function _0x1c6410(_0x2b9286,_0x3efa77){return _0x13a5eb(_0x2b9286,_0x3efa77- -0x1d4);}delete _0x4ba1d8['positions'],this[_0x1c6410(_0xc50678._0xf26195,_0xc50678._0x453e66)]=new mars3d__namespace['graphic'][(_0x1c6410(0x153,_0xc50678._0x24931b))](_0x4ba1d8),this[_0x1f812d(0x1a9,_0xc50678._0x11e19e)][_0x1c6410(0xda,0xc3)](this['_graphic']);}['_createArcGraphic'](_0x15dde3){var _0x325062={_0x4bfa30:0x3c1,_0x39a871:0x1e2,_0x4b7323:0x41d,_0x3a3c08:0x41e,_0x3ff27b:0x413,_0x212ae7:0x1cd,_0x4e6bb5:0x412,_0x2a5bf7:0x1bc,_0x151d8f:0x3f3,_0x52fda1:0x3d5,_0x2b60e4:0x19f,_0x237fac:0x16a,_0x2c8a2a:0x1ad,_0x35f201:0x14e,_0x61deb4:0x3b4,_0x54d96e:0x200,_0x1e3597:0x249,_0x3641e1:0x3f8,_0x1ceb5d:0x409,_0x4d8a66:0x42d,_0x3f9251:0x438,_0x42045f:0x387,_0xf1e7ca:0x3e4,_0x3512d6:0x3f7,_0x2bf74e:0x3fc,_0x59d12b:0x448,_0x129cd0:0x412,_0x893569:0x3d6,_0x4fd92c:0x1ab,_0x1da7cc:0x192,_0x504dc9:0x171};this[_0x1470d4(_0x325062._0x4bfa30,0x40c)]();var _0x22b0a6={};_0x22b0a6[_0x69ae52(_0x325062._0x39a871,0x222)]=!![];var _0x65311e={};_0x65311e[_0x1470d4(0x428,_0x325062._0x4b7323)]=!![];const _0x32a442=Cesium['RenderState']['fromCache']({'cull':_0x22b0a6,'depthTest':_0x65311e,'stencilTest':{'enabled':!![],'frontFunction':Cesium[_0x1470d4(_0x325062._0x3a3c08,0x3df)]['ALWAYS'],'frontOperation':{'fail':Cesium[_0x1470d4(_0x325062._0x3ff27b,0x3f7)]['KEEP'],'zFail':Cesium['StencilOperation']['KEEP'],'zPass':Cesium[_0x69ae52(0x1bc,_0x325062._0x212ae7)]['REPLACE']},'backFunction':Cesium['StencilFunction'][_0x1470d4(_0x325062._0x4e6bb5,0x449)],'backOperation':{'fail':Cesium[_0x69ae52(_0x325062._0x2a5bf7,0x1f2)]['KEEP'],'zFail':Cesium[_0x69ae52(0x1bc,0x1bd)][_0x1470d4(_0x325062._0x151d8f,_0x325062._0x52fda1)],'zPass':Cesium[_0x69ae52(0x1bc,_0x325062._0x2b60e4)]['REPLACE']},'reference':0x2,'mask':0x2},'blending':Cesium[_0x69ae52(0x1e4,0x21e)][_0x69ae52(_0x325062._0x237fac,0x12e)]}),_0x56e860=Math['floor'](this['style']['diffHeight']??this['_bounds']['radius']*0.05)+0.1;this['style'][_0x69ae52(0x210,0x1f9)]&&delete this['style']['diffHeight'];this['style'][_0x69ae52(_0x325062._0x2c8a2a,_0x325062._0x35f201)]=this[_0x1470d4(_0x325062._0x61deb4,0x3b5)][_0x69ae52(0x1ad,0x186)]/(this[_0x69ae52(_0x325062._0x54d96e,_0x325062._0x1e3597)]['splitNum'],0x64);function _0x1470d4(_0x3d2945,_0x3c6560){return _0xdbdd05(_0x3c6560-0x5e1,_0x3d2945);}function _0x69ae52(_0x345a1e,_0x3ff3ad){return _0x13a5eb(_0x3ff3ad,_0x345a1e- -0x134);}const _0x50b234=new Cesium[(_0x1470d4(_0x325062._0x3641e1,_0x325062._0x1ceb5d))]({'fabric':{'uniforms':{'image':_0x15dde3,'repeat':new Cesium['Cartesian2'](0x1,0x1),'color':new Cesium[(_0x69ae52(0x1a2,0x182))](0x1,0x1,0x1,0x0),'bumpMap':this[_0x1470d4(0x409,_0x325062._0x4d8a66)]()},'source':HeatMaterial},'translucent':!![]}),_0x2e0ae8=this['style']['arcDirection']||0x1;this[_0x1470d4(0x3ed,0x448)]=new mars3d__namespace['graphic']['RectanglePrimitive']({...this['options'],'private':!![],'flyTo':![],'rectangle':this[_0x1470d4(_0x325062._0x3f9251,0x3ef)],'appearance':new Cesium[(_0x1470d4(_0x325062._0x42045f,_0x325062._0xf1e7ca))]({'flat':!![],'aboveGround':!![],'renderState':_0x32a442,'material':_0x50b234,'vertexShaderSource':getVertexShaderSource(_0x56e860*_0x2e0ae8)})}),this[_0x1470d4(_0x325062._0x3512d6,_0x325062._0x2bf74e)]['addGraphic'](this[_0x1470d4(0x493,_0x325062._0x59d12b)]),this['style'][_0x1470d4(0x3a3,0x3de)]===0x0&&(this[_0x69ae52(0x1c6,0x1c4)]=new mars3d__namespace[(_0x1470d4(_0x325062._0x129cd0,_0x325062._0x893569))][(_0x69ae52(_0x325062._0x4fd92c,_0x325062._0x1da7cc))]({...this['options'],'private':!![],'flyTo':![],'rectangle':this[_0x69ae52(0x1b4,_0x325062._0x504dc9)],'appearance':new Cesium['EllipsoidSurfaceAppearance']({'flat':!![],'aboveGround':!![],'renderState':_0x32a442,'material':_0x50b234,'vertexShaderSource':getVertexShaderSource(-_0x56e860)})}),this['_layer']['addGraphic'](this['_graphic2']));}}mars3d__namespace['LayerUtil'][_0x13a5eb(0x2e0,0x30a)]('heat',HeatLayer),mars3d__namespace['layer'][_0xdbdd05(-0x1c3,-0x1ac)]=HeatLayer,mars3d__namespace[_0x13a5eb(0x34b,0x340)]=h337;function getVertexShaderSource(_0x44cfdd){return'in\x20vec3\x20position3DHigh;\x0a\x20\x20in\x20vec3\x20position3DLow;\x0a\x20\x20in\x20vec2\x20st;\x0a\x20\x20in\x20float\x20batchId;\x0a\x20\x20uniform\x20sampler2D\x20bumpMap_3;\x0a\x20\x20out\x20vec3\x20v_positionMC;\x0a\x20\x20out\x20vec3\x20v_positionEC;\x0a\x20\x20out\x20vec2\x20v_st;\x0a\x0a\x20\x20void\x20main()\x0a\x20\x20{\x0a\x20\x20\x20\x20vec4\x20p\x20=\x20czm_computePosition();\x0a\x20\x20\x20\x20v_positionMC\x20=\x20position3DHigh\x20+\x20position3DLow;\x0a\x20\x20\x20\x20v_positionEC\x20=\x20(czm_modelViewRelativeToEye\x20*\x20p).xyz;\x0a\x20\x20\x20\x20v_st\x20=\x20st;\x0a\x20\x20\x20\x20vec4\x20color\x20=\x20texture(bumpMap_3,\x20v_st);\x0a\x20\x20\x20\x20float\x20centerBump\x20=\x20distance(vec3(0.0),color.rgb);\x0a\x20\x20\x20\x20vec3\x20upDir\x20=\x20normalize(v_positionMC.xyz);\x0a\x20\x20\x20\x20vec3\x20disPos\x20=\x20upDir\x20*\x20centerBump\x20*\x20'+_0x44cfdd+';\x0a\x20\x20\x20\x20p\x20+=vec4(disPos,0.0);\x0a\x20\x20\x20\x20gl_Position\x20=\x20czm_modelViewProjectionRelativeToEye\x20*\x20p;\x0a\x20\x20}\x0a';}function getCanvas(_0x331aa9){return _0x331aa9=mars3d__namespace['DomUtil']['copyCanvas'](_0x331aa9),_0x331aa9;}function getSurfaceDistance(_0x279a9e){var _0x2ba81e={_0x491557:0x2bb,_0x3c9883:0x269,_0x255919:0x32d},_0x958e56={_0x5bf1a4:0x4a9};const _0x2e5de8=_0x279a9e[_0x4c2a1b(0x37d,0x325)]['ellipsoid'],_0x2bd676=_0x279a9e['canvas'];function _0x453f5a(_0x302d4e,_0xa11d61){return _0xdbdd05(_0xa11d61-_0x958e56._0x5bf1a4,_0x302d4e);}const _0x2738c0=_0x2bd676[_0x453f5a(_0x2ba81e._0x491557,_0x2ba81e._0x3c9883)]/0x2,_0x25e4b2=_0x2bd676[_0x453f5a(0x2a7,0x29f)]/0x2,_0x2e0d0d=_0x2bd676[_0x4c2a1b(_0x2ba81e._0x255919,0x301)]/0x64,_0x540505=new Cesium['Cartesian2'](_0x2738c0,_0x25e4b2);function _0x4c2a1b(_0x4ab89b,_0x503c62){return _0x13a5eb(_0x4ab89b,_0x503c62-0x67);}let _0x31aceb,_0x173fb5;_0x540505['x']=_0x2738c0;for(let _0x1d87cd=0x0;_0x1d87cd<0x64;_0x1d87cd++){_0x540505['y']=_0x2e0d0d*_0x1d87cd;const _0x1d68c2=_0x279a9e['camera']['pickEllipsoid'](_0x540505,_0x2e5de8);if(_0x1d68c2){_0x31aceb=_0x1d68c2;break;}}for(let _0x73263f=0x64;_0x73263f>0x0;_0x73263f--){_0x540505['y']=_0x2e0d0d*_0x73263f;const _0x3d481a=_0x279a9e['camera'][_0x4c2a1b(0x3f6,0x397)](_0x540505,_0x2e5de8);if(_0x3d481a){_0x173fb5=_0x3d481a;break;}}return _0x31aceb&&_0x173fb5?mars3d__namespace[_0x453f5a(0x2df,0x2ec)]['getSurfaceDistance']([_0x31aceb,_0x173fb5]):0x0;}mars3d__namespace['Log']['logInfo']('mars3d-heatmap插件\x20注册成功'),exports['HeatLayer']=HeatLayer;function _0xdbdd05(_0x376bd3,_0x5da331){return _0x44a8(_0x376bd3- -0x385,_0x5da331);}var _0x3e38f7={};_0x3e38f7[_0xdbdd05(-0x1cc,-0x1af)]=!![],Object[_0xdbdd05(-0x212,-0x270)](exports,'__esModule',_0x3e38f7);
}));
