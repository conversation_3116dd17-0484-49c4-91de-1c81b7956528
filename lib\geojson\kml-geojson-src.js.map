{"version": 3, "sources": ["webpack://kgUtil/webpack/universalModuleDefinition", "webpack://kgUtil/webpack/bootstrap", "webpack://kgUtil/(webpack)/buildin/global.js", "webpack://kgUtil/./node_modules/_@babel_runtime@7.14.8@@babel/runtime/helpers/typeof.js", "webpack://kgUtil/./node_modules/_process@0.11.10@process/browser.js", "webpack://kgUtil/(webpack)/buildin/harmony-module.js", "webpack://kgUtil/./node_modules/_xmldom@0.6.0@xmldom/lib/dom.js", "webpack://kgUtil/./src/index.js", "webpack://kgUtil/./src/toGeoJSON.js", "webpack://kgUtil/./node_modules/_xmldom@0.6.0@xmldom/lib/dom-parser.js", "webpack://kgUtil/./node_modules/_xmldom@0.6.0@xmldom/lib/entities.js", "webpack://kgUtil/./node_modules/_xmldom@0.6.0@xmldom/lib/sax.js", "webpack://kgUtil/./src/toKml.js", "webpack://kgUtil/(webpack)/buildin/amd-options.js", "webpack://kgUtil/./src sync", "webpack://kgUtil/./node_modules/_jszip@3.7.0@jszip/dist/jszip.min.js", "webpack://kgUtil/./node_modules/_buffer@4.9.2@buffer/index.js", "webpack://kgUtil/./node_modules/_base64-js@1.5.1@base64-js/index.js", "webpack://kgUtil/./node_modules/_ieee754@1.2.1@ieee754/index.js", "webpack://kgUtil/./node_modules/_isarray@1.0.0@isarray/index.js", "webpack://kgUtil/./node_modules/_timers-browserify@2.0.12@timers-browserify/main.js", "webpack://kgUtil/./node_modules/_setimmediate@1.0.5@setimmediate/setImmediate.js", "webpack://kgUtil/./node_modules/_jszip@3.7.0@jszip/dist sync"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "tokml", "JSZip", "toKml", "g<PERSON><PERSON><PERSON>", "options", "getDom", "xml", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "getExtension", "fileName", "split", "pop", "getKmlDom", "kmzFile", "zip", "loadAsync", "then", "kmlDom", "for<PERSON>ach", "re<PERSON><PERSON><PERSON>", "file", "async", "Promise", "reject", "toGeoJSON", "doc", "isString", "extension", "Cesium", "Resource", "fetchXML", "kml", "fetchBlob", "resolve", "getRootNode", "str", "constructor", "String", "removeSpace", "trimSpace", "splitSpace", "<PERSON><PERSON><PERSON>", "x", "length", "i", "h", "charCodeAt", "get", "y", "getElementsByTagName", "attr", "getAttribute", "attrf", "parseFloat", "get1", "n", "norm", "el", "normalize", "numarray", "j", "o", "nodeVal", "textContent", "getMulti", "ys", "k", "extend", "coord1", "v", "replace", "coord", "coords", "push", "coordPair", "ll", "ele", "heartRate", "time", "e", "isNaN", "coordinates", "fc", "type", "features", "serializer", "XMLSerializer", "exports", "process", "browser", "xml2str", "undefined", "serializeToString", "t", "gj", "styleIndex", "styleByHash", "styleMapIndex", "geotypes", "placemarks", "styles", "styleMaps", "hash", "toString", "l", "pairs", "pairsMap", "m", "concat", "getPlacemark", "kmlColor", "color", "opacity", "substr", "parseInt", "gxCoord", "gxCoords", "root", "elems", "times", "timeElems", "getGeometry", "geomNode", "geomNodes", "geoms", "coordTimes", "rings", "track", "geomsAndTimes", "properties", "name", "styleUrl", "description", "timeSpan", "timeStamp", "extendedData", "lineStyle", "polyStyle", "visibility", "styleHash", "styleMapHash", "normal", "style", "begin", "end", "timespan", "timestamp", "linestyles", "width", "stroke", "polystyles", "pcolor", "popacity", "fill", "outline", "datas", "simpleDatas", "feature", "geometry", "geometries", "id", "gpx", "tracks", "routes", "waypoints", "getTrack", "getRoute", "getPoint", "getPoints", "node", "pointname", "pts", "line", "heartRates", "c", "segments", "getProperties", "routeObj", "prop", "links", "link", "href", "module", "f", "define", "g", "window", "global", "self", "r", "s", "u", "a", "Error", "code", "call", "strxml", "tag", "encode", "documentName", "documentDescription", "<PERSON><PERSON>", "styleHashesArray", "_", "valid", "geometryString", "any", "styleDefinition", "styleReference", "hashStyle", "isPoint", "hasMarkerStyle", "indexOf", "markerStyle", "isPolygon", "isLine", "hasPolygonAndLineStyle", "polygonAndLineStyle", "extendeddata", "map", "join", "Point", "LineString", "linearring", "Polygon", "outer", "inner", "slice", "outerRing", "innerRings", "MultiPoint", "MultiPolygon", "MultiLineString", "GeometryCollection", "every", "cds", "data", "iconUrl", "iconSize", "size", "symbol", "char<PERSON>t", "key", "hexToKmlColor", "hexColor", "toLowerCase", "b", "tagClose", "attributes", "contents"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;AClFA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;;AAE5C;;;;;;;ACnBA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,6E;;;;;;ACrBA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,qCAAqC;;AAErC;AACA;AACA;;AAEA,2BAA2B;AAC3B;AACA;AACA;AACA,4BAA4B,UAAU;;;;;;;ACvLtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,oBAAoB,YAAY,QAAQ;AAChF,2CAA2C,QAAQ;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA,0BAA0B,cAAc;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,kLAAkL;AAClL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;;AAEA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA;;;AAGA,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,EAAE;AACF;AACA,gEAAgE;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA,gEAAgE;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C;AACA,EAAE;AACF,2CAA2C;AAC3C;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA,yBAAyB;AACzB,0BAA0B;AAC1B,2BAA2B;AAC3B,4BAA4B;AAC5B,+BAA+B;AAC/B;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC,SAAS;AACT;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,8CAA8C;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,GAAG;AACH,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,EAAE;;AAEF;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,GAAG;AACH,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA,GAAG;AACH;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;;AAEA,EAAE;AACF;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;AAIA,cAAc,MAAM;AACpB;AACA;AACA;AACA,4BAA4B,gDAAgD;AAC5E,IAAI;AACJ,4BAA4B,oCAAoC;AAChE;AACA;AACA,cAAc,MAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,gCAAgC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,gCAAgC;AAC5D;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,WAAW;AACtB,2EAA2E;AAC3E,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,MAAM;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,MAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,SAAS;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AClwCA;AAAA;AAAA;AAAA,IAAIA,SAAS,GAAGC,mBAAO,CAAC,CAAD,CAAvB;;AACA,IAAIC,KAAK,GAAGD,mBAAO,CAAC,EAAD,CAAnB;;AACA,IAAIE,KAAK,GAAGF,mBAAO,CAAC,EAAD,CAAnB,C,CAEA;;;AACO,SAASG,KAAT,CAAeC,OAAf,EAAwBC,OAAxB,EAAiC;AACtC,SAAOJ,KAAK,CAACG,OAAD,EAAUC,OAAV,CAAZ;AACD;;AAED,IAAIC,MAAM,GAAG,SAATA,MAAS,CAACC,GAAD;AAAA,SAAS,IAAIC,SAAJ,GAAgBC,eAAhB,CAAgCF,GAAhC,EAAqC,UAArC,CAAT;AAAA,CAAb;;AACA,IAAIG,YAAY,GAAG,SAAfA,YAAe,CAACC,QAAD;AAAA,SAAcA,QAAQ,CAACC,KAAT,CAAe,GAAf,EAAoBC,GAApB,EAAd;AAAA,CAAnB;;AAEA,IAAIC,SAAS,GAAG,SAAZA,SAAY,CAACC,OAAD,EAAa;AAC3B,MAAIC,GAAG,GAAG,IAAId,KAAJ,EAAV;AACA,SAAOc,GAAG,CAACC,SAAJ,CAAcF,OAAd,EAAuBG,IAAvB,CAA4B,UAACF,GAAD,EAAS;AAC1C,QAAIG,MAAM,GAAG,IAAb;AACAH,OAAG,CAACI,OAAJ,CAAY,UAACC,OAAD,EAAUC,IAAV,EAAmB;AAC7B,UAAIZ,YAAY,CAACW,OAAD,CAAZ,KAA0B,KAA1B,IAAmCF,MAAM,KAAK,IAAlD,EAAwD;AACtDA,cAAM,GAAGG,IAAI,CAACC,KAAL,CAAW,QAAX,EAAqBL,IAArB,CAA0BZ,MAA1B,CAAT;AACD;AACF,KAJD;AAKA,WAAOa,MAAM,IAAIK,OAAO,CAACC,MAAR,CAAe,mBAAf,CAAjB;AACD,GARM,CAAP;AASD,CAXD,C,CAaA;;;AACO,SAASC,SAAT,CAAmBC,GAAnB,EAAwB;AAC7B,MAAI,CAACA,GAAL,EAAU,OAAOH,OAAO,CAACC,MAAR,CAAe,QAAf,CAAP;;AAEV,MAAIG,QAAQ,CAACD,GAAD,CAAZ,EAAmB;AACjB,QAAIE,SAAS,GAAGnB,YAAY,CAACiB,GAAD,CAA5B;;AACA,QAAIE,SAAS,KAAK,KAAlB,EAAyB;AACvB,aAAOC,MAAM,CAACC,QAAP,CAAgBC,QAAhB,CAAyBL,GAAzB,EAA8BT,IAA9B,CAAmC,UAAUC,MAAV,EAAkB;AAC1D,eAAOpB,SAAS,CAACkC,GAAV,CAAcd,MAAd,CAAP;AACD,OAFM,CAAP;AAGD,KAJD,MAIO,IAAIU,SAAS,KAAK,KAAlB,EAAyB;AAC9B,aAAOC,MAAM,CAACC,QAAP,CAAgBG,SAAhB,CAA0BP,GAA1B,EACJT,IADI,CACC,UAAUX,GAAV,EAAe;AACnB,eAAOO,SAAS,CAACP,GAAD,CAAhB;AACD,OAHI,EAIJW,IAJI,CAIC,UAAUC,MAAV,EAAkB;AACtB,eAAOpB,SAAS,CAACkC,GAAV,CAAcd,MAAd,CAAP;AACD,OANI,CAAP;AAOD,KARM,MAQA;AACL;AACA,UAAIf,OAAO,GAAGL,SAAS,CAACkC,GAAV,CAAcnB,SAAS,CAACa,GAAD,CAAvB,CAAd;AACA,aAAOH,OAAO,CAACW,OAAR,CAAgB/B,OAAhB,CAAP;AACD;AACF,GAnBD,MAmBO,IAAIuB,GAAG,CAACS,WAAR,EAAqB;AAC1B;AACA,QAAIhC,QAAO,GAAGL,SAAS,CAACkC,GAAV,CAAcN,GAAd,CAAd;;AACA,WAAOH,OAAO,CAACW,OAAR,CAAgB/B,QAAhB,CAAP;AACD,GAJM,MAIA;AACL;AACA,WAAOU,SAAS,CAACa,GAAD,CAAT,CAAeT,IAAf,CAAoB,UAAUC,MAAV,EAAkB;AAC3C,aAAOpB,SAAS,CAACkC,GAAV,CAAcd,MAAd,CAAP;AACD,KAFM,CAAP;AAGD;AACF;;AAED,SAASS,QAAT,CAAkBS,GAAlB,EAAuB;AACrB,SAAO,OAAOA,GAAP,IAAc,QAAd,IAA0BA,GAAG,CAACC,WAAJ,IAAmBC,MAApD;AACD,C;;;;;;;;;;;;AC9DD,IAAIb,SAAS,GAAI,YAAW;AACxB;;AAEA,MAAIc,WAAW,GAAG,MAAlB;AAAA,MACIC,SAAS,GAAG,YADhB;AAAA,MAEIC,UAAU,GAAG,KAFjB,CAHwB,CAMxB;;AACA,WAASC,MAAT,CAAgBC,CAAhB,EAAmB;AACf,QAAI,CAACA,CAAD,IAAM,CAACA,CAAC,CAACC,MAAb,EAAqB,OAAO,CAAP;;AACrB,SAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,CAAC,GAAG,CAApB,EAAuBD,CAAC,GAAGF,CAAC,CAACC,MAA7B,EAAqCC,CAAC,EAAtC,EAA0C;AACtCC,OAAC,GAAI,CAACA,CAAC,IAAI,CAAN,IAAWA,CAAZ,GAAiBH,CAAC,CAACI,UAAF,CAAaF,CAAb,CAAjB,GAAmC,CAAvC;AACH;;AAAC,WAAOC,CAAP;AACL,GAZuB,CAaxB;;;AACA,WAASE,GAAT,CAAaL,CAAb,EAAgBM,CAAhB,EAAmB;AAAE,WAAON,CAAC,CAACO,oBAAF,CAAuBD,CAAvB,CAAP;AAAmC;;AACxD,WAASE,IAAT,CAAcR,CAAd,EAAiBM,CAAjB,EAAoB;AAAE,WAAON,CAAC,CAACS,YAAF,CAAeH,CAAf,CAAP;AAA2B;;AACjD,WAASI,KAAT,CAAeV,CAAf,EAAkBM,CAAlB,EAAqB;AAAE,WAAOK,UAAU,CAACH,IAAI,CAACR,CAAD,EAAIM,CAAJ,CAAL,CAAjB;AAAgC,GAhB/B,CAiBxB;;;AACA,WAASM,IAAT,CAAcZ,CAAd,EAAiBM,CAAjB,EAAoB;AAAE,QAAIO,CAAC,GAAGR,GAAG,CAACL,CAAD,EAAIM,CAAJ,CAAX;AAAmB,WAAOO,CAAC,CAACZ,MAAF,GAAWY,CAAC,CAAC,CAAD,CAAZ,GAAkB,IAAzB;AAAgC,GAlBjD,CAmBxB;;;AACA,WAASC,IAAT,CAAcC,EAAd,EAAkB;AAAE,QAAIA,EAAE,CAACC,SAAP,EAAkB;AAAED,QAAE,CAACC,SAAH;AAAiB;;AAAC,WAAOD,EAAP;AAAY,GApB9C,CAqBxB;;;AACA,WAASE,QAAT,CAAkBjB,CAAlB,EAAqB;AACjB,SAAK,IAAIkB,CAAC,GAAG,CAAR,EAAWC,CAAC,GAAG,EAApB,EAAwBD,CAAC,GAAGlB,CAAC,CAACC,MAA9B,EAAsCiB,CAAC,EAAvC,EAA2C;AAAEC,OAAC,CAACD,CAAD,CAAD,GAAOP,UAAU,CAACX,CAAC,CAACkB,CAAD,CAAF,CAAjB;AAA0B;;AACvE,WAAOC,CAAP;AACH,GAzBuB,CA0BxB;;;AACA,WAASC,OAAT,CAAiBpB,CAAjB,EAAoB;AAChB,QAAIA,CAAJ,EAAO;AAAEc,UAAI,CAACd,CAAD,CAAJ;AAAU;;AACnB,WAAQA,CAAC,IAAIA,CAAC,CAACqB,WAAR,IAAwB,EAA/B;AACH,GA9BuB,CA+BxB;;;AACA,WAASC,QAAT,CAAkBtB,CAAlB,EAAqBuB,EAArB,EAAyB;AACrB,QAAIJ,CAAC,GAAG,EAAR;AAAA,QAAYN,CAAZ;AAAA,QAAeW,CAAf;;AACA,SAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGD,EAAE,CAACtB,MAAnB,EAA2BuB,CAAC,EAA5B,EAAgC;AAC5BX,OAAC,GAAGD,IAAI,CAACZ,CAAD,EAAIuB,EAAE,CAACC,CAAD,CAAN,CAAR;AACA,UAAIX,CAAJ,EAAOM,CAAC,CAACI,EAAE,CAACC,CAAD,CAAH,CAAD,GAAWJ,OAAO,CAACP,CAAD,CAAlB;AACV;;AACD,WAAOM,CAAP;AACH,GAvCuB,CAwCxB;;;AACA,WAASM,MAAT,CAAgBzB,CAAhB,EAAmBM,CAAnB,EAAsB;AAAE,SAAK,IAAIkB,CAAT,IAAclB,CAAd;AAAiBN,OAAC,CAACwB,CAAD,CAAD,GAAOlB,CAAC,CAACkB,CAAD,CAAR;AAAjB;AAA+B,GAzC/B,CA0CxB;;;AACA,WAASE,MAAT,CAAgBC,CAAhB,EAAmB;AAAE,WAAOV,QAAQ,CAACU,CAAC,CAACC,OAAF,CAAUhC,WAAV,EAAuB,EAAvB,EAA2B5B,KAA3B,CAAiC,GAAjC,CAAD,CAAf;AAAyD,GA3CtD,CA4CxB;;;AACA,WAAS6D,KAAT,CAAeF,CAAf,EAAkB;AACd,QAAIG,MAAM,GAAGH,CAAC,CAACC,OAAF,CAAU/B,SAAV,EAAqB,EAArB,EAAyB7B,KAAzB,CAA+B8B,UAA/B,CAAb;AAAA,QACIqB,CAAC,GAAG,EADR;;AAEA,SAAK,IAAIjB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4B,MAAM,CAAC7B,MAA3B,EAAmCC,CAAC,EAApC,EAAwC;AACpCiB,OAAC,CAACY,IAAF,CAAOL,MAAM,CAACI,MAAM,CAAC5B,CAAD,CAAP,CAAb;AACH;;AACD,WAAOiB,CAAP;AACH;;AACD,WAASa,SAAT,CAAmBhC,CAAnB,EAAsB;AAClB,QAAIiC,EAAE,GAAG,CAACvB,KAAK,CAACV,CAAD,EAAI,KAAJ,CAAN,EAAkBU,KAAK,CAACV,CAAD,EAAI,KAAJ,CAAvB,CAAT;AAAA,QACIkC,GAAG,GAAGtB,IAAI,CAACZ,CAAD,EAAI,KAAJ,CADd;AAAA,QAEI;AACAmC,aAAS,GAAGvB,IAAI,CAACZ,CAAD,EAAI,WAAJ,CAAJ,IAAwBY,IAAI,CAACZ,CAAD,EAAI,IAAJ,CAH5C;AAAA,QAIIoC,IAAI,GAAGxB,IAAI,CAACZ,CAAD,EAAI,MAAJ,CAJf;AAAA,QAKIqC,CALJ;;AAMA,QAAIH,GAAJ,EAAS;AACLG,OAAC,GAAG1B,UAAU,CAACS,OAAO,CAACc,GAAD,CAAR,CAAd;;AACA,UAAI,CAACI,KAAK,CAACD,CAAD,CAAV,EAAe;AACXJ,UAAE,CAACF,IAAH,CAAQM,CAAR;AACH;AACJ;;AACD,WAAO;AACHE,iBAAW,EAAEN,EADV;AAEHG,UAAI,EAAEA,IAAI,GAAGhB,OAAO,CAACgB,IAAD,CAAV,GAAmB,IAF1B;AAGHD,eAAS,EAAEA,SAAS,GAAGxB,UAAU,CAACS,OAAO,CAACe,SAAD,CAAR,CAAb,GAAoC;AAHrD,KAAP;AAKH,GAvEuB,CAyExB;;;AACA,WAASK,EAAT,GAAc;AACV,WAAO;AACHC,UAAI,EAAE,mBADH;AAEHC,cAAQ,EAAE;AAFP,KAAP;AAIH;;AAED,MAAIC,UAAJ;;AACA,MAAI,OAAOC,aAAP,KAAyB,WAA7B,EAA0C;AACtC;AACAD,cAAU,GAAG,IAAIC,aAAJ,EAAb,CAFsC,CAG1C;AACC,GAJD,MAIO,IAAI,QAAOC,OAAP,sGAAOA,OAAP,OAAmB,QAAnB,IAA+B,QAAOC,OAAP,sGAAOA,OAAP,OAAmB,QAAlD,IAA8D,CAACA,OAAO,CAACC,OAA3E,EAAoF;AACvFJ,cAAU,GAAG,KAAKvF,mBAAO,CAAC,CAAD,CAAP,CAAkBwF,aAAvB,GAAb;AACH;;AACD,WAASI,OAAT,CAAiBvD,GAAjB,EAAsB;AAClB;AACA;;AACA;AACA,QAAIA,GAAG,CAAC9B,GAAJ,KAAYsF,SAAhB,EAA2B,OAAOxD,GAAG,CAAC9B,GAAX;AAC3B,WAAOgF,UAAU,CAACO,iBAAX,CAA6BzD,GAA7B,CAAP;AACH;;AAED,MAAI0D,CAAC,GAAG;AACJ9D,OAAG,EAAE,aAASN,GAAT,EAAc;AAEf,UAAIqE,EAAE,GAAGZ,EAAE,EAAX;AAAA,UACI;AACAa,gBAAU,GAAG,EAFjB;AAAA,UAEqBC,WAAW,GAAG,EAFnC;AAAA,UAGI;AACAC,mBAAa,GAAG,EAJpB;AAAA,UAKI;AACA;AACAC,cAAQ,GAAG,CAAC,SAAD,EAAY,YAAZ,EAA0B,OAA1B,EAAmC,OAAnC,EAA4C,UAA5C,CAPf;AAAA,UAQI;AACAC,gBAAU,GAAGpD,GAAG,CAACtB,GAAD,EAAM,WAAN,CATpB;AAAA,UAUI2E,MAAM,GAAGrD,GAAG,CAACtB,GAAD,EAAM,OAAN,CAVhB;AAAA,UAWI4E,SAAS,GAAGtD,GAAG,CAACtB,GAAD,EAAM,UAAN,CAXnB;;AAaA,WAAK,IAAIyC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkC,MAAM,CAACzD,MAA3B,EAAmCuB,CAAC,EAApC,EAAwC;AACpC,YAAIoC,IAAI,GAAG7D,MAAM,CAACiD,OAAO,CAACU,MAAM,CAAClC,CAAD,CAAP,CAAR,CAAN,CAA2BqC,QAA3B,CAAoC,EAApC,CAAX;AACAR,kBAAU,CAAC,MAAM7C,IAAI,CAACkD,MAAM,CAAClC,CAAD,CAAP,EAAY,IAAZ,CAAX,CAAV,GAA0CoC,IAA1C;AACAN,mBAAW,CAACM,IAAD,CAAX,GAAoBF,MAAM,CAAClC,CAAD,CAA1B;AACH;;AACD,WAAK,IAAIsC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,SAAS,CAAC1D,MAA9B,EAAsC6D,CAAC,EAAvC,EAA2C;AACvCT,kBAAU,CAAC,MAAM7C,IAAI,CAACmD,SAAS,CAACG,CAAD,CAAV,EAAe,IAAf,CAAX,CAAV,GAA6C/D,MAAM,CAACiD,OAAO,CAACW,SAAS,CAACG,CAAD,CAAV,CAAR,CAAN,CAA8BD,QAA9B,CAAuC,EAAvC,CAA7C;AACA,YAAIE,KAAK,GAAG1D,GAAG,CAACsD,SAAS,CAACG,CAAD,CAAV,EAAe,MAAf,CAAf;AACA,YAAIE,QAAQ,GAAG,EAAf;;AACA,aAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,KAAK,CAAC9D,MAA1B,EAAkCgE,CAAC,EAAnC,EAAuC;AACnCD,kBAAQ,CAAC5C,OAAO,CAACR,IAAI,CAACmD,KAAK,CAACE,CAAD,CAAN,EAAW,KAAX,CAAL,CAAR,CAAR,GAA2C7C,OAAO,CAACR,IAAI,CAACmD,KAAK,CAACE,CAAD,CAAN,EAAW,UAAX,CAAL,CAAlD;AACH;;AACDV,qBAAa,CAAC,MAAM/C,IAAI,CAACmD,SAAS,CAACG,CAAD,CAAV,EAAe,IAAf,CAAX,CAAb,GAAgDE,QAAhD;AAEH;;AACD,WAAK,IAAI9C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuC,UAAU,CAACxD,MAA/B,EAAuCiB,CAAC,EAAxC,EAA4C;AACxCkC,UAAE,CAACV,QAAH,GAAcU,EAAE,CAACV,QAAH,CAAYwB,MAAZ,CAAmBC,YAAY,CAACV,UAAU,CAACvC,CAAD,CAAX,CAA/B,CAAd;AACH;;AACD,eAASkD,QAAT,CAAkBzC,CAAlB,EAAqB;AACjB,YAAI0C,KAAJ,EAAWC,OAAX;AACA3C,SAAC,GAAGA,CAAC,IAAI,EAAT;;AACA,YAAIA,CAAC,CAAC4C,MAAF,CAAS,CAAT,EAAY,CAAZ,MAAmB,GAAvB,EAA4B;AAAE5C,WAAC,GAAGA,CAAC,CAAC4C,MAAF,CAAS,CAAT,CAAJ;AAAkB;;AAChD,YAAI5C,CAAC,CAAC1B,MAAF,KAAa,CAAb,IAAkB0B,CAAC,CAAC1B,MAAF,KAAa,CAAnC,EAAsC;AAAEoE,eAAK,GAAG1C,CAAR;AAAY;;AACpD,YAAIA,CAAC,CAAC1B,MAAF,KAAa,CAAjB,EAAoB;AAChBqE,iBAAO,GAAGE,QAAQ,CAAC7C,CAAC,CAAC4C,MAAF,CAAS,CAAT,EAAY,CAAZ,CAAD,EAAiB,EAAjB,CAAR,GAA+B,GAAzC;AACAF,eAAK,GAAG,MAAM1C,CAAC,CAAC4C,MAAF,CAAS,CAAT,EAAY,CAAZ,CAAN,GACJ5C,CAAC,CAAC4C,MAAF,CAAS,CAAT,EAAY,CAAZ,CADI,GAEJ5C,CAAC,CAAC4C,MAAF,CAAS,CAAT,EAAY,CAAZ,CAFJ;AAGH;;AACD,eAAO,CAACF,KAAD,EAAQ/B,KAAK,CAACgC,OAAD,CAAL,GAAiBrB,SAAjB,GAA6BqB,OAArC,CAAP;AACH;;AACD,eAASG,OAAT,CAAiB9C,CAAjB,EAAoB;AAAE,eAAOV,QAAQ,CAACU,CAAC,CAAC3D,KAAF,CAAQ,GAAR,CAAD,CAAf;AAAgC;;AACtD,eAAS0G,QAAT,CAAkBC,IAAlB,EAAwB;AACpB,YAAIC,KAAK,GAAGvE,GAAG,CAACsE,IAAD,EAAO,OAAP,EAAgB,IAAhB,CAAf;AAAA,YAAsC7C,MAAM,GAAG,EAA/C;AAAA,YAAmD+C,KAAK,GAAG,EAA3D;AACA,YAAID,KAAK,CAAC3E,MAAN,KAAiB,CAArB,EAAwB2E,KAAK,GAAGvE,GAAG,CAACsE,IAAD,EAAO,UAAP,CAAX;;AACxB,aAAK,IAAIzE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0E,KAAK,CAAC3E,MAA1B,EAAkCC,CAAC,EAAnC;AAAuC4B,gBAAM,CAACC,IAAP,CAAY0C,OAAO,CAACrD,OAAO,CAACwD,KAAK,CAAC1E,CAAD,CAAN,CAAR,CAAnB;AAAvC;;AACA,YAAI4E,SAAS,GAAGzE,GAAG,CAACsE,IAAD,EAAO,MAAP,CAAnB;;AACA,aAAK,IAAIzD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4D,SAAS,CAAC7E,MAA9B,EAAsCiB,CAAC,EAAvC;AAA2C2D,eAAK,CAAC9C,IAAN,CAAWX,OAAO,CAAC0D,SAAS,CAAC5D,CAAD,CAAV,CAAlB;AAA3C;;AACA,eAAO;AACHY,gBAAM,EAAEA,MADL;AAEH+C,eAAK,EAAEA;AAFJ,SAAP;AAIH;;AACD,eAASE,WAAT,CAAqBJ,IAArB,EAA2B;AACvB,YAAIK,QAAJ;AAAA,YAAcC,SAAd;AAAA,YAAyB/E,CAAzB;AAAA,YAA4BgB,CAA5B;AAAA,YAA+BM,CAA/B;AAAA,YAAkC0D,KAAK,GAAG,EAA1C;AAAA,YAA8CC,UAAU,GAAG,EAA3D;;AACA,YAAIvE,IAAI,CAAC+D,IAAD,EAAO,eAAP,CAAR,EAAiC;AAAE,iBAAOI,WAAW,CAACnE,IAAI,CAAC+D,IAAD,EAAO,eAAP,CAAL,CAAlB;AAAkD;;AACrF,YAAI/D,IAAI,CAAC+D,IAAD,EAAO,YAAP,CAAR,EAA8B;AAAE,iBAAOI,WAAW,CAACnE,IAAI,CAAC+D,IAAD,EAAO,YAAP,CAAL,CAAlB;AAA+C;;AAC/E,YAAI/D,IAAI,CAAC+D,IAAD,EAAO,eAAP,CAAR,EAAiC;AAAE,iBAAOI,WAAW,CAACnE,IAAI,CAAC+D,IAAD,EAAO,eAAP,CAAL,CAAlB;AAAkD;;AACrF,aAAKzE,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGsD,QAAQ,CAACvD,MAAzB,EAAiCC,CAAC,EAAlC,EAAsC;AAClC+E,mBAAS,GAAG5E,GAAG,CAACsE,IAAD,EAAOnB,QAAQ,CAACtD,CAAD,CAAf,CAAf;;AACA,cAAI+E,SAAJ,EAAe;AACX,iBAAK/D,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG+D,SAAS,CAAChF,MAA1B,EAAkCiB,CAAC,EAAnC,EAAuC;AACnC8D,sBAAQ,GAAGC,SAAS,CAAC/D,CAAD,CAApB;;AACA,kBAAIsC,QAAQ,CAACtD,CAAD,CAAR,KAAgB,OAApB,EAA6B;AACzBgF,qBAAK,CAACnD,IAAN,CAAW;AACPU,sBAAI,EAAE,OADC;AAEPF,6BAAW,EAAEb,MAAM,CAACN,OAAO,CAACR,IAAI,CAACoE,QAAD,EAAW,aAAX,CAAL,CAAR;AAFZ,iBAAX;AAIH,eALD,MAKO,IAAIxB,QAAQ,CAACtD,CAAD,CAAR,KAAgB,YAApB,EAAkC;AACrCgF,qBAAK,CAACnD,IAAN,CAAW;AACPU,sBAAI,EAAE,YADC;AAEPF,6BAAW,EAAEV,KAAK,CAACT,OAAO,CAACR,IAAI,CAACoE,QAAD,EAAW,aAAX,CAAL,CAAR;AAFX,iBAAX;AAIH,eALM,MAKA,IAAIxB,QAAQ,CAACtD,CAAD,CAAR,KAAgB,SAApB,EAA+B;AAClC,oBAAIkF,KAAK,GAAG/E,GAAG,CAAC2E,QAAD,EAAW,YAAX,CAAf;AAAA,oBACIlD,MAAM,GAAG,EADb;;AAEA,qBAAKN,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG4D,KAAK,CAACnF,MAAtB,EAA8BuB,CAAC,EAA/B,EAAmC;AAC/BM,wBAAM,CAACC,IAAP,CAAYF,KAAK,CAACT,OAAO,CAACR,IAAI,CAACwE,KAAK,CAAC5D,CAAD,CAAN,EAAW,aAAX,CAAL,CAAR,CAAjB;AACH;;AACD0D,qBAAK,CAACnD,IAAN,CAAW;AACPU,sBAAI,EAAE,SADC;AAEPF,6BAAW,EAAET;AAFN,iBAAX;AAIH,eAVM,MAUA,IAAI0B,QAAQ,CAACtD,CAAD,CAAR,KAAgB,OAAhB,IACPsD,QAAQ,CAACtD,CAAD,CAAR,KAAgB,UADb,EACyB;AAC5B,oBAAImF,KAAK,GAAGX,QAAQ,CAACM,QAAD,CAApB;AACAE,qBAAK,CAACnD,IAAN,CAAW;AACPU,sBAAI,EAAE,YADC;AAEPF,6BAAW,EAAE8C,KAAK,CAACvD;AAFZ,iBAAX;AAIA,oBAAIuD,KAAK,CAACR,KAAN,CAAY5E,MAAhB,EAAwBkF,UAAU,CAACpD,IAAX,CAAgBsD,KAAK,CAACR,KAAtB;AAC3B;AACJ;AACJ;AACJ;;AACD,eAAO;AACHK,eAAK,EAAEA,KADJ;AAEHC,oBAAU,EAAEA;AAFT,SAAP;AAIH;;AACD,eAAShB,YAAT,CAAsBQ,IAAtB,EAA4B;AACxB,YAAIW,aAAa,GAAGP,WAAW,CAACJ,IAAD,CAA/B;AAAA,YAAuCzE,CAAvC;AAAA,YAA0CqF,UAAU,GAAG,EAAvD;AAAA,YACIC,IAAI,GAAGpE,OAAO,CAACR,IAAI,CAAC+D,IAAD,EAAO,MAAP,CAAL,CADlB;AAAA,YAEIc,QAAQ,GAAGrE,OAAO,CAACR,IAAI,CAAC+D,IAAD,EAAO,UAAP,CAAL,CAFtB;AAAA,YAGIe,WAAW,GAAGtE,OAAO,CAACR,IAAI,CAAC+D,IAAD,EAAO,aAAP,CAAL,CAHzB;AAAA,YAIIgB,QAAQ,GAAG/E,IAAI,CAAC+D,IAAD,EAAO,UAAP,CAJnB;AAAA,YAKIiB,SAAS,GAAGhF,IAAI,CAAC+D,IAAD,EAAO,WAAP,CALpB;AAAA,YAMIkB,YAAY,GAAGjF,IAAI,CAAC+D,IAAD,EAAO,cAAP,CANvB;AAAA,YAOImB,SAAS,GAAGlF,IAAI,CAAC+D,IAAD,EAAO,WAAP,CAPpB;AAAA,YAQIoB,SAAS,GAAGnF,IAAI,CAAC+D,IAAD,EAAO,WAAP,CARpB;AAAA,YASIqB,UAAU,GAAGpF,IAAI,CAAC+D,IAAD,EAAO,YAAP,CATrB;AAWA,YAAI,CAACW,aAAa,CAACJ,KAAd,CAAoBjF,MAAzB,EAAiC,OAAO,EAAP;AACjC,YAAIuF,IAAJ,EAAUD,UAAU,CAACC,IAAX,GAAkBA,IAAlB;;AACV,YAAIC,QAAJ,EAAc;AACV,cAAIA,QAAQ,CAAC,CAAD,CAAR,KAAgB,GAApB,EAAyB;AACrBA,oBAAQ,GAAG,MAAMA,QAAjB;AACH;;AAEDF,oBAAU,CAACE,QAAX,GAAsBA,QAAtB;;AACA,cAAIpC,UAAU,CAACoC,QAAD,CAAd,EAA0B;AACtBF,sBAAU,CAACU,SAAX,GAAuB5C,UAAU,CAACoC,QAAD,CAAjC;AACH;;AACD,cAAIlC,aAAa,CAACkC,QAAD,CAAjB,EAA6B;AACzBF,sBAAU,CAACW,YAAX,GAA0B3C,aAAa,CAACkC,QAAD,CAAvC;AACAF,sBAAU,CAACU,SAAX,GAAuB5C,UAAU,CAACE,aAAa,CAACkC,QAAD,CAAb,CAAwBU,MAAzB,CAAjC;AACH,WAZS,CAaV;;;AACA,cAAIC,KAAK,GAAG9C,WAAW,CAACiC,UAAU,CAACU,SAAZ,CAAvB;;AACA,cAAIG,KAAJ,EAAW;AACP,gBAAI,CAACN,SAAL,EAAgBA,SAAS,GAAGlF,IAAI,CAACwF,KAAD,EAAQ,WAAR,CAAhB;AAChB,gBAAI,CAACL,SAAL,EAAgBA,SAAS,GAAGnF,IAAI,CAACwF,KAAD,EAAQ,WAAR,CAAhB;AACnB;AACJ;;AACD,YAAIV,WAAJ,EAAiBH,UAAU,CAACG,WAAX,GAAyBA,WAAzB;;AACjB,YAAIC,QAAJ,EAAc;AACV,cAAIU,KAAK,GAAGjF,OAAO,CAACR,IAAI,CAAC+E,QAAD,EAAW,OAAX,CAAL,CAAnB;AACA,cAAIW,GAAG,GAAGlF,OAAO,CAACR,IAAI,CAAC+E,QAAD,EAAW,KAAX,CAAL,CAAjB;AACAJ,oBAAU,CAACgB,QAAX,GAAsB;AAAEF,iBAAK,EAAEA,KAAT;AAAgBC,eAAG,EAAEA;AAArB,WAAtB;AACH;;AACD,YAAIV,SAAJ,EAAe;AACXL,oBAAU,CAACiB,SAAX,GAAuBpF,OAAO,CAACR,IAAI,CAACgF,SAAD,EAAY,MAAZ,CAAL,CAA9B;AACH;;AACD,YAAIE,SAAJ,EAAe;AACX,cAAIW,UAAU,GAAGrC,QAAQ,CAAChD,OAAO,CAACR,IAAI,CAACkF,SAAD,EAAY,OAAZ,CAAL,CAAR,CAAzB;AAAA,cACIzB,KAAK,GAAGoC,UAAU,CAAC,CAAD,CADtB;AAAA,cAEInC,OAAO,GAAGmC,UAAU,CAAC,CAAD,CAFxB;AAAA,cAGIC,KAAK,GAAG/F,UAAU,CAACS,OAAO,CAACR,IAAI,CAACkF,SAAD,EAAY,OAAZ,CAAL,CAAR,CAHtB;AAIA,cAAIzB,KAAJ,EAAWkB,UAAU,CAACoB,MAAX,GAAoBtC,KAApB;AACX,cAAI,CAAC/B,KAAK,CAACgC,OAAD,CAAV,EAAqBiB,UAAU,CAAC,gBAAD,CAAV,GAA+BjB,OAA/B;AACrB,cAAI,CAAChC,KAAK,CAACoE,KAAD,CAAV,EAAmBnB,UAAU,CAAC,cAAD,CAAV,GAA6BmB,KAA7B;AACtB;;AACD,YAAIX,SAAJ,EAAe;AACX,cAAIa,UAAU,GAAGxC,QAAQ,CAAChD,OAAO,CAACR,IAAI,CAACmF,SAAD,EAAY,OAAZ,CAAL,CAAR,CAAzB;AAAA,cACIc,MAAM,GAAGD,UAAU,CAAC,CAAD,CADvB;AAAA,cAEIE,QAAQ,GAAGF,UAAU,CAAC,CAAD,CAFzB;AAAA,cAGIG,IAAI,GAAG3F,OAAO,CAACR,IAAI,CAACmF,SAAD,EAAY,MAAZ,CAAL,CAHlB;AAAA,cAIIiB,OAAO,GAAG5F,OAAO,CAACR,IAAI,CAACmF,SAAD,EAAY,SAAZ,CAAL,CAJrB;AAKA,cAAIc,MAAJ,EAAYtB,UAAU,CAACwB,IAAX,GAAkBF,MAAlB;AACZ,cAAI,CAACvE,KAAK,CAACwE,QAAD,CAAV,EAAsBvB,UAAU,CAAC,cAAD,CAAV,GAA6BuB,QAA7B;AACtB,cAAIC,IAAJ,EAAUxB,UAAU,CAAC,cAAD,CAAV,GAA6BwB,IAAI,KAAK,GAAT,GAAexB,UAAU,CAAC,cAAD,CAAV,IAA8B,CAA7C,GAAiD,CAA9E;AACV,cAAIyB,OAAJ,EAAazB,UAAU,CAAC,gBAAD,CAAV,GAA+ByB,OAAO,KAAK,GAAZ,GAAkBzB,UAAU,CAAC,gBAAD,CAAV,IAAgC,CAAlD,GAAsD,CAArF;AAChB;;AACD,YAAIM,YAAJ,EAAkB;AACd,cAAIoB,KAAK,GAAG5G,GAAG,CAACwF,YAAD,EAAe,MAAf,CAAf;AAAA,cACIqB,WAAW,GAAG7G,GAAG,CAACwF,YAAD,EAAe,YAAf,CADrB;;AAGA,eAAK3F,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG+G,KAAK,CAAChH,MAAtB,EAA8BC,CAAC,EAA/B,EAAmC;AAC/BqF,sBAAU,CAAC0B,KAAK,CAAC/G,CAAD,CAAL,CAASO,YAAT,CAAsB,MAAtB,CAAD,CAAV,GAA4CW,OAAO,CAACR,IAAI,CAACqG,KAAK,CAAC/G,CAAD,CAAN,EAAW,OAAX,CAAL,CAAnD;AACH;;AACD,eAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGgH,WAAW,CAACjH,MAA5B,EAAoCC,CAAC,EAArC,EAAyC;AACrCqF,sBAAU,CAAC2B,WAAW,CAAChH,CAAD,CAAX,CAAeO,YAAf,CAA4B,MAA5B,CAAD,CAAV,GAAkDW,OAAO,CAAC8F,WAAW,CAAChH,CAAD,CAAZ,CAAzD;AACH;AACJ;;AACD,YAAI8F,UAAJ,EAAgB;AACZT,oBAAU,CAACS,UAAX,GAAwB5E,OAAO,CAAC4E,UAAD,CAA/B;AACH;;AACD,YAAIV,aAAa,CAACH,UAAd,CAAyBlF,MAA7B,EAAqC;AACjCsF,oBAAU,CAACJ,UAAX,GAAyBG,aAAa,CAACH,UAAd,CAAyBlF,MAAzB,KAAoC,CAArC,GACpBqF,aAAa,CAACH,UAAd,CAAyB,CAAzB,CADoB,GACUG,aAAa,CAACH,UADhD;AAEH;;AACD,YAAIgC,OAAO,GAAG;AACV1E,cAAI,EAAE,SADI;AAEV2E,kBAAQ,EAAG9B,aAAa,CAACJ,KAAd,CAAoBjF,MAApB,KAA+B,CAAhC,GAAqCqF,aAAa,CAACJ,KAAd,CAAoB,CAApB,CAArC,GAA8D;AACpEzC,gBAAI,EAAE,oBAD8D;AAEpE4E,sBAAU,EAAE/B,aAAa,CAACJ;AAF0C,WAF9D;AAMVK,oBAAU,EAAEA;AANF,SAAd;AAQA,YAAI/E,IAAI,CAACmE,IAAD,EAAO,IAAP,CAAR,EAAsBwC,OAAO,CAACG,EAAR,GAAa9G,IAAI,CAACmE,IAAD,EAAO,IAAP,CAAjB;AACtB,eAAO,CAACwC,OAAD,CAAP;AACH;;AACD,aAAO/D,EAAP;AACH,KAvMG;AAwMJmE,OAAG,EAAE,aAASxI,GAAT,EAAc;AACf,UAAImB,CAAJ;AAAA,UACIsH,MAAM,GAAGnH,GAAG,CAACtB,GAAD,EAAM,KAAN,CADhB;AAAA,UAEI0I,MAAM,GAAGpH,GAAG,CAACtB,GAAD,EAAM,KAAN,CAFhB;AAAA,UAGI2I,SAAS,GAAGrH,GAAG,CAACtB,GAAD,EAAM,KAAN,CAHnB;AAAA,UAII;AACAqE,QAAE,GAAGZ,EAAE,EALX;AAAA,UAMI2E,OANJ;;AAOA,WAAKjH,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGsH,MAAM,CAACvH,MAAvB,EAA+BC,CAAC,EAAhC,EAAoC;AAChCiH,eAAO,GAAGQ,QAAQ,CAACH,MAAM,CAACtH,CAAD,CAAP,CAAlB;AACA,YAAIiH,OAAJ,EAAa/D,EAAE,CAACV,QAAH,CAAYX,IAAZ,CAAiBoF,OAAjB;AAChB;;AACD,WAAKjH,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGuH,MAAM,CAACxH,MAAvB,EAA+BC,CAAC,EAAhC,EAAoC;AAChCiH,eAAO,GAAGS,QAAQ,CAACH,MAAM,CAACvH,CAAD,CAAP,CAAlB;AACA,YAAIiH,OAAJ,EAAa/D,EAAE,CAACV,QAAH,CAAYX,IAAZ,CAAiBoF,OAAjB;AAChB;;AACD,WAAKjH,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGwH,SAAS,CAACzH,MAA1B,EAAkCC,CAAC,EAAnC,EAAuC;AACnCkD,UAAE,CAACV,QAAH,CAAYX,IAAZ,CAAiB8F,QAAQ,CAACH,SAAS,CAACxH,CAAD,CAAV,CAAzB;AACH;;AACD,eAAS4H,SAAT,CAAmBC,IAAnB,EAAyBC,SAAzB,EAAoC;AAChC,YAAIC,GAAG,GAAG5H,GAAG,CAAC0H,IAAD,EAAOC,SAAP,CAAb;AAAA,YACIE,IAAI,GAAG,EADX;AAAA,YAEIrD,KAAK,GAAG,EAFZ;AAAA,YAGIsD,UAAU,GAAG,EAHjB;AAAA,YAIIrE,CAAC,GAAGmE,GAAG,CAAChI,MAJZ;AAKA,YAAI6D,CAAC,GAAG,CAAR,EAAW,OAAO,EAAP,CANqB,CAMT;;AACvB,aAAK,IAAI5D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4D,CAApB,EAAuB5D,CAAC,EAAxB,EAA4B;AACxB,cAAIkI,CAAC,GAAGpG,SAAS,CAACiG,GAAG,CAAC/H,CAAD,CAAJ,CAAjB;AACAgI,cAAI,CAACnG,IAAL,CAAUqG,CAAC,CAAC7F,WAAZ;AACA,cAAI6F,CAAC,CAAChG,IAAN,EAAYyC,KAAK,CAAC9C,IAAN,CAAWqG,CAAC,CAAChG,IAAb;AACZ,cAAIgG,CAAC,CAACjG,SAAN,EAAiBgG,UAAU,CAACpG,IAAX,CAAgBqG,CAAC,CAACjG,SAAlB;AACpB;;AACD,eAAO;AACH+F,cAAI,EAAEA,IADH;AAEHrD,eAAK,EAAEA,KAFJ;AAGHsD,oBAAU,EAAEA;AAHT,SAAP;AAKH;;AACD,eAASR,QAAT,CAAkBI,IAAlB,EAAwB;AACpB,YAAIM,QAAQ,GAAGhI,GAAG,CAAC0H,IAAD,EAAO,QAAP,CAAlB;AAAA,YACI1C,KAAK,GAAG,EADZ;AAAA,YAEIR,KAAK,GAAG,EAFZ;AAAA,YAGIsD,UAAU,GAAG,EAHjB;AAAA,YAIID,IAJJ;;AAKA,aAAK,IAAIhI,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmI,QAAQ,CAACpI,MAA7B,EAAqCC,CAAC,EAAtC,EAA0C;AACtCgI,cAAI,GAAGJ,SAAS,CAACO,QAAQ,CAACnI,CAAD,CAAT,EAAc,OAAd,CAAhB;;AACA,cAAIgI,IAAJ,EAAU;AACN,gBAAIA,IAAI,CAACA,IAAT,EAAe7C,KAAK,CAACtD,IAAN,CAAWmG,IAAI,CAACA,IAAhB;AACf,gBAAIA,IAAI,CAACrD,KAAL,IAAcqD,IAAI,CAACrD,KAAL,CAAW5E,MAA7B,EAAqC4E,KAAK,CAAC9C,IAAN,CAAWmG,IAAI,CAACrD,KAAhB;AACrC,gBAAIqD,IAAI,CAACC,UAAL,IAAmBD,IAAI,CAACC,UAAL,CAAgBlI,MAAvC,EAA+CkI,UAAU,CAACpG,IAAX,CAAgBmG,IAAI,CAACC,UAArB;AAClD;AACJ;;AACD,YAAI9C,KAAK,CAACpF,MAAN,KAAiB,CAArB,EAAwB;AACxB,YAAIsF,UAAU,GAAG+C,aAAa,CAACP,IAAD,CAA9B;AACA,YAAIlD,KAAK,CAAC5E,MAAV,EAAkBsF,UAAU,CAACJ,UAAX,GAAwBE,KAAK,CAACpF,MAAN,KAAiB,CAAjB,GAAqB4E,KAAK,CAAC,CAAD,CAA1B,GAAgCA,KAAxD;AAClB,YAAIsD,UAAU,CAAClI,MAAf,EAAuBsF,UAAU,CAAC4C,UAAX,GAAwB9C,KAAK,CAACpF,MAAN,KAAiB,CAAjB,GAAqBkI,UAAU,CAAC,CAAD,CAA/B,GAAqCA,UAA7D;AACvB,eAAO;AACH1F,cAAI,EAAE,SADH;AAEH8C,oBAAU,EAAEA,UAFT;AAGH6B,kBAAQ,EAAE;AACN3E,gBAAI,EAAE4C,KAAK,CAACpF,MAAN,KAAiB,CAAjB,GAAqB,YAArB,GAAoC,iBADpC;AAENsC,uBAAW,EAAE8C,KAAK,CAACpF,MAAN,KAAiB,CAAjB,GAAqBoF,KAAK,CAAC,CAAD,CAA1B,GAAgCA;AAFvC;AAHP,SAAP;AAQH;;AACD,eAASuC,QAAT,CAAkBG,IAAlB,EAAwB;AACpB,YAAIG,IAAI,GAAGJ,SAAS,CAACC,IAAD,EAAO,OAAP,CAApB;AACA,YAAI,CAACG,IAAI,CAACA,IAAV,EAAgB;AAChB,YAAIK,QAAQ,GAAG;AACX9F,cAAI,EAAE,SADK;AAEX8C,oBAAU,EAAE+C,aAAa,CAACP,IAAD,CAFd;AAGXX,kBAAQ,EAAE;AACN3E,gBAAI,EAAE,YADA;AAENF,uBAAW,EAAE2F,IAAI,CAACA;AAFZ;AAHC,SAAf;AAQA,eAAOK,QAAP;AACH;;AACD,eAASV,QAAT,CAAkBE,IAAlB,EAAwB;AACpB,YAAIS,IAAI,GAAGF,aAAa,CAACP,IAAD,CAAxB;AACAtG,cAAM,CAAC+G,IAAD,EAAOlH,QAAQ,CAACyG,IAAD,EAAO,CAAC,KAAD,EAAQ,MAAR,CAAP,CAAf,CAAN;AACA,eAAO;AACHtF,cAAI,EAAE,SADH;AAEH8C,oBAAU,EAAEiD,IAFT;AAGHpB,kBAAQ,EAAE;AACN3E,gBAAI,EAAE,OADA;AAENF,uBAAW,EAAEP,SAAS,CAAC+F,IAAD,CAAT,CAAgBxF;AAFvB;AAHP,SAAP;AAQH;;AACD,eAAS+F,aAAT,CAAuBP,IAAvB,EAA6B;AACzB,YAAIS,IAAJ,EAAUC,KAAV;AACAD,YAAI,GAAGlH,QAAQ,CAACyG,IAAD,EAAO,CAAC,MAAD,EAAS,KAAT,EAAgB,MAAhB,EAAwB,MAAxB,EAAgC,UAAhC,CAAP,CAAf;AACAU,aAAK,GAAGpI,GAAG,CAAC0H,IAAD,EAAO,MAAP,CAAX;AACA,YAAIU,KAAK,CAACxI,MAAV,EAAkBuI,IAAI,CAACC,KAAL,GAAa,EAAb;;AAClB,aAAK,IAAIvI,CAAC,GAAG,CAAR,EAAWwI,IAAhB,EAAsBxI,CAAC,GAAGuI,KAAK,CAACxI,MAAhC,EAAwCC,CAAC,EAAzC,EAA6C;AACzCwI,cAAI,GAAG;AAAEC,gBAAI,EAAEnI,IAAI,CAACiI,KAAK,CAACvI,CAAD,CAAN,EAAW,MAAX;AAAZ,WAAP;AACAuB,gBAAM,CAACiH,IAAD,EAAOpH,QAAQ,CAACmH,KAAK,CAACvI,CAAD,CAAN,EAAW,CAAC,MAAD,EAAS,MAAT,CAAX,CAAf,CAAN;AACAsI,cAAI,CAACC,KAAL,CAAW1G,IAAX,CAAgB2G,IAAhB;AACH;;AACD,eAAOF,IAAP;AACH;;AACD,aAAOpF,EAAP;AACH;AA/SG,GAAR;AAiTA,SAAOD,CAAP;AACH,CAnZe,EAAhB;;AAqZA,IAAI,IAAJ,EAAmCyF,MAAM,CAAC/F,OAAP,GAAiB/D,SAAjB,C;;;;;;;ACrZnC;AACA,2BAA2B;AAC3B;;AAEA;AACA;AACA;AACA,yDAAyD;AACzD;AACA;AACA;AACA,0CAA0C;AAC1C,gDAAgD;AAChD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,mBAAmB;AAC/D;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,EAAE;AACF;AACA,EAAE;AACF;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,EAAE;AACF;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA,EAAE;AACF;AACA;AACA,EAAE;AACF;AACA,gCAAgC;AAChC;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;;AAEF;AACA;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC,CAAC;;AAED,kHAAkH;AAClH;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC;;AAED;AACA,iBAAiB,mBAAO,CAAC,CAAY;AACrC,UAAU,mBAAO,CAAC,CAAO;AACzB;AACA;AACA,oDAAoD,mBAAO,CAAC,CAAO;AACnE,wBAAwB,mBAAO,CAAC,CAAO;AACvC;AACA;AACA;;;;;;;AC5PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AClPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd,eAAe;AACf,mBAAmB;AACnB,aAAa;AACb,4BAA4B;AAC5B,mBAAmB;AACnB,oBAAoB;AACpB,oBAAoB;;AAEpB;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,uB;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA,wDAAwD;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,oBAAoB,8BAA8B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4HAA4H;AAC5H;AACA,WAAW;AACX;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C,WAAW;AACX,mBAAmB,MAAM;AACzB;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;;;AAIA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,YAAY,OAAO;AACnB,YAAY,OAAO;AACnB,YAAY,OAAO;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA,2DAA2D;AAC3D;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD;AACnD;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,IAAI;AACJ,kDAAkD;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,sDAAsD;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA,eAAe;AACf;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,IAAI,KAAK;AACT;AACA;AACA;AACA,wBAAwB;AACxB,yBAAyB;AACzB,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,IAAI;AACJ;;AAEA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,GAAG,KAAK;AACZ,gCAAgC;AAChC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,I;AACA;AACA;AACA,sBAAsB;AACtB;AACA,wDAAwD;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,KAAK;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,EAAE;AACF;AACA,0BAA0B,yBAAyB;AACnD,wBAAwB,uBAAuB;AAC/C,sBAAsB,qBAAqB;AAC3C,oBAAoB,mBAAmB;AACvC,sBAAsB;AACtB;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,IAAI;AACJ,uBAAuB,0DAA0D;AACjF;AACA,wBAAwB;AACxB;;;;AAIA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;;;;;;;;;;ACjoBA,CAAC,UAAS+J,CAAT,EAAW;AAAC,MAAG,QAAOhG,OAAP,sGAAOA,OAAP,OAAiB,QAAjB,IAA2B,OAAO+F,MAAP,KAAgB,WAA9C,EAA0D;AAACA,UAAM,CAAC/F,OAAP,GAAegG,CAAC,EAAhB;AAAmB,GAA9E,MAAmF,IAAG,OAAOC,MAAP,KAAgB,UAAhB,IAA4BA,uBAA/B,EAA0C;AAACA,UAAM,CAAC,EAAD,EAAID,CAAJ,CAAN;AAAa,GAAxD,MAA4D;AAAC,QAAIE,CAAJ;;AAAM,QAAG,OAAOC,MAAP,KAAgB,WAAnB,EAA+B;AAACD,OAAC,GAACC,MAAF;AAAS,KAAzC,MAA8C,IAAG,OAAOC,MAAP,KAAgB,WAAnB,EAA+B;AAACF,OAAC,GAACE,MAAF;AAAS,KAAzC,MAA8C,IAAG,OAAOC,IAAP,KAAc,WAAjB,EAA6B;AAACH,OAAC,GAACG,IAAF;AAAO,KAArC,MAAyC;AAACH,OAAC,GAAC,IAAF;AAAO;;AAAAA,KAAC,CAAC1L,KAAF,GAAUwL,CAAC,EAAX;AAAc;AAAC,CAA/T,EAAiU,YAAU;AAAC,MAAIC,MAAJ,EAAWF,MAAX,EAAkB/F,OAAlB;AAA0B,SAAQ,SAASR,CAAT,CAAWc,CAAX,EAAatC,CAAb,EAAesI,CAAf,EAAiB;AAAC,aAASC,CAAT,CAAWjI,CAAX,EAAakI,CAAb,EAAe;AAAC,UAAG,CAACxI,CAAC,CAACM,CAAD,CAAL,EAAS;AAAC,YAAG,CAACgC,CAAC,CAAChC,CAAD,CAAL,EAAS;AAAC,cAAImI,CAAC,GAAC,OAAOlM,OAAP,IAAgB,UAAhB,IAA4BA,OAAlC;AAA0C,cAAG,CAACiM,CAAD,IAAIC,uBAAP,EAAS,OAAOA,OAAC,CAACnI,CAAD,EAAG,CAAC,CAAJ,CAAR;AAAe,cAAGjB,CAAH,EAAK,OAAOA,CAAC,CAACiB,CAAD,EAAG,CAAC,CAAJ,CAAR;AAAe,cAAI0H,CAAC,GAAC,IAAIU,KAAJ,CAAU,yBAAuBpI,CAAvB,GAAyB,GAAnC,CAAN;AAA8C,gBAAM0H,CAAC,CAACW,IAAF,GAAO,kBAAP,EAA0BX,CAAhC;AAAkC;;AAAA,YAAI/E,CAAC,GAACjD,CAAC,CAACM,CAAD,CAAD,GAAK;AAAC0B,iBAAO,EAAC;AAAT,SAAX;AAAwBM,SAAC,CAAChC,CAAD,CAAD,CAAK,CAAL,EAAQsI,IAAR,CAAa3F,CAAC,CAACjB,OAAf,EAAuB,UAASR,CAAT,EAAW;AAAC,cAAIxB,CAAC,GAACsC,CAAC,CAAChC,CAAD,CAAD,CAAK,CAAL,EAAQkB,CAAR,CAAN;AAAiB,iBAAO+G,CAAC,CAACvI,CAAC,GAACA,CAAD,GAAGwB,CAAL,CAAR;AAAgB,SAApE,EAAqEyB,CAArE,EAAuEA,CAAC,CAACjB,OAAzE,EAAiFR,CAAjF,EAAmFc,CAAnF,EAAqFtC,CAArF,EAAuFsI,CAAvF;AAA0F;;AAAA,aAAOtI,CAAC,CAACM,CAAD,CAAD,CAAK0B,OAAZ;AAAoB;;AAAA,QAAI3C,CAAC,GAAC,OAAO9C,OAAP,IAAgB,UAAhB,IAA4BA,OAAlC;;AAA0C,SAAI,IAAI+D,CAAC,GAAC,CAAV,EAAYA,CAAC,GAACgI,CAAC,CAAClJ,MAAhB,EAAuBkB,CAAC,EAAxB;AAA2BiI,OAAC,CAACD,CAAC,CAAChI,CAAD,CAAF,CAAD;AAA3B;;AAAmC,WAAOiI,CAAP;AAAS,GAAzb,CAA2b;AAAC,OAAE,CAAC,UAAShM,OAAT,EAAiBwL,MAAjB,EAAwB/F,OAAxB,EAAgC;AAC50B,UAAI6G,MAAM,GAAGtM,OAAO,CAAC,QAAD,CAApB;AAAA,UACIuM,GAAG,GAAGD,MAAM,CAACC,GADjB;AAAA,UAEIC,MAAM,GAAGF,MAAM,CAACE,MAFpB;;AAIAhB,YAAM,CAAC/F,OAAP,GAAiB,SAASxF,KAAT,CAAeG,OAAf,EAAwBC,OAAxB,EAAiC;AAE9CA,eAAO,GAAGA,OAAO,IAAI;AACjBoM,sBAAY,EAAE5G,SADG;AAEjB6G,6BAAmB,EAAE7G,SAFJ;AAGjBuC,cAAI,EAAE,MAHW;AAIjBE,qBAAW,EAAE,aAJI;AAKjBqE,qBAAW,EAAE,KALI;AAMjBvD,mBAAS,EAAE;AANM,SAArB;AASA,eAAO,2CACHmD,GAAG,CAAC,KAAD,EACCA,GAAG,CAAC,UAAD,EACCE,YAAY,CAACpM,OAAD,CAAZ,GACAqM,mBAAmB,CAACrM,OAAD,CADnB,GAEAkH,IAAI,CAACnH,OAAD,EAAUC,OAAV,CAHL,CADJ,EAKO,CAAC,CAAC,OAAD,EAAU,gCAAV,CAAD,CALP,CADP;AAOH,OAlBD;;AAoBA,eAAS0J,OAAT,CAAiB1J,OAAjB,EAA0BuM,gBAA1B,EAA4C;AACxC,eAAO,UAASC,CAAT,EAAY;AACf,cAAI,CAACA,CAAC,CAAC1E,UAAH,IAAiB,CAAC6B,QAAQ,CAAC8C,KAAT,CAAeD,CAAC,CAAC7C,QAAjB,CAAtB,EAAkD,OAAO,EAAP;AAClD,cAAI+C,cAAc,GAAG/C,QAAQ,CAACgD,GAAT,CAAaH,CAAC,CAAC7C,QAAf,CAArB;AACA,cAAI,CAAC+C,cAAL,EAAqB,OAAO,EAAP;AAErB,cAAIE,eAAe,GAAG,EAAtB;AAAA,cACIC,cAAc,GAAG,EADrB;;AAEA,cAAI7M,OAAO,CAACsM,WAAZ,EAAyB;AACrB,gBAAI9D,SAAS,GAAGsE,SAAS,CAACN,CAAC,CAAC1E,UAAH,CAAzB;;AACA,gBAAIU,SAAJ,EAAe;AACX,kBAAImB,QAAQ,CAACoD,OAAT,CAAiBP,CAAC,CAAC7C,QAAnB,KAAgCqD,cAAc,CAACR,CAAC,CAAC1E,UAAH,CAAlD,EAAkE;AAC9D,oBAAIyE,gBAAgB,CAACU,OAAjB,CAAyBzE,SAAzB,MAAwC,CAAC,CAA7C,EAAgD;AAC5CoE,iCAAe,GAAGM,WAAW,CAACV,CAAC,CAAC1E,UAAH,EAAeU,SAAf,CAA7B;AACA+D,kCAAgB,CAACjI,IAAjB,CAAsBkE,SAAtB;AACH;;AACDqE,8BAAc,GAAGX,GAAG,CAAC,UAAD,EAAa,MAAM1D,SAAnB,CAApB;AACH,eAND,MAMO,IAAI,CAACmB,QAAQ,CAACwD,SAAT,CAAmBX,CAAC,CAAC7C,QAArB,KAAkCA,QAAQ,CAACyD,MAAT,CAAgBZ,CAAC,CAAC7C,QAAlB,CAAnC,KACP0D,sBAAsB,CAACb,CAAC,CAAC1E,UAAH,CADnB,EACmC;AACtC,oBAAIyE,gBAAgB,CAACU,OAAjB,CAAyBzE,SAAzB,MAAwC,CAAC,CAA7C,EAAgD;AAC5CoE,iCAAe,GAAGU,mBAAmB,CAACd,CAAC,CAAC1E,UAAH,EAAeU,SAAf,CAArC;AACA+D,kCAAgB,CAACjI,IAAjB,CAAsBkE,SAAtB;AACH;;AACDqE,8BAAc,GAAGX,GAAG,CAAC,UAAD,EAAa,MAAM1D,SAAnB,CAApB;AACH,eAdU,CAeX;;AACH;AACJ;;AAED,iBAAOoE,eAAe,GAAGV,GAAG,CAAC,WAAD,EACxBnE,IAAI,CAACyE,CAAC,CAAC1E,UAAH,EAAe9H,OAAf,CAAJ,GACAiI,WAAW,CAACuE,CAAC,CAAC1E,UAAH,EAAe9H,OAAf,CADX,GAEAuN,YAAY,CAACf,CAAC,CAAC1E,UAAH,CAFZ,GAGAiB,SAAS,CAACyD,CAAC,CAAC1E,UAAH,EAAe9H,OAAf,CAHT,GAIA0M,cAJA,GAKAG,cANwB,CAA5B;AAOH,SAnCD;AAoCH;;AAED,eAAS3F,IAAT,CAAcsF,CAAd,EAAiBxM,OAAjB,EAA0B;AACtB,YAAI,CAACwM,CAAC,CAACxH,IAAP,EAAa,OAAO,EAAP;AACb,YAAIuH,gBAAgB,GAAG,EAAvB;;AAEA,gBAAQC,CAAC,CAACxH,IAAV;AACI,eAAK,mBAAL;AACI,gBAAI,CAACwH,CAAC,CAACvH,QAAP,EAAiB,OAAO,EAAP;AACjB,mBAAOuH,CAAC,CAACvH,QAAF,CAAWuI,GAAX,CAAe9D,OAAO,CAAC1J,OAAD,EAAUuM,gBAAV,CAAtB,EAAmDkB,IAAnD,CAAwD,EAAxD,CAAP;;AACJ,eAAK,SAAL;AACI,mBAAO/D,OAAO,CAAC1J,OAAD,EAAUuM,gBAAV,CAAP,CAAmCC,CAAnC,CAAP;;AACJ;AACI,mBAAO9C,OAAO,CAAC1J,OAAD,EAAUuM,gBAAV,CAAP,CAAmC;AACtCvH,kBAAI,EAAE,SADgC;AAEtC2E,sBAAQ,EAAE6C,CAF4B;AAGtC1E,wBAAU,EAAE;AAH0B,aAAnC,CAAP;AAPR;AAaH;;AAED,eAASsE,YAAT,CAAsBpM,OAAtB,EAA+B;AAC3B,eAAQA,OAAO,CAACoM,YAAR,KAAyB5G,SAA1B,GAAuC0G,GAAG,CAAC,MAAD,EAASlM,OAAO,CAACoM,YAAjB,CAA1C,GAA2E,EAAlF;AACH;;AAED,eAASC,mBAAT,CAA6BrM,OAA7B,EAAsC;AAClC,eAAQA,OAAO,CAACqM,mBAAR,KAAgC7G,SAAjC,GAA8C0G,GAAG,CAAC,aAAD,EAAgBlM,OAAO,CAACqM,mBAAxB,CAAjD,GAAgG,EAAvG;AACH;;AAED,eAAStE,IAAT,CAAcyE,CAAd,EAAiBxM,OAAjB,EAA0B;AACtB,eAAOwM,CAAC,CAACxM,OAAO,CAAC+H,IAAT,CAAD,GAAkBmE,GAAG,CAAC,MAAD,EAASC,MAAM,CAACK,CAAC,CAACxM,OAAO,CAAC+H,IAAT,CAAF,CAAf,CAArB,GAAyD,EAAhE;AACH;;AAED,eAASE,WAAT,CAAqBuE,CAArB,EAAwBxM,OAAxB,EAAiC;AAC7B,eAAOwM,CAAC,CAACxM,OAAO,CAACiI,WAAT,CAAD,GAAyBiE,GAAG,CAAC,aAAD,EAAgBC,MAAM,CAACK,CAAC,CAACxM,OAAO,CAACiI,WAAT,CAAF,CAAtB,CAA5B,GAA8E,EAArF;AACH;;AAED,eAASc,SAAT,CAAmByD,CAAnB,EAAsBxM,OAAtB,EAA+B;AAC3B,eAAOwM,CAAC,CAACxM,OAAO,CAAC+I,SAAT,CAAD,GAAuBmD,GAAG,CAAC,WAAD,EAAcA,GAAG,CAAC,MAAD,EAASC,MAAM,CAACK,CAAC,CAACxM,OAAO,CAAC+I,SAAT,CAAF,CAAf,CAAjB,CAA1B,GAAqF,EAA5F;AACH,OArG20B,CAuG50B;AACA;AACA;;;AACA,UAAIY,QAAQ,GAAG;AACX+D,aAAK,EAAE,eAASlB,CAAT,EAAY;AACf,iBAAON,GAAG,CAAC,OAAD,EAAUA,GAAG,CAAC,aAAD,EAAgBM,CAAC,CAAC1H,WAAF,CAAc2I,IAAd,CAAmB,GAAnB,CAAhB,CAAb,CAAV;AACH,SAHU;AAIXE,kBAAU,EAAE,oBAASnB,CAAT,EAAY;AACpB,iBAAON,GAAG,CAAC,YAAD,EAAeA,GAAG,CAAC,aAAD,EAAgB0B,UAAU,CAACpB,CAAC,CAAC1H,WAAH,CAA1B,CAAlB,CAAV;AACH,SANU;AAOX+I,eAAO,EAAE,iBAASrB,CAAT,EAAY;AACjB,cAAI,CAACA,CAAC,CAAC1H,WAAF,CAActC,MAAnB,EAA2B,OAAO,EAAP;;AAC3B,cAAIsL,KAAK,GAAGtB,CAAC,CAAC1H,WAAF,CAAc,CAAd,CAAZ;AAAA,cACIiJ,KAAK,GAAGvB,CAAC,CAAC1H,WAAF,CAAckJ,KAAd,CAAoB,CAApB,CADZ;AAAA,cAEIC,SAAS,GAAG/B,GAAG,CAAC,iBAAD,EACXA,GAAG,CAAC,YAAD,EAAeA,GAAG,CAAC,aAAD,EAAgB0B,UAAU,CAACE,KAAD,CAA1B,CAAlB,CADQ,CAFnB;AAAA,cAIII,UAAU,GAAGH,KAAK,CAACP,GAAN,CAAU,UAAS/K,CAAT,EAAY;AAC/B,mBAAOyJ,GAAG,CAAC,iBAAD,EACNA,GAAG,CAAC,YAAD,EAAeA,GAAG,CAAC,aAAD,EAAgB0B,UAAU,CAACnL,CAAD,CAA1B,CAAlB,CADG,CAAV;AAEH,WAHY,EAGVgL,IAHU,CAGL,EAHK,CAJjB;;AAQA,iBAAOvB,GAAG,CAAC,SAAD,EAAY+B,SAAS,GAAGC,UAAxB,CAAV;AACH,SAlBU;AAmBXC,kBAAU,EAAE,oBAAS3B,CAAT,EAAY;AACpB,cAAI,CAACA,CAAC,CAAC1H,WAAF,CAActC,MAAnB,EAA2B,OAAO,EAAP;AAC3B,iBAAO0J,GAAG,CAAC,eAAD,EAAkBM,CAAC,CAAC1H,WAAF,CAAc0I,GAAd,CAAkB,UAAS7C,CAAT,EAAY;AACtD,mBAAOhB,QAAQ,CAAC+D,KAAT,CAAe;AAAE5I,yBAAW,EAAE6F;AAAf,aAAf,CAAP;AACH,WAF2B,EAEzB8C,IAFyB,CAEpB,EAFoB,CAAlB,CAAV;AAGH,SAxBU;AAyBXW,oBAAY,EAAE,sBAAS5B,CAAT,EAAY;AACtB,cAAI,CAACA,CAAC,CAAC1H,WAAF,CAActC,MAAnB,EAA2B,OAAO,EAAP;AAC3B,iBAAO0J,GAAG,CAAC,eAAD,EAAkBM,CAAC,CAAC1H,WAAF,CAAc0I,GAAd,CAAkB,UAAS7C,CAAT,EAAY;AACtD,mBAAOhB,QAAQ,CAACkE,OAAT,CAAiB;AAAE/I,yBAAW,EAAE6F;AAAf,aAAjB,CAAP;AACH,WAF2B,EAEzB8C,IAFyB,CAEpB,EAFoB,CAAlB,CAAV;AAGH,SA9BU;AA+BXY,uBAAe,EAAE,yBAAS7B,CAAT,EAAY;AACzB,cAAI,CAACA,CAAC,CAAC1H,WAAF,CAActC,MAAnB,EAA2B,OAAO,EAAP;AAC3B,iBAAO0J,GAAG,CAAC,eAAD,EAAkBM,CAAC,CAAC1H,WAAF,CAAc0I,GAAd,CAAkB,UAAS7C,CAAT,EAAY;AACtD,mBAAOhB,QAAQ,CAACgE,UAAT,CAAoB;AAAE7I,yBAAW,EAAE6F;AAAf,aAApB,CAAP;AACH,WAF2B,EAEzB8C,IAFyB,CAEpB,EAFoB,CAAlB,CAAV;AAGH,SApCU;AAqCXa,0BAAkB,EAAE,4BAAS9B,CAAT,EAAY;AAC5B,iBAAON,GAAG,CAAC,eAAD,EACNM,CAAC,CAAC5C,UAAF,CAAa4D,GAAb,CAAiB7D,QAAQ,CAACgD,GAA1B,EAA+Bc,IAA/B,CAAoC,EAApC,CADM,CAAV;AAEH,SAxCU;AAyCXhB,aAAK,EAAE,eAASD,CAAT,EAAY;AACf,iBAAOA,CAAC,IAAIA,CAAC,CAACxH,IAAP,KAAgBwH,CAAC,CAAC1H,WAAF,IACnB0H,CAAC,CAACxH,IAAF,KAAW,oBAAX,IAAmCwH,CAAC,CAAC5C,UAArC,IAAmD4C,CAAC,CAAC5C,UAAF,CAAa2E,KAAb,CAAmB5E,QAAQ,CAAC8C,KAA5B,CADhD,CAAP;AAEH,SA5CU;AA6CXE,WAAG,EAAE,aAASH,CAAT,EAAY;AACb,cAAI7C,QAAQ,CAAC6C,CAAC,CAACxH,IAAH,CAAZ,EAAsB;AAClB,mBAAO2E,QAAQ,CAAC6C,CAAC,CAACxH,IAAH,CAAR,CAAiBwH,CAAjB,CAAP;AACH,WAFD,MAEO;AACH,mBAAO,EAAP;AACH;AACJ,SAnDU;AAoDXO,eAAO,EAAE,iBAASP,CAAT,EAAY;AACjB,iBAAOA,CAAC,CAACxH,IAAF,KAAW,OAAX,IACPwH,CAAC,CAACxH,IAAF,KAAW,YADX;AAEH,SAvDU;AAwDXmI,iBAAS,EAAE,mBAASX,CAAT,EAAY;AACnB,iBAAOA,CAAC,CAACxH,IAAF,KAAW,SAAX,IACPwH,CAAC,CAACxH,IAAF,KAAW,cADX;AAEH,SA3DU;AA4DXoI,cAAM,EAAE,gBAASZ,CAAT,EAAY;AAChB,iBAAOA,CAAC,CAACxH,IAAF,KAAW,YAAX,IACPwH,CAAC,CAACxH,IAAF,KAAW,iBADX;AAEH;AA/DU,OAAf;;AAkEA,eAAS4I,UAAT,CAAoBpB,CAApB,EAAuB;AACnB,eAAOA,CAAC,CAACgB,GAAF,CAAM,UAASgB,GAAT,EAAc;AAAE,iBAAOA,GAAG,CAACf,IAAJ,CAAS,GAAT,CAAP;AAAuB,SAA7C,EAA+CA,IAA/C,CAAoD,GAApD,CAAP;AACH,OA9K20B,CAgL50B;;;AACA,eAASF,YAAT,CAAsBf,CAAtB,EAAyB;AACrB,eAAON,GAAG,CAAC,cAAD,EAAiB5F,KAAK,CAACkG,CAAD,CAAL,CAASgB,GAAT,CAAaiB,IAAb,EAAmBhB,IAAnB,CAAwB,EAAxB,CAAjB,CAAV;AACH;;AAED,eAASgB,IAAT,CAAcjC,CAAd,EAAiB;AACb,eAAON,GAAG,CAAC,MAAD,EAASA,GAAG,CAAC,OAAD,EAAUC,MAAM,CAACK,CAAC,CAAC,CAAD,CAAF,CAAhB,CAAZ,EAAqC,CAAC,CAAC,MAAD,EAASL,MAAM,CAACK,CAAC,CAAC,CAAD,CAAF,CAAf,CAAD,CAArC,CAAV;AACH,OAvL20B,CAyL50B;;;AACA,eAASQ,cAAT,CAAwBR,CAAxB,EAA2B;AACvB,eAAO,CAAC,EAAEA,CAAC,CAAC,aAAD,CAAD,IAAoBA,CAAC,CAAC,eAAD,CAArB,IAA0CA,CAAC,CAAC,cAAD,CAA7C,CAAR;AACH;;AAED,eAASU,WAAT,CAAqBV,CAArB,EAAwBhE,SAAxB,EAAmC;AAC/B,eAAO0D,GAAG,CAAC,OAAD,EACNA,GAAG,CAAC,WAAD,EACCA,GAAG,CAAC,MAAD,EACCA,GAAG,CAAC,MAAD,EAASwC,OAAO,CAAClC,CAAD,CAAhB,CADJ,CADJ,CAAH,GAGAmC,QAAQ,CAACnC,CAAD,CAJF,EAIO,CAAC,CAAC,IAAD,EAAOhE,SAAP,CAAD,CAJP,CAAV;AAKH;;AAED,eAASkG,OAAT,CAAiBlC,CAAjB,EAAoB;AAChB,YAAIoC,IAAI,GAAGpC,CAAC,CAAC,aAAD,CAAD,IAAoB,QAA/B;AAAA,YACIqC,MAAM,GAAGrC,CAAC,CAAC,eAAD,CAAD,GAAqB,MAAMA,CAAC,CAAC,eAAD,CAA5B,GAAgD,EAD7D;AAAA,YAEI5F,KAAK,GAAG,CAAC4F,CAAC,CAAC,cAAD,CAAD,IAAqB,QAAtB,EAAgCrI,OAAhC,CAAwC,GAAxC,EAA6C,EAA7C,CAFZ;AAIA,eAAO,4CAA4C,MAA5C,GAAqDyK,IAAI,CAACE,MAAL,CAAY,CAAZ,CAArD,GACHD,MADG,GACM,GADN,GACYjI,KADZ,GACoB,MAD3B;AAEH;;AAED,eAAS+H,QAAT,CAAkBnC,CAAlB,EAAqB;AACjB,eAAON,GAAG,CAAC,SAAD,EAAY,EAAZ,EAAgB,CACtB,CAAC,QAAD,EAAW,UAAX,CADsB,EAEtB,CAAC,QAAD,EAAW,UAAX,CAFsB,EAGtB,CAAC,GAAD,EAAM,GAAN,CAHsB,EAItB,CAAC,GAAD,EAAM,GAAN,CAJsB,CAAhB,CAAV;AAMH,OAtN20B,CAwN50B;;;AACA,eAASmB,sBAAT,CAAgCb,CAAhC,EAAmC;AAC/B,aAAK,IAAIuC,GAAT,IAAgBvC,CAAhB,EAAmB;AACf,cAAI;AACA,sBAAU,IADV;AAEA,8BAAkB,IAFlB;AAGA,4BAAgB,IAHhB;AAIA,oBAAQ,IAJR;AAKA,4BAAgB;AALhB,YAMFuC,GANE,CAAJ,EAMQ,OAAO,IAAP;AACX;AACJ;;AAED,eAASzB,mBAAT,CAA6Bd,CAA7B,EAAgChE,SAAhC,EAA2C;AACvC,YAAIH,SAAS,GAAG6D,GAAG,CAAC,WAAD,EAAc,CAC7BA,GAAG,CAAC,OAAD,EAAU8C,aAAa,CAACxC,CAAC,CAAC,QAAD,CAAF,EAAcA,CAAC,CAAC,gBAAD,CAAf,CAAb,IAAmD,UAA7D,CAAH,GACAN,GAAG,CAAC,OAAD,EAAUM,CAAC,CAAC,cAAD,CAAD,KAAsBhH,SAAtB,GAAkC,CAAlC,GAAsCgH,CAAC,CAAC,cAAD,CAAjD,CAF0B,CAAd,CAAnB;AAKA,YAAIlE,SAAS,GAAG,EAAhB;;AAEA,YAAIkE,CAAC,CAAC,MAAD,CAAD,IAAaA,CAAC,CAAC,cAAD,CAAlB,EAAoC;AAChClE,mBAAS,GAAG4D,GAAG,CAAC,WAAD,EAAc,CACzBA,GAAG,CAAC,OAAD,EAAU8C,aAAa,CAACxC,CAAC,CAAC,MAAD,CAAF,EAAYA,CAAC,CAAC,cAAD,CAAb,CAAb,IAA+C,UAAzD,CADsB,CAAd,CAAf;AAGH;;AAED,eAAON,GAAG,CAAC,OAAD,EAAU7D,SAAS,GAAGC,SAAtB,EAAiC,CAAC,CAAC,IAAD,EAAOE,SAAP,CAAD,CAAjC,CAAV;AACH,OApP20B,CAsP50B;;;AACA,eAASsE,SAAT,CAAmBN,CAAnB,EAAsB;AAClB,YAAIrG,IAAI,GAAG,EAAX;AAEA,YAAIqG,CAAC,CAAC,eAAD,CAAL,EAAwBrG,IAAI,GAAGA,IAAI,GAAG,IAAP,GAAcqG,CAAC,CAAC,eAAD,CAAtB;AACxB,YAAIA,CAAC,CAAC,cAAD,CAAL,EAAuBrG,IAAI,GAAGA,IAAI,GAAG,IAAP,GAAcqG,CAAC,CAAC,cAAD,CAAD,CAAkBrI,OAAlB,CAA0B,GAA1B,EAA+B,EAA/B,CAArB;AACvB,YAAIqI,CAAC,CAAC,aAAD,CAAL,EAAsBrG,IAAI,GAAGA,IAAI,GAAG,IAAP,GAAcqG,CAAC,CAAC,aAAD,CAAtB;AACtB,YAAIA,CAAC,CAAC,QAAD,CAAL,EAAiBrG,IAAI,GAAGA,IAAI,GAAG,GAAP,GAAaqG,CAAC,CAAC,QAAD,CAAD,CAAYrI,OAAZ,CAAoB,GAApB,EAAyB,EAAzB,CAApB;AACjB,YAAIqI,CAAC,CAAC,cAAD,CAAL,EAAuBrG,IAAI,GAAGA,IAAI,GAAG,IAAP,GAAcqG,CAAC,CAAC,cAAD,CAAD,CAAkBpG,QAAlB,GAA6BjC,OAA7B,CAAqC,GAArC,EAA0C,EAA1C,CAArB;AACvB,YAAIqI,CAAC,CAAC,gBAAD,CAAL,EAAyBrG,IAAI,GAAGA,IAAI,GAAG,IAAP,GAAcqG,CAAC,CAAC,gBAAD,CAAD,CAAoBpG,QAApB,GAA+BjC,OAA/B,CAAuC,GAAvC,EAA4C,EAA5C,CAArB;AACzB,YAAIqI,CAAC,CAAC,MAAD,CAAL,EAAerG,IAAI,GAAGA,IAAI,GAAG,GAAP,GAAaqG,CAAC,CAAC,MAAD,CAAD,CAAUrI,OAAV,CAAkB,GAAlB,EAAuB,EAAvB,CAApB;AACf,YAAIqI,CAAC,CAAC,cAAD,CAAL,EAAuBrG,IAAI,GAAGA,IAAI,GAAG,IAAP,GAAcqG,CAAC,CAAC,cAAD,CAAD,CAAkBpG,QAAlB,GAA6BjC,OAA7B,CAAqC,GAArC,EAA0C,EAA1C,CAArB;AAEvB,eAAOgC,IAAP;AACH;;AAED,eAAS6I,aAAT,CAAuBC,QAAvB,EAAiCpI,OAAjC,EAA0C;AACtC,YAAI,OAAOoI,QAAP,KAAoB,QAAxB,EAAkC,OAAO,EAAP;AAElCA,gBAAQ,GAAGA,QAAQ,CAAC9K,OAAT,CAAiB,GAAjB,EAAsB,EAAtB,EAA0B+K,WAA1B,EAAX;;AAEA,YAAID,QAAQ,CAACzM,MAAT,KAAoB,CAAxB,EAA2B;AACvByM,kBAAQ,GAAGA,QAAQ,CAAC,CAAD,CAAR,GAAcA,QAAQ,CAAC,CAAD,CAAtB,GACXA,QAAQ,CAAC,CAAD,CADG,GACGA,QAAQ,CAAC,CAAD,CADX,GAEXA,QAAQ,CAAC,CAAD,CAFG,GAEGA,QAAQ,CAAC,CAAD,CAFtB;AAGH,SAJD,MAIO,IAAIA,QAAQ,CAACzM,MAAT,KAAoB,CAAxB,EAA2B;AAC9B,iBAAO,EAAP;AACH;;AAED,YAAIkJ,CAAC,GAAGuD,QAAQ,CAAC,CAAD,CAAR,GAAcA,QAAQ,CAAC,CAAD,CAA9B;AACA,YAAI3D,CAAC,GAAG2D,QAAQ,CAAC,CAAD,CAAR,GAAcA,QAAQ,CAAC,CAAD,CAA9B;AACA,YAAIE,CAAC,GAAGF,QAAQ,CAAC,CAAD,CAAR,GAAcA,QAAQ,CAAC,CAAD,CAA9B;AAEA,YAAIvL,CAAC,GAAG,IAAR;;AACA,YAAI,OAAOmD,OAAP,KAAmB,QAAnB,IAA+BA,OAAO,IAAI,GAA1C,IAAiDA,OAAO,IAAI,GAAhE,EAAqE;AACjEnD,WAAC,GAAG,CAACmD,OAAO,GAAG,GAAX,EAAgBT,QAAhB,CAAyB,EAAzB,CAAJ;AACA,cAAI1C,CAAC,CAACuJ,OAAF,CAAU,GAAV,IAAiB,CAAC,CAAtB,EAAyBvJ,CAAC,GAAGA,CAAC,CAACoD,MAAF,CAAS,CAAT,EAAYpD,CAAC,CAACuJ,OAAF,CAAU,GAAV,CAAZ,CAAJ;AACzB,cAAIvJ,CAAC,CAAClB,MAAF,GAAW,CAAf,EAAkBkB,CAAC,GAAG,MAAMA,CAAV;AACrB;;AAED,eAAOA,CAAC,GAAGyL,CAAJ,GAAQ7D,CAAR,GAAYI,CAAnB;AACH,OA/R20B,CAiS50B;;;AACA,eAASpF,KAAT,CAAekG,CAAf,EAAkB;AACd,YAAI9I,CAAC,GAAG,EAAR;;AACA,aAAK,IAAIjB,CAAT,IAAc+J,CAAd;AAAiB9I,WAAC,CAACY,IAAF,CAAO,CAAC7B,CAAD,EAAI+J,CAAC,CAAC/J,CAAD,CAAL,CAAP;AAAjB;;AACA,eAAOiB,CAAP;AACH;AACA,KAvS0yB,EAuSzyB;AAAC,gBAAS;AAAV,KAvSyyB,CAAH;AAuSxxB,OAAE,CAAC,UAAS/D,OAAT,EAAiBwL,MAAjB,EAAwB/F,OAAxB,EAAgC;AACnD+F,YAAM,CAAC/F,OAAP,CAAerC,IAAf,GAAsBA,IAAtB;AACAoI,YAAM,CAAC/F,OAAP,CAAegK,QAAf,GAA0BA,QAA1B;AACAjE,YAAM,CAAC/F,OAAP,CAAe8G,GAAf,GAAqBA,GAArB;AACAf,YAAM,CAAC/F,OAAP,CAAe+G,MAAf,GAAwBA,MAAxB;AAEA;AACA;AACA;AACA;;AACA,eAASpJ,IAAT,CAAcyJ,CAAd,EAAiB;AACb,eAAQA,CAAC,IAAIA,CAAC,CAAChK,MAAR,GAAmB,MAAMgK,CAAC,CAACgB,GAAF,CAAM,UAAS3B,CAAT,EAAY;AAC9C,iBAAOA,CAAC,CAAC,CAAD,CAAD,GAAO,IAAP,GAAcA,CAAC,CAAC,CAAD,CAAf,GAAqB,GAA5B;AACH,SAF+B,EAE7B4B,IAF6B,CAExB,GAFwB,CAAzB,GAES,EAFhB;AAGH;AAED;AACA;AACA;AACA;AACA;;;AACA,eAAS2B,QAAT,CAAkB9L,EAAlB,EAAsB+L,UAAtB,EAAkC;AAC9B,eAAO,MAAM/L,EAAN,GAAWP,IAAI,CAACsM,UAAD,CAAf,GAA8B,IAArC;AACH;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,eAASnD,GAAT,CAAa5I,EAAb,EAAiBgM,QAAjB,EAA2BD,UAA3B,EAAuC;AACnC,eAAO,MAAM/L,EAAN,GAAWP,IAAI,CAACsM,UAAD,CAAf,GAA8B,GAA9B,GAAoCC,QAApC,GAA+C,IAA/C,GAAsDhM,EAAtD,GAA2D,GAAlE;AACH;AAED;AACA;AACA;AACA;;;AACA,eAAS6I,MAAT,CAAgBK,CAAhB,EAAmB;AACf,eAAO,CAACA,CAAC,KAAK,IAAN,GAAa,EAAb,GAAkBA,CAAC,CAACpG,QAAF,EAAnB,EAAiCjC,OAAjC,CAAyC,IAAzC,EAA+C,OAA/C,EACFA,OADE,CACM,IADN,EACY,MADZ,EAEFA,OAFE,CAEM,IAFN,EAEY,MAFZ,EAGFA,OAHE,CAGM,IAHN,EAGY,QAHZ,CAAP;AAIH;AAEA,KA9CiB,EA8ChB,EA9CgB;AAvSsxB,GAA3b,EAqVtW,EArVsW,EAqVnW,CAAC,CAAD,CArVmW,EAqV9V,CArV8V,CAAP;AAsVrW,CAtVD,E;;;;;;;ACAA;AACA;;;;;;;;ACDA;AACA;AACA;AACA;AACA;AACA,uCAAuC,WAAW;AAClD;AACA;AACA,4B;;;;;;ACRA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,aAAa,GAAG,IAAoD,oBAAoB,KAAK,EAA8K,CAAC,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,0CAA0C,OAAO,uBAAC,QAAQ,OAAC,OAAO,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,iBAAiB,eAAe,sBAAsB,oBAAoB,kDAAkD,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,aAAa,2IAA2I,YAAY,yBAAyB,gBAAgB,UAAU,UAAU,8BAA8B,wBAAwB,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,wBAAwB,sBAAsB,oBAAoB,sCAAsC,WAAW,YAAY,SAAS,EAAE,mBAAmB,aAAa,0GAA0G,qBAAqB,0EAA0E,WAAW,+OAA+O,kBAAkB,sBAAsB,wBAAwB,2GAA2G,2DAA2D,yJAAyJ,sDAAsD,WAAW,kMAAkM,UAAU,EAAE,4BAA4B,qBAAqB,aAAa,4GAA4G,sBAAsB,uGAAuG,aAAa,4BAA4B,mIAAmI,6BAA6B,6GAA6G,IAAI,gCAAgC,yPAAyP,oCAAoC,6IAA6I,aAAa,EAAE,+FAA+F,qBAAqB,aAAa,kCAAkC,SAAS,wCAAwC,kCAAkC,6BAA6B,qCAAqC,wBAAwB,EAAE,wCAAwC,qBAAqB,aAAa,gCAAgC,mBAAmB,MAAM,KAAK,IAAI,YAAY,IAAI,iCAAiC,OAAO,SAAS,GAAG,wBAAwB,sEAAsE,cAAc,MAAM,YAAY,IAAI,4BAA4B,WAAW,iCAAiC,cAAc,MAAM,YAAY,IAAI,uCAAuC,WAAW,oBAAoB,EAAE,aAAa,qBAAqB,aAAa,yKAAyK,GAAG,qBAAqB,aAAa,MAAM,0DAA0D,WAAW,EAAE,OAAO,qBAAqB,aAAa,yLAAyL,gBAAgB,kGAAkG,oEAAoE,mGAAmG,8BAA8B,0FAA0F,gCAAgC,+CAA+C,oCAAoC,oCAAoC,yCAAyC,EAAE,WAAW,8BAA8B,QAAQ,mBAAmB,GAAG,8BAA8B,0BAA0B,+BAA+B,yBAAyB,GAAG,EAAE,iDAAiD,qBAAqB,aAAa,gBAAgB,WAAW,QAAQ,IAAI,yCAAyC,SAAS,wBAAwB,gTAAgT,6CAA6C,iGAAiG,QAAQ,+BAA+B,cAAc,wXAAwX,SAAS,iKAAiK,4HAA4H,sGAAsG,oBAAoB,iRAAiR,6CAA6C,mEAAmE,yGAAyG,kBAAkB,8DAA8D,GAAG,sCAAsC,wEAAwE,oCAAoC,MAAM,8EAA8E,WAAW,wBAAwB,WAAW,EAAE,wBAAwB,sCAAsC,mBAAmB,gHAAgH,kDAAkD,8FAA8F,aAAa,EAAE,oBAAoB,wBAAwB,WAAW,EAAE,0BAA0B,uCAAuC,sBAAsB,8BAA8B,gCAAgC,yBAAyB,eAAe,8BAA8B,aAAa,EAAE,iOAAiO,WAAW,aAAa,aAAa,EAAE,0CAA0C,2IAA2I,0CAA0C,sBAAsB,WAAW,+BAA+B,kBAAkB,wBAAwB,sFAAsF,2BAA2B,WAAW,OAAO,+BAA+B,4LAA4L,+BAA+B,oBAAoB,4CAA4C,YAAY,WAAW,QAAQ,cAAc,UAAU,SAAS,6BAA6B,4BAA4B,4BAA4B,WAAW,gBAAgB,aAAa,EAAE,uFAAuF,qBAAqB,aAAa,kDAAkD,iCAAiC,6DAA6D,IAAI,wBAAwB,IAAI,oBAAoB,kBAAkB,gEAAgE,SAAS,8FAA8F,kBAAkB,8CAA8C,4GAA4G,UAAU,mBAAmB,SAAS,WAAW,UAAU,EAAE,wCAAwC,sBAAsB,aAAa,aAAa,qCAAqC,sIAAsI,aAAa,sDAAsD,YAAY,6DAA6D,UAAU,kJAAkJ,6BAA6B,wCAAwC,EAAE,uEAAuE,sBAAsB,aAAa,uHAAuH,cAAc,mCAAmC,oDAAoD,yBAAyB,KAAK,sBAAsB,6FAA6F,WAAW,EAAE,wBAAwB,WAAW,uBAAuB,EAAE,8FAA8F,6MAA6M,eAAe,mBAAmB,mBAAmB,uCAAuC,4BAA4B,WAAW,oBAAoB,wBAAwB,mBAAmB,kCAAkC,WAAW,KAAK,WAAW,qCAAqC,+MAA+M,EAAE,uDAAuD,GAAG,EAAE,sGAAsG,sBAAsB,aAAa,mDAAmD,gBAAgB,6FAA6F,oDAAoD,WAAW,iDAAiD,QAAQ,aAAa,WAAW,EAAE,yBAAyB,4CAA4C,sBAAsB,uCAAuC,EAAE,8BAA8B,gEAAgE,+BAA+B,iGAAiG,aAAa,EAAE,2CAA2C,sBAAsB,aAAa,oCAAoC,kBAAkB,8BAA8B,WAAW,0BAA0B,qCAAqC,yBAAyB,kBAAkB,sBAAsB,aAAa,EAAE,yDAAyD,sBAAsB,aAAa,EAAE,mCAAmC,sBAAsB,aAAa,WAAW,8DAA8D,sEAAsE,kFAAkF,uBAAuB,yBAAyB,uCAAuC,oBAAoB,mBAAmB,sBAAsB,0BAA0B,sBAAsB,6FAA6F,GAAG,sBAAsB,aAAa,kBAAkB,uCAAuC,IAAI,yUAAyU,iDAAiD,yBAAyB,+BAA+B,wBAAwB,mDAAmD,6SAA6S,mBAAmB,gBAAgB,cAAc,oCAAoC,2PAA2P,gFAAgF,uBAAuB,iBAAiB,cAAc,4DAA4D,OAAO,gBAAgB,8FAA8F,qBAAqB,UAAU,4JAA4J,oBAAoB,SAAS,kCAAkC,kBAAkB,IAAI,sBAAsB,qEAAqE,SAAS,QAAQ,iCAAiC,wBAAwB,EAAE,8BAA8B,wBAAwB,oBAAoB,kBAAkB,yCAAyC,wBAAwB,EAAE,kDAAkD,uBAAuB,oBAAoB,cAAc,oBAAoB,mFAAmF,yCAAyC,oCAAoC,MAAM,WAAW,iCAAiC,YAAY,sBAAsB,8FAA8F,oCAAoC,WAAW,IAAI,oBAAoB,EAAE,sJAAsJ,uKAAuK,+KAA+K,kCAAkC,6BAA6B,SAAS,4BAA4B,4CAA4C,6BAA6B,oDAAoD,kCAAkC,cAAc,iFAAiF,YAAY,EAAE,gNAAgN,sBAAsB,sBAAsB,EAAE,cAAc,sBAAsB,aAAa,wBAAwB,cAAc,eAAe,YAAY,mBAAmB,kBAAkB,2DAA2D,8BAA8B,8CAA8C,gGAAgG,KAAK,uGAAuG,SAAS,+CAA+C,+FAA+F,8CAA8C,kCAAkC,sCAAsC,mEAAmE,uBAAuB,aAAa,EAAE,gCAAgC,sBAAsB,aAAa,oBAAoB,cAAc,0DAA0D,aAAa,wBAAwB,8BAA8B,wBAAwB,6IAA6I,sBAAsB,gCAAgC,kBAAkB,4BAA4B,qBAAqB,qBAAqB,UAAU,yCAAyC,cAAc,4BAA4B,uBAAuB,wBAAwB,gDAAgD,uBAAuB,mCAAmC,oCAAoC,qBAAqB,sBAAsB,8FAA8F,aAAa,EAAE,cAAc,sBAAsB,aAAa,8BAA8B,cAAc,eAAe,6DAA6D,oBAAoB,mEAAmE,uBAAuB,aAAa,EAAE,sCAAsC,sBAAsB,aAAa,wBAAwB,cAAc,eAAe,2DAA2D,yCAAyC,8CAA8C,0CAA0C,+CAA+C,4BAA4B,kCAAkC,oBAAoB,mEAAmE,uBAAuB,aAAa,EAAE,gCAAgC,sBAAsB,aAAa,yBAAyB,cAAc,eAAe,6DAA6D,sDAAsD,sEAAsE,uBAAuB,aAAa,EAAE,iCAAiC,sBAAsB,aAAa,qIAAqI,sBAAsB,qBAAqB,0KAA0K,EAAE,qHAAqH,sBAAsB,aAAa,+LAA+L,GAAG,sBAAsB,aAAa,2CAA2C,cAAc,mDAAmD,qDAAqD,WAAW,qDAAqD,EAAE,aAAa,EAAE,mCAAmC,sBAAsB,aAAa,2CAA2C,aAAa,yDAAyD,iEAAiE,sEAAsE,aAAa,EAAE,gDAAgD,sBAAsB,aAAa,2CAA2C,cAAc,+EAA+E,qDAAqD,MAAM,wCAAwC,+CAA+C,sCAAsC,aAAa,EAAE,mCAAmC,sBAAsB,aAAa,2CAA2C,cAAc,0BAA0B,WAAW,kHAAkH,oGAAoG,aAAa,WAAW,EAAE,+CAA+C,8CAA8C,+BAA+B,kJAAkJ,uCAAuC,qJAAqJ,8BAA8B,2CAA2C,iDAAiD,0CAA0C,kBAAkB,iDAAiD,MAAM,oDAAoD,MAAM,6DAA6D,+BAA+B,aAAa,4CAA4C,EAAE,aAAa,EAAE,mCAAmC,sBAAsB,aAAa,cAAc,yCAAyC,iDAAiD,uEAAuE,wBAAwB,oBAAoB,aAAa,iBAAiB,oBAAoB,gBAAgB,4BAA4B,aAAa,IAAI,mDAAmD,SAAS,qBAAqB,SAAS,mBAAmB,gKAAgK,kBAAkB,uCAAuC,oBAAoB,iFAAiF,oBAAoB,kCAAkC,4BAA4B,uCAAuC,kBAAkB,gCAAgC,8BAA8B,iFAAiF,oEAAoE,WAAW,+BAA+B,kBAAkB,wBAAwB,QAAQ,2BAA2B,WAAW,OAAO,kBAAkB,mGAAmG,mBAAmB,4CAA4C,uBAAuB,4GAA4G,mBAAmB,0BAA0B,aAAa,8BAA8B,6DAA6D,4BAA4B,uHAAuH,iBAAiB,iFAAiF,qDAAqD,qBAAqB,0BAA0B,+CAA+C,aAAa,GAAG,sBAAsB,aAAa,+HAA+H,oBAAoB,2CAA2C,UAAU,kBAAkB,QAAQ,UAAU,4CAA4C,MAAM,wBAAwB,IAAI,kHAAkH,SAAS,mDAAmD,aAAa,uBAAuB,8CAA8C,yDAAyD,0BAA0B,kBAAkB,yBAAyB,UAAU,sBAAsB,IAAI,sBAAsB,UAAU,8DAA8D,gCAAgC,mCAAmC,iBAAiB,qBAAqB,QAAQ,WAAW,mBAAmB,UAAU,+BAA+B,sDAAsD,6CAA6C,WAAW,iCAAiC,SAAS,yCAAyC,8DAA8D,SAAS,KAAK,SAAS,KAAK,KAAK,WAAW,EAAE,QAAQ,kBAAkB,WAAW,+CAA+C,wBAAwB,+BAA+B,uBAAuB,OAAO,mBAAmB,yDAAyD,kBAAkB,iCAAiC,4BAA4B,qIAAqI,mBAAmB,2CAA2C,KAAK,aAAa,EAAE,+IAA+I,sBAAsB,aAAa,kPAAkP,KAAK,yBAAyB,IAAI,yBAAyB,uBAAuB,OAAO,SAAS,IAAI,6FAA6F,yDAAyD,SAAS,YAAY,IAAI,6CAA6C,SAAS,iBAAiB,EAAE,qBAAqB,sBAAsB,aAAa,gHAAgH,MAAM,wDAAwD,aAAa,+CAA+C,aAAa,4BAA4B,yCAAyC,2DAA2D,6BAA6B,QAAQ,IAAI,2JAA2J,wDAAwD,IAAI,6QAA6Q,SAAS,IAAI,0BAA0B,gFAAgF,wCAAwC,UAAU,IAAI,4BAA4B,uCAAuC,KAAK,2BAA2B,SAAS,sBAAsB,yFAAyF,sFAAsF,uDAAuD,sDAAsD,8DAA8D,wCAAwC,iBAAiB,QAAQ,qGAAqG,+BAA+B,mBAAmB,oBAAoB,MAAM,iDAAiD,sBAAsB,KAAK,qCAAqC,QAAQ,oJAAoJ,iCAAiC,EAAE,8BAA8B,iDAAiD,yCAAyC,sBAAsB,2EAA2E,WAAW,sCAAsC,EAAE,sBAAsB,EAAE,2EAA2E,sBAAsB,aAAa,sGAAsG,cAAc,SAAS,gBAAgB,YAAY,WAAW,6BAA6B,SAAS,wBAAwB,uBAAuB,IAAI,qBAAqB,OAAO,EAAE,SAAS,IAAI,6FAA6F,gCAAgC,SAAS,sDAAsD,OAAO,iCAAiC,wBAAwB,iDAAiD,KAAK,IAAI,6KAA6K,kBAAkB,6BAA6B,iBAAiB,WAAW,iCAAiC,SAAS,iBAAiB,sBAAsB,IAAI,kFAAkF,SAAS,UAAU,yBAAyB,IAAI,iFAAiF,SAAS,UAAU,KAAK,cAAc,kCAAkC,2GAA2G,IAAI,KAAK,iCAAiC,SAAS,kBAAkB,4BAA4B,gBAAgB,YAAY,WAAW,cAAc,SAAS,sBAAsB,SAAS,UAAU,2BAA2B,gCAAgC,yBAAyB,qCAAqC,wBAAwB,qCAAqC,wBAAwB,qCAAqC,UAAU,yCAAyC,gCAAgC,wBAAwB,yBAAyB,wBAAwB,2BAA2B,gBAAgB,mBAAmB,4BAA4B,mBAAmB,oDAAoD,sCAAsC,yBAAyB,wBAAwB,2CAA2C,eAAe,2BAA2B,gCAAgC,yBAAyB,gBAAgB,qCAAqC,2BAA2B,eAAe,2BAA2B,gCAAgC,yBAAyB,yCAAyC,wBAAwB,qCAAqC,cAAc,6BAA6B,uBAAuB,kBAAkB,qBAAqB,kBAAkB,yBAAyB,wPAAwP,4BAA4B,+EAA+E,qEAAqE,aAAa,QAAQ,iBAAiB,0EAA0E,SAAS,yBAAyB,aAAa,uBAAuB,EAAE,0BAA0B,cAAc,0CAA0C,qBAAqB,aAAa,QAAQ,mBAAmB,gGAAgG,SAAS,sCAAsC,6CAA6C,kLAAkL,qBAAqB,qBAAqB,mBAAmB,uBAAuB,kBAAkB,wBAAwB,IAAI,mBAAmB,uBAAuB,sTAAsT,GAAG,EAAE,sFAAsF,sBAAsB,aAAa,iHAAiH,cAAc,iCAAiC,aAAa,2BAA2B,0CAA0C,qBAAqB,gCAAgC,2GAA2G,2BAA2B,wBAAwB,wBAAwB,oCAAoC,iCAAiC,kCAAkC,sUAAsU,2GAA2G,mDAAmD,uCAAuC,2XAA2X,8CAA8C,IAAI,0GAA0G,uBAAuB,8CAA8C,2OAA2O,2BAA2B,QAAQ,QAAQ,oBAAoB,yKAAyK,2BAA2B,MAAM,gDAAgD,yDAAyD,WAAW,iBAAiB,oEAAoE,6NAA6N,6BAA6B,gEAAgE,0QAA0Q,wBAAwB,QAAQ,gWAAgW,mLAAmL,ybAAyb,mJAAmJ,gDAAgD,qDAAqD,UAAU,uEAAuE,6EAA6E,2BAA2B,iBAAiB,kBAAkB,2FAA2F,aAAa,EAAE,iGAAiG,sBAAsB,aAAa,2IAA2I,gBAAgB,kCAAkC,aAAa,uBAAuB,2BAA2B,oBAAoB,iCAAiC,2BAA2B,QAAQ,iUAAiU,yBAAyB,kEAAkE,YAAY,+KAA+K,gHAAgH,6BAA6B,8NAA8N,mBAAmB,ySAAyS,mHAAmH,8BAA8B,mDAAmD,4BAA4B,oOAAoO,kCAAkC,wBAAwB,mCAAmC,iUAAiU,6BAA6B,2CAA2C,0CAA0C,EAAE,YAAY,oEAAoE,uBAAuB,cAAc,uBAAuB,wCAAwC,kHAAkH,KAAK,uCAAuC,+BAA+B,KAAK,qCAAqC,oDAAoD,0CAA0C,kCAAkC,KAAK,wCAAwC,yDAAyD,sCAAsC,8BAA8B,MAAM,iBAAiB,uGAAuG,YAAY,yCAAyC,8BAA8B,MAAM,iBAAiB,0GAA0G,aAAa,aAAa,EAAE,sHAAsH,sBAAsB,aAAa,kBAAkB,oMAAoM,mEAAmE,kIAAkI,aAAa,2BAA2B,sBAAsB,IAAI,mDAAmD,iDAAiD,wEAAwE,wBAAwB,oFAAoF,SAAS,4BAA4B,qBAAqB,qBAAqB,4CAA4C,0BAA0B,8DAA8D,+BAA+B,2GAA2G,+BAA+B,sFAAsF,8BAA8B,oHAAoH,2FAA2F,8FAA8F,KAAK,WAAW,wBAAwB,YAAY,EAAE,mHAAmH,sBAAsB,aAAa,aAAa,uDAAuD,MAAM,mDAAmD,aAAa,iBAAiB,eAAe,gBAAgB,yIAAyI,yCAAyC,gCAAgC,iEAAiE,2CAA2C,YAAY,iBAAiB,KAAK,2BAA2B,iCAAiC,wBAAwB,SAAS,aAAa,QAAQ,KAAK,mBAAmB,EAAE,EAAE,kBAAkB,MAAM,QAAQ,WAAW,KAAK,sBAAsB,uBAAuB,2FAA2F,EAAE,GAAG,sBAAsB,aAAa,qBAAqB,cAAc,QAAQ,8CAA8C,cAAc,2EAA2E,gEAAgE,kBAAkB,wLAAwL,kBAAkB,aAAa,MAAM,IAAI,OAAO,SAAS,qBAAqB,qFAAqF,EAAE,cAAc,gBAAgB,yFAAyF,sBAAsB,gBAAgB,SAAS,cAAc,wBAAwB,cAAc,yBAAyB,mBAAmB,OAAO,EAAE,+BAA+B,gBAAgB,SAAS,IAAI,gCAAgC,SAAS,2BAA2B,SAAS,4CAA4C,oCAAoC,uBAAuB,6BAA6B,sCAAsC,SAAS,EAAE,aAAa,sCAAsC,QAAQ,EAAE,EAAE,+BAA+B,yBAAyB,gCAAgC,0FAA0F,8BAA8B,2FAA2F,uCAAuC,0BAA0B,4CAA4C,mCAAmC,sCAAsC,yBAAyB,2CAA2C,kCAAkC,yBAAyB,aAAa,iDAAiD,cAAc,YAAY,KAAK,sBAAsB,8BAA8B,MAAM,6BAA6B,SAAS,wBAAwB,sBAAsB,8BAA8B,MAAM,4BAA4B,SAAS,uBAAuB,oDAAoD,sBAAsB,kBAAkB,qBAAqB,mBAAmB,WAAW,8GAA8G,oBAAoB,8BAA8B,8CAA8C,MAAM,WAAW,SAAS,gBAAgB,8BAA8B,yCAAyC,aAAa,wBAAwB,GAAG,oBAAoB,8GAA8G,oBAAoB,8BAA8B,6BAA6B,MAAM,yCAAyC,yBAAyB,aAAa,wBAAwB,EAAE,UAAU,EAAE,aAAa,sBAAsB,aAAa,SAAS,kHAAkH,EAAE,wFAAwF,sBAAsB,aAAa,iKAAiK,cAAc,wCAAwC,uBAAuB,2EAA2E,MAAM,EAAE,mBAAmB,uMAAuM,oFAAoF,+BAA+B,kEAAkE,MAAM,wNAAwN,mBAAmB,gBAAgB,eAAe,4CAA4C,gBAAgB,+BAA+B,6CAA6C,uBAAuB,+KAA+K,GAAG,4IAA4I,2LAA2L,8CAA8C,mHAAmH,gCAAgC,oBAAoB,+BAA+B,+JAA+J,oDAAoD,cAAc,gBAAgB,sBAAsB,cAAc,kBAAkB,EAAE,sGAAsG,sBAAsB,aAAa,+LAA+L,cAAc,wCAAwC,uBAAuB,mCAAmC,MAAM,EAAE,mBAAmB,yVAAyV,6CAA6C,oCAAoC,4DAA4D,gBAAgB,eAAe,4CAA4C,gBAAgB,+BAA+B,oFAAoF,uBAAuB,sMAAsM,GAAG,8WAA8W,+XAA+X,2DAA2D,sLAAsL,gCAAgC,oBAAoB,+BAA+B,oKAAoK,oDAAoD,cAAc,gBAAgB,YAAY,EAAE,iJAAiJ,sBAAsB,aAAa,sGAAsG,qBAAqB,kDAAkD,SAAS,EAAE,gBAAgB,MAAM,kEAAkE,iDAAiD,SAAS,2BAA2B,iEAAiE,OAAO,6BAA6B,qDAAqD,iBAAiB,IAAI,kBAAkB,2BAA2B,gBAAgB,qBAAqB,IAAI,mBAAmB,yCAAyC,IAAI,kCAAkC,UAAU,IAAI,6BAA6B,YAAY,IAAI,kBAAkB,2BAA2B,8BAA8B,uBAAuB,oIAAoI,eAAe,GAAG,sBAAsB,aAAa,8BAA8B,IAAI,oCAAoC,SAAS,KAAK,IAAI,kDAAkD,SAAS,KAAK,8BAA8B,MAAM,wDAAwD,gBAAgB,oGAAoG,iBAAiB,IAAI,iCAAiC,SAAS,yCAAyC,6BAA6B,QAAQ,IAAI,2JAA2J,0BAA0B,IAAI,6QAA6Q,SAAS,6BAA6B,qBAAqB,6BAA6B,8CAA8C,IAAI,yBAAyB,SAAS,4BAA4B,2CAA2C,UAAU,IAAI,4BAA4B,uCAAuC,KAAK,2BAA2B,SAAS,sBAAsB,yFAAyF,cAAc,4BAA4B,MAAM,iDAAiD,sBAAsB,KAAK,sCAAsC,EAAE,cAAc,sBAAsB,aAAa,4BAA4B,yCAAyC,MAAM,EAAE,qBAAqB,yBAAyB,EAAE,kBAAkB,kBAAkB,GAAG,sBAAsB,aAAa,WAAW,+XAA+X,GAAG,sBAAsB,aAAa,iBAAiB,mBAAmB,MAAM,KAAK,IAAI,YAAY,IAAI,iCAAiC,OAAO,SAAS,GAAG,4BAA4B,cAAc,MAAM,YAAY,IAAI,4BAA4B,YAAY,GAAG,sBAAsB,aAAa,6KAA6K,gBAAgB,oBAAoB,cAAc,uBAAuB,cAAc,mBAAmB,OAAO,QAAQ,cAAc,0BAA0B,iNAAiN,gBAAgB,qHAAqH,gBAAgB,6BAA6B,gBAAgB,sEAAsE,gBAAgB,6LAA6L,oEAAoE,GAAG,+DAA+D,SAAS,IAAI,mJAAmJ,wBAAwB,kCAAkC,sBAAsB,4BAA4B,oCAAoC,cAAc,mCAAmC,GAAG,+DAA+D,wGAAwG,uCAAuC,EAAE,UAAU,uCAAuC,EAAE,KAAK,6BAA6B,sZAAsZ,sKAAsK,GAAG,0CAA0C,gBAAgB,aAAa,EAAE,kBAAkB,sCAAsC,yBAAyB,8XAA8X,qBAAqB,+KAA+K,EAAE,aAAa,iJAAiJ,wEAAwE,8CAA8C,sIAAsI,gBAAgB,eAAe,EAAE,kBAAkB,sCAAsC,yBAAyB,yeAAye,wIAAwI,oLAAoL,EAAE,kGAAkG,2BAA2B,iHAAiH,oDAAoD,yNAAyN,sBAAsB,mFAAmF,aAAa,8nCAA8nC,cAAc,MAAM,6MAA6M,cAAc,aAAa,yUAAyU,wBAAwB,eAAe,QAAQ,+GAA+G,aAAa,YAAY,ueAAue,+BAA+B,YAAY,sDAAsD,EAAE,mBAAmB,wCAAwC,yBAAyB,sCAAsC,sBAAsB,kHAAkH,iFAAiF,oHAAoH,0NAA0N,uBAAuB,yFAAyF,4DAA4D,yBAAyB,YAAY,4CAA4C,yGAAyG,mrBAAmrB,KAAK,2BAA2B,qLAAqL,oCAAoC,gBAAgB,0MAA0M,gDAAgD,0IAA0I,iBAAiB,mCAAmC,YAAY,GAAG,mKAAmK,IAAI,MAAM,oFAAoF,aAAa,8GAA8G,iBAAiB,sCAAsC,YAAY,GAAG,mKAAmK,IAAI,MAAM,0FAA0F,aAAa,mGAAmG,kBAAkB,iMAAiM,iDAAiD,yDAAyD,iDAAiD,2DAA2D,mCAAmC,WAAW,EAAE,4CAA4C,kBAAkB,MAAM,kIAAkI,0GAA0G,mCAAmC,4BAA4B,EAAE,mBAAmB,uCAAuC,yBAAyB,0GAA0G,eAAe,IAAI,2GAA2G,gFAAgF,mPAAmP,0GAA0G,2BAA2B,yFAAyF,mMAAmM,6SAA6S,0BAA0B,MAAM,kIAAkI,sCAAsC,+BAA+B,yBAAyB,uEAAuE,gRAAgR,eAAe,EAAE,qCAAqC,yHAAyH,EAAE,kCAAkC,8LAA8L,oDAAoD,EAAE,8EAA8E,sBAAsB,aAAa,qBAAqB,wIAAwI,GAAG,sBAAsB,aAAa,wBAAwB,sDAAsD,yPAAyP,KAAK,qDAAqD,QAAQ,EAAE,uDAAuD,KAAK,YAAY,cAAc,4BAA4B,WAAW,SAAS,UAAU,QAAQ,8CAA8C,QAAQ,6HAA6H,QAAQ,EAAE,4CAA4C,cAAc,4BAA4B,WAAW,wCAAwC,QAAQ,wFAAwF,gDAAgD,QAAQ,0BAA0B,sBAAsB,gDAAgD,QAAQ,kBAAkB,eAAe,SAAS,kBAAkB,EAAE,WAAW,aAAa,sBAAsB,SAAS,kBAAkB,EAAE,YAAY,WAAW,kBAAkB,EAAE,YAAY,oBAAoB,SAAS,kBAAkB,EAAE,UAAU,KAAK,IAAI,gDAAgD,wCAAwC,KAAK,UAAU,mDAAmD,EAAE,wCAAwC,OAAO,OAAO,gBAAgB,yIAAyI,GAAG,sBAAsB,aAAa,+HAA+H,cAAc,8DAA8D,aAAa,+fAA+f,cAAc,MAAM,0QAA0Q,cAAc,MAAM,mEAAmE,gBAAgB,QAAQ,mKAAmK,gBAAgB,QAAQ,8EAA8E,aAAa,cAAc,MAAM,MAAM,6CAA6C,MAAM,eAAe,KAAK,MAAM,eAAe,KAAK,MAAM,eAAe,KAAK,MAAM,eAAe,iCAAiC,OAAO,MAAM,KAAK,eAAe,4BAA4B,OAAO,OAAO,kDAAkD,oBAAoB,gBAAgB,kYAAkY,kFAAkF,eAAe,0CAA0C,2HAA2H,8DAA8D,0IAA0I,QAAQ,gBAAgB,sBAAsB,UAAU,MAAM,KAAK,KAAK,EAAE,iBAAiB,sBAAsB,wBAAwB,0EAA0E,MAAM,6EAA6E,yCAAyC,MAAM,cAAc,6CAA6C,MAAM,gDAAgD,mBAAmB,sCAAsC,MAAM,uDAAuD,MAAM,YAAY,KAAK,EAAE,iBAAiB,sBAAsB,+BAA+B,6CAA6C,MAAM,kBAAkB,2CAA2C,MAAM,8GAA8G,YAAY,KAAK,EAAE,iBAAiB,sBAAsB,yIAAyI,YAAY,KAAK,EAAE,iBAAiB,sBAAsB,8HAA8H,wBAAwB,KAAK,KAAK,EAAE,iBAAiB,sBAAsB,gHAAgH,iCAAiC,SAAS,oQAAoQ,oBAAoB,wBAAwB,iBAAiB,QAAQ,mFAAmF,EAAE,+DAA+D,gCAAgC,oBAAoB,wBAAwB,iBAAiB,QAAQ,sFAAsF,EAAE,+DAA+D,mCAAmC,SAAS,uBAAuB,KAAK,KAAK,EAAE,iBAAiB,sBAAsB,wBAAwB,sCAAsC,MAAM,MAAM,8EAA8E,MAAM,aAAa,KAAK,EAAE,iBAAiB,sBAAsB,qCAAqC,yGAAyG,4BAA4B,gCAAgC,mBAAmB,0BAA0B,MAAM,KAAK,IAAI,EAAE,iBAAiB,sBAAsB,mCAAmC,iBAAiB,MAAM,qCAAqC,YAAY,QAAQ,iBAAiB,MAAM,4CAA4C,YAAY,MAAM,4BAA4B,KAAK,EAAE,iBAAiB,sBAAsB,8BAA8B,+CAA+C,MAAM,kDAAkD,kBAAkB,uBAAuB,uCAAuC,sDAAsD,MAAM,UAAU,MAAM,aAAa,KAAK,EAAE,iBAAiB,sBAAsB,mHAAmH,sDAAsD,MAAM,mBAAmB,aAAa,eAAe,EAAE,KAAK,IAAI,EAAE,iBAAiB,sBAAsB,oCAAoC,KAAK,UAAU,uBAAuB,qCAAqC,eAAe,6DAA6D,2CAA2C,MAAM,mBAAmB,aAAa,sBAAsB,EAAE,KAAK,wEAAwE,EAAE,iBAAiB,sBAAsB,uCAAuC,KAAK,WAAW,UAAU,IAAI,EAAE,iBAAiB,sBAAsB,2BAA2B,4CAA4C,MAAM,yCAAyC,gBAAgB,UAAU,IAAI,EAAE,iBAAiB,sBAAsB,sCAAsC,KAAK,UAAU,IAAI,EAAE,iBAAiB,sBAAsB,yCAAyC,4BAA4B,4CAA4C,MAAM,KAAK,IAAI,qBAAqB,qBAAqB,oBAAoB,uDAAuD,MAAM,kBAAkB,eAAe,iEAAiE,8CAA8C,MAAM,wCAAwC,gBAAgB,yEAAyE,wCAAwC,MAAM,2BAA2B,kBAAkB,yBAAyB,iMAAiM,MAAM,aAAa,wEAAwE,EAAE,iBAAiB,sBAAsB,kBAAkB,gBAAgB,6EAA6E,EAAE,iBAAiB,sBAAsB,sBAAsB,2CAA2C,UAAU,MAAM,SAAS,oBAAoB,MAAM,SAAS,8CAA8C,MAAM,uBAAuB,oBAAoB,cAAc,IAAI,EAAE,iBAAiB,sBAAsB,mEAAmE,yBAAyB,aAAa,0EAA0E,EAAE,iBAAiB,sBAAsB,eAAe,gBAAgB,8EAA8E,EAAE,iBAAiB,sBAAsB,sBAAsB,+BAA+B,wCAAwC,MAAM,kCAAkC,oBAAoB,cAAc,IAAI,EAAE,iBAAiB,sBAAsB,mEAAmE,oBAAoB,gDAAgD,MAAM,UAAU,yBAAyB,qBAAqB,mCAAmC,gDAAgD,MAAM,iFAAiF,iCAAiC,gCAAgC,kBAAkB,EAAE,0BAA0B,MAAM,yBAAyB,8BAA8B,MAAM,mBAAmB,KAAK,KAAK,EAAE,iBAAiB,sBAAsB,qIAAqI,uCAAuC,MAAM,MAAM,UAAU,4BAA4B,KAAK,KAAK,EAAE,iBAAiB,sBAAsB,6BAA6B,yCAAyC,MAAM,MAAM,UAAU,YAAY,QAAQ,aAAa,QAAQ,iBAAiB,yBAAyB,8dAA8d,0BAA0B,yBAAyB,cAAc,gDAAgD,kCAAkC,MAAM,qEAAqE,sCAAsC,iBAAiB,wIAAwI,oDAAoD,EAAE,gFAAgF,sBAAsB,aAAa,sbAAsb,oCAAoC,iIAAiI,QAAQ,MAAM,WAAW,QAAQ,IAAI,gBAAgB,aAAa,eAAe,KAAK,sEAAsE,QAAQ,cAAc,KAAK,qBAAqB,MAAM,kCAAkC,gCAAgC,eAAe,KAAK,qBAAqB,QAAQ,IAAI,mCAAmC,+IAA+I,MAAM,EAAE,wFAAwF,yCAAyC,EAAE,aAAa,IAAI,OAAO,0CAA0C,eAAe,YAAY,mBAAmB,mCAAmC,yBAAyB,WAAW,+CAA+C,4BAA4B,oDAAoD,EAAE,qBAAqB,sBAAsB,aAAa,WAAW,4KAA4K,GAAG,sBAAsB,aAAa,2BAA2B,cAAc,mBAAmB,OAAO,QAAQ,kQAAkQ,KAAK,oBAAoB,KAAK,qBAAqB,KAAK,qBAAqB,KAAK,oBAAoB,KAAK,0BAA0B,sBAAsB,iHAAiH,gBAAgB,iDAAiD,cAAc,iCAAiC,gBAAgB,sEAAsE,kBAAkB,oJAAoJ,kBAAkB,qBAAqB,gBAAgB,YAAY,0BAA0B,EAAE,aAAa,kBAAkB,6BAA6B,QAAQ,KAAK,uBAAuB,QAAQ,KAAK,KAAK,eAAe,6BAA6B,cAAc,MAAM,QAAQ,MAAM,uBAAuB,QAAQ,KAAK,uBAAuB,QAAQ,KAAK,qBAAqB,mEAAmE,cAAc,uGAAuG,oBAAoB,gBAAgB,0CAA0C,kBAAkB,2BAA2B,iGAAiG,+BAA+B,YAAY,kBAAkB,gBAAgB,uBAAuB,0NAA0N,EAAE,WAAW,gBAAgB,kGAAkG,oCAAoC,IAAI,kEAAkE,KAAK,aAAa,gGAAgG,iCAAiC,KAAK,aAAa,QAAQ,wPAAwP,EAAE,6CAA6C,2KAA2K,QAAQ,KAAK,oBAAoB,+CAA+C,MAAM,wKAAwK,UAAU,GAAG,UAAU,kBAAkB,KAAK,wDAAwD,WAAW,QAAQ,MAAM,wBAAwB,MAAM,qFAAqF,wBAAwB,kBAAkB,gCAAgC,8CAA8C,KAAK,mMAAmM,kBAAkB,gCAAgC,2BAA2B,KAAK,2CAA2C,YAAY,wBAAwB,EAAE,6IAA6I,iDAAiD,KAAK,SAAS,oBAAoB,UAAU,6GAA6G,uBAAuB,eAAe,+BAA+B,UAAU,KAAK,mBAAmB,UAAU,aAAa,mBAAmB,KAAK,mBAAmB,UAAU,aAAa,UAAU,KAAK,sBAAsB,YAAY,iBAAiB,QAAQ,KAAK,WAAW,QAAQ,OAAO,uBAAuB,KAAK,OAAO,uBAAuB,KAAK,OAAO,uBAAuB,KAAK,OAAO,uBAAuB,mBAAmB,KAAK,6BAA6B,0EAA0E,+HAA+H,0DAA0D,YAAY,+DAA+D,mBAAmB,QAAQ,MAAM,iDAAiD,0EAA0E,SAAS,MAAM,qCAAqC,SAAS,+CAA+C,MAAM,8FAA8F,8BAA8B,KAAK,kCAAkC,oLAAoL,MAAM,2CAA2C,IAAI,+BAA+B,0CAA0C,2FAA2F,6BAA6B,kRAAkR,yBAAyB,MAAM,qKAAqK,EAAE,qBAAqB,sBAAsB,aAAa,qBAAqB,6LAA6L,GAAG,sBAAsB,aAAa,kEAAkE,gCAAgC,0CAA0C,GAAG,EAAE,GAAG,WAAW,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,2FAA2F,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,gHAAgH,EAAE,GAAG,EAAE,GAAG,SAAS,E;;;;;;;;ACZn2xG;AACA;AACA;AACA;AACA;AACA;AACA;;AAEY;;AAEZ,aAAa,mBAAO,CAAC,EAAW;AAChC,cAAc,mBAAO,CAAC,EAAS;AAC/B,cAAc,mBAAO,CAAC,EAAS;;AAE/B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB,mDAAmD;AACxE;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mBAAmB,UAAU;AAC7B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA,uCAAuC,SAAS;AAChD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;;AAEA;AACA;AACA,aAAa,iBAAiB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,gDAAgD,EAAE;AAClD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB,eAAe;AACvC;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,wBAAwB,QAAQ;AAChC;AACA,qBAAqB,eAAe;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,kBAAkB;AACnC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,mBAAmB,cAAc;AACjC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,uDAAuD,OAAO;AAC9D;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA,uDAAuD,OAAO;AAC9D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,qBAAqB,QAAQ;AAC7B;AACA;AACA,GAAG;AACH;AACA,eAAe,SAAS;AACxB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA,mBAAmB,SAAS;AAC5B;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,gBAAgB;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;AC5vDY;;AAEZ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kCAAkC,SAAS;AAC3C;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0CAA0C,UAAU;AACpD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;;;;ACrJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,QAAQ,WAAW;;AAEnB;AACA;AACA;AACA,QAAQ,WAAW;;AAEnB;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA,QAAQ,WAAW;;AAEnB;AACA;AACA,QAAQ,UAAU;;AAElB;AACA;;;;;;;ACpFA,iBAAiB;;AAEjB;AACA;AACA;;;;;;;ACJA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA,mBAAO,CAAC,EAAc;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AC9DA;AACA;;AAEA;AACA;AACA;;AAEA,uBAAuB;AACvB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,iBAAiB;AACtC;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,0CAA0C,sBAAsB,EAAE;AAClE;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,UAAU;AACV;AACA;;AAEA,KAAK;AACL;AACA;;AAEA,KAAK;AACL;AACA;;AAEA,KAAK;AACL;AACA;;AAEA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA,CAAC;;;;;;;;ACzLD;AACA;AACA;AACA;AACA;AACA,uCAAuC,WAAW;AAClD;AACA;AACA,4B", "file": "kml-geojson-src.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"kgUtil\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"kgUtil\"] = factory();\n\telse\n\t\troot[\"kgUtil\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 5);\n", "var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n", "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    module.exports = _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n\n    module.exports[\"default\"] = module.exports, module.exports.__esModule = true;\n  } else {\n    module.exports = _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n\n    module.exports[\"default\"] = module.exports, module.exports.__esModule = true;\n  }\n\n  return _typeof(obj);\n}\n\nmodule.exports = _typeof;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n", "module.exports = function(originalModule) {\n\tif (!originalModule.webpackPolyfill) {\n\t\tvar module = Object.create(originalModule);\n\t\t// module.parent = undefined by default\n\t\tif (!module.children) module.children = [];\n\t\tObject.defineProperty(module, \"loaded\", {\n\t\t\tenumerable: true,\n\t\t\tget: function() {\n\t\t\t\treturn module.l;\n\t\t\t}\n\t\t});\n\t\tObject.defineProperty(module, \"id\", {\n\t\t\tenumerable: true,\n\t\t\tget: function() {\n\t\t\t\treturn module.i;\n\t\t\t}\n\t\t});\n\t\tObject.defineProperty(module, \"exports\", {\n\t\t\tenumerable: true\n\t\t});\n\t\tmodule.webpackPolyfill = 1;\n\t}\n\treturn module;\n};\n", "function copy(src,dest){\n\tfor(var p in src){\n\t\tdest[p] = src[p];\n\t}\n}\n/**\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*((?:.*\\{\\s*?[\\r\\n][\\s\\S]*?^})|\\S.*?(?=[;\\r\\n]));?\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*(\\S.*?(?=[;\\r\\n]));?\n */\nfunction _extends(Class,Super){\n\tvar pt = Class.prototype;\n\tif(!(pt instanceof Super)){\n\t\tfunction t(){};\n\t\tt.prototype = Super.prototype;\n\t\tt = new t();\n\t\tcopy(pt,t);\n\t\tClass.prototype = pt = t;\n\t}\n\tif(pt.constructor != Class){\n\t\tif(typeof Class != 'function'){\n\t\t\tconsole.error(\"unknow Class:\"+Class)\n\t\t}\n\t\tpt.constructor = Class\n\t}\n}\nvar htmlns = 'http://www.w3.org/1999/xhtml' ;\n// Node Types\nvar NodeType = {}\nvar ELEMENT_NODE                = NodeType.ELEMENT_NODE                = 1;\nvar ATTRIBUTE_NODE              = NodeType.ATTRIBUTE_NODE              = 2;\nvar TEXT_NODE                   = NodeType.TEXT_NODE                   = 3;\nvar CDATA_SECTION_NODE          = NodeType.CDATA_SECTION_NODE          = 4;\nvar ENTITY_REFERENCE_NODE       = NodeType.ENTITY_REFERENCE_NODE       = 5;\nvar ENTITY_NODE                 = NodeType.ENTITY_NODE                 = 6;\nvar PROCESSING_INSTRUCTION_NODE = NodeType.PROCESSING_INSTRUCTION_NODE = 7;\nvar COMMENT_NODE                = NodeType.COMMENT_NODE                = 8;\nvar DOCUMENT_NODE               = NodeType.DOCUMENT_NODE               = 9;\nvar DOCUMENT_TYPE_NODE          = NodeType.DOCUMENT_TYPE_NODE          = 10;\nvar DOCUMENT_FRAGMENT_NODE      = NodeType.DOCUMENT_FRAGMENT_NODE      = 11;\nvar NOTATION_NODE               = NodeType.NOTATION_NODE               = 12;\n\n// ExceptionCode\nvar ExceptionCode = {}\nvar ExceptionMessage = {};\nvar INDEX_SIZE_ERR              = ExceptionCode.INDEX_SIZE_ERR              = ((ExceptionMessage[1]=\"Index size error\"),1);\nvar DOMSTRING_SIZE_ERR          = ExceptionCode.DOMSTRING_SIZE_ERR          = ((ExceptionMessage[2]=\"DOMString size error\"),2);\nvar HIERARCHY_REQUEST_ERR       = ExceptionCode.HIERARCHY_REQUEST_ERR       = ((ExceptionMessage[3]=\"Hierarchy request error\"),3);\nvar WRONG_DOCUMENT_ERR          = ExceptionCode.WRONG_DOCUMENT_ERR          = ((ExceptionMessage[4]=\"Wrong document\"),4);\nvar INVALID_CHARACTER_ERR       = ExceptionCode.INVALID_CHARACTER_ERR       = ((ExceptionMessage[5]=\"Invalid character\"),5);\nvar NO_DATA_ALLOWED_ERR         = ExceptionCode.NO_DATA_ALLOWED_ERR         = ((ExceptionMessage[6]=\"No data allowed\"),6);\nvar NO_MODIFICATION_ALLOWED_ERR = ExceptionCode.NO_MODIFICATION_ALLOWED_ERR = ((ExceptionMessage[7]=\"No modification allowed\"),7);\nvar NOT_FOUND_ERR               = ExceptionCode.NOT_FOUND_ERR               = ((ExceptionMessage[8]=\"Not found\"),8);\nvar NOT_SUPPORTED_ERR           = ExceptionCode.NOT_SUPPORTED_ERR           = ((ExceptionMessage[9]=\"Not supported\"),9);\nvar INUSE_ATTRIBUTE_ERR         = ExceptionCode.INUSE_ATTRIBUTE_ERR         = ((ExceptionMessage[10]=\"Attribute in use\"),10);\n//level2\nvar INVALID_STATE_ERR        \t= ExceptionCode.INVALID_STATE_ERR        \t= ((ExceptionMessage[11]=\"Invalid state\"),11);\nvar SYNTAX_ERR               \t= ExceptionCode.SYNTAX_ERR               \t= ((ExceptionMessage[12]=\"Syntax error\"),12);\nvar INVALID_MODIFICATION_ERR \t= ExceptionCode.INVALID_MODIFICATION_ERR \t= ((ExceptionMessage[13]=\"Invalid modification\"),13);\nvar NAMESPACE_ERR            \t= ExceptionCode.NAMESPACE_ERR           \t= ((ExceptionMessage[14]=\"Invalid namespace\"),14);\nvar INVALID_ACCESS_ERR       \t= ExceptionCode.INVALID_ACCESS_ERR      \t= ((ExceptionMessage[15]=\"Invalid access\"),15);\n\n/**\n * DOM Level 2\n * Object DOMException\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/ecma-script-binding.html\n * @see http://www.w3.org/TR/REC-DOM-Level-1/ecma-script-language-binding.html\n */\nfunction DOMException(code, message) {\n\tif(message instanceof Error){\n\t\tvar error = message;\n\t}else{\n\t\terror = this;\n\t\tError.call(this, ExceptionMessage[code]);\n\t\tthis.message = ExceptionMessage[code];\n\t\tif(Error.captureStackTrace) Error.captureStackTrace(this, DOMException);\n\t}\n\terror.code = code;\n\tif(message) this.message = this.message + \": \" + message;\n\treturn error;\n};\nDOMException.prototype = Error.prototype;\ncopy(ExceptionCode,DOMException)\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-536297177\n * The NodeList interface provides the abstraction of an ordered collection of nodes, without defining or constraining how this collection is implemented. NodeList objects in the DOM are live.\n * The items in the NodeList are accessible via an integral index, starting from 0.\n */\nfunction NodeList() {\n};\nNodeList.prototype = {\n\t/**\n\t * The number of nodes in the list. The range of valid child node indices is 0 to length-1 inclusive.\n\t * @standard level1\n\t */\n\tlength:0, \n\t/**\n\t * Returns the indexth item in the collection. If index is greater than or equal to the number of nodes in the list, this returns null.\n\t * @standard level1\n\t * @param index  unsigned long \n\t *   Index into the collection.\n\t * @return Node\n\t * \tThe node at the indexth position in the NodeList, or null if that is not a valid index. \n\t */\n\titem: function(index) {\n\t\treturn this[index] || null;\n\t},\n\ttoString:function(isHTML,nodeFilter){\n\t\tfor(var buf = [], i = 0;i<this.length;i++){\n\t\t\tserializeToString(this[i],buf,isHTML,nodeFilter);\n\t\t}\n\t\treturn buf.join('');\n\t}\n};\nfunction LiveNodeList(node,refresh){\n\tthis._node = node;\n\tthis._refresh = refresh\n\t_updateLiveList(this);\n}\nfunction _updateLiveList(list){\n\tvar inc = list._node._inc || list._node.ownerDocument._inc;\n\tif(list._inc != inc){\n\t\tvar ls = list._refresh(list._node);\n\t\t//console.log(ls.length)\n\t\t__set__(list,'length',ls.length);\n\t\tcopy(ls,list);\n\t\tlist._inc = inc;\n\t}\n}\nLiveNodeList.prototype.item = function(i){\n\t_updateLiveList(this);\n\treturn this[i];\n}\n\n_extends(LiveNodeList,NodeList);\n/**\n * \n * Objects implementing the NamedNodeMap interface are used to represent collections of nodes that can be accessed by name. Note that NamedNodeMap does not inherit from NodeList; NamedNodeMaps are not maintained in any particular order. Objects contained in an object implementing NamedNodeMap may also be accessed by an ordinal index, but this is simply to allow convenient enumeration of the contents of a NamedNodeMap, and does not imply that the DOM specifies an order to these Nodes.\n * NamedNodeMap objects in the DOM are live.\n * used for attributes or DocumentType entities \n */\nfunction NamedNodeMap() {\n};\n\nfunction _findNodeIndex(list,node){\n\tvar i = list.length;\n\twhile(i--){\n\t\tif(list[i] === node){return i}\n\t}\n}\n\nfunction _addNamedNode(el,list,newAttr,oldAttr){\n\tif(oldAttr){\n\t\tlist[_findNodeIndex(list,oldAttr)] = newAttr;\n\t}else{\n\t\tlist[list.length++] = newAttr;\n\t}\n\tif(el){\n\t\tnewAttr.ownerElement = el;\n\t\tvar doc = el.ownerDocument;\n\t\tif(doc){\n\t\t\toldAttr && _onRemoveAttribute(doc,el,oldAttr);\n\t\t\t_onAddAttribute(doc,el,newAttr);\n\t\t}\n\t}\n}\nfunction _removeNamedNode(el,list,attr){\n\t//console.log('remove attr:'+attr)\n\tvar i = _findNodeIndex(list,attr);\n\tif(i>=0){\n\t\tvar lastIndex = list.length-1\n\t\twhile(i<lastIndex){\n\t\t\tlist[i] = list[++i]\n\t\t}\n\t\tlist.length = lastIndex;\n\t\tif(el){\n\t\t\tvar doc = el.ownerDocument;\n\t\t\tif(doc){\n\t\t\t\t_onRemoveAttribute(doc,el,attr);\n\t\t\t\tattr.ownerElement = null;\n\t\t\t}\n\t\t}\n\t}else{\n\t\tthrow DOMException(NOT_FOUND_ERR,new Error(el.tagName+'@'+attr))\n\t}\n}\nNamedNodeMap.prototype = {\n\tlength:0,\n\titem:NodeList.prototype.item,\n\tgetNamedItem: function(key) {\n//\t\tif(key.indexOf(':')>0 || key == 'xmlns'){\n//\t\t\treturn null;\n//\t\t}\n\t\t//console.log()\n\t\tvar i = this.length;\n\t\twhile(i--){\n\t\t\tvar attr = this[i];\n\t\t\t//console.log(attr.nodeName,key)\n\t\t\tif(attr.nodeName == key){\n\t\t\t\treturn attr;\n\t\t\t}\n\t\t}\n\t},\n\tsetNamedItem: function(attr) {\n\t\tvar el = attr.ownerElement;\n\t\tif(el && el!=this._ownerElement){\n\t\t\tthrow new DOMException(INUSE_ATTRIBUTE_ERR);\n\t\t}\n\t\tvar oldAttr = this.getNamedItem(attr.nodeName);\n\t\t_addNamedNode(this._ownerElement,this,attr,oldAttr);\n\t\treturn oldAttr;\n\t},\n\t/* returns Node */\n\tsetNamedItemNS: function(attr) {// raises: WRONG_DOCUMENT_ERR,NO_MODIFICATION_ALLOWED_ERR,INUSE_ATTRIBUTE_ERR\n\t\tvar el = attr.ownerElement, oldAttr;\n\t\tif(el && el!=this._ownerElement){\n\t\t\tthrow new DOMException(INUSE_ATTRIBUTE_ERR);\n\t\t}\n\t\toldAttr = this.getNamedItemNS(attr.namespaceURI,attr.localName);\n\t\t_addNamedNode(this._ownerElement,this,attr,oldAttr);\n\t\treturn oldAttr;\n\t},\n\n\t/* returns Node */\n\tremoveNamedItem: function(key) {\n\t\tvar attr = this.getNamedItem(key);\n\t\t_removeNamedNode(this._ownerElement,this,attr);\n\t\treturn attr;\n\t\t\n\t\t\n\t},// raises: NOT_FOUND_ERR,NO_MODIFICATION_ALLOWED_ERR\n\t\n\t//for level2\n\tremoveNamedItemNS:function(namespaceURI,localName){\n\t\tvar attr = this.getNamedItemNS(namespaceURI,localName);\n\t\t_removeNamedNode(this._ownerElement,this,attr);\n\t\treturn attr;\n\t},\n\tgetNamedItemNS: function(namespaceURI, localName) {\n\t\tvar i = this.length;\n\t\twhile(i--){\n\t\t\tvar node = this[i];\n\t\t\tif(node.localName == localName && node.namespaceURI == namespaceURI){\n\t\t\t\treturn node;\n\t\t\t}\n\t\t}\n\t\treturn null;\n\t}\n};\n/**\n * @see http://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-102161490\n */\nfunction DOMImplementation(/* Object */ features) {\n\tthis._features = {};\n\tif (features) {\n\t\tfor (var feature in features) {\n\t\t\t this._features = features[feature];\n\t\t}\n\t}\n};\n\nDOMImplementation.prototype = {\n\thasFeature: function(/* string */ feature, /* string */ version) {\n\t\tvar versions = this._features[feature.toLowerCase()];\n\t\tif (versions && (!version || version in versions)) {\n\t\t\treturn true;\n\t\t} else {\n\t\t\treturn false;\n\t\t}\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateDocument:function(namespaceURI,  qualifiedName, doctype){// raises:INVALID_CHARACTER_ERR,NAMESPACE_ERR,WRONG_DOCUMENT_ERR\n\t\tvar doc = new Document();\n\t\tdoc.implementation = this;\n\t\tdoc.childNodes = new NodeList();\n\t\tdoc.doctype = doctype;\n\t\tif(doctype){\n\t\t\tdoc.appendChild(doctype);\n\t\t}\n\t\tif(qualifiedName){\n\t\t\tvar root = doc.createElementNS(namespaceURI,qualifiedName);\n\t\t\tdoc.appendChild(root);\n\t\t}\n\t\treturn doc;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateDocumentType:function(qualifiedName, publicId, systemId){// raises:INVALID_CHARACTER_ERR,NAMESPACE_ERR\n\t\tvar node = new DocumentType();\n\t\tnode.name = qualifiedName;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.publicId = publicId;\n\t\tnode.systemId = systemId;\n\t\t// Introduced in DOM Level 2:\n\t\t//readonly attribute DOMString        internalSubset;\n\t\t\n\t\t//TODO:..\n\t\t//  readonly attribute NamedNodeMap     entities;\n\t\t//  readonly attribute NamedNodeMap     notations;\n\t\treturn node;\n\t}\n};\n\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-1950641247\n */\n\nfunction Node() {\n};\n\nNode.prototype = {\n\tfirstChild : null,\n\tlastChild : null,\n\tpreviousSibling : null,\n\tnextSibling : null,\n\tattributes : null,\n\tparentNode : null,\n\tchildNodes : null,\n\townerDocument : null,\n\tnodeValue : null,\n\tnamespaceURI : null,\n\tprefix : null,\n\tlocalName : null,\n\t// Modified in DOM Level 2:\n\tinsertBefore:function(newChild, refChild){//raises \n\t\treturn _insertBefore(this,newChild,refChild);\n\t},\n\treplaceChild:function(newChild, oldChild){//raises \n\t\tthis.insertBefore(newChild,oldChild);\n\t\tif(oldChild){\n\t\t\tthis.removeChild(oldChild);\n\t\t}\n\t},\n\tremoveChild:function(oldChild){\n\t\treturn _removeChild(this,oldChild);\n\t},\n\tappendChild:function(newChild){\n\t\treturn this.insertBefore(newChild,null);\n\t},\n\thasChildNodes:function(){\n\t\treturn this.firstChild != null;\n\t},\n\tcloneNode:function(deep){\n\t\treturn cloneNode(this.ownerDocument||this,this,deep);\n\t},\n\t// Modified in DOM Level 2:\n\tnormalize:function(){\n\t\tvar child = this.firstChild;\n\t\twhile(child){\n\t\t\tvar next = child.nextSibling;\n\t\t\tif(next && next.nodeType == TEXT_NODE && child.nodeType == TEXT_NODE){\n\t\t\t\tthis.removeChild(next);\n\t\t\t\tchild.appendData(next.data);\n\t\t\t}else{\n\t\t\t\tchild.normalize();\n\t\t\t\tchild = next;\n\t\t\t}\n\t\t}\n\t},\n  \t// Introduced in DOM Level 2:\n\tisSupported:function(feature, version){\n\t\treturn this.ownerDocument.implementation.hasFeature(feature,version);\n\t},\n    // Introduced in DOM Level 2:\n    hasAttributes:function(){\n    \treturn this.attributes.length>0;\n    },\n    lookupPrefix:function(namespaceURI){\n    \tvar el = this;\n    \twhile(el){\n    \t\tvar map = el._nsMap;\n    \t\t//console.dir(map)\n    \t\tif(map){\n    \t\t\tfor(var n in map){\n    \t\t\t\tif(map[n] == namespaceURI){\n    \t\t\t\t\treturn n;\n    \t\t\t\t}\n    \t\t\t}\n    \t\t}\n    \t\tel = el.nodeType == ATTRIBUTE_NODE?el.ownerDocument : el.parentNode;\n    \t}\n    \treturn null;\n    },\n    // Introduced in DOM Level 3:\n    lookupNamespaceURI:function(prefix){\n    \tvar el = this;\n    \twhile(el){\n    \t\tvar map = el._nsMap;\n    \t\t//console.dir(map)\n    \t\tif(map){\n    \t\t\tif(prefix in map){\n    \t\t\t\treturn map[prefix] ;\n    \t\t\t}\n    \t\t}\n    \t\tel = el.nodeType == ATTRIBUTE_NODE?el.ownerDocument : el.parentNode;\n    \t}\n    \treturn null;\n    },\n    // Introduced in DOM Level 3:\n    isDefaultNamespace:function(namespaceURI){\n    \tvar prefix = this.lookupPrefix(namespaceURI);\n    \treturn prefix == null;\n    }\n};\n\n\nfunction _xmlEncoder(c){\n\treturn c == '<' && '&lt;' ||\n         c == '>' && '&gt;' ||\n         c == '&' && '&amp;' ||\n         c == '\"' && '&quot;' ||\n         '&#'+c.charCodeAt()+';'\n}\n\n\ncopy(NodeType,Node);\ncopy(NodeType,Node.prototype);\n\n/**\n * @param callback return true for continue,false for break\n * @return boolean true: break visit;\n */\nfunction _visitNode(node,callback){\n\tif(callback(node)){\n\t\treturn true;\n\t}\n\tif(node = node.firstChild){\n\t\tdo{\n\t\t\tif(_visitNode(node,callback)){return true}\n        }while(node=node.nextSibling)\n    }\n}\n\n\n\nfunction Document(){\n}\nfunction _onAddAttribute(doc,el,newAttr){\n\tdoc && doc._inc++;\n\tvar ns = newAttr.namespaceURI ;\n\tif(ns == 'http://www.w3.org/2000/xmlns/'){\n\t\t//update namespace\n\t\tel._nsMap[newAttr.prefix?newAttr.localName:''] = newAttr.value\n\t}\n}\nfunction _onRemoveAttribute(doc,el,newAttr,remove){\n\tdoc && doc._inc++;\n\tvar ns = newAttr.namespaceURI ;\n\tif(ns == 'http://www.w3.org/2000/xmlns/'){\n\t\t//update namespace\n\t\tdelete el._nsMap[newAttr.prefix?newAttr.localName:'']\n\t}\n}\nfunction _onUpdateChild(doc,el,newChild){\n\tif(doc && doc._inc){\n\t\tdoc._inc++;\n\t\t//update childNodes\n\t\tvar cs = el.childNodes;\n\t\tif(newChild){\n\t\t\tcs[cs.length++] = newChild;\n\t\t}else{\n\t\t\t//console.log(1)\n\t\t\tvar child = el.firstChild;\n\t\t\tvar i = 0;\n\t\t\twhile(child){\n\t\t\t\tcs[i++] = child;\n\t\t\t\tchild =child.nextSibling;\n\t\t\t}\n\t\t\tcs.length = i;\n\t\t}\n\t}\n}\n\n/**\n * attributes;\n * children;\n * \n * writeable properties:\n * nodeValue,Attr:value,CharacterData:data\n * prefix\n */\nfunction _removeChild(parentNode,child){\n\tvar previous = child.previousSibling;\n\tvar next = child.nextSibling;\n\tif(previous){\n\t\tprevious.nextSibling = next;\n\t}else{\n\t\tparentNode.firstChild = next\n\t}\n\tif(next){\n\t\tnext.previousSibling = previous;\n\t}else{\n\t\tparentNode.lastChild = previous;\n\t}\n\t_onUpdateChild(parentNode.ownerDocument,parentNode);\n\treturn child;\n}\n/**\n * preformance key(refChild == null)\n */\nfunction _insertBefore(parentNode,newChild,nextChild){\n\tvar cp = newChild.parentNode;\n\tif(cp){\n\t\tcp.removeChild(newChild);//remove and update\n\t}\n\tif(newChild.nodeType === DOCUMENT_FRAGMENT_NODE){\n\t\tvar newFirst = newChild.firstChild;\n\t\tif (newFirst == null) {\n\t\t\treturn newChild;\n\t\t}\n\t\tvar newLast = newChild.lastChild;\n\t}else{\n\t\tnewFirst = newLast = newChild;\n\t}\n\tvar pre = nextChild ? nextChild.previousSibling : parentNode.lastChild;\n\n\tnewFirst.previousSibling = pre;\n\tnewLast.nextSibling = nextChild;\n\t\n\t\n\tif(pre){\n\t\tpre.nextSibling = newFirst;\n\t}else{\n\t\tparentNode.firstChild = newFirst;\n\t}\n\tif(nextChild == null){\n\t\tparentNode.lastChild = newLast;\n\t}else{\n\t\tnextChild.previousSibling = newLast;\n\t}\n\tdo{\n\t\tnewFirst.parentNode = parentNode;\n\t}while(newFirst !== newLast && (newFirst= newFirst.nextSibling))\n\t_onUpdateChild(parentNode.ownerDocument||parentNode,parentNode);\n\t//console.log(parentNode.lastChild.nextSibling == null)\n\tif (newChild.nodeType == DOCUMENT_FRAGMENT_NODE) {\n\t\tnewChild.firstChild = newChild.lastChild = null;\n\t}\n\treturn newChild;\n}\nfunction _appendSingleChild(parentNode,newChild){\n\tvar cp = newChild.parentNode;\n\tif(cp){\n\t\tvar pre = parentNode.lastChild;\n\t\tcp.removeChild(newChild);//remove and update\n\t\tvar pre = parentNode.lastChild;\n\t}\n\tvar pre = parentNode.lastChild;\n\tnewChild.parentNode = parentNode;\n\tnewChild.previousSibling = pre;\n\tnewChild.nextSibling = null;\n\tif(pre){\n\t\tpre.nextSibling = newChild;\n\t}else{\n\t\tparentNode.firstChild = newChild;\n\t}\n\tparentNode.lastChild = newChild;\n\t_onUpdateChild(parentNode.ownerDocument,parentNode,newChild);\n\treturn newChild;\n\t//console.log(\"__aa\",parentNode.lastChild.nextSibling == null)\n}\nDocument.prototype = {\n\t//implementation : null,\n\tnodeName :  '#document',\n\tnodeType :  DOCUMENT_NODE,\n\tdoctype :  null,\n\tdocumentElement :  null,\n\t_inc : 1,\n\t\n\tinsertBefore :  function(newChild, refChild){//raises \n\t\tif(newChild.nodeType == DOCUMENT_FRAGMENT_NODE){\n\t\t\tvar child = newChild.firstChild;\n\t\t\twhile(child){\n\t\t\t\tvar next = child.nextSibling;\n\t\t\t\tthis.insertBefore(child,refChild);\n\t\t\t\tchild = next;\n\t\t\t}\n\t\t\treturn newChild;\n\t\t}\n\t\tif(this.documentElement == null && newChild.nodeType == ELEMENT_NODE){\n\t\t\tthis.documentElement = newChild;\n\t\t}\n\t\t\n\t\treturn _insertBefore(this,newChild,refChild),(newChild.ownerDocument = this),newChild;\n\t},\n\tremoveChild :  function(oldChild){\n\t\tif(this.documentElement == oldChild){\n\t\t\tthis.documentElement = null;\n\t\t}\n\t\treturn _removeChild(this,oldChild);\n\t},\n\t// Introduced in DOM Level 2:\n\timportNode : function(importedNode,deep){\n\t\treturn importNode(this,importedNode,deep);\n\t},\n\t// Introduced in DOM Level 2:\n\tgetElementById :\tfunction(id){\n\t\tvar rtv = null;\n\t\t_visitNode(this.documentElement,function(node){\n\t\t\tif(node.nodeType == ELEMENT_NODE){\n\t\t\t\tif(node.getAttribute('id') == id){\n\t\t\t\t\trtv = node;\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t\treturn rtv;\n\t},\n\t\n\tgetElementsByClassName: function(className) {\n\t\tvar pattern = new RegExp(\"(^|\\\\s)\" + className + \"(\\\\s|$)\");\n\t\treturn new LiveNodeList(this, function(base) {\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base.documentElement, function(node) {\n\t\t\t\tif(node !== base && node.nodeType == ELEMENT_NODE) {\n\t\t\t\t\tif(pattern.test(node.getAttribute('class'))) {\n\t\t\t\t\t\tls.push(node);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\t\t});\n\t},\n\t\n\t//document factory method:\n\tcreateElement :\tfunction(tagName){\n\t\tvar node = new Element();\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = tagName;\n\t\tnode.tagName = tagName;\n\t\tnode.childNodes = new NodeList();\n\t\tvar attrs\t= node.attributes = new NamedNodeMap();\n\t\tattrs._ownerElement = node;\n\t\treturn node;\n\t},\n\tcreateDocumentFragment :\tfunction(){\n\t\tvar node = new DocumentFragment();\n\t\tnode.ownerDocument = this;\n\t\tnode.childNodes = new NodeList();\n\t\treturn node;\n\t},\n\tcreateTextNode :\tfunction(data){\n\t\tvar node = new Text();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateComment :\tfunction(data){\n\t\tvar node = new Comment();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateCDATASection :\tfunction(data){\n\t\tvar node = new CDATASection();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateProcessingInstruction :\tfunction(target,data){\n\t\tvar node = new ProcessingInstruction();\n\t\tnode.ownerDocument = this;\n\t\tnode.tagName = node.target = target;\n\t\tnode.nodeValue= node.data = data;\n\t\treturn node;\n\t},\n\tcreateAttribute :\tfunction(name){\n\t\tvar node = new Attr();\n\t\tnode.ownerDocument\t= this;\n\t\tnode.name = name;\n\t\tnode.nodeName\t= name;\n\t\tnode.localName = name;\n\t\tnode.specified = true;\n\t\treturn node;\n\t},\n\tcreateEntityReference :\tfunction(name){\n\t\tvar node = new EntityReference();\n\t\tnode.ownerDocument\t= this;\n\t\tnode.nodeName\t= name;\n\t\treturn node;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateElementNS :\tfunction(namespaceURI,qualifiedName){\n\t\tvar node = new Element();\n\t\tvar pl = qualifiedName.split(':');\n\t\tvar attrs\t= node.attributes = new NamedNodeMap();\n\t\tnode.childNodes = new NodeList();\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.tagName = qualifiedName;\n\t\tnode.namespaceURI = namespaceURI;\n\t\tif(pl.length == 2){\n\t\t\tnode.prefix = pl[0];\n\t\t\tnode.localName = pl[1];\n\t\t}else{\n\t\t\t//el.prefix = null;\n\t\t\tnode.localName = qualifiedName;\n\t\t}\n\t\tattrs._ownerElement = node;\n\t\treturn node;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateAttributeNS :\tfunction(namespaceURI,qualifiedName){\n\t\tvar node = new Attr();\n\t\tvar pl = qualifiedName.split(':');\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.name = qualifiedName;\n\t\tnode.namespaceURI = namespaceURI;\n\t\tnode.specified = true;\n\t\tif(pl.length == 2){\n\t\t\tnode.prefix = pl[0];\n\t\t\tnode.localName = pl[1];\n\t\t}else{\n\t\t\t//el.prefix = null;\n\t\t\tnode.localName = qualifiedName;\n\t\t}\n\t\treturn node;\n\t}\n};\n_extends(Document,Node);\n\n\nfunction Element() {\n\tthis._nsMap = {};\n};\nElement.prototype = {\n\tnodeType : ELEMENT_NODE,\n\thasAttribute : function(name){\n\t\treturn this.getAttributeNode(name)!=null;\n\t},\n\tgetAttribute : function(name){\n\t\tvar attr = this.getAttributeNode(name);\n\t\treturn attr && attr.value || '';\n\t},\n\tgetAttributeNode : function(name){\n\t\treturn this.attributes.getNamedItem(name);\n\t},\n\tsetAttribute : function(name, value){\n\t\tvar attr = this.ownerDocument.createAttribute(name);\n\t\tattr.value = attr.nodeValue = \"\" + value;\n\t\tthis.setAttributeNode(attr)\n\t},\n\tremoveAttribute : function(name){\n\t\tvar attr = this.getAttributeNode(name)\n\t\tattr && this.removeAttributeNode(attr);\n\t},\n\t\n\t//four real opeartion method\n\tappendChild:function(newChild){\n\t\tif(newChild.nodeType === DOCUMENT_FRAGMENT_NODE){\n\t\t\treturn this.insertBefore(newChild,null);\n\t\t}else{\n\t\t\treturn _appendSingleChild(this,newChild);\n\t\t}\n\t},\n\tsetAttributeNode : function(newAttr){\n\t\treturn this.attributes.setNamedItem(newAttr);\n\t},\n\tsetAttributeNodeNS : function(newAttr){\n\t\treturn this.attributes.setNamedItemNS(newAttr);\n\t},\n\tremoveAttributeNode : function(oldAttr){\n\t\t//console.log(this == oldAttr.ownerElement)\n\t\treturn this.attributes.removeNamedItem(oldAttr.nodeName);\n\t},\n\t//get real attribute name,and remove it by removeAttributeNode\n\tremoveAttributeNS : function(namespaceURI, localName){\n\t\tvar old = this.getAttributeNodeNS(namespaceURI, localName);\n\t\told && this.removeAttributeNode(old);\n\t},\n\t\n\thasAttributeNS : function(namespaceURI, localName){\n\t\treturn this.getAttributeNodeNS(namespaceURI, localName)!=null;\n\t},\n\tgetAttributeNS : function(namespaceURI, localName){\n\t\tvar attr = this.getAttributeNodeNS(namespaceURI, localName);\n\t\treturn attr && attr.value || '';\n\t},\n\tsetAttributeNS : function(namespaceURI, qualifiedName, value){\n\t\tvar attr = this.ownerDocument.createAttributeNS(namespaceURI, qualifiedName);\n\t\tattr.value = attr.nodeValue = \"\" + value;\n\t\tthis.setAttributeNode(attr)\n\t},\n\tgetAttributeNodeNS : function(namespaceURI, localName){\n\t\treturn this.attributes.getNamedItemNS(namespaceURI, localName);\n\t},\n\t\n\tgetElementsByTagName : function(tagName){\n\t\treturn new LiveNodeList(this,function(base){\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base,function(node){\n\t\t\t\tif(node !== base && node.nodeType == ELEMENT_NODE && (tagName === '*' || node.tagName == tagName)){\n\t\t\t\t\tls.push(node);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\t\t});\n\t},\n\tgetElementsByTagNameNS : function(namespaceURI, localName){\n\t\treturn new LiveNodeList(this,function(base){\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base,function(node){\n\t\t\t\tif(node !== base && node.nodeType === ELEMENT_NODE && (namespaceURI === '*' || node.namespaceURI === namespaceURI) && (localName === '*' || node.localName == localName)){\n\t\t\t\t\tls.push(node);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\t\t\t\n\t\t});\n\t}\n};\nDocument.prototype.getElementsByTagName = Element.prototype.getElementsByTagName;\nDocument.prototype.getElementsByTagNameNS = Element.prototype.getElementsByTagNameNS;\n\n\n_extends(Element,Node);\nfunction Attr() {\n};\nAttr.prototype.nodeType = ATTRIBUTE_NODE;\n_extends(Attr,Node);\n\n\nfunction CharacterData() {\n};\nCharacterData.prototype = {\n\tdata : '',\n\tsubstringData : function(offset, count) {\n\t\treturn this.data.substring(offset, offset+count);\n\t},\n\tappendData: function(text) {\n\t\ttext = this.data+text;\n\t\tthis.nodeValue = this.data = text;\n\t\tthis.length = text.length;\n\t},\n\tinsertData: function(offset,text) {\n\t\tthis.replaceData(offset,0,text);\n\t\n\t},\n\tappendChild:function(newChild){\n\t\tthrow new Error(ExceptionMessage[HIERARCHY_REQUEST_ERR])\n\t},\n\tdeleteData: function(offset, count) {\n\t\tthis.replaceData(offset,count,\"\");\n\t},\n\treplaceData: function(offset, count, text) {\n\t\tvar start = this.data.substring(0,offset);\n\t\tvar end = this.data.substring(offset+count);\n\t\ttext = start + text + end;\n\t\tthis.nodeValue = this.data = text;\n\t\tthis.length = text.length;\n\t}\n}\n_extends(CharacterData,Node);\nfunction Text() {\n};\nText.prototype = {\n\tnodeName : \"#text\",\n\tnodeType : TEXT_NODE,\n\tsplitText : function(offset) {\n\t\tvar text = this.data;\n\t\tvar newText = text.substring(offset);\n\t\ttext = text.substring(0, offset);\n\t\tthis.data = this.nodeValue = text;\n\t\tthis.length = text.length;\n\t\tvar newNode = this.ownerDocument.createTextNode(newText);\n\t\tif(this.parentNode){\n\t\t\tthis.parentNode.insertBefore(newNode, this.nextSibling);\n\t\t}\n\t\treturn newNode;\n\t}\n}\n_extends(Text,CharacterData);\nfunction Comment() {\n};\nComment.prototype = {\n\tnodeName : \"#comment\",\n\tnodeType : COMMENT_NODE\n}\n_extends(Comment,CharacterData);\n\nfunction CDATASection() {\n};\nCDATASection.prototype = {\n\tnodeName : \"#cdata-section\",\n\tnodeType : CDATA_SECTION_NODE\n}\n_extends(CDATASection,CharacterData);\n\n\nfunction DocumentType() {\n};\nDocumentType.prototype.nodeType = DOCUMENT_TYPE_NODE;\n_extends(DocumentType,Node);\n\nfunction Notation() {\n};\nNotation.prototype.nodeType = NOTATION_NODE;\n_extends(Notation,Node);\n\nfunction Entity() {\n};\nEntity.prototype.nodeType = ENTITY_NODE;\n_extends(Entity,Node);\n\nfunction EntityReference() {\n};\nEntityReference.prototype.nodeType = ENTITY_REFERENCE_NODE;\n_extends(EntityReference,Node);\n\nfunction DocumentFragment() {\n};\nDocumentFragment.prototype.nodeName =\t\"#document-fragment\";\nDocumentFragment.prototype.nodeType =\tDOCUMENT_FRAGMENT_NODE;\n_extends(DocumentFragment,Node);\n\n\nfunction ProcessingInstruction() {\n}\nProcessingInstruction.prototype.nodeType = PROCESSING_INSTRUCTION_NODE;\n_extends(ProcessingInstruction,Node);\nfunction XMLSerializer(){}\nXMLSerializer.prototype.serializeToString = function(node,isHtml,nodeFilter){\n\treturn nodeSerializeToString.call(node,isHtml,nodeFilter);\n}\nNode.prototype.toString = nodeSerializeToString;\nfunction nodeSerializeToString(isHtml,nodeFilter){\n\tvar buf = [];\n\tvar refNode = this.nodeType == 9 && this.documentElement || this;\n\tvar prefix = refNode.prefix;\n\tvar uri = refNode.namespaceURI;\n\t\n\tif(uri && prefix == null){\n\t\t//console.log(prefix)\n\t\tvar prefix = refNode.lookupPrefix(uri);\n\t\tif(prefix == null){\n\t\t\t//isHTML = true;\n\t\t\tvar visibleNamespaces=[\n\t\t\t{namespace:uri,prefix:null}\n\t\t\t//{namespace:uri,prefix:''}\n\t\t\t]\n\t\t}\n\t}\n\tserializeToString(this,buf,isHtml,nodeFilter,visibleNamespaces);\n\t//console.log('###',this.nodeType,uri,prefix,buf.join(''))\n\treturn buf.join('');\n}\nfunction needNamespaceDefine(node,isHTML, visibleNamespaces) {\n\tvar prefix = node.prefix||'';\n\tvar uri = node.namespaceURI;\n\tif (!prefix && !uri){\n\t\treturn false;\n\t}\n\tif (prefix === \"xml\" && uri === \"http://www.w3.org/XML/1998/namespace\" \n\t\t|| uri == 'http://www.w3.org/2000/xmlns/'){\n\t\treturn false;\n\t}\n\t\n\tvar i = visibleNamespaces.length \n\t//console.log('@@@@',node.tagName,prefix,uri,visibleNamespaces)\n\twhile (i--) {\n\t\tvar ns = visibleNamespaces[i];\n\t\t// get namespace prefix\n\t\t//console.log(node.nodeType,node.tagName,ns.prefix,prefix)\n\t\tif (ns.prefix == prefix){\n\t\t\treturn ns.namespace != uri;\n\t\t}\n\t}\n\t//console.log(isHTML,uri,prefix=='')\n\t//if(isHTML && prefix ==null && uri == 'http://www.w3.org/1999/xhtml'){\n\t//\treturn false;\n\t//}\n\t//node.flag = '11111'\n\t//console.error(3,true,node.flag,node.prefix,node.namespaceURI)\n\treturn true;\n}\nfunction serializeToString(node,buf,isHTML,nodeFilter,visibleNamespaces){\n\tif(nodeFilter){\n\t\tnode = nodeFilter(node);\n\t\tif(node){\n\t\t\tif(typeof node == 'string'){\n\t\t\t\tbuf.push(node);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}else{\n\t\t\treturn;\n\t\t}\n\t\t//buf.sort.apply(attrs, attributeSorter);\n\t}\n\tswitch(node.nodeType){\n\tcase ELEMENT_NODE:\n\t\tif (!visibleNamespaces) visibleNamespaces = [];\n\t\tvar startVisibleNamespaces = visibleNamespaces.length;\n\t\tvar attrs = node.attributes;\n\t\tvar len = attrs.length;\n\t\tvar child = node.firstChild;\n\t\tvar nodeName = node.tagName;\n\t\t\n\t\tisHTML =  (htmlns === node.namespaceURI) ||isHTML \n\t\tbuf.push('<',nodeName);\n\t\t\n\t\t\n\t\t\n\t\tfor(var i=0;i<len;i++){\n\t\t\t// add namespaces for attributes\n\t\t\tvar attr = attrs.item(i);\n\t\t\tif (attr.prefix == 'xmlns') {\n\t\t\t\tvisibleNamespaces.push({ prefix: attr.localName, namespace: attr.value });\n\t\t\t}else if(attr.nodeName == 'xmlns'){\n\t\t\t\tvisibleNamespaces.push({ prefix: '', namespace: attr.value });\n\t\t\t}\n\t\t}\n\t\tfor(var i=0;i<len;i++){\n\t\t\tvar attr = attrs.item(i);\n\t\t\tif (needNamespaceDefine(attr,isHTML, visibleNamespaces)) {\n\t\t\t\tvar prefix = attr.prefix||'';\n\t\t\t\tvar uri = attr.namespaceURI;\n\t\t\t\tvar ns = prefix ? ' xmlns:' + prefix : \" xmlns\";\n\t\t\t\tbuf.push(ns, '=\"' , uri , '\"');\n\t\t\t\tvisibleNamespaces.push({ prefix: prefix, namespace:uri });\n\t\t\t}\n\t\t\tserializeToString(attr,buf,isHTML,nodeFilter,visibleNamespaces);\n\t\t}\n\t\t// add namespace for current node\t\t\n\t\tif (needNamespaceDefine(node,isHTML, visibleNamespaces)) {\n\t\t\tvar prefix = node.prefix||'';\n\t\t\tvar uri = node.namespaceURI;\n\t\t\tif (uri) {\n\t\t\t\t// Avoid empty namespace value like xmlns:ds=\"\"\n\t\t\t\t// Empty namespace URL will we produce an invalid XML document\n\t\t\t\tvar ns = prefix ? ' xmlns:' + prefix : \" xmlns\";\n\t\t\t\tbuf.push(ns, '=\"' , uri , '\"');\n\t\t\t\tvisibleNamespaces.push({ prefix: prefix, namespace:uri });\n\t\t\t}\n\t\t}\n\t\t\n\t\tif(child || isHTML && !/^(?:meta|link|img|br|hr|input)$/i.test(nodeName)){\n\t\t\tbuf.push('>');\n\t\t\t//if is cdata child node\n\t\t\tif(isHTML && /^script$/i.test(nodeName)){\n\t\t\t\twhile(child){\n\t\t\t\t\tif(child.data){\n\t\t\t\t\t\tbuf.push(child.data);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tserializeToString(child,buf,isHTML,nodeFilter,visibleNamespaces);\n\t\t\t\t\t}\n\t\t\t\t\tchild = child.nextSibling;\n\t\t\t\t}\n\t\t\t}else\n\t\t\t{\n\t\t\t\twhile(child){\n\t\t\t\t\tserializeToString(child,buf,isHTML,nodeFilter,visibleNamespaces);\n\t\t\t\t\tchild = child.nextSibling;\n\t\t\t\t}\n\t\t\t}\n\t\t\tbuf.push('</',nodeName,'>');\n\t\t}else{\n\t\t\tbuf.push('/>');\n\t\t}\n\t\t// remove added visible namespaces\n\t\t//visibleNamespaces.length = startVisibleNamespaces;\n\t\treturn;\n\tcase DOCUMENT_NODE:\n\tcase DOCUMENT_FRAGMENT_NODE:\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tserializeToString(child,buf,isHTML,nodeFilter,visibleNamespaces);\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t\treturn;\n\tcase ATTRIBUTE_NODE:\n\t\t/**\n\t\t * Well-formedness constraint: No < in Attribute Values\n\t\t * The replacement text of any entity referred to directly or indirectly in an attribute value must not contain a <.\n\t\t * @see https://www.w3.org/TR/xml/#CleanAttrVals\n\t\t * @see https://www.w3.org/TR/xml/#NT-AttValue\n\t\t */\n\t\treturn buf.push(' ', node.name, '=\"', node.value.replace(/[<&\"]/g,_xmlEncoder), '\"');\n\tcase TEXT_NODE:\n\t\t/**\n\t\t * The ampersand character (&) and the left angle bracket (<) must not appear in their literal form,\n\t\t * except when used as markup delimiters, or within a comment, a processing instruction, or a CDATA section.\n\t\t * If they are needed elsewhere, they must be escaped using either numeric character references or the strings\n\t\t * `&amp;` and `&lt;` respectively.\n\t\t * The right angle bracket (>) may be represented using the string \" &gt; \", and must, for compatibility,\n\t\t * be escaped using either `&gt;` or a character reference when it appears in the string `]]>` in content,\n\t\t * when that string is not marking the end of a CDATA section.\n\t\t *\n\t\t * In the content of elements, character data is any string of characters\n\t\t * which does not contain the start-delimiter of any markup\n\t\t * and does not include the CDATA-section-close delimiter, `]]>`.\n\t\t *\n\t\t * @see https://www.w3.org/TR/xml/#NT-CharData\n\t\t */\n\t\treturn buf.push(node.data\n\t\t\t.replace(/[<&]/g,_xmlEncoder)\n\t\t\t.replace(/]]>/g, ']]&gt;')\n\t\t);\n\tcase CDATA_SECTION_NODE:\n\t\treturn buf.push( '<![CDATA[',node.data,']]>');\n\tcase COMMENT_NODE:\n\t\treturn buf.push( \"<!--\",node.data,\"-->\");\n\tcase DOCUMENT_TYPE_NODE:\n\t\tvar pubid = node.publicId;\n\t\tvar sysid = node.systemId;\n\t\tbuf.push('<!DOCTYPE ',node.name);\n\t\tif(pubid){\n\t\t\tbuf.push(' PUBLIC ', pubid);\n\t\t\tif (sysid && sysid!='.') {\n\t\t\t\tbuf.push(' ', sysid);\n\t\t\t}\n\t\t\tbuf.push('>');\n\t\t}else if(sysid && sysid!='.'){\n\t\t\tbuf.push(' SYSTEM ', sysid, '>');\n\t\t}else{\n\t\t\tvar sub = node.internalSubset;\n\t\t\tif(sub){\n\t\t\t\tbuf.push(\" [\",sub,\"]\");\n\t\t\t}\n\t\t\tbuf.push(\">\");\n\t\t}\n\t\treturn;\n\tcase PROCESSING_INSTRUCTION_NODE:\n\t\treturn buf.push( \"<?\",node.target,\" \",node.data,\"?>\");\n\tcase ENTITY_REFERENCE_NODE:\n\t\treturn buf.push( '&',node.nodeName,';');\n\t//case ENTITY_NODE:\n\t//case NOTATION_NODE:\n\tdefault:\n\t\tbuf.push('??',node.nodeName);\n\t}\n}\nfunction importNode(doc,node,deep){\n\tvar node2;\n\tswitch (node.nodeType) {\n\tcase ELEMENT_NODE:\n\t\tnode2 = node.cloneNode(false);\n\t\tnode2.ownerDocument = doc;\n\t\t//var attrs = node2.attributes;\n\t\t//var len = attrs.length;\n\t\t//for(var i=0;i<len;i++){\n\t\t\t//node2.setAttributeNodeNS(importNode(doc,attrs.item(i),deep));\n\t\t//}\n\tcase DOCUMENT_FRAGMENT_NODE:\n\t\tbreak;\n\tcase ATTRIBUTE_NODE:\n\t\tdeep = true;\n\t\tbreak;\n\t//case ENTITY_REFERENCE_NODE:\n\t//case PROCESSING_INSTRUCTION_NODE:\n\t////case TEXT_NODE:\n\t//case CDATA_SECTION_NODE:\n\t//case COMMENT_NODE:\n\t//\tdeep = false;\n\t//\tbreak;\n\t//case DOCUMENT_NODE:\n\t//case DOCUMENT_TYPE_NODE:\n\t//cannot be imported.\n\t//case ENTITY_NODE:\n\t//case NOTATION_NODE：\n\t//can not hit in level3\n\t//default:throw e;\n\t}\n\tif(!node2){\n\t\tnode2 = node.cloneNode(false);//false\n\t}\n\tnode2.ownerDocument = doc;\n\tnode2.parentNode = null;\n\tif(deep){\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tnode2.appendChild(importNode(doc,child,deep));\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t}\n\treturn node2;\n}\n//\n//var _relationMap = {firstChild:1,lastChild:1,previousSibling:1,nextSibling:1,\n//\t\t\t\t\tattributes:1,childNodes:1,parentNode:1,documentElement:1,doctype,};\nfunction cloneNode(doc,node,deep){\n\tvar node2 = new node.constructor();\n\tfor(var n in node){\n\t\tvar v = node[n];\n\t\tif(typeof v != 'object' ){\n\t\t\tif(v != node2[n]){\n\t\t\t\tnode2[n] = v;\n\t\t\t}\n\t\t}\n\t}\n\tif(node.childNodes){\n\t\tnode2.childNodes = new NodeList();\n\t}\n\tnode2.ownerDocument = doc;\n\tswitch (node2.nodeType) {\n\tcase ELEMENT_NODE:\n\t\tvar attrs\t= node.attributes;\n\t\tvar attrs2\t= node2.attributes = new NamedNodeMap();\n\t\tvar len = attrs.length\n\t\tattrs2._ownerElement = node2;\n\t\tfor(var i=0;i<len;i++){\n\t\t\tnode2.setAttributeNode(cloneNode(doc,attrs.item(i),true));\n\t\t}\n\t\tbreak;;\n\tcase ATTRIBUTE_NODE:\n\t\tdeep = true;\n\t}\n\tif(deep){\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tnode2.appendChild(cloneNode(doc,child,deep));\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t}\n\treturn node2;\n}\n\nfunction __set__(object,key,value){\n\tobject[key] = value\n}\n//do dynamic\ntry{\n\tif(Object.defineProperty){\n\t\tObject.defineProperty(LiveNodeList.prototype,'length',{\n\t\t\tget:function(){\n\t\t\t\t_updateLiveList(this);\n\t\t\t\treturn this.$$length;\n\t\t\t}\n\t\t});\n\t\tObject.defineProperty(Node.prototype,'textContent',{\n\t\t\tget:function(){\n\t\t\t\treturn getTextContent(this);\n\t\t\t},\n\t\t\tset:function(data){\n\t\t\t\tswitch(this.nodeType){\n\t\t\t\tcase ELEMENT_NODE:\n\t\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\t\t\twhile(this.firstChild){\n\t\t\t\t\t\tthis.removeChild(this.firstChild);\n\t\t\t\t\t}\n\t\t\t\t\tif(data || String(data)){\n\t\t\t\t\t\tthis.appendChild(this.ownerDocument.createTextNode(data));\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\t//TODO:\n\t\t\t\t\tthis.data = data;\n\t\t\t\t\tthis.value = data;\n\t\t\t\t\tthis.nodeValue = data;\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t\t\n\t\tfunction getTextContent(node){\n\t\t\tswitch(node.nodeType){\n\t\t\tcase ELEMENT_NODE:\n\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\t\tvar buf = [];\n\t\t\t\tnode = node.firstChild;\n\t\t\t\twhile(node){\n\t\t\t\t\tif(node.nodeType!==7 && node.nodeType !==8){\n\t\t\t\t\t\tbuf.push(getTextContent(node));\n\t\t\t\t\t}\n\t\t\t\t\tnode = node.nextSibling;\n\t\t\t\t}\n\t\t\t\treturn buf.join('');\n\t\t\tdefault:\n\t\t\t\treturn node.nodeValue;\n\t\t\t}\n\t\t}\n\t\t__set__ = function(object,key,value){\n\t\t\t//console.log(value)\n\t\t\tobject['$$'+key] = value\n\t\t}\n\t}\n}catch(e){//ie8\n}\n\n//if(typeof require == 'function'){\n\texports.Node = Node;\n\texports.DOMException = DOMException;\n\texports.DOMImplementation = DOMImplementation;\n\texports.XMLSerializer = XMLSerializer;\n//}\n", "var togeojson = require('./toGeoJSON')\nvar tokml = require('./toKml')\nvar JSZip = require('JSZip')\n\n//geojson转kml\nexport function toKml(geojson, options) {\n  return tokml(geojson, options)\n}\n\nlet getDom = (xml) => new DOMParser().parseFromString(xml, 'text/xml')\nlet getExtension = (fileName) => fileName.split('.').pop()\n\nlet getKmlDom = (kmzFile) => {\n  var zip = new JSZip()\n  return zip.loadAsync(kmzFile).then((zip) => {\n    let kmlDom = null\n    zip.forEach((relPath, file) => {\n      if (getExtension(relPath) === 'kml' && kmlDom === null) {\n        kmlDom = file.async('string').then(getDom)\n      }\n    })\n    return kmlDom || Promise.reject('No kml file found')\n  })\n}\n\n//kml转geojson\nexport function toGeoJSON(doc) {\n  if (!doc) return Promise.reject('参数不能为空')\n\n  if (isString(doc)) {\n    let extension = getExtension(doc)\n    if (extension === 'kml') {\n      return Cesium.Resource.fetchXML(doc).then(function (kmlDom) {\n        return togeojson.kml(kmlDom)\n      })\n    } else if (extension === 'kmz') {\n      return Cesium.Resource.fetchBlob(doc)\n        .then(function (xml) {\n          return getKmlDom(xml)\n        })\n        .then(function (kmlDom) {\n          return togeojson.kml(kmlDom)\n        })\n    } else {\n      //直接传kml字符串文档\n      let geojson = togeojson.kml(getKmlDom(doc))\n      return Promise.resolve(geojson)\n    }\n  } else if (doc.getRootNode) {\n    //直接传docmect文档\n    let geojson = togeojson.kml(doc)\n    return Promise.resolve(geojson)\n  } else {\n    //直接传blob\n    return getKmlDom(doc).then(function (kmlDom) {\n      return togeojson.kml(kmlDom)\n    })\n  }\n}\n\nfunction isString(str) {\n  return typeof str == 'string' && str.constructor == String\n}\n", "var toGeoJSON = (function() {\n    'use strict';\n\n    var removeSpace = /\\s*/g,\n        trimSpace = /^\\s*|\\s*$/g,\n        splitSpace = /\\s+/;\n    // generate a short, numeric hash of a string\n    function okhash(x) {\n        if (!x || !x.length) return 0;\n        for (var i = 0, h = 0; i < x.length; i++) {\n            h = ((h << 5) - h) + x.charCodeAt(i) | 0;\n        } return h;\n    }\n    // all Y children of X\n    function get(x, y) { return x.getElementsByTagName(y); }\n    function attr(x, y) { return x.getAttribute(y); }\n    function attrf(x, y) { return parseFloat(attr(x, y)); }\n    // one Y child of X, if any, otherwise null\n    function get1(x, y) { var n = get(x, y); return n.length ? n[0] : null; }\n    // https://developer.mozilla.org/en-US/docs/Web/API/Node.normalize\n    function norm(el) { if (el.normalize) { el.normalize(); } return el; }\n    // cast array x into numbers\n    function numarray(x) {\n        for (var j = 0, o = []; j < x.length; j++) { o[j] = parseFloat(x[j]); }\n        return o;\n    }\n    // get the content of a text node, if any\n    function nodeVal(x) {\n        if (x) { norm(x); }\n        return (x && x.textContent) || '';\n    }\n    // get the contents of multiple text nodes, if present\n    function getMulti(x, ys) {\n        var o = {}, n, k;\n        for (k = 0; k < ys.length; k++) {\n            n = get1(x, ys[k]);\n            if (n) o[ys[k]] = nodeVal(n);\n        }\n        return o;\n    }\n    // add properties of Y to X, overwriting if present in both\n    function extend(x, y) { for (var k in y) x[k] = y[k]; }\n    // get one coordinate from a coordinate array, if any\n    function coord1(v) { return numarray(v.replace(removeSpace, '').split(',')); }\n    // get all coordinates from a coordinate array as [[],[]]\n    function coord(v) {\n        var coords = v.replace(trimSpace, '').split(splitSpace),\n            o = [];\n        for (var i = 0; i < coords.length; i++) {\n            o.push(coord1(coords[i]));\n        }\n        return o;\n    }\n    function coordPair(x) {\n        var ll = [attrf(x, 'lon'), attrf(x, 'lat')],\n            ele = get1(x, 'ele'),\n            // handle namespaced attribute in browser\n            heartRate = get1(x, 'gpxtpx:hr') || get1(x, 'hr'),\n            time = get1(x, 'time'),\n            e;\n        if (ele) {\n            e = parseFloat(nodeVal(ele));\n            if (!isNaN(e)) {\n                ll.push(e);\n            }\n        }\n        return {\n            coordinates: ll,\n            time: time ? nodeVal(time) : null,\n            heartRate: heartRate ? parseFloat(nodeVal(heartRate)) : null\n        };\n    }\n\n    // create a new feature collection parent object\n    function fc() {\n        return {\n            type: 'FeatureCollection',\n            features: []\n        };\n    }\n\n    var serializer;\n    if (typeof XMLSerializer !== 'undefined') {\n        /* istanbul ignore next */\n        serializer = new XMLSerializer();\n    // only require xmldom in a node environment\n    } else if (typeof exports === 'object' && typeof process === 'object' && !process.browser) {\n        serializer = new (require('xmldom').XMLSerializer)();\n    }\n    function xml2str(str) {\n        // IE9 will create a new XMLSerializer but it'll crash immediately.\n        // This line is ignored because we don't run coverage tests in IE9\n        /* istanbul ignore next */\n        if (str.xml !== undefined) return str.xml;\n        return serializer.serializeToString(str);\n    }\n\n    var t = {\n        kml: function(doc) {\n\n            var gj = fc(),\n                // styleindex keeps track of hashed styles in order to match features\n                styleIndex = {}, styleByHash = {},\n                // stylemapindex keeps track of style maps to expose in properties\n                styleMapIndex = {},\n                // atomic geospatial types supported by KML - MultiGeometry is\n                // handled separately\n                geotypes = ['Polygon', 'LineString', 'Point', 'Track', 'gx:Track'],\n                // all root placemarks in the file\n                placemarks = get(doc, 'Placemark'),\n                styles = get(doc, 'Style'),\n                styleMaps = get(doc, 'StyleMap');\n\n            for (var k = 0; k < styles.length; k++) {\n                var hash = okhash(xml2str(styles[k])).toString(16);\n                styleIndex['#' + attr(styles[k], 'id')] = hash;\n                styleByHash[hash] = styles[k];\n            }\n            for (var l = 0; l < styleMaps.length; l++) {\n                styleIndex['#' + attr(styleMaps[l], 'id')] = okhash(xml2str(styleMaps[l])).toString(16);\n                var pairs = get(styleMaps[l], 'Pair');\n                var pairsMap = {};\n                for (var m = 0; m < pairs.length; m++) {\n                    pairsMap[nodeVal(get1(pairs[m], 'key'))] = nodeVal(get1(pairs[m], 'styleUrl'));\n                }\n                styleMapIndex['#' + attr(styleMaps[l], 'id')] = pairsMap;\n\n            }\n            for (var j = 0; j < placemarks.length; j++) {\n                gj.features = gj.features.concat(getPlacemark(placemarks[j]));\n            }\n            function kmlColor(v) {\n                var color, opacity;\n                v = v || '';\n                if (v.substr(0, 1) === '#') { v = v.substr(1); }\n                if (v.length === 6 || v.length === 3) { color = v; }\n                if (v.length === 8) {\n                    opacity = parseInt(v.substr(0, 2), 16) / 255;\n                    color = '#' + v.substr(6, 2) +\n                        v.substr(4, 2) +\n                        v.substr(2, 2);\n                }\n                return [color, isNaN(opacity) ? undefined : opacity];\n            }\n            function gxCoord(v) { return numarray(v.split(' ')); }\n            function gxCoords(root) {\n                var elems = get(root, 'coord', 'gx'), coords = [], times = [];\n                if (elems.length === 0) elems = get(root, 'gx:coord');\n                for (var i = 0; i < elems.length; i++) coords.push(gxCoord(nodeVal(elems[i])));\n                var timeElems = get(root, 'when');\n                for (var j = 0; j < timeElems.length; j++) times.push(nodeVal(timeElems[j]));\n                return {\n                    coords: coords,\n                    times: times\n                };\n            }\n            function getGeometry(root) {\n                var geomNode, geomNodes, i, j, k, geoms = [], coordTimes = [];\n                if (get1(root, 'MultiGeometry')) { return getGeometry(get1(root, 'MultiGeometry')); }\n                if (get1(root, 'MultiTrack')) { return getGeometry(get1(root, 'MultiTrack')); }\n                if (get1(root, 'gx:MultiTrack')) { return getGeometry(get1(root, 'gx:MultiTrack')); }\n                for (i = 0; i < geotypes.length; i++) {\n                    geomNodes = get(root, geotypes[i]);\n                    if (geomNodes) {\n                        for (j = 0; j < geomNodes.length; j++) {\n                            geomNode = geomNodes[j];\n                            if (geotypes[i] === 'Point') {\n                                geoms.push({\n                                    type: 'Point',\n                                    coordinates: coord1(nodeVal(get1(geomNode, 'coordinates')))\n                                });\n                            } else if (geotypes[i] === 'LineString') {\n                                geoms.push({\n                                    type: 'LineString',\n                                    coordinates: coord(nodeVal(get1(geomNode, 'coordinates')))\n                                });\n                            } else if (geotypes[i] === 'Polygon') {\n                                var rings = get(geomNode, 'LinearRing'),\n                                    coords = [];\n                                for (k = 0; k < rings.length; k++) {\n                                    coords.push(coord(nodeVal(get1(rings[k], 'coordinates'))));\n                                }\n                                geoms.push({\n                                    type: 'Polygon',\n                                    coordinates: coords\n                                });\n                            } else if (geotypes[i] === 'Track' ||\n                                geotypes[i] === 'gx:Track') {\n                                var track = gxCoords(geomNode);\n                                geoms.push({\n                                    type: 'LineString',\n                                    coordinates: track.coords\n                                });\n                                if (track.times.length) coordTimes.push(track.times);\n                            }\n                        }\n                    }\n                }\n                return {\n                    geoms: geoms,\n                    coordTimes: coordTimes\n                };\n            }\n            function getPlacemark(root) {\n                var geomsAndTimes = getGeometry(root), i, properties = {},\n                    name = nodeVal(get1(root, 'name')),\n                    styleUrl = nodeVal(get1(root, 'styleUrl')),\n                    description = nodeVal(get1(root, 'description')),\n                    timeSpan = get1(root, 'TimeSpan'),\n                    timeStamp = get1(root, 'TimeStamp'),\n                    extendedData = get1(root, 'ExtendedData'),\n                    lineStyle = get1(root, 'LineStyle'),\n                    polyStyle = get1(root, 'PolyStyle'),\n                    visibility = get1(root, 'visibility');\n\n                if (!geomsAndTimes.geoms.length) return [];\n                if (name) properties.name = name;\n                if (styleUrl) {\n                    if (styleUrl[0] !== '#') {\n                        styleUrl = '#' + styleUrl;\n                    }\n\n                    properties.styleUrl = styleUrl;\n                    if (styleIndex[styleUrl]) {\n                        properties.styleHash = styleIndex[styleUrl];\n                    }\n                    if (styleMapIndex[styleUrl]) {\n                        properties.styleMapHash = styleMapIndex[styleUrl];\n                        properties.styleHash = styleIndex[styleMapIndex[styleUrl].normal];\n                    }\n                    // Try to populate the lineStyle or polyStyle since we got the style hash\n                    var style = styleByHash[properties.styleHash];\n                    if (style) {\n                        if (!lineStyle) lineStyle = get1(style, 'LineStyle');\n                        if (!polyStyle) polyStyle = get1(style, 'PolyStyle');\n                    }\n                }\n                if (description) properties.description = description;\n                if (timeSpan) {\n                    var begin = nodeVal(get1(timeSpan, 'begin'));\n                    var end = nodeVal(get1(timeSpan, 'end'));\n                    properties.timespan = { begin: begin, end: end };\n                }\n                if (timeStamp) {\n                    properties.timestamp = nodeVal(get1(timeStamp, 'when'));\n                }\n                if (lineStyle) {\n                    var linestyles = kmlColor(nodeVal(get1(lineStyle, 'color'))),\n                        color = linestyles[0],\n                        opacity = linestyles[1],\n                        width = parseFloat(nodeVal(get1(lineStyle, 'width')));\n                    if (color) properties.stroke = color;\n                    if (!isNaN(opacity)) properties['stroke-opacity'] = opacity;\n                    if (!isNaN(width)) properties['stroke-width'] = width;\n                }\n                if (polyStyle) {\n                    var polystyles = kmlColor(nodeVal(get1(polyStyle, 'color'))),\n                        pcolor = polystyles[0],\n                        popacity = polystyles[1],\n                        fill = nodeVal(get1(polyStyle, 'fill')),\n                        outline = nodeVal(get1(polyStyle, 'outline'));\n                    if (pcolor) properties.fill = pcolor;\n                    if (!isNaN(popacity)) properties['fill-opacity'] = popacity;\n                    if (fill) properties['fill-opacity'] = fill === '1' ? properties['fill-opacity'] || 1 : 0;\n                    if (outline) properties['stroke-opacity'] = outline === '1' ? properties['stroke-opacity'] || 1 : 0;\n                }\n                if (extendedData) {\n                    var datas = get(extendedData, 'Data'),\n                        simpleDatas = get(extendedData, 'SimpleData');\n\n                    for (i = 0; i < datas.length; i++) {\n                        properties[datas[i].getAttribute('name')] = nodeVal(get1(datas[i], 'value'));\n                    }\n                    for (i = 0; i < simpleDatas.length; i++) {\n                        properties[simpleDatas[i].getAttribute('name')] = nodeVal(simpleDatas[i]);\n                    }\n                }\n                if (visibility) {\n                    properties.visibility = nodeVal(visibility);\n                }\n                if (geomsAndTimes.coordTimes.length) {\n                    properties.coordTimes = (geomsAndTimes.coordTimes.length === 1) ?\n                        geomsAndTimes.coordTimes[0] : geomsAndTimes.coordTimes;\n                }\n                var feature = {\n                    type: 'Feature',\n                    geometry: (geomsAndTimes.geoms.length === 1) ? geomsAndTimes.geoms[0] : {\n                        type: 'GeometryCollection',\n                        geometries: geomsAndTimes.geoms\n                    },\n                    properties: properties\n                };\n                if (attr(root, 'id')) feature.id = attr(root, 'id');\n                return [feature];\n            }\n            return gj;\n        },\n        gpx: function(doc) {\n            var i,\n                tracks = get(doc, 'trk'),\n                routes = get(doc, 'rte'),\n                waypoints = get(doc, 'wpt'),\n                // a feature collection\n                gj = fc(),\n                feature;\n            for (i = 0; i < tracks.length; i++) {\n                feature = getTrack(tracks[i]);\n                if (feature) gj.features.push(feature);\n            }\n            for (i = 0; i < routes.length; i++) {\n                feature = getRoute(routes[i]);\n                if (feature) gj.features.push(feature);\n            }\n            for (i = 0; i < waypoints.length; i++) {\n                gj.features.push(getPoint(waypoints[i]));\n            }\n            function getPoints(node, pointname) {\n                var pts = get(node, pointname),\n                    line = [],\n                    times = [],\n                    heartRates = [],\n                    l = pts.length;\n                if (l < 2) return {};  // Invalid line in GeoJSON\n                for (var i = 0; i < l; i++) {\n                    var c = coordPair(pts[i]);\n                    line.push(c.coordinates);\n                    if (c.time) times.push(c.time);\n                    if (c.heartRate) heartRates.push(c.heartRate);\n                }\n                return {\n                    line: line,\n                    times: times,\n                    heartRates: heartRates\n                };\n            }\n            function getTrack(node) {\n                var segments = get(node, 'trkseg'),\n                    track = [],\n                    times = [],\n                    heartRates = [],\n                    line;\n                for (var i = 0; i < segments.length; i++) {\n                    line = getPoints(segments[i], 'trkpt');\n                    if (line) {\n                        if (line.line) track.push(line.line);\n                        if (line.times && line.times.length) times.push(line.times);\n                        if (line.heartRates && line.heartRates.length) heartRates.push(line.heartRates);\n                    }\n                }\n                if (track.length === 0) return;\n                var properties = getProperties(node);\n                if (times.length) properties.coordTimes = track.length === 1 ? times[0] : times;\n                if (heartRates.length) properties.heartRates = track.length === 1 ? heartRates[0] : heartRates;\n                return {\n                    type: 'Feature',\n                    properties: properties,\n                    geometry: {\n                        type: track.length === 1 ? 'LineString' : 'MultiLineString',\n                        coordinates: track.length === 1 ? track[0] : track\n                    }\n                };\n            }\n            function getRoute(node) {\n                var line = getPoints(node, 'rtept');\n                if (!line.line) return;\n                var routeObj = {\n                    type: 'Feature',\n                    properties: getProperties(node),\n                    geometry: {\n                        type: 'LineString',\n                        coordinates: line.line\n                    }\n                };\n                return routeObj;\n            }\n            function getPoint(node) {\n                var prop = getProperties(node);\n                extend(prop, getMulti(node, ['sym', 'type']));\n                return {\n                    type: 'Feature',\n                    properties: prop,\n                    geometry: {\n                        type: 'Point',\n                        coordinates: coordPair(node).coordinates\n                    }\n                };\n            }\n            function getProperties(node) {\n                var prop, links;\n                prop = getMulti(node, ['name', 'cmt', 'desc', 'time', 'keywords']);\n                links = get(node, 'link');\n                if (links.length) prop.links = [];\n                for (var i = 0, link; i < links.length; i++) {\n                    link = { href: attr(links[i], 'href') };\n                    extend(link, getMulti(links[i], ['text', 'type']));\n                    prop.links.push(link);\n                }\n                return prop;\n            }\n            return gj;\n        }\n    };\n    return t;\n})();\n\nif (typeof module !== 'undefined') module.exports = toGeoJSON;\n", "function DOMParser(options){\n\tthis.options = options ||{locator:{}};\n}\n\nDOMParser.prototype.parseFromString = function(source,mimeType){\n\tvar options = this.options;\n\tvar sax =  new XMLReader();\n\tvar domBuilder = options.domBuilder || new DOMHandler();//contentHandler and LexicalHandler\n\tvar errorHandler = options.errorHandler;\n\tvar locator = options.locator;\n\tvar defaultNSMap = options.xmlns||{};\n\tvar isHTML = /\\/x?html?$/.test(mimeType);//mimeType.toLowerCase().indexOf('html') > -1;\n  \tvar entityMap = isHTML?htmlEntity.entityMap:{'lt':'<','gt':'>','amp':'&','quot':'\"','apos':\"'\"};\n\tif(locator){\n\t\tdomBuilder.setDocumentLocator(locator)\n\t}\n\n\tsax.errorHandler = buildErrorHandler(errorHandler,domBuilder,locator);\n\tsax.domBuilder = options.domBuilder || domBuilder;\n\tif(isHTML){\n\t\tdefaultNSMap['']= 'http://www.w3.org/1999/xhtml';\n\t}\n\tdefaultNSMap.xml = defaultNSMap.xml || 'http://www.w3.org/XML/1998/namespace';\n\tif(source && typeof source === 'string'){\n\t\tsax.parse(source,defaultNSMap,entityMap);\n\t}else{\n\t\tsax.errorHandler.error(\"invalid doc source\");\n\t}\n\treturn domBuilder.doc;\n}\nfunction buildErrorHandler(errorImpl,domBuilder,locator){\n\tif(!errorImpl){\n\t\tif(domBuilder instanceof DOMHandler){\n\t\t\treturn domBuilder;\n\t\t}\n\t\terrorImpl = domBuilder ;\n\t}\n\tvar errorHandler = {}\n\tvar isCallback = errorImpl instanceof Function;\n\tlocator = locator||{}\n\tfunction build(key){\n\t\tvar fn = errorImpl[key];\n\t\tif(!fn && isCallback){\n\t\t\tfn = errorImpl.length == 2?function(msg){errorImpl(key,msg)}:errorImpl;\n\t\t}\n\t\terrorHandler[key] = fn && function(msg){\n\t\t\tfn('[xmldom '+key+']\\t'+msg+_locator(locator));\n\t\t}||function(){};\n\t}\n\tbuild('warning');\n\tbuild('error');\n\tbuild('fatalError');\n\treturn errorHandler;\n}\n\n//console.log('#\\n\\n\\n\\n\\n\\n\\n####')\n/**\n * +ContentHandler+ErrorHandler\n * +LexicalHandler+EntityResolver2\n * -DeclHandler-DTDHandler\n *\n * DefaultHandler:EntityResolver, DTDHandler, ContentHandler, ErrorHandler\n * DefaultHandler2:DefaultHandler,LexicalHandler, DeclHandler, EntityResolver2\n * @link http://www.saxproject.org/apidoc/org/xml/sax/helpers/DefaultHandler.html\n */\nfunction DOMHandler() {\n    this.cdata = false;\n}\nfunction position(locator,node){\n\tnode.lineNumber = locator.lineNumber;\n\tnode.columnNumber = locator.columnNumber;\n}\n/**\n * @see org.xml.sax.ContentHandler#startDocument\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ContentHandler.html\n */\nDOMHandler.prototype = {\n\tstartDocument : function() {\n    \tthis.doc = new DOMImplementation().createDocument(null, null, null);\n    \tif (this.locator) {\n        \tthis.doc.documentURI = this.locator.systemId;\n    \t}\n\t},\n\tstartElement:function(namespaceURI, localName, qName, attrs) {\n\t\tvar doc = this.doc;\n\t    var el = doc.createElementNS(namespaceURI, qName||localName);\n\t    var len = attrs.length;\n\t    appendElement(this, el);\n\t    this.currentElement = el;\n\n\t\tthis.locator && position(this.locator,el)\n\t    for (var i = 0 ; i < len; i++) {\n\t        var namespaceURI = attrs.getURI(i);\n\t        var value = attrs.getValue(i);\n\t        var qName = attrs.getQName(i);\n\t\t\tvar attr = doc.createAttributeNS(namespaceURI, qName);\n\t\t\tthis.locator &&position(attrs.getLocator(i),attr);\n\t\t\tattr.value = attr.nodeValue = value;\n\t\t\tel.setAttributeNode(attr)\n\t    }\n\t},\n\tendElement:function(namespaceURI, localName, qName) {\n\t\tvar current = this.currentElement\n\t\tvar tagName = current.tagName;\n\t\tthis.currentElement = current.parentNode;\n\t},\n\tstartPrefixMapping:function(prefix, uri) {\n\t},\n\tendPrefixMapping:function(prefix) {\n\t},\n\tprocessingInstruction:function(target, data) {\n\t    var ins = this.doc.createProcessingInstruction(target, data);\n\t    this.locator && position(this.locator,ins)\n\t    appendElement(this, ins);\n\t},\n\tignorableWhitespace:function(ch, start, length) {\n\t},\n\tcharacters:function(chars, start, length) {\n\t\tchars = _toString.apply(this,arguments)\n\t\t//console.log(chars)\n\t\tif(chars){\n\t\t\tif (this.cdata) {\n\t\t\t\tvar charNode = this.doc.createCDATASection(chars);\n\t\t\t} else {\n\t\t\t\tvar charNode = this.doc.createTextNode(chars);\n\t\t\t}\n\t\t\tif(this.currentElement){\n\t\t\t\tthis.currentElement.appendChild(charNode);\n\t\t\t}else if(/^\\s*$/.test(chars)){\n\t\t\t\tthis.doc.appendChild(charNode);\n\t\t\t\t//process xml\n\t\t\t}\n\t\t\tthis.locator && position(this.locator,charNode)\n\t\t}\n\t},\n\tskippedEntity:function(name) {\n\t},\n\tendDocument:function() {\n\t\tthis.doc.normalize();\n\t},\n\tsetDocumentLocator:function (locator) {\n\t    if(this.locator = locator){// && !('lineNumber' in locator)){\n\t    \tlocator.lineNumber = 0;\n\t    }\n\t},\n\t//LexicalHandler\n\tcomment:function(chars, start, length) {\n\t\tchars = _toString.apply(this,arguments)\n\t    var comm = this.doc.createComment(chars);\n\t    this.locator && position(this.locator,comm)\n\t    appendElement(this, comm);\n\t},\n\n\tstartCDATA:function() {\n\t    //used in characters() methods\n\t    this.cdata = true;\n\t},\n\tendCDATA:function() {\n\t    this.cdata = false;\n\t},\n\n\tstartDTD:function(name, publicId, systemId) {\n\t\tvar impl = this.doc.implementation;\n\t    if (impl && impl.createDocumentType) {\n\t        var dt = impl.createDocumentType(name, publicId, systemId);\n\t        this.locator && position(this.locator,dt)\n\t        appendElement(this, dt);\n\t    }\n\t},\n\t/**\n\t * @see org.xml.sax.ErrorHandler\n\t * @link http://www.saxproject.org/apidoc/org/xml/sax/ErrorHandler.html\n\t */\n\twarning:function(error) {\n\t\tconsole.warn('[xmldom warning]\\t'+error,_locator(this.locator));\n\t},\n\terror:function(error) {\n\t\tconsole.error('[xmldom error]\\t'+error,_locator(this.locator));\n\t},\n\tfatalError:function(error) {\n\t\tthrow new ParseError(error, this.locator);\n\t}\n}\nfunction _locator(l){\n\tif(l){\n\t\treturn '\\n@'+(l.systemId ||'')+'#[line:'+l.lineNumber+',col:'+l.columnNumber+']'\n\t}\n}\nfunction _toString(chars,start,length){\n\tif(typeof chars == 'string'){\n\t\treturn chars.substr(start,length)\n\t}else{//java sax connect width xmldom on rhino(what about: \"? && !(chars instanceof String)\")\n\t\tif(chars.length >= start+length || start){\n\t\t\treturn new java.lang.String(chars,start,length)+'';\n\t\t}\n\t\treturn chars;\n\t}\n}\n\n/*\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/LexicalHandler.html\n * used method of org.xml.sax.ext.LexicalHandler:\n *  #comment(chars, start, length)\n *  #startCDATA()\n *  #endCDATA()\n *  #startDTD(name, publicId, systemId)\n *\n *\n * IGNORED method of org.xml.sax.ext.LexicalHandler:\n *  #endDTD()\n *  #startEntity(name)\n *  #endEntity(name)\n *\n *\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/DeclHandler.html\n * IGNORED method of org.xml.sax.ext.DeclHandler\n * \t#attributeDecl(eName, aName, type, mode, value)\n *  #elementDecl(name, model)\n *  #externalEntityDecl(name, publicId, systemId)\n *  #internalEntityDecl(name, value)\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/EntityResolver2.html\n * IGNORED method of org.xml.sax.EntityResolver2\n *  #resolveEntity(String name,String publicId,String baseURI,String systemId)\n *  #resolveEntity(publicId, systemId)\n *  #getExternalSubset(name, baseURI)\n * @link http://www.saxproject.org/apidoc/org/xml/sax/DTDHandler.html\n * IGNORED method of org.xml.sax.DTDHandler\n *  #notationDecl(name, publicId, systemId) {};\n *  #unparsedEntityDecl(name, publicId, systemId, notationName) {};\n */\n\"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl\".replace(/\\w+/g,function(key){\n\tDOMHandler.prototype[key] = function(){return null}\n})\n\n/* Private static helpers treated below as private instance methods, so don't need to add these to the public API; we might use a Relator to also get rid of non-standard public properties */\nfunction appendElement (hander,node) {\n    if (!hander.currentElement) {\n        hander.doc.appendChild(node);\n    } else {\n        hander.currentElement.appendChild(node);\n    }\n}//appendChild and setAttributeNS are preformance key\n\n//if(typeof require == 'function'){\nvar htmlEntity = require('./entities');\nvar sax = require('./sax');\nvar XMLReader = sax.XMLReader;\nvar ParseError = sax.ParseError;\nvar DOMImplementation = exports.DOMImplementation = require('./dom').DOMImplementation;\nexports.XMLSerializer = require('./dom').XMLSerializer ;\nexports.DOMParser = DOMParser;\nexports.__DOMHandler = DOMHandler;\n//}\n", "exports.entityMap = {\n       lt: '<',\n       gt: '>',\n       amp: '&',\n       quot: '\"',\n       apos: \"'\",\n       Agrave: \"À\",\n       Aacute: \"Á\",\n       Acirc: \"Â\",\n       Atilde: \"Ã\",\n       Auml: \"Ä\",\n       Aring: \"Å\",\n       AElig: \"Æ\",\n       Ccedil: \"Ç\",\n       Egrave: \"È\",\n       Eacute: \"É\",\n       Ecirc: \"Ê\",\n       Euml: \"Ë\",\n       Igrave: \"Ì\",\n       Iacute: \"Í\",\n       Icirc: \"Î\",\n       Iuml: \"Ï\",\n       ETH: \"Ð\",\n       Ntilde: \"Ñ\",\n       Ograve: \"Ò\",\n       Oacute: \"Ó\",\n       Ocirc: \"Ô\",\n       Otilde: \"Õ\",\n       Ouml: \"Ö\",\n       Oslash: \"Ø\",\n       Ugrave: \"Ù\",\n       Uacute: \"Ú\",\n       Ucirc: \"Û\",\n       Uuml: \"Ü\",\n       Yacute: \"Ý\",\n       THORN: \"Þ\",\n       szlig: \"ß\",\n       agrave: \"à\",\n       aacute: \"á\",\n       acirc: \"â\",\n       atilde: \"ã\",\n       auml: \"ä\",\n       aring: \"å\",\n       aelig: \"æ\",\n       ccedil: \"ç\",\n       egrave: \"è\",\n       eacute: \"é\",\n       ecirc: \"ê\",\n       euml: \"ë\",\n       igrave: \"ì\",\n       iacute: \"í\",\n       icirc: \"î\",\n       iuml: \"ï\",\n       eth: \"ð\",\n       ntilde: \"ñ\",\n       ograve: \"ò\",\n       oacute: \"ó\",\n       ocirc: \"ô\",\n       otilde: \"õ\",\n       ouml: \"ö\",\n       oslash: \"ø\",\n       ugrave: \"ù\",\n       uacute: \"ú\",\n       ucirc: \"û\",\n       uuml: \"ü\",\n       yacute: \"ý\",\n       thorn: \"þ\",\n       yuml: \"ÿ\",\n       nbsp: \"\\u00a0\",\n       iexcl: \"¡\",\n       cent: \"¢\",\n       pound: \"£\",\n       curren: \"¤\",\n       yen: \"¥\",\n       brvbar: \"¦\",\n       sect: \"§\",\n       uml: \"¨\",\n       copy: \"©\",\n       ordf: \"ª\",\n       laquo: \"«\",\n       not: \"¬\",\n       shy: \"­­\",\n       reg: \"®\",\n       macr: \"¯\",\n       deg: \"°\",\n       plusmn: \"±\",\n       sup2: \"²\",\n       sup3: \"³\",\n       acute: \"´\",\n       micro: \"µ\",\n       para: \"¶\",\n       middot: \"·\",\n       cedil: \"¸\",\n       sup1: \"¹\",\n       ordm: \"º\",\n       raquo: \"»\",\n       frac14: \"¼\",\n       frac12: \"½\",\n       frac34: \"¾\",\n       iquest: \"¿\",\n       times: \"×\",\n       divide: \"÷\",\n       forall: \"∀\",\n       part: \"∂\",\n       exist: \"∃\",\n       empty: \"∅\",\n       nabla: \"∇\",\n       isin: \"∈\",\n       notin: \"∉\",\n       ni: \"∋\",\n       prod: \"∏\",\n       sum: \"∑\",\n       minus: \"−\",\n       lowast: \"∗\",\n       radic: \"√\",\n       prop: \"∝\",\n       infin: \"∞\",\n       ang: \"∠\",\n       and: \"∧\",\n       or: \"∨\",\n       cap: \"∩\",\n       cup: \"∪\",\n       'int': \"∫\",\n       there4: \"∴\",\n       sim: \"∼\",\n       cong: \"≅\",\n       asymp: \"≈\",\n       ne: \"≠\",\n       equiv: \"≡\",\n       le: \"≤\",\n       ge: \"≥\",\n       sub: \"⊂\",\n       sup: \"⊃\",\n       nsub: \"⊄\",\n       sube: \"⊆\",\n       supe: \"⊇\",\n       oplus: \"⊕\",\n       otimes: \"⊗\",\n       perp: \"⊥\",\n       sdot: \"⋅\",\n       Alpha: \"Α\",\n       Beta: \"Β\",\n       Gamma: \"Γ\",\n       Delta: \"Δ\",\n       Epsilon: \"Ε\",\n       Zeta: \"Ζ\",\n       Eta: \"Η\",\n       Theta: \"Θ\",\n       Iota: \"Ι\",\n       Kappa: \"Κ\",\n       Lambda: \"Λ\",\n       Mu: \"Μ\",\n       Nu: \"Ν\",\n       Xi: \"Ξ\",\n       Omicron: \"Ο\",\n       Pi: \"Π\",\n       Rho: \"Ρ\",\n       Sigma: \"Σ\",\n       Tau: \"Τ\",\n       Upsilon: \"Υ\",\n       Phi: \"Φ\",\n       Chi: \"Χ\",\n       Psi: \"Ψ\",\n       Omega: \"Ω\",\n       alpha: \"α\",\n       beta: \"β\",\n       gamma: \"γ\",\n       delta: \"δ\",\n       epsilon: \"ε\",\n       zeta: \"ζ\",\n       eta: \"η\",\n       theta: \"θ\",\n       iota: \"ι\",\n       kappa: \"κ\",\n       lambda: \"λ\",\n       mu: \"μ\",\n       nu: \"ν\",\n       xi: \"ξ\",\n       omicron: \"ο\",\n       pi: \"π\",\n       rho: \"ρ\",\n       sigmaf: \"ς\",\n       sigma: \"σ\",\n       tau: \"τ\",\n       upsilon: \"υ\",\n       phi: \"φ\",\n       chi: \"χ\",\n       psi: \"ψ\",\n       omega: \"ω\",\n       thetasym: \"ϑ\",\n       upsih: \"ϒ\",\n       piv: \"ϖ\",\n       OElig: \"Œ\",\n       oelig: \"œ\",\n       Scaron: \"Š\",\n       scaron: \"š\",\n       Yuml: \"Ÿ\",\n       fnof: \"ƒ\",\n       circ: \"ˆ\",\n       tilde: \"˜\",\n       ensp: \" \",\n       emsp: \" \",\n       thinsp: \" \",\n       zwnj: \"‌\",\n       zwj: \"‍\",\n       lrm: \"‎\",\n       rlm: \"‏\",\n       ndash: \"–\",\n       mdash: \"—\",\n       lsquo: \"‘\",\n       rsquo: \"’\",\n       sbquo: \"‚\",\n       ldquo: \"“\",\n       rdquo: \"”\",\n       bdquo: \"„\",\n       dagger: \"†\",\n       Dagger: \"‡\",\n       bull: \"•\",\n       hellip: \"…\",\n       permil: \"‰\",\n       prime: \"′\",\n       Prime: \"″\",\n       lsaquo: \"‹\",\n       rsaquo: \"›\",\n       oline: \"‾\",\n       euro: \"€\",\n       trade: \"™\",\n       larr: \"←\",\n       uarr: \"↑\",\n       rarr: \"→\",\n       darr: \"↓\",\n       harr: \"↔\",\n       crarr: \"↵\",\n       lceil: \"⌈\",\n       rceil: \"⌉\",\n       lfloor: \"⌊\",\n       rfloor: \"⌋\",\n       loz: \"◊\",\n       spades: \"♠\",\n       clubs: \"♣\",\n       hearts: \"♥\",\n       diams: \"♦\"\n};\n", "//[4]   \tNameStartChar\t   ::=   \t\":\" | [A-Z] | \"_\" | [a-z] | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x2FF] | [#x370-#x37D] | [#x37F-#x1FFF] | [#x200C-#x200D] | [#x2070-#x218F] | [#x2C00-#x2FEF] | [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n//[4a]   \tNameChar\t   ::=   \tNameStartChar | \"-\" | \".\" | [0-9] | #xB7 | [#x0300-#x036F] | [#x203F-#x2040]\n//[5]   \tName\t   ::=   \tNameStartChar (NameChar)*\nvar nameStartChar = /[A-Z_a-z\\xC0-\\xD6\\xD8-\\xF6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]///\\u10000-\\uEFFFF\nvar nameChar = new RegExp(\"[\\\\-\\\\.0-9\"+nameStartChar.source.slice(1,-1)+\"\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040]\");\nvar tagNamePattern = new RegExp('^'+nameStartChar.source+nameChar.source+'*(?:\\:'+nameStartChar.source+nameChar.source+'*)?$');\n//var tagNamePattern = /^[a-zA-Z_][\\w\\-\\.]*(?:\\:[a-zA-Z_][\\w\\-\\.]*)?$/\n//var handlers = 'resolveEntity,getExternalSubset,characters,endDocument,endElement,endPrefixMapping,ignorableWhitespace,processingInstruction,setDocumentLocator,skippedEntity,startDocument,startElement,startPrefixMapping,notationDecl,unparsedEntityDecl,error,fatalError,warning,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,comment,endCDATA,endDTD,endEntity,startCDATA,startDTD,startEntity'.split(',')\n\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\nvar S_TAG = 0;//tag name offerring\nvar S_ATTR = 1;//attr name offerring \nvar S_ATTR_SPACE=2;//attr name end and space offer\nvar S_EQ = 3;//=space?\nvar S_ATTR_NOQUOT_VALUE = 4;//attr value(no quot value only)\nvar S_ATTR_END = 5;//attr value end and no space(quot end)\nvar S_TAG_SPACE = 6;//(attr value end || tag end ) && (space offer)\nvar S_TAG_CLOSE = 7;//closed el<el />\n\n/**\n * Creates an error that will not be caught by XMLReader aka the SAX parser.\n *\n * @param {string} message\n * @param {any?} locator Optional, can provide details about the location in the source\n * @constructor\n */\nfunction ParseError(message, locator) {\n\tthis.message = message\n\tthis.locator = locator\n\tif(Error.captureStackTrace) Error.captureStackTrace(this, ParseError);\n}\nParseError.prototype = new Error();\nParseError.prototype.name = ParseError.name\n\nfunction XMLReader(){\n\t\n}\n\nXMLReader.prototype = {\n\tparse:function(source,defaultNSMap,entityMap){\n\t\tvar domBuilder = this.domBuilder;\n\t\tdomBuilder.startDocument();\n\t\t_copy(defaultNSMap ,defaultNSMap = {})\n\t\tparse(source,defaultNSMap,entityMap,\n\t\t\t\tdomBuilder,this.errorHandler);\n\t\tdomBuilder.endDocument();\n\t}\n}\nfunction parse(source,defaultNSMapCopy,entityMap,domBuilder,errorHandler){\n\tfunction fixedFromCharCode(code) {\n\t\t// String.prototype.fromCharCode does not supports\n\t\t// > 2 bytes unicode chars directly\n\t\tif (code > 0xffff) {\n\t\t\tcode -= 0x10000;\n\t\t\tvar surrogate1 = 0xd800 + (code >> 10)\n\t\t\t\t, surrogate2 = 0xdc00 + (code & 0x3ff);\n\n\t\t\treturn String.fromCharCode(surrogate1, surrogate2);\n\t\t} else {\n\t\t\treturn String.fromCharCode(code);\n\t\t}\n\t}\n\tfunction entityReplacer(a){\n\t\tvar k = a.slice(1,-1);\n\t\tif(k in entityMap){\n\t\t\treturn entityMap[k]; \n\t\t}else if(k.charAt(0) === '#'){\n\t\t\treturn fixedFromCharCode(parseInt(k.substr(1).replace('x','0x')))\n\t\t}else{\n\t\t\terrorHandler.error('entity not found:'+a);\n\t\t\treturn a;\n\t\t}\n\t}\n\tfunction appendText(end){//has some bugs\n\t\tif(end>start){\n\t\t\tvar xt = source.substring(start,end).replace(/&#?\\w+;/g,entityReplacer);\n\t\t\tlocator&&position(start);\n\t\t\tdomBuilder.characters(xt,0,end-start);\n\t\t\tstart = end\n\t\t}\n\t}\n\tfunction position(p,m){\n\t\twhile(p>=lineEnd && (m = linePattern.exec(source))){\n\t\t\tlineStart = m.index;\n\t\t\tlineEnd = lineStart + m[0].length;\n\t\t\tlocator.lineNumber++;\n\t\t\t//console.log('line++:',locator,startPos,endPos)\n\t\t}\n\t\tlocator.columnNumber = p-lineStart+1;\n\t}\n\tvar lineStart = 0;\n\tvar lineEnd = 0;\n\tvar linePattern = /.*(?:\\r\\n?|\\n)|.*$/g\n\tvar locator = domBuilder.locator;\n\t\n\tvar parseStack = [{currentNSMap:defaultNSMapCopy}]\n\tvar closeMap = {};\n\tvar start = 0;\n\twhile(true){\n\t\ttry{\n\t\t\tvar tagStart = source.indexOf('<',start);\n\t\t\tif(tagStart<0){\n\t\t\t\tif(!source.substr(start).match(/^\\s*$/)){\n\t\t\t\t\tvar doc = domBuilder.doc;\n\t    \t\t\tvar text = doc.createTextNode(source.substr(start));\n\t    \t\t\tdoc.appendChild(text);\n\t    \t\t\tdomBuilder.currentElement = text;\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(tagStart>start){\n\t\t\t\tappendText(tagStart);\n\t\t\t}\n\t\t\tswitch(source.charAt(tagStart+1)){\n\t\t\tcase '/':\n\t\t\t\tvar end = source.indexOf('>',tagStart+3);\n\t\t\t\tvar tagName = source.substring(tagStart+2,end);\n\t\t\t\tvar config = parseStack.pop();\n\t\t\t\tif(end<0){\n\t\t\t\t\t\n\t        \t\ttagName = source.substring(tagStart+2).replace(/[\\s<].*/,'');\n\t        \t\terrorHandler.error(\"end tag name: \"+tagName+' is not complete:'+config.tagName);\n\t        \t\tend = tagStart+1+tagName.length;\n\t        \t}else if(tagName.match(/\\s</)){\n\t        \t\ttagName = tagName.replace(/[\\s<].*/,'');\n\t        \t\terrorHandler.error(\"end tag name: \"+tagName+' maybe not complete');\n\t        \t\tend = tagStart+1+tagName.length;\n\t\t\t\t}\n\t\t\t\tvar localNSMap = config.localNSMap;\n\t\t\t\tvar endMatch = config.tagName == tagName;\n\t\t\t\tvar endIgnoreCaseMach = endMatch || config.tagName&&config.tagName.toLowerCase() == tagName.toLowerCase()\n\t\t        if(endIgnoreCaseMach){\n\t\t        \tdomBuilder.endElement(config.uri,config.localName,tagName);\n\t\t\t\t\tif(localNSMap){\n\t\t\t\t\t\tfor(var prefix in localNSMap){\n\t\t\t\t\t\t\tdomBuilder.endPrefixMapping(prefix) ;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(!endMatch){\n\t\t            \terrorHandler.fatalError(\"end tag name: \"+tagName+' is not match the current start tagName:'+config.tagName ); // No known test case\n\t\t\t\t\t}\n\t\t        }else{\n\t\t        \tparseStack.push(config)\n\t\t        }\n\t\t\t\t\n\t\t\t\tend++;\n\t\t\t\tbreak;\n\t\t\t\t// end elment\n\t\t\tcase '?':// <?...?>\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tend = parseInstruction(source,tagStart,domBuilder);\n\t\t\t\tbreak;\n\t\t\tcase '!':// <!doctype,<![CDATA,<!--\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tend = parseDCC(source,tagStart,domBuilder,errorHandler);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tvar el = new ElementAttributes();\n\t\t\t\tvar currentNSMap = parseStack[parseStack.length-1].currentNSMap;\n\t\t\t\t//elStartEnd\n\t\t\t\tvar end = parseElementStartPart(source,tagStart,el,currentNSMap,entityReplacer,errorHandler);\n\t\t\t\tvar len = el.length;\n\t\t\t\t\n\t\t\t\t\n\t\t\t\tif(!el.closed && fixSelfClosed(source,end,el.tagName,closeMap)){\n\t\t\t\t\tel.closed = true;\n\t\t\t\t\tif(!entityMap.nbsp){\n\t\t\t\t\t\terrorHandler.warning('unclosed xml attribute');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(locator && len){\n\t\t\t\t\tvar locator2 = copyLocator(locator,{});\n\t\t\t\t\t//try{//attribute position fixed\n\t\t\t\t\tfor(var i = 0;i<len;i++){\n\t\t\t\t\t\tvar a = el[i];\n\t\t\t\t\t\tposition(a.offset);\n\t\t\t\t\t\ta.locator = copyLocator(locator,{});\n\t\t\t\t\t}\n\t\t\t\t\tdomBuilder.locator = locator2\n\t\t\t\t\tif(appendElement(el,domBuilder,currentNSMap)){\n\t\t\t\t\t\tparseStack.push(el)\n\t\t\t\t\t}\n\t\t\t\t\tdomBuilder.locator = locator;\n\t\t\t\t}else{\n\t\t\t\t\tif(appendElement(el,domBuilder,currentNSMap)){\n\t\t\t\t\t\tparseStack.push(el)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t\n\t\t\t\t\n\t\t\t\tif(el.uri === 'http://www.w3.org/1999/xhtml' && !el.closed){\n\t\t\t\t\tend = parseHtmlSpecialContent(source,end,el.tagName,entityReplacer,domBuilder)\n\t\t\t\t}else{\n\t\t\t\t\tend++;\n\t\t\t\t}\n\t\t\t}\n\t\t}catch(e){\n\t\t\tif (e instanceof ParseError) {\n\t\t\t\tthrow e;\n\t\t\t}\n\t\t\terrorHandler.error('element parse error: '+e)\n\t\t\tend = -1;\n\t\t}\n\t\tif(end>start){\n\t\t\tstart = end;\n\t\t}else{\n\t\t\t//TODO: 这里有可能sax回退，有位置错误风险\n\t\t\tappendText(Math.max(tagStart,start)+1);\n\t\t}\n\t}\n}\nfunction copyLocator(f,t){\n\tt.lineNumber = f.lineNumber;\n\tt.columnNumber = f.columnNumber;\n\treturn t;\n}\n\n/**\n * @see #appendElement(source,elStartEnd,el,selfClosed,entityReplacer,domBuilder,parseStack);\n * @return end of the elementStartPart(end of elementEndPart for selfClosed el)\n */\nfunction parseElementStartPart(source,start,el,currentNSMap,entityReplacer,errorHandler){\n\n\t/**\n\t * @param {string} qname\n\t * @param {string} value\n\t * @param {number} startIndex\n\t */\n\tfunction addAttribute(qname, value, startIndex) {\n\t\tif (qname in el.attributeNames) errorHandler.fatalError('Attribute ' + qname + ' redefined')\n\t\tel.addValue(qname, value, startIndex)\n\t}\n\tvar attrName;\n\tvar value;\n\tvar p = ++start;\n\tvar s = S_TAG;//status\n\twhile(true){\n\t\tvar c = source.charAt(p);\n\t\tswitch(c){\n\t\tcase '=':\n\t\t\tif(s === S_ATTR){//attrName\n\t\t\t\tattrName = source.slice(start,p);\n\t\t\t\ts = S_EQ;\n\t\t\t}else if(s === S_ATTR_SPACE){\n\t\t\t\ts = S_EQ;\n\t\t\t}else{\n\t\t\t\t//fatalError: equal must after attrName or space after attrName\n\t\t\t\tthrow new Error('attribute equal must after attrName'); // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase '\\'':\n\t\tcase '\"':\n\t\t\tif(s === S_EQ || s === S_ATTR //|| s == S_ATTR_SPACE\n\t\t\t\t){//equal\n\t\t\t\tif(s === S_ATTR){\n\t\t\t\t\terrorHandler.warning('attribute value must after \"=\"')\n\t\t\t\t\tattrName = source.slice(start,p)\n\t\t\t\t}\n\t\t\t\tstart = p+1;\n\t\t\t\tp = source.indexOf(c,start)\n\t\t\t\tif(p>0){\n\t\t\t\t\tvalue = source.slice(start,p).replace(/&#?\\w+;/g,entityReplacer);\n\t\t\t\t\taddAttribute(attrName, value, start-1);\n\t\t\t\t\ts = S_ATTR_END;\n\t\t\t\t}else{\n\t\t\t\t\t//fatalError: no end quot match\n\t\t\t\t\tthrow new Error('attribute value no end \\''+c+'\\' match');\n\t\t\t\t}\n\t\t\t}else if(s == S_ATTR_NOQUOT_VALUE){\n\t\t\t\tvalue = source.slice(start,p).replace(/&#?\\w+;/g,entityReplacer);\n\t\t\t\t//console.log(attrName,value,start,p)\n\t\t\t\taddAttribute(attrName, value, start);\n\t\t\t\t//console.dir(el)\n\t\t\t\terrorHandler.warning('attribute \"'+attrName+'\" missed start quot('+c+')!!');\n\t\t\t\tstart = p+1;\n\t\t\t\ts = S_ATTR_END\n\t\t\t}else{\n\t\t\t\t//fatalError: no equal before\n\t\t\t\tthrow new Error('attribute value must after \"=\"'); // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase '/':\n\t\t\tswitch(s){\n\t\t\tcase S_TAG:\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\tcase S_ATTR_END:\n\t\t\tcase S_TAG_SPACE:\n\t\t\tcase S_TAG_CLOSE:\n\t\t\t\ts =S_TAG_CLOSE;\n\t\t\t\tel.closed = true;\n\t\t\tcase S_ATTR_NOQUOT_VALUE:\n\t\t\tcase S_ATTR:\n\t\t\tcase S_ATTR_SPACE:\n\t\t\t\tbreak;\n\t\t\t//case S_EQ:\n\t\t\tdefault:\n\t\t\t\tthrow new Error(\"attribute invalid close char('/')\") // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase ''://end document\n\t\t\terrorHandler.error('unexpected end of input');\n\t\t\tif(s == S_TAG){\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\t}\n\t\t\treturn p;\n\t\tcase '>':\n\t\t\tswitch(s){\n\t\t\tcase S_TAG:\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\tcase S_ATTR_END:\n\t\t\tcase S_TAG_SPACE:\n\t\t\tcase S_TAG_CLOSE:\n\t\t\t\tbreak;//normal\n\t\t\tcase S_ATTR_NOQUOT_VALUE://Compatible state\n\t\t\tcase S_ATTR:\n\t\t\t\tvalue = source.slice(start,p);\n\t\t\t\tif(value.slice(-1) === '/'){\n\t\t\t\t\tel.closed  = true;\n\t\t\t\t\tvalue = value.slice(0,-1)\n\t\t\t\t}\n\t\t\tcase S_ATTR_SPACE:\n\t\t\t\tif(s === S_ATTR_SPACE){\n\t\t\t\t\tvalue = attrName;\n\t\t\t\t}\n\t\t\t\tif(s == S_ATTR_NOQUOT_VALUE){\n\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed quot(\")!');\n\t\t\t\t\taddAttribute(attrName, value.replace(/&#?\\w+;/g,entityReplacer), start)\n\t\t\t\t}else{\n\t\t\t\t\tif(currentNSMap[''] !== 'http://www.w3.org/1999/xhtml' || !value.match(/^(?:disabled|checked|selected)$/i)){\n\t\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed value!! \"'+value+'\" instead!!')\n\t\t\t\t\t}\n\t\t\t\t\taddAttribute(value, value, start)\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase S_EQ:\n\t\t\t\tthrow new Error('attribute value missed!!');\n\t\t\t}\n//\t\t\tconsole.log(tagName,tagNamePattern,tagNamePattern.test(tagName))\n\t\t\treturn p;\n\t\t/*xml space '\\x20' | #x9 | #xD | #xA; */\n\t\tcase '\\u0080':\n\t\t\tc = ' ';\n\t\tdefault:\n\t\t\tif(c<= ' '){//space\n\t\t\t\tswitch(s){\n\t\t\t\tcase S_TAG:\n\t\t\t\t\tel.setTagName(source.slice(start,p));//tagName\n\t\t\t\t\ts = S_TAG_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR:\n\t\t\t\t\tattrName = source.slice(start,p)\n\t\t\t\t\ts = S_ATTR_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_NOQUOT_VALUE:\n\t\t\t\t\tvar value = source.slice(start,p).replace(/&#?\\w+;/g,entityReplacer);\n\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed quot(\")!!');\n\t\t\t\t\taddAttribute(attrName, value, start)\n\t\t\t\tcase S_ATTR_END:\n\t\t\t\t\ts = S_TAG_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\t//case S_TAG_SPACE:\n\t\t\t\t//case S_EQ:\n\t\t\t\t//case S_ATTR_SPACE:\n\t\t\t\t//\tvoid();break;\n\t\t\t\t//case S_TAG_CLOSE:\n\t\t\t\t\t//ignore warning\n\t\t\t\t}\n\t\t\t}else{//not space\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\n\t\t\t\tswitch(s){\n\t\t\t\t//case S_TAG:void();break;\n\t\t\t\t//case S_ATTR:void();break;\n\t\t\t\t//case S_ATTR_NOQUOT_VALUE:void();break;\n\t\t\t\tcase S_ATTR_SPACE:\n\t\t\t\t\tvar tagName =  el.tagName;\n\t\t\t\t\tif(currentNSMap[''] !== 'http://www.w3.org/1999/xhtml' || !attrName.match(/^(?:disabled|checked|selected)$/i)){\n\t\t\t\t\t\terrorHandler.warning('attribute \"'+attrName+'\" missed value!! \"'+attrName+'\" instead2!!')\n\t\t\t\t\t}\n\t\t\t\t\taddAttribute(attrName, attrName, start);\n\t\t\t\t\tstart = p;\n\t\t\t\t\ts = S_ATTR;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_END:\n\t\t\t\t\terrorHandler.warning('attribute space is required\"'+attrName+'\"!!')\n\t\t\t\tcase S_TAG_SPACE:\n\t\t\t\t\ts = S_ATTR;\n\t\t\t\t\tstart = p;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_EQ:\n\t\t\t\t\ts = S_ATTR_NOQUOT_VALUE;\n\t\t\t\t\tstart = p;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_TAG_CLOSE:\n\t\t\t\t\tthrow new Error(\"elements closed character '/' and '>' must be connected to\");\n\t\t\t\t}\n\t\t\t}\n\t\t}//end outer switch\n\t\t//console.log('p++',p)\n\t\tp++;\n\t}\n}\n/**\n * @return true if has new namespace define\n */\nfunction appendElement(el,domBuilder,currentNSMap){\n\tvar tagName = el.tagName;\n\tvar localNSMap = null;\n\t//var currentNSMap = parseStack[parseStack.length-1].currentNSMap;\n\tvar i = el.length;\n\twhile(i--){\n\t\tvar a = el[i];\n\t\tvar qName = a.qName;\n\t\tvar value = a.value;\n\t\tvar nsp = qName.indexOf(':');\n\t\tif(nsp>0){\n\t\t\tvar prefix = a.prefix = qName.slice(0,nsp);\n\t\t\tvar localName = qName.slice(nsp+1);\n\t\t\tvar nsPrefix = prefix === 'xmlns' && localName\n\t\t}else{\n\t\t\tlocalName = qName;\n\t\t\tprefix = null\n\t\t\tnsPrefix = qName === 'xmlns' && ''\n\t\t}\n\t\t//can not set prefix,because prefix !== ''\n\t\ta.localName = localName ;\n\t\t//prefix == null for no ns prefix attribute \n\t\tif(nsPrefix !== false){//hack!!\n\t\t\tif(localNSMap == null){\n\t\t\t\tlocalNSMap = {}\n\t\t\t\t//console.log(currentNSMap,0)\n\t\t\t\t_copy(currentNSMap,currentNSMap={})\n\t\t\t\t//console.log(currentNSMap,1)\n\t\t\t}\n\t\t\tcurrentNSMap[nsPrefix] = localNSMap[nsPrefix] = value;\n\t\t\ta.uri = 'http://www.w3.org/2000/xmlns/'\n\t\t\tdomBuilder.startPrefixMapping(nsPrefix, value) \n\t\t}\n\t}\n\tvar i = el.length;\n\twhile(i--){\n\t\ta = el[i];\n\t\tvar prefix = a.prefix;\n\t\tif(prefix){//no prefix attribute has no namespace\n\t\t\tif(prefix === 'xml'){\n\t\t\t\ta.uri = 'http://www.w3.org/XML/1998/namespace';\n\t\t\t}if(prefix !== 'xmlns'){\n\t\t\t\ta.uri = currentNSMap[prefix || '']\n\t\t\t\t\n\t\t\t\t//{console.log('###'+a.qName,domBuilder.locator.systemId+'',currentNSMap,a.uri)}\n\t\t\t}\n\t\t}\n\t}\n\tvar nsp = tagName.indexOf(':');\n\tif(nsp>0){\n\t\tprefix = el.prefix = tagName.slice(0,nsp);\n\t\tlocalName = el.localName = tagName.slice(nsp+1);\n\t}else{\n\t\tprefix = null;//important!!\n\t\tlocalName = el.localName = tagName;\n\t}\n\t//no prefix element has default namespace\n\tvar ns = el.uri = currentNSMap[prefix || ''];\n\tdomBuilder.startElement(ns,localName,tagName,el);\n\t//endPrefixMapping and startPrefixMapping have not any help for dom builder\n\t//localNSMap = null\n\tif(el.closed){\n\t\tdomBuilder.endElement(ns,localName,tagName);\n\t\tif(localNSMap){\n\t\t\tfor(prefix in localNSMap){\n\t\t\t\tdomBuilder.endPrefixMapping(prefix) \n\t\t\t}\n\t\t}\n\t}else{\n\t\tel.currentNSMap = currentNSMap;\n\t\tel.localNSMap = localNSMap;\n\t\t//parseStack.push(el);\n\t\treturn true;\n\t}\n}\nfunction parseHtmlSpecialContent(source,elStartEnd,tagName,entityReplacer,domBuilder){\n\tif(/^(?:script|textarea)$/i.test(tagName)){\n\t\tvar elEndStart =  source.indexOf('</'+tagName+'>',elStartEnd);\n\t\tvar text = source.substring(elStartEnd+1,elEndStart);\n\t\tif(/[&<]/.test(text)){\n\t\t\tif(/^script$/i.test(tagName)){\n\t\t\t\t//if(!/\\]\\]>/.test(text)){\n\t\t\t\t\t//lexHandler.startCDATA();\n\t\t\t\t\tdomBuilder.characters(text,0,text.length);\n\t\t\t\t\t//lexHandler.endCDATA();\n\t\t\t\t\treturn elEndStart;\n\t\t\t\t//}\n\t\t\t}//}else{//text area\n\t\t\t\ttext = text.replace(/&#?\\w+;/g,entityReplacer);\n\t\t\t\tdomBuilder.characters(text,0,text.length);\n\t\t\t\treturn elEndStart;\n\t\t\t//}\n\t\t\t\n\t\t}\n\t}\n\treturn elStartEnd+1;\n}\nfunction fixSelfClosed(source,elStartEnd,tagName,closeMap){\n\t//if(tagName in closeMap){\n\tvar pos = closeMap[tagName];\n\tif(pos == null){\n\t\t//console.log(tagName)\n\t\tpos =  source.lastIndexOf('</'+tagName+'>')\n\t\tif(pos<elStartEnd){//忘记闭合\n\t\t\tpos = source.lastIndexOf('</'+tagName)\n\t\t}\n\t\tcloseMap[tagName] =pos\n\t}\n\treturn pos<elStartEnd;\n\t//} \n}\nfunction _copy(source,target){\n\tfor(var n in source){target[n] = source[n]}\n}\nfunction parseDCC(source,start,domBuilder,errorHandler){//sure start with '<!'\n\tvar next= source.charAt(start+2)\n\tswitch(next){\n\tcase '-':\n\t\tif(source.charAt(start + 3) === '-'){\n\t\t\tvar end = source.indexOf('-->',start+4);\n\t\t\t//append comment source.substring(4,end)//<!--\n\t\t\tif(end>start){\n\t\t\t\tdomBuilder.comment(source,start+4,end-start-4);\n\t\t\t\treturn end+3;\n\t\t\t}else{\n\t\t\t\terrorHandler.error(\"Unclosed comment\");\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t}else{\n\t\t\t//error\n\t\t\treturn -1;\n\t\t}\n\tdefault:\n\t\tif(source.substr(start+3,6) == 'CDATA['){\n\t\t\tvar end = source.indexOf(']]>',start+9);\n\t\t\tdomBuilder.startCDATA();\n\t\t\tdomBuilder.characters(source,start+9,end-start-9);\n\t\t\tdomBuilder.endCDATA() \n\t\t\treturn end+3;\n\t\t}\n\t\t//<!DOCTYPE\n\t\t//startDTD(java.lang.String name, java.lang.String publicId, java.lang.String systemId) \n\t\tvar matchs = split(source,start);\n\t\tvar len = matchs.length;\n\t\tif(len>1 && /!doctype/i.test(matchs[0][0])){\n\t\t\tvar name = matchs[1][0];\n\t\t\tvar pubid = false;\n\t\t\tvar sysid = false;\n\t\t\tif(len>3){\n\t\t\t\tif(/^public$/i.test(matchs[2][0])){\n\t\t\t\t\tpubid = matchs[3][0];\n\t\t\t\t\tsysid = len>4 && matchs[4][0];\n\t\t\t\t}else if(/^system$/i.test(matchs[2][0])){\n\t\t\t\t\tsysid = matchs[3][0];\n\t\t\t\t}\n\t\t\t}\n\t\t\tvar lastMatch = matchs[len-1]\n\t\t\tdomBuilder.startDTD(name, pubid, sysid);\n\t\t\tdomBuilder.endDTD();\n\t\t\t\n\t\t\treturn lastMatch.index+lastMatch[0].length\n\t\t}\n\t}\n\treturn -1;\n}\n\n\n\nfunction parseInstruction(source,start,domBuilder){\n\tvar end = source.indexOf('?>',start);\n\tif(end){\n\t\tvar match = source.substring(start,end).match(/^<\\?(\\S*)\\s*([\\s\\S]*?)\\s*$/);\n\t\tif(match){\n\t\t\tvar len = match[0].length;\n\t\t\tdomBuilder.processingInstruction(match[1], match[2]) ;\n\t\t\treturn end+2;\n\t\t}else{//error\n\t\t\treturn -1;\n\t\t}\n\t}\n\treturn -1;\n}\n\nfunction ElementAttributes(){\n\tthis.attributeNames = {}\n}\nElementAttributes.prototype = {\n\tsetTagName:function(tagName){\n\t\tif(!tagNamePattern.test(tagName)){\n\t\t\tthrow new Error('invalid tagName:'+tagName)\n\t\t}\n\t\tthis.tagName = tagName\n\t},\n\taddValue:function(qName, value, offset) {\n\t\tif(!tagNamePattern.test(qName)){\n\t\t\tthrow new Error('invalid attribute:'+qName)\n\t\t}\n\t\tthis.attributeNames[qName] = this.length;\n\t\tthis[this.length++] = {qName:qName,value:value,offset:offset}\n\t},\n\tlength:0,\n\tgetLocalName:function(i){return this[i].localName},\n\tgetLocator:function(i){return this[i].locator},\n\tgetQName:function(i){return this[i].qName},\n\tgetURI:function(i){return this[i].uri},\n\tgetValue:function(i){return this[i].value}\n//\t,getIndex:function(uri, localName)){\n//\t\tif(localName){\n//\t\t\t\n//\t\t}else{\n//\t\t\tvar qName = uri\n//\t\t}\n//\t},\n//\tgetValue:function(){return this.getValue(this.getIndex.apply(this,arguments))},\n//\tgetType:function(uri,localName){}\n//\tgetType:function(i){},\n}\n\n\n\nfunction split(source,start){\n\tvar match;\n\tvar buf = [];\n\tvar reg = /'[^']+'|\"[^\"]+\"|[^\\s<>\\/=]+=?|(\\/?\\s*>|<)/g;\n\treg.lastIndex = start;\n\treg.exec(source);//skip <\n\twhile(match = reg.exec(source)){\n\t\tbuf.push(match);\n\t\tif(match[1])return buf;\n\t}\n}\n\nexports.XMLReader = XMLReader;\nexports.ParseError = ParseError;\n", "(function(f){if(typeof exports===\"object\"&&typeof module!==\"undefined\"){module.exports=f()}else if(typeof define===\"function\"&&define.amd){define([],f)}else{var g;if(typeof window!==\"undefined\"){g=window}else if(typeof global!==\"undefined\"){g=global}else if(typeof self!==\"undefined\"){g=self}else{g=this}g.tokml = f()}})(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require==\"function\"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error(\"Cannot find module '\"+o+\"'\");throw f.code=\"MODULE_NOT_FOUND\",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require==\"function\"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){\nvar strxml = require('strxml'),\n    tag = strxml.tag,\n    encode = strxml.encode;\n\nmodule.exports = function tokml(geojson, options) {\n\n    options = options || {\n        documentName: undefined,\n        documentDescription: undefined,\n        name: 'name',\n        description: 'description',\n        simplestyle: false,\n        timestamp: 'timestamp'\n    };\n\n    return '<?xml version=\"1.0\" encoding=\"UTF-8\"?>' +\n        tag('kml',\n            tag('Document',\n                documentName(options) +\n                documentDescription(options) +\n                root(geojson, options)\n               ), [['xmlns', 'http://www.opengis.net/kml/2.2']]);\n};\n\nfunction feature(options, styleHashesArray) {\n    return function(_) {\n        if (!_.properties || !geometry.valid(_.geometry)) return '';\n        var geometryString = geometry.any(_.geometry);\n        if (!geometryString) return '';\n        \n        var styleDefinition = '',\n            styleReference = '';\n        if (options.simplestyle) {\n            var styleHash = hashStyle(_.properties);\n            if (styleHash) {\n                if (geometry.isPoint(_.geometry) && hasMarkerStyle(_.properties)) {\n                    if (styleHashesArray.indexOf(styleHash) === -1) {\n                        styleDefinition = markerStyle(_.properties, styleHash);\n                        styleHashesArray.push(styleHash);\n                    }\n                    styleReference = tag('styleUrl', '#' + styleHash);\n                } else if ((geometry.isPolygon(_.geometry) || geometry.isLine(_.geometry)) && \n                    hasPolygonAndLineStyle(_.properties)) {\n                    if (styleHashesArray.indexOf(styleHash) === -1) {\n                        styleDefinition = polygonAndLineStyle(_.properties, styleHash);\n                        styleHashesArray.push(styleHash);\n                    }\n                    styleReference = tag('styleUrl', '#' + styleHash);\n                }\n                // Note that style of GeometryCollection / MultiGeometry is not supported\n            }\n        }\n        \n        return styleDefinition + tag('Placemark',\n            name(_.properties, options) +\n            description(_.properties, options) +\n            extendeddata(_.properties) +\n            timestamp(_.properties, options) +\n            geometryString +\n            styleReference);\n    };\n}\n\nfunction root(_, options) {\n    if (!_.type) return '';\n    var styleHashesArray = [];\n            \n    switch (_.type) {\n        case 'FeatureCollection':\n            if (!_.features) return '';\n            return _.features.map(feature(options, styleHashesArray)).join('');\n        case 'Feature':\n            return feature(options, styleHashesArray)(_);\n        default:\n            return feature(options, styleHashesArray)({\n                type: 'Feature',\n                geometry: _,\n                properties: {}\n            });\n    }\n}\n\nfunction documentName(options) {\n    return (options.documentName !== undefined) ? tag('name', options.documentName) : '';\n}\n\nfunction documentDescription(options) {\n    return (options.documentDescription !== undefined) ? tag('description', options.documentDescription) : '';\n}\n\nfunction name(_, options) {\n    return _[options.name] ? tag('name', encode(_[options.name])) : '';\n}\n\nfunction description(_, options) {\n    return _[options.description] ? tag('description', encode(_[options.description])) : '';\n}\n\nfunction timestamp(_, options) {\n    return _[options.timestamp] ? tag('TimeStamp', tag('when', encode(_[options.timestamp]))) : '';\n}\n\n// ## Geometry Types\n//\n// https://developers.google.com/kml/documentation/kmlreference#geometry\nvar geometry = {\n    Point: function(_) {\n        return tag('Point', tag('coordinates', _.coordinates.join(',')));\n    },\n    LineString: function(_) {\n        return tag('LineString', tag('coordinates', linearring(_.coordinates)));\n    },\n    Polygon: function(_) {\n        if (!_.coordinates.length) return '';\n        var outer = _.coordinates[0],\n            inner = _.coordinates.slice(1),\n            outerRing = tag('outerBoundaryIs',\n                tag('LinearRing', tag('coordinates', linearring(outer)))),\n            innerRings = inner.map(function(i) {\n                return tag('innerBoundaryIs',\n                    tag('LinearRing', tag('coordinates', linearring(i))));\n            }).join('');\n        return tag('Polygon', outerRing + innerRings);\n    },\n    MultiPoint: function(_) {\n        if (!_.coordinates.length) return '';\n        return tag('MultiGeometry', _.coordinates.map(function(c) {\n            return geometry.Point({ coordinates: c });\n        }).join(''));\n    },\n    MultiPolygon: function(_) {\n        if (!_.coordinates.length) return '';\n        return tag('MultiGeometry', _.coordinates.map(function(c) {\n            return geometry.Polygon({ coordinates: c });\n        }).join(''));\n    },\n    MultiLineString: function(_) {\n        if (!_.coordinates.length) return '';\n        return tag('MultiGeometry', _.coordinates.map(function(c) {\n            return geometry.LineString({ coordinates: c });\n        }).join(''));\n    },\n    GeometryCollection: function(_) {\n        return tag('MultiGeometry',\n            _.geometries.map(geometry.any).join(''));\n    },\n    valid: function(_) {\n        return _ && _.type && (_.coordinates ||\n            _.type === 'GeometryCollection' && _.geometries && _.geometries.every(geometry.valid));\n    },\n    any: function(_) {\n        if (geometry[_.type]) {\n            return geometry[_.type](_);\n        } else {\n            return '';\n        }\n    },\n    isPoint: function(_) {\n        return _.type === 'Point' ||\n        _.type === 'MultiPoint';\n    },\n    isPolygon: function(_) {\n        return _.type === 'Polygon' ||\n        _.type === 'MultiPolygon';\n    },\n    isLine: function(_) {\n        return _.type === 'LineString' ||\n        _.type === 'MultiLineString';\n    }\n};\n\nfunction linearring(_) {\n    return _.map(function(cds) { return cds.join(','); }).join(' ');\n}\n\n// ## Data\nfunction extendeddata(_) {\n    return tag('ExtendedData', pairs(_).map(data).join(''));\n}\n\nfunction data(_) {\n    return tag('Data', tag('value', encode(_[1])), [['name', encode(_[0])]]);\n}\n\n// ## Marker style\nfunction hasMarkerStyle(_) {\n    return !!(_['marker-size'] || _['marker-symbol'] || _['marker-color']);\n}\n\nfunction markerStyle(_, styleHash) {\n    return tag('Style',\n        tag('IconStyle',\n            tag('Icon',\n                tag('href', iconUrl(_)))) +\n        iconSize(_), [['id', styleHash]]);\n}\n\nfunction iconUrl(_) {\n    var size = _['marker-size'] || 'medium',\n        symbol = _['marker-symbol'] ? '-' + _['marker-symbol'] : '',\n        color = (_['marker-color'] || '7e7e7e').replace('#', '');\n\n    return 'https://api.tiles.mapbox.com/v3/marker/' + 'pin-' + size.charAt(0) +\n        symbol + '+' + color + '.png';\n}\n\nfunction iconSize(_) {\n    return tag('hotSpot', '', [\n        ['xunits', 'fraction'],\n        ['yunits', 'fraction'],\n        ['x', 0.5],\n        ['y', 0.5]\n    ]);\n}\n\n// ## Polygon and Line style\nfunction hasPolygonAndLineStyle(_) {\n    for (var key in _) {\n        if ({\n            \"stroke\": true,\n            \"stroke-opacity\": true,\n            \"stroke-width\": true,\n            \"fill\": true,\n            \"fill-opacity\": true\n        }[key]) return true;\n    }\n}\n\nfunction polygonAndLineStyle(_, styleHash) {\n    var lineStyle = tag('LineStyle', [\n        tag('color', hexToKmlColor(_['stroke'], _['stroke-opacity']) || 'ff555555') +\n        tag('width', _['stroke-width'] === undefined ? 2 : _['stroke-width'])\n    ]);\n    \n    var polyStyle = '';\n    \n    if (_['fill'] || _['fill-opacity']) {\n        polyStyle = tag('PolyStyle', [\n            tag('color', hexToKmlColor(_['fill'], _['fill-opacity']) || '88555555')\n        ]);\n    }\n    \n    return tag('Style', lineStyle + polyStyle, [['id', styleHash]]);\n}\n\n// ## Style helpers\nfunction hashStyle(_) {\n    var hash = '';\n    \n    if (_['marker-symbol']) hash = hash + 'ms' + _['marker-symbol'];\n    if (_['marker-color']) hash = hash + 'mc' + _['marker-color'].replace('#', '');\n    if (_['marker-size']) hash = hash + 'ms' + _['marker-size'];\n    if (_['stroke']) hash = hash + 's' + _['stroke'].replace('#', '');\n    if (_['stroke-width']) hash = hash + 'sw' + _['stroke-width'].toString().replace('.', '');\n    if (_['stroke-opacity']) hash = hash + 'mo' + _['stroke-opacity'].toString().replace('.', '');\n    if (_['fill']) hash = hash + 'f' + _['fill'].replace('#', '');\n    if (_['fill-opacity']) hash = hash + 'fo' + _['fill-opacity'].toString().replace('.', '');\n    \n    return hash;\n}\n\nfunction hexToKmlColor(hexColor, opacity) {\n    if (typeof hexColor !== 'string') return '';\n    \n    hexColor = hexColor.replace('#', '').toLowerCase();\n    \n    if (hexColor.length === 3) {\n        hexColor = hexColor[0] + hexColor[0] + \n        hexColor[1] + hexColor[1] + \n        hexColor[2] + hexColor[2];\n    } else if (hexColor.length !== 6) {\n        return '';\n    }\n    \n    var r = hexColor[0] + hexColor[1];\n    var g = hexColor[2] + hexColor[3];\n    var b = hexColor[4] + hexColor[5];\n    \n    var o = 'ff';\n    if (typeof opacity === 'number' && opacity >= 0.0 && opacity <= 1.0) {\n        o = (opacity * 255).toString(16);\n        if (o.indexOf('.') > -1) o = o.substr(0, o.indexOf('.'));\n        if (o.length < 2) o = '0' + o;\n    }\n    \n    return o + b + g + r;\n}\n\n// ## General helpers\nfunction pairs(_) {\n    var o = [];\n    for (var i in _) o.push([i, _[i]]);\n    return o;\n}\n},{\"strxml\":2}],2:[function(require,module,exports){\nmodule.exports.attr = attr;\nmodule.exports.tagClose = tagClose;\nmodule.exports.tag = tag;\nmodule.exports.encode = encode;\n\n/**\n * @param {array} _ an array of attributes\n * @returns {string}\n */\nfunction attr(_) {\n    return (_ && _.length) ? (' ' + _.map(function(a) {\n        return a[0] + '=\"' + a[1] + '\"';\n    }).join(' ')) : '';\n}\n\n/**\n * @param {string} el element name\n * @param {array} attributes array of pairs\n * @returns {string}\n */\nfunction tagClose(el, attributes) {\n    return '<' + el + attr(attributes) + '/>';\n}\n\n/**\n * @param {string} el element name\n * @param {string} contents innerXML\n * @param {array} attributes array of pairs\n * @returns {string}\n */\nfunction tag(el, contents, attributes) {\n    return '<' + el + attr(attributes) + '>' + contents + '</' + el + '>';\n}\n\n/**\n * @param {string} _ a string of attribute\n * @returns {string}\n */\nfunction encode(_) {\n    return (_ === null ? '' : _.toString()).replace(/&/g, '&amp;')\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;')\n        .replace(/\"/g, '&quot;');\n}\n\n},{}]},{},[1])(1)\n});", "/* globals __webpack_amd_options__ */\nmodule.exports = __webpack_amd_options__;\n", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = function() { return []; };\nwebpackEmptyContext.resolve = webpackEmptyContext;\nmodule.exports = webpackEmptyContext;\nwebpackEmptyContext.id = 12;", "/*!\n\nJSZip v3.7.0 - A JavaScript class for generating and reading zip files\n<http://stuartk.com/jszip>\n\n(c) 2009-2016 <PERSON> <stuart [at] stuartk.com>\nDual licenced under the MIT license or GPLv3. See https://raw.github.com/Stuk/jszip/master/LICENSE.markdown.\n\nJSZip uses the library pako released under the MIT license :\nhttps://github.com/nodeca/pako/blob/master/LICENSE\n*/\n\n!function(e){if(\"object\"==typeof exports&&\"undefined\"!=typeof module)module.exports=e();else if(\"function\"==typeof define&&define.amd)define([],e);else{(\"undefined\"!=typeof window?window:\"undefined\"!=typeof global?global:\"undefined\"!=typeof self?self:this).JSZip=e()}}(function(){return function s(o,a,f){function u(r,e){if(!a[r]){if(!o[r]){var t=\"function\"==typeof require&&require;if(!e&&t)return t(r,!0);if(d)return d(r,!0);var n=new Error(\"Cannot find module '\"+r+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[r]={exports:{}};o[r][0].call(i.exports,function(e){var t=o[r][1][e];return u(t||e)},i,i.exports,s,o,a,f)}return a[r].exports}for(var d=\"function\"==typeof require&&require,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(h,t,n){(function(r){!function(e){\"object\"==typeof n&&void 0!==t?t.exports=e():(\"undefined\"!=typeof window?window:void 0!==r?r:\"undefined\"!=typeof self?self:this).JSZip=e()}(function(){return function s(o,a,f){function u(t,e){if(!a[t]){if(!o[t]){var r=\"function\"==typeof h&&h;if(!e&&r)return r(t,!0);if(d)return d(t,!0);var n=new Error(\"Cannot find module '\"+t+\"'\");throw n.code=\"MODULE_NOT_FOUND\",n}var i=a[t]={exports:{}};o[t][0].call(i.exports,function(e){return u(o[t][1][e]||e)},i,i.exports,s,o,a,f)}return a[t].exports}for(var d=\"function\"==typeof h&&h,e=0;e<f.length;e++)u(f[e]);return u}({1:[function(e,t,r){\"use strict\";var c=e(\"./utils\"),h=e(\"./support\"),p=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";r.encode=function(e){for(var t,r,n,i,s,o,a,f=[],u=0,d=e.length,h=d,l=\"string\"!==c.getTypeOf(e);u<e.length;)h=d-u,n=l?(t=e[u++],r=u<d?e[u++]:0,u<d?e[u++]:0):(t=e.charCodeAt(u++),r=u<d?e.charCodeAt(u++):0,u<d?e.charCodeAt(u++):0),i=t>>2,s=(3&t)<<4|r>>4,o=1<h?(15&r)<<2|n>>6:64,a=2<h?63&n:64,f.push(p.charAt(i)+p.charAt(s)+p.charAt(o)+p.charAt(a));return f.join(\"\")},r.decode=function(e){var t,r,n,i,s,o,a=0,f=0;if(\"data:\"===e.substr(0,\"data:\".length))throw new Error(\"Invalid base64 input, it looks like a data url.\");var u,d=3*(e=e.replace(/[^A-Za-z0-9\\+\\/\\=]/g,\"\")).length/4;if(e.charAt(e.length-1)===p.charAt(64)&&d--,e.charAt(e.length-2)===p.charAt(64)&&d--,d%1!=0)throw new Error(\"Invalid base64 input, bad content length.\");for(u=h.uint8array?new Uint8Array(0|d):new Array(0|d);a<e.length;)t=p.indexOf(e.charAt(a++))<<2|(i=p.indexOf(e.charAt(a++)))>>4,r=(15&i)<<4|(s=p.indexOf(e.charAt(a++)))>>2,n=(3&s)<<6|(o=p.indexOf(e.charAt(a++))),u[f++]=t,64!==s&&(u[f++]=r),64!==o&&(u[f++]=n);return u}},{\"./support\":30,\"./utils\":32}],2:[function(e,t,r){\"use strict\";var n=e(\"./external\"),i=e(\"./stream/DataWorker\"),s=e(\"./stream/Crc32Probe\"),o=e(\"./stream/DataLengthProbe\");function a(e,t,r,n,i){this.compressedSize=e,this.uncompressedSize=t,this.crc32=r,this.compression=n,this.compressedContent=i}a.prototype={getContentWorker:function(){var e=new i(n.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new o(\"data_length\")),t=this;return e.on(\"end\",function(){if(this.streamInfo.data_length!==t.uncompressedSize)throw new Error(\"Bug : uncompressed data size mismatch\")}),e},getCompressedWorker:function(){return new i(n.Promise.resolve(this.compressedContent)).withStreamInfo(\"compressedSize\",this.compressedSize).withStreamInfo(\"uncompressedSize\",this.uncompressedSize).withStreamInfo(\"crc32\",this.crc32).withStreamInfo(\"compression\",this.compression)}},a.createWorkerFrom=function(e,t,r){return e.pipe(new s).pipe(new o(\"uncompressedSize\")).pipe(t.compressWorker(r)).pipe(new o(\"compressedSize\")).withStreamInfo(\"compression\",t)},t.exports=a},{\"./external\":6,\"./stream/Crc32Probe\":25,\"./stream/DataLengthProbe\":26,\"./stream/DataWorker\":27}],3:[function(e,t,r){\"use strict\";var n=e(\"./stream/GenericWorker\");r.STORE={magic:\"\\0\\0\",compressWorker:function(e){return new n(\"STORE compression\")},uncompressWorker:function(){return new n(\"STORE decompression\")}},r.DEFLATE=e(\"./flate\")},{\"./flate\":7,\"./stream/GenericWorker\":28}],4:[function(e,t,r){\"use strict\";var n=e(\"./utils\"),o=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();t.exports=function(e,t){return void 0!==e&&e.length?\"string\"!==n.getTypeOf(e)?function(e,t,r){var n=o,i=0+r;e^=-1;for(var s=0;s<i;s++)e=e>>>8^n[255&(e^t[s])];return-1^e}(0|t,e,e.length):function(e,t,r){var n=o,i=0+r;e^=-1;for(var s=0;s<i;s++)e=e>>>8^n[255&(e^t.charCodeAt(s))];return-1^e}(0|t,e,e.length):0}},{\"./utils\":32}],5:[function(e,t,r){\"use strict\";r.base64=!1,r.binary=!1,r.dir=!1,r.createFolders=!0,r.date=null,r.compression=null,r.compressionOptions=null,r.comment=null,r.unixPermissions=null,r.dosPermissions=null},{}],6:[function(e,t,r){\"use strict\";var n;n=\"undefined\"!=typeof Promise?Promise:e(\"lie\"),t.exports={Promise:n}},{lie:37}],7:[function(e,t,r){\"use strict\";var n=\"undefined\"!=typeof Uint8Array&&\"undefined\"!=typeof Uint16Array&&\"undefined\"!=typeof Uint32Array,i=e(\"pako\"),s=e(\"./utils\"),o=e(\"./stream/GenericWorker\"),a=n?\"uint8array\":\"array\";function f(e,t){o.call(this,\"FlateWorker/\"+e),this._pako=null,this._pakoAction=e,this._pakoOptions=t,this.meta={}}r.magic=\"\\b\\0\",s.inherits(f,o),f.prototype.processChunk=function(e){this.meta=e.meta,null===this._pako&&this._createPako(),this._pako.push(s.transformTo(a,e.data),!1)},f.prototype.flush=function(){o.prototype.flush.call(this),null===this._pako&&this._createPako(),this._pako.push([],!0)},f.prototype.cleanUp=function(){o.prototype.cleanUp.call(this),this._pako=null},f.prototype._createPako=function(){this._pako=new i[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var t=this;this._pako.onData=function(e){t.push({data:e,meta:t.meta})}},r.compressWorker=function(e){return new f(\"Deflate\",e)},r.uncompressWorker=function(){return new f(\"Inflate\",{})}},{\"./stream/GenericWorker\":28,\"./utils\":32,pako:38}],8:[function(e,t,r){\"use strict\";function O(e,t){var r,n=\"\";for(r=0;r<t;r++)n+=String.fromCharCode(255&e),e>>>=8;return n}function i(e,t,r,n,i,s){var o,a,f=e.file,u=e.compression,d=s!==D.utf8encode,h=I.transformTo(\"string\",s(f.name)),l=I.transformTo(\"string\",D.utf8encode(f.name)),c=f.comment,p=I.transformTo(\"string\",s(c)),m=I.transformTo(\"string\",D.utf8encode(c)),_=l.length!==f.name.length,w=m.length!==c.length,v=\"\",g=\"\",y=\"\",b=f.dir,k=f.date,x={crc32:0,compressedSize:0,uncompressedSize:0};t&&!r||(x.crc32=e.crc32,x.compressedSize=e.compressedSize,x.uncompressedSize=e.uncompressedSize);var S=0;t&&(S|=8),d||!_&&!w||(S|=2048);var E,z=0,C=0;b&&(z|=16),\"UNIX\"===i?(C=798,z|=((E=f.unixPermissions)||(E=b?16893:33204),(65535&E)<<16)):(C=20,z|=63&(f.dosPermissions||0)),o=k.getUTCHours(),o<<=6,o|=k.getUTCMinutes(),o<<=5,o|=k.getUTCSeconds()/2,a=k.getUTCFullYear()-1980,a<<=4,a|=k.getUTCMonth()+1,a<<=5,a|=k.getUTCDate(),_&&(v+=\"up\"+O((g=O(1,1)+O(B(h),4)+l).length,2)+g),w&&(v+=\"uc\"+O((y=O(1,1)+O(B(p),4)+m).length,2)+y);var A=\"\";return A+=\"\\n\\0\",A+=O(S,2),A+=u.magic,A+=O(o,2),A+=O(a,2),A+=O(x.crc32,4),A+=O(x.compressedSize,4),A+=O(x.uncompressedSize,4),A+=O(h.length,2),A+=O(v.length,2),{fileRecord:T.LOCAL_FILE_HEADER+A+h+v,dirRecord:T.CENTRAL_FILE_HEADER+O(C,2)+A+O(p.length,2)+\"\\0\\0\\0\\0\"+O(z,4)+O(n,4)+h+v+p}}var I=e(\"../utils\"),s=e(\"../stream/GenericWorker\"),D=e(\"../utf8\"),B=e(\"../crc32\"),T=e(\"../signature\");function n(e,t,r,n){s.call(this,\"ZipFileWorker\"),this.bytesWritten=0,this.zipComment=t,this.zipPlatform=r,this.encodeFileName=n,this.streamFiles=e,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}I.inherits(n,s),n.prototype.push=function(e){var t=e.meta.percent||0,r=this.entriesCount,n=this._sources.length;this.accumulate?this.contentBuffer.push(e):(this.bytesWritten+=e.data.length,s.prototype.push.call(this,{data:e.data,meta:{currentFile:this.currentFile,percent:r?(t+100*(r-n-1))/r:100}}))},n.prototype.openedSource=function(e){this.currentSourceOffset=this.bytesWritten,this.currentFile=e.file.name;var t=this.streamFiles&&!e.file.dir;if(t){var r=i(e,t,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:r.fileRecord,meta:{percent:0}})}else this.accumulate=!0},n.prototype.closedSource=function(e){this.accumulate=!1;var t,r=this.streamFiles&&!e.file.dir,n=i(e,r,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(n.dirRecord),r)this.push({data:(t=e,T.DATA_DESCRIPTOR+O(t.crc32,4)+O(t.compressedSize,4)+O(t.uncompressedSize,4)),meta:{percent:100}});else for(this.push({data:n.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},n.prototype.flush=function(){for(var e=this.bytesWritten,t=0;t<this.dirRecords.length;t++)this.push({data:this.dirRecords[t],meta:{percent:100}});var r,n,i,s,o,a,f=this.bytesWritten-e,u=(r=this.dirRecords.length,n=f,i=e,s=this.zipComment,o=this.encodeFileName,a=I.transformTo(\"string\",o(s)),T.CENTRAL_DIRECTORY_END+\"\\0\\0\\0\\0\"+O(r,2)+O(r,2)+O(n,4)+O(i,4)+O(a.length,2)+a);this.push({data:u,meta:{percent:100}})},n.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},n.prototype.registerPrevious=function(e){this._sources.push(e);var t=this;return e.on(\"data\",function(e){t.processChunk(e)}),e.on(\"end\",function(){t.closedSource(t.previous.streamInfo),t._sources.length?t.prepareNextSource():t.end()}),e.on(\"error\",function(e){t.error(e)}),this},n.prototype.resume=function(){return!!s.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},n.prototype.error=function(e){var t=this._sources;if(!s.prototype.error.call(this,e))return!1;for(var r=0;r<t.length;r++)try{t[r].error(e)}catch(e){}return!0},n.prototype.lock=function(){s.prototype.lock.call(this);for(var e=this._sources,t=0;t<e.length;t++)e[t].lock()},t.exports=n},{\"../crc32\":4,\"../signature\":23,\"../stream/GenericWorker\":28,\"../utf8\":31,\"../utils\":32}],9:[function(e,t,r){\"use strict\";var u=e(\"../compressions\"),n=e(\"./ZipFileWorker\");r.generateWorker=function(e,o,t){var a=new n(o.streamFiles,t,o.platform,o.encodeFileName),f=0;try{e.forEach(function(e,t){f++;var r=function(e,t){var r=e||t,n=u[r];if(!n)throw new Error(r+\" is not a valid compression method !\");return n}(t.options.compression,o.compression),n=t.options.compressionOptions||o.compressionOptions||{},i=t.dir,s=t.date;t._compressWorker(r,n).withStreamInfo(\"file\",{name:e,dir:i,date:s,comment:t.comment||\"\",unixPermissions:t.unixPermissions,dosPermissions:t.dosPermissions}).pipe(a)}),a.entriesCount=f}catch(e){a.error(e)}return a}},{\"../compressions\":3,\"./ZipFileWorker\":8}],10:[function(e,t,r){\"use strict\";function n(){if(!(this instanceof n))return new n;if(arguments.length)throw new Error(\"The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.\");this.files={},this.comment=null,this.root=\"\",this.clone=function(){var e=new n;for(var t in this)\"function\"!=typeof this[t]&&(e[t]=this[t]);return e}}(n.prototype=e(\"./object\")).loadAsync=e(\"./load\"),n.support=e(\"./support\"),n.defaults=e(\"./defaults\"),n.version=\"3.5.0\",n.loadAsync=function(e,t){return(new n).loadAsync(e,t)},n.external=e(\"./external\"),t.exports=n},{\"./defaults\":5,\"./external\":6,\"./load\":11,\"./object\":15,\"./support\":30}],11:[function(e,t,r){\"use strict\";var n=e(\"./utils\"),i=e(\"./external\"),a=e(\"./utf8\"),f=e(\"./zipEntries\"),s=e(\"./stream/Crc32Probe\"),u=e(\"./nodejsUtils\");function d(n){return new i.Promise(function(e,t){var r=n.decompressed.getContentWorker().pipe(new s);r.on(\"error\",function(e){t(e)}).on(\"end\",function(){r.streamInfo.crc32!==n.decompressed.crc32?t(new Error(\"Corrupted zip : CRC32 mismatch\")):e()}).resume()})}t.exports=function(e,s){var o=this;return s=n.extend(s||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:a.utf8decode}),u.isNode&&u.isStream(e)?i.Promise.reject(new Error(\"JSZip can't accept a stream when loading a zip file.\")):n.prepareContent(\"the loaded zip file\",e,!0,s.optimizedBinaryString,s.base64).then(function(e){var t=new f(s);return t.load(e),t}).then(function(e){var t=[i.Promise.resolve(e)],r=e.files;if(s.checkCRC32)for(var n=0;n<r.length;n++)t.push(d(r[n]));return i.Promise.all(t)}).then(function(e){for(var t=e.shift(),r=t.files,n=0;n<r.length;n++){var i=r[n];o.file(i.fileNameStr,i.decompressed,{binary:!0,optimizedBinaryString:!0,date:i.date,dir:i.dir,comment:i.fileCommentStr.length?i.fileCommentStr:null,unixPermissions:i.unixPermissions,dosPermissions:i.dosPermissions,createFolders:s.createFolders})}return t.zipComment.length&&(o.comment=t.zipComment),o})}},{\"./external\":6,\"./nodejsUtils\":14,\"./stream/Crc32Probe\":25,\"./utf8\":31,\"./utils\":32,\"./zipEntries\":33}],12:[function(e,t,r){\"use strict\";var n=e(\"../utils\"),i=e(\"../stream/GenericWorker\");function s(e,t){i.call(this,\"Nodejs stream input adapter for \"+e),this._upstreamEnded=!1,this._bindStream(t)}n.inherits(s,i),s.prototype._bindStream=function(e){var t=this;(this._stream=e).pause(),e.on(\"data\",function(e){t.push({data:e,meta:{percent:0}})}).on(\"error\",function(e){t.isPaused?this.generatedError=e:t.error(e)}).on(\"end\",function(){t.isPaused?t._upstreamEnded=!0:t.end()})},s.prototype.pause=function(){return!!i.prototype.pause.call(this)&&(this._stream.pause(),!0)},s.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},t.exports=s},{\"../stream/GenericWorker\":28,\"../utils\":32}],13:[function(e,t,r){\"use strict\";var i=e(\"readable-stream\").Readable;function n(e,t,r){i.call(this,t),this._helper=e;var n=this;e.on(\"data\",function(e,t){n.push(e)||n._helper.pause(),r&&r(t)}).on(\"error\",function(e){n.emit(\"error\",e)}).on(\"end\",function(){n.push(null)})}e(\"../utils\").inherits(n,i),n.prototype._read=function(){this._helper.resume()},t.exports=n},{\"../utils\":32,\"readable-stream\":16}],14:[function(e,t,r){\"use strict\";t.exports={isNode:\"undefined\"!=typeof Buffer,newBufferFrom:function(e,t){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(e,t);if(\"number\"==typeof e)throw new Error('The \"data\" argument must not be a number');return new Buffer(e,t)},allocBuffer:function(e){if(Buffer.alloc)return Buffer.alloc(e);var t=new Buffer(e);return t.fill(0),t},isBuffer:function(e){return Buffer.isBuffer(e)},isStream:function(e){return e&&\"function\"==typeof e.on&&\"function\"==typeof e.pause&&\"function\"==typeof e.resume}}},{}],15:[function(e,t,r){\"use strict\";function s(e,t,r){var n,i=d.getTypeOf(t),s=d.extend(r||{},l);s.date=s.date||new Date,null!==s.compression&&(s.compression=s.compression.toUpperCase()),\"string\"==typeof s.unixPermissions&&(s.unixPermissions=parseInt(s.unixPermissions,8)),s.unixPermissions&&16384&s.unixPermissions&&(s.dir=!0),s.dosPermissions&&16&s.dosPermissions&&(s.dir=!0),s.dir&&(e=u(e)),s.createFolders&&(n=function(e){\"/\"===e.slice(-1)&&(e=e.substring(0,e.length-1));var t=e.lastIndexOf(\"/\");return 0<t?e.substring(0,t):\"\"}(e))&&w.call(this,n,!0);var o,a=\"string\"===i&&!1===s.binary&&!1===s.base64;r&&void 0!==r.binary||(s.binary=!a),(t instanceof c&&0===t.uncompressedSize||s.dir||!t||0===t.length)&&(s.base64=!1,s.binary=!0,t=\"\",s.compression=\"STORE\",i=\"string\"),o=t instanceof c||t instanceof h?t:m.isNode&&m.isStream(t)?new _(e,t):d.prepareContent(e,t,s.binary,s.optimizedBinaryString,s.base64);var f=new p(e,o,s);this.files[e]=f}function u(e){return\"/\"!==e.slice(-1)&&(e+=\"/\"),e}var i=e(\"./utf8\"),d=e(\"./utils\"),h=e(\"./stream/GenericWorker\"),o=e(\"./stream/StreamHelper\"),l=e(\"./defaults\"),c=e(\"./compressedObject\"),p=e(\"./zipObject\"),a=e(\"./generate\"),m=e(\"./nodejsUtils\"),_=e(\"./nodejs/NodejsStreamInputAdapter\"),w=function(e,t){return t=void 0!==t?t:l.createFolders,e=u(e),this.files[e]||s.call(this,e,null,{dir:!0,createFolders:t}),this.files[e]};function f(e){return\"[object RegExp]\"===Object.prototype.toString.call(e)}var n={load:function(){throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\")},forEach:function(e){var t,r,n;for(t in this.files)this.files.hasOwnProperty(t)&&(n=this.files[t],(r=t.slice(this.root.length,t.length))&&t.slice(0,this.root.length)===this.root&&e(r,n))},filter:function(r){var n=[];return this.forEach(function(e,t){r(e,t)&&n.push(t)}),n},file:function(e,t,r){if(1!==arguments.length)return e=this.root+e,s.call(this,e,t,r),this;if(f(e)){var n=e;return this.filter(function(e,t){return!t.dir&&n.test(e)})}var i=this.files[this.root+e];return i&&!i.dir?i:null},folder:function(r){if(!r)return this;if(f(r))return this.filter(function(e,t){return t.dir&&r.test(e)});var e=this.root+r,t=w.call(this,e),n=this.clone();return n.root=t.name,n},remove:function(r){r=this.root+r;var e=this.files[r];if(e||(\"/\"!==r.slice(-1)&&(r+=\"/\"),e=this.files[r]),e&&!e.dir)delete this.files[r];else for(var t=this.filter(function(e,t){return t.name.slice(0,r.length)===r}),n=0;n<t.length;n++)delete this.files[t[n].name];return this},generate:function(e){throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\")},generateInternalStream:function(e){var t,r={};try{if((r=d.extend(e||{},{streamFiles:!1,compression:\"STORE\",compressionOptions:null,type:\"\",platform:\"DOS\",comment:null,mimeType:\"application/zip\",encodeFileName:i.utf8encode})).type=r.type.toLowerCase(),r.compression=r.compression.toUpperCase(),\"binarystring\"===r.type&&(r.type=\"string\"),!r.type)throw new Error(\"No output type specified.\");d.checkSupport(r.type),\"darwin\"!==r.platform&&\"freebsd\"!==r.platform&&\"linux\"!==r.platform&&\"sunos\"!==r.platform||(r.platform=\"UNIX\"),\"win32\"===r.platform&&(r.platform=\"DOS\");var n=r.comment||this.comment||\"\";t=a.generateWorker(this,r,n)}catch(e){(t=new h(\"error\")).error(e)}return new o(t,r.type||\"string\",r.mimeType)},generateAsync:function(e,t){return this.generateInternalStream(e).accumulate(t)},generateNodeStream:function(e,t){return(e=e||{}).type||(e.type=\"nodebuffer\"),this.generateInternalStream(e).toNodejsStream(t)}};t.exports=n},{\"./compressedObject\":2,\"./defaults\":5,\"./generate\":9,\"./nodejs/NodejsStreamInputAdapter\":12,\"./nodejsUtils\":14,\"./stream/GenericWorker\":28,\"./stream/StreamHelper\":29,\"./utf8\":31,\"./utils\":32,\"./zipObject\":35}],16:[function(e,t,r){t.exports=e(\"stream\")},{stream:void 0}],17:[function(e,t,r){\"use strict\";var n=e(\"./DataReader\");function i(e){n.call(this,e);for(var t=0;t<this.data.length;t++)e[t]=255&e[t]}e(\"../utils\").inherits(i,n),i.prototype.byteAt=function(e){return this.data[this.zero+e]},i.prototype.lastIndexOfSignature=function(e){for(var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),i=e.charCodeAt(3),s=this.length-4;0<=s;--s)if(this.data[s]===t&&this.data[s+1]===r&&this.data[s+2]===n&&this.data[s+3]===i)return s-this.zero;return-1},i.prototype.readAndCheckSignature=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),i=e.charCodeAt(3),s=this.readData(4);return t===s[0]&&r===s[1]&&n===s[2]&&i===s[3]},i.prototype.readData=function(e){if(this.checkOffset(e),0===e)return[];var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=i},{\"../utils\":32,\"./DataReader\":18}],18:[function(e,t,r){\"use strict\";var n=e(\"../utils\");function i(e){this.data=e,this.length=e.length,this.index=0,this.zero=0}i.prototype={checkOffset:function(e){this.checkIndex(this.index+e)},checkIndex:function(e){if(this.length<this.zero+e||e<0)throw new Error(\"End of data reached (data length = \"+this.length+\", asked index = \"+e+\"). Corrupted zip ?\")},setIndex:function(e){this.checkIndex(e),this.index=e},skip:function(e){this.setIndex(this.index+e)},byteAt:function(e){},readInt:function(e){var t,r=0;for(this.checkOffset(e),t=this.index+e-1;t>=this.index;t--)r=(r<<8)+this.byteAt(t);return this.index+=e,r},readString:function(e){return n.transformTo(\"string\",this.readData(e))},readData:function(e){},lastIndexOfSignature:function(e){},readAndCheckSignature:function(e){},readDate:function(){var e=this.readInt(4);return new Date(Date.UTC(1980+(e>>25&127),(e>>21&15)-1,e>>16&31,e>>11&31,e>>5&63,(31&e)<<1))}},t.exports=i},{\"../utils\":32}],19:[function(e,t,r){\"use strict\";var n=e(\"./Uint8ArrayReader\");function i(e){n.call(this,e)}e(\"../utils\").inherits(i,n),i.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=i},{\"../utils\":32,\"./Uint8ArrayReader\":21}],20:[function(e,t,r){\"use strict\";var n=e(\"./DataReader\");function i(e){n.call(this,e)}e(\"../utils\").inherits(i,n),i.prototype.byteAt=function(e){return this.data.charCodeAt(this.zero+e)},i.prototype.lastIndexOfSignature=function(e){return this.data.lastIndexOf(e)-this.zero},i.prototype.readAndCheckSignature=function(e){return e===this.readData(4)},i.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=i},{\"../utils\":32,\"./DataReader\":18}],21:[function(e,t,r){\"use strict\";var n=e(\"./ArrayReader\");function i(e){n.call(this,e)}e(\"../utils\").inherits(i,n),i.prototype.readData=function(e){if(this.checkOffset(e),0===e)return new Uint8Array(0);var t=this.data.subarray(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=i},{\"../utils\":32,\"./ArrayReader\":17}],22:[function(e,t,r){\"use strict\";var n=e(\"../utils\"),i=e(\"../support\"),s=e(\"./ArrayReader\"),o=e(\"./StringReader\"),a=e(\"./NodeBufferReader\"),f=e(\"./Uint8ArrayReader\");t.exports=function(e){var t=n.getTypeOf(e);return n.checkSupport(t),\"string\"!==t||i.uint8array?\"nodebuffer\"===t?new a(e):i.uint8array?new f(n.transformTo(\"uint8array\",e)):new s(n.transformTo(\"array\",e)):new o(e)}},{\"../support\":30,\"../utils\":32,\"./ArrayReader\":17,\"./NodeBufferReader\":19,\"./StringReader\":20,\"./Uint8ArrayReader\":21}],23:[function(e,t,r){\"use strict\";r.LOCAL_FILE_HEADER=\"PK\u0003\u0004\",r.CENTRAL_FILE_HEADER=\"PK\u0001\u0002\",r.CENTRAL_DIRECTORY_END=\"PK\u0005\u0006\",r.ZIP64_CENTRAL_DIRECTORY_LOCATOR=\"PK\u0006\u0007\",r.ZIP64_CENTRAL_DIRECTORY_END=\"PK\u0006\u0006\",r.DATA_DESCRIPTOR=\"PK\u0007\\b\"},{}],24:[function(e,t,r){\"use strict\";var n=e(\"./GenericWorker\"),i=e(\"../utils\");function s(e){n.call(this,\"ConvertWorker to \"+e),this.destType=e}i.inherits(s,n),s.prototype.processChunk=function(e){this.push({data:i.transformTo(this.destType,e.data),meta:e.meta})},t.exports=s},{\"../utils\":32,\"./GenericWorker\":28}],25:[function(e,t,r){\"use strict\";var n=e(\"./GenericWorker\"),i=e(\"../crc32\");function s(){n.call(this,\"Crc32Probe\"),this.withStreamInfo(\"crc32\",0)}e(\"../utils\").inherits(s,n),s.prototype.processChunk=function(e){this.streamInfo.crc32=i(e.data,this.streamInfo.crc32||0),this.push(e)},t.exports=s},{\"../crc32\":4,\"../utils\":32,\"./GenericWorker\":28}],26:[function(e,t,r){\"use strict\";var n=e(\"../utils\"),i=e(\"./GenericWorker\");function s(e){i.call(this,\"DataLengthProbe for \"+e),this.propName=e,this.withStreamInfo(e,0)}n.inherits(s,i),s.prototype.processChunk=function(e){if(e){var t=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=t+e.data.length}i.prototype.processChunk.call(this,e)},t.exports=s},{\"../utils\":32,\"./GenericWorker\":28}],27:[function(e,t,r){\"use strict\";var n=e(\"../utils\"),i=e(\"./GenericWorker\");function s(e){i.call(this,\"DataWorker\");var t=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type=\"\",this._tickScheduled=!1,e.then(function(e){t.dataIsReady=!0,t.data=e,t.max=e&&e.length||0,t.type=n.getTypeOf(e),t.isPaused||t._tickAndRepeat()},function(e){t.error(e)})}n.inherits(s,i),s.prototype.cleanUp=function(){i.prototype.cleanUp.call(this),this.data=null},s.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,n.delay(this._tickAndRepeat,[],this)),!0)},s.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(n.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},s.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var e=null,t=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case\"string\":e=this.data.substring(this.index,t);break;case\"uint8array\":e=this.data.subarray(this.index,t);break;case\"array\":case\"nodebuffer\":e=this.data.slice(this.index,t)}return this.index=t,this.push({data:e,meta:{percent:this.max?this.index/this.max*100:0}})},t.exports=s},{\"../utils\":32,\"./GenericWorker\":28}],28:[function(e,t,r){\"use strict\";function n(e){this.name=e||\"default\",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}n.prototype={push:function(e){this.emit(\"data\",e)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit(\"end\"),this.cleanUp(),this.isFinished=!0}catch(e){this.emit(\"error\",e)}return!0},error:function(e){return!this.isFinished&&(this.isPaused?this.generatedError=e:(this.isFinished=!0,this.emit(\"error\",e),this.previous&&this.previous.error(e),this.cleanUp()),!0)},on:function(e,t){return this._listeners[e].push(t),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(e,t){if(this._listeners[e])for(var r=0;r<this._listeners[e].length;r++)this._listeners[e][r].call(this,t)},pipe:function(e){return e.registerPrevious(this)},registerPrevious:function(e){if(this.isLocked)throw new Error(\"The stream '\"+this+\"' has already been used.\");this.streamInfo=e.streamInfo,this.mergeStreamInfo(),this.previous=e;var t=this;return e.on(\"data\",function(e){t.processChunk(e)}),e.on(\"end\",function(){t.end()}),e.on(\"error\",function(e){t.error(e)}),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;var e=this.isPaused=!1;return this.generatedError&&(this.error(this.generatedError),e=!0),this.previous&&this.previous.resume(),!e},flush:function(){},processChunk:function(e){this.push(e)},withStreamInfo:function(e,t){return this.extraStreamInfo[e]=t,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var e in this.extraStreamInfo)this.extraStreamInfo.hasOwnProperty(e)&&(this.streamInfo[e]=this.extraStreamInfo[e])},lock:function(){if(this.isLocked)throw new Error(\"The stream '\"+this+\"' has already been used.\");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var e=\"Worker \"+this.name;return this.previous?this.previous+\" -> \"+e:e}},t.exports=n},{}],29:[function(e,t,r){\"use strict\";var u=e(\"../utils\"),i=e(\"./ConvertWorker\"),s=e(\"./GenericWorker\"),d=e(\"../base64\"),n=e(\"../support\"),o=e(\"../external\"),a=null;if(n.nodestream)try{a=e(\"../nodejs/NodejsStreamOutputAdapter\")}catch(e){}function f(e,t,r){var n=t;switch(t){case\"blob\":case\"arraybuffer\":n=\"uint8array\";break;case\"base64\":n=\"string\"}try{this._internalType=n,this._outputType=t,this._mimeType=r,u.checkSupport(n),this._worker=e.pipe(new i(n)),e.lock()}catch(e){this._worker=new s(\"error\"),this._worker.error(e)}}f.prototype={accumulate:function(e){return a=this,f=e,new o.Promise(function(t,r){var n=[],i=a._internalType,s=a._outputType,o=a._mimeType;a.on(\"data\",function(e,t){n.push(e),f&&f(t)}).on(\"error\",function(e){n=[],r(e)}).on(\"end\",function(){try{var e=function(e,t,r){switch(e){case\"blob\":return u.newBlob(u.transformTo(\"arraybuffer\",t),r);case\"base64\":return d.encode(t);default:return u.transformTo(e,t)}}(s,function(e,t){var r,n=0,i=null,s=0;for(r=0;r<t.length;r++)s+=t[r].length;switch(e){case\"string\":return t.join(\"\");case\"array\":return Array.prototype.concat.apply([],t);case\"uint8array\":for(i=new Uint8Array(s),r=0;r<t.length;r++)i.set(t[r],n),n+=t[r].length;return i;case\"nodebuffer\":return Buffer.concat(t);default:throw new Error(\"concat : unsupported type '\"+e+\"'\")}}(i,n),o);t(e)}catch(e){r(e)}n=[]}).resume()});var a,f},on:function(e,t){var r=this;return\"data\"===e?this._worker.on(e,function(e){t.call(r,e.data,e.meta)}):this._worker.on(e,function(){u.delay(t,arguments,r)}),this},resume:function(){return u.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(e){if(u.checkSupport(\"nodestream\"),\"nodebuffer\"!==this._outputType)throw new Error(this._outputType+\" is not supported by this method\");return new a(this,{objectMode:\"nodebuffer\"!==this._outputType},e)}},t.exports=f},{\"../base64\":1,\"../external\":6,\"../nodejs/NodejsStreamOutputAdapter\":13,\"../support\":30,\"../utils\":32,\"./ConvertWorker\":24,\"./GenericWorker\":28}],30:[function(e,t,r){\"use strict\";if(r.base64=!0,r.array=!0,r.string=!0,r.arraybuffer=\"undefined\"!=typeof ArrayBuffer&&\"undefined\"!=typeof Uint8Array,r.nodebuffer=\"undefined\"!=typeof Buffer,r.uint8array=\"undefined\"!=typeof Uint8Array,\"undefined\"==typeof ArrayBuffer)r.blob=!1;else{var n=new ArrayBuffer(0);try{r.blob=0===new Blob([n],{type:\"application/zip\"}).size}catch(e){try{var i=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);i.append(n),r.blob=0===i.getBlob(\"application/zip\").size}catch(e){r.blob=!1}}}try{r.nodestream=!!e(\"readable-stream\").Readable}catch(e){r.nodestream=!1}},{\"readable-stream\":16}],31:[function(e,t,s){\"use strict\";for(var a=e(\"./utils\"),f=e(\"./support\"),r=e(\"./nodejsUtils\"),n=e(\"./stream/GenericWorker\"),u=new Array(256),i=0;i<256;i++)u[i]=252<=i?6:248<=i?5:240<=i?4:224<=i?3:192<=i?2:1;function o(){n.call(this,\"utf-8 decode\"),this.leftOver=null}function d(){n.call(this,\"utf-8 encode\")}u[254]=u[254]=1,s.utf8encode=function(e){return f.nodebuffer?r.newBufferFrom(e,\"utf-8\"):function(e){var t,r,n,i,s,o=e.length,a=0;for(i=0;i<o;i++)55296==(64512&(r=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(n=e.charCodeAt(i+1)))&&(r=65536+(r-55296<<10)+(n-56320),i++),a+=r<128?1:r<2048?2:r<65536?3:4;for(t=f.uint8array?new Uint8Array(a):new Array(a),i=s=0;s<a;i++)55296==(64512&(r=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(n=e.charCodeAt(i+1)))&&(r=65536+(r-55296<<10)+(n-56320),i++),r<128?t[s++]=r:(r<2048?t[s++]=192|r>>>6:(r<65536?t[s++]=224|r>>>12:(t[s++]=240|r>>>18,t[s++]=128|r>>>12&63),t[s++]=128|r>>>6&63),t[s++]=128|63&r);return t}(e)},s.utf8decode=function(e){return f.nodebuffer?a.transformTo(\"nodebuffer\",e).toString(\"utf-8\"):function(e){var t,r,n,i,s=e.length,o=new Array(2*s);for(t=r=0;t<s;)if((n=e[t++])<128)o[r++]=n;else if(4<(i=u[n]))o[r++]=65533,t+=i-1;else{for(n&=2===i?31:3===i?15:7;1<i&&t<s;)n=n<<6|63&e[t++],i--;1<i?o[r++]=65533:n<65536?o[r++]=n:(n-=65536,o[r++]=55296|n>>10&1023,o[r++]=56320|1023&n)}return o.length!==r&&(o.subarray?o=o.subarray(0,r):o.length=r),a.applyFromCharCode(o)}(e=a.transformTo(f.uint8array?\"uint8array\":\"array\",e))},a.inherits(o,n),o.prototype.processChunk=function(e){var t=a.transformTo(f.uint8array?\"uint8array\":\"array\",e.data);if(this.leftOver&&this.leftOver.length){if(f.uint8array){var r=t;(t=new Uint8Array(r.length+this.leftOver.length)).set(this.leftOver,0),t.set(r,this.leftOver.length)}else t=this.leftOver.concat(t);this.leftOver=null}var n=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;0<=r&&128==(192&e[r]);)r--;return r<0?t:0===r?t:r+u[e[r]]>t?r:t}(t),i=t;n!==t.length&&(f.uint8array?(i=t.subarray(0,n),this.leftOver=t.subarray(n,t.length)):(i=t.slice(0,n),this.leftOver=t.slice(n,t.length))),this.push({data:s.utf8decode(i),meta:e.meta})},o.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:s.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},s.Utf8DecodeWorker=o,a.inherits(d,n),d.prototype.processChunk=function(e){this.push({data:s.utf8encode(e.data),meta:e.meta})},s.Utf8EncodeWorker=d},{\"./nodejsUtils\":14,\"./stream/GenericWorker\":28,\"./support\":30,\"./utils\":32}],32:[function(e,t,a){\"use strict\";var f=e(\"./support\"),u=e(\"./base64\"),r=e(\"./nodejsUtils\"),n=e(\"set-immediate-shim\"),d=e(\"./external\");function i(e){return e}function h(e,t){for(var r=0;r<e.length;++r)t[r]=255&e.charCodeAt(r);return t}a.newBlob=function(t,r){a.checkSupport(\"blob\");try{return new Blob([t],{type:r})}catch(e){try{var n=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return n.append(t),n.getBlob(r)}catch(e){throw new Error(\"Bug : can't construct the Blob.\")}}};var s={stringifyByChunk:function(e,t,r){var n=[],i=0,s=e.length;if(s<=r)return String.fromCharCode.apply(null,e);for(;i<s;)\"array\"===t||\"nodebuffer\"===t?n.push(String.fromCharCode.apply(null,e.slice(i,Math.min(i+r,s)))):n.push(String.fromCharCode.apply(null,e.subarray(i,Math.min(i+r,s)))),i+=r;return n.join(\"\")},stringifyByChar:function(e){for(var t=\"\",r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t},applyCanBeUsed:{uint8array:function(){try{return f.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(e){return!1}}(),nodebuffer:function(){try{return f.nodebuffer&&1===String.fromCharCode.apply(null,r.allocBuffer(1)).length}catch(e){return!1}}()}};function o(e){var t=65536,r=a.getTypeOf(e),n=!0;if(\"uint8array\"===r?n=s.applyCanBeUsed.uint8array:\"nodebuffer\"===r&&(n=s.applyCanBeUsed.nodebuffer),n)for(;1<t;)try{return s.stringifyByChunk(e,r,t)}catch(e){t=Math.floor(t/2)}return s.stringifyByChar(e)}function l(e,t){for(var r=0;r<e.length;r++)t[r]=e[r];return t}a.applyFromCharCode=o;var c={};c.string={string:i,array:function(e){return h(e,new Array(e.length))},arraybuffer:function(e){return c.string.uint8array(e).buffer},uint8array:function(e){return h(e,new Uint8Array(e.length))},nodebuffer:function(e){return h(e,r.allocBuffer(e.length))}},c.array={string:o,array:i,arraybuffer:function(e){return new Uint8Array(e).buffer},uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return r.newBufferFrom(e)}},c.arraybuffer={string:function(e){return o(new Uint8Array(e))},array:function(e){return l(new Uint8Array(e),new Array(e.byteLength))},arraybuffer:i,uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return r.newBufferFrom(new Uint8Array(e))}},c.uint8array={string:o,array:function(e){return l(e,new Array(e.length))},arraybuffer:function(e){return e.buffer},uint8array:i,nodebuffer:function(e){return r.newBufferFrom(e)}},c.nodebuffer={string:o,array:function(e){return l(e,new Array(e.length))},arraybuffer:function(e){return c.nodebuffer.uint8array(e).buffer},uint8array:function(e){return l(e,new Uint8Array(e.length))},nodebuffer:i},a.transformTo=function(e,t){if(t=t||\"\",!e)return t;a.checkSupport(e);var r=a.getTypeOf(t);return c[r][e](t)},a.getTypeOf=function(e){return\"string\"==typeof e?\"string\":\"[object Array]\"===Object.prototype.toString.call(e)?\"array\":f.nodebuffer&&r.isBuffer(e)?\"nodebuffer\":f.uint8array&&e instanceof Uint8Array?\"uint8array\":f.arraybuffer&&e instanceof ArrayBuffer?\"arraybuffer\":void 0},a.checkSupport=function(e){if(!f[e.toLowerCase()])throw new Error(e+\" is not supported by this platform\")},a.MAX_VALUE_16BITS=65535,a.MAX_VALUE_32BITS=-1,a.pretty=function(e){var t,r,n=\"\";for(r=0;r<(e||\"\").length;r++)n+=\"\\\\x\"+((t=e.charCodeAt(r))<16?\"0\":\"\")+t.toString(16).toUpperCase();return n},a.delay=function(e,t,r){n(function(){e.apply(r||null,t||[])})},a.inherits=function(e,t){function r(){}r.prototype=t.prototype,e.prototype=new r},a.extend=function(){var e,t,r={};for(e=0;e<arguments.length;e++)for(t in arguments[e])arguments[e].hasOwnProperty(t)&&void 0===r[t]&&(r[t]=arguments[e][t]);return r},a.prepareContent=function(n,e,i,s,o){return d.Promise.resolve(e).then(function(n){return f.blob&&(n instanceof Blob||-1!==[\"[object File]\",\"[object Blob]\"].indexOf(Object.prototype.toString.call(n)))&&\"undefined\"!=typeof FileReader?new d.Promise(function(t,r){var e=new FileReader;e.onload=function(e){t(e.target.result)},e.onerror=function(e){r(e.target.error)},e.readAsArrayBuffer(n)}):n}).then(function(e){var t,r=a.getTypeOf(e);return r?(\"arraybuffer\"===r?e=a.transformTo(\"uint8array\",e):\"string\"===r&&(o?e=u.decode(e):i&&!0!==s&&(e=h(t=e,f.uint8array?new Uint8Array(t.length):new Array(t.length)))),e):d.Promise.reject(new Error(\"Can't read the data of '\"+n+\"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?\"))})}},{\"./base64\":1,\"./external\":6,\"./nodejsUtils\":14,\"./support\":30,\"set-immediate-shim\":54}],33:[function(e,t,r){\"use strict\";var n=e(\"./reader/readerFor\"),i=e(\"./utils\"),s=e(\"./signature\"),o=e(\"./zipEntry\"),a=(e(\"./utf8\"),e(\"./support\"));function f(e){this.files=[],this.loadOptions=e}f.prototype={checkSignature:function(e){if(!this.reader.readAndCheckSignature(e)){this.reader.index-=4;var t=this.reader.readString(4);throw new Error(\"Corrupted zip or bug: unexpected signature (\"+i.pretty(t)+\", expected \"+i.pretty(e)+\")\")}},isSignature:function(e,t){var r=this.reader.index;this.reader.setIndex(e);var n=this.reader.readString(4)===t;return this.reader.setIndex(r),n},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var e=this.reader.readData(this.zipCommentLength),t=a.uint8array?\"uint8array\":\"array\",r=i.transformTo(t,e);this.zipComment=this.loadOptions.decodeFileName(r)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var e,t,r,n=this.zip64EndOfCentralSize-44;0<n;)e=this.reader.readInt(2),t=this.reader.readInt(4),r=this.reader.readData(t),this.zip64ExtensibleData[e]={id:e,length:t,value:r}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw new Error(\"Multi-volumes zip are not supported\")},readLocalFiles:function(){var e,t;for(e=0;e<this.files.length;e++)t=this.files[e],this.reader.setIndex(t.localHeaderOffset),this.checkSignature(s.LOCAL_FILE_HEADER),t.readLocalPart(this.reader),t.handleUTF8(),t.processAttributes()},readCentralDir:function(){var e;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(s.CENTRAL_FILE_HEADER);)(e=new o({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(e);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error(\"Corrupted zip or bug: expected \"+this.centralDirRecords+\" records in central dir, got \"+this.files.length)},readEndOfCentral:function(){var e=this.reader.lastIndexOfSignature(s.CENTRAL_DIRECTORY_END);if(e<0)throw this.isSignature(0,s.LOCAL_FILE_HEADER)?new Error(\"Corrupted zip: can't find end of central directory\"):new Error(\"Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html\");this.reader.setIndex(e);var t=e;if(this.checkSignature(s.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===i.MAX_VALUE_16BITS||this.diskWithCentralDirStart===i.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===i.MAX_VALUE_16BITS||this.centralDirRecords===i.MAX_VALUE_16BITS||this.centralDirSize===i.MAX_VALUE_32BITS||this.centralDirOffset===i.MAX_VALUE_32BITS){if(this.zip64=!0,(e=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory locator\");if(this.reader.setIndex(e),this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,s.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory\");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var r=this.centralDirOffset+this.centralDirSize;this.zip64&&(r+=20,r+=12+this.zip64EndOfCentralSize);var n=t-r;if(0<n)this.isSignature(t,s.CENTRAL_FILE_HEADER)||(this.reader.zero=n);else if(n<0)throw new Error(\"Corrupted zip: missing \"+Math.abs(n)+\" bytes.\")},prepareReader:function(e){this.reader=n(e)},load:function(e){this.prepareReader(e),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},t.exports=f},{\"./reader/readerFor\":22,\"./signature\":23,\"./support\":30,\"./utf8\":31,\"./utils\":32,\"./zipEntry\":34}],34:[function(e,t,r){\"use strict\";var n=e(\"./reader/readerFor\"),s=e(\"./utils\"),i=e(\"./compressedObject\"),o=e(\"./crc32\"),a=e(\"./utf8\"),f=e(\"./compressions\"),u=e(\"./support\");function d(e,t){this.options=e,this.loadOptions=t}d.prototype={isEncrypted:function(){return 1==(1&this.bitFlag)},useUTF8:function(){return 2048==(2048&this.bitFlag)},readLocalPart:function(e){var t,r;if(e.skip(22),this.fileNameLength=e.readInt(2),r=e.readInt(2),this.fileName=e.readData(this.fileNameLength),e.skip(r),-1===this.compressedSize||-1===this.uncompressedSize)throw new Error(\"Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)\");if(null===(t=function(e){for(var t in f)if(f.hasOwnProperty(t)&&f[t].magic===e)return f[t];return null}(this.compressionMethod)))throw new Error(\"Corrupted zip : compression \"+s.pretty(this.compressionMethod)+\" unknown (inner file : \"+s.transformTo(\"string\",this.fileName)+\")\");this.decompressed=new i(this.compressedSize,this.uncompressedSize,this.crc32,t,e.readData(this.compressedSize))},readCentralPart:function(e){this.versionMadeBy=e.readInt(2),e.skip(2),this.bitFlag=e.readInt(2),this.compressionMethod=e.readString(2),this.date=e.readDate(),this.crc32=e.readInt(4),this.compressedSize=e.readInt(4),this.uncompressedSize=e.readInt(4);var t=e.readInt(2);if(this.extraFieldsLength=e.readInt(2),this.fileCommentLength=e.readInt(2),this.diskNumberStart=e.readInt(2),this.internalFileAttributes=e.readInt(2),this.externalFileAttributes=e.readInt(4),this.localHeaderOffset=e.readInt(4),this.isEncrypted())throw new Error(\"Encrypted zip are not supported\");e.skip(t),this.readExtraFields(e),this.parseZIP64ExtraField(e),this.fileComment=e.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var e=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0==e&&(this.dosPermissions=63&this.externalFileAttributes),3==e&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||\"/\"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(e){if(this.extraFields[1]){var t=n(this.extraFields[1].value);this.uncompressedSize===s.MAX_VALUE_32BITS&&(this.uncompressedSize=t.readInt(8)),this.compressedSize===s.MAX_VALUE_32BITS&&(this.compressedSize=t.readInt(8)),this.localHeaderOffset===s.MAX_VALUE_32BITS&&(this.localHeaderOffset=t.readInt(8)),this.diskNumberStart===s.MAX_VALUE_32BITS&&(this.diskNumberStart=t.readInt(4))}},readExtraFields:function(e){var t,r,n,i=e.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});e.index+4<i;)t=e.readInt(2),r=e.readInt(2),n=e.readData(r),this.extraFields[t]={id:t,length:r,value:n};e.setIndex(i)},handleUTF8:function(){var e=u.uint8array?\"uint8array\":\"array\";if(this.useUTF8())this.fileNameStr=a.utf8decode(this.fileName),this.fileCommentStr=a.utf8decode(this.fileComment);else{var t=this.findExtraFieldUnicodePath();if(null!==t)this.fileNameStr=t;else{var r=s.transformTo(e,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(r)}var n=this.findExtraFieldUnicodeComment();if(null!==n)this.fileCommentStr=n;else{var i=s.transformTo(e,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(i)}}},findExtraFieldUnicodePath:function(){var e=this.extraFields[28789];if(e){var t=n(e.value);return 1!==t.readInt(1)?null:o(this.fileName)!==t.readInt(4)?null:a.utf8decode(t.readData(e.length-5))}return null},findExtraFieldUnicodeComment:function(){var e=this.extraFields[25461];if(e){var t=n(e.value);return 1!==t.readInt(1)?null:o(this.fileComment)!==t.readInt(4)?null:a.utf8decode(t.readData(e.length-5))}return null}},t.exports=d},{\"./compressedObject\":2,\"./compressions\":3,\"./crc32\":4,\"./reader/readerFor\":22,\"./support\":30,\"./utf8\":31,\"./utils\":32}],35:[function(e,t,r){\"use strict\";function n(e,t,r){this.name=e,this.dir=r.dir,this.date=r.date,this.comment=r.comment,this.unixPermissions=r.unixPermissions,this.dosPermissions=r.dosPermissions,this._data=t,this._dataBinary=r.binary,this.options={compression:r.compression,compressionOptions:r.compressionOptions}}var s=e(\"./stream/StreamHelper\"),i=e(\"./stream/DataWorker\"),o=e(\"./utf8\"),a=e(\"./compressedObject\"),f=e(\"./stream/GenericWorker\");n.prototype={internalStream:function(e){var t=null,r=\"string\";try{if(!e)throw new Error(\"No output type specified.\");var n=\"string\"===(r=e.toLowerCase())||\"text\"===r;\"binarystring\"!==r&&\"text\"!==r||(r=\"string\"),t=this._decompressWorker();var i=!this._dataBinary;i&&!n&&(t=t.pipe(new o.Utf8EncodeWorker)),!i&&n&&(t=t.pipe(new o.Utf8DecodeWorker))}catch(e){(t=new f(\"error\")).error(e)}return new s(t,r,\"\")},async:function(e,t){return this.internalStream(e).accumulate(t)},nodeStream:function(e,t){return this.internalStream(e||\"nodebuffer\").toNodejsStream(t)},_compressWorker:function(e,t){if(this._data instanceof a&&this._data.compression.magic===e.magic)return this._data.getCompressedWorker();var r=this._decompressWorker();return this._dataBinary||(r=r.pipe(new o.Utf8EncodeWorker)),a.createWorkerFrom(r,e,t)},_decompressWorker:function(){return this._data instanceof a?this._data.getContentWorker():this._data instanceof f?this._data:new i(this._data)}};for(var u=[\"asText\",\"asBinary\",\"asNodeBuffer\",\"asUint8Array\",\"asArrayBuffer\"],d=function(){throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\")},h=0;h<u.length;h++)n.prototype[u[h]]=d;t.exports=n},{\"./compressedObject\":2,\"./stream/DataWorker\":27,\"./stream/GenericWorker\":28,\"./stream/StreamHelper\":29,\"./utf8\":31}],36:[function(e,d,t){(function(t){\"use strict\";var r,n,e=t.MutationObserver||t.WebKitMutationObserver;if(e){var i=0,s=new e(u),o=t.document.createTextNode(\"\");s.observe(o,{characterData:!0}),r=function(){o.data=i=++i%2}}else if(t.setImmediate||void 0===t.MessageChannel)r=\"document\"in t&&\"onreadystatechange\"in t.document.createElement(\"script\")?function(){var e=t.document.createElement(\"script\");e.onreadystatechange=function(){u(),e.onreadystatechange=null,e.parentNode.removeChild(e),e=null},t.document.documentElement.appendChild(e)}:function(){setTimeout(u,0)};else{var a=new t.MessageChannel;a.port1.onmessage=u,r=function(){a.port2.postMessage(0)}}var f=[];function u(){var e,t;n=!0;for(var r=f.length;r;){for(t=f,f=[],e=-1;++e<r;)t[e]();r=f.length}n=!1}d.exports=function(e){1!==f.push(e)||n||r()}}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}],37:[function(e,t,r){\"use strict\";var i=e(\"immediate\");function u(){}var d={},s=[\"REJECTED\"],o=[\"FULFILLED\"],n=[\"PENDING\"];function a(e){if(\"function\"!=typeof e)throw new TypeError(\"resolver must be a function\");this.state=n,this.queue=[],this.outcome=void 0,e!==u&&c(this,e)}function f(e,t,r){this.promise=e,\"function\"==typeof t&&(this.onFulfilled=t,this.callFulfilled=this.otherCallFulfilled),\"function\"==typeof r&&(this.onRejected=r,this.callRejected=this.otherCallRejected)}function h(t,r,n){i(function(){var e;try{e=r(n)}catch(e){return d.reject(t,e)}e===t?d.reject(t,new TypeError(\"Cannot resolve promise with itself\")):d.resolve(t,e)})}function l(e){var t=e&&e.then;if(e&&(\"object\"==typeof e||\"function\"==typeof e)&&\"function\"==typeof t)return function(){t.apply(e,arguments)}}function c(t,e){var r=!1;function n(e){r||(r=!0,d.reject(t,e))}function i(e){r||(r=!0,d.resolve(t,e))}var s=p(function(){e(i,n)});\"error\"===s.status&&n(s.value)}function p(e,t){var r={};try{r.value=e(t),r.status=\"success\"}catch(e){r.status=\"error\",r.value=e}return r}(t.exports=a).prototype.finally=function(t){if(\"function\"!=typeof t)return this;var r=this.constructor;return this.then(function(e){return r.resolve(t()).then(function(){return e})},function(e){return r.resolve(t()).then(function(){throw e})})},a.prototype.catch=function(e){return this.then(null,e)},a.prototype.then=function(e,t){if(\"function\"!=typeof e&&this.state===o||\"function\"!=typeof t&&this.state===s)return this;var r=new this.constructor(u);return this.state!==n?h(r,this.state===o?e:t,this.outcome):this.queue.push(new f(r,e,t)),r},f.prototype.callFulfilled=function(e){d.resolve(this.promise,e)},f.prototype.otherCallFulfilled=function(e){h(this.promise,this.onFulfilled,e)},f.prototype.callRejected=function(e){d.reject(this.promise,e)},f.prototype.otherCallRejected=function(e){h(this.promise,this.onRejected,e)},d.resolve=function(e,t){var r=p(l,t);if(\"error\"===r.status)return d.reject(e,r.value);var n=r.value;if(n)c(e,n);else{e.state=o,e.outcome=t;for(var i=-1,s=e.queue.length;++i<s;)e.queue[i].callFulfilled(t)}return e},d.reject=function(e,t){e.state=s,e.outcome=t;for(var r=-1,n=e.queue.length;++r<n;)e.queue[r].callRejected(t);return e},a.resolve=function(e){return e instanceof this?e:d.resolve(new this(u),e)},a.reject=function(e){var t=new this(u);return d.reject(t,e)},a.all=function(e){var r=this;if(\"[object Array]\"!==Object.prototype.toString.call(e))return this.reject(new TypeError(\"must be an array\"));var n=e.length,i=!1;if(!n)return this.resolve([]);for(var s=new Array(n),o=0,t=-1,a=new this(u);++t<n;)f(e[t],t);return a;function f(e,t){r.resolve(e).then(function(e){s[t]=e,++o!==n||i||(i=!0,d.resolve(a,s))},function(e){i||(i=!0,d.reject(a,e))})}},a.race=function(e){if(\"[object Array]\"!==Object.prototype.toString.call(e))return this.reject(new TypeError(\"must be an array\"));var t=e.length,r=!1;if(!t)return this.resolve([]);for(var n,i=-1,s=new this(u);++i<t;)n=e[i],this.resolve(n).then(function(e){r||(r=!0,d.resolve(s,e))},function(e){r||(r=!0,d.reject(s,e))});return s}},{immediate:36}],38:[function(e,t,r){\"use strict\";var n={};(0,e(\"./lib/utils/common\").assign)(n,e(\"./lib/deflate\"),e(\"./lib/inflate\"),e(\"./lib/zlib/constants\")),t.exports=n},{\"./lib/deflate\":39,\"./lib/inflate\":40,\"./lib/utils/common\":41,\"./lib/zlib/constants\":44}],39:[function(e,t,r){\"use strict\";var o=e(\"./zlib/deflate\"),a=e(\"./utils/common\"),f=e(\"./utils/strings\"),i=e(\"./zlib/messages\"),s=e(\"./zlib/zstream\"),u=Object.prototype.toString,d=0,h=-1,l=0,c=8;function p(e){if(!(this instanceof p))return new p(e);this.options=a.assign({level:h,method:c,chunkSize:16384,windowBits:15,memLevel:8,strategy:l,to:\"\"},e||{});var t=this.options;t.raw&&0<t.windowBits?t.windowBits=-t.windowBits:t.gzip&&0<t.windowBits&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg=\"\",this.ended=!1,this.chunks=[],this.strm=new s,this.strm.avail_out=0;var r=o.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(r!==d)throw new Error(i[r]);if(t.header&&o.deflateSetHeader(this.strm,t.header),t.dictionary){var n;if(n=\"string\"==typeof t.dictionary?f.string2buf(t.dictionary):\"[object ArrayBuffer]\"===u.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,(r=o.deflateSetDictionary(this.strm,n))!==d)throw new Error(i[r]);this._dict_set=!0}}function n(e,t){var r=new p(t);if(r.push(e,!0),r.err)throw r.msg||i[r.err];return r.result}p.prototype.push=function(e,t){var r,n,i=this.strm,s=this.options.chunkSize;if(this.ended)return!1;n=t===~~t?t:!0===t?4:0,\"string\"==typeof e?i.input=f.string2buf(e):\"[object ArrayBuffer]\"===u.call(e)?i.input=new Uint8Array(e):i.input=e,i.next_in=0,i.avail_in=i.input.length;do{if(0===i.avail_out&&(i.output=new a.Buf8(s),i.next_out=0,i.avail_out=s),1!==(r=o.deflate(i,n))&&r!==d)return this.onEnd(r),!(this.ended=!0);0!==i.avail_out&&(0!==i.avail_in||4!==n&&2!==n)||(\"string\"===this.options.to?this.onData(f.buf2binstring(a.shrinkBuf(i.output,i.next_out))):this.onData(a.shrinkBuf(i.output,i.next_out)))}while((0<i.avail_in||0===i.avail_out)&&1!==r);return 4===n?(r=o.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===d):2!==n||(this.onEnd(d),!(i.avail_out=0))},p.prototype.onData=function(e){this.chunks.push(e)},p.prototype.onEnd=function(e){e===d&&(\"string\"===this.options.to?this.result=this.chunks.join(\"\"):this.result=a.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Deflate=p,r.deflate=n,r.deflateRaw=function(e,t){return(t=t||{}).raw=!0,n(e,t)},r.gzip=function(e,t){return(t=t||{}).gzip=!0,n(e,t)}},{\"./utils/common\":41,\"./utils/strings\":42,\"./zlib/deflate\":46,\"./zlib/messages\":51,\"./zlib/zstream\":53}],40:[function(e,t,r){\"use strict\";var l=e(\"./zlib/inflate\"),c=e(\"./utils/common\"),p=e(\"./utils/strings\"),m=e(\"./zlib/constants\"),n=e(\"./zlib/messages\"),i=e(\"./zlib/zstream\"),s=e(\"./zlib/gzheader\"),_=Object.prototype.toString;function o(e){if(!(this instanceof o))return new o(e);this.options=c.assign({chunkSize:16384,windowBits:0,to:\"\"},e||{});var t=this.options;t.raw&&0<=t.windowBits&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(0<=t.windowBits&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),15<t.windowBits&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg=\"\",this.ended=!1,this.chunks=[],this.strm=new i,this.strm.avail_out=0;var r=l.inflateInit2(this.strm,t.windowBits);if(r!==m.Z_OK)throw new Error(n[r]);this.header=new s,l.inflateGetHeader(this.strm,this.header)}function a(e,t){var r=new o(t);if(r.push(e,!0),r.err)throw r.msg||n[r.err];return r.result}o.prototype.push=function(e,t){var r,n,i,s,o,a,f=this.strm,u=this.options.chunkSize,d=this.options.dictionary,h=!1;if(this.ended)return!1;n=t===~~t?t:!0===t?m.Z_FINISH:m.Z_NO_FLUSH,\"string\"==typeof e?f.input=p.binstring2buf(e):\"[object ArrayBuffer]\"===_.call(e)?f.input=new Uint8Array(e):f.input=e,f.next_in=0,f.avail_in=f.input.length;do{if(0===f.avail_out&&(f.output=new c.Buf8(u),f.next_out=0,f.avail_out=u),(r=l.inflate(f,m.Z_NO_FLUSH))===m.Z_NEED_DICT&&d&&(a=\"string\"==typeof d?p.string2buf(d):\"[object ArrayBuffer]\"===_.call(d)?new Uint8Array(d):d,r=l.inflateSetDictionary(this.strm,a)),r===m.Z_BUF_ERROR&&!0===h&&(r=m.Z_OK,h=!1),r!==m.Z_STREAM_END&&r!==m.Z_OK)return this.onEnd(r),!(this.ended=!0);f.next_out&&(0!==f.avail_out&&r!==m.Z_STREAM_END&&(0!==f.avail_in||n!==m.Z_FINISH&&n!==m.Z_SYNC_FLUSH)||(\"string\"===this.options.to?(i=p.utf8border(f.output,f.next_out),s=f.next_out-i,o=p.buf2string(f.output,i),f.next_out=s,f.avail_out=u-s,s&&c.arraySet(f.output,f.output,i,s,0),this.onData(o)):this.onData(c.shrinkBuf(f.output,f.next_out)))),0===f.avail_in&&0===f.avail_out&&(h=!0)}while((0<f.avail_in||0===f.avail_out)&&r!==m.Z_STREAM_END);return r===m.Z_STREAM_END&&(n=m.Z_FINISH),n===m.Z_FINISH?(r=l.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===m.Z_OK):n!==m.Z_SYNC_FLUSH||(this.onEnd(m.Z_OK),!(f.avail_out=0))},o.prototype.onData=function(e){this.chunks.push(e)},o.prototype.onEnd=function(e){e===m.Z_OK&&(\"string\"===this.options.to?this.result=this.chunks.join(\"\"):this.result=c.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Inflate=o,r.inflate=a,r.inflateRaw=function(e,t){return(t=t||{}).raw=!0,a(e,t)},r.ungzip=a},{\"./utils/common\":41,\"./utils/strings\":42,\"./zlib/constants\":44,\"./zlib/gzheader\":47,\"./zlib/inflate\":49,\"./zlib/messages\":51,\"./zlib/zstream\":53}],41:[function(e,t,r){\"use strict\";var n=\"undefined\"!=typeof Uint8Array&&\"undefined\"!=typeof Uint16Array&&\"undefined\"!=typeof Int32Array;r.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if(\"object\"!=typeof r)throw new TypeError(r+\"must be non-object\");for(var n in r)r.hasOwnProperty(n)&&(e[n]=r[n])}}return e},r.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var i={arraySet:function(e,t,r,n,i){if(t.subarray&&e.subarray)e.set(t.subarray(r,r+n),i);else for(var s=0;s<n;s++)e[i+s]=t[r+s]},flattenChunks:function(e){var t,r,n,i,s,o;for(t=n=0,r=e.length;t<r;t++)n+=e[t].length;for(o=new Uint8Array(n),t=i=0,r=e.length;t<r;t++)s=e[t],o.set(s,i),i+=s.length;return o}},s={arraySet:function(e,t,r,n,i){for(var s=0;s<n;s++)e[i+s]=t[r+s]},flattenChunks:function(e){return[].concat.apply([],e)}};r.setTyped=function(e){e?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,i)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,s))},r.setTyped(n)},{}],42:[function(e,t,r){\"use strict\";var f=e(\"./common\"),i=!0,s=!0;try{String.fromCharCode.apply(null,[0])}catch(e){i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){s=!1}for(var u=new f.Buf8(256),n=0;n<256;n++)u[n]=252<=n?6:248<=n?5:240<=n?4:224<=n?3:192<=n?2:1;function d(e,t){if(t<65537&&(e.subarray&&s||!e.subarray&&i))return String.fromCharCode.apply(null,f.shrinkBuf(e,t));for(var r=\"\",n=0;n<t;n++)r+=String.fromCharCode(e[n]);return r}u[254]=u[254]=1,r.string2buf=function(e){var t,r,n,i,s,o=e.length,a=0;for(i=0;i<o;i++)55296==(64512&(r=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(n=e.charCodeAt(i+1)))&&(r=65536+(r-55296<<10)+(n-56320),i++),a+=r<128?1:r<2048?2:r<65536?3:4;for(t=new f.Buf8(a),i=s=0;s<a;i++)55296==(64512&(r=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(n=e.charCodeAt(i+1)))&&(r=65536+(r-55296<<10)+(n-56320),i++),r<128?t[s++]=r:(r<2048?t[s++]=192|r>>>6:(r<65536?t[s++]=224|r>>>12:(t[s++]=240|r>>>18,t[s++]=128|r>>>12&63),t[s++]=128|r>>>6&63),t[s++]=128|63&r);return t},r.buf2binstring=function(e){return d(e,e.length)},r.binstring2buf=function(e){for(var t=new f.Buf8(e.length),r=0,n=t.length;r<n;r++)t[r]=e.charCodeAt(r);return t},r.buf2string=function(e,t){var r,n,i,s,o=t||e.length,a=new Array(2*o);for(r=n=0;r<o;)if((i=e[r++])<128)a[n++]=i;else if(4<(s=u[i]))a[n++]=65533,r+=s-1;else{for(i&=2===s?31:3===s?15:7;1<s&&r<o;)i=i<<6|63&e[r++],s--;1<s?a[n++]=65533:i<65536?a[n++]=i:(i-=65536,a[n++]=55296|i>>10&1023,a[n++]=56320|1023&i)}return d(a,n)},r.utf8border=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;0<=r&&128==(192&e[r]);)r--;return r<0?t:0===r?t:r+u[e[r]]>t?r:t}},{\"./common\":41}],43:[function(e,t,r){\"use strict\";t.exports=function(e,t,r,n){for(var i=65535&e|0,s=e>>>16&65535|0,o=0;0!==r;){for(r-=o=2e3<r?2e3:r;s=s+(i=i+t[n++]|0)|0,--o;);i%=65521,s%=65521}return i|s<<16|0}},{}],44:[function(e,t,r){\"use strict\";t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],45:[function(e,t,r){\"use strict\";var a=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();t.exports=function(e,t,r,n){var i=a,s=n+r;e^=-1;for(var o=n;o<s;o++)e=e>>>8^i[255&(e^t[o])];return-1^e}},{}],46:[function(e,t,r){\"use strict\";var f,l=e(\"../utils/common\"),u=e(\"./trees\"),c=e(\"./adler32\"),p=e(\"./crc32\"),n=e(\"./messages\"),d=0,h=0,m=-2,i=2,_=8,s=286,o=30,a=19,w=2*s+1,v=15,g=3,y=258,b=y+g+1,k=42,x=113;function S(e,t){return e.msg=n[t],t}function E(e){return(e<<1)-(4<e?9:0)}function z(e){for(var t=e.length;0<=--t;)e[t]=0}function C(e){var t=e.state,r=t.pending;r>e.avail_out&&(r=e.avail_out),0!==r&&(l.arraySet(e.output,t.pending_buf,t.pending_out,r,e.next_out),e.next_out+=r,t.pending_out+=r,e.total_out+=r,e.avail_out-=r,t.pending-=r,0===t.pending&&(t.pending_out=0))}function A(e,t){u._tr_flush_block(e,0<=e.block_start?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,C(e.strm)}function O(e,t){e.pending_buf[e.pending++]=t}function I(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function D(e,t){var r,n,i=e.max_chain_length,s=e.strstart,o=e.prev_length,a=e.nice_match,f=e.strstart>e.w_size-b?e.strstart-(e.w_size-b):0,u=e.window,d=e.w_mask,h=e.prev,l=e.strstart+y,c=u[s+o-1],p=u[s+o];e.prev_length>=e.good_match&&(i>>=2),a>e.lookahead&&(a=e.lookahead);do{if(u[(r=t)+o]===p&&u[r+o-1]===c&&u[r]===u[s]&&u[++r]===u[s+1]){s+=2,r++;do{}while(u[++s]===u[++r]&&u[++s]===u[++r]&&u[++s]===u[++r]&&u[++s]===u[++r]&&u[++s]===u[++r]&&u[++s]===u[++r]&&u[++s]===u[++r]&&u[++s]===u[++r]&&s<l);if(n=y-(l-s),s=l-y,o<n){if(e.match_start=t,a<=(o=n))break;c=u[s+o-1],p=u[s+o]}}}while((t=h[t&d])>f&&0!=--i);return o<=e.lookahead?o:e.lookahead}function B(e){var t,r,n,i,s,o,a,f,u,d,h=e.w_size;do{if(i=e.window_size-e.lookahead-e.strstart,e.strstart>=h+(h-b)){for(l.arraySet(e.window,e.window,h,h,0),e.match_start-=h,e.strstart-=h,e.block_start-=h,t=r=e.hash_size;n=e.head[--t],e.head[t]=h<=n?n-h:0,--r;);for(t=r=h;n=e.prev[--t],e.prev[t]=h<=n?n-h:0,--r;);i+=h}if(0===e.strm.avail_in)break;if(o=e.strm,a=e.window,f=e.strstart+e.lookahead,d=void 0,(u=i)<(d=o.avail_in)&&(d=u),r=0===d?0:(o.avail_in-=d,l.arraySet(a,o.input,o.next_in,d,f),1===o.state.wrap?o.adler=c(o.adler,a,d,f):2===o.state.wrap&&(o.adler=p(o.adler,a,d,f)),o.next_in+=d,o.total_in+=d,d),e.lookahead+=r,e.lookahead+e.insert>=g)for(s=e.strstart-e.insert,e.ins_h=e.window[s],e.ins_h=(e.ins_h<<e.hash_shift^e.window[s+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[s+g-1])&e.hash_mask,e.prev[s&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=s,s++,e.insert--,!(e.lookahead+e.insert<g)););}while(e.lookahead<b&&0!==e.strm.avail_in)}function T(e,t){for(var r,n;;){if(e.lookahead<b){if(B(e),e.lookahead<b&&t===d)return 1;if(0===e.lookahead)break}if(r=0,e.lookahead>=g&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+g-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==r&&e.strstart-r<=e.w_size-b&&(e.match_length=D(e,r)),e.match_length>=g)if(n=u._tr_tally(e,e.strstart-e.match_start,e.match_length-g),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=g){for(e.match_length--;e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+g-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart,0!=--e.match_length;);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else n=u._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(n&&(A(e,!1),0===e.strm.avail_out))return 1}return e.insert=e.strstart<g-1?e.strstart:g-1,4===t?(A(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(A(e,!1),0===e.strm.avail_out)?1:2}function R(e,t){for(var r,n,i;;){if(e.lookahead<b){if(B(e),e.lookahead<b&&t===d)return 1;if(0===e.lookahead)break}if(r=0,e.lookahead>=g&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+g-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=g-1,0!==r&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-b&&(e.match_length=D(e,r),e.match_length<=5&&(1===e.strategy||e.match_length===g&&4096<e.strstart-e.match_start)&&(e.match_length=g-1)),e.prev_length>=g&&e.match_length<=e.prev_length){for(i=e.strstart+e.lookahead-g,n=u._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-g),e.lookahead-=e.prev_length-1,e.prev_length-=2;++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+g-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!=--e.prev_length;);if(e.match_available=0,e.match_length=g-1,e.strstart++,n&&(A(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if((n=u._tr_tally(e,0,e.window[e.strstart-1]))&&A(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(n=u._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<g-1?e.strstart:g-1,4===t?(A(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(A(e,!1),0===e.strm.avail_out)?1:2}function F(e,t,r,n,i){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=n,this.func=i}function N(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=_,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new l.Buf16(2*w),this.dyn_dtree=new l.Buf16(2*(2*o+1)),this.bl_tree=new l.Buf16(2*(2*a+1)),z(this.dyn_ltree),z(this.dyn_dtree),z(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new l.Buf16(v+1),this.heap=new l.Buf16(2*s+1),z(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new l.Buf16(2*s+1),z(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function U(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=i,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?k:x,e.adler=2===t.wrap?0:1,t.last_flush=d,u._tr_init(t),h):S(e,m)}function L(e){var t,r=U(e);return r===h&&((t=e.state).window_size=2*t.w_size,z(t.head),t.max_lazy_match=f[t.level].max_lazy,t.good_match=f[t.level].good_length,t.nice_match=f[t.level].nice_length,t.max_chain_length=f[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=g-1,t.match_available=0,t.ins_h=0),r}function P(e,t,r,n,i,s){if(!e)return m;var o=1;if(-1===t&&(t=6),n<0?(o=0,n=-n):15<n&&(o=2,n-=16),i<1||9<i||r!==_||n<8||15<n||t<0||9<t||s<0||4<s)return S(e,m);8===n&&(n=9);var a=new N;return(e.state=a).strm=e,a.wrap=o,a.gzhead=null,a.w_bits=n,a.w_size=1<<a.w_bits,a.w_mask=a.w_size-1,a.hash_bits=i+7,a.hash_size=1<<a.hash_bits,a.hash_mask=a.hash_size-1,a.hash_shift=~~((a.hash_bits+g-1)/g),a.window=new l.Buf8(2*a.w_size),a.head=new l.Buf16(a.hash_size),a.prev=new l.Buf16(a.w_size),a.lit_bufsize=1<<i+6,a.pending_buf_size=4*a.lit_bufsize,a.pending_buf=new l.Buf8(a.pending_buf_size),a.d_buf=1*a.lit_bufsize,a.l_buf=3*a.lit_bufsize,a.level=t,a.strategy=s,a.method=r,L(e)}f=[new F(0,0,0,0,function(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(B(e),0===e.lookahead&&t===d)return 1;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var n=e.block_start+r;if((0===e.strstart||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,A(e,!1),0===e.strm.avail_out))return 1;if(e.strstart-e.block_start>=e.w_size-b&&(A(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(A(e,!0),0===e.strm.avail_out?3:4):(e.strstart>e.block_start&&(A(e,!1),e.strm.avail_out),1)}),new F(4,4,8,4,T),new F(4,5,16,8,T),new F(4,6,32,32,T),new F(4,4,16,16,R),new F(8,16,32,32,R),new F(8,16,128,128,R),new F(8,32,128,256,R),new F(32,128,258,1024,R),new F(32,258,258,4096,R)],r.deflateInit=function(e,t){return P(e,t,_,15,8,0)},r.deflateInit2=P,r.deflateReset=L,r.deflateResetKeep=U,r.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?m:(e.state.gzhead=t,h):m},r.deflate=function(e,t){var r,n,i,s;if(!e||!e.state||5<t||t<0)return e?S(e,m):m;if(n=e.state,!e.output||!e.input&&0!==e.avail_in||666===n.status&&4!==t)return S(e,0===e.avail_out?-5:m);if(n.strm=e,r=n.last_flush,n.last_flush=t,n.status===k)if(2===n.wrap)e.adler=0,O(n,31),O(n,139),O(n,8),n.gzhead?(O(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),O(n,255&n.gzhead.time),O(n,n.gzhead.time>>8&255),O(n,n.gzhead.time>>16&255),O(n,n.gzhead.time>>24&255),O(n,9===n.level?2:2<=n.strategy||n.level<2?4:0),O(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(O(n,255&n.gzhead.extra.length),O(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(e.adler=p(e.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=69):(O(n,0),O(n,0),O(n,0),O(n,0),O(n,0),O(n,9===n.level?2:2<=n.strategy||n.level<2?4:0),O(n,3),n.status=x);else{var o=_+(n.w_bits-8<<4)<<8;o|=(2<=n.strategy||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(o|=32),o+=31-o%31,n.status=x,I(n,o),0!==n.strstart&&(I(n,e.adler>>>16),I(n,65535&e.adler)),e.adler=1}if(69===n.status)if(n.gzhead.extra){for(i=n.pending;n.gzindex<(65535&n.gzhead.extra.length)&&(n.pending!==n.pending_buf_size||(n.gzhead.hcrc&&n.pending>i&&(e.adler=p(e.adler,n.pending_buf,n.pending-i,i)),C(e),i=n.pending,n.pending!==n.pending_buf_size));)O(n,255&n.gzhead.extra[n.gzindex]),n.gzindex++;n.gzhead.hcrc&&n.pending>i&&(e.adler=p(e.adler,n.pending_buf,n.pending-i,i)),n.gzindex===n.gzhead.extra.length&&(n.gzindex=0,n.status=73)}else n.status=73;if(73===n.status)if(n.gzhead.name){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(e.adler=p(e.adler,n.pending_buf,n.pending-i,i)),C(e),i=n.pending,n.pending===n.pending_buf_size)){s=1;break}s=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,O(n,s)}while(0!==s);n.gzhead.hcrc&&n.pending>i&&(e.adler=p(e.adler,n.pending_buf,n.pending-i,i)),0===s&&(n.gzindex=0,n.status=91)}else n.status=91;if(91===n.status)if(n.gzhead.comment){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(e.adler=p(e.adler,n.pending_buf,n.pending-i,i)),C(e),i=n.pending,n.pending===n.pending_buf_size)){s=1;break}s=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,O(n,s)}while(0!==s);n.gzhead.hcrc&&n.pending>i&&(e.adler=p(e.adler,n.pending_buf,n.pending-i,i)),0===s&&(n.status=103)}else n.status=103;if(103===n.status&&(n.gzhead.hcrc?(n.pending+2>n.pending_buf_size&&C(e),n.pending+2<=n.pending_buf_size&&(O(n,255&e.adler),O(n,e.adler>>8&255),e.adler=0,n.status=x)):n.status=x),0!==n.pending){if(C(e),0===e.avail_out)return n.last_flush=-1,h}else if(0===e.avail_in&&E(t)<=E(r)&&4!==t)return S(e,-5);if(666===n.status&&0!==e.avail_in)return S(e,-5);if(0!==e.avail_in||0!==n.lookahead||t!==d&&666!==n.status){var a=2===n.strategy?function(e,t){for(var r;;){if(0===e.lookahead&&(B(e),0===e.lookahead)){if(t===d)return 1;break}if(e.match_length=0,r=u._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(A(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(A(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(A(e,!1),0===e.strm.avail_out)?1:2}(n,t):3===n.strategy?function(e,t){for(var r,n,i,s,o=e.window;;){if(e.lookahead<=y){if(B(e),e.lookahead<=y&&t===d)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=g&&0<e.strstart&&(n=o[i=e.strstart-1])===o[++i]&&n===o[++i]&&n===o[++i]){s=e.strstart+y;do{}while(n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&i<s);e.match_length=y-(s-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=g?(r=u._tr_tally(e,1,e.match_length-g),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=u._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(A(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(A(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(A(e,!1),0===e.strm.avail_out)?1:2}(n,t):f[n.level].func(n,t);if(3!==a&&4!==a||(n.status=666),1===a||3===a)return 0===e.avail_out&&(n.last_flush=-1),h;if(2===a&&(1===t?u._tr_align(n):5!==t&&(u._tr_stored_block(n,0,0,!1),3===t&&(z(n.head),0===n.lookahead&&(n.strstart=0,n.block_start=0,n.insert=0))),C(e),0===e.avail_out))return n.last_flush=-1,h}return 4!==t?h:n.wrap<=0?1:(2===n.wrap?(O(n,255&e.adler),O(n,e.adler>>8&255),O(n,e.adler>>16&255),O(n,e.adler>>24&255),O(n,255&e.total_in),O(n,e.total_in>>8&255),O(n,e.total_in>>16&255),O(n,e.total_in>>24&255)):(I(n,e.adler>>>16),I(n,65535&e.adler)),C(e),0<n.wrap&&(n.wrap=-n.wrap),0!==n.pending?h:1)},r.deflateEnd=function(e){var t;return e&&e.state?(t=e.state.status)!==k&&69!==t&&73!==t&&91!==t&&103!==t&&t!==x&&666!==t?S(e,m):(e.state=null,t===x?S(e,-3):h):m},r.deflateSetDictionary=function(e,t){var r,n,i,s,o,a,f,u,d=t.length;if(!e||!e.state)return m;if(2===(s=(r=e.state).wrap)||1===s&&r.status!==k||r.lookahead)return m;for(1===s&&(e.adler=c(e.adler,t,d,0)),r.wrap=0,d>=r.w_size&&(0===s&&(z(r.head),r.strstart=0,r.block_start=0,r.insert=0),u=new l.Buf8(r.w_size),l.arraySet(u,t,d-r.w_size,r.w_size,0),t=u,d=r.w_size),o=e.avail_in,a=e.next_in,f=e.input,e.avail_in=d,e.next_in=0,e.input=t,B(r);r.lookahead>=g;){for(n=r.strstart,i=r.lookahead-(g-1);r.ins_h=(r.ins_h<<r.hash_shift^r.window[n+g-1])&r.hash_mask,r.prev[n&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=n,n++,--i;);r.strstart=n,r.lookahead=g-1,B(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=g-1,r.match_available=0,e.next_in=a,e.input=f,e.avail_in=o,r.wrap=s,h},r.deflateInfo=\"pako deflate (from Nodeca project)\"},{\"../utils/common\":41,\"./adler32\":43,\"./crc32\":45,\"./messages\":51,\"./trees\":52}],47:[function(e,t,r){\"use strict\";t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name=\"\",this.comment=\"\",this.hcrc=0,this.done=!1}},{}],48:[function(e,t,r){\"use strict\";t.exports=function(e,t){var r,n,i,s,o,a,f,u,d,h,l,c,p,m,_,w,v,g,y,b,k,x,S,E,z;r=e.state,n=e.next_in,E=e.input,i=n+(e.avail_in-5),s=e.next_out,z=e.output,o=s-(t-e.avail_out),a=s+(e.avail_out-257),f=r.dmax,u=r.wsize,d=r.whave,h=r.wnext,l=r.window,c=r.hold,p=r.bits,m=r.lencode,_=r.distcode,w=(1<<r.lenbits)-1,v=(1<<r.distbits)-1;e:do{p<15&&(c+=E[n++]<<p,p+=8,c+=E[n++]<<p,p+=8),g=m[c&w];t:for(;;){if(c>>>=y=g>>>24,p-=y,0==(y=g>>>16&255))z[s++]=65535&g;else{if(!(16&y)){if(0==(64&y)){g=m[(65535&g)+(c&(1<<y)-1)];continue t}if(32&y){r.mode=12;break e}e.msg=\"invalid literal/length code\",r.mode=30;break e}b=65535&g,(y&=15)&&(p<y&&(c+=E[n++]<<p,p+=8),b+=c&(1<<y)-1,c>>>=y,p-=y),p<15&&(c+=E[n++]<<p,p+=8,c+=E[n++]<<p,p+=8),g=_[c&v];r:for(;;){if(c>>>=y=g>>>24,p-=y,!(16&(y=g>>>16&255))){if(0==(64&y)){g=_[(65535&g)+(c&(1<<y)-1)];continue r}e.msg=\"invalid distance code\",r.mode=30;break e}if(k=65535&g,p<(y&=15)&&(c+=E[n++]<<p,(p+=8)<y&&(c+=E[n++]<<p,p+=8)),f<(k+=c&(1<<y)-1)){e.msg=\"invalid distance too far back\",r.mode=30;break e}if(c>>>=y,p-=y,(y=s-o)<k){if(d<(y=k-y)&&r.sane){e.msg=\"invalid distance too far back\",r.mode=30;break e}if(S=l,(x=0)===h){if(x+=u-y,y<b){for(b-=y;z[s++]=l[x++],--y;);x=s-k,S=z}}else if(h<y){if(x+=u+h-y,(y-=h)<b){for(b-=y;z[s++]=l[x++],--y;);if(x=0,h<b){for(b-=y=h;z[s++]=l[x++],--y;);x=s-k,S=z}}}else if(x+=h-y,y<b){for(b-=y;z[s++]=l[x++],--y;);x=s-k,S=z}for(;2<b;)z[s++]=S[x++],z[s++]=S[x++],z[s++]=S[x++],b-=3;b&&(z[s++]=S[x++],1<b&&(z[s++]=S[x++]))}else{for(x=s-k;z[s++]=z[x++],z[s++]=z[x++],z[s++]=z[x++],2<(b-=3););b&&(z[s++]=z[x++],1<b&&(z[s++]=z[x++]))}break}}break}}while(n<i&&s<a);n-=b=p>>3,c&=(1<<(p-=b<<3))-1,e.next_in=n,e.next_out=s,e.avail_in=n<i?i-n+5:5-(n-i),e.avail_out=s<a?a-s+257:257-(s-a),r.hold=c,r.bits=p}},{}],49:[function(e,t,r){\"use strict\";var O=e(\"../utils/common\"),I=e(\"./adler32\"),D=e(\"./crc32\"),B=e(\"./inffast\"),T=e(\"./inftrees\"),R=1,F=2,N=0,U=-2,L=1,n=852,i=592;function P(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function s(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new O.Buf16(320),this.work=new O.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function o(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg=\"\",t.wrap&&(e.adler=1&t.wrap),t.mode=L,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new O.Buf32(n),t.distcode=t.distdyn=new O.Buf32(i),t.sane=1,t.back=-1,N):U}function a(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,o(e)):U}function f(e,t){var r,n;return e&&e.state?(n=e.state,t<0?(r=0,t=-t):(r=1+(t>>4),t<48&&(t&=15)),t&&(t<8||15<t)?U:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,a(e))):U}function u(e,t){var r,n;return e?(n=new s,(e.state=n).window=null,(r=f(e,t))!==N&&(e.state=null),r):U}var d,h,l=!0;function j(e){if(l){var t;for(d=new O.Buf32(512),h=new O.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(T(R,e.lens,0,288,d,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;T(F,e.lens,0,32,h,0,e.work,{bits:5}),l=!1}e.lencode=d,e.lenbits=9,e.distcode=h,e.distbits=5}function Z(e,t,r,n){var i,s=e.state;return null===s.window&&(s.wsize=1<<s.wbits,s.wnext=0,s.whave=0,s.window=new O.Buf8(s.wsize)),n>=s.wsize?(O.arraySet(s.window,t,r-s.wsize,s.wsize,0),s.wnext=0,s.whave=s.wsize):(n<(i=s.wsize-s.wnext)&&(i=n),O.arraySet(s.window,t,r-n,i,s.wnext),(n-=i)?(O.arraySet(s.window,t,r-n,n,0),s.wnext=n,s.whave=s.wsize):(s.wnext+=i,s.wnext===s.wsize&&(s.wnext=0),s.whave<s.wsize&&(s.whave+=i))),0}r.inflateReset=a,r.inflateReset2=f,r.inflateResetKeep=o,r.inflateInit=function(e){return u(e,15)},r.inflateInit2=u,r.inflate=function(e,t){var r,n,i,s,o,a,f,u,d,h,l,c,p,m,_,w,v,g,y,b,k,x,S,E,z=0,C=new O.Buf8(4),A=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return U;12===(r=e.state).mode&&(r.mode=13),o=e.next_out,i=e.output,f=e.avail_out,s=e.next_in,n=e.input,a=e.avail_in,u=r.hold,d=r.bits,h=a,l=f,x=N;e:for(;;)switch(r.mode){case L:if(0===r.wrap){r.mode=13;break}for(;d<16;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}if(2&r.wrap&&35615===u){C[r.check=0]=255&u,C[1]=u>>>8&255,r.check=D(r.check,C,2,0),d=u=0,r.mode=2;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&u)<<8)+(u>>8))%31){e.msg=\"incorrect header check\",r.mode=30;break}if(8!=(15&u)){e.msg=\"unknown compression method\",r.mode=30;break}if(d-=4,k=8+(15&(u>>>=4)),0===r.wbits)r.wbits=k;else if(k>r.wbits){e.msg=\"invalid window size\",r.mode=30;break}r.dmax=1<<k,e.adler=r.check=1,r.mode=512&u?10:12,d=u=0;break;case 2:for(;d<16;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}if(r.flags=u,8!=(255&r.flags)){e.msg=\"unknown compression method\",r.mode=30;break}if(57344&r.flags){e.msg=\"unknown header flags set\",r.mode=30;break}r.head&&(r.head.text=u>>8&1),512&r.flags&&(C[0]=255&u,C[1]=u>>>8&255,r.check=D(r.check,C,2,0)),d=u=0,r.mode=3;case 3:for(;d<32;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}r.head&&(r.head.time=u),512&r.flags&&(C[0]=255&u,C[1]=u>>>8&255,C[2]=u>>>16&255,C[3]=u>>>24&255,r.check=D(r.check,C,4,0)),d=u=0,r.mode=4;case 4:for(;d<16;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}r.head&&(r.head.xflags=255&u,r.head.os=u>>8),512&r.flags&&(C[0]=255&u,C[1]=u>>>8&255,r.check=D(r.check,C,2,0)),d=u=0,r.mode=5;case 5:if(1024&r.flags){for(;d<16;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}r.length=u,r.head&&(r.head.extra_len=u),512&r.flags&&(C[0]=255&u,C[1]=u>>>8&255,r.check=D(r.check,C,2,0)),d=u=0}else r.head&&(r.head.extra=null);r.mode=6;case 6:if(1024&r.flags&&(a<(c=r.length)&&(c=a),c&&(r.head&&(k=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),O.arraySet(r.head.extra,n,s,c,k)),512&r.flags&&(r.check=D(r.check,n,c,s)),a-=c,s+=c,r.length-=c),r.length))break e;r.length=0,r.mode=7;case 7:if(2048&r.flags){if(0===a)break e;for(c=0;k=n[s+c++],r.head&&k&&r.length<65536&&(r.head.name+=String.fromCharCode(k)),k&&c<a;);if(512&r.flags&&(r.check=D(r.check,n,c,s)),a-=c,s+=c,k)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=8;case 8:if(4096&r.flags){if(0===a)break e;for(c=0;k=n[s+c++],r.head&&k&&r.length<65536&&(r.head.comment+=String.fromCharCode(k)),k&&c<a;);if(512&r.flags&&(r.check=D(r.check,n,c,s)),a-=c,s+=c,k)break e}else r.head&&(r.head.comment=null);r.mode=9;case 9:if(512&r.flags){for(;d<16;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}if(u!==(65535&r.check)){e.msg=\"header crc mismatch\",r.mode=30;break}d=u=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=12;break;case 10:for(;d<32;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}e.adler=r.check=P(u),d=u=0,r.mode=11;case 11:if(0===r.havedict)return e.next_out=o,e.avail_out=f,e.next_in=s,e.avail_in=a,r.hold=u,r.bits=d,2;e.adler=r.check=1,r.mode=12;case 12:if(5===t||6===t)break e;case 13:if(r.last){u>>>=7&d,d-=7&d,r.mode=27;break}for(;d<3;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}switch(r.last=1&u,d-=1,3&(u>>>=1)){case 0:r.mode=14;break;case 1:if(j(r),r.mode=20,6!==t)break;u>>>=2,d-=2;break e;case 2:r.mode=17;break;case 3:e.msg=\"invalid block type\",r.mode=30}u>>>=2,d-=2;break;case 14:for(u>>>=7&d,d-=7&d;d<32;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}if((65535&u)!=(u>>>16^65535)){e.msg=\"invalid stored block lengths\",r.mode=30;break}if(r.length=65535&u,d=u=0,r.mode=15,6===t)break e;case 15:r.mode=16;case 16:if(c=r.length){if(a<c&&(c=a),f<c&&(c=f),0===c)break e;O.arraySet(i,n,s,c,o),a-=c,s+=c,f-=c,o+=c,r.length-=c;break}r.mode=12;break;case 17:for(;d<14;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}if(r.nlen=257+(31&u),u>>>=5,d-=5,r.ndist=1+(31&u),u>>>=5,d-=5,r.ncode=4+(15&u),u>>>=4,d-=4,286<r.nlen||30<r.ndist){e.msg=\"too many length or distance symbols\",r.mode=30;break}r.have=0,r.mode=18;case 18:for(;r.have<r.ncode;){for(;d<3;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}r.lens[A[r.have++]]=7&u,u>>>=3,d-=3}for(;r.have<19;)r.lens[A[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,S={bits:r.lenbits},x=T(0,r.lens,0,19,r.lencode,0,r.work,S),r.lenbits=S.bits,x){e.msg=\"invalid code lengths set\",r.mode=30;break}r.have=0,r.mode=19;case 19:for(;r.have<r.nlen+r.ndist;){for(;w=(z=r.lencode[u&(1<<r.lenbits)-1])>>>16&255,v=65535&z,!((_=z>>>24)<=d);){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}if(v<16)u>>>=_,d-=_,r.lens[r.have++]=v;else{if(16===v){for(E=_+2;d<E;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}if(u>>>=_,d-=_,0===r.have){e.msg=\"invalid bit length repeat\",r.mode=30;break}k=r.lens[r.have-1],c=3+(3&u),u>>>=2,d-=2}else if(17===v){for(E=_+3;d<E;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}d-=_,k=0,c=3+(7&(u>>>=_)),u>>>=3,d-=3}else{for(E=_+7;d<E;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}d-=_,k=0,c=11+(127&(u>>>=_)),u>>>=7,d-=7}if(r.have+c>r.nlen+r.ndist){e.msg=\"invalid bit length repeat\",r.mode=30;break}for(;c--;)r.lens[r.have++]=k}}if(30===r.mode)break;if(0===r.lens[256]){e.msg=\"invalid code -- missing end-of-block\",r.mode=30;break}if(r.lenbits=9,S={bits:r.lenbits},x=T(R,r.lens,0,r.nlen,r.lencode,0,r.work,S),r.lenbits=S.bits,x){e.msg=\"invalid literal/lengths set\",r.mode=30;break}if(r.distbits=6,r.distcode=r.distdyn,S={bits:r.distbits},x=T(F,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,S),r.distbits=S.bits,x){e.msg=\"invalid distances set\",r.mode=30;break}if(r.mode=20,6===t)break e;case 20:r.mode=21;case 21:if(6<=a&&258<=f){e.next_out=o,e.avail_out=f,e.next_in=s,e.avail_in=a,r.hold=u,r.bits=d,B(e,l),o=e.next_out,i=e.output,f=e.avail_out,s=e.next_in,n=e.input,a=e.avail_in,u=r.hold,d=r.bits,12===r.mode&&(r.back=-1);break}for(r.back=0;w=(z=r.lencode[u&(1<<r.lenbits)-1])>>>16&255,v=65535&z,!((_=z>>>24)<=d);){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}if(w&&0==(240&w)){for(g=_,y=w,b=v;w=(z=r.lencode[b+((u&(1<<g+y)-1)>>g)])>>>16&255,v=65535&z,!(g+(_=z>>>24)<=d);){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}u>>>=g,d-=g,r.back+=g}if(u>>>=_,d-=_,r.back+=_,r.length=v,0===w){r.mode=26;break}if(32&w){r.back=-1,r.mode=12;break}if(64&w){e.msg=\"invalid literal/length code\",r.mode=30;break}r.extra=15&w,r.mode=22;case 22:if(r.extra){for(E=r.extra;d<E;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}r.length+=u&(1<<r.extra)-1,u>>>=r.extra,d-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=23;case 23:for(;w=(z=r.distcode[u&(1<<r.distbits)-1])>>>16&255,v=65535&z,!((_=z>>>24)<=d);){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}if(0==(240&w)){for(g=_,y=w,b=v;w=(z=r.distcode[b+((u&(1<<g+y)-1)>>g)])>>>16&255,v=65535&z,!(g+(_=z>>>24)<=d);){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}u>>>=g,d-=g,r.back+=g}if(u>>>=_,d-=_,r.back+=_,64&w){e.msg=\"invalid distance code\",r.mode=30;break}r.offset=v,r.extra=15&w,r.mode=24;case 24:if(r.extra){for(E=r.extra;d<E;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}r.offset+=u&(1<<r.extra)-1,u>>>=r.extra,d-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg=\"invalid distance too far back\",r.mode=30;break}r.mode=25;case 25:if(0===f)break e;if(c=l-f,r.offset>c){if((c=r.offset-c)>r.whave&&r.sane){e.msg=\"invalid distance too far back\",r.mode=30;break}p=c>r.wnext?(c-=r.wnext,r.wsize-c):r.wnext-c,c>r.length&&(c=r.length),m=r.window}else m=i,p=o-r.offset,c=r.length;for(f<c&&(c=f),f-=c,r.length-=c;i[o++]=m[p++],--c;);0===r.length&&(r.mode=21);break;case 26:if(0===f)break e;i[o++]=r.length,f--,r.mode=21;break;case 27:if(r.wrap){for(;d<32;){if(0===a)break e;a--,u|=n[s++]<<d,d+=8}if(l-=f,e.total_out+=l,r.total+=l,l&&(e.adler=r.check=r.flags?D(r.check,i,l,o-l):I(r.check,i,l,o-l)),l=f,(r.flags?u:P(u))!==r.check){e.msg=\"incorrect data check\",r.mode=30;break}d=u=0}r.mode=28;case 28:if(r.wrap&&r.flags){for(;d<32;){if(0===a)break e;a--,u+=n[s++]<<d,d+=8}if(u!==(4294967295&r.total)){e.msg=\"incorrect length check\",r.mode=30;break}d=u=0}r.mode=29;case 29:x=1;break e;case 30:x=-3;break e;case 31:return-4;case 32:default:return U}return e.next_out=o,e.avail_out=f,e.next_in=s,e.avail_in=a,r.hold=u,r.bits=d,(r.wsize||l!==e.avail_out&&r.mode<30&&(r.mode<27||4!==t))&&Z(e,e.output,e.next_out,l-e.avail_out)?(r.mode=31,-4):(h-=e.avail_in,l-=e.avail_out,e.total_in+=h,e.total_out+=l,r.total+=l,r.wrap&&l&&(e.adler=r.check=r.flags?D(r.check,i,l,e.next_out-l):I(r.check,i,l,e.next_out-l)),e.data_type=r.bits+(r.last?64:0)+(12===r.mode?128:0)+(20===r.mode||15===r.mode?256:0),(0==h&&0===l||4===t)&&x===N&&(x=-5),x)},r.inflateEnd=function(e){if(!e||!e.state)return U;var t=e.state;return t.window&&(t.window=null),e.state=null,N},r.inflateGetHeader=function(e,t){var r;return e&&e.state?0==(2&(r=e.state).wrap)?U:((r.head=t).done=!1,N):U},r.inflateSetDictionary=function(e,t){var r,n=t.length;return e&&e.state?0!==(r=e.state).wrap&&11!==r.mode?U:11===r.mode&&I(1,t,n,0)!==r.check?-3:Z(e,t,n,n)?(r.mode=31,-4):(r.havedict=1,N):U},r.inflateInfo=\"pako inflate (from Nodeca project)\"},{\"../utils/common\":41,\"./adler32\":43,\"./crc32\":45,\"./inffast\":48,\"./inftrees\":50}],50:[function(e,t,r){\"use strict\";var R=e(\"../utils/common\"),F=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],N=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],U=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],L=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(e,t,r,n,i,s,o,a){var f,u,d,h,l,c,p,m,_,w=a.bits,v=0,g=0,y=0,b=0,k=0,x=0,S=0,E=0,z=0,C=0,A=null,O=0,I=new R.Buf16(16),D=new R.Buf16(16),B=null,T=0;for(v=0;v<=15;v++)I[v]=0;for(g=0;g<n;g++)I[t[r+g]]++;for(k=w,b=15;1<=b&&0===I[b];b--);if(b<k&&(k=b),0===b)return i[s++]=20971520,i[s++]=20971520,a.bits=1,0;for(y=1;y<b&&0===I[y];y++);for(k<y&&(k=y),v=E=1;v<=15;v++)if(E<<=1,(E-=I[v])<0)return-1;if(0<E&&(0===e||1!==b))return-1;for(D[1]=0,v=1;v<15;v++)D[v+1]=D[v]+I[v];for(g=0;g<n;g++)0!==t[r+g]&&(o[D[t[r+g]]++]=g);if(c=0===e?(A=B=o,19):1===e?(A=F,O-=257,B=N,T-=257,256):(A=U,B=L,-1),v=y,l=s,S=g=C=0,d=-1,h=(z=1<<(x=k))-1,1===e&&852<z||2===e&&592<z)return 1;for(;;){for(p=v-S,_=o[g]<c?(m=0,o[g]):o[g]>c?(m=B[T+o[g]],A[O+o[g]]):(m=96,0),f=1<<v-S,y=u=1<<x;i[l+(C>>S)+(u-=f)]=p<<24|m<<16|_|0,0!==u;);for(f=1<<v-1;C&f;)f>>=1;if(0!==f?(C&=f-1,C+=f):C=0,g++,0==--I[v]){if(v===b)break;v=t[r+o[g]]}if(k<v&&(C&h)!==d){for(0===S&&(S=k),l+=y,E=1<<(x=v-S);x+S<b&&!((E-=I[x+S])<=0);)x++,E<<=1;if(z+=1<<x,1===e&&852<z||2===e&&592<z)return 1;i[d=C&h]=k<<24|x<<16|l-s|0}}return 0!==C&&(i[l+C]=v-S<<24|64<<16|0),a.bits=k,0}},{\"../utils/common\":41}],51:[function(e,t,r){\"use strict\";t.exports={2:\"need dictionary\",1:\"stream end\",0:\"\",\"-1\":\"file error\",\"-2\":\"stream error\",\"-3\":\"data error\",\"-4\":\"insufficient memory\",\"-5\":\"buffer error\",\"-6\":\"incompatible version\"}},{}],52:[function(e,t,r){\"use strict\";var a=e(\"../utils/common\");function n(e){for(var t=e.length;0<=--t;)e[t]=0}var _=15,i=16,f=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],u=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],d=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],h=new Array(576);n(h);var l=new Array(60);n(l);var c=new Array(512);n(c);var p=new Array(256);n(p);var m=new Array(29);n(m);var w,v,g,y=new Array(30);function b(e,t,r,n,i){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=e&&e.length}function s(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function k(e){return e<256?c[e]:c[256+(e>>>7)]}function x(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function S(e,t,r){e.bi_valid>i-r?(e.bi_buf|=t<<e.bi_valid&65535,x(e,e.bi_buf),e.bi_buf=t>>i-e.bi_valid,e.bi_valid+=r-i):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function E(e,t,r){S(e,r[2*t],r[2*t+1])}function z(e,t){for(var r=0;r|=1&e,e>>>=1,r<<=1,0<--t;);return r>>>1}function C(e,t,r){var n,i,s=new Array(_+1),o=0;for(n=1;n<=_;n++)s[n]=o=o+r[n-1]<<1;for(i=0;i<=t;i++){var a=e[2*i+1];0!==a&&(e[2*i]=z(s[a]++,a))}}function A(e){var t;for(t=0;t<286;t++)e.dyn_ltree[2*t]=0;for(t=0;t<30;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function O(e){8<e.bi_valid?x(e,e.bi_buf):0<e.bi_valid&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function I(e,t,r,n){var i=2*t,s=2*r;return e[i]<e[s]||e[i]===e[s]&&n[t]<=n[r]}function D(e,t,r){for(var n=e.heap[r],i=r<<1;i<=e.heap_len&&(i<e.heap_len&&I(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!I(t,n,e.heap[i],e.depth));)e.heap[r]=e.heap[i],r=i,i<<=1;e.heap[r]=n}function B(e,t,r){var n,i,s,o,a=0;if(0!==e.last_lit)for(;n=e.pending_buf[e.d_buf+2*a]<<8|e.pending_buf[e.d_buf+2*a+1],i=e.pending_buf[e.l_buf+a],a++,0===n?E(e,i,t):(E(e,(s=p[i])+256+1,t),0!==(o=f[s])&&S(e,i-=m[s],o),E(e,s=k(--n),r),0!==(o=u[s])&&S(e,n-=y[s],o)),a<e.last_lit;);E(e,256,t)}function T(e,t){var r,n,i,s=t.dyn_tree,o=t.stat_desc.static_tree,a=t.stat_desc.has_stree,f=t.stat_desc.elems,u=-1;for(e.heap_len=0,e.heap_max=573,r=0;r<f;r++)0!==s[2*r]?(e.heap[++e.heap_len]=u=r,e.depth[r]=0):s[2*r+1]=0;for(;e.heap_len<2;)s[2*(i=e.heap[++e.heap_len]=u<2?++u:0)]=1,e.depth[i]=0,e.opt_len--,a&&(e.static_len-=o[2*i+1]);for(t.max_code=u,r=e.heap_len>>1;1<=r;r--)D(e,s,r);for(i=f;r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],D(e,s,1),n=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=n,s[2*i]=s[2*r]+s[2*n],e.depth[i]=(e.depth[r]>=e.depth[n]?e.depth[r]:e.depth[n])+1,s[2*r+1]=s[2*n+1]=i,e.heap[1]=i++,D(e,s,1),2<=e.heap_len;);e.heap[--e.heap_max]=e.heap[1],function(e,t){var r,n,i,s,o,a,f=t.dyn_tree,u=t.max_code,d=t.stat_desc.static_tree,h=t.stat_desc.has_stree,l=t.stat_desc.extra_bits,c=t.stat_desc.extra_base,p=t.stat_desc.max_length,m=0;for(s=0;s<=_;s++)e.bl_count[s]=0;for(f[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<573;r++)p<(s=f[2*f[2*(n=e.heap[r])+1]+1]+1)&&(s=p,m++),f[2*n+1]=s,u<n||(e.bl_count[s]++,o=0,c<=n&&(o=l[n-c]),a=f[2*n],e.opt_len+=a*(s+o),h&&(e.static_len+=a*(d[2*n+1]+o)));if(0!==m){do{for(s=p-1;0===e.bl_count[s];)s--;e.bl_count[s]--,e.bl_count[s+1]+=2,e.bl_count[p]--,m-=2}while(0<m);for(s=p;0!==s;s--)for(n=e.bl_count[s];0!==n;)u<(i=e.heap[--r])||(f[2*i+1]!==s&&(e.opt_len+=(s-f[2*i+1])*f[2*i],f[2*i+1]=s),n--)}}(e,t),C(s,u,e.bl_count)}function R(e,t,r){var n,i,s=-1,o=t[1],a=0,f=7,u=4;for(0===o&&(f=138,u=3),t[2*(r+1)+1]=65535,n=0;n<=r;n++)i=o,o=t[2*(n+1)+1],++a<f&&i===o||(a<u?e.bl_tree[2*i]+=a:0!==i?(i!==s&&e.bl_tree[2*i]++,e.bl_tree[32]++):a<=10?e.bl_tree[34]++:e.bl_tree[36]++,s=i,u=(a=0)===o?(f=138,3):i===o?(f=6,3):(f=7,4))}function F(e,t,r){var n,i,s=-1,o=t[1],a=0,f=7,u=4;for(0===o&&(f=138,u=3),n=0;n<=r;n++)if(i=o,o=t[2*(n+1)+1],!(++a<f&&i===o)){if(a<u)for(;E(e,i,e.bl_tree),0!=--a;);else 0!==i?(i!==s&&(E(e,i,e.bl_tree),a--),E(e,16,e.bl_tree),S(e,a-3,2)):a<=10?(E(e,17,e.bl_tree),S(e,a-3,3)):(E(e,18,e.bl_tree),S(e,a-11,7));s=i,u=(a=0)===o?(f=138,3):i===o?(f=6,3):(f=7,4)}}n(y);var N=!1;function U(e,t,r,n){var i,s,o;S(e,0+(n?1:0),3),s=t,o=r,O(i=e),x(i,o),x(i,~o),a.arraySet(i.pending_buf,i.window,s,o,i.pending),i.pending+=o}r._tr_init=function(e){N||(function(){var e,t,r,n,i,s=new Array(_+1);for(n=r=0;n<28;n++)for(m[n]=r,e=0;e<1<<f[n];e++)p[r++]=n;for(p[r-1]=n,n=i=0;n<16;n++)for(y[n]=i,e=0;e<1<<u[n];e++)c[i++]=n;for(i>>=7;n<30;n++)for(y[n]=i<<7,e=0;e<1<<u[n]-7;e++)c[256+i++]=n;for(t=0;t<=_;t++)s[t]=0;for(e=0;e<=143;)h[2*e+1]=8,e++,s[8]++;for(;e<=255;)h[2*e+1]=9,e++,s[9]++;for(;e<=279;)h[2*e+1]=7,e++,s[7]++;for(;e<=287;)h[2*e+1]=8,e++,s[8]++;for(C(h,287,s),e=0;e<30;e++)l[2*e+1]=5,l[2*e]=z(e,5);w=new b(h,f,257,286,_),v=new b(l,u,0,30,_),g=new b(new Array(0),o,0,19,7)}(),N=!0),e.l_desc=new s(e.dyn_ltree,w),e.d_desc=new s(e.dyn_dtree,v),e.bl_desc=new s(e.bl_tree,g),e.bi_buf=0,e.bi_valid=0,A(e)},r._tr_stored_block=U,r._tr_flush_block=function(e,t,r,n){var i,s,o=0;0<e.level?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,r=4093624447;for(t=0;t<=31;t++,r>>>=1)if(1&r&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<256;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0}(e)),T(e,e.l_desc),T(e,e.d_desc),o=function(e){var t;for(R(e,e.dyn_ltree,e.l_desc.max_code),R(e,e.dyn_dtree,e.d_desc.max_code),T(e,e.bl_desc),t=18;3<=t&&0===e.bl_tree[2*d[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),i=e.opt_len+3+7>>>3,(s=e.static_len+3+7>>>3)<=i&&(i=s)):i=s=r+5,r+4<=i&&-1!==t?U(e,t,r,n):4===e.strategy||s===i?(S(e,2+(n?1:0),3),B(e,h,l)):(S(e,4+(n?1:0),3),function(e,t,r,n){var i;for(S(e,t-257,5),S(e,r-1,5),S(e,n-4,4),i=0;i<n;i++)S(e,e.bl_tree[2*d[i]+1],3);F(e,e.dyn_ltree,t-1),F(e,e.dyn_dtree,r-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,o+1),B(e,e.dyn_ltree,e.dyn_dtree)),A(e),n&&O(e)},r._tr_tally=function(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(p[r]+256+1)]++,e.dyn_dtree[2*k(t)]++),e.last_lit===e.lit_bufsize-1},r._tr_align=function(e){var t;S(e,2,3),E(e,256,h),16===(t=e).bi_valid?(x(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):8<=t.bi_valid&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}},{\"../utils/common\":41}],53:[function(e,t,r){\"use strict\";t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg=\"\",this.state=null,this.data_type=2,this.adler=0}},{}],54:[function(e,t,r){\"use strict\";t.exports=\"function\"==typeof setImmediate?setImmediate:function(){var e=[].slice.apply(arguments);e.splice(1,0,0),setTimeout.apply(null,e)}},{}]},{},[10])(10)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,void 0!==r?r:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)})}).call(this,\"undefined\"!=typeof global?global:\"undefined\"!=typeof self?self:\"undefined\"!=typeof window?window:{})},{}]},{},[1])(1)});", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <http://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict'\n\nvar base64 = require('base64-js')\nvar ieee754 = require('ieee754')\nvar isArray = require('isarray')\n\nexports.Buffer = Buffer\nexports.SlowBuffer = SlowBuffer\nexports.INSPECT_MAX_BYTES = 50\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - <PERSON><PERSON><PERSON> has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\nBuffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined\n  ? global.TYPED_ARRAY_SUPPORT\n  : typedArraySupport()\n\n/*\n * Export kMaxLength after typed array support is determined.\n */\nexports.kMaxLength = kMaxLength()\n\nfunction typedArraySupport () {\n  try {\n    var arr = new Uint8Array(1)\n    arr.__proto__ = {__proto__: Uint8Array.prototype, foo: function () { return 42 }}\n    return arr.foo() === 42 && // typed array instances can be augmented\n        typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`\n        arr.subarray(1, 1).byteLength === 0 // ie10 has broken `subarray`\n  } catch (e) {\n    return false\n  }\n}\n\nfunction kMaxLength () {\n  return Buffer.TYPED_ARRAY_SUPPORT\n    ? 0x7fffffff\n    : 0x3fffffff\n}\n\nfunction createBuffer (that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length')\n  }\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length)\n    that.__proto__ = Buffer.prototype\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length)\n    }\n    that.length = length\n  }\n\n  return that\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length)\n  }\n\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error(\n        'If encoding is specified then the first argument must be a string'\n      )\n    }\n    return allocUnsafe(this, arg)\n  }\n  return from(this, arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192 // not used by this implementation\n\n// TODO: Legacy, not needed anymore. Remove in next major version.\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype\n  return arr\n}\n\nfunction from (that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number')\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset)\n  }\n\n  return fromObject(that, value)\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length)\n}\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype\n  Buffer.__proto__ = Uint8Array\n  if (typeof Symbol !== 'undefined' && Symbol.species &&\n      Buffer[Symbol.species] === Buffer) {\n    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true\n    })\n  }\n}\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number')\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative')\n  }\n}\n\nfunction alloc (that, size, fill, encoding) {\n  assertSize(size)\n  if (size <= 0) {\n    return createBuffer(that, size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(that, size).fill(fill, encoding)\n      : createBuffer(that, size).fill(fill)\n  }\n  return createBuffer(that, size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding)\n}\n\nfunction allocUnsafe (that, size) {\n  assertSize(size)\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0\n    }\n  }\n  return that\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size)\n}\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size)\n}\n\nfunction fromString (that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding')\n  }\n\n  var length = byteLength(string, encoding) | 0\n  that = createBuffer(that, length)\n\n  var actual = that.write(string, encoding)\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual)\n  }\n\n  return that\n}\n\nfunction fromArrayLike (that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0\n  that = createBuffer(that, length)\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255\n  }\n  return that\n}\n\nfunction fromArrayBuffer (that, array, byteOffset, length) {\n  array.byteLength // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds')\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array)\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset)\n  } else {\n    array = new Uint8Array(array, byteOffset, length)\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array\n    that.__proto__ = Buffer.prototype\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array)\n  }\n  return that\n}\n\nfunction fromObject (that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0\n    that = createBuffer(that, len)\n\n    if (that.length === 0) {\n      return that\n    }\n\n    obj.copy(that, 0, 0, len)\n    return that\n  }\n\n  if (obj) {\n    if ((typeof ArrayBuffer !== 'undefined' &&\n        obj.buffer instanceof ArrayBuffer) || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0)\n      }\n      return fromArrayLike(that, obj)\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data)\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.')\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + kMaxLength().toString(16) + ' bytes')\n  }\n  return length | 0\n}\n\nfunction SlowBuffer (length) {\n  if (+length != length) { // eslint-disable-line eqeqeq\n    length = 0\n  }\n  return Buffer.alloc(+length)\n}\n\nBuffer.isBuffer = function isBuffer (b) {\n  return !!(b != null && b._isBuffer)\n}\n\nBuffer.compare = function compare (a, b) {\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers')\n  }\n\n  if (a === b) return 0\n\n  var x = a.length\n  var y = b.length\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i]\n      y = b[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n}\n\nBuffer.concat = function concat (list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  var i\n  if (length === undefined) {\n    length = 0\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length)\n  var pos = 0\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i]\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    }\n    buf.copy(buffer, pos)\n    pos += buf.length\n  }\n  return buffer\n}\n\nfunction byteLength (string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length\n  }\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' &&\n      (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    string = '' + string\n  }\n\n  var len = string.length\n  if (len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  var loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) return utf8ToBytes(string).length // assume utf8\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\nBuffer.byteLength = byteLength\n\nfunction slowToString (encoding, start, end) {\n  var loweredCase = false\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0\n  start >>>= 0\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\n// The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\nBuffer.prototype._isBuffer = true\n\nfunction swap (b, n, m) {\n  var i = b[n]\n  b[n] = b[m]\n  b[m] = i\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  var len = this.length\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1)\n  }\n  return this\n}\n\nBuffer.prototype.swap32 = function swap32 () {\n  var len = this.length\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3)\n    swap(this, i + 1, i + 2)\n  }\n  return this\n}\n\nBuffer.prototype.swap64 = function swap64 () {\n  var len = this.length\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7)\n    swap(this, i + 1, i + 6)\n    swap(this, i + 2, i + 5)\n    swap(this, i + 3, i + 4)\n  }\n  return this\n}\n\nBuffer.prototype.toString = function toString () {\n  var length = this.length | 0\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n}\n\nBuffer.prototype.equals = function equals (b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n}\n\nBuffer.prototype.inspect = function inspect () {\n  var str = ''\n  var max = exports.INSPECT_MAX_BYTES\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ')\n    if (this.length > max) str += ' ... '\n  }\n  return '<Buffer ' + str + '>'\n}\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer')\n  }\n\n  if (start === undefined) {\n    start = 0\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0\n  }\n  if (thisStart === undefined) {\n    thisStart = 0\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0\n  end >>>= 0\n  thisStart >>>= 0\n  thisEnd >>>= 0\n\n  if (this === target) return 0\n\n  var x = thisEnd - thisStart\n  var y = end - start\n  var len = Math.min(x, y)\n\n  var thisCopy = this.slice(thisStart, thisEnd)\n  var targetCopy = target.slice(start, end)\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i]\n      y = targetCopy[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset\n    byteOffset = 0\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000\n  }\n  byteOffset = +byteOffset  // Coerce to Number.\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1)\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding)\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF // Search for a byte value [0-255]\n    if (Buffer.TYPED_ARRAY_SUPPORT &&\n        typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [ val ], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1\n  var arrLength = arr.length\n  var valLength = val.length\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase()\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2\n      arrLength /= 2\n      valLength /= 2\n      byteOffset /= 2\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  var i\n  if (dir) {\n    var foundIndex = -1\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex\n        foundIndex = -1\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n}\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n}\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n}\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0\n  var remaining = buf.length - offset\n  if (!length) {\n    length = remaining\n  } else {\n    length = Number(length)\n    if (length > remaining) {\n      length = remaining\n    }\n  }\n\n  // must be an even number of digits\n  var strLen = string.length\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string')\n\n  if (length > strLen / 2) {\n    length = strLen / 2\n  }\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16)\n    if (isNaN(parsed)) return i\n    buf[offset + i] = parsed\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction latin1Write (buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8'\n    length = this.length\n    offset = 0\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset\n    length = this.length\n    offset = 0\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0\n    if (isFinite(length)) {\n      length = length | 0\n      if (encoding === undefined) encoding = 'utf8'\n    } else {\n      encoding = length\n      length = undefined\n    }\n  // legacy write(string, encoding, offset, length) - remove in v0.13\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  var remaining = this.length - offset\n  if (length === undefined || length > remaining) length = remaining\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  var loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n}\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf)\n  } else {\n    return base64.fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end)\n  var res = []\n\n  var i = start\n  while (i < end) {\n    var firstByte = buf[i]\n    var codePoint = null\n    var bytesPerSequence = (firstByte > 0xEF) ? 4\n      : (firstByte > 0xDF) ? 3\n      : (firstByte > 0xBF) ? 2\n      : 1\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1]\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          fourthByte = buf[i + 3]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD\n      bytesPerSequence = 1\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800)\n      codePoint = 0xDC00 | codePoint & 0x3FF\n    }\n\n    res.push(codePoint)\n    i += bytesPerSequence\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nvar MAX_ARGUMENTS_LENGTH = 0x1000\n\nfunction decodeCodePointsArray (codePoints) {\n  var len = codePoints.length\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  var res = ''\n  var i = 0\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    )\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  var ret = ''\n  end = Math.min(buf.length, end)\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F)\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  var ret = ''\n  end = Math.min(buf.length, end)\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i])\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  var len = buf.length\n\n  if (!start || start < 0) start = 0\n  if (!end || end < 0 || end > len) end = len\n\n  var out = ''\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i])\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  var bytes = buf.slice(start, end)\n  var res = ''\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256)\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  var len = this.length\n  start = ~~start\n  end = end === undefined ? len : ~~end\n\n  if (start < 0) {\n    start += len\n    if (start < 0) start = 0\n  } else if (start > len) {\n    start = len\n  }\n\n  if (end < 0) {\n    end += len\n    if (end < 0) end = 0\n  } else if (end > len) {\n    end = len\n  }\n\n  if (end < start) end = start\n\n  var newBuf\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end)\n    newBuf.__proto__ = Buffer.prototype\n  } else {\n    var sliceLen = end - start\n    newBuf = new Buffer(sliceLen, undefined)\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start]\n    }\n  }\n\n  return newBuf\n}\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var val = this[offset]\n  var mul = 1\n  var i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length)\n  }\n\n  var val = this[offset + --byteLength]\n  var mul = 1\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  return this[offset]\n}\n\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return this[offset] | (this[offset + 1] << 8)\n}\n\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return (this[offset] << 8) | this[offset + 1]\n}\n\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n}\n\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n}\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var val = this[offset]\n  var mul = 1\n  var i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var i = byteLength\n  var mul = 1\n  var val = this[offset + --i]\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n}\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  var val = this[offset] | (this[offset + 1] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  var val = this[offset + 1] | (this[offset] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n}\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n}\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, true, 23, 4)\n}\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, false, 23, 4)\n}\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, true, 52, 8)\n}\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, false, 52, 8)\n}\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  var mul = 1\n  var i = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  var i = byteLength - 1\n  var mul = 1\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nfunction objectWriteUInt16 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & (0xff << (8 * (littleEndian ? i : 1 - i)))) >>>\n      (littleEndian ? i : 1 - i) * 8\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n  } else {\n    objectWriteUInt16(this, value, offset, true)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8)\n    this[offset + 1] = (value & 0xff)\n  } else {\n    objectWriteUInt16(this, value, offset, false)\n  }\n  return offset + 2\n}\n\nfunction objectWriteUInt32 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = (value >>> (littleEndian ? i : 3 - i) * 8) & 0xff\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = (value >>> 24)\n    this[offset + 2] = (value >>> 16)\n    this[offset + 1] = (value >>> 8)\n    this[offset] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, true)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24)\n    this[offset + 1] = (value >>> 16)\n    this[offset + 2] = (value >>> 8)\n    this[offset + 3] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, false)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  var i = 0\n  var mul = 1\n  var sub = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  var i = byteLength - 1\n  var mul = 1\n  var sub = 0\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)\n  if (value < 0) value = 0xff + value + 1\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n  } else {\n    objectWriteUInt16(this, value, offset, true)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8)\n    this[offset + 1] = (value & 0xff)\n  } else {\n    objectWriteUInt16(this, value, offset, false)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n    this[offset + 2] = (value >>> 16)\n    this[offset + 3] = (value >>> 24)\n  } else {\n    objectWriteUInt32(this, value, offset, true)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (value < 0) value = 0xffffffff + value + 1\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24)\n    this[offset + 1] = (value >>> 16)\n    this[offset + 2] = (value >>> 8)\n    this[offset + 3] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, false)\n  }\n  return offset + 4\n}\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4)\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n}\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8)\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n}\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!start) start = 0\n  if (!end && end !== 0) end = this.length\n  if (targetStart >= target.length) targetStart = target.length\n  if (!targetStart) targetStart = 0\n  if (end > 0 && end < start) end = start\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start\n  }\n\n  var len = end - start\n  var i\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start]\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start]\n    }\n  } else {\n    Uint8Array.prototype.set.call(\n      target,\n      this.subarray(start, start + len),\n      targetStart\n    )\n  }\n\n  return len\n}\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start\n      start = 0\n      end = this.length\n    } else if (typeof end === 'string') {\n      encoding = end\n      end = this.length\n    }\n    if (val.length === 1) {\n      var code = val.charCodeAt(0)\n      if (code < 256) {\n        val = code\n      }\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0\n  end = end === undefined ? this.length : end >>> 0\n\n  if (!val) val = 0\n\n  var i\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val)\n      ? val\n      : utf8ToBytes(new Buffer(val, encoding).toString())\n    var len = bytes.length\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len]\n    }\n  }\n\n  return this\n}\n\n// HELPER FUNCTIONS\n// ================\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g\n\nfunction base64clean (str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, '')\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '='\n  }\n  return str\n}\n\nfunction stringtrim (str) {\n  if (str.trim) return str.trim()\n  return str.replace(/^\\s+|\\s+$/g, '')\n}\n\nfunction toHex (n) {\n  if (n < 16) return '0' + n.toString(16)\n  return n.toString(16)\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity\n  var codePoint\n  var length = string.length\n  var leadSurrogate = null\n  var bytes = []\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i)\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n        leadSurrogate = codePoint\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n    }\n\n    leadSurrogate = null\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint)\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  var byteArray = []\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF)\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  var c, hi, lo\n  var byteArray = []\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i)\n    hi = c >> 8\n    lo = c % 256\n    byteArray.push(lo)\n    byteArray.push(hi)\n  }\n\n  return byteArray\n}\n\nfunction base64ToBytes (str) {\n  return base64.toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i]\n  }\n  return i\n}\n\nfunction isnan (val) {\n  return val !== val // eslint-disable-line no-self-compare\n}\n", "'use strict'\n\nexports.byteLength = byteLength\nexports.toByteArray = toByteArray\nexports.fromByteArray = fromByteArray\n\nvar lookup = []\nvar revLookup = []\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i]\n  revLookup[code.charCodeAt(i)] = i\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62\nrevLookup['_'.charCodeAt(0)] = 63\n\nfunction getLens (b64) {\n  var len = b64.length\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf('=')\n  if (validLen === -1) validLen = len\n\n  var placeHoldersLen = validLen === len\n    ? 0\n    : 4 - (validLen % 4)\n\n  return [validLen, placeHoldersLen]\n}\n\n// base64 is 4/3 + up to two characters of the original data\nfunction byteLength (b64) {\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction _byteLength (b64, validLen, placeHoldersLen) {\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction toByteArray (b64) {\n  var tmp\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))\n\n  var curByte = 0\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0\n    ? validLen - 4\n    : validLen\n\n  var i\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)]\n    arr[curByte++] = (tmp >> 16) & 0xFF\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4)\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2)\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] +\n    lookup[num >> 12 & 0x3F] +\n    lookup[num >> 6 & 0x3F] +\n    lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp\n  var output = []\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xFF0000) +\n      ((uint8[i + 1] << 8) & 0xFF00) +\n      (uint8[i + 2] & 0xFF)\n    output.push(tripletToBase64(tmp))\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  var tmp\n  var len = uint8.length\n  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes\n  var parts = []\n  var maxChunkLength = 16383 // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 2] +\n      lookup[(tmp << 4) & 0x3F] +\n      '=='\n    )\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 10] +\n      lookup[(tmp >> 4) & 0x3F] +\n      lookup[(tmp << 2) & 0x3F] +\n      '='\n    )\n  }\n\n  return parts.join('')\n}\n", "/*! ieee754. BSD-3-Clause License. Feross A<PERSON> <https://feross.org/opensource> */\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = ((value * c) - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n", "var toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n", "var scope = (typeof global !== \"undefined\" && global) ||\n            (typeof self !== \"undefined\" && self) ||\n            window;\nvar apply = Function.prototype.apply;\n\n// DOM APIs, for completeness\n\nexports.setTimeout = function() {\n  return new Timeout(apply.call(setTimeout, scope, arguments), clearTimeout);\n};\nexports.setInterval = function() {\n  return new Timeout(apply.call(setInterval, scope, arguments), clearInterval);\n};\nexports.clearTimeout =\nexports.clearInterval = function(timeout) {\n  if (timeout) {\n    timeout.close();\n  }\n};\n\nfunction Timeout(id, clearFn) {\n  this._id = id;\n  this._clearFn = clearFn;\n}\nTimeout.prototype.unref = Timeout.prototype.ref = function() {};\nTimeout.prototype.close = function() {\n  this._clearFn.call(scope, this._id);\n};\n\n// Does not start the time, just sets up the members needed.\nexports.enroll = function(item, msecs) {\n  clearTimeout(item._idleTimeoutId);\n  item._idleTimeout = msecs;\n};\n\nexports.unenroll = function(item) {\n  clearTimeout(item._idleTimeoutId);\n  item._idleTimeout = -1;\n};\n\nexports._unrefActive = exports.active = function(item) {\n  clearTimeout(item._idleTimeoutId);\n\n  var msecs = item._idleTimeout;\n  if (msecs >= 0) {\n    item._idleTimeoutId = setTimeout(function onTimeout() {\n      if (item._onTimeout)\n        item._onTimeout();\n    }, msecs);\n  }\n};\n\n// setimmediate attaches itself to the global object\nrequire(\"setimmediate\");\n// On some exotic environments, it's not clear which object `setimmediate` was\n// able to install onto.  Search each possibility in the same order as the\n// `setimmediate` library.\nexports.setImmediate = (typeof self !== \"undefined\" && self.setImmediate) ||\n                       (typeof global !== \"undefined\" && global.setImmediate) ||\n                       (this && this.setImmediate);\nexports.clearImmediate = (typeof self !== \"undefined\" && self.clearImmediate) ||\n                         (typeof global !== \"undefined\" && global.clearImmediate) ||\n                         (this && this.clearImmediate);\n", "(function (global, undefined) {\n    \"use strict\";\n\n    if (global.setImmediate) {\n        return;\n    }\n\n    var nextHandle = 1; // Spec says greater than zero\n    var tasksByHandle = {};\n    var currentlyRunningATask = false;\n    var doc = global.document;\n    var registerImmediate;\n\n    function setImmediate(callback) {\n      // Callback can either be a function or a string\n      if (typeof callback !== \"function\") {\n        callback = new Function(\"\" + callback);\n      }\n      // Copy function arguments\n      var args = new Array(arguments.length - 1);\n      for (var i = 0; i < args.length; i++) {\n          args[i] = arguments[i + 1];\n      }\n      // Store and register the task\n      var task = { callback: callback, args: args };\n      tasksByHandle[nextHandle] = task;\n      registerImmediate(nextHandle);\n      return nextHandle++;\n    }\n\n    function clearImmediate(handle) {\n        delete tasksByHandle[handle];\n    }\n\n    function run(task) {\n        var callback = task.callback;\n        var args = task.args;\n        switch (args.length) {\n        case 0:\n            callback();\n            break;\n        case 1:\n            callback(args[0]);\n            break;\n        case 2:\n            callback(args[0], args[1]);\n            break;\n        case 3:\n            callback(args[0], args[1], args[2]);\n            break;\n        default:\n            callback.apply(undefined, args);\n            break;\n        }\n    }\n\n    function runIfPresent(handle) {\n        // From the spec: \"Wait until any invocations of this algorithm started before this one have completed.\"\n        // So if we're currently running a task, we'll need to delay this invocation.\n        if (currentlyRunningATask) {\n            // Delay by doing a setTimeout. setImmediate was tried instead, but in Firefox 7 it generated a\n            // \"too much recursion\" error.\n            setTimeout(runIfPresent, 0, handle);\n        } else {\n            var task = tasksByHandle[handle];\n            if (task) {\n                currentlyRunningATask = true;\n                try {\n                    run(task);\n                } finally {\n                    clearImmediate(handle);\n                    currentlyRunningATask = false;\n                }\n            }\n        }\n    }\n\n    function installNextTickImplementation() {\n        registerImmediate = function(handle) {\n            process.nextTick(function () { runIfPresent(handle); });\n        };\n    }\n\n    function canUsePostMessage() {\n        // The test against `importScripts` prevents this implementation from being installed inside a web worker,\n        // where `global.postMessage` means something completely different and can't be used for this purpose.\n        if (global.postMessage && !global.importScripts) {\n            var postMessageIsAsynchronous = true;\n            var oldOnMessage = global.onmessage;\n            global.onmessage = function() {\n                postMessageIsAsynchronous = false;\n            };\n            global.postMessage(\"\", \"*\");\n            global.onmessage = oldOnMessage;\n            return postMessageIsAsynchronous;\n        }\n    }\n\n    function installPostMessageImplementation() {\n        // Installs an event handler on `global` for the `message` event: see\n        // * https://developer.mozilla.org/en/DOM/window.postMessage\n        // * http://www.whatwg.org/specs/web-apps/current-work/multipage/comms.html#crossDocumentMessages\n\n        var messagePrefix = \"setImmediate$\" + Math.random() + \"$\";\n        var onGlobalMessage = function(event) {\n            if (event.source === global &&\n                typeof event.data === \"string\" &&\n                event.data.indexOf(messagePrefix) === 0) {\n                runIfPresent(+event.data.slice(messagePrefix.length));\n            }\n        };\n\n        if (global.addEventListener) {\n            global.addEventListener(\"message\", onGlobalMessage, false);\n        } else {\n            global.attachEvent(\"onmessage\", onGlobalMessage);\n        }\n\n        registerImmediate = function(handle) {\n            global.postMessage(messagePrefix + handle, \"*\");\n        };\n    }\n\n    function installMessageChannelImplementation() {\n        var channel = new MessageChannel();\n        channel.port1.onmessage = function(event) {\n            var handle = event.data;\n            runIfPresent(handle);\n        };\n\n        registerImmediate = function(handle) {\n            channel.port2.postMessage(handle);\n        };\n    }\n\n    function installReadyStateChangeImplementation() {\n        var html = doc.documentElement;\n        registerImmediate = function(handle) {\n            // Create a <script> element; its readystatechange event will be fired asynchronously once it is inserted\n            // into the document. Do so, thus queuing up the task. Remember to clean up once it's been called.\n            var script = doc.createElement(\"script\");\n            script.onreadystatechange = function () {\n                runIfPresent(handle);\n                script.onreadystatechange = null;\n                html.removeChild(script);\n                script = null;\n            };\n            html.appendChild(script);\n        };\n    }\n\n    function installSetTimeoutImplementation() {\n        registerImmediate = function(handle) {\n            setTimeout(runIfPresent, 0, handle);\n        };\n    }\n\n    // If supported, we should attach to the prototype of global, since that is where setTimeout et al. live.\n    var attachTo = Object.getPrototypeOf && Object.getPrototypeOf(global);\n    attachTo = attachTo && attachTo.setTimeout ? attachTo : global;\n\n    // Don't get fooled by e.g. browserify environments.\n    if ({}.toString.call(global.process) === \"[object process]\") {\n        // For Node.js before 0.9\n        installNextTickImplementation();\n\n    } else if (canUsePostMessage()) {\n        // For non-IE10 modern browsers\n        installPostMessageImplementation();\n\n    } else if (global.MessageChannel) {\n        // For web workers, where supported\n        installMessageChannelImplementation();\n\n    } else if (doc && \"onreadystatechange\" in doc.createElement(\"script\")) {\n        // For IE 6–8\n        installReadyStateChangeImplementation();\n\n    } else {\n        // For older browsers\n        installSetTimeoutImplementation();\n    }\n\n    attachTo.setImmediate = setImmediate;\n    attachTo.clearImmediate = clearImmediate;\n}(typeof self === \"undefined\" ? typeof global === \"undefined\" ? this : global : self));\n", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = function() { return []; };\nwebpackEmptyContext.resolve = webpackEmptyContext;\nmodule.exports = webpackEmptyContext;\nwebpackEmptyContext.id = 20;"], "sourceRoot": ""}