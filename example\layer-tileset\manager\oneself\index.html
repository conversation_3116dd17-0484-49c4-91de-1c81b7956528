<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0" />
    <meta name="author" content="火星科技 http://mars3d.cn " />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="x5-fullscreen" content="true" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />

    <!-- 标题及搜索关键字 -->
    <meta name="keywords" content="火星科技,cesium,3D,GIS,marsgis,三维,地球,地图,开发,框架,系统,示例,资料,模型,离线,外包,合肥,安徽,中国" />
    <meta
      name="description"
      content="火星科技 合肥火星 合肥火星科技 合肥火星科技有限公司 leaflet leaflet框架 leaflet开发 cesium cesium开发 cesium框架 三维 地球 模型  gis marsgis 地图离线 地图开发 地图框架 地图外包 框架 开发 外包  地图离线 二维地图 三维地图 全景漫游 地理信息系统 云GIS 三维GIS GIS平台 WebGIS"
    />

    <link rel="shortcut icon" type="image/x-icon" href="/img/favicon/favicon.ico" />
    <title>单模型场景(无地球) | Mars3D | 三维地图 | 火星科技 | 合肥火星科技有限公司</title>

    <!--第三方lib-->
    <script
      type="text/javascript"
      src="/lib/include-lib.js"
      libpath="/lib/"
      include="jquery,layer,toastr,jquery.minicolors,font-awesome,bootstrap,bootstrap-checkbox,layer,haoutil,admui,mars3d,localforage"
    ></script>

    <link href="/css/style.css" rel="stylesheet" />
  </head>

  <body class="dark">


    <div id="mars3dContainer" class="mars3d-container"></div>

    <!-- 面板 -->
    <div class="infoview infoview-left">
      <table class="mars-table">
        <tr>
          <td>模型URL：</td>
          <td>
            <input id="txtModel" type="text" value="https://data.mars3d.cn/3dtiles/qx-simiao/tileset.json" class="form-control" style="width: 500px" />
          </td>
          <td>
            <input type="button" class="btn btn-primary" value="加载模型" onclick="showMd()" />
          </td>
        </tr>
        <tr>
          <td>背景颜色：</td>
          <td>
            <input type="text" id="txtColor" class="form-control" style="width: 100px" value="#363635" />
          </td>
          <td>
            <input type="button" class="btn btn-primary" value="视角复位" onclick="flyTo()" />
          </td>
        </tr>
      </table>
    </div>

    <script src="/js/common.js"></script>
    <script src="./map.js"></script>
    <script type="text/javascript">
      "use script" //开发环境建议开启严格模式
      let url
      function initUI(options) {
        let color = $("#txtColor").val()
        $("#txtColor").minicolors({
          position: "bottom left",
          control: "saturation",
          change: function (color) {
            changeColor(color)

            // map.setSceneOptions({
            //   backgroundColor: color, //天空背景色
            //   globe: {
            //     baseColor: color, //地球地面背景色
            //   },
            // })

            $("body").css("background", color)
          }
        })

        //历史记录模型地址
        localforage.getItem("g20_onlyModel").then(function (lasturl) {
          if (lasturl) {
            $("#txtModel").val(lasturl)
            url = lasturl
          } else {
            url = "https://data.mars3d.cn/3dtiles/qx-simiao/tileset.json"
            $("#txtModel").val(url)
          }
        })

        setTimeout(showMd, 1000)
      }

      //当前页面业务相关
      function showMd() {
        let url = $("#txtModel").val()
        localforage.setItem("g20_onlyModel", url)

        showModel(url)
      }
    </script>
  </body>
</html>
