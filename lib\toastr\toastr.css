/**
 * UI v1.0.0
 * Copyright 2015-2017 Muyao
 * Licensed under the Muyao License 1.0
 */
.toast-title {
	font-weight: 700;
}
.toast-message {
	-ms-word-wrap: break-word;
	    word-wrap: break-word;
}
.toast-message a, .toast-message label {
	color: #fff;
}
.toast-message a:hover {
	color: #ccd5db;
	text-decoration: none;
}
.toast-close-button {
	position: relative;
	top: -.3em;
	right: -.3em;
	float: right;
	margin-bottom: -.5em;
	font-size: 20px;
	font-weight: 400;
	color: #fff;
	        text-shadow: 0 1px 0 #fff;
	filter: alpha(opacity=80);
	opacity: .8;

	-webkit-text-shadow: 0 1px 0 #fff;
}
.toast-close-button:hover, .toast-close-button:focus {
	color: #000;
	text-decoration: none;
	cursor: pointer;
	filter: alpha(opacity=40);
	opacity: .4;
}
button.toast-close-button {
	-webkit-appearance: none;
	padding: 0;
	cursor: pointer;
	background: transparent;
	border: 0;
}
.toast-top-center {
	top: 12px;
	right: 0;
	width: 100%;
}
.toast-bottom-center {
	right: 0;
	bottom: 12px;
	width: 100%;
}
.toast-top-full-width {
	top: 0;
	right: 0;
	width: 100%;
}
.toast-top-full-width .toast {
	margin-bottom: 0;
}
.toast-bottom-full-width {
	right: 0;
	bottom: 0;
	width: 100%;
}
.toast-bottom-full-width .toast {
	margin-bottom: 0 !important;
}
.toast-top-left {
	top: 12px;
	left: 12px;
}
.toast-top-right {
	top: 12px;
	right: 12px;
}
.toast-bottom-right {
	right: 12px;
	bottom: 12px;
}
.toast-bottom-left {
	bottom: 12px;
	left: 12px;
}
[class^="toast-"][aria-live="polite"] {
	position: fixed;
	z-index: 1991050199;
	pointer-events: none;
	/* 覆盖 */
}
[class^="toast-"][aria-live="polite"] * {
	-webkit-box-sizing: border-box;
	   -moz-box-sizing: border-box;
	        box-sizing: border-box;
}
[class^="toast-"][aria-live="polite"] > div {
	position: relative;
	width: 300px;
	padding: 10px 15px;
	margin: 0 0 16px;
	overflow: hidden;
	color: #a3afb7;
	pointer-events: auto;
	filter: alpha(opacity=95);
	background-repeat: no-repeat;
	background-position: 15px center;
	border: 1px solid transparent;
	border-radius: 3px;
	opacity: .95;
}
[class^="toast-"][aria-live="polite"] > .toast-shadow {
	-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, .23);
	        box-shadow: 0 1px 3px rgba(0, 0, 0, .23);
}
[class^="toast-"][aria-live="polite"] > .toast-shadow:hover {
	-webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, .23);
	        box-shadow: 0 1px 6px rgba(0, 0, 0, .23);
}
[class^="toast-"][aria-live="polite"] > :hover {
	cursor: pointer;
	filter: alpha(opacity=100);
	opacity: 1;
}
[class^="toast-"][aria-live="polite"] > .toast-success:not(.toast-just-text), [class^="toast-"][aria-live="polite"] > .toast-info:not(.toast-just-text), [class^="toast-"][aria-live="polite"] > .toast-warning:not(.toast-just-text), [class^="toast-"][aria-live="polite"] > .toast-error:not(.toast-just-text) {
	padding-left: 50px;
}
[class^="toast-"][aria-live="polite"] > .toast-success:not(.toast-just-text):before, [class^="toast-"][aria-live="polite"] > .toast-info:not(.toast-just-text):before, [class^="toast-"][aria-live="polite"] > .toast-warning:not(.toast-just-text):before, [class^="toast-"][aria-live="polite"] > .toast-error:not(.toast-just-text):before {
	position: absolute;
	top: 50%;
	left: 12px;
  font-family: "FontAwesome";
	font-size: 30px;
	font-style: normal;
	font-weight: 400;
	-webkit-transform: translate(0, -50%);
	    -ms-transform: translate(0, -50%);
	     -o-transform: translate(0, -50%);
	        transform: translate(0, -50%);

	text-rendering: auto;
	speak: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
[class^="toast-"][aria-live="polite"] > .toast-success {
	color: #fff;
}
[class^="toast-"][aria-live="polite"] > .toast-success:not(.toast-just-text):before {
  content: "\f00c";
}
[class^="toast-"][aria-live="polite"] > .toast-info {
	color: #fff;
}
[class^="toast-"][aria-live="polite"] > .toast-info:not(.toast-just-text):before {
  content: "\f05a";
}
[class^="toast-"][aria-live="polite"] > .toast-warning {
	color: #fff;
}
[class^="toast-"][aria-live="polite"] > .toast-warning:not(.toast-just-text):before {
  content: "\f071";
}
[class^="toast-"][aria-live="polite"] > .toast-error {
	color: #fff;
}
[class^="toast-"][aria-live="polite"] > .toast-error:not(.toast-just-text):before {
  content: "\f056";
}
[class^="toast-"][aria-live="polite"].toast-top-center > div, [class^="toast-"][aria-live="polite"].toast-bottom-center > div {
	width: 300px;
	margin-right: auto;
	margin-left: auto;
}
[class^="toast-"][aria-live="polite"].toast-top-full-width > div, [class^="toast-"][aria-live="polite"].toast-bottom-full-width > div {
	width: 100%;
	margin-right: auto;
	margin-left: auto;
	border-radius: 0;
}
.toast {
	background-color: #77d6e1;
}
.toast-success {
	background-color: #5cd29d !important;
}
.toast-error {
	background-color: #fa7a7a !important;
}
.toast-info {
	background-color: #77d6e1 !important;
}
.toast-warning {
	background-color: #f4b066 !important;
}
.toast-progress {
	position: absolute;
	bottom: 0;
	left: 0;
	height: 4px;
	background-color: #000;
	filter: alpha(opacity=10);
	opacity: .1;
}
/* 响应式设计 */
@media all and (max-width: 240px) {
	[class^="toast-"][aria-live="polite"] > div {
		width: 11em;
		padding: 8px 8px 8px 50px;
	}
	[class^="toast-"][aria-live="polite"] .toast-close-button {
		top: -.2em;
		right: -.2em;
	}
}
@media all and (min-width: 241px) and (max-width: 480px) {
	[class^="toast-"][aria-live="polite"] > div {
		width: 18em;
		padding: 8px 8px 8px 50px;
	}
	[class^="toast-"][aria-live="polite"] .toast-close-button {
		top: -.2em;
		right: -.2em;
	}
}
@media all and (min-width: 481px) and (max-width: 768px) {
	[class^="toast-"][aria-live="polite"] > div {
		width: 25em;
		padding: 15px 15px 15px 50px;
	}
}
