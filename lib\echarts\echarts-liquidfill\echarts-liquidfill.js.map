{"version": 3, "sources": ["webpack://echarts-liquidfill/webpack/universalModuleDefinition", "webpack://echarts-liquidfill/./src/liquidFillSeries.js", "webpack://echarts-liquidfill/./node_modules/zrender/lib/core/util.js", "webpack://echarts-liquidfill/./node_modules/echarts/lib/util/number.js", "webpack://echarts-liquidfill/./src/liquidFillShape.js", "webpack://echarts-liquidfill/./src/liquidFillView.js", "webpack://echarts-liquidfill/./src/liquidFill.js", "webpack://echarts-liquidfill/./index.js", "webpack://echarts-liquidfill/external \"echarts\"", "webpack://echarts-liquidfill/webpack/bootstrap", "webpack://echarts-liquidfill/webpack/runtime/make namespace object", "webpack://echarts-liquidfill/webpack/startup"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;;;;;;ACV+C;;AAE/C,mCAAyB;;AAEzB;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA,yBAAyB,yCAA+B;AACxD;AACA,SAAS;AACT,uBAAuB,sBAAY;AACnC;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;AC7ED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,EAAE;AAClC;AACA;AACO;AACP;AACA;AACA;AACO;AACP;AACA;AACO;AACP;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,SAAS;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,SAAS;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA,kDAAkD,SAAS;AAC3D;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA,mBAAmB,oBAAoB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA,2CAA2C,SAAS;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA,uBAAuB,oBAAoB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,SAAS;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,SAAS;AAClD;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA,qCAAqC,SAAS;AAC9C;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,SAAS;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA,qCAAqC,SAAS;AAC9C;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACiB;AACV;AACP;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACA;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACO;AACP;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA,sCAAsC,SAAS;AAC/C;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACkB;AACZ;AACP;AACA;AACO;AACP;AACA,mBAAmB,cAAc;AACjC;AACA;AACA;AACA,mBAAmB,cAAc;AACjC;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACO,iBAAiB;;;;AC3exB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEgD;AAChD;;AAEA;AACA;AACA;;AAEO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACO;AACP;AACA;AACA,GAAG;AACH;AACA;AACO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACO;AACP;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;;AAEA,YAAY,MAAa;AACzB;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA,sBAAsB,GAAU;AAChC;AACA,GAAG;AACH;AACA,cAAc,GAAU;AACxB;AACA,GAAG;AACH,mBAAmB,MAAa;AAChC;AACA,GAAG;AACH,kBAAkB,GAAU;AAC5B;AACA,GAAG;;AAEH;AACA;AACA;;AAEA,2CAA2C,SAAS;AACpD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACO;AACA;AACP;AACA;AACA;AACO;AACP;AACA;AACA,wBAAwB,EAAE,aAAa,IAAI,aAAa,IAAI,YAAY,IAAI,SAAS,IAAI,SAAS,IAAI;AAC/F;AACP;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACO;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA,GAAG;AACH;AACA;;AAEA,iBAAiB,iBAAiB;AAClC;AACA;;AAEA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACO;AACP;AACA;AACA;;AAEA;AACA;AACO;AACP;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,C;;AC3V+C;;AAE/C,sDAAe,qCAA2B;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,YAAY;AACnC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC,CAAC,EAAC;;;;AAIH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AClK+C;AACO;AACV;;AAE5C,MAAM,2BAAY,GAAG,YAAuB;;AAE5C,iCAAuB;;AAEvB;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iCAAiC,2BAAY;AAC7C;AACA;AACA;;AAEA,iBAAiB,2BAAY;AAC7B,iBAAiB,2BAAY;;AAE7B;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,2BAAY;AAC5B,gBAAgB,2BAAY;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,2BAAY;AACvC;AACA,4BAA4B,2BAAY;;AAExC;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,gBAAgB,mCAAyB;AACzC;AACA;AACA;AACA,iBAAiB;;AAEjB;AACA;;AAEA;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,+BAA+B,uBAAuB;AACtD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,+BAA+B,uBAAuB;AACtD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,gBAAgB,qCAA2B;AAC3C;AACA,iBAAiB;;AAEjB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,mBAAmB,OAAO;AAC1B,mBAAmB,kBAAkB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,kCAAwB,oBAAoB;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,2BAA2B,kCAAwB;AACnD;AACA,0BAA0B;AAC1B,4BAA4B,sCAA4B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,qCAA2B;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,qCAA2B;AACtD;AACA;;AAEA,uBAAuB,gCAAsB;AAC7C;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,4BAA4B,+BAAqB;AACjD;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,4BAA4B,2BAAY;AACxC;AACA,6BAA6B,2BAAY;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,2BAA2B,eAAW;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA;;AAEA;AACA,YAAY,4CAAkC;;AAE9C;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,wCAA8B;;AAE1E,sCAAsC,8BAAoB;AAC1D,qCAAqC,8BAAoB;AACzD;AACA;;AAEA,kCAAkC,8BAAoB;AACtD,iCAAiC,8BAAoB;AACrD;;AAEA;AACA;AACA;;AAEA,4BAA4B,+BAAqB;AACjD;AACA;;AAEA;AACA;;AAEA,2BAA2B,sCAA4B;AACvD;AACA;AACA,iBAAiB;AACjB;AACA,aAAa;;AAEb;AACA;;AAEA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,CAAC;;;AC3d2B;;;ACAF;;;;;;;;;;;ACA1B,iE;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCrBA;WACA;WACA;WACA,sDAAsD,kBAAkB;WACxE;WACA,+CAA+C,cAAc;WAC7D,E;;;;UCNA;UACA;UACA;UACA", "file": "echarts-liquidfill.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"echarts\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"echarts\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"echarts-liquidfill\"] = factory(require(\"echarts\"));\n\telse\n\t\troot[\"echarts-liquidfill\"] = factory(root[\"echarts\"]);\n})(self, function(__WEBPACK_EXTERNAL_MODULE_echarts_lib_echarts__) {\nreturn ", "import * as echarts from 'echarts/lib/echarts';\n\necharts.extendSeriesModel({\n\n    type: 'series.liquidFill',\n\n    optionUpdated: function () {\n        var option = this.option;\n        option.gridSize = Math.max(Math.floor(option.gridSize), 4);\n    },\n\n    getInitialData: function (option, ecModel) {\n        var dimensions = echarts.helper.createDimensions(option.data, {\n            coordDimensions: ['value']\n        });\n        var list = new echarts.List(dimensions, this);\n        list.initData(option.data);\n        return list;\n    },\n\n    defaultOption: {\n        color: ['#294D99', '#156ACF', '#1598ED', '#45BDFF'],\n        center: ['50%', '50%'],\n        radius: '50%',\n        amplitude: '8%',\n        waveLength: '80%',\n        phase: 'auto',\n        period: 'auto',\n        direction: 'right',\n        shape: 'circle',\n\n        waveAnimation: true,\n        animationEasing: 'linear',\n        animationEasingUpdate: 'linear',\n        animationDuration: 2000,\n        animationDurationUpdate: 1000,\n\n        outline: {\n            show: true,\n            borderDistance: 8,\n            itemStyle: {\n                color: 'none',\n                borderColor: '#294D99',\n                borderWidth: 8,\n                shadowBlur: 20,\n                shadowColor: 'rgba(0, 0, 0, 0.25)'\n            }\n        },\n\n        backgroundStyle: {\n            color: '#E3F7FF'\n        },\n\n        itemStyle: {\n            opacity: 0.95,\n            shadowBlur: 50,\n            shadowColor: 'rgba(0, 0, 0, 0.4)'\n        },\n\n        label: {\n            show: true,\n            color: '#294D99',\n            insideColor: '#fff',\n            fontSize: 50,\n            fontWeight: 'bold',\n\n            align: 'center',\n            baseline: 'middle',\n            position: 'inside'\n        },\n\n        emphasis: {\n            itemStyle: {\n                opacity: 0.8\n            }\n        }\n    }\n});\n", "var BUILTIN_OBJECT = {\n    '[object Function]': true,\n    '[object RegExp]': true,\n    '[object Date]': true,\n    '[object Error]': true,\n    '[object CanvasGradient]': true,\n    '[object CanvasPattern]': true,\n    '[object Image]': true,\n    '[object Canvas]': true\n};\nvar TYPED_ARRAY = {\n    '[object Int8Array]': true,\n    '[object Uint8Array]': true,\n    '[object Uint8ClampedArray]': true,\n    '[object Int16Array]': true,\n    '[object Uint16Array]': true,\n    '[object Int32Array]': true,\n    '[object Uint32Array]': true,\n    '[object Float32Array]': true,\n    '[object Float64Array]': true\n};\nvar objToString = Object.prototype.toString;\nvar arrayProto = Array.prototype;\nvar nativeForEach = arrayProto.forEach;\nvar nativeFilter = arrayProto.filter;\nvar nativeSlice = arrayProto.slice;\nvar nativeMap = arrayProto.map;\nvar ctorFunction = function () { }.constructor;\nvar protoFunction = ctorFunction ? ctorFunction.prototype : null;\nvar methods = {};\nexport function $override(name, fn) {\n    methods[name] = fn;\n}\nvar idStart = 0x0907;\nexport function guid() {\n    return idStart++;\n}\nexport function logError() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    if (typeof console !== 'undefined') {\n        console.error.apply(console, args);\n    }\n}\nexport function clone(source) {\n    if (source == null || typeof source !== 'object') {\n        return source;\n    }\n    var result = source;\n    var typeStr = objToString.call(source);\n    if (typeStr === '[object Array]') {\n        if (!isPrimitive(source)) {\n            result = [];\n            for (var i = 0, len = source.length; i < len; i++) {\n                result[i] = clone(source[i]);\n            }\n        }\n    }\n    else if (TYPED_ARRAY[typeStr]) {\n        if (!isPrimitive(source)) {\n            var Ctor = source.constructor;\n            if (Ctor.from) {\n                result = Ctor.from(source);\n            }\n            else {\n                result = new Ctor(source.length);\n                for (var i = 0, len = source.length; i < len; i++) {\n                    result[i] = clone(source[i]);\n                }\n            }\n        }\n    }\n    else if (!BUILTIN_OBJECT[typeStr] && !isPrimitive(source) && !isDom(source)) {\n        result = {};\n        for (var key in source) {\n            if (source.hasOwnProperty(key)) {\n                result[key] = clone(source[key]);\n            }\n        }\n    }\n    return result;\n}\nexport function merge(target, source, overwrite) {\n    if (!isObject(source) || !isObject(target)) {\n        return overwrite ? clone(source) : target;\n    }\n    for (var key in source) {\n        if (source.hasOwnProperty(key)) {\n            var targetProp = target[key];\n            var sourceProp = source[key];\n            if (isObject(sourceProp)\n                && isObject(targetProp)\n                && !isArray(sourceProp)\n                && !isArray(targetProp)\n                && !isDom(sourceProp)\n                && !isDom(targetProp)\n                && !isBuiltInObject(sourceProp)\n                && !isBuiltInObject(targetProp)\n                && !isPrimitive(sourceProp)\n                && !isPrimitive(targetProp)) {\n                merge(targetProp, sourceProp, overwrite);\n            }\n            else if (overwrite || !(key in target)) {\n                target[key] = clone(source[key]);\n            }\n        }\n    }\n    return target;\n}\nexport function mergeAll(targetAndSources, overwrite) {\n    var result = targetAndSources[0];\n    for (var i = 1, len = targetAndSources.length; i < len; i++) {\n        result = merge(result, targetAndSources[i], overwrite);\n    }\n    return result;\n}\nexport function extend(target, source) {\n    if (Object.assign) {\n        Object.assign(target, source);\n    }\n    else {\n        for (var key in source) {\n            if (source.hasOwnProperty(key)) {\n                target[key] = source[key];\n            }\n        }\n    }\n    return target;\n}\nexport function defaults(target, source, overlay) {\n    var keysArr = keys(source);\n    for (var i = 0; i < keysArr.length; i++) {\n        var key = keysArr[i];\n        if ((overlay ? source[key] != null : target[key] == null)) {\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nexport var createCanvas = function () {\n    return methods.createCanvas();\n};\nmethods.createCanvas = function () {\n    return document.createElement('canvas');\n};\nexport function indexOf(array, value) {\n    if (array) {\n        if (array.indexOf) {\n            return array.indexOf(value);\n        }\n        for (var i = 0, len = array.length; i < len; i++) {\n            if (array[i] === value) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\nexport function inherits(clazz, baseClazz) {\n    var clazzPrototype = clazz.prototype;\n    function F() { }\n    F.prototype = baseClazz.prototype;\n    clazz.prototype = new F();\n    for (var prop in clazzPrototype) {\n        if (clazzPrototype.hasOwnProperty(prop)) {\n            clazz.prototype[prop] = clazzPrototype[prop];\n        }\n    }\n    clazz.prototype.constructor = clazz;\n    clazz.superClass = baseClazz;\n}\nexport function mixin(target, source, override) {\n    target = 'prototype' in target ? target.prototype : target;\n    source = 'prototype' in source ? source.prototype : source;\n    if (Object.getOwnPropertyNames) {\n        var keyList = Object.getOwnPropertyNames(source);\n        for (var i = 0; i < keyList.length; i++) {\n            var key = keyList[i];\n            if (key !== 'constructor') {\n                if ((override ? source[key] != null : target[key] == null)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n    }\n    else {\n        defaults(target, source, override);\n    }\n}\nexport function isArrayLike(data) {\n    if (!data) {\n        return false;\n    }\n    if (typeof data === 'string') {\n        return false;\n    }\n    return typeof data.length === 'number';\n}\nexport function each(arr, cb, context) {\n    if (!(arr && cb)) {\n        return;\n    }\n    if (arr.forEach && arr.forEach === nativeForEach) {\n        arr.forEach(cb, context);\n    }\n    else if (arr.length === +arr.length) {\n        for (var i = 0, len = arr.length; i < len; i++) {\n            cb.call(context, arr[i], i, arr);\n        }\n    }\n    else {\n        for (var key in arr) {\n            if (arr.hasOwnProperty(key)) {\n                cb.call(context, arr[key], key, arr);\n            }\n        }\n    }\n}\nexport function map(arr, cb, context) {\n    if (!arr) {\n        return [];\n    }\n    if (!cb) {\n        return slice(arr);\n    }\n    if (arr.map && arr.map === nativeMap) {\n        return arr.map(cb, context);\n    }\n    else {\n        var result = [];\n        for (var i = 0, len = arr.length; i < len; i++) {\n            result.push(cb.call(context, arr[i], i, arr));\n        }\n        return result;\n    }\n}\nexport function reduce(arr, cb, memo, context) {\n    if (!(arr && cb)) {\n        return;\n    }\n    for (var i = 0, len = arr.length; i < len; i++) {\n        memo = cb.call(context, memo, arr[i], i, arr);\n    }\n    return memo;\n}\nexport function filter(arr, cb, context) {\n    if (!arr) {\n        return [];\n    }\n    if (!cb) {\n        return slice(arr);\n    }\n    if (arr.filter && arr.filter === nativeFilter) {\n        return arr.filter(cb, context);\n    }\n    else {\n        var result = [];\n        for (var i = 0, len = arr.length; i < len; i++) {\n            if (cb.call(context, arr[i], i, arr)) {\n                result.push(arr[i]);\n            }\n        }\n        return result;\n    }\n}\nexport function find(arr, cb, context) {\n    if (!(arr && cb)) {\n        return;\n    }\n    for (var i = 0, len = arr.length; i < len; i++) {\n        if (cb.call(context, arr[i], i, arr)) {\n            return arr[i];\n        }\n    }\n}\nexport function keys(obj) {\n    if (!obj) {\n        return [];\n    }\n    if (Object.keys) {\n        return Object.keys(obj);\n    }\n    var keyList = [];\n    for (var key in obj) {\n        if (obj.hasOwnProperty(key)) {\n            keyList.push(key);\n        }\n    }\n    return keyList;\n}\nfunction bindPolyfill(func, context) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        args[_i - 2] = arguments[_i];\n    }\n    return function () {\n        return func.apply(context, args.concat(nativeSlice.call(arguments)));\n    };\n}\nexport var bind = (protoFunction && isFunction(protoFunction.bind))\n    ? protoFunction.call.bind(protoFunction.bind)\n    : bindPolyfill;\nfunction curry(func) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    return function () {\n        return func.apply(this, args.concat(nativeSlice.call(arguments)));\n    };\n}\nexport { curry };\nexport function isArray(value) {\n    if (Array.isArray) {\n        return Array.isArray(value);\n    }\n    return objToString.call(value) === '[object Array]';\n}\nexport function isFunction(value) {\n    return typeof value === 'function';\n}\nexport function isString(value) {\n    return typeof value === 'string';\n}\nexport function isStringSafe(value) {\n    return objToString.call(value) === '[object String]';\n}\nexport function isNumber(value) {\n    return typeof value === 'number';\n}\nexport function isObject(value) {\n    var type = typeof value;\n    return type === 'function' || (!!value && type === 'object');\n}\nexport function isBuiltInObject(value) {\n    return !!BUILTIN_OBJECT[objToString.call(value)];\n}\nexport function isTypedArray(value) {\n    return !!TYPED_ARRAY[objToString.call(value)];\n}\nexport function isDom(value) {\n    return typeof value === 'object'\n        && typeof value.nodeType === 'number'\n        && typeof value.ownerDocument === 'object';\n}\nexport function isGradientObject(value) {\n    return value.colorStops != null;\n}\nexport function isPatternObject(value) {\n    return value.image != null;\n}\nexport function isRegExp(value) {\n    return objToString.call(value) === '[object RegExp]';\n}\nexport function eqNaN(value) {\n    return value !== value;\n}\nexport function retrieve() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    for (var i = 0, len = args.length; i < len; i++) {\n        if (args[i] != null) {\n            return args[i];\n        }\n    }\n}\nexport function retrieve2(value0, value1) {\n    return value0 != null\n        ? value0\n        : value1;\n}\nexport function retrieve3(value0, value1, value2) {\n    return value0 != null\n        ? value0\n        : value1 != null\n            ? value1\n            : value2;\n}\nexport function slice(arr) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    return nativeSlice.apply(arr, args);\n}\nexport function normalizeCssArray(val) {\n    if (typeof (val) === 'number') {\n        return [val, val, val, val];\n    }\n    var len = val.length;\n    if (len === 2) {\n        return [val[0], val[1], val[0], val[1]];\n    }\n    else if (len === 3) {\n        return [val[0], val[1], val[2], val[1]];\n    }\n    return val;\n}\nexport function assert(condition, message) {\n    if (!condition) {\n        throw new Error(message);\n    }\n}\nexport function trim(str) {\n    if (str == null) {\n        return null;\n    }\n    else if (typeof str.trim === 'function') {\n        return str.trim();\n    }\n    else {\n        return str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n    }\n}\nvar primitiveKey = '__ec_primitive__';\nexport function setAsPrimitive(obj) {\n    obj[primitiveKey] = true;\n}\nexport function isPrimitive(obj) {\n    return obj[primitiveKey];\n}\nvar HashMap = (function () {\n    function HashMap(obj) {\n        this.data = {};\n        var isArr = isArray(obj);\n        this.data = {};\n        var thisMap = this;\n        (obj instanceof HashMap)\n            ? obj.each(visit)\n            : (obj && each(obj, visit));\n        function visit(value, key) {\n            isArr ? thisMap.set(value, key) : thisMap.set(key, value);\n        }\n    }\n    HashMap.prototype.get = function (key) {\n        return this.data.hasOwnProperty(key) ? this.data[key] : null;\n    };\n    HashMap.prototype.set = function (key, value) {\n        return (this.data[key] = value);\n    };\n    HashMap.prototype.each = function (cb, context) {\n        for (var key in this.data) {\n            if (this.data.hasOwnProperty(key)) {\n                cb.call(context, this.data[key], key);\n            }\n        }\n    };\n    HashMap.prototype.keys = function () {\n        return keys(this.data);\n    };\n    HashMap.prototype.removeKey = function (key) {\n        delete this.data[key];\n    };\n    return HashMap;\n}());\nexport { HashMap };\nexport function createHashMap(obj) {\n    return new HashMap(obj);\n}\nexport function concatArray(a, b) {\n    var newArray = new a.constructor(a.length + b.length);\n    for (var i = 0; i < a.length; i++) {\n        newArray[i] = a[i];\n    }\n    var offset = a.length;\n    for (var i = 0; i < b.length; i++) {\n        newArray[i + offset] = b[i];\n    }\n    return newArray;\n}\nexport function createObject(proto, properties) {\n    var obj;\n    if (Object.create) {\n        obj = Object.create(proto);\n    }\n    else {\n        var StyleCtor = function () { };\n        StyleCtor.prototype = proto;\n        obj = new StyleCtor();\n    }\n    if (properties) {\n        extend(obj, properties);\n    }\n    return obj;\n}\nexport function hasOwn(own, prop) {\n    return own.hasOwnProperty(prop);\n}\nexport function noop() { }\n", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport * as zrUtil from 'zrender/lib/core/util';\nvar RADIAN_EPSILON = 1e-4;\n\nfunction _trim(str) {\n  return str.replace(/^\\s+|\\s+$/g, '');\n}\n\nexport function linearMap(val, domain, range, clamp) {\n  var subDomain = domain[1] - domain[0];\n  var subRange = range[1] - range[0];\n\n  if (subDomain === 0) {\n    return subRange === 0 ? range[0] : (range[0] + range[1]) / 2;\n  }\n\n  if (clamp) {\n    if (subDomain > 0) {\n      if (val <= domain[0]) {\n        return range[0];\n      } else if (val >= domain[1]) {\n        return range[1];\n      }\n    } else {\n      if (val >= domain[0]) {\n        return range[0];\n      } else if (val <= domain[1]) {\n        return range[1];\n      }\n    }\n  } else {\n    if (val === domain[0]) {\n      return range[0];\n    }\n\n    if (val === domain[1]) {\n      return range[1];\n    }\n  }\n\n  return (val - domain[0]) / subDomain * subRange + range[0];\n}\nexport function parsePercent(percent, all) {\n  switch (percent) {\n    case 'center':\n    case 'middle':\n      percent = '50%';\n      break;\n\n    case 'left':\n    case 'top':\n      percent = '0%';\n      break;\n\n    case 'right':\n    case 'bottom':\n      percent = '100%';\n      break;\n  }\n\n  if (typeof percent === 'string') {\n    if (_trim(percent).match(/%$/)) {\n      return parseFloat(percent) / 100 * all;\n    }\n\n    return parseFloat(percent);\n  }\n\n  return percent == null ? NaN : +percent;\n}\nexport function round(x, precision, returnStr) {\n  if (precision == null) {\n    precision = 10;\n  }\n\n  precision = Math.min(Math.max(0, precision), 20);\n  x = (+x).toFixed(precision);\n  return returnStr ? x : +x;\n}\nexport function asc(arr) {\n  arr.sort(function (a, b) {\n    return a - b;\n  });\n  return arr;\n}\nexport function getPrecision(val) {\n  val = +val;\n\n  if (isNaN(val)) {\n    return 0;\n  }\n\n  var e = 1;\n  var count = 0;\n\n  while (Math.round(val * e) / e !== val) {\n    e *= 10;\n    count++;\n  }\n\n  return count;\n}\nexport function getPrecisionSafe(val) {\n  var str = val.toString();\n  var eIndex = str.indexOf('e');\n\n  if (eIndex > 0) {\n    var precision = +str.slice(eIndex + 1);\n    return precision < 0 ? -precision : 0;\n  } else {\n    var dotIndex = str.indexOf('.');\n    return dotIndex < 0 ? 0 : str.length - 1 - dotIndex;\n  }\n}\nexport function getPixelPrecision(dataExtent, pixelExtent) {\n  var log = Math.log;\n  var LN10 = Math.LN10;\n  var dataQuantity = Math.floor(log(dataExtent[1] - dataExtent[0]) / LN10);\n  var sizeQuantity = Math.round(log(Math.abs(pixelExtent[1] - pixelExtent[0])) / LN10);\n  var precision = Math.min(Math.max(-dataQuantity + sizeQuantity, 0), 20);\n  return !isFinite(precision) ? 20 : precision;\n}\nexport function getPercentWithPrecision(valueList, idx, precision) {\n  if (!valueList[idx]) {\n    return 0;\n  }\n\n  var sum = zrUtil.reduce(valueList, function (acc, val) {\n    return acc + (isNaN(val) ? 0 : val);\n  }, 0);\n\n  if (sum === 0) {\n    return 0;\n  }\n\n  var digits = Math.pow(10, precision);\n  var votesPerQuota = zrUtil.map(valueList, function (val) {\n    return (isNaN(val) ? 0 : val) / sum * digits * 100;\n  });\n  var targetSeats = digits * 100;\n  var seats = zrUtil.map(votesPerQuota, function (votes) {\n    return Math.floor(votes);\n  });\n  var currentSum = zrUtil.reduce(seats, function (acc, val) {\n    return acc + val;\n  }, 0);\n  var remainder = zrUtil.map(votesPerQuota, function (votes, idx) {\n    return votes - seats[idx];\n  });\n\n  while (currentSum < targetSeats) {\n    var max = Number.NEGATIVE_INFINITY;\n    var maxId = null;\n\n    for (var i = 0, len = remainder.length; i < len; ++i) {\n      if (remainder[i] > max) {\n        max = remainder[i];\n        maxId = i;\n      }\n    }\n\n    ++seats[maxId];\n    remainder[maxId] = 0;\n    ++currentSum;\n  }\n\n  return seats[idx] / digits;\n}\nexport var MAX_SAFE_INTEGER = 9007199254740991;\nexport function remRadian(radian) {\n  var pi2 = Math.PI * 2;\n  return (radian % pi2 + pi2) % pi2;\n}\nexport function isRadianAroundZero(val) {\n  return val > -RADIAN_EPSILON && val < RADIAN_EPSILON;\n}\nvar TIME_REG = /^(?:(\\d{4})(?:[-\\/](\\d{1,2})(?:[-\\/](\\d{1,2})(?:[T ](\\d{1,2})(?::(\\d{1,2})(?::(\\d{1,2})(?:[.,](\\d+))?)?)?(Z|[\\+\\-]\\d\\d:?\\d\\d)?)?)?)?)?$/;\nexport function parseDate(value) {\n  if (value instanceof Date) {\n    return value;\n  } else if (typeof value === 'string') {\n    var match = TIME_REG.exec(value);\n\n    if (!match) {\n      return new Date(NaN);\n    }\n\n    if (!match[8]) {\n      return new Date(+match[1], +(match[2] || 1) - 1, +match[3] || 1, +match[4] || 0, +(match[5] || 0), +match[6] || 0, +match[7] || 0);\n    } else {\n      var hour = +match[4] || 0;\n\n      if (match[8].toUpperCase() !== 'Z') {\n        hour -= +match[8].slice(0, 3);\n      }\n\n      return new Date(Date.UTC(+match[1], +(match[2] || 1) - 1, +match[3] || 1, hour, +(match[5] || 0), +match[6] || 0, +match[7] || 0));\n    }\n  } else if (value == null) {\n    return new Date(NaN);\n  }\n\n  return new Date(Math.round(value));\n}\nexport function quantity(val) {\n  return Math.pow(10, quantityExponent(val));\n}\nexport function quantityExponent(val) {\n  if (val === 0) {\n    return 0;\n  }\n\n  var exp = Math.floor(Math.log(val) / Math.LN10);\n\n  if (val / Math.pow(10, exp) >= 10) {\n    exp++;\n  }\n\n  return exp;\n}\nexport function nice(val, round) {\n  var exponent = quantityExponent(val);\n  var exp10 = Math.pow(10, exponent);\n  var f = val / exp10;\n  var nf;\n\n  if (round) {\n    if (f < 1.5) {\n      nf = 1;\n    } else if (f < 2.5) {\n      nf = 2;\n    } else if (f < 4) {\n      nf = 3;\n    } else if (f < 7) {\n      nf = 5;\n    } else {\n      nf = 10;\n    }\n  } else {\n    if (f < 1) {\n      nf = 1;\n    } else if (f < 2) {\n      nf = 2;\n    } else if (f < 3) {\n      nf = 3;\n    } else if (f < 5) {\n      nf = 5;\n    } else {\n      nf = 10;\n    }\n  }\n\n  val = nf * exp10;\n  return exponent >= -20 ? +val.toFixed(exponent < 0 ? -exponent : 0) : val;\n}\nexport function quantile(ascArr, p) {\n  var H = (ascArr.length - 1) * p + 1;\n  var h = Math.floor(H);\n  var v = +ascArr[h - 1];\n  var e = H - h;\n  return e ? v + e * (ascArr[h] - v) : v;\n}\nexport function reformIntervals(list) {\n  list.sort(function (a, b) {\n    return littleThan(a, b, 0) ? -1 : 1;\n  });\n  var curr = -Infinity;\n  var currClose = 1;\n\n  for (var i = 0; i < list.length;) {\n    var interval = list[i].interval;\n    var close_1 = list[i].close;\n\n    for (var lg = 0; lg < 2; lg++) {\n      if (interval[lg] <= curr) {\n        interval[lg] = curr;\n        close_1[lg] = !lg ? 1 - currClose : 1;\n      }\n\n      curr = interval[lg];\n      currClose = close_1[lg];\n    }\n\n    if (interval[0] === interval[1] && close_1[0] * close_1[1] !== 1) {\n      list.splice(i, 1);\n    } else {\n      i++;\n    }\n  }\n\n  return list;\n\n  function littleThan(a, b, lg) {\n    return a.interval[lg] < b.interval[lg] || a.interval[lg] === b.interval[lg] && (a.close[lg] - b.close[lg] === (!lg ? 1 : -1) || !lg && littleThan(a, b, 1));\n  }\n}\nexport function numericToNumber(val) {\n  var valFloat = parseFloat(val);\n  return valFloat == val && (valFloat !== 0 || typeof val !== 'string' || val.indexOf('x') <= 0) ? valFloat : NaN;\n}\nexport function isNumeric(val) {\n  return !isNaN(numericToNumber(val));\n}\nexport function getRandomIdBase() {\n  return Math.round(Math.random() * 9);\n}\nexport function getGreatestCommonDividor(a, b) {\n  if (b === 0) {\n    return a;\n  }\n\n  return getGreatestCommonDividor(b, a % b);\n}\nexport function getLeastCommonMultiple(a, b) {\n  if (a == null) {\n    return b;\n  }\n\n  if (b == null) {\n    return a;\n  }\n\n  return a * b / getGreatestCommonDividor(a, b);\n}", "import * as echarts from 'echarts/lib/echarts';\n\nexport default echarts.graphic.extendShape({\n    type: 'ec-liquid-fill',\n\n    shape: {\n        waveLength: 0,\n        radius: 0,\n        radiusY: 0,\n        cx: 0,\n        cy: 0,\n        waterLevel: 0,\n        amplitude: 0,\n        phase: 0,\n        inverse: false\n    },\n\n    buildPath: function (ctx, shape) {\n        if (shape.radiusY == null) {\n            shape.radiusY = shape.radius;\n        }\n\n        /**\n         * We define a sine wave having 4 waves, and make sure at least 8 curves\n         * is drawn. Otherwise, it may cause blank area for some waves when\n         * wave length is large enough.\n         */\n        var curves = Math.max(\n            Math.ceil(2 * shape.radius / shape.waveLength * 4) * 2,\n            8\n        );\n\n        // map phase to [-Math.PI * 2, 0]\n        while (shape.phase < -Math.PI * 2) {\n            shape.phase += Math.PI * 2;\n        }\n        while (shape.phase > 0) {\n            shape.phase -= Math.PI * 2;\n        }\n        var phase = shape.phase / Math.PI / 2 * shape.waveLength;\n\n        var left = shape.cx - shape.radius + phase - shape.radius * 2;\n\n        /**\n         * top-left corner as start point\n         *\n         * draws this point\n         *  |\n         * \\|/\n         *  ~~~~~~~~\n         *  |      |\n         *  +------+\n         */\n        ctx.moveTo(left, shape.waterLevel);\n\n        /**\n         * top wave\n         *\n         * ~~~~~~~~ <- draws this sine wave\n         * |      |\n         * +------+\n         */\n        var waveRight = 0;\n        for (var c = 0; c < curves; ++c) {\n            var stage = c % 4;\n            var pos = getWaterPositions(c * shape.waveLength / 4, stage,\n                shape.waveLength, shape.amplitude);\n            ctx.bezierCurveTo(pos[0][0] + left, -pos[0][1] + shape.waterLevel,\n                pos[1][0] + left, -pos[1][1] + shape.waterLevel,\n                pos[2][0] + left, -pos[2][1] + shape.waterLevel);\n\n            if (c === curves - 1) {\n                waveRight = pos[2][0];\n            }\n        }\n\n        if (shape.inverse) {\n            /**\n             * top-right corner\n             *                  2. draws this line\n             *                          |\n             *                       +------+\n             * 3. draws this line -> |      | <- 1. draws this line\n             *                       ~~~~~~~~\n             */\n            ctx.lineTo(waveRight + left, shape.cy - shape.radiusY);\n            ctx.lineTo(left, shape.cy - shape.radiusY);\n            ctx.lineTo(left, shape.waterLevel);\n        }\n        else {\n            /**\n             * top-right corner\n             *\n             *                       ~~~~~~~~\n             * 3. draws this line -> |      | <- 1. draws this line\n             *                       +------+\n             *                          ^\n             *                          |\n             *                  2. draws this line\n             */\n            ctx.lineTo(waveRight + left, shape.cy + shape.radiusY);\n            ctx.lineTo(left, shape.cy + shape.radiusY);\n            ctx.lineTo(left, shape.waterLevel);\n        }\n\n        ctx.closePath();\n    }\n});\n\n\n\n/**\n * Using Bezier curves to fit sine wave.\n * There is 4 control points for each curve of wave,\n * which is at 1/4 wave length of the sine wave.\n *\n * The control points for a wave from (a) to (d) are a-b-c-d:\n *          c *----* d\n *     b *\n *       |\n * ... a * ..................\n *\n * whose positions are a: (0, 0), b: (0.5, 0.5), c: (1, 1), d: (PI / 2, 1)\n *\n * @param {number} x          x position of the left-most point (a)\n * @param {number} stage      0-3, stating which part of the wave it is\n * @param {number} waveLength wave length of the sine wave\n * @param {number} amplitude  wave amplitude\n */\nfunction getWaterPositions(x, stage, waveLength, amplitude) {\n    if (stage === 0) {\n        return [\n            [x + 1 / 2 * waveLength / Math.PI / 2, amplitude / 2],\n            [x + 1 / 2 * waveLength / Math.PI,     amplitude],\n            [x + waveLength / 4,                   amplitude]\n        ];\n    }\n    else if (stage === 1) {\n        return [\n            [x + 1 / 2 * waveLength / Math.PI / 2 * (Math.PI - 2),\n            amplitude],\n            [x + 1 / 2 * waveLength / Math.PI / 2 * (Math.PI - 1),\n            amplitude / 2],\n            [x + waveLength / 4,                   0]\n        ]\n    }\n    else if (stage === 2) {\n        return [\n            [x + 1 / 2 * waveLength / Math.PI / 2, -amplitude / 2],\n            [x + 1 / 2 * waveLength / Math.PI,     -amplitude],\n            [x + waveLength / 4,                   -amplitude]\n        ]\n    }\n    else {\n        return [\n            [x + 1 / 2 * waveLength / Math.PI / 2 * (Math.PI - 2),\n            -amplitude],\n            [x + 1 / 2 * waveLength / Math.PI / 2 * (Math.PI - 1),\n            -amplitude / 2],\n            [x + waveLength / 4,                   0]\n        ]\n    }\n}\n", "import * as echarts from 'echarts/lib/echarts';\nimport * as numberUtil from 'echarts/lib/util/number';\nimport LiquidShape from './liquidFillShape';\n\nconst parsePercent = numberUtil.parsePercent;\n\necharts.extendChartView({\n\n    type: 'liquidFill',\n\n    render: function (seriesModel, ecModel, api) {\n        var group = this.group;\n        group.removeAll();\n\n        var data = seriesModel.getData();\n\n        var itemModel = data.getItemModel(0);\n\n        var center = itemModel.get('center');\n        var radius = itemModel.get('radius');\n\n        var width = api.getWidth();\n        var height = api.getHeight();\n        var size = Math.min(width, height);\n        // itemStyle\n        var outlineDistance = 0;\n        var outlineBorderWidth = 0;\n        var showOutline = seriesModel.get('outline.show');\n\n        if (showOutline) {\n            outlineDistance = seriesModel.get('outline.borderDistance');\n            outlineBorderWidth = parsePercent(\n                seriesModel.get('outline.itemStyle.borderWidth'), size\n            );\n        }\n\n        var cx = parsePercent(center[0], width);\n        var cy = parsePercent(center[1], height);\n\n        var outterRadius;\n        var innerRadius;\n        var paddingRadius;\n\n        var isFillContainer = false;\n\n        var symbol = seriesModel.get('shape');\n        if (symbol === 'container') {\n            // a shape that fully fills the container\n            isFillContainer = true;\n\n            outterRadius = [\n                width / 2,\n                height / 2\n            ];\n            innerRadius = [\n                outterRadius[0] - outlineBorderWidth / 2,\n                outterRadius[1] - outlineBorderWidth / 2\n            ];\n            paddingRadius = [\n                parsePercent(outlineDistance, width),\n                parsePercent(outlineDistance, height)\n            ];\n\n            radius = [\n                Math.max(innerRadius[0] - paddingRadius[0], 0),\n                Math.max(innerRadius[1] - paddingRadius[1], 0)\n            ];\n        }\n        else {\n            outterRadius = parsePercent(radius, size) / 2;\n            innerRadius = outterRadius - outlineBorderWidth / 2;\n            paddingRadius = parsePercent(outlineDistance, size);\n\n            radius = Math.max(innerRadius - paddingRadius, 0);\n        }\n\n        if (showOutline) {\n            var outline = getOutline();\n            outline.style.lineWidth = outlineBorderWidth;\n            group.add(getOutline());\n        }\n\n        var left = isFillContainer ? 0 : cx - radius;\n        var top = isFillContainer ? 0 : cy - radius;\n\n        var wavePath = null;\n\n        group.add(getBackground());\n\n        // each data item for a wave\n        var oldData = this._data;\n        var waves = [];\n        data.diff(oldData)\n            .add(function (idx) {\n                var wave = getWave(idx, false);\n\n                var waterLevel = wave.shape.waterLevel;\n                wave.shape.waterLevel = isFillContainer ? height / 2 : radius;\n                echarts.graphic.initProps(wave, {\n                    shape: {\n                        waterLevel: waterLevel\n                    }\n                }, seriesModel);\n\n                wave.z2 = 2;\n                setWaveAnimation(idx, wave, null);\n\n                group.add(wave);\n                data.setItemGraphicEl(idx, wave);\n                waves.push(wave);\n            })\n            .update(function (newIdx, oldIdx) {\n                var waveElement = oldData.getItemGraphicEl(oldIdx);\n\n                // new wave is used to calculate position, but not added\n                var newWave = getWave(newIdx, false, waveElement);\n\n                // changes with animation\n                var shape = {};\n                var shapeAttrs = ['amplitude', 'cx', 'cy', 'phase', 'radius', 'radiusY', 'waterLevel', 'waveLength'];\n                for (var i = 0; i < shapeAttrs.length; ++i) {\n                    var attr = shapeAttrs[i];\n                    if (newWave.shape.hasOwnProperty(attr)) {\n                        shape[attr] = newWave.shape[attr];\n                    }\n                }\n\n                var style = {};\n                var styleAttrs = ['fill', 'opacity', 'shadowBlur', 'shadowColor'];\n                for (var i = 0; i < styleAttrs.length; ++i) {\n                    var attr = styleAttrs[i];\n                    if (newWave.style.hasOwnProperty(attr)) {\n                        style[attr] = newWave.style[attr];\n                    }\n                }\n\n                if (isFillContainer) {\n                    shape.radiusY = height / 2;\n                }\n\n                // changes with animation\n                echarts.graphic.updateProps(waveElement, {\n                    shape: shape\n                }, seriesModel);\n\n                waveElement.useStyle(style);\n\n                // instant changes\n                waveElement.position = newWave.position;\n                waveElement.setClipPath(newWave.getClipPath());\n                waveElement.shape.inverse = newWave.inverse;\n\n                setWaveAnimation(newIdx, waveElement, waveElement);\n                group.add(waveElement);\n                data.setItemGraphicEl(newIdx, waveElement);\n                waves.push(waveElement);\n            })\n            .remove(function (idx) {\n                var wave = oldData.getItemGraphicEl(idx);\n                group.remove(wave);\n            })\n            .execute();\n\n        if (itemModel.get('label.show')) {\n            group.add(getText(waves));\n        }\n\n        this._data = data;\n\n        /**\n         * Get path for outline, background and clipping\n         *\n         * @param {number} r outter radius of shape\n         * @param {boolean|undefined} isForClipping if the shape is used\n         *                                          for clipping\n         */\n        function getPath(r, isForClipping) {\n            if (symbol) {\n                // customed symbol path\n                if (symbol.indexOf('path://') === 0) {\n                    var path = echarts.graphic.makePath(symbol.slice(7), {});\n                    var bouding = path.getBoundingRect();\n                    var w = bouding.width;\n                    var h = bouding.height;\n                    if (w > h) {\n                        h = r * 2 / w * h;\n                        w = r * 2;\n                    }\n                    else {\n                        w = r * 2 / h * w;\n                        h = r * 2;\n                    }\n\n                    var left = isForClipping ? 0 : cx - w / 2;\n                    var top = isForClipping ? 0 : cy - h / 2;\n                    path = echarts.graphic.makePath(\n                        symbol.slice(7),\n                        {},\n                        new echarts.graphic.BoundingRect(left, top, w, h)\n                    );\n                    if (isForClipping) {\n                        path.position = [-w / 2, -h / 2];\n                    }\n                    return path;\n                }\n                else if (isFillContainer) {\n                    // fully fill the container\n                    var x = isForClipping ? -r[0] : cx - r[0];\n                    var y = isForClipping ? -r[1] : cy - r[1];\n                    return echarts.helper.createSymbol(\n                        'rect', x, y, r[0] * 2, r[1] * 2\n                    );\n                }\n                else {\n                    var x = isForClipping ? -r : cx - r;\n                    var y = isForClipping ? -r : cy - r;\n                    if (symbol === 'pin') {\n                        y += r;\n                    }\n                    else if (symbol === 'arrow') {\n                        y -= r;\n                    }\n                    return echarts.helper.createSymbol(symbol, x, y, r * 2, r * 2);\n                }\n            }\n\n            return new echarts.graphic.Circle({\n                shape: {\n                    cx: isForClipping ? 0 : cx,\n                    cy: isForClipping ? 0 : cy,\n                    r: r\n                }\n            });\n        }\n        /**\n         * Create outline\n         */\n        function getOutline() {\n            var outlinePath = getPath(outterRadius);\n            outlinePath.style.fill = null;\n\n            outlinePath.setStyle(seriesModel.getModel('outline.itemStyle')\n                .getItemStyle());\n\n            return outlinePath;\n        }\n\n        /**\n         * Create background\n         */\n        function getBackground() {\n            // Seperate stroke and fill, so we can use stroke to cover the alias of clipping.\n            var strokePath = getPath(radius);\n            strokePath.setStyle(seriesModel.getModel('backgroundStyle')\n                .getItemStyle());\n            strokePath.style.fill = null;\n\n            // Stroke is front of wave\n            strokePath.z2 = 5;\n\n            var fillPath = getPath(radius);\n            fillPath.setStyle(seriesModel.getModel('backgroundStyle')\n                .getItemStyle());\n            fillPath.style.stroke = null;\n\n            var group = new echarts.graphic.Group();\n            group.add(strokePath);\n            group.add(fillPath);\n\n            return group;\n        }\n\n        /**\n         * wave shape\n         */\n        function getWave(idx, isInverse, oldWave) {\n            var radiusX = isFillContainer ? radius[0] : radius;\n            var radiusY = isFillContainer ? height / 2 : radius;\n\n            var itemModel = data.getItemModel(idx);\n            var itemStyleModel = itemModel.getModel('itemStyle');\n            var phase = itemModel.get('phase');\n            var amplitude = parsePercent(itemModel.get('amplitude'),\n                radiusY * 2);\n            var waveLength = parsePercent(itemModel.get('waveLength'),\n                radiusX * 2);\n\n            var value = data.get('value', idx);\n            var waterLevel = radiusY - value * radiusY * 2;\n            phase = oldWave ? oldWave.shape.phase\n                : (phase === 'auto' ? idx * Math.PI / 4 : phase);\n            var normalStyle = itemStyleModel.getItemStyle();\n            if (!normalStyle.fill) {\n                var seriesColor = seriesModel.get('color');\n                var id = idx % seriesColor.length;\n                normalStyle.fill = seriesColor[id];\n            }\n\n            var x = radiusX * 2;\n            var wave = new LiquidShape({\n                shape: {\n                    waveLength: waveLength,\n                    radius: radiusX,\n                    radiusY: radiusY,\n                    cx: x,\n                    cy: 0,\n                    waterLevel: waterLevel,\n                    amplitude: amplitude,\n                    phase: phase,\n                    inverse: isInverse\n                },\n                style: normalStyle,\n                position: [cx, cy]\n            });\n            wave.shape._waterLevel = waterLevel;\n\n            var hoverStyle = itemModel.getModel('emphasis.itemStyle')\n                .getItemStyle();\n            hoverStyle.lineWidth = 0;\n\n            wave.ensureState('emphasis').style = hoverStyle;\n            echarts.helper.enableHoverEmphasis(wave);\n\n            // clip out the part outside the circle\n            var clip = getPath(radius, true);\n            // set fill for clipPath, otherwise it will not trigger hover event\n            clip.setStyle({\n                fill: 'white'\n            });\n            wave.setClipPath(clip);\n\n            return wave;\n        }\n\n        function setWaveAnimation(idx, wave, oldWave) {\n            var itemModel = data.getItemModel(idx);\n\n            var maxSpeed = itemModel.get('period');\n            var direction = itemModel.get('direction');\n\n            var value = data.get('value', idx);\n\n            var phase = itemModel.get('phase');\n            phase = oldWave ? oldWave.shape.phase\n                : (phase === 'auto' ? idx * Math.PI / 4 : phase);\n\n            var defaultSpeed = function (maxSpeed) {\n                var cnt = data.count();\n                return cnt === 0 ? maxSpeed : maxSpeed *\n                    (0.2 + (cnt - idx) / cnt * 0.8);\n            };\n            var speed = 0;\n            if (maxSpeed === 'auto') {\n                speed = defaultSpeed(5000);\n            }\n            else {\n                speed = typeof maxSpeed === 'function'\n                    ? maxSpeed(value, idx) : maxSpeed;\n            }\n\n            // phase for moving left/right\n            var phaseOffset = 0;\n            if (direction === 'right' || direction == null) {\n                phaseOffset = Math.PI;\n            }\n            else if (direction === 'left') {\n                phaseOffset = -Math.PI;\n            }\n            else if (direction === 'none') {\n                phaseOffset = 0;\n            }\n            else {\n                console.error('Illegal direction value for liquid fill.');\n            }\n\n            // wave animation of moving left/right\n            if (direction !== 'none' && itemModel.get('waveAnimation')) {\n                wave\n                    .animate('shape', true)\n                    .when(0, {\n                        phase: phase\n                    })\n                    .when(speed / 2, {\n                        phase: phaseOffset + phase\n                    })\n                    .when(speed, {\n                        phase: phaseOffset * 2 + phase\n                    })\n                    .during(function () {\n                        if (wavePath) {\n                            wavePath.dirty(true);\n                        }\n                    })\n                    .start();\n            }\n        }\n\n        /**\n         * text on wave\n         */\n        function getText(waves) {\n            var labelModel = itemModel.getModel('label');\n\n            function formatLabel() {\n                var formatted = seriesModel.getFormattedLabel(0, 'normal');\n                var defaultVal = (data.get('value', 0) * 100);\n                var defaultLabel = data.getName(0) || seriesModel.name;\n                if (!isNaN(defaultVal)) {\n                    defaultLabel = defaultVal.toFixed(0) + '%';\n                }\n                return formatted == null ? defaultLabel : formatted;\n            }\n\n            var textRectOption = {\n                z2: 10,\n                shape: {\n                    x: left,\n                    y: top,\n                    width: (isFillContainer ? radius[0] : radius) * 2,\n                    height: (isFillContainer ? radius[1] : radius) * 2\n                },\n                style: {\n                    fill: 'transparent'\n                },\n                textConfig: {\n                    position: labelModel.get('position') || 'inside'\n                },\n                silent: true\n            };\n            var textOption = {\n                style: {\n                    text: formatLabel(),\n                    textAlign: labelModel.get('align'),\n                    textVerticalAlign: labelModel.get('baseline')\n                }\n            };\n            Object.assign(textOption.style, echarts.helper.createTextStyle(labelModel));\n\n            var outsideTextRect = new echarts.graphic.Rect(textRectOption);\n            var insideTextRect = new echarts.graphic.Rect(textRectOption);\n            insideTextRect.disableLabelAnimation = true;\n            outsideTextRect.disableLabelAnimation = true;\n\n            var outsideText = new echarts.graphic.Text(textOption);\n            var insideText = new echarts.graphic.Text(textOption);\n            outsideTextRect.setTextContent(outsideText);\n\n            insideTextRect.setTextContent(insideText);\n            var insColor = labelModel.get('insideColor');\n            insideText.style.fill = insColor;\n\n            var group = new echarts.graphic.Group();\n            group.add(outsideTextRect);\n            group.add(insideTextRect);\n\n            // clip out waves for insideText\n            var boundingCircle = getPath(radius, true);\n\n            wavePath = new echarts.graphic.CompoundPath({\n                shape: {\n                    paths: waves\n                },\n                position: [cx, cy]\n            });\n\n            wavePath.setClipPath(boundingCircle);\n            insideTextRect.setClipPath(wavePath);\n\n            return group;\n        }\n    },\n\n    dispose: function () {\n        // dispose nothing here\n    }\n});\n", "import './liquidFillSeries';\nimport './liquidFillView';", "import './src/liquidFill';\n", "module.exports = __WEBPACK_EXTERNAL_MODULE_echarts_lib_echarts__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tif(__webpack_module_cache__[moduleId]) {\n\t\treturn __webpack_module_cache__[moduleId].exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// module exports must be returned from runtime so entry inlining is disabled\n// startup\n// Load entry module and return exports\nreturn __webpack_require__(\"./index.js\");\n"], "sourceRoot": ""}