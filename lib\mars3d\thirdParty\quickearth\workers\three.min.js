/**
 * @license
 * Copyright 2010-2022 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
!(function (t, e) {
  "object" == typeof exports && "undefined" != typeof module
    ? e(exports)
    : "function" == typeof define && define.amd
    ? define(["exports"], e)
    : e(
        ((t = "undefined" != typeof globalThis ? globalThis : t || self).THREE =
          {})
      );
})(this, function (t) {
  "use strict";
  const e = "144",
    s = 1e3,
    r = 1001,
    i = 1002,
    n = 1009,
    h = 1023,
    a = 33776,
    o = 33777,
    u = 33778,
    l = 33779,
    c = 35840,
    d = 35841,
    m = 35842,
    y = 35843,
    p = 37492,
    x = 37496,
    f = 37808,
    g = 37809,
    b = 37810,
    S = 37811,
    M = 37812,
    w = 37813,
    _ = 37814,
    v = 37815,
    A = 37816,
    z = 37817,
    C = 37818,
    B = 37819,
    R = 37820,
    E = 37821,
    P = 36492,
    T = 3001,
    O = "srgb",
    N = "srgb-linear",
    F = 35044,
    k = [
      "00",
      "01",
      "02",
      "03",
      "04",
      "05",
      "06",
      "07",
      "08",
      "09",
      "0a",
      "0b",
      "0c",
      "0d",
      "0e",
      "0f",
      "10",
      "11",
      "12",
      "13",
      "14",
      "15",
      "16",
      "17",
      "18",
      "19",
      "1a",
      "1b",
      "1c",
      "1d",
      "1e",
      "1f",
      "20",
      "21",
      "22",
      "23",
      "24",
      "25",
      "26",
      "27",
      "28",
      "29",
      "2a",
      "2b",
      "2c",
      "2d",
      "2e",
      "2f",
      "30",
      "31",
      "32",
      "33",
      "34",
      "35",
      "36",
      "37",
      "38",
      "39",
      "3a",
      "3b",
      "3c",
      "3d",
      "3e",
      "3f",
      "40",
      "41",
      "42",
      "43",
      "44",
      "45",
      "46",
      "47",
      "48",
      "49",
      "4a",
      "4b",
      "4c",
      "4d",
      "4e",
      "4f",
      "50",
      "51",
      "52",
      "53",
      "54",
      "55",
      "56",
      "57",
      "58",
      "59",
      "5a",
      "5b",
      "5c",
      "5d",
      "5e",
      "5f",
      "60",
      "61",
      "62",
      "63",
      "64",
      "65",
      "66",
      "67",
      "68",
      "69",
      "6a",
      "6b",
      "6c",
      "6d",
      "6e",
      "6f",
      "70",
      "71",
      "72",
      "73",
      "74",
      "75",
      "76",
      "77",
      "78",
      "79",
      "7a",
      "7b",
      "7c",
      "7d",
      "7e",
      "7f",
      "80",
      "81",
      "82",
      "83",
      "84",
      "85",
      "86",
      "87",
      "88",
      "89",
      "8a",
      "8b",
      "8c",
      "8d",
      "8e",
      "8f",
      "90",
      "91",
      "92",
      "93",
      "94",
      "95",
      "96",
      "97",
      "98",
      "99",
      "9a",
      "9b",
      "9c",
      "9d",
      "9e",
      "9f",
      "a0",
      "a1",
      "a2",
      "a3",
      "a4",
      "a5",
      "a6",
      "a7",
      "a8",
      "a9",
      "aa",
      "ab",
      "ac",
      "ad",
      "ae",
      "af",
      "b0",
      "b1",
      "b2",
      "b3",
      "b4",
      "b5",
      "b6",
      "b7",
      "b8",
      "b9",
      "ba",
      "bb",
      "bc",
      "bd",
      "be",
      "bf",
      "c0",
      "c1",
      "c2",
      "c3",
      "c4",
      "c5",
      "c6",
      "c7",
      "c8",
      "c9",
      "ca",
      "cb",
      "cc",
      "cd",
      "ce",
      "cf",
      "d0",
      "d1",
      "d2",
      "d3",
      "d4",
      "d5",
      "d6",
      "d7",
      "d8",
      "d9",
      "da",
      "db",
      "dc",
      "dd",
      "de",
      "df",
      "e0",
      "e1",
      "e2",
      "e3",
      "e4",
      "e5",
      "e6",
      "e7",
      "e8",
      "e9",
      "ea",
      "eb",
      "ec",
      "ed",
      "ee",
      "ef",
      "f0",
      "f1",
      "f2",
      "f3",
      "f4",
      "f5",
      "f6",
      "f7",
      "f8",
      "f9",
      "fa",
      "fb",
      "fc",
      "fd",
      "fe",
      "ff",
    ],
    G = Math.PI / 180;
  function L() {
    const t = (4294967295 * Math.random()) | 0,
      e = (4294967295 * Math.random()) | 0,
      s = (4294967295 * Math.random()) | 0,
      r = (4294967295 * Math.random()) | 0;
    return (
      k[255 & t] +
      k[(t >> 8) & 255] +
      k[(t >> 16) & 255] +
      k[(t >> 24) & 255] +
      "-" +
      k[255 & e] +
      k[(e >> 8) & 255] +
      "-" +
      k[((e >> 16) & 15) | 64] +
      k[(e >> 24) & 255] +
      "-" +
      k[(63 & s) | 128] +
      k[(s >> 8) & 255] +
      "-" +
      k[(s >> 16) & 255] +
      k[(s >> 24) & 255] +
      k[255 & r] +
      k[(r >> 8) & 255] +
      k[(r >> 16) & 255] +
      k[(r >> 24) & 255]
    ).toLowerCase();
  }
  function I(t, e, s) {
    return Math.max(e, Math.min(s, t));
  }
  function D(t, e, s) {
    return (1 - s) * t + s * e;
  }
  function V(t, e) {
    switch (e.constructor) {
      case Float32Array:
        return t;
      case Uint16Array:
        return t / 65535;
      case Uint8Array:
        return t / 255;
      case Int16Array:
        return Math.max(t / 32767, -1);
      case Int8Array:
        return Math.max(t / 127, -1);
      default:
        throw new Error("Invalid component type.");
    }
  }
  function q(t, e) {
    switch (e.constructor) {
      case Float32Array:
        return t;
      case Uint16Array:
        return Math.round(65535 * t);
      case Uint8Array:
        return Math.round(255 * t);
      case Int16Array:
        return Math.round(32767 * t);
      case Int8Array:
        return Math.round(127 * t);
      default:
        throw new Error("Invalid component type.");
    }
  }
  class U {
    constructor(t = 0, e = 0, s = 0, r = 1) {
      (this.isQuaternion = !0),
        (this._x = t),
        (this._y = e),
        (this._z = s),
        (this._w = r);
    }
    static slerpFlat(t, e, s, r, i, n, h) {
      let a = s[r + 0],
        o = s[r + 1],
        u = s[r + 2],
        l = s[r + 3];
      const c = i[n + 0],
        d = i[n + 1],
        m = i[n + 2],
        y = i[n + 3];
      if (0 === h)
        return (
          (t[e + 0] = a), (t[e + 1] = o), (t[e + 2] = u), void (t[e + 3] = l)
        );
      if (1 === h)
        return (
          (t[e + 0] = c), (t[e + 1] = d), (t[e + 2] = m), void (t[e + 3] = y)
        );
      if (l !== y || a !== c || o !== d || u !== m) {
        let t = 1 - h;
        const e = a * c + o * d + u * m + l * y,
          s = e >= 0 ? 1 : -1,
          r = 1 - e * e;
        if (r > Number.EPSILON) {
          const i = Math.sqrt(r),
            n = Math.atan2(i, e * s);
          (t = Math.sin(t * n) / i), (h = Math.sin(h * n) / i);
        }
        const i = h * s;
        if (
          ((a = a * t + c * i),
          (o = o * t + d * i),
          (u = u * t + m * i),
          (l = l * t + y * i),
          t === 1 - h)
        ) {
          const t = 1 / Math.sqrt(a * a + o * o + u * u + l * l);
          (a *= t), (o *= t), (u *= t), (l *= t);
        }
      }
      (t[e] = a), (t[e + 1] = o), (t[e + 2] = u), (t[e + 3] = l);
    }
    static multiplyQuaternionsFlat(t, e, s, r, i, n) {
      const h = s[r],
        a = s[r + 1],
        o = s[r + 2],
        u = s[r + 3],
        l = i[n],
        c = i[n + 1],
        d = i[n + 2],
        m = i[n + 3];
      return (
        (t[e] = h * m + u * l + a * d - o * c),
        (t[e + 1] = a * m + u * c + o * l - h * d),
        (t[e + 2] = o * m + u * d + h * c - a * l),
        (t[e + 3] = u * m - h * l - a * c - o * d),
        t
      );
    }
    get x() {
      return this._x;
    }
    set x(t) {
      (this._x = t), this._onChangeCallback();
    }
    get y() {
      return this._y;
    }
    set y(t) {
      (this._y = t), this._onChangeCallback();
    }
    get z() {
      return this._z;
    }
    set z(t) {
      (this._z = t), this._onChangeCallback();
    }
    get w() {
      return this._w;
    }
    set w(t) {
      (this._w = t), this._onChangeCallback();
    }
    set(t, e, s, r) {
      return (
        (this._x = t),
        (this._y = e),
        (this._z = s),
        (this._w = r),
        this._onChangeCallback(),
        this
      );
    }
    clone() {
      return new this.constructor(this._x, this._y, this._z, this._w);
    }
    copy(t) {
      return (
        (this._x = t.x),
        (this._y = t.y),
        (this._z = t.z),
        (this._w = t.w),
        this._onChangeCallback(),
        this
      );
    }
    setFromEuler(t, e) {
      const s = t._x,
        r = t._y,
        i = t._z,
        n = t._order,
        h = Math.cos,
        a = Math.sin,
        o = h(s / 2),
        u = h(r / 2),
        l = h(i / 2),
        c = a(s / 2),
        d = a(r / 2),
        m = a(i / 2);
      switch (n) {
        case "XYZ":
          (this._x = c * u * l + o * d * m),
            (this._y = o * d * l - c * u * m),
            (this._z = o * u * m + c * d * l),
            (this._w = o * u * l - c * d * m);
          break;
        case "YXZ":
          (this._x = c * u * l + o * d * m),
            (this._y = o * d * l - c * u * m),
            (this._z = o * u * m - c * d * l),
            (this._w = o * u * l + c * d * m);
          break;
        case "ZXY":
          (this._x = c * u * l - o * d * m),
            (this._y = o * d * l + c * u * m),
            (this._z = o * u * m + c * d * l),
            (this._w = o * u * l - c * d * m);
          break;
        case "ZYX":
          (this._x = c * u * l - o * d * m),
            (this._y = o * d * l + c * u * m),
            (this._z = o * u * m - c * d * l),
            (this._w = o * u * l + c * d * m);
          break;
        case "YZX":
          (this._x = c * u * l + o * d * m),
            (this._y = o * d * l + c * u * m),
            (this._z = o * u * m - c * d * l),
            (this._w = o * u * l - c * d * m);
          break;
        case "XZY":
          (this._x = c * u * l - o * d * m),
            (this._y = o * d * l - c * u * m),
            (this._z = o * u * m + c * d * l),
            (this._w = o * u * l + c * d * m);
          break;
        default:
          console.warn(
            "THREE.Quaternion: .setFromEuler() encountered an unknown order: " +
              n
          );
      }
      return !1 !== e && this._onChangeCallback(), this;
    }
    setFromAxisAngle(t, e) {
      const s = e / 2,
        r = Math.sin(s);
      return (
        (this._x = t.x * r),
        (this._y = t.y * r),
        (this._z = t.z * r),
        (this._w = Math.cos(s)),
        this._onChangeCallback(),
        this
      );
    }
    setFromRotationMatrix(t) {
      const e = t.elements,
        s = e[0],
        r = e[4],
        i = e[8],
        n = e[1],
        h = e[5],
        a = e[9],
        o = e[2],
        u = e[6],
        l = e[10],
        c = s + h + l;
      if (c > 0) {
        const t = 0.5 / Math.sqrt(c + 1);
        (this._w = 0.25 / t),
          (this._x = (u - a) * t),
          (this._y = (i - o) * t),
          (this._z = (n - r) * t);
      } else if (s > h && s > l) {
        const t = 2 * Math.sqrt(1 + s - h - l);
        (this._w = (u - a) / t),
          (this._x = 0.25 * t),
          (this._y = (r + n) / t),
          (this._z = (i + o) / t);
      } else if (h > l) {
        const t = 2 * Math.sqrt(1 + h - s - l);
        (this._w = (i - o) / t),
          (this._x = (r + n) / t),
          (this._y = 0.25 * t),
          (this._z = (a + u) / t);
      } else {
        const t = 2 * Math.sqrt(1 + l - s - h);
        (this._w = (n - r) / t),
          (this._x = (i + o) / t),
          (this._y = (a + u) / t),
          (this._z = 0.25 * t);
      }
      return this._onChangeCallback(), this;
    }
    setFromUnitVectors(t, e) {
      let s = t.dot(e) + 1;
      return (
        s < Number.EPSILON
          ? ((s = 0),
            Math.abs(t.x) > Math.abs(t.z)
              ? ((this._x = -t.y),
                (this._y = t.x),
                (this._z = 0),
                (this._w = s))
              : ((this._x = 0),
                (this._y = -t.z),
                (this._z = t.y),
                (this._w = s)))
          : ((this._x = t.y * e.z - t.z * e.y),
            (this._y = t.z * e.x - t.x * e.z),
            (this._z = t.x * e.y - t.y * e.x),
            (this._w = s)),
        this.normalize()
      );
    }
    angleTo(t) {
      return 2 * Math.acos(Math.abs(I(this.dot(t), -1, 1)));
    }
    rotateTowards(t, e) {
      const s = this.angleTo(t);
      if (0 === s) return this;
      const r = Math.min(1, e / s);
      return this.slerp(t, r), this;
    }
    identity() {
      return this.set(0, 0, 0, 1);
    }
    invert() {
      return this.conjugate();
    }
    conjugate() {
      return (
        (this._x *= -1),
        (this._y *= -1),
        (this._z *= -1),
        this._onChangeCallback(),
        this
      );
    }
    dot(t) {
      return this._x * t._x + this._y * t._y + this._z * t._z + this._w * t._w;
    }
    lengthSq() {
      return (
        this._x * this._x +
        this._y * this._y +
        this._z * this._z +
        this._w * this._w
      );
    }
    length() {
      return Math.sqrt(
        this._x * this._x +
          this._y * this._y +
          this._z * this._z +
          this._w * this._w
      );
    }
    normalize() {
      let t = this.length();
      return (
        0 === t
          ? ((this._x = 0), (this._y = 0), (this._z = 0), (this._w = 1))
          : ((t = 1 / t),
            (this._x = this._x * t),
            (this._y = this._y * t),
            (this._z = this._z * t),
            (this._w = this._w * t)),
        this._onChangeCallback(),
        this
      );
    }
    multiply(t) {
      return this.multiplyQuaternions(this, t);
    }
    premultiply(t) {
      return this.multiplyQuaternions(t, this);
    }
    multiplyQuaternions(t, e) {
      const s = t._x,
        r = t._y,
        i = t._z,
        n = t._w,
        h = e._x,
        a = e._y,
        o = e._z,
        u = e._w;
      return (
        (this._x = s * u + n * h + r * o - i * a),
        (this._y = r * u + n * a + i * h - s * o),
        (this._z = i * u + n * o + s * a - r * h),
        (this._w = n * u - s * h - r * a - i * o),
        this._onChangeCallback(),
        this
      );
    }
    slerp(t, e) {
      if (0 === e) return this;
      if (1 === e) return this.copy(t);
      const s = this._x,
        r = this._y,
        i = this._z,
        n = this._w;
      let h = n * t._w + s * t._x + r * t._y + i * t._z;
      if (
        (h < 0
          ? ((this._w = -t._w),
            (this._x = -t._x),
            (this._y = -t._y),
            (this._z = -t._z),
            (h = -h))
          : this.copy(t),
        h >= 1)
      )
        return (this._w = n), (this._x = s), (this._y = r), (this._z = i), this;
      const a = 1 - h * h;
      if (a <= Number.EPSILON) {
        const t = 1 - e;
        return (
          (this._w = t * n + e * this._w),
          (this._x = t * s + e * this._x),
          (this._y = t * r + e * this._y),
          (this._z = t * i + e * this._z),
          this.normalize(),
          this._onChangeCallback(),
          this
        );
      }
      const o = Math.sqrt(a),
        u = Math.atan2(o, h),
        l = Math.sin((1 - e) * u) / o,
        c = Math.sin(e * u) / o;
      return (
        (this._w = n * l + this._w * c),
        (this._x = s * l + this._x * c),
        (this._y = r * l + this._y * c),
        (this._z = i * l + this._z * c),
        this._onChangeCallback(),
        this
      );
    }
    slerpQuaternions(t, e, s) {
      return this.copy(t).slerp(e, s);
    }
    random() {
      const t = Math.random(),
        e = Math.sqrt(1 - t),
        s = Math.sqrt(t),
        r = 2 * Math.PI * Math.random(),
        i = 2 * Math.PI * Math.random();
      return this.set(
        e * Math.cos(r),
        s * Math.sin(i),
        s * Math.cos(i),
        e * Math.sin(r)
      );
    }
    equals(t) {
      return (
        t._x === this._x &&
        t._y === this._y &&
        t._z === this._z &&
        t._w === this._w
      );
    }
    fromArray(t, e = 0) {
      return (
        (this._x = t[e]),
        (this._y = t[e + 1]),
        (this._z = t[e + 2]),
        (this._w = t[e + 3]),
        this._onChangeCallback(),
        this
      );
    }
    toArray(t = [], e = 0) {
      return (
        (t[e] = this._x),
        (t[e + 1] = this._y),
        (t[e + 2] = this._z),
        (t[e + 3] = this._w),
        t
      );
    }
    fromBufferAttribute(t, e) {
      return (
        (this._x = t.getX(e)),
        (this._y = t.getY(e)),
        (this._z = t.getZ(e)),
        (this._w = t.getW(e)),
        this
      );
    }
    _onChange(t) {
      return (this._onChangeCallback = t), this;
    }
    _onChangeCallback() {}
    *[Symbol.iterator]() {
      yield this._x, yield this._y, yield this._z, yield this._w;
    }
  }
  class J {
    constructor(t = 0, e = 0, s = 0) {
      (J.prototype.isVector3 = !0), (this.x = t), (this.y = e), (this.z = s);
    }
    set(t, e, s) {
      return (
        void 0 === s && (s = this.z),
        (this.x = t),
        (this.y = e),
        (this.z = s),
        this
      );
    }
    setScalar(t) {
      return (this.x = t), (this.y = t), (this.z = t), this;
    }
    setX(t) {
      return (this.x = t), this;
    }
    setY(t) {
      return (this.y = t), this;
    }
    setZ(t) {
      return (this.z = t), this;
    }
    setComponent(t, e) {
      switch (t) {
        case 0:
          this.x = e;
          break;
        case 1:
          this.y = e;
          break;
        case 2:
          this.z = e;
          break;
        default:
          throw new Error("index is out of range: " + t);
      }
      return this;
    }
    getComponent(t) {
      switch (t) {
        case 0:
          return this.x;
        case 1:
          return this.y;
        case 2:
          return this.z;
        default:
          throw new Error("index is out of range: " + t);
      }
    }
    clone() {
      return new this.constructor(this.x, this.y, this.z);
    }
    copy(t) {
      return (this.x = t.x), (this.y = t.y), (this.z = t.z), this;
    }
    add(t) {
      return (this.x += t.x), (this.y += t.y), (this.z += t.z), this;
    }
    addScalar(t) {
      return (this.x += t), (this.y += t), (this.z += t), this;
    }
    addVectors(t, e) {
      return (
        (this.x = t.x + e.x), (this.y = t.y + e.y), (this.z = t.z + e.z), this
      );
    }
    addScaledVector(t, e) {
      return (
        (this.x += t.x * e), (this.y += t.y * e), (this.z += t.z * e), this
      );
    }
    sub(t) {
      return (this.x -= t.x), (this.y -= t.y), (this.z -= t.z), this;
    }
    subScalar(t) {
      return (this.x -= t), (this.y -= t), (this.z -= t), this;
    }
    subVectors(t, e) {
      return (
        (this.x = t.x - e.x), (this.y = t.y - e.y), (this.z = t.z - e.z), this
      );
    }
    multiply(t) {
      return (this.x *= t.x), (this.y *= t.y), (this.z *= t.z), this;
    }
    multiplyScalar(t) {
      return (this.x *= t), (this.y *= t), (this.z *= t), this;
    }
    multiplyVectors(t, e) {
      return (
        (this.x = t.x * e.x), (this.y = t.y * e.y), (this.z = t.z * e.z), this
      );
    }
    applyEuler(t) {
      return this.applyQuaternion(X.setFromEuler(t));
    }
    applyAxisAngle(t, e) {
      return this.applyQuaternion(X.setFromAxisAngle(t, e));
    }
    applyMatrix3(t) {
      const e = this.x,
        s = this.y,
        r = this.z,
        i = t.elements;
      return (
        (this.x = i[0] * e + i[3] * s + i[6] * r),
        (this.y = i[1] * e + i[4] * s + i[7] * r),
        (this.z = i[2] * e + i[5] * s + i[8] * r),
        this
      );
    }
    applyNormalMatrix(t) {
      return this.applyMatrix3(t).normalize();
    }
    applyMatrix4(t) {
      const e = this.x,
        s = this.y,
        r = this.z,
        i = t.elements,
        n = 1 / (i[3] * e + i[7] * s + i[11] * r + i[15]);
      return (
        (this.x = (i[0] * e + i[4] * s + i[8] * r + i[12]) * n),
        (this.y = (i[1] * e + i[5] * s + i[9] * r + i[13]) * n),
        (this.z = (i[2] * e + i[6] * s + i[10] * r + i[14]) * n),
        this
      );
    }
    applyQuaternion(t) {
      const e = this.x,
        s = this.y,
        r = this.z,
        i = t.x,
        n = t.y,
        h = t.z,
        a = t.w,
        o = a * e + n * r - h * s,
        u = a * s + h * e - i * r,
        l = a * r + i * s - n * e,
        c = -i * e - n * s - h * r;
      return (
        (this.x = o * a + c * -i + u * -h - l * -n),
        (this.y = u * a + c * -n + l * -i - o * -h),
        (this.z = l * a + c * -h + o * -n - u * -i),
        this
      );
    }
    project(t) {
      return this.applyMatrix4(t.matrixWorldInverse).applyMatrix4(
        t.projectionMatrix
      );
    }
    unproject(t) {
      return this.applyMatrix4(t.projectionMatrixInverse).applyMatrix4(
        t.matrixWorld
      );
    }
    transformDirection(t) {
      const e = this.x,
        s = this.y,
        r = this.z,
        i = t.elements;
      return (
        (this.x = i[0] * e + i[4] * s + i[8] * r),
        (this.y = i[1] * e + i[5] * s + i[9] * r),
        (this.z = i[2] * e + i[6] * s + i[10] * r),
        this.normalize()
      );
    }
    divide(t) {
      return (this.x /= t.x), (this.y /= t.y), (this.z /= t.z), this;
    }
    divideScalar(t) {
      return this.multiplyScalar(1 / t);
    }
    min(t) {
      return (
        (this.x = Math.min(this.x, t.x)),
        (this.y = Math.min(this.y, t.y)),
        (this.z = Math.min(this.z, t.z)),
        this
      );
    }
    max(t) {
      return (
        (this.x = Math.max(this.x, t.x)),
        (this.y = Math.max(this.y, t.y)),
        (this.z = Math.max(this.z, t.z)),
        this
      );
    }
    clamp(t, e) {
      return (
        (this.x = Math.max(t.x, Math.min(e.x, this.x))),
        (this.y = Math.max(t.y, Math.min(e.y, this.y))),
        (this.z = Math.max(t.z, Math.min(e.z, this.z))),
        this
      );
    }
    clampScalar(t, e) {
      return (
        (this.x = Math.max(t, Math.min(e, this.x))),
        (this.y = Math.max(t, Math.min(e, this.y))),
        (this.z = Math.max(t, Math.min(e, this.z))),
        this
      );
    }
    clampLength(t, e) {
      const s = this.length();
      return this.divideScalar(s || 1).multiplyScalar(
        Math.max(t, Math.min(e, s))
      );
    }
    floor() {
      return (
        (this.x = Math.floor(this.x)),
        (this.y = Math.floor(this.y)),
        (this.z = Math.floor(this.z)),
        this
      );
    }
    ceil() {
      return (
        (this.x = Math.ceil(this.x)),
        (this.y = Math.ceil(this.y)),
        (this.z = Math.ceil(this.z)),
        this
      );
    }
    round() {
      return (
        (this.x = Math.round(this.x)),
        (this.y = Math.round(this.y)),
        (this.z = Math.round(this.z)),
        this
      );
    }
    roundToZero() {
      return (
        (this.x = this.x < 0 ? Math.ceil(this.x) : Math.floor(this.x)),
        (this.y = this.y < 0 ? Math.ceil(this.y) : Math.floor(this.y)),
        (this.z = this.z < 0 ? Math.ceil(this.z) : Math.floor(this.z)),
        this
      );
    }
    negate() {
      return (this.x = -this.x), (this.y = -this.y), (this.z = -this.z), this;
    }
    dot(t) {
      return this.x * t.x + this.y * t.y + this.z * t.z;
    }
    lengthSq() {
      return this.x * this.x + this.y * this.y + this.z * this.z;
    }
    length() {
      return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
    }
    manhattanLength() {
      return Math.abs(this.x) + Math.abs(this.y) + Math.abs(this.z);
    }
    normalize() {
      return this.divideScalar(this.length() || 1);
    }
    setLength(t) {
      return this.normalize().multiplyScalar(t);
    }
    lerp(t, e) {
      return (
        (this.x += (t.x - this.x) * e),
        (this.y += (t.y - this.y) * e),
        (this.z += (t.z - this.z) * e),
        this
      );
    }
    lerpVectors(t, e, s) {
      return (
        (this.x = t.x + (e.x - t.x) * s),
        (this.y = t.y + (e.y - t.y) * s),
        (this.z = t.z + (e.z - t.z) * s),
        this
      );
    }
    cross(t) {
      return this.crossVectors(this, t);
    }
    crossVectors(t, e) {
      const s = t.x,
        r = t.y,
        i = t.z,
        n = e.x,
        h = e.y,
        a = e.z;
      return (
        (this.x = r * a - i * h),
        (this.y = i * n - s * a),
        (this.z = s * h - r * n),
        this
      );
    }
    projectOnVector(t) {
      const e = t.lengthSq();
      if (0 === e) return this.set(0, 0, 0);
      const s = t.dot(this) / e;
      return this.copy(t).multiplyScalar(s);
    }
    projectOnPlane(t) {
      return H.copy(this).projectOnVector(t), this.sub(H);
    }
    reflect(t) {
      return this.sub(H.copy(t).multiplyScalar(2 * this.dot(t)));
    }
    angleTo(t) {
      const e = Math.sqrt(this.lengthSq() * t.lengthSq());
      if (0 === e) return Math.PI / 2;
      const s = this.dot(t) / e;
      return Math.acos(I(s, -1, 1));
    }
    distanceTo(t) {
      return Math.sqrt(this.distanceToSquared(t));
    }
    distanceToSquared(t) {
      const e = this.x - t.x,
        s = this.y - t.y,
        r = this.z - t.z;
      return e * e + s * s + r * r;
    }
    manhattanDistanceTo(t) {
      return (
        Math.abs(this.x - t.x) + Math.abs(this.y - t.y) + Math.abs(this.z - t.z)
      );
    }
    setFromSpherical(t) {
      return this.setFromSphericalCoords(t.radius, t.phi, t.theta);
    }
    setFromSphericalCoords(t, e, s) {
      const r = Math.sin(e) * t;
      return (
        (this.x = r * Math.sin(s)),
        (this.y = Math.cos(e) * t),
        (this.z = r * Math.cos(s)),
        this
      );
    }
    setFromCylindrical(t) {
      return this.setFromCylindricalCoords(t.radius, t.theta, t.y);
    }
    setFromCylindricalCoords(t, e, s) {
      return (
        (this.x = t * Math.sin(e)),
        (this.y = s),
        (this.z = t * Math.cos(e)),
        this
      );
    }
    setFromMatrixPosition(t) {
      const e = t.elements;
      return (this.x = e[12]), (this.y = e[13]), (this.z = e[14]), this;
    }
    setFromMatrixScale(t) {
      const e = this.setFromMatrixColumn(t, 0).length(),
        s = this.setFromMatrixColumn(t, 1).length(),
        r = this.setFromMatrixColumn(t, 2).length();
      return (this.x = e), (this.y = s), (this.z = r), this;
    }
    setFromMatrixColumn(t, e) {
      return this.fromArray(t.elements, 4 * e);
    }
    setFromMatrix3Column(t, e) {
      return this.fromArray(t.elements, 3 * e);
    }
    setFromEuler(t) {
      return (this.x = t._x), (this.y = t._y), (this.z = t._z), this;
    }
    equals(t) {
      return t.x === this.x && t.y === this.y && t.z === this.z;
    }
    fromArray(t, e = 0) {
      return (this.x = t[e]), (this.y = t[e + 1]), (this.z = t[e + 2]), this;
    }
    toArray(t = [], e = 0) {
      return (t[e] = this.x), (t[e + 1] = this.y), (t[e + 2] = this.z), t;
    }
    fromBufferAttribute(t, e) {
      return (
        (this.x = t.getX(e)), (this.y = t.getY(e)), (this.z = t.getZ(e)), this
      );
    }
    random() {
      return (
        (this.x = Math.random()),
        (this.y = Math.random()),
        (this.z = Math.random()),
        this
      );
    }
    randomDirection() {
      const t = 2 * (Math.random() - 0.5),
        e = Math.random() * Math.PI * 2,
        s = Math.sqrt(1 - t ** 2);
      return (
        (this.x = s * Math.cos(e)),
        (this.y = s * Math.sin(e)),
        (this.z = t),
        this
      );
    }
    *[Symbol.iterator]() {
      yield this.x, yield this.y, yield this.z;
    }
  }
  const H = new J(),
    X = new U();
  class W {
    constructor() {
      (W.prototype.isMatrix4 = !0),
        (this.elements = [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]);
    }
    set(t, e, s, r, i, n, h, a, o, u, l, c, d, m, y, p) {
      const x = this.elements;
      return (
        (x[0] = t),
        (x[4] = e),
        (x[8] = s),
        (x[12] = r),
        (x[1] = i),
        (x[5] = n),
        (x[9] = h),
        (x[13] = a),
        (x[2] = o),
        (x[6] = u),
        (x[10] = l),
        (x[14] = c),
        (x[3] = d),
        (x[7] = m),
        (x[11] = y),
        (x[15] = p),
        this
      );
    }
    identity() {
      return this.set(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1), this;
    }
    clone() {
      return new W().fromArray(this.elements);
    }
    copy(t) {
      const e = this.elements,
        s = t.elements;
      return (
        (e[0] = s[0]),
        (e[1] = s[1]),
        (e[2] = s[2]),
        (e[3] = s[3]),
        (e[4] = s[4]),
        (e[5] = s[5]),
        (e[6] = s[6]),
        (e[7] = s[7]),
        (e[8] = s[8]),
        (e[9] = s[9]),
        (e[10] = s[10]),
        (e[11] = s[11]),
        (e[12] = s[12]),
        (e[13] = s[13]),
        (e[14] = s[14]),
        (e[15] = s[15]),
        this
      );
    }
    copyPosition(t) {
      const e = this.elements,
        s = t.elements;
      return (e[12] = s[12]), (e[13] = s[13]), (e[14] = s[14]), this;
    }
    setFromMatrix3(t) {
      const e = t.elements;
      return (
        this.set(
          e[0],
          e[3],
          e[6],
          0,
          e[1],
          e[4],
          e[7],
          0,
          e[2],
          e[5],
          e[8],
          0,
          0,
          0,
          0,
          1
        ),
        this
      );
    }
    extractBasis(t, e, s) {
      return (
        t.setFromMatrixColumn(this, 0),
        e.setFromMatrixColumn(this, 1),
        s.setFromMatrixColumn(this, 2),
        this
      );
    }
    makeBasis(t, e, s) {
      return (
        this.set(
          t.x,
          e.x,
          s.x,
          0,
          t.y,
          e.y,
          s.y,
          0,
          t.z,
          e.z,
          s.z,
          0,
          0,
          0,
          0,
          1
        ),
        this
      );
    }
    extractRotation(t) {
      const e = this.elements,
        s = t.elements,
        r = 1 / Z.setFromMatrixColumn(t, 0).length(),
        i = 1 / Z.setFromMatrixColumn(t, 1).length(),
        n = 1 / Z.setFromMatrixColumn(t, 2).length();
      return (
        (e[0] = s[0] * r),
        (e[1] = s[1] * r),
        (e[2] = s[2] * r),
        (e[3] = 0),
        (e[4] = s[4] * i),
        (e[5] = s[5] * i),
        (e[6] = s[6] * i),
        (e[7] = 0),
        (e[8] = s[8] * n),
        (e[9] = s[9] * n),
        (e[10] = s[10] * n),
        (e[11] = 0),
        (e[12] = 0),
        (e[13] = 0),
        (e[14] = 0),
        (e[15] = 1),
        this
      );
    }
    makeRotationFromEuler(t) {
      const e = this.elements,
        s = t.x,
        r = t.y,
        i = t.z,
        n = Math.cos(s),
        h = Math.sin(s),
        a = Math.cos(r),
        o = Math.sin(r),
        u = Math.cos(i),
        l = Math.sin(i);
      if ("XYZ" === t.order) {
        const t = n * u,
          s = n * l,
          r = h * u,
          i = h * l;
        (e[0] = a * u),
          (e[4] = -a * l),
          (e[8] = o),
          (e[1] = s + r * o),
          (e[5] = t - i * o),
          (e[9] = -h * a),
          (e[2] = i - t * o),
          (e[6] = r + s * o),
          (e[10] = n * a);
      } else if ("YXZ" === t.order) {
        const t = a * u,
          s = a * l,
          r = o * u,
          i = o * l;
        (e[0] = t + i * h),
          (e[4] = r * h - s),
          (e[8] = n * o),
          (e[1] = n * l),
          (e[5] = n * u),
          (e[9] = -h),
          (e[2] = s * h - r),
          (e[6] = i + t * h),
          (e[10] = n * a);
      } else if ("ZXY" === t.order) {
        const t = a * u,
          s = a * l,
          r = o * u,
          i = o * l;
        (e[0] = t - i * h),
          (e[4] = -n * l),
          (e[8] = r + s * h),
          (e[1] = s + r * h),
          (e[5] = n * u),
          (e[9] = i - t * h),
          (e[2] = -n * o),
          (e[6] = h),
          (e[10] = n * a);
      } else if ("ZYX" === t.order) {
        const t = n * u,
          s = n * l,
          r = h * u,
          i = h * l;
        (e[0] = a * u),
          (e[4] = r * o - s),
          (e[8] = t * o + i),
          (e[1] = a * l),
          (e[5] = i * o + t),
          (e[9] = s * o - r),
          (e[2] = -o),
          (e[6] = h * a),
          (e[10] = n * a);
      } else if ("YZX" === t.order) {
        const t = n * a,
          s = n * o,
          r = h * a,
          i = h * o;
        (e[0] = a * u),
          (e[4] = i - t * l),
          (e[8] = r * l + s),
          (e[1] = l),
          (e[5] = n * u),
          (e[9] = -h * u),
          (e[2] = -o * u),
          (e[6] = s * l + r),
          (e[10] = t - i * l);
      } else if ("XZY" === t.order) {
        const t = n * a,
          s = n * o,
          r = h * a,
          i = h * o;
        (e[0] = a * u),
          (e[4] = -l),
          (e[8] = o * u),
          (e[1] = t * l + i),
          (e[5] = n * u),
          (e[9] = s * l - r),
          (e[2] = r * l - s),
          (e[6] = h * u),
          (e[10] = i * l + t);
      }
      return (
        (e[3] = 0),
        (e[7] = 0),
        (e[11] = 0),
        (e[12] = 0),
        (e[13] = 0),
        (e[14] = 0),
        (e[15] = 1),
        this
      );
    }
    makeRotationFromQuaternion(t) {
      return this.compose(j, t, $);
    }
    lookAt(t, e, s) {
      const r = this.elements;
      return (
        tt.subVectors(t, e),
        0 === tt.lengthSq() && (tt.z = 1),
        tt.normalize(),
        K.crossVectors(s, tt),
        0 === K.lengthSq() &&
          (1 === Math.abs(s.z) ? (tt.x += 1e-4) : (tt.z += 1e-4),
          tt.normalize(),
          K.crossVectors(s, tt)),
        K.normalize(),
        Q.crossVectors(tt, K),
        (r[0] = K.x),
        (r[4] = Q.x),
        (r[8] = tt.x),
        (r[1] = K.y),
        (r[5] = Q.y),
        (r[9] = tt.y),
        (r[2] = K.z),
        (r[6] = Q.z),
        (r[10] = tt.z),
        this
      );
    }
    multiply(t) {
      return this.multiplyMatrices(this, t);
    }
    premultiply(t) {
      return this.multiplyMatrices(t, this);
    }
    multiplyMatrices(t, e) {
      const s = t.elements,
        r = e.elements,
        i = this.elements,
        n = s[0],
        h = s[4],
        a = s[8],
        o = s[12],
        u = s[1],
        l = s[5],
        c = s[9],
        d = s[13],
        m = s[2],
        y = s[6],
        p = s[10],
        x = s[14],
        f = s[3],
        g = s[7],
        b = s[11],
        S = s[15],
        M = r[0],
        w = r[4],
        _ = r[8],
        v = r[12],
        A = r[1],
        z = r[5],
        C = r[9],
        B = r[13],
        R = r[2],
        E = r[6],
        P = r[10],
        T = r[14],
        O = r[3],
        N = r[7],
        F = r[11],
        k = r[15];
      return (
        (i[0] = n * M + h * A + a * R + o * O),
        (i[4] = n * w + h * z + a * E + o * N),
        (i[8] = n * _ + h * C + a * P + o * F),
        (i[12] = n * v + h * B + a * T + o * k),
        (i[1] = u * M + l * A + c * R + d * O),
        (i[5] = u * w + l * z + c * E + d * N),
        (i[9] = u * _ + l * C + c * P + d * F),
        (i[13] = u * v + l * B + c * T + d * k),
        (i[2] = m * M + y * A + p * R + x * O),
        (i[6] = m * w + y * z + p * E + x * N),
        (i[10] = m * _ + y * C + p * P + x * F),
        (i[14] = m * v + y * B + p * T + x * k),
        (i[3] = f * M + g * A + b * R + S * O),
        (i[7] = f * w + g * z + b * E + S * N),
        (i[11] = f * _ + g * C + b * P + S * F),
        (i[15] = f * v + g * B + b * T + S * k),
        this
      );
    }
    multiplyScalar(t) {
      const e = this.elements;
      return (
        (e[0] *= t),
        (e[4] *= t),
        (e[8] *= t),
        (e[12] *= t),
        (e[1] *= t),
        (e[5] *= t),
        (e[9] *= t),
        (e[13] *= t),
        (e[2] *= t),
        (e[6] *= t),
        (e[10] *= t),
        (e[14] *= t),
        (e[3] *= t),
        (e[7] *= t),
        (e[11] *= t),
        (e[15] *= t),
        this
      );
    }
    determinant() {
      const t = this.elements,
        e = t[0],
        s = t[4],
        r = t[8],
        i = t[12],
        n = t[1],
        h = t[5],
        a = t[9],
        o = t[13],
        u = t[2],
        l = t[6],
        c = t[10],
        d = t[14];
      return (
        t[3] *
          (+i * a * l -
            r * o * l -
            i * h * c +
            s * o * c +
            r * h * d -
            s * a * d) +
        t[7] *
          (+e * a * d -
            e * o * c +
            i * n * c -
            r * n * d +
            r * o * u -
            i * a * u) +
        t[11] *
          (+e * o * l -
            e * h * d -
            i * n * l +
            s * n * d +
            i * h * u -
            s * o * u) +
        t[15] *
          (-r * h * u -
            e * a * l +
            e * h * c +
            r * n * l -
            s * n * c +
            s * a * u)
      );
    }
    transpose() {
      const t = this.elements;
      let e;
      return (
        (e = t[1]),
        (t[1] = t[4]),
        (t[4] = e),
        (e = t[2]),
        (t[2] = t[8]),
        (t[8] = e),
        (e = t[6]),
        (t[6] = t[9]),
        (t[9] = e),
        (e = t[3]),
        (t[3] = t[12]),
        (t[12] = e),
        (e = t[7]),
        (t[7] = t[13]),
        (t[13] = e),
        (e = t[11]),
        (t[11] = t[14]),
        (t[14] = e),
        this
      );
    }
    setPosition(t, e, s) {
      const r = this.elements;
      return (
        t.isVector3
          ? ((r[12] = t.x), (r[13] = t.y), (r[14] = t.z))
          : ((r[12] = t), (r[13] = e), (r[14] = s)),
        this
      );
    }
    invert() {
      const t = this.elements,
        e = t[0],
        s = t[1],
        r = t[2],
        i = t[3],
        n = t[4],
        h = t[5],
        a = t[6],
        o = t[7],
        u = t[8],
        l = t[9],
        c = t[10],
        d = t[11],
        m = t[12],
        y = t[13],
        p = t[14],
        x = t[15],
        f =
          l * p * o - y * c * o + y * a * d - h * p * d - l * a * x + h * c * x,
        g =
          m * c * o - u * p * o - m * a * d + n * p * d + u * a * x - n * c * x,
        b =
          u * y * o - m * l * o + m * h * d - n * y * d - u * h * x + n * l * x,
        S =
          m * l * a - u * y * a - m * h * c + n * y * c + u * h * p - n * l * p,
        M = e * f + s * g + r * b + i * S;
      if (0 === M)
        return this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
      const w = 1 / M;
      return (
        (t[0] = f * w),
        (t[1] =
          (y * c * i -
            l * p * i -
            y * r * d +
            s * p * d +
            l * r * x -
            s * c * x) *
          w),
        (t[2] =
          (h * p * i -
            y * a * i +
            y * r * o -
            s * p * o -
            h * r * x +
            s * a * x) *
          w),
        (t[3] =
          (l * a * i -
            h * c * i -
            l * r * o +
            s * c * o +
            h * r * d -
            s * a * d) *
          w),
        (t[4] = g * w),
        (t[5] =
          (u * p * i -
            m * c * i +
            m * r * d -
            e * p * d -
            u * r * x +
            e * c * x) *
          w),
        (t[6] =
          (m * a * i -
            n * p * i -
            m * r * o +
            e * p * o +
            n * r * x -
            e * a * x) *
          w),
        (t[7] =
          (n * c * i -
            u * a * i +
            u * r * o -
            e * c * o -
            n * r * d +
            e * a * d) *
          w),
        (t[8] = b * w),
        (t[9] =
          (m * l * i -
            u * y * i -
            m * s * d +
            e * y * d +
            u * s * x -
            e * l * x) *
          w),
        (t[10] =
          (n * y * i -
            m * h * i +
            m * s * o -
            e * y * o -
            n * s * x +
            e * h * x) *
          w),
        (t[11] =
          (u * h * i -
            n * l * i -
            u * s * o +
            e * l * o +
            n * s * d -
            e * h * d) *
          w),
        (t[12] = S * w),
        (t[13] =
          (u * y * r -
            m * l * r +
            m * s * c -
            e * y * c -
            u * s * p +
            e * l * p) *
          w),
        (t[14] =
          (m * h * r -
            n * y * r -
            m * s * a +
            e * y * a +
            n * s * p -
            e * h * p) *
          w),
        (t[15] =
          (n * l * r -
            u * h * r +
            u * s * a -
            e * l * a -
            n * s * c +
            e * h * c) *
          w),
        this
      );
    }
    scale(t) {
      const e = this.elements,
        s = t.x,
        r = t.y,
        i = t.z;
      return (
        (e[0] *= s),
        (e[4] *= r),
        (e[8] *= i),
        (e[1] *= s),
        (e[5] *= r),
        (e[9] *= i),
        (e[2] *= s),
        (e[6] *= r),
        (e[10] *= i),
        (e[3] *= s),
        (e[7] *= r),
        (e[11] *= i),
        this
      );
    }
    getMaxScaleOnAxis() {
      const t = this.elements,
        e = t[0] * t[0] + t[1] * t[1] + t[2] * t[2],
        s = t[4] * t[4] + t[5] * t[5] + t[6] * t[6],
        r = t[8] * t[8] + t[9] * t[9] + t[10] * t[10];
      return Math.sqrt(Math.max(e, s, r));
    }
    makeTranslation(t, e, s) {
      return this.set(1, 0, 0, t, 0, 1, 0, e, 0, 0, 1, s, 0, 0, 0, 1), this;
    }
    makeRotationX(t) {
      const e = Math.cos(t),
        s = Math.sin(t);
      return this.set(1, 0, 0, 0, 0, e, -s, 0, 0, s, e, 0, 0, 0, 0, 1), this;
    }
    makeRotationY(t) {
      const e = Math.cos(t),
        s = Math.sin(t);
      return this.set(e, 0, s, 0, 0, 1, 0, 0, -s, 0, e, 0, 0, 0, 0, 1), this;
    }
    makeRotationZ(t) {
      const e = Math.cos(t),
        s = Math.sin(t);
      return this.set(e, -s, 0, 0, s, e, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1), this;
    }
    makeRotationAxis(t, e) {
      const s = Math.cos(e),
        r = Math.sin(e),
        i = 1 - s,
        n = t.x,
        h = t.y,
        a = t.z,
        o = i * n,
        u = i * h;
      return (
        this.set(
          o * n + s,
          o * h - r * a,
          o * a + r * h,
          0,
          o * h + r * a,
          u * h + s,
          u * a - r * n,
          0,
          o * a - r * h,
          u * a + r * n,
          i * a * a + s,
          0,
          0,
          0,
          0,
          1
        ),
        this
      );
    }
    makeScale(t, e, s) {
      return this.set(t, 0, 0, 0, 0, e, 0, 0, 0, 0, s, 0, 0, 0, 0, 1), this;
    }
    makeShear(t, e, s, r, i, n) {
      return this.set(1, s, i, 0, t, 1, n, 0, e, r, 1, 0, 0, 0, 0, 1), this;
    }
    compose(t, e, s) {
      const r = this.elements,
        i = e._x,
        n = e._y,
        h = e._z,
        a = e._w,
        o = i + i,
        u = n + n,
        l = h + h,
        c = i * o,
        d = i * u,
        m = i * l,
        y = n * u,
        p = n * l,
        x = h * l,
        f = a * o,
        g = a * u,
        b = a * l,
        S = s.x,
        M = s.y,
        w = s.z;
      return (
        (r[0] = (1 - (y + x)) * S),
        (r[1] = (d + b) * S),
        (r[2] = (m - g) * S),
        (r[3] = 0),
        (r[4] = (d - b) * M),
        (r[5] = (1 - (c + x)) * M),
        (r[6] = (p + f) * M),
        (r[7] = 0),
        (r[8] = (m + g) * w),
        (r[9] = (p - f) * w),
        (r[10] = (1 - (c + y)) * w),
        (r[11] = 0),
        (r[12] = t.x),
        (r[13] = t.y),
        (r[14] = t.z),
        (r[15] = 1),
        this
      );
    }
    decompose(t, e, s) {
      const r = this.elements;
      let i = Z.set(r[0], r[1], r[2]).length();
      const n = Z.set(r[4], r[5], r[6]).length(),
        h = Z.set(r[8], r[9], r[10]).length();
      this.determinant() < 0 && (i = -i),
        (t.x = r[12]),
        (t.y = r[13]),
        (t.z = r[14]),
        Y.copy(this);
      const a = 1 / i,
        o = 1 / n,
        u = 1 / h;
      return (
        (Y.elements[0] *= a),
        (Y.elements[1] *= a),
        (Y.elements[2] *= a),
        (Y.elements[4] *= o),
        (Y.elements[5] *= o),
        (Y.elements[6] *= o),
        (Y.elements[8] *= u),
        (Y.elements[9] *= u),
        (Y.elements[10] *= u),
        e.setFromRotationMatrix(Y),
        (s.x = i),
        (s.y = n),
        (s.z = h),
        this
      );
    }
    makePerspective(t, e, s, r, i, n) {
      const h = this.elements,
        a = (2 * i) / (e - t),
        o = (2 * i) / (s - r),
        u = (e + t) / (e - t),
        l = (s + r) / (s - r),
        c = -(n + i) / (n - i),
        d = (-2 * n * i) / (n - i);
      return (
        (h[0] = a),
        (h[4] = 0),
        (h[8] = u),
        (h[12] = 0),
        (h[1] = 0),
        (h[5] = o),
        (h[9] = l),
        (h[13] = 0),
        (h[2] = 0),
        (h[6] = 0),
        (h[10] = c),
        (h[14] = d),
        (h[3] = 0),
        (h[7] = 0),
        (h[11] = -1),
        (h[15] = 0),
        this
      );
    }
    makeOrthographic(t, e, s, r, i, n) {
      const h = this.elements,
        a = 1 / (e - t),
        o = 1 / (s - r),
        u = 1 / (n - i),
        l = (e + t) * a,
        c = (s + r) * o,
        d = (n + i) * u;
      return (
        (h[0] = 2 * a),
        (h[4] = 0),
        (h[8] = 0),
        (h[12] = -l),
        (h[1] = 0),
        (h[5] = 2 * o),
        (h[9] = 0),
        (h[13] = -c),
        (h[2] = 0),
        (h[6] = 0),
        (h[10] = -2 * u),
        (h[14] = -d),
        (h[3] = 0),
        (h[7] = 0),
        (h[11] = 0),
        (h[15] = 1),
        this
      );
    }
    equals(t) {
      const e = this.elements,
        s = t.elements;
      for (let t = 0; t < 16; t++) if (e[t] !== s[t]) return !1;
      return !0;
    }
    fromArray(t, e = 0) {
      for (let s = 0; s < 16; s++) this.elements[s] = t[s + e];
      return this;
    }
    toArray(t = [], e = 0) {
      const s = this.elements;
      return (
        (t[e] = s[0]),
        (t[e + 1] = s[1]),
        (t[e + 2] = s[2]),
        (t[e + 3] = s[3]),
        (t[e + 4] = s[4]),
        (t[e + 5] = s[5]),
        (t[e + 6] = s[6]),
        (t[e + 7] = s[7]),
        (t[e + 8] = s[8]),
        (t[e + 9] = s[9]),
        (t[e + 10] = s[10]),
        (t[e + 11] = s[11]),
        (t[e + 12] = s[12]),
        (t[e + 13] = s[13]),
        (t[e + 14] = s[14]),
        (t[e + 15] = s[15]),
        t
      );
    }
  }
  const Z = new J(),
    Y = new W(),
    j = new J(0, 0, 0),
    $ = new J(1, 1, 1),
    K = new J(),
    Q = new J(),
    tt = new J();
  class et {
    addEventListener(t, e) {
      void 0 === this._listeners && (this._listeners = {});
      const s = this._listeners;
      void 0 === s[t] && (s[t] = []), -1 === s[t].indexOf(e) && s[t].push(e);
    }
    hasEventListener(t, e) {
      if (void 0 === this._listeners) return !1;
      const s = this._listeners;
      return void 0 !== s[t] && -1 !== s[t].indexOf(e);
    }
    removeEventListener(t, e) {
      if (void 0 === this._listeners) return;
      const s = this._listeners[t];
      if (void 0 !== s) {
        const t = s.indexOf(e);
        -1 !== t && s.splice(t, 1);
      }
    }
    dispatchEvent(t) {
      if (void 0 === this._listeners) return;
      const e = this._listeners[t.type];
      if (void 0 !== e) {
        t.target = this;
        const s = e.slice(0);
        for (let e = 0, r = s.length; e < r; e++) s[e].call(this, t);
        t.target = null;
      }
    }
  }
  const st = new W(),
    rt = new U();
  class it {
    constructor(t = 0, e = 0, s = 0, r = it.DefaultOrder) {
      (this.isEuler = !0),
        (this._x = t),
        (this._y = e),
        (this._z = s),
        (this._order = r);
    }
    get x() {
      return this._x;
    }
    set x(t) {
      (this._x = t), this._onChangeCallback();
    }
    get y() {
      return this._y;
    }
    set y(t) {
      (this._y = t), this._onChangeCallback();
    }
    get z() {
      return this._z;
    }
    set z(t) {
      (this._z = t), this._onChangeCallback();
    }
    get order() {
      return this._order;
    }
    set order(t) {
      (this._order = t), this._onChangeCallback();
    }
    set(t, e, s, r = this._order) {
      return (
        (this._x = t),
        (this._y = e),
        (this._z = s),
        (this._order = r),
        this._onChangeCallback(),
        this
      );
    }
    clone() {
      return new this.constructor(this._x, this._y, this._z, this._order);
    }
    copy(t) {
      return (
        (this._x = t._x),
        (this._y = t._y),
        (this._z = t._z),
        (this._order = t._order),
        this._onChangeCallback(),
        this
      );
    }
    setFromRotationMatrix(t, e = this._order, s = !0) {
      const r = t.elements,
        i = r[0],
        n = r[4],
        h = r[8],
        a = r[1],
        o = r[5],
        u = r[9],
        l = r[2],
        c = r[6],
        d = r[10];
      switch (e) {
        case "XYZ":
          (this._y = Math.asin(I(h, -1, 1))),
            Math.abs(h) < 0.9999999
              ? ((this._x = Math.atan2(-u, d)), (this._z = Math.atan2(-n, i)))
              : ((this._x = Math.atan2(c, o)), (this._z = 0));
          break;
        case "YXZ":
          (this._x = Math.asin(-I(u, -1, 1))),
            Math.abs(u) < 0.9999999
              ? ((this._y = Math.atan2(h, d)), (this._z = Math.atan2(a, o)))
              : ((this._y = Math.atan2(-l, i)), (this._z = 0));
          break;
        case "ZXY":
          (this._x = Math.asin(I(c, -1, 1))),
            Math.abs(c) < 0.9999999
              ? ((this._y = Math.atan2(-l, d)), (this._z = Math.atan2(-n, o)))
              : ((this._y = 0), (this._z = Math.atan2(a, i)));
          break;
        case "ZYX":
          (this._y = Math.asin(-I(l, -1, 1))),
            Math.abs(l) < 0.9999999
              ? ((this._x = Math.atan2(c, d)), (this._z = Math.atan2(a, i)))
              : ((this._x = 0), (this._z = Math.atan2(-n, o)));
          break;
        case "YZX":
          (this._z = Math.asin(I(a, -1, 1))),
            Math.abs(a) < 0.9999999
              ? ((this._x = Math.atan2(-u, o)), (this._y = Math.atan2(-l, i)))
              : ((this._x = 0), (this._y = Math.atan2(h, d)));
          break;
        case "XZY":
          (this._z = Math.asin(-I(n, -1, 1))),
            Math.abs(n) < 0.9999999
              ? ((this._x = Math.atan2(c, o)), (this._y = Math.atan2(h, i)))
              : ((this._x = Math.atan2(-u, d)), (this._y = 0));
          break;
        default:
          console.warn(
            "THREE.Euler: .setFromRotationMatrix() encountered an unknown order: " +
              e
          );
      }
      return (this._order = e), !0 === s && this._onChangeCallback(), this;
    }
    setFromQuaternion(t, e, s) {
      return (
        st.makeRotationFromQuaternion(t), this.setFromRotationMatrix(st, e, s)
      );
    }
    setFromVector3(t, e = this._order) {
      return this.set(t.x, t.y, t.z, e);
    }
    reorder(t) {
      return rt.setFromEuler(this), this.setFromQuaternion(rt, t);
    }
    equals(t) {
      return (
        t._x === this._x &&
        t._y === this._y &&
        t._z === this._z &&
        t._order === this._order
      );
    }
    fromArray(t) {
      return (
        (this._x = t[0]),
        (this._y = t[1]),
        (this._z = t[2]),
        void 0 !== t[3] && (this._order = t[3]),
        this._onChangeCallback(),
        this
      );
    }
    toArray(t = [], e = 0) {
      return (
        (t[e] = this._x),
        (t[e + 1] = this._y),
        (t[e + 2] = this._z),
        (t[e + 3] = this._order),
        t
      );
    }
    _onChange(t) {
      return (this._onChangeCallback = t), this;
    }
    _onChangeCallback() {}
    *[Symbol.iterator]() {
      yield this._x, yield this._y, yield this._z, yield this._order;
    }
    toVector3() {
      console.error(
        "THREE.Euler: .toVector3() has been removed. Use Vector3.setFromEuler() instead"
      );
    }
  }
  (it.DefaultOrder = "XYZ"),
    (it.RotationOrders = ["XYZ", "YZX", "ZXY", "XZY", "YXZ", "ZYX"]);
  class nt {
    constructor() {
      this.mask = 1;
    }
    set(t) {
      this.mask = ((1 << t) | 0) >>> 0;
    }
    enable(t) {
      this.mask |= (1 << t) | 0;
    }
    enableAll() {
      this.mask = -1;
    }
    toggle(t) {
      this.mask ^= (1 << t) | 0;
    }
    disable(t) {
      this.mask &= ~((1 << t) | 0);
    }
    disableAll() {
      this.mask = 0;
    }
    test(t) {
      return 0 != (this.mask & t.mask);
    }
    isEnabled(t) {
      return 0 != (this.mask & ((1 << t) | 0));
    }
  }
  class ht {
    constructor() {
      (ht.prototype.isMatrix3 = !0),
        (this.elements = [1, 0, 0, 0, 1, 0, 0, 0, 1]);
    }
    set(t, e, s, r, i, n, h, a, o) {
      const u = this.elements;
      return (
        (u[0] = t),
        (u[1] = r),
        (u[2] = h),
        (u[3] = e),
        (u[4] = i),
        (u[5] = a),
        (u[6] = s),
        (u[7] = n),
        (u[8] = o),
        this
      );
    }
    identity() {
      return this.set(1, 0, 0, 0, 1, 0, 0, 0, 1), this;
    }
    copy(t) {
      const e = this.elements,
        s = t.elements;
      return (
        (e[0] = s[0]),
        (e[1] = s[1]),
        (e[2] = s[2]),
        (e[3] = s[3]),
        (e[4] = s[4]),
        (e[5] = s[5]),
        (e[6] = s[6]),
        (e[7] = s[7]),
        (e[8] = s[8]),
        this
      );
    }
    extractBasis(t, e, s) {
      return (
        t.setFromMatrix3Column(this, 0),
        e.setFromMatrix3Column(this, 1),
        s.setFromMatrix3Column(this, 2),
        this
      );
    }
    setFromMatrix4(t) {
      const e = t.elements;
      return (
        this.set(e[0], e[4], e[8], e[1], e[5], e[9], e[2], e[6], e[10]), this
      );
    }
    multiply(t) {
      return this.multiplyMatrices(this, t);
    }
    premultiply(t) {
      return this.multiplyMatrices(t, this);
    }
    multiplyMatrices(t, e) {
      const s = t.elements,
        r = e.elements,
        i = this.elements,
        n = s[0],
        h = s[3],
        a = s[6],
        o = s[1],
        u = s[4],
        l = s[7],
        c = s[2],
        d = s[5],
        m = s[8],
        y = r[0],
        p = r[3],
        x = r[6],
        f = r[1],
        g = r[4],
        b = r[7],
        S = r[2],
        M = r[5],
        w = r[8];
      return (
        (i[0] = n * y + h * f + a * S),
        (i[3] = n * p + h * g + a * M),
        (i[6] = n * x + h * b + a * w),
        (i[1] = o * y + u * f + l * S),
        (i[4] = o * p + u * g + l * M),
        (i[7] = o * x + u * b + l * w),
        (i[2] = c * y + d * f + m * S),
        (i[5] = c * p + d * g + m * M),
        (i[8] = c * x + d * b + m * w),
        this
      );
    }
    multiplyScalar(t) {
      const e = this.elements;
      return (
        (e[0] *= t),
        (e[3] *= t),
        (e[6] *= t),
        (e[1] *= t),
        (e[4] *= t),
        (e[7] *= t),
        (e[2] *= t),
        (e[5] *= t),
        (e[8] *= t),
        this
      );
    }
    determinant() {
      const t = this.elements,
        e = t[0],
        s = t[1],
        r = t[2],
        i = t[3],
        n = t[4],
        h = t[5],
        a = t[6],
        o = t[7],
        u = t[8];
      return (
        e * n * u - e * h * o - s * i * u + s * h * a + r * i * o - r * n * a
      );
    }
    invert() {
      const t = this.elements,
        e = t[0],
        s = t[1],
        r = t[2],
        i = t[3],
        n = t[4],
        h = t[5],
        a = t[6],
        o = t[7],
        u = t[8],
        l = u * n - h * o,
        c = h * a - u * i,
        d = o * i - n * a,
        m = e * l + s * c + r * d;
      if (0 === m) return this.set(0, 0, 0, 0, 0, 0, 0, 0, 0);
      const y = 1 / m;
      return (
        (t[0] = l * y),
        (t[1] = (r * o - u * s) * y),
        (t[2] = (h * s - r * n) * y),
        (t[3] = c * y),
        (t[4] = (u * e - r * a) * y),
        (t[5] = (r * i - h * e) * y),
        (t[6] = d * y),
        (t[7] = (s * a - o * e) * y),
        (t[8] = (n * e - s * i) * y),
        this
      );
    }
    transpose() {
      let t;
      const e = this.elements;
      return (
        (t = e[1]),
        (e[1] = e[3]),
        (e[3] = t),
        (t = e[2]),
        (e[2] = e[6]),
        (e[6] = t),
        (t = e[5]),
        (e[5] = e[7]),
        (e[7] = t),
        this
      );
    }
    getNormalMatrix(t) {
      return this.setFromMatrix4(t).invert().transpose();
    }
    transposeIntoArray(t) {
      const e = this.elements;
      return (
        (t[0] = e[0]),
        (t[1] = e[3]),
        (t[2] = e[6]),
        (t[3] = e[1]),
        (t[4] = e[4]),
        (t[5] = e[7]),
        (t[6] = e[2]),
        (t[7] = e[5]),
        (t[8] = e[8]),
        this
      );
    }
    setUvTransform(t, e, s, r, i, n, h) {
      const a = Math.cos(i),
        o = Math.sin(i);
      return (
        this.set(
          s * a,
          s * o,
          -s * (a * n + o * h) + n + t,
          -r * o,
          r * a,
          -r * (-o * n + a * h) + h + e,
          0,
          0,
          1
        ),
        this
      );
    }
    scale(t, e) {
      const s = this.elements;
      return (
        (s[0] *= t),
        (s[3] *= t),
        (s[6] *= t),
        (s[1] *= e),
        (s[4] *= e),
        (s[7] *= e),
        this
      );
    }
    rotate(t) {
      const e = Math.cos(t),
        s = Math.sin(t),
        r = this.elements,
        i = r[0],
        n = r[3],
        h = r[6],
        a = r[1],
        o = r[4],
        u = r[7];
      return (
        (r[0] = e * i + s * a),
        (r[3] = e * n + s * o),
        (r[6] = e * h + s * u),
        (r[1] = -s * i + e * a),
        (r[4] = -s * n + e * o),
        (r[7] = -s * h + e * u),
        this
      );
    }
    translate(t, e) {
      const s = this.elements;
      return (
        (s[0] += t * s[2]),
        (s[3] += t * s[5]),
        (s[6] += t * s[8]),
        (s[1] += e * s[2]),
        (s[4] += e * s[5]),
        (s[7] += e * s[8]),
        this
      );
    }
    equals(t) {
      const e = this.elements,
        s = t.elements;
      for (let t = 0; t < 9; t++) if (e[t] !== s[t]) return !1;
      return !0;
    }
    fromArray(t, e = 0) {
      for (let s = 0; s < 9; s++) this.elements[s] = t[s + e];
      return this;
    }
    toArray(t = [], e = 0) {
      const s = this.elements;
      return (
        (t[e] = s[0]),
        (t[e + 1] = s[1]),
        (t[e + 2] = s[2]),
        (t[e + 3] = s[3]),
        (t[e + 4] = s[4]),
        (t[e + 5] = s[5]),
        (t[e + 6] = s[6]),
        (t[e + 7] = s[7]),
        (t[e + 8] = s[8]),
        t
      );
    }
    clone() {
      return new this.constructor().fromArray(this.elements);
    }
  }
  let at = 0;
  const ot = new J(),
    ut = new U(),
    lt = new W(),
    ct = new J(),
    dt = new J(),
    mt = new J(),
    yt = new U(),
    pt = new J(1, 0, 0),
    xt = new J(0, 1, 0),
    ft = new J(0, 0, 1),
    gt = { type: "added" },
    bt = { type: "removed" };
  class St extends et {
    constructor() {
      super(),
        (this.isObject3D = !0),
        Object.defineProperty(this, "id", { value: at++ }),
        (this.uuid = L()),
        (this.name = ""),
        (this.type = "Object3D"),
        (this.parent = null),
        (this.children = []),
        (this.up = St.DefaultUp.clone());
      const t = new J(),
        e = new it(),
        s = new U(),
        r = new J(1, 1, 1);
      e._onChange(function () {
        s.setFromEuler(e, !1);
      }),
        s._onChange(function () {
          e.setFromQuaternion(s, void 0, !1);
        }),
        Object.defineProperties(this, {
          position: { configurable: !0, enumerable: !0, value: t },
          rotation: { configurable: !0, enumerable: !0, value: e },
          quaternion: { configurable: !0, enumerable: !0, value: s },
          scale: { configurable: !0, enumerable: !0, value: r },
          modelViewMatrix: { value: new W() },
          normalMatrix: { value: new ht() },
        }),
        (this.matrix = new W()),
        (this.matrixWorld = new W()),
        (this.matrixAutoUpdate = St.DefaultMatrixAutoUpdate),
        (this.matrixWorldNeedsUpdate = !1),
        (this.matrixWorldAutoUpdate = St.DefaultMatrixWorldAutoUpdate),
        (this.layers = new nt()),
        (this.visible = !0),
        (this.castShadow = !1),
        (this.receiveShadow = !1),
        (this.frustumCulled = !0),
        (this.renderOrder = 0),
        (this.animations = []),
        (this.userData = {});
    }
    onBeforeRender() {}
    onAfterRender() {}
    applyMatrix4(t) {
      this.matrixAutoUpdate && this.updateMatrix(),
        this.matrix.premultiply(t),
        this.matrix.decompose(this.position, this.quaternion, this.scale);
    }
    applyQuaternion(t) {
      return this.quaternion.premultiply(t), this;
    }
    setRotationFromAxisAngle(t, e) {
      this.quaternion.setFromAxisAngle(t, e);
    }
    setRotationFromEuler(t) {
      this.quaternion.setFromEuler(t, !0);
    }
    setRotationFromMatrix(t) {
      this.quaternion.setFromRotationMatrix(t);
    }
    setRotationFromQuaternion(t) {
      this.quaternion.copy(t);
    }
    rotateOnAxis(t, e) {
      return ut.setFromAxisAngle(t, e), this.quaternion.multiply(ut), this;
    }
    rotateOnWorldAxis(t, e) {
      return ut.setFromAxisAngle(t, e), this.quaternion.premultiply(ut), this;
    }
    rotateX(t) {
      return this.rotateOnAxis(pt, t);
    }
    rotateY(t) {
      return this.rotateOnAxis(xt, t);
    }
    rotateZ(t) {
      return this.rotateOnAxis(ft, t);
    }
    translateOnAxis(t, e) {
      return (
        ot.copy(t).applyQuaternion(this.quaternion),
        this.position.add(ot.multiplyScalar(e)),
        this
      );
    }
    translateX(t) {
      return this.translateOnAxis(pt, t);
    }
    translateY(t) {
      return this.translateOnAxis(xt, t);
    }
    translateZ(t) {
      return this.translateOnAxis(ft, t);
    }
    localToWorld(t) {
      return t.applyMatrix4(this.matrixWorld);
    }
    worldToLocal(t) {
      return t.applyMatrix4(lt.copy(this.matrixWorld).invert());
    }
    lookAt(t, e, s) {
      t.isVector3 ? ct.copy(t) : ct.set(t, e, s);
      const r = this.parent;
      this.updateWorldMatrix(!0, !1),
        dt.setFromMatrixPosition(this.matrixWorld),
        this.isCamera || this.isLight
          ? lt.lookAt(dt, ct, this.up)
          : lt.lookAt(ct, dt, this.up),
        this.quaternion.setFromRotationMatrix(lt),
        r &&
          (lt.extractRotation(r.matrixWorld),
          ut.setFromRotationMatrix(lt),
          this.quaternion.premultiply(ut.invert()));
    }
    add(t) {
      if (arguments.length > 1) {
        for (let t = 0; t < arguments.length; t++) this.add(arguments[t]);
        return this;
      }
      return t === this
        ? (console.error(
            "THREE.Object3D.add: object can't be added as a child of itself.",
            t
          ),
          this)
        : (t && t.isObject3D
            ? (null !== t.parent && t.parent.remove(t),
              (t.parent = this),
              this.children.push(t),
              t.dispatchEvent(gt))
            : console.error(
                "THREE.Object3D.add: object not an instance of THREE.Object3D.",
                t
              ),
          this);
    }
    remove(t) {
      if (arguments.length > 1) {
        for (let t = 0; t < arguments.length; t++) this.remove(arguments[t]);
        return this;
      }
      const e = this.children.indexOf(t);
      return (
        -1 !== e &&
          ((t.parent = null), this.children.splice(e, 1), t.dispatchEvent(bt)),
        this
      );
    }
    removeFromParent() {
      const t = this.parent;
      return null !== t && t.remove(this), this;
    }
    clear() {
      for (let t = 0; t < this.children.length; t++) {
        const e = this.children[t];
        (e.parent = null), e.dispatchEvent(bt);
      }
      return (this.children.length = 0), this;
    }
    attach(t) {
      return (
        this.updateWorldMatrix(!0, !1),
        lt.copy(this.matrixWorld).invert(),
        null !== t.parent &&
          (t.parent.updateWorldMatrix(!0, !1),
          lt.multiply(t.parent.matrixWorld)),
        t.applyMatrix4(lt),
        this.add(t),
        t.updateWorldMatrix(!1, !0),
        this
      );
    }
    getObjectById(t) {
      return this.getObjectByProperty("id", t);
    }
    getObjectByName(t) {
      return this.getObjectByProperty("name", t);
    }
    getObjectByProperty(t, e) {
      if (this[t] === e) return this;
      for (let s = 0, r = this.children.length; s < r; s++) {
        const r = this.children[s].getObjectByProperty(t, e);
        if (void 0 !== r) return r;
      }
    }
    getWorldPosition(t) {
      return (
        this.updateWorldMatrix(!0, !1),
        t.setFromMatrixPosition(this.matrixWorld)
      );
    }
    getWorldQuaternion(t) {
      return (
        this.updateWorldMatrix(!0, !1), this.matrixWorld.decompose(dt, t, mt), t
      );
    }
    getWorldScale(t) {
      return (
        this.updateWorldMatrix(!0, !1), this.matrixWorld.decompose(dt, yt, t), t
      );
    }
    getWorldDirection(t) {
      this.updateWorldMatrix(!0, !1);
      const e = this.matrixWorld.elements;
      return t.set(e[8], e[9], e[10]).normalize();
    }
    raycast() {}
    traverse(t) {
      t(this);
      const e = this.children;
      for (let s = 0, r = e.length; s < r; s++) e[s].traverse(t);
    }
    traverseVisible(t) {
      if (!1 === this.visible) return;
      t(this);
      const e = this.children;
      for (let s = 0, r = e.length; s < r; s++) e[s].traverseVisible(t);
    }
    traverseAncestors(t) {
      const e = this.parent;
      null !== e && (t(e), e.traverseAncestors(t));
    }
    updateMatrix() {
      this.matrix.compose(this.position, this.quaternion, this.scale),
        (this.matrixWorldNeedsUpdate = !0);
    }
    updateMatrixWorld(t) {
      this.matrixAutoUpdate && this.updateMatrix(),
        (this.matrixWorldNeedsUpdate || t) &&
          (null === this.parent
            ? this.matrixWorld.copy(this.matrix)
            : this.matrixWorld.multiplyMatrices(
                this.parent.matrixWorld,
                this.matrix
              ),
          (this.matrixWorldNeedsUpdate = !1),
          (t = !0));
      const e = this.children;
      for (let s = 0, r = e.length; s < r; s++) {
        const r = e[s];
        (!0 !== r.matrixWorldAutoUpdate && !0 !== t) || r.updateMatrixWorld(t);
      }
    }
    updateWorldMatrix(t, e) {
      const s = this.parent;
      if (
        (!0 === t &&
          null !== s &&
          !0 === s.matrixWorldAutoUpdate &&
          s.updateWorldMatrix(!0, !1),
        this.matrixAutoUpdate && this.updateMatrix(),
        null === this.parent
          ? this.matrixWorld.copy(this.matrix)
          : this.matrixWorld.multiplyMatrices(
              this.parent.matrixWorld,
              this.matrix
            ),
        !0 === e)
      ) {
        const t = this.children;
        for (let e = 0, s = t.length; e < s; e++) {
          const s = t[e];
          !0 === s.matrixWorldAutoUpdate && s.updateWorldMatrix(!1, !0);
        }
      }
    }
    toJSON(t) {
      const e = void 0 === t || "string" == typeof t,
        s = {};
      e &&
        ((t = {
          geometries: {},
          materials: {},
          textures: {},
          images: {},
          shapes: {},
          skeletons: {},
          animations: {},
          nodes: {},
        }),
        (s.metadata = {
          version: 4.5,
          type: "Object",
          generator: "Object3D.toJSON",
        }));
      const r = {};
      function i(e, s) {
        return void 0 === e[s.uuid] && (e[s.uuid] = s.toJSON(t)), s.uuid;
      }
      if (
        ((r.uuid = this.uuid),
        (r.type = this.type),
        "" !== this.name && (r.name = this.name),
        !0 === this.castShadow && (r.castShadow = !0),
        !0 === this.receiveShadow && (r.receiveShadow = !0),
        !1 === this.visible && (r.visible = !1),
        !1 === this.frustumCulled && (r.frustumCulled = !1),
        0 !== this.renderOrder && (r.renderOrder = this.renderOrder),
        "{}" !== JSON.stringify(this.userData) && (r.userData = this.userData),
        (r.layers = this.layers.mask),
        (r.matrix = this.matrix.toArray()),
        !1 === this.matrixAutoUpdate && (r.matrixAutoUpdate = !1),
        this.isInstancedMesh &&
          ((r.type = "InstancedMesh"),
          (r.count = this.count),
          (r.instanceMatrix = this.instanceMatrix.toJSON()),
          null !== this.instanceColor &&
            (r.instanceColor = this.instanceColor.toJSON())),
        this.isScene)
      )
        this.background &&
          (this.background.isColor
            ? (r.background = this.background.toJSON())
            : this.background.isTexture &&
              (r.background = this.background.toJSON(t).uuid)),
          this.environment &&
            this.environment.isTexture &&
            !0 !== this.environment.isRenderTargetTexture &&
            (r.environment = this.environment.toJSON(t).uuid);
      else if (this.isMesh || this.isLine || this.isPoints) {
        r.geometry = i(t.geometries, this.geometry);
        const e = this.geometry.parameters;
        if (void 0 !== e && void 0 !== e.shapes) {
          const s = e.shapes;
          if (Array.isArray(s))
            for (let e = 0, r = s.length; e < r; e++) {
              const r = s[e];
              i(t.shapes, r);
            }
          else i(t.shapes, s);
        }
      }
      if (
        (this.isSkinnedMesh &&
          ((r.bindMode = this.bindMode),
          (r.bindMatrix = this.bindMatrix.toArray()),
          void 0 !== this.skeleton &&
            (i(t.skeletons, this.skeleton), (r.skeleton = this.skeleton.uuid))),
        void 0 !== this.material)
      )
        if (Array.isArray(this.material)) {
          const e = [];
          for (let s = 0, r = this.material.length; s < r; s++)
            e.push(i(t.materials, this.material[s]));
          r.material = e;
        } else r.material = i(t.materials, this.material);
      if (this.children.length > 0) {
        r.children = [];
        for (let e = 0; e < this.children.length; e++)
          r.children.push(this.children[e].toJSON(t).object);
      }
      if (this.animations.length > 0) {
        r.animations = [];
        for (let e = 0; e < this.animations.length; e++) {
          const s = this.animations[e];
          r.animations.push(i(t.animations, s));
        }
      }
      if (e) {
        const e = n(t.geometries),
          r = n(t.materials),
          i = n(t.textures),
          h = n(t.images),
          a = n(t.shapes),
          o = n(t.skeletons),
          u = n(t.animations),
          l = n(t.nodes);
        e.length > 0 && (s.geometries = e),
          r.length > 0 && (s.materials = r),
          i.length > 0 && (s.textures = i),
          h.length > 0 && (s.images = h),
          a.length > 0 && (s.shapes = a),
          o.length > 0 && (s.skeletons = o),
          u.length > 0 && (s.animations = u),
          l.length > 0 && (s.nodes = l);
      }
      return (s.object = r), s;
      function n(t) {
        const e = [];
        for (const s in t) {
          const r = t[s];
          delete r.metadata, e.push(r);
        }
        return e;
      }
    }
    clone(t) {
      return new this.constructor().copy(this, t);
    }
    copy(t, e = !0) {
      if (
        ((this.name = t.name),
        this.up.copy(t.up),
        this.position.copy(t.position),
        (this.rotation.order = t.rotation.order),
        this.quaternion.copy(t.quaternion),
        this.scale.copy(t.scale),
        this.matrix.copy(t.matrix),
        this.matrixWorld.copy(t.matrixWorld),
        (this.matrixAutoUpdate = t.matrixAutoUpdate),
        (this.matrixWorldNeedsUpdate = t.matrixWorldNeedsUpdate),
        (this.matrixWorldAutoUpdate = t.matrixWorldAutoUpdate),
        (this.layers.mask = t.layers.mask),
        (this.visible = t.visible),
        (this.castShadow = t.castShadow),
        (this.receiveShadow = t.receiveShadow),
        (this.frustumCulled = t.frustumCulled),
        (this.renderOrder = t.renderOrder),
        (this.userData = JSON.parse(JSON.stringify(t.userData))),
        !0 === e)
      )
        for (let e = 0; e < t.children.length; e++) {
          const s = t.children[e];
          this.add(s.clone());
        }
      return this;
    }
  }
  (St.DefaultUp = new J(0, 1, 0)),
    (St.DefaultMatrixAutoUpdate = !0),
    (St.DefaultMatrixWorldAutoUpdate = !0);
  class Mt {
    constructor(t = 0, e = 0) {
      (Mt.prototype.isVector2 = !0), (this.x = t), (this.y = e);
    }
    get width() {
      return this.x;
    }
    set width(t) {
      this.x = t;
    }
    get height() {
      return this.y;
    }
    set height(t) {
      this.y = t;
    }
    set(t, e) {
      return (this.x = t), (this.y = e), this;
    }
    setScalar(t) {
      return (this.x = t), (this.y = t), this;
    }
    setX(t) {
      return (this.x = t), this;
    }
    setY(t) {
      return (this.y = t), this;
    }
    setComponent(t, e) {
      switch (t) {
        case 0:
          this.x = e;
          break;
        case 1:
          this.y = e;
          break;
        default:
          throw new Error("index is out of range: " + t);
      }
      return this;
    }
    getComponent(t) {
      switch (t) {
        case 0:
          return this.x;
        case 1:
          return this.y;
        default:
          throw new Error("index is out of range: " + t);
      }
    }
    clone() {
      return new this.constructor(this.x, this.y);
    }
    copy(t) {
      return (this.x = t.x), (this.y = t.y), this;
    }
    add(t) {
      return (this.x += t.x), (this.y += t.y), this;
    }
    addScalar(t) {
      return (this.x += t), (this.y += t), this;
    }
    addVectors(t, e) {
      return (this.x = t.x + e.x), (this.y = t.y + e.y), this;
    }
    addScaledVector(t, e) {
      return (this.x += t.x * e), (this.y += t.y * e), this;
    }
    sub(t) {
      return (this.x -= t.x), (this.y -= t.y), this;
    }
    subScalar(t) {
      return (this.x -= t), (this.y -= t), this;
    }
    subVectors(t, e) {
      return (this.x = t.x - e.x), (this.y = t.y - e.y), this;
    }
    multiply(t) {
      return (this.x *= t.x), (this.y *= t.y), this;
    }
    multiplyScalar(t) {
      return (this.x *= t), (this.y *= t), this;
    }
    divide(t) {
      return (this.x /= t.x), (this.y /= t.y), this;
    }
    divideScalar(t) {
      return this.multiplyScalar(1 / t);
    }
    applyMatrix3(t) {
      const e = this.x,
        s = this.y,
        r = t.elements;
      return (
        (this.x = r[0] * e + r[3] * s + r[6]),
        (this.y = r[1] * e + r[4] * s + r[7]),
        this
      );
    }
    min(t) {
      return (
        (this.x = Math.min(this.x, t.x)), (this.y = Math.min(this.y, t.y)), this
      );
    }
    max(t) {
      return (
        (this.x = Math.max(this.x, t.x)), (this.y = Math.max(this.y, t.y)), this
      );
    }
    clamp(t, e) {
      return (
        (this.x = Math.max(t.x, Math.min(e.x, this.x))),
        (this.y = Math.max(t.y, Math.min(e.y, this.y))),
        this
      );
    }
    clampScalar(t, e) {
      return (
        (this.x = Math.max(t, Math.min(e, this.x))),
        (this.y = Math.max(t, Math.min(e, this.y))),
        this
      );
    }
    clampLength(t, e) {
      const s = this.length();
      return this.divideScalar(s || 1).multiplyScalar(
        Math.max(t, Math.min(e, s))
      );
    }
    floor() {
      return (this.x = Math.floor(this.x)), (this.y = Math.floor(this.y)), this;
    }
    ceil() {
      return (this.x = Math.ceil(this.x)), (this.y = Math.ceil(this.y)), this;
    }
    round() {
      return (this.x = Math.round(this.x)), (this.y = Math.round(this.y)), this;
    }
    roundToZero() {
      return (
        (this.x = this.x < 0 ? Math.ceil(this.x) : Math.floor(this.x)),
        (this.y = this.y < 0 ? Math.ceil(this.y) : Math.floor(this.y)),
        this
      );
    }
    negate() {
      return (this.x = -this.x), (this.y = -this.y), this;
    }
    dot(t) {
      return this.x * t.x + this.y * t.y;
    }
    cross(t) {
      return this.x * t.y - this.y * t.x;
    }
    lengthSq() {
      return this.x * this.x + this.y * this.y;
    }
    length() {
      return Math.sqrt(this.x * this.x + this.y * this.y);
    }
    manhattanLength() {
      return Math.abs(this.x) + Math.abs(this.y);
    }
    normalize() {
      return this.divideScalar(this.length() || 1);
    }
    angle() {
      return Math.atan2(-this.y, -this.x) + Math.PI;
    }
    distanceTo(t) {
      return Math.sqrt(this.distanceToSquared(t));
    }
    distanceToSquared(t) {
      const e = this.x - t.x,
        s = this.y - t.y;
      return e * e + s * s;
    }
    manhattanDistanceTo(t) {
      return Math.abs(this.x - t.x) + Math.abs(this.y - t.y);
    }
    setLength(t) {
      return this.normalize().multiplyScalar(t);
    }
    lerp(t, e) {
      return (
        (this.x += (t.x - this.x) * e), (this.y += (t.y - this.y) * e), this
      );
    }
    lerpVectors(t, e, s) {
      return (
        (this.x = t.x + (e.x - t.x) * s), (this.y = t.y + (e.y - t.y) * s), this
      );
    }
    equals(t) {
      return t.x === this.x && t.y === this.y;
    }
    fromArray(t, e = 0) {
      return (this.x = t[e]), (this.y = t[e + 1]), this;
    }
    toArray(t = [], e = 0) {
      return (t[e] = this.x), (t[e + 1] = this.y), t;
    }
    fromBufferAttribute(t, e) {
      return (this.x = t.getX(e)), (this.y = t.getY(e)), this;
    }
    rotateAround(t, e) {
      const s = Math.cos(e),
        r = Math.sin(e),
        i = this.x - t.x,
        n = this.y - t.y;
      return (
        (this.x = i * s - n * r + t.x), (this.y = i * r + n * s + t.y), this
      );
    }
    random() {
      return (this.x = Math.random()), (this.y = Math.random()), this;
    }
    *[Symbol.iterator]() {
      yield this.x, yield this.y;
    }
  }
  function wt(t) {
    return document.createElementNS("http://www.w3.org/1999/xhtml", t);
  }
  function _t(t) {
    return t < 0.04045
      ? 0.0773993808 * t
      : Math.pow(0.9478672986 * t + 0.0521327014, 2.4);
  }
  function vt(t) {
    return t < 0.0031308 ? 12.92 * t : 1.055 * Math.pow(t, 0.41666) - 0.055;
  }
  const At = { [O]: { [N]: _t }, [N]: { [O]: vt } },
    zt = {
      legacyMode: !0,
      get workingColorSpace() {
        return N;
      },
      set workingColorSpace(t) {
        console.warn("THREE.ColorManagement: .workingColorSpace is readonly.");
      },
      convert: function (t, e, s) {
        if (this.legacyMode || e === s || !e || !s) return t;
        if (At[e] && void 0 !== At[e][s]) {
          const r = At[e][s];
          return (t.r = r(t.r)), (t.g = r(t.g)), (t.b = r(t.b)), t;
        }
        throw new Error("Unsupported color space conversion.");
      },
      fromWorkingColorSpace: function (t, e) {
        return this.convert(t, this.workingColorSpace, e);
      },
      toWorkingColorSpace: function (t, e) {
        return this.convert(t, e, this.workingColorSpace);
      },
    },
    Ct = {
      aliceblue: 15792383,
      antiquewhite: 16444375,
      aqua: 65535,
      aquamarine: 8388564,
      azure: 15794175,
      beige: 16119260,
      bisque: 16770244,
      black: 0,
      blanchedalmond: 16772045,
      blue: 255,
      blueviolet: 9055202,
      brown: 10824234,
      burlywood: 14596231,
      cadetblue: 6266528,
      chartreuse: 8388352,
      chocolate: 13789470,
      coral: 16744272,
      cornflowerblue: 6591981,
      cornsilk: 16775388,
      crimson: 14423100,
      cyan: 65535,
      darkblue: 139,
      darkcyan: 35723,
      darkgoldenrod: 12092939,
      darkgray: 11119017,
      darkgreen: 25600,
      darkgrey: 11119017,
      darkkhaki: 12433259,
      darkmagenta: 9109643,
      darkolivegreen: 5597999,
      darkorange: 16747520,
      darkorchid: 10040012,
      darkred: 9109504,
      darksalmon: 15308410,
      darkseagreen: 9419919,
      darkslateblue: 4734347,
      darkslategray: 3100495,
      darkslategrey: 3100495,
      darkturquoise: 52945,
      darkviolet: 9699539,
      deeppink: 16716947,
      deepskyblue: 49151,
      dimgray: 6908265,
      dimgrey: 6908265,
      dodgerblue: 2003199,
      firebrick: 11674146,
      floralwhite: 16775920,
      forestgreen: 2263842,
      fuchsia: 16711935,
      gainsboro: 14474460,
      ghostwhite: 16316671,
      gold: 16766720,
      goldenrod: 14329120,
      gray: 8421504,
      green: 32768,
      greenyellow: 11403055,
      grey: 8421504,
      honeydew: 15794160,
      hotpink: 16738740,
      indianred: 13458524,
      indigo: 4915330,
      ivory: 16777200,
      khaki: 15787660,
      lavender: 15132410,
      lavenderblush: 16773365,
      lawngreen: 8190976,
      lemonchiffon: 16775885,
      lightblue: 11393254,
      lightcoral: 15761536,
      lightcyan: 14745599,
      lightgoldenrodyellow: 16448210,
      lightgray: 13882323,
      lightgreen: 9498256,
      lightgrey: 13882323,
      lightpink: 16758465,
      lightsalmon: 16752762,
      lightseagreen: 2142890,
      lightskyblue: 8900346,
      lightslategray: 7833753,
      lightslategrey: 7833753,
      lightsteelblue: 11584734,
      lightyellow: 16777184,
      lime: 65280,
      limegreen: 3329330,
      linen: 16445670,
      magenta: 16711935,
      maroon: 8388608,
      mediumaquamarine: 6737322,
      mediumblue: 205,
      mediumorchid: 12211667,
      mediumpurple: 9662683,
      mediumseagreen: 3978097,
      mediumslateblue: 8087790,
      mediumspringgreen: 64154,
      mediumturquoise: 4772300,
      mediumvioletred: 13047173,
      midnightblue: 1644912,
      mintcream: 16121850,
      mistyrose: 16770273,
      moccasin: 16770229,
      navajowhite: 16768685,
      navy: 128,
      oldlace: 16643558,
      olive: 8421376,
      olivedrab: 7048739,
      orange: 16753920,
      orangered: 16729344,
      orchid: 14315734,
      palegoldenrod: 15657130,
      palegreen: 10025880,
      paleturquoise: 11529966,
      palevioletred: 14381203,
      papayawhip: 16773077,
      peachpuff: 16767673,
      peru: 13468991,
      pink: 16761035,
      plum: 14524637,
      powderblue: 11591910,
      purple: 8388736,
      rebeccapurple: 6697881,
      red: 16711680,
      rosybrown: 12357519,
      royalblue: 4286945,
      saddlebrown: 9127187,
      salmon: 16416882,
      sandybrown: 16032864,
      seagreen: 3050327,
      seashell: 16774638,
      sienna: 10506797,
      silver: 12632256,
      skyblue: 8900331,
      slateblue: 6970061,
      slategray: 7372944,
      slategrey: 7372944,
      snow: 16775930,
      springgreen: 65407,
      steelblue: 4620980,
      tan: 13808780,
      teal: 32896,
      thistle: 14204888,
      tomato: 16737095,
      turquoise: 4251856,
      violet: 15631086,
      wheat: 16113331,
      white: 16777215,
      whitesmoke: 16119285,
      yellow: 16776960,
      yellowgreen: 10145074,
    },
    Bt = { r: 0, g: 0, b: 0 },
    Rt = { h: 0, s: 0, l: 0 },
    Et = { h: 0, s: 0, l: 0 };
  function Pt(t, e, s) {
    return (
      s < 0 && (s += 1),
      s > 1 && (s -= 1),
      s < 1 / 6
        ? t + 6 * (e - t) * s
        : s < 0.5
        ? e
        : s < 2 / 3
        ? t + 6 * (e - t) * (2 / 3 - s)
        : t
    );
  }
  function Tt(t, e) {
    return (e.r = t.r), (e.g = t.g), (e.b = t.b), e;
  }
  class Ot {
    constructor(t, e, s) {
      return (
        (this.isColor = !0),
        (this.r = 1),
        (this.g = 1),
        (this.b = 1),
        void 0 === e && void 0 === s ? this.set(t) : this.setRGB(t, e, s)
      );
    }
    set(t) {
      return (
        t && t.isColor
          ? this.copy(t)
          : "number" == typeof t
          ? this.setHex(t)
          : "string" == typeof t && this.setStyle(t),
        this
      );
    }
    setScalar(t) {
      return (this.r = t), (this.g = t), (this.b = t), this;
    }
    setHex(t, e = "srgb") {
      return (
        (t = Math.floor(t)),
        (this.r = ((t >> 16) & 255) / 255),
        (this.g = ((t >> 8) & 255) / 255),
        (this.b = (255 & t) / 255),
        zt.toWorkingColorSpace(this, e),
        this
      );
    }
    setRGB(t, e, s, r = "srgb-linear") {
      return (
        (this.r = t),
        (this.g = e),
        (this.b = s),
        zt.toWorkingColorSpace(this, r),
        this
      );
    }
    setHSL(t, e, s, r = "srgb-linear") {
      var i;
      if (
        ((t = ((t % (i = 1)) + i) % i),
        (e = I(e, 0, 1)),
        (s = I(s, 0, 1)),
        0 === e)
      )
        this.r = this.g = this.b = s;
      else {
        const r = s <= 0.5 ? s * (1 + e) : s + e - s * e,
          i = 2 * s - r;
        (this.r = Pt(i, r, t + 1 / 3)),
          (this.g = Pt(i, r, t)),
          (this.b = Pt(i, r, t - 1 / 3));
      }
      return zt.toWorkingColorSpace(this, r), this;
    }
    setStyle(t, e = "srgb") {
      function s(e) {
        void 0 !== e &&
          parseFloat(e) < 1 &&
          console.warn(
            "THREE.Color: Alpha component of " + t + " will be ignored."
          );
      }
      let r;
      if ((r = /^((?:rgb|hsl)a?)\(([^\)]*)\)/.exec(t))) {
        let t;
        const i = r[1],
          n = r[2];
        switch (i) {
          case "rgb":
          case "rgba":
            if (
              (t =
                /^\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(
                  n
                ))
            )
              return (
                (this.r = Math.min(255, parseInt(t[1], 10)) / 255),
                (this.g = Math.min(255, parseInt(t[2], 10)) / 255),
                (this.b = Math.min(255, parseInt(t[3], 10)) / 255),
                zt.toWorkingColorSpace(this, e),
                s(t[4]),
                this
              );
            if (
              (t =
                /^\s*(\d+)\%\s*,\s*(\d+)\%\s*,\s*(\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(
                  n
                ))
            )
              return (
                (this.r = Math.min(100, parseInt(t[1], 10)) / 100),
                (this.g = Math.min(100, parseInt(t[2], 10)) / 100),
                (this.b = Math.min(100, parseInt(t[3], 10)) / 100),
                zt.toWorkingColorSpace(this, e),
                s(t[4]),
                this
              );
            break;
          case "hsl":
          case "hsla":
            if (
              (t =
                /^\s*(\d*\.?\d+)\s*,\s*(\d*\.?\d+)\%\s*,\s*(\d*\.?\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(
                  n
                ))
            ) {
              const r = parseFloat(t[1]) / 360,
                i = parseFloat(t[2]) / 100,
                n = parseFloat(t[3]) / 100;
              return s(t[4]), this.setHSL(r, i, n, e);
            }
        }
      } else if ((r = /^\#([A-Fa-f\d]+)$/.exec(t))) {
        const t = r[1],
          s = t.length;
        if (3 === s)
          return (
            (this.r = parseInt(t.charAt(0) + t.charAt(0), 16) / 255),
            (this.g = parseInt(t.charAt(1) + t.charAt(1), 16) / 255),
            (this.b = parseInt(t.charAt(2) + t.charAt(2), 16) / 255),
            zt.toWorkingColorSpace(this, e),
            this
          );
        if (6 === s)
          return (
            (this.r = parseInt(t.charAt(0) + t.charAt(1), 16) / 255),
            (this.g = parseInt(t.charAt(2) + t.charAt(3), 16) / 255),
            (this.b = parseInt(t.charAt(4) + t.charAt(5), 16) / 255),
            zt.toWorkingColorSpace(this, e),
            this
          );
      }
      return t && t.length > 0 ? this.setColorName(t, e) : this;
    }
    setColorName(t, e = "srgb") {
      const s = Ct[t.toLowerCase()];
      return (
        void 0 !== s
          ? this.setHex(s, e)
          : console.warn("THREE.Color: Unknown color " + t),
        this
      );
    }
    clone() {
      return new this.constructor(this.r, this.g, this.b);
    }
    copy(t) {
      return (this.r = t.r), (this.g = t.g), (this.b = t.b), this;
    }
    copySRGBToLinear(t) {
      return (this.r = _t(t.r)), (this.g = _t(t.g)), (this.b = _t(t.b)), this;
    }
    copyLinearToSRGB(t) {
      return (this.r = vt(t.r)), (this.g = vt(t.g)), (this.b = vt(t.b)), this;
    }
    convertSRGBToLinear() {
      return this.copySRGBToLinear(this), this;
    }
    convertLinearToSRGB() {
      return this.copyLinearToSRGB(this), this;
    }
    getHex(t = "srgb") {
      return (
        zt.fromWorkingColorSpace(Tt(this, Bt), t),
        (I(255 * Bt.r, 0, 255) << 16) ^
          (I(255 * Bt.g, 0, 255) << 8) ^
          (I(255 * Bt.b, 0, 255) << 0)
      );
    }
    getHexString(t = "srgb") {
      return ("000000" + this.getHex(t).toString(16)).slice(-6);
    }
    getHSL(t, e = "srgb-linear") {
      zt.fromWorkingColorSpace(Tt(this, Bt), e);
      const s = Bt.r,
        r = Bt.g,
        i = Bt.b,
        n = Math.max(s, r, i),
        h = Math.min(s, r, i);
      let a, o;
      const u = (h + n) / 2;
      if (h === n) (a = 0), (o = 0);
      else {
        const t = n - h;
        switch (((o = u <= 0.5 ? t / (n + h) : t / (2 - n - h)), n)) {
          case s:
            a = (r - i) / t + (r < i ? 6 : 0);
            break;
          case r:
            a = (i - s) / t + 2;
            break;
          case i:
            a = (s - r) / t + 4;
        }
        a /= 6;
      }
      return (t.h = a), (t.s = o), (t.l = u), t;
    }
    getRGB(t, e = "srgb-linear") {
      return (
        zt.fromWorkingColorSpace(Tt(this, Bt), e),
        (t.r = Bt.r),
        (t.g = Bt.g),
        (t.b = Bt.b),
        t
      );
    }
    getStyle(t = "srgb") {
      return (
        zt.fromWorkingColorSpace(Tt(this, Bt), t),
        t !== O
          ? `color(${t} ${Bt.r} ${Bt.g} ${Bt.b})`
          : `rgb(${(255 * Bt.r) | 0},${(255 * Bt.g) | 0},${(255 * Bt.b) | 0})`
      );
    }
    offsetHSL(t, e, s) {
      return (
        this.getHSL(Rt),
        (Rt.h += t),
        (Rt.s += e),
        (Rt.l += s),
        this.setHSL(Rt.h, Rt.s, Rt.l),
        this
      );
    }
    add(t) {
      return (this.r += t.r), (this.g += t.g), (this.b += t.b), this;
    }
    addColors(t, e) {
      return (
        (this.r = t.r + e.r), (this.g = t.g + e.g), (this.b = t.b + e.b), this
      );
    }
    addScalar(t) {
      return (this.r += t), (this.g += t), (this.b += t), this;
    }
    sub(t) {
      return (
        (this.r = Math.max(0, this.r - t.r)),
        (this.g = Math.max(0, this.g - t.g)),
        (this.b = Math.max(0, this.b - t.b)),
        this
      );
    }
    multiply(t) {
      return (this.r *= t.r), (this.g *= t.g), (this.b *= t.b), this;
    }
    multiplyScalar(t) {
      return (this.r *= t), (this.g *= t), (this.b *= t), this;
    }
    lerp(t, e) {
      return (
        (this.r += (t.r - this.r) * e),
        (this.g += (t.g - this.g) * e),
        (this.b += (t.b - this.b) * e),
        this
      );
    }
    lerpColors(t, e, s) {
      return (
        (this.r = t.r + (e.r - t.r) * s),
        (this.g = t.g + (e.g - t.g) * s),
        (this.b = t.b + (e.b - t.b) * s),
        this
      );
    }
    lerpHSL(t, e) {
      this.getHSL(Rt), t.getHSL(Et);
      const s = D(Rt.h, Et.h, e),
        r = D(Rt.s, Et.s, e),
        i = D(Rt.l, Et.l, e);
      return this.setHSL(s, r, i), this;
    }
    equals(t) {
      return t.r === this.r && t.g === this.g && t.b === this.b;
    }
    fromArray(t, e = 0) {
      return (this.r = t[e]), (this.g = t[e + 1]), (this.b = t[e + 2]), this;
    }
    toArray(t = [], e = 0) {
      return (t[e] = this.r), (t[e + 1] = this.g), (t[e + 2] = this.b), t;
    }
    fromBufferAttribute(t, e) {
      return (
        (this.r = t.getX(e)), (this.g = t.getY(e)), (this.b = t.getZ(e)), this
      );
    }
    toJSON() {
      return this.getHex();
    }
    *[Symbol.iterator]() {
      yield this.r, yield this.g, yield this.b;
    }
  }
  let Nt;
  Ot.NAMES = Ct;
  class Ft {
    constructor(t = null) {
      (this.isSource = !0),
        (this.uuid = L()),
        (this.data = t),
        (this.version = 0);
    }
    set needsUpdate(t) {
      !0 === t && this.version++;
    }
    toJSON(t) {
      const e = void 0 === t || "string" == typeof t;
      if (!e && void 0 !== t.images[this.uuid]) return t.images[this.uuid];
      const s = { uuid: this.uuid, url: "" },
        r = this.data;
      if (null !== r) {
        let t;
        if (Array.isArray(r)) {
          t = [];
          for (let e = 0, s = r.length; e < s; e++)
            r[e].isDataTexture ? t.push(kt(r[e].image)) : t.push(kt(r[e]));
        } else t = kt(r);
        s.url = t;
      }
      return e || (t.images[this.uuid] = s), s;
    }
  }
  function kt(t) {
    return ("undefined" != typeof HTMLImageElement &&
      t instanceof HTMLImageElement) ||
      ("undefined" != typeof HTMLCanvasElement &&
        t instanceof HTMLCanvasElement) ||
      ("undefined" != typeof ImageBitmap && t instanceof ImageBitmap)
      ? class {
          static getDataURL(t) {
            if (/^data:/i.test(t.src)) return t.src;
            if ("undefined" == typeof HTMLCanvasElement) return t.src;
            let e;
            if (t instanceof HTMLCanvasElement) e = t;
            else {
              void 0 === Nt && (Nt = wt("canvas")),
                (Nt.width = t.width),
                (Nt.height = t.height);
              const s = Nt.getContext("2d");
              t instanceof ImageData
                ? s.putImageData(t, 0, 0)
                : s.drawImage(t, 0, 0, t.width, t.height),
                (e = Nt);
            }
            return e.width > 2048 || e.height > 2048
              ? (console.warn(
                  "THREE.ImageUtils.getDataURL: Image converted to jpg for performance reasons",
                  t
                ),
                e.toDataURL("image/jpeg", 0.6))
              : e.toDataURL("image/png");
          }
          static sRGBToLinear(t) {
            if (
              ("undefined" != typeof HTMLImageElement &&
                t instanceof HTMLImageElement) ||
              ("undefined" != typeof HTMLCanvasElement &&
                t instanceof HTMLCanvasElement) ||
              ("undefined" != typeof ImageBitmap && t instanceof ImageBitmap)
            ) {
              const e = wt("canvas");
              (e.width = t.width), (e.height = t.height);
              const s = e.getContext("2d");
              s.drawImage(t, 0, 0, t.width, t.height);
              const r = s.getImageData(0, 0, t.width, t.height),
                i = r.data;
              for (let t = 0; t < i.length; t++) i[t] = 255 * _t(i[t] / 255);
              return s.putImageData(r, 0, 0), e;
            }
            if (t.data) {
              const e = t.data.slice(0);
              for (let t = 0; t < e.length; t++)
                e instanceof Uint8Array || e instanceof Uint8ClampedArray
                  ? (e[t] = Math.floor(255 * _t(e[t] / 255)))
                  : (e[t] = _t(e[t]));
              return { data: e, width: t.width, height: t.height };
            }
            return (
              console.warn(
                "THREE.ImageUtils.sRGBToLinear(): Unsupported image type. No color space conversion applied."
              ),
              t
            );
          }
        }.getDataURL(t)
      : t.data
      ? {
          data: Array.from(t.data),
          width: t.width,
          height: t.height,
          type: t.data.constructor.name,
        }
      : (console.warn("THREE.Texture: Unable to serialize Texture."), {});
  }
  let Gt = 0;
  class Lt extends et {
    constructor(
      t = Lt.DEFAULT_IMAGE,
      e = Lt.DEFAULT_MAPPING,
      s = 1001,
      r = 1001,
      i = 1006,
      n = 1008,
      h = 1023,
      a = 1009,
      o = 1,
      u = 3e3
    ) {
      super(),
        (this.isTexture = !0),
        Object.defineProperty(this, "id", { value: Gt++ }),
        (this.uuid = L()),
        (this.name = ""),
        (this.source = new Ft(t)),
        (this.mipmaps = []),
        (this.mapping = e),
        (this.wrapS = s),
        (this.wrapT = r),
        (this.magFilter = i),
        (this.minFilter = n),
        (this.anisotropy = o),
        (this.format = h),
        (this.internalFormat = null),
        (this.type = a),
        (this.offset = new Mt(0, 0)),
        (this.repeat = new Mt(1, 1)),
        (this.center = new Mt(0, 0)),
        (this.rotation = 0),
        (this.matrixAutoUpdate = !0),
        (this.matrix = new ht()),
        (this.generateMipmaps = !0),
        (this.premultiplyAlpha = !1),
        (this.flipY = !0),
        (this.unpackAlignment = 4),
        (this.encoding = u),
        (this.userData = {}),
        (this.version = 0),
        (this.onUpdate = null),
        (this.isRenderTargetTexture = !1),
        (this.needsPMREMUpdate = !1);
    }
    get image() {
      return this.source.data;
    }
    set image(t) {
      this.source.data = t;
    }
    updateMatrix() {
      this.matrix.setUvTransform(
        this.offset.x,
        this.offset.y,
        this.repeat.x,
        this.repeat.y,
        this.rotation,
        this.center.x,
        this.center.y
      );
    }
    clone() {
      return new this.constructor().copy(this);
    }
    copy(t) {
      return (
        (this.name = t.name),
        (this.source = t.source),
        (this.mipmaps = t.mipmaps.slice(0)),
        (this.mapping = t.mapping),
        (this.wrapS = t.wrapS),
        (this.wrapT = t.wrapT),
        (this.magFilter = t.magFilter),
        (this.minFilter = t.minFilter),
        (this.anisotropy = t.anisotropy),
        (this.format = t.format),
        (this.internalFormat = t.internalFormat),
        (this.type = t.type),
        this.offset.copy(t.offset),
        this.repeat.copy(t.repeat),
        this.center.copy(t.center),
        (this.rotation = t.rotation),
        (this.matrixAutoUpdate = t.matrixAutoUpdate),
        this.matrix.copy(t.matrix),
        (this.generateMipmaps = t.generateMipmaps),
        (this.premultiplyAlpha = t.premultiplyAlpha),
        (this.flipY = t.flipY),
        (this.unpackAlignment = t.unpackAlignment),
        (this.encoding = t.encoding),
        (this.userData = JSON.parse(JSON.stringify(t.userData))),
        (this.needsUpdate = !0),
        this
      );
    }
    toJSON(t) {
      const e = void 0 === t || "string" == typeof t;
      if (!e && void 0 !== t.textures[this.uuid]) return t.textures[this.uuid];
      const s = {
        metadata: {
          version: 4.5,
          type: "Texture",
          generator: "Texture.toJSON",
        },
        uuid: this.uuid,
        name: this.name,
        image: this.source.toJSON(t).uuid,
        mapping: this.mapping,
        repeat: [this.repeat.x, this.repeat.y],
        offset: [this.offset.x, this.offset.y],
        center: [this.center.x, this.center.y],
        rotation: this.rotation,
        wrap: [this.wrapS, this.wrapT],
        format: this.format,
        type: this.type,
        encoding: this.encoding,
        minFilter: this.minFilter,
        magFilter: this.magFilter,
        anisotropy: this.anisotropy,
        flipY: this.flipY,
        premultiplyAlpha: this.premultiplyAlpha,
        unpackAlignment: this.unpackAlignment,
      };
      return (
        "{}" !== JSON.stringify(this.userData) && (s.userData = this.userData),
        e || (t.textures[this.uuid] = s),
        s
      );
    }
    dispose() {
      this.dispatchEvent({ type: "dispose" });
    }
    transformUv(t) {
      if (300 !== this.mapping) return t;
      if ((t.applyMatrix3(this.matrix), t.x < 0 || t.x > 1))
        switch (this.wrapS) {
          case s:
            t.x = t.x - Math.floor(t.x);
            break;
          case r:
            t.x = t.x < 0 ? 0 : 1;
            break;
          case i:
            1 === Math.abs(Math.floor(t.x) % 2)
              ? (t.x = Math.ceil(t.x) - t.x)
              : (t.x = t.x - Math.floor(t.x));
        }
      if (t.y < 0 || t.y > 1)
        switch (this.wrapT) {
          case s:
            t.y = t.y - Math.floor(t.y);
            break;
          case r:
            t.y = t.y < 0 ? 0 : 1;
            break;
          case i:
            1 === Math.abs(Math.floor(t.y) % 2)
              ? (t.y = Math.ceil(t.y) - t.y)
              : (t.y = t.y - Math.floor(t.y));
        }
      return this.flipY && (t.y = 1 - t.y), t;
    }
    set needsUpdate(t) {
      !0 === t && (this.version++, (this.source.needsUpdate = !0));
    }
  }
  (Lt.DEFAULT_IMAGE = null), (Lt.DEFAULT_MAPPING = 300);
  class It {
    constructor(
      t = new J(1 / 0, 1 / 0, 1 / 0),
      e = new J(-1 / 0, -1 / 0, -1 / 0)
    ) {
      (this.isBox3 = !0), (this.min = t), (this.max = e);
    }
    set(t, e) {
      return this.min.copy(t), this.max.copy(e), this;
    }
    setFromArray(t) {
      let e = 1 / 0,
        s = 1 / 0,
        r = 1 / 0,
        i = -1 / 0,
        n = -1 / 0,
        h = -1 / 0;
      for (let a = 0, o = t.length; a < o; a += 3) {
        const o = t[a],
          u = t[a + 1],
          l = t[a + 2];
        o < e && (e = o),
          u < s && (s = u),
          l < r && (r = l),
          o > i && (i = o),
          u > n && (n = u),
          l > h && (h = l);
      }
      return this.min.set(e, s, r), this.max.set(i, n, h), this;
    }
    setFromBufferAttribute(t) {
      let e = 1 / 0,
        s = 1 / 0,
        r = 1 / 0,
        i = -1 / 0,
        n = -1 / 0,
        h = -1 / 0;
      for (let a = 0, o = t.count; a < o; a++) {
        const o = t.getX(a),
          u = t.getY(a),
          l = t.getZ(a);
        o < e && (e = o),
          u < s && (s = u),
          l < r && (r = l),
          o > i && (i = o),
          u > n && (n = u),
          l > h && (h = l);
      }
      return this.min.set(e, s, r), this.max.set(i, n, h), this;
    }
    setFromPoints(t) {
      this.makeEmpty();
      for (let e = 0, s = t.length; e < s; e++) this.expandByPoint(t[e]);
      return this;
    }
    setFromCenterAndSize(t, e) {
      const s = Vt.copy(e).multiplyScalar(0.5);
      return this.min.copy(t).sub(s), this.max.copy(t).add(s), this;
    }
    setFromObject(t, e = !1) {
      return this.makeEmpty(), this.expandByObject(t, e);
    }
    clone() {
      return new this.constructor().copy(this);
    }
    copy(t) {
      return this.min.copy(t.min), this.max.copy(t.max), this;
    }
    makeEmpty() {
      return (
        (this.min.x = this.min.y = this.min.z = 1 / 0),
        (this.max.x = this.max.y = this.max.z = -1 / 0),
        this
      );
    }
    isEmpty() {
      return (
        this.max.x < this.min.x ||
        this.max.y < this.min.y ||
        this.max.z < this.min.z
      );
    }
    getCenter(t) {
      return this.isEmpty()
        ? t.set(0, 0, 0)
        : t.addVectors(this.min, this.max).multiplyScalar(0.5);
    }
    getSize(t) {
      return this.isEmpty() ? t.set(0, 0, 0) : t.subVectors(this.max, this.min);
    }
    expandByPoint(t) {
      return this.min.min(t), this.max.max(t), this;
    }
    expandByVector(t) {
      return this.min.sub(t), this.max.add(t), this;
    }
    expandByScalar(t) {
      return this.min.addScalar(-t), this.max.addScalar(t), this;
    }
    expandByObject(t, e = !1) {
      t.updateWorldMatrix(!1, !1);
      const s = t.geometry;
      if (void 0 !== s)
        if (e && null != s.attributes && void 0 !== s.attributes.position) {
          const e = s.attributes.position;
          for (let s = 0, r = e.count; s < r; s++)
            Vt.fromBufferAttribute(e, s).applyMatrix4(t.matrixWorld),
              this.expandByPoint(Vt);
        } else
          null === s.boundingBox && s.computeBoundingBox(),
            qt.copy(s.boundingBox),
            qt.applyMatrix4(t.matrixWorld),
            this.union(qt);
      const r = t.children;
      for (let t = 0, s = r.length; t < s; t++) this.expandByObject(r[t], e);
      return this;
    }
    containsPoint(t) {
      return !(
        t.x < this.min.x ||
        t.x > this.max.x ||
        t.y < this.min.y ||
        t.y > this.max.y ||
        t.z < this.min.z ||
        t.z > this.max.z
      );
    }
    containsBox(t) {
      return (
        this.min.x <= t.min.x &&
        t.max.x <= this.max.x &&
        this.min.y <= t.min.y &&
        t.max.y <= this.max.y &&
        this.min.z <= t.min.z &&
        t.max.z <= this.max.z
      );
    }
    getParameter(t, e) {
      return e.set(
        (t.x - this.min.x) / (this.max.x - this.min.x),
        (t.y - this.min.y) / (this.max.y - this.min.y),
        (t.z - this.min.z) / (this.max.z - this.min.z)
      );
    }
    intersectsBox(t) {
      return !(
        t.max.x < this.min.x ||
        t.min.x > this.max.x ||
        t.max.y < this.min.y ||
        t.min.y > this.max.y ||
        t.max.z < this.min.z ||
        t.min.z > this.max.z
      );
    }
    intersectsSphere(t) {
      return (
        this.clampPoint(t.center, Vt),
        Vt.distanceToSquared(t.center) <= t.radius * t.radius
      );
    }
    intersectsPlane(t) {
      let e, s;
      return (
        t.normal.x > 0
          ? ((e = t.normal.x * this.min.x), (s = t.normal.x * this.max.x))
          : ((e = t.normal.x * this.max.x), (s = t.normal.x * this.min.x)),
        t.normal.y > 0
          ? ((e += t.normal.y * this.min.y), (s += t.normal.y * this.max.y))
          : ((e += t.normal.y * this.max.y), (s += t.normal.y * this.min.y)),
        t.normal.z > 0
          ? ((e += t.normal.z * this.min.z), (s += t.normal.z * this.max.z))
          : ((e += t.normal.z * this.max.z), (s += t.normal.z * this.min.z)),
        e <= -t.constant && s >= -t.constant
      );
    }
    intersectsTriangle(t) {
      if (this.isEmpty()) return !1;
      this.getCenter(Yt),
        jt.subVectors(this.max, Yt),
        Ut.subVectors(t.a, Yt),
        Jt.subVectors(t.b, Yt),
        Ht.subVectors(t.c, Yt),
        Xt.subVectors(Jt, Ut),
        Wt.subVectors(Ht, Jt),
        Zt.subVectors(Ut, Ht);
      let e = [
        0,
        -Xt.z,
        Xt.y,
        0,
        -Wt.z,
        Wt.y,
        0,
        -Zt.z,
        Zt.y,
        Xt.z,
        0,
        -Xt.x,
        Wt.z,
        0,
        -Wt.x,
        Zt.z,
        0,
        -Zt.x,
        -Xt.y,
        Xt.x,
        0,
        -Wt.y,
        Wt.x,
        0,
        -Zt.y,
        Zt.x,
        0,
      ];
      return (
        !!Qt(e, Ut, Jt, Ht, jt) &&
        ((e = [1, 0, 0, 0, 1, 0, 0, 0, 1]),
        !!Qt(e, Ut, Jt, Ht, jt) &&
          ($t.crossVectors(Xt, Wt),
          (e = [$t.x, $t.y, $t.z]),
          Qt(e, Ut, Jt, Ht, jt)))
      );
    }
    clampPoint(t, e) {
      return e.copy(t).clamp(this.min, this.max);
    }
    distanceToPoint(t) {
      return Vt.copy(t).clamp(this.min, this.max).sub(t).length();
    }
    getBoundingSphere(t) {
      return (
        this.getCenter(t.center),
        (t.radius = 0.5 * this.getSize(Vt).length()),
        t
      );
    }
    intersect(t) {
      return (
        this.min.max(t.min),
        this.max.min(t.max),
        this.isEmpty() && this.makeEmpty(),
        this
      );
    }
    union(t) {
      return this.min.min(t.min), this.max.max(t.max), this;
    }
    applyMatrix4(t) {
      return (
        this.isEmpty() ||
          (Dt[0].set(this.min.x, this.min.y, this.min.z).applyMatrix4(t),
          Dt[1].set(this.min.x, this.min.y, this.max.z).applyMatrix4(t),
          Dt[2].set(this.min.x, this.max.y, this.min.z).applyMatrix4(t),
          Dt[3].set(this.min.x, this.max.y, this.max.z).applyMatrix4(t),
          Dt[4].set(this.max.x, this.min.y, this.min.z).applyMatrix4(t),
          Dt[5].set(this.max.x, this.min.y, this.max.z).applyMatrix4(t),
          Dt[6].set(this.max.x, this.max.y, this.min.z).applyMatrix4(t),
          Dt[7].set(this.max.x, this.max.y, this.max.z).applyMatrix4(t),
          this.setFromPoints(Dt)),
        this
      );
    }
    translate(t) {
      return this.min.add(t), this.max.add(t), this;
    }
    equals(t) {
      return t.min.equals(this.min) && t.max.equals(this.max);
    }
  }
  const Dt = [
      new J(),
      new J(),
      new J(),
      new J(),
      new J(),
      new J(),
      new J(),
      new J(),
    ],
    Vt = new J(),
    qt = new It(),
    Ut = new J(),
    Jt = new J(),
    Ht = new J(),
    Xt = new J(),
    Wt = new J(),
    Zt = new J(),
    Yt = new J(),
    jt = new J(),
    $t = new J(),
    Kt = new J();
  function Qt(t, e, s, r, i) {
    for (let n = 0, h = t.length - 3; n <= h; n += 3) {
      Kt.fromArray(t, n);
      const h =
          i.x * Math.abs(Kt.x) + i.y * Math.abs(Kt.y) + i.z * Math.abs(Kt.z),
        a = e.dot(Kt),
        o = s.dot(Kt),
        u = r.dot(Kt);
      if (Math.max(-Math.max(a, o, u), Math.min(a, o, u)) > h) return !1;
    }
    return !0;
  }
  const te = new J(),
    ee = new Mt();
  class se {
    constructor(t, e, s) {
      if (Array.isArray(t))
        throw new TypeError(
          "THREE.BufferAttribute: array should be a Typed Array."
        );
      (this.isBufferAttribute = !0),
        (this.name = ""),
        (this.array = t),
        (this.itemSize = e),
        (this.count = void 0 !== t ? t.length / e : 0),
        (this.normalized = !0 === s),
        (this.usage = F),
        (this.updateRange = { offset: 0, count: -1 }),
        (this.version = 0);
    }
    onUploadCallback() {}
    set needsUpdate(t) {
      !0 === t && this.version++;
    }
    setUsage(t) {
      return (this.usage = t), this;
    }
    copy(t) {
      return (
        (this.name = t.name),
        (this.array = new t.array.constructor(t.array)),
        (this.itemSize = t.itemSize),
        (this.count = t.count),
        (this.normalized = t.normalized),
        (this.usage = t.usage),
        this
      );
    }
    copyAt(t, e, s) {
      (t *= this.itemSize), (s *= e.itemSize);
      for (let r = 0, i = this.itemSize; r < i; r++)
        this.array[t + r] = e.array[s + r];
      return this;
    }
    copyArray(t) {
      return this.array.set(t), this;
    }
    applyMatrix3(t) {
      if (2 === this.itemSize)
        for (let e = 0, s = this.count; e < s; e++)
          ee.fromBufferAttribute(this, e),
            ee.applyMatrix3(t),
            this.setXY(e, ee.x, ee.y);
      else if (3 === this.itemSize)
        for (let e = 0, s = this.count; e < s; e++)
          te.fromBufferAttribute(this, e),
            te.applyMatrix3(t),
            this.setXYZ(e, te.x, te.y, te.z);
      return this;
    }
    applyMatrix4(t) {
      for (let e = 0, s = this.count; e < s; e++)
        te.fromBufferAttribute(this, e),
          te.applyMatrix4(t),
          this.setXYZ(e, te.x, te.y, te.z);
      return this;
    }
    applyNormalMatrix(t) {
      for (let e = 0, s = this.count; e < s; e++)
        te.fromBufferAttribute(this, e),
          te.applyNormalMatrix(t),
          this.setXYZ(e, te.x, te.y, te.z);
      return this;
    }
    transformDirection(t) {
      for (let e = 0, s = this.count; e < s; e++)
        te.fromBufferAttribute(this, e),
          te.transformDirection(t),
          this.setXYZ(e, te.x, te.y, te.z);
      return this;
    }
    set(t, e = 0) {
      return this.array.set(t, e), this;
    }
    getX(t) {
      let e = this.array[t * this.itemSize];
      return this.normalized && (e = V(e, this.array)), e;
    }
    setX(t, e) {
      return (
        this.normalized && (e = q(e, this.array)),
        (this.array[t * this.itemSize] = e),
        this
      );
    }
    getY(t) {
      let e = this.array[t * this.itemSize + 1];
      return this.normalized && (e = V(e, this.array)), e;
    }
    setY(t, e) {
      return (
        this.normalized && (e = q(e, this.array)),
        (this.array[t * this.itemSize + 1] = e),
        this
      );
    }
    getZ(t) {
      let e = this.array[t * this.itemSize + 2];
      return this.normalized && (e = V(e, this.array)), e;
    }
    setZ(t, e) {
      return (
        this.normalized && (e = q(e, this.array)),
        (this.array[t * this.itemSize + 2] = e),
        this
      );
    }
    getW(t) {
      let e = this.array[t * this.itemSize + 3];
      return this.normalized && (e = V(e, this.array)), e;
    }
    setW(t, e) {
      return (
        this.normalized && (e = q(e, this.array)),
        (this.array[t * this.itemSize + 3] = e),
        this
      );
    }
    setXY(t, e, s) {
      return (
        (t *= this.itemSize),
        this.normalized && ((e = q(e, this.array)), (s = q(s, this.array))),
        (this.array[t + 0] = e),
        (this.array[t + 1] = s),
        this
      );
    }
    setXYZ(t, e, s, r) {
      return (
        (t *= this.itemSize),
        this.normalized &&
          ((e = q(e, this.array)),
          (s = q(s, this.array)),
          (r = q(r, this.array))),
        (this.array[t + 0] = e),
        (this.array[t + 1] = s),
        (this.array[t + 2] = r),
        this
      );
    }
    setXYZW(t, e, s, r, i) {
      return (
        (t *= this.itemSize),
        this.normalized &&
          ((e = q(e, this.array)),
          (s = q(s, this.array)),
          (r = q(r, this.array)),
          (i = q(i, this.array))),
        (this.array[t + 0] = e),
        (this.array[t + 1] = s),
        (this.array[t + 2] = r),
        (this.array[t + 3] = i),
        this
      );
    }
    onUpload(t) {
      return (this.onUploadCallback = t), this;
    }
    clone() {
      return new this.constructor(this.array, this.itemSize).copy(this);
    }
    toJSON() {
      const t = {
        itemSize: this.itemSize,
        type: this.array.constructor.name,
        array: Array.from(this.array),
        normalized: this.normalized,
      };
      return (
        "" !== this.name && (t.name = this.name),
        this.usage !== F && (t.usage = this.usage),
        (0 === this.updateRange.offset && -1 === this.updateRange.count) ||
          (t.updateRange = this.updateRange),
        t
      );
    }
    copyColorsArray() {
      console.error(
        "THREE.BufferAttribute: copyColorsArray() was removed in r144."
      );
    }
    copyVector2sArray() {
      console.error(
        "THREE.BufferAttribute: copyVector2sArray() was removed in r144."
      );
    }
    copyVector3sArray() {
      console.error(
        "THREE.BufferAttribute: copyVector3sArray() was removed in r144."
      );
    }
    copyVector4sArray() {
      console.error(
        "THREE.BufferAttribute: copyVector4sArray() was removed in r144."
      );
    }
  }
  class re extends se {
    constructor(t, e, s) {
      super(new Uint16Array(t), e, s);
    }
  }
  class ie extends se {
    constructor(t, e, s) {
      super(new Uint32Array(t), e, s);
    }
  }
  class ne extends se {
    constructor(t, e, s) {
      super(new Float32Array(t), e, s);
    }
  }
  const he = new It(),
    ae = new J(),
    oe = new J(),
    ue = new J();
  class le {
    constructor(t = new J(), e = -1) {
      (this.center = t), (this.radius = e);
    }
    set(t, e) {
      return this.center.copy(t), (this.radius = e), this;
    }
    setFromPoints(t, e) {
      const s = this.center;
      void 0 !== e ? s.copy(e) : he.setFromPoints(t).getCenter(s);
      let r = 0;
      for (let e = 0, i = t.length; e < i; e++)
        r = Math.max(r, s.distanceToSquared(t[e]));
      return (this.radius = Math.sqrt(r)), this;
    }
    copy(t) {
      return this.center.copy(t.center), (this.radius = t.radius), this;
    }
    isEmpty() {
      return this.radius < 0;
    }
    makeEmpty() {
      return this.center.set(0, 0, 0), (this.radius = -1), this;
    }
    containsPoint(t) {
      return t.distanceToSquared(this.center) <= this.radius * this.radius;
    }
    distanceToPoint(t) {
      return t.distanceTo(this.center) - this.radius;
    }
    intersectsSphere(t) {
      const e = this.radius + t.radius;
      return t.center.distanceToSquared(this.center) <= e * e;
    }
    intersectsBox(t) {
      return t.intersectsSphere(this);
    }
    intersectsPlane(t) {
      return Math.abs(t.distanceToPoint(this.center)) <= this.radius;
    }
    clampPoint(t, e) {
      const s = this.center.distanceToSquared(t);
      return (
        e.copy(t),
        s > this.radius * this.radius &&
          (e.sub(this.center).normalize(),
          e.multiplyScalar(this.radius).add(this.center)),
        e
      );
    }
    getBoundingBox(t) {
      return this.isEmpty()
        ? (t.makeEmpty(), t)
        : (t.set(this.center, this.center), t.expandByScalar(this.radius), t);
    }
    applyMatrix4(t) {
      return (
        this.center.applyMatrix4(t),
        (this.radius = this.radius * t.getMaxScaleOnAxis()),
        this
      );
    }
    translate(t) {
      return this.center.add(t), this;
    }
    expandByPoint(t) {
      ue.subVectors(t, this.center);
      const e = ue.lengthSq();
      if (e > this.radius * this.radius) {
        const t = Math.sqrt(e),
          s = 0.5 * (t - this.radius);
        this.center.add(ue.multiplyScalar(s / t)), (this.radius += s);
      }
      return this;
    }
    union(t) {
      return (
        !0 === this.center.equals(t.center)
          ? oe.set(0, 0, 1).multiplyScalar(t.radius)
          : oe
              .subVectors(t.center, this.center)
              .normalize()
              .multiplyScalar(t.radius),
        this.expandByPoint(ae.copy(t.center).add(oe)),
        this.expandByPoint(ae.copy(t.center).sub(oe)),
        this
      );
    }
    equals(t) {
      return t.center.equals(this.center) && t.radius === this.radius;
    }
    clone() {
      return new this.constructor().copy(this);
    }
  }
  let ce = 0;
  const de = new W(),
    me = new St(),
    ye = new J(),
    pe = new It(),
    xe = new It(),
    fe = new J();
  class ge extends et {
    constructor() {
      super(),
        (this.isBufferGeometry = !0),
        Object.defineProperty(this, "id", { value: ce++ }),
        (this.uuid = L()),
        (this.name = ""),
        (this.type = "BufferGeometry"),
        (this.index = null),
        (this.attributes = {}),
        (this.morphAttributes = {}),
        (this.morphTargetsRelative = !1),
        (this.groups = []),
        (this.boundingBox = null),
        (this.boundingSphere = null),
        (this.drawRange = { start: 0, count: 1 / 0 }),
        (this.userData = {});
    }
    getIndex() {
      return this.index;
    }
    setIndex(t) {
      return (
        Array.isArray(t)
          ? (this.index = new (
              (function (t) {
                for (let e = t.length - 1; e >= 0; --e)
                  if (t[e] >= 65535) return !0;
                return !1;
              })(t)
                ? ie
                : re
            )(t, 1))
          : (this.index = t),
        this
      );
    }
    getAttribute(t) {
      return this.attributes[t];
    }
    setAttribute(t, e) {
      return (this.attributes[t] = e), this;
    }
    deleteAttribute(t) {
      return delete this.attributes[t], this;
    }
    hasAttribute(t) {
      return void 0 !== this.attributes[t];
    }
    addGroup(t, e, s = 0) {
      this.groups.push({ start: t, count: e, materialIndex: s });
    }
    clearGroups() {
      this.groups = [];
    }
    setDrawRange(t, e) {
      (this.drawRange.start = t), (this.drawRange.count = e);
    }
    applyMatrix4(t) {
      const e = this.attributes.position;
      void 0 !== e && (e.applyMatrix4(t), (e.needsUpdate = !0));
      const s = this.attributes.normal;
      if (void 0 !== s) {
        const e = new ht().getNormalMatrix(t);
        s.applyNormalMatrix(e), (s.needsUpdate = !0);
      }
      const r = this.attributes.tangent;
      return (
        void 0 !== r && (r.transformDirection(t), (r.needsUpdate = !0)),
        null !== this.boundingBox && this.computeBoundingBox(),
        null !== this.boundingSphere && this.computeBoundingSphere(),
        this
      );
    }
    applyQuaternion(t) {
      return de.makeRotationFromQuaternion(t), this.applyMatrix4(de), this;
    }
    rotateX(t) {
      return de.makeRotationX(t), this.applyMatrix4(de), this;
    }
    rotateY(t) {
      return de.makeRotationY(t), this.applyMatrix4(de), this;
    }
    rotateZ(t) {
      return de.makeRotationZ(t), this.applyMatrix4(de), this;
    }
    translate(t, e, s) {
      return de.makeTranslation(t, e, s), this.applyMatrix4(de), this;
    }
    scale(t, e, s) {
      return de.makeScale(t, e, s), this.applyMatrix4(de), this;
    }
    lookAt(t) {
      return (
        me.lookAt(t), me.updateMatrix(), this.applyMatrix4(me.matrix), this
      );
    }
    center() {
      return (
        this.computeBoundingBox(),
        this.boundingBox.getCenter(ye).negate(),
        this.translate(ye.x, ye.y, ye.z),
        this
      );
    }
    setFromPoints(t) {
      const e = [];
      for (let s = 0, r = t.length; s < r; s++) {
        const r = t[s];
        e.push(r.x, r.y, r.z || 0);
      }
      return this.setAttribute("position", new ne(e, 3)), this;
    }
    computeBoundingBox() {
      null === this.boundingBox && (this.boundingBox = new It());
      const t = this.attributes.position,
        e = this.morphAttributes.position;
      if (t && t.isGLBufferAttribute)
        return (
          console.error(
            'THREE.BufferGeometry.computeBoundingBox(): GLBufferAttribute requires a manual bounding box. Alternatively set "mesh.frustumCulled" to "false".',
            this
          ),
          void this.boundingBox.set(
            new J(-1 / 0, -1 / 0, -1 / 0),
            new J(1 / 0, 1 / 0, 1 / 0)
          )
        );
      if (void 0 !== t) {
        if ((this.boundingBox.setFromBufferAttribute(t), e))
          for (let t = 0, s = e.length; t < s; t++) {
            const s = e[t];
            pe.setFromBufferAttribute(s),
              this.morphTargetsRelative
                ? (fe.addVectors(this.boundingBox.min, pe.min),
                  this.boundingBox.expandByPoint(fe),
                  fe.addVectors(this.boundingBox.max, pe.max),
                  this.boundingBox.expandByPoint(fe))
                : (this.boundingBox.expandByPoint(pe.min),
                  this.boundingBox.expandByPoint(pe.max));
          }
      } else this.boundingBox.makeEmpty();
      (isNaN(this.boundingBox.min.x) ||
        isNaN(this.boundingBox.min.y) ||
        isNaN(this.boundingBox.min.z)) &&
        console.error(
          'THREE.BufferGeometry.computeBoundingBox(): Computed min/max have NaN values. The "position" attribute is likely to have NaN values.',
          this
        );
    }
    computeBoundingSphere() {
      null === this.boundingSphere && (this.boundingSphere = new le());
      const t = this.attributes.position,
        e = this.morphAttributes.position;
      if (t && t.isGLBufferAttribute)
        return (
          console.error(
            'THREE.BufferGeometry.computeBoundingSphere(): GLBufferAttribute requires a manual bounding sphere. Alternatively set "mesh.frustumCulled" to "false".',
            this
          ),
          void this.boundingSphere.set(new J(), 1 / 0)
        );
      if (t) {
        const s = this.boundingSphere.center;
        if ((pe.setFromBufferAttribute(t), e))
          for (let t = 0, s = e.length; t < s; t++) {
            const s = e[t];
            xe.setFromBufferAttribute(s),
              this.morphTargetsRelative
                ? (fe.addVectors(pe.min, xe.min),
                  pe.expandByPoint(fe),
                  fe.addVectors(pe.max, xe.max),
                  pe.expandByPoint(fe))
                : (pe.expandByPoint(xe.min), pe.expandByPoint(xe.max));
          }
        pe.getCenter(s);
        let r = 0;
        for (let e = 0, i = t.count; e < i; e++)
          fe.fromBufferAttribute(t, e),
            (r = Math.max(r, s.distanceToSquared(fe)));
        if (e)
          for (let i = 0, n = e.length; i < n; i++) {
            const n = e[i],
              h = this.morphTargetsRelative;
            for (let e = 0, i = n.count; e < i; e++)
              fe.fromBufferAttribute(n, e),
                h && (ye.fromBufferAttribute(t, e), fe.add(ye)),
                (r = Math.max(r, s.distanceToSquared(fe)));
          }
        (this.boundingSphere.radius = Math.sqrt(r)),
          isNaN(this.boundingSphere.radius) &&
            console.error(
              'THREE.BufferGeometry.computeBoundingSphere(): Computed radius is NaN. The "position" attribute is likely to have NaN values.',
              this
            );
      }
    }
    computeTangents() {
      const t = this.index,
        e = this.attributes;
      if (
        null === t ||
        void 0 === e.position ||
        void 0 === e.normal ||
        void 0 === e.uv
      )
        return void console.error(
          "THREE.BufferGeometry: .computeTangents() failed. Missing required attributes (index, position, normal or uv)"
        );
      const s = t.array,
        r = e.position.array,
        i = e.normal.array,
        n = e.uv.array,
        h = r.length / 3;
      !1 === this.hasAttribute("tangent") &&
        this.setAttribute("tangent", new se(new Float32Array(4 * h), 4));
      const a = this.getAttribute("tangent").array,
        o = [],
        u = [];
      for (let t = 0; t < h; t++) (o[t] = new J()), (u[t] = new J());
      const l = new J(),
        c = new J(),
        d = new J(),
        m = new Mt(),
        y = new Mt(),
        p = new Mt(),
        x = new J(),
        f = new J();
      function g(t, e, s) {
        l.fromArray(r, 3 * t),
          c.fromArray(r, 3 * e),
          d.fromArray(r, 3 * s),
          m.fromArray(n, 2 * t),
          y.fromArray(n, 2 * e),
          p.fromArray(n, 2 * s),
          c.sub(l),
          d.sub(l),
          y.sub(m),
          p.sub(m);
        const i = 1 / (y.x * p.y - p.x * y.y);
        isFinite(i) &&
          (x
            .copy(c)
            .multiplyScalar(p.y)
            .addScaledVector(d, -y.y)
            .multiplyScalar(i),
          f
            .copy(d)
            .multiplyScalar(y.x)
            .addScaledVector(c, -p.x)
            .multiplyScalar(i),
          o[t].add(x),
          o[e].add(x),
          o[s].add(x),
          u[t].add(f),
          u[e].add(f),
          u[s].add(f));
      }
      let b = this.groups;
      0 === b.length && (b = [{ start: 0, count: s.length }]);
      for (let t = 0, e = b.length; t < e; ++t) {
        const e = b[t],
          r = e.start;
        for (let t = r, i = r + e.count; t < i; t += 3)
          g(s[t + 0], s[t + 1], s[t + 2]);
      }
      const S = new J(),
        M = new J(),
        w = new J(),
        _ = new J();
      function v(t) {
        w.fromArray(i, 3 * t), _.copy(w);
        const e = o[t];
        S.copy(e),
          S.sub(w.multiplyScalar(w.dot(e))).normalize(),
          M.crossVectors(_, e);
        const s = M.dot(u[t]) < 0 ? -1 : 1;
        (a[4 * t] = S.x),
          (a[4 * t + 1] = S.y),
          (a[4 * t + 2] = S.z),
          (a[4 * t + 3] = s);
      }
      for (let t = 0, e = b.length; t < e; ++t) {
        const e = b[t],
          r = e.start;
        for (let t = r, i = r + e.count; t < i; t += 3)
          v(s[t + 0]), v(s[t + 1]), v(s[t + 2]);
      }
    }
    computeVertexNormals() {
      const t = this.index,
        e = this.getAttribute("position");
      if (void 0 !== e) {
        let s = this.getAttribute("normal");
        if (void 0 === s)
          (s = new se(new Float32Array(3 * e.count), 3)),
            this.setAttribute("normal", s);
        else for (let t = 0, e = s.count; t < e; t++) s.setXYZ(t, 0, 0, 0);
        const r = new J(),
          i = new J(),
          n = new J(),
          h = new J(),
          a = new J(),
          o = new J(),
          u = new J(),
          l = new J();
        if (t)
          for (let c = 0, d = t.count; c < d; c += 3) {
            const d = t.getX(c + 0),
              m = t.getX(c + 1),
              y = t.getX(c + 2);
            r.fromBufferAttribute(e, d),
              i.fromBufferAttribute(e, m),
              n.fromBufferAttribute(e, y),
              u.subVectors(n, i),
              l.subVectors(r, i),
              u.cross(l),
              h.fromBufferAttribute(s, d),
              a.fromBufferAttribute(s, m),
              o.fromBufferAttribute(s, y),
              h.add(u),
              a.add(u),
              o.add(u),
              s.setXYZ(d, h.x, h.y, h.z),
              s.setXYZ(m, a.x, a.y, a.z),
              s.setXYZ(y, o.x, o.y, o.z);
          }
        else
          for (let t = 0, h = e.count; t < h; t += 3)
            r.fromBufferAttribute(e, t + 0),
              i.fromBufferAttribute(e, t + 1),
              n.fromBufferAttribute(e, t + 2),
              u.subVectors(n, i),
              l.subVectors(r, i),
              u.cross(l),
              s.setXYZ(t + 0, u.x, u.y, u.z),
              s.setXYZ(t + 1, u.x, u.y, u.z),
              s.setXYZ(t + 2, u.x, u.y, u.z);
        this.normalizeNormals(), (s.needsUpdate = !0);
      }
    }
    merge() {
      return (
        console.error(
          "THREE.BufferGeometry.merge() has been removed. Use THREE.BufferGeometryUtils.mergeBufferGeometries() instead."
        ),
        this
      );
    }
    normalizeNormals() {
      const t = this.attributes.normal;
      for (let e = 0, s = t.count; e < s; e++)
        fe.fromBufferAttribute(t, e),
          fe.normalize(),
          t.setXYZ(e, fe.x, fe.y, fe.z);
    }
    toNonIndexed() {
      function t(t, e) {
        const s = t.array,
          r = t.itemSize,
          i = t.normalized,
          n = new s.constructor(e.length * r);
        let h = 0,
          a = 0;
        for (let i = 0, o = e.length; i < o; i++) {
          h = t.isInterleavedBufferAttribute
            ? e[i] * t.data.stride + t.offset
            : e[i] * r;
          for (let t = 0; t < r; t++) n[a++] = s[h++];
        }
        return new se(n, r, i);
      }
      if (null === this.index)
        return (
          console.warn(
            "THREE.BufferGeometry.toNonIndexed(): BufferGeometry is already non-indexed."
          ),
          this
        );
      const e = new ge(),
        s = this.index.array,
        r = this.attributes;
      for (const i in r) {
        const n = t(r[i], s);
        e.setAttribute(i, n);
      }
      const i = this.morphAttributes;
      for (const r in i) {
        const n = [],
          h = i[r];
        for (let e = 0, r = h.length; e < r; e++) {
          const r = t(h[e], s);
          n.push(r);
        }
        e.morphAttributes[r] = n;
      }
      e.morphTargetsRelative = this.morphTargetsRelative;
      const n = this.groups;
      for (let t = 0, s = n.length; t < s; t++) {
        const s = n[t];
        e.addGroup(s.start, s.count, s.materialIndex);
      }
      return e;
    }
    toJSON() {
      const t = {
        metadata: {
          version: 4.5,
          type: "BufferGeometry",
          generator: "BufferGeometry.toJSON",
        },
      };
      if (
        ((t.uuid = this.uuid),
        (t.type = this.type),
        "" !== this.name && (t.name = this.name),
        Object.keys(this.userData).length > 0 && (t.userData = this.userData),
        void 0 !== this.parameters)
      ) {
        const e = this.parameters;
        for (const s in e) void 0 !== e[s] && (t[s] = e[s]);
        return t;
      }
      t.data = { attributes: {} };
      const e = this.index;
      null !== e &&
        (t.data.index = {
          type: e.array.constructor.name,
          array: Array.prototype.slice.call(e.array),
        });
      const s = this.attributes;
      for (const e in s) {
        const r = s[e];
        t.data.attributes[e] = r.toJSON(t.data);
      }
      const r = {};
      let i = !1;
      for (const e in this.morphAttributes) {
        const s = this.morphAttributes[e],
          n = [];
        for (let e = 0, r = s.length; e < r; e++) {
          const r = s[e];
          n.push(r.toJSON(t.data));
        }
        n.length > 0 && ((r[e] = n), (i = !0));
      }
      i &&
        ((t.data.morphAttributes = r),
        (t.data.morphTargetsRelative = this.morphTargetsRelative));
      const n = this.groups;
      n.length > 0 && (t.data.groups = JSON.parse(JSON.stringify(n)));
      const h = this.boundingSphere;
      return (
        null !== h &&
          (t.data.boundingSphere = {
            center: h.center.toArray(),
            radius: h.radius,
          }),
        t
      );
    }
    clone() {
      return new this.constructor().copy(this);
    }
    copy(t) {
      (this.index = null),
        (this.attributes = {}),
        (this.morphAttributes = {}),
        (this.groups = []),
        (this.boundingBox = null),
        (this.boundingSphere = null);
      const e = {};
      this.name = t.name;
      const s = t.index;
      null !== s && this.setIndex(s.clone(e));
      const r = t.attributes;
      for (const t in r) {
        const s = r[t];
        this.setAttribute(t, s.clone(e));
      }
      const i = t.morphAttributes;
      for (const t in i) {
        const s = [],
          r = i[t];
        for (let t = 0, i = r.length; t < i; t++) s.push(r[t].clone(e));
        this.morphAttributes[t] = s;
      }
      this.morphTargetsRelative = t.morphTargetsRelative;
      const n = t.groups;
      for (let t = 0, e = n.length; t < e; t++) {
        const e = n[t];
        this.addGroup(e.start, e.count, e.materialIndex);
      }
      const h = t.boundingBox;
      null !== h && (this.boundingBox = h.clone());
      const a = t.boundingSphere;
      return (
        null !== a && (this.boundingSphere = a.clone()),
        (this.drawRange.start = t.drawRange.start),
        (this.drawRange.count = t.drawRange.count),
        (this.userData = t.userData),
        void 0 !== t.parameters &&
          (this.parameters = Object.assign({}, t.parameters)),
        this
      );
    }
    dispose() {
      this.dispatchEvent({ type: "dispose" });
    }
  }
  class be extends ge {
    constructor(t = 1, e = 1, s = 1, r = 1, i = 1, n = 1) {
      super(),
        (this.type = "BoxGeometry"),
        (this.parameters = {
          width: t,
          height: e,
          depth: s,
          widthSegments: r,
          heightSegments: i,
          depthSegments: n,
        });
      const h = this;
      (r = Math.floor(r)), (i = Math.floor(i)), (n = Math.floor(n));
      const a = [],
        o = [],
        u = [],
        l = [];
      let c = 0,
        d = 0;
      function m(t, e, s, r, i, n, m, y, p, x, f) {
        const g = n / p,
          b = m / x,
          S = n / 2,
          M = m / 2,
          w = y / 2,
          _ = p + 1,
          v = x + 1;
        let A = 0,
          z = 0;
        const C = new J();
        for (let n = 0; n < v; n++) {
          const h = n * b - M;
          for (let a = 0; a < _; a++) {
            const c = a * g - S;
            (C[t] = c * r),
              (C[e] = h * i),
              (C[s] = w),
              o.push(C.x, C.y, C.z),
              (C[t] = 0),
              (C[e] = 0),
              (C[s] = y > 0 ? 1 : -1),
              u.push(C.x, C.y, C.z),
              l.push(a / p),
              l.push(1 - n / x),
              (A += 1);
          }
        }
        for (let t = 0; t < x; t++)
          for (let e = 0; e < p; e++) {
            const s = c + e + _ * t,
              r = c + e + _ * (t + 1),
              i = c + (e + 1) + _ * (t + 1),
              n = c + (e + 1) + _ * t;
            a.push(s, r, n), a.push(r, i, n), (z += 6);
          }
        h.addGroup(d, z, f), (d += z), (c += A);
      }
      m("z", "y", "x", -1, -1, s, e, t, n, i, 0),
        m("z", "y", "x", 1, -1, s, e, -t, n, i, 1),
        m("x", "z", "y", 1, 1, t, s, e, r, n, 2),
        m("x", "z", "y", 1, -1, t, s, -e, r, n, 3),
        m("x", "y", "z", 1, -1, t, e, s, r, i, 4),
        m("x", "y", "z", -1, -1, t, e, -s, r, i, 5),
        this.setIndex(a),
        this.setAttribute("position", new ne(o, 3)),
        this.setAttribute("normal", new ne(u, 3)),
        this.setAttribute("uv", new ne(l, 2));
    }
    static fromJSON(t) {
      return new be(
        t.width,
        t.height,
        t.depth,
        t.widthSegments,
        t.heightSegments,
        t.depthSegments
      );
    }
  }
  class Se {
    constructor() {
      (this.type = "Curve"), (this.arcLengthDivisions = 200);
    }
    getPoint() {
      return console.warn("THREE.Curve: .getPoint() not implemented."), null;
    }
    getPointAt(t, e) {
      const s = this.getUtoTmapping(t);
      return this.getPoint(s, e);
    }
    getPoints(t = 5) {
      const e = [];
      for (let s = 0; s <= t; s++) e.push(this.getPoint(s / t));
      return e;
    }
    getSpacedPoints(t = 5) {
      const e = [];
      for (let s = 0; s <= t; s++) e.push(this.getPointAt(s / t));
      return e;
    }
    getLength() {
      const t = this.getLengths();
      return t[t.length - 1];
    }
    getLengths(t = this.arcLengthDivisions) {
      if (
        this.cacheArcLengths &&
        this.cacheArcLengths.length === t + 1 &&
        !this.needsUpdate
      )
        return this.cacheArcLengths;
      this.needsUpdate = !1;
      const e = [];
      let s,
        r = this.getPoint(0),
        i = 0;
      e.push(0);
      for (let n = 1; n <= t; n++)
        (s = this.getPoint(n / t)), (i += s.distanceTo(r)), e.push(i), (r = s);
      return (this.cacheArcLengths = e), e;
    }
    updateArcLengths() {
      (this.needsUpdate = !0), this.getLengths();
    }
    getUtoTmapping(t, e) {
      const s = this.getLengths();
      let r = 0;
      const i = s.length;
      let n;
      n = e || t * s[i - 1];
      let h,
        a = 0,
        o = i - 1;
      for (; a <= o; )
        if (((r = Math.floor(a + (o - a) / 2)), (h = s[r] - n), h < 0))
          a = r + 1;
        else {
          if (!(h > 0)) {
            o = r;
            break;
          }
          o = r - 1;
        }
      if (((r = o), s[r] === n)) return r / (i - 1);
      const u = s[r];
      return (r + (n - u) / (s[r + 1] - u)) / (i - 1);
    }
    getTangent(t, e) {
      const s = 1e-4;
      let r = t - s,
        i = t + s;
      r < 0 && (r = 0), i > 1 && (i = 1);
      const n = this.getPoint(r),
        h = this.getPoint(i),
        a = e || (n.isVector2 ? new Mt() : new J());
      return a.copy(h).sub(n).normalize(), a;
    }
    getTangentAt(t, e) {
      const s = this.getUtoTmapping(t);
      return this.getTangent(s, e);
    }
    computeFrenetFrames(t, e) {
      const s = new J(),
        r = [],
        i = [],
        n = [],
        h = new J(),
        a = new W();
      for (let e = 0; e <= t; e++) {
        const s = e / t;
        r[e] = this.getTangentAt(s, new J());
      }
      (i[0] = new J()), (n[0] = new J());
      let o = Number.MAX_VALUE;
      const u = Math.abs(r[0].x),
        l = Math.abs(r[0].y),
        c = Math.abs(r[0].z);
      u <= o && ((o = u), s.set(1, 0, 0)),
        l <= o && ((o = l), s.set(0, 1, 0)),
        c <= o && s.set(0, 0, 1),
        h.crossVectors(r[0], s).normalize(),
        i[0].crossVectors(r[0], h),
        n[0].crossVectors(r[0], i[0]);
      for (let e = 1; e <= t; e++) {
        if (
          ((i[e] = i[e - 1].clone()),
          (n[e] = n[e - 1].clone()),
          h.crossVectors(r[e - 1], r[e]),
          h.length() > Number.EPSILON)
        ) {
          h.normalize();
          const t = Math.acos(I(r[e - 1].dot(r[e]), -1, 1));
          i[e].applyMatrix4(a.makeRotationAxis(h, t));
        }
        n[e].crossVectors(r[e], i[e]);
      }
      if (!0 === e) {
        let e = Math.acos(I(i[0].dot(i[t]), -1, 1));
        (e /= t), r[0].dot(h.crossVectors(i[0], i[t])) > 0 && (e = -e);
        for (let s = 1; s <= t; s++)
          i[s].applyMatrix4(a.makeRotationAxis(r[s], e * s)),
            n[s].crossVectors(r[s], i[s]);
      }
      return { tangents: r, normals: i, binormals: n };
    }
    clone() {
      return new this.constructor().copy(this);
    }
    copy(t) {
      return (this.arcLengthDivisions = t.arcLengthDivisions), this;
    }
    toJSON() {
      const t = {
        metadata: { version: 4.5, type: "Curve", generator: "Curve.toJSON" },
      };
      return (
        (t.arcLengthDivisions = this.arcLengthDivisions),
        (t.type = this.type),
        t
      );
    }
    fromJSON(t) {
      return (this.arcLengthDivisions = t.arcLengthDivisions), this;
    }
  }
  class Me extends Se {
    constructor(
      t = 0,
      e = 0,
      s = 1,
      r = 1,
      i = 0,
      n = 2 * Math.PI,
      h = !1,
      a = 0
    ) {
      super(),
        (this.isEllipseCurve = !0),
        (this.type = "EllipseCurve"),
        (this.aX = t),
        (this.aY = e),
        (this.xRadius = s),
        (this.yRadius = r),
        (this.aStartAngle = i),
        (this.aEndAngle = n),
        (this.aClockwise = h),
        (this.aRotation = a);
    }
    getPoint(t, e) {
      const s = e || new Mt(),
        r = 2 * Math.PI;
      let i = this.aEndAngle - this.aStartAngle;
      const n = Math.abs(i) < Number.EPSILON;
      for (; i < 0; ) i += r;
      for (; i > r; ) i -= r;
      i < Number.EPSILON && (i = n ? 0 : r),
        !0 !== this.aClockwise || n || (i === r ? (i = -r) : (i -= r));
      const h = this.aStartAngle + t * i;
      let a = this.aX + this.xRadius * Math.cos(h),
        o = this.aY + this.yRadius * Math.sin(h);
      if (0 !== this.aRotation) {
        const t = Math.cos(this.aRotation),
          e = Math.sin(this.aRotation),
          s = a - this.aX,
          r = o - this.aY;
        (a = s * t - r * e + this.aX), (o = s * e + r * t + this.aY);
      }
      return s.set(a, o);
    }
    copy(t) {
      return (
        super.copy(t),
        (this.aX = t.aX),
        (this.aY = t.aY),
        (this.xRadius = t.xRadius),
        (this.yRadius = t.yRadius),
        (this.aStartAngle = t.aStartAngle),
        (this.aEndAngle = t.aEndAngle),
        (this.aClockwise = t.aClockwise),
        (this.aRotation = t.aRotation),
        this
      );
    }
    toJSON() {
      const t = super.toJSON();
      return (
        (t.aX = this.aX),
        (t.aY = this.aY),
        (t.xRadius = this.xRadius),
        (t.yRadius = this.yRadius),
        (t.aStartAngle = this.aStartAngle),
        (t.aEndAngle = this.aEndAngle),
        (t.aClockwise = this.aClockwise),
        (t.aRotation = this.aRotation),
        t
      );
    }
    fromJSON(t) {
      return (
        super.fromJSON(t),
        (this.aX = t.aX),
        (this.aY = t.aY),
        (this.xRadius = t.xRadius),
        (this.yRadius = t.yRadius),
        (this.aStartAngle = t.aStartAngle),
        (this.aEndAngle = t.aEndAngle),
        (this.aClockwise = t.aClockwise),
        (this.aRotation = t.aRotation),
        this
      );
    }
  }
  class we extends Me {
    constructor(t, e, s, r, i, n) {
      super(t, e, s, s, r, i, n),
        (this.isArcCurve = !0),
        (this.type = "ArcCurve");
    }
  }
  function _e() {
    let t = 0,
      e = 0,
      s = 0,
      r = 0;
    function i(i, n, h, a) {
      (t = i),
        (e = h),
        (s = -3 * i + 3 * n - 2 * h - a),
        (r = 2 * i - 2 * n + h + a);
    }
    return {
      initCatmullRom: function (t, e, s, r, n) {
        i(e, s, n * (s - t), n * (r - e));
      },
      initNonuniformCatmullRom: function (t, e, s, r, n, h, a) {
        let o = (e - t) / n - (s - t) / (n + h) + (s - e) / h,
          u = (s - e) / h - (r - e) / (h + a) + (r - s) / a;
        (o *= h), (u *= h), i(e, s, o, u);
      },
      calc: function (i) {
        const n = i * i;
        return t + e * i + s * n + r * (n * i);
      },
    };
  }
  const ve = new J(),
    Ae = new _e(),
    ze = new _e(),
    Ce = new _e();
  class Be extends Se {
    constructor(t = [], e = !1, s = "centripetal", r = 0.5) {
      super(),
        (this.isCatmullRomCurve3 = !0),
        (this.type = "CatmullRomCurve3"),
        (this.points = t),
        (this.closed = e),
        (this.curveType = s),
        (this.tension = r);
    }
    getPoint(t, e = new J()) {
      const s = e,
        r = this.points,
        i = r.length,
        n = (i - (this.closed ? 0 : 1)) * t;
      let h,
        a,
        o = Math.floor(n),
        u = n - o;
      this.closed
        ? (o += o > 0 ? 0 : (Math.floor(Math.abs(o) / i) + 1) * i)
        : 0 === u && o === i - 1 && ((o = i - 2), (u = 1)),
        this.closed || o > 0
          ? (h = r[(o - 1) % i])
          : (ve.subVectors(r[0], r[1]).add(r[0]), (h = ve));
      const l = r[o % i],
        c = r[(o + 1) % i];
      if (
        (this.closed || o + 2 < i
          ? (a = r[(o + 2) % i])
          : (ve.subVectors(r[i - 1], r[i - 2]).add(r[i - 1]), (a = ve)),
        "centripetal" === this.curveType || "chordal" === this.curveType)
      ) {
        const t = "chordal" === this.curveType ? 0.5 : 0.25;
        let e = Math.pow(h.distanceToSquared(l), t),
          s = Math.pow(l.distanceToSquared(c), t),
          r = Math.pow(c.distanceToSquared(a), t);
        s < 1e-4 && (s = 1),
          e < 1e-4 && (e = s),
          r < 1e-4 && (r = s),
          Ae.initNonuniformCatmullRom(h.x, l.x, c.x, a.x, e, s, r),
          ze.initNonuniformCatmullRom(h.y, l.y, c.y, a.y, e, s, r),
          Ce.initNonuniformCatmullRom(h.z, l.z, c.z, a.z, e, s, r);
      } else "catmullrom" === this.curveType && (Ae.initCatmullRom(h.x, l.x, c.x, a.x, this.tension), ze.initCatmullRom(h.y, l.y, c.y, a.y, this.tension), Ce.initCatmullRom(h.z, l.z, c.z, a.z, this.tension));
      return s.set(Ae.calc(u), ze.calc(u), Ce.calc(u)), s;
    }
    copy(t) {
      super.copy(t), (this.points = []);
      for (let e = 0, s = t.points.length; e < s; e++) {
        const s = t.points[e];
        this.points.push(s.clone());
      }
      return (
        (this.closed = t.closed),
        (this.curveType = t.curveType),
        (this.tension = t.tension),
        this
      );
    }
    toJSON() {
      const t = super.toJSON();
      t.points = [];
      for (let e = 0, s = this.points.length; e < s; e++) {
        const s = this.points[e];
        t.points.push(s.toArray());
      }
      return (
        (t.closed = this.closed),
        (t.curveType = this.curveType),
        (t.tension = this.tension),
        t
      );
    }
    fromJSON(t) {
      super.fromJSON(t), (this.points = []);
      for (let e = 0, s = t.points.length; e < s; e++) {
        const s = t.points[e];
        this.points.push(new J().fromArray(s));
      }
      return (
        (this.closed = t.closed),
        (this.curveType = t.curveType),
        (this.tension = t.tension),
        this
      );
    }
  }
  function Re(t, e, s, r, i) {
    const n = 0.5 * (r - e),
      h = 0.5 * (i - s),
      a = t * t;
    return (
      (2 * s - 2 * r + n + h) * (t * a) +
      (-3 * s + 3 * r - 2 * n - h) * a +
      n * t +
      s
    );
  }
  function Ee(t, e, s, r) {
    return (
      (function (t, e) {
        const s = 1 - t;
        return s * s * e;
      })(t, e) +
      (function (t, e) {
        return 2 * (1 - t) * t * e;
      })(t, s) +
      (function (t, e) {
        return t * t * e;
      })(t, r)
    );
  }
  function Pe(t, e, s, r, i) {
    return (
      (function (t, e) {
        const s = 1 - t;
        return s * s * s * e;
      })(t, e) +
      (function (t, e) {
        const s = 1 - t;
        return 3 * s * s * t * e;
      })(t, s) +
      (function (t, e) {
        return 3 * (1 - t) * t * t * e;
      })(t, r) +
      (function (t, e) {
        return t * t * t * e;
      })(t, i)
    );
  }
  class Te extends Se {
    constructor(t = new Mt(), e = new Mt(), s = new Mt(), r = new Mt()) {
      super(),
        (this.isCubicBezierCurve = !0),
        (this.type = "CubicBezierCurve"),
        (this.v0 = t),
        (this.v1 = e),
        (this.v2 = s),
        (this.v3 = r);
    }
    getPoint(t, e = new Mt()) {
      const s = e,
        r = this.v0,
        i = this.v1,
        n = this.v2,
        h = this.v3;
      return s.set(Pe(t, r.x, i.x, n.x, h.x), Pe(t, r.y, i.y, n.y, h.y)), s;
    }
    copy(t) {
      return (
        super.copy(t),
        this.v0.copy(t.v0),
        this.v1.copy(t.v1),
        this.v2.copy(t.v2),
        this.v3.copy(t.v3),
        this
      );
    }
    toJSON() {
      const t = super.toJSON();
      return (
        (t.v0 = this.v0.toArray()),
        (t.v1 = this.v1.toArray()),
        (t.v2 = this.v2.toArray()),
        (t.v3 = this.v3.toArray()),
        t
      );
    }
    fromJSON(t) {
      return (
        super.fromJSON(t),
        this.v0.fromArray(t.v0),
        this.v1.fromArray(t.v1),
        this.v2.fromArray(t.v2),
        this.v3.fromArray(t.v3),
        this
      );
    }
  }
  class Oe extends Se {
    constructor(t = new J(), e = new J(), s = new J(), r = new J()) {
      super(),
        (this.isCubicBezierCurve3 = !0),
        (this.type = "CubicBezierCurve3"),
        (this.v0 = t),
        (this.v1 = e),
        (this.v2 = s),
        (this.v3 = r);
    }
    getPoint(t, e = new J()) {
      const s = e,
        r = this.v0,
        i = this.v1,
        n = this.v2,
        h = this.v3;
      return (
        s.set(
          Pe(t, r.x, i.x, n.x, h.x),
          Pe(t, r.y, i.y, n.y, h.y),
          Pe(t, r.z, i.z, n.z, h.z)
        ),
        s
      );
    }
    copy(t) {
      return (
        super.copy(t),
        this.v0.copy(t.v0),
        this.v1.copy(t.v1),
        this.v2.copy(t.v2),
        this.v3.copy(t.v3),
        this
      );
    }
    toJSON() {
      const t = super.toJSON();
      return (
        (t.v0 = this.v0.toArray()),
        (t.v1 = this.v1.toArray()),
        (t.v2 = this.v2.toArray()),
        (t.v3 = this.v3.toArray()),
        t
      );
    }
    fromJSON(t) {
      return (
        super.fromJSON(t),
        this.v0.fromArray(t.v0),
        this.v1.fromArray(t.v1),
        this.v2.fromArray(t.v2),
        this.v3.fromArray(t.v3),
        this
      );
    }
  }
  class Ne extends Se {
    constructor(t = new Mt(), e = new Mt()) {
      super(),
        (this.isLineCurve = !0),
        (this.type = "LineCurve"),
        (this.v1 = t),
        (this.v2 = e);
    }
    getPoint(t, e = new Mt()) {
      const s = e;
      return (
        1 === t
          ? s.copy(this.v2)
          : (s.copy(this.v2).sub(this.v1), s.multiplyScalar(t).add(this.v1)),
        s
      );
    }
    getPointAt(t, e) {
      return this.getPoint(t, e);
    }
    getTangent(t, e) {
      const s = e || new Mt();
      return s.copy(this.v2).sub(this.v1).normalize(), s;
    }
    copy(t) {
      return super.copy(t), this.v1.copy(t.v1), this.v2.copy(t.v2), this;
    }
    toJSON() {
      const t = super.toJSON();
      return (t.v1 = this.v1.toArray()), (t.v2 = this.v2.toArray()), t;
    }
    fromJSON(t) {
      return (
        super.fromJSON(t),
        this.v1.fromArray(t.v1),
        this.v2.fromArray(t.v2),
        this
      );
    }
  }
  class Fe extends Se {
    constructor(t = new J(), e = new J()) {
      super(),
        (this.isLineCurve3 = !0),
        (this.type = "LineCurve3"),
        (this.v1 = t),
        (this.v2 = e);
    }
    getPoint(t, e = new J()) {
      const s = e;
      return (
        1 === t
          ? s.copy(this.v2)
          : (s.copy(this.v2).sub(this.v1), s.multiplyScalar(t).add(this.v1)),
        s
      );
    }
    getPointAt(t, e) {
      return this.getPoint(t, e);
    }
    copy(t) {
      return super.copy(t), this.v1.copy(t.v1), this.v2.copy(t.v2), this;
    }
    toJSON() {
      const t = super.toJSON();
      return (t.v1 = this.v1.toArray()), (t.v2 = this.v2.toArray()), t;
    }
    fromJSON(t) {
      return (
        super.fromJSON(t),
        this.v1.fromArray(t.v1),
        this.v2.fromArray(t.v2),
        this
      );
    }
  }
  class ke extends Se {
    constructor(t = new Mt(), e = new Mt(), s = new Mt()) {
      super(),
        (this.isQuadraticBezierCurve = !0),
        (this.type = "QuadraticBezierCurve"),
        (this.v0 = t),
        (this.v1 = e),
        (this.v2 = s);
    }
    getPoint(t, e = new Mt()) {
      const s = e,
        r = this.v0,
        i = this.v1,
        n = this.v2;
      return s.set(Ee(t, r.x, i.x, n.x), Ee(t, r.y, i.y, n.y)), s;
    }
    copy(t) {
      return (
        super.copy(t),
        this.v0.copy(t.v0),
        this.v1.copy(t.v1),
        this.v2.copy(t.v2),
        this
      );
    }
    toJSON() {
      const t = super.toJSON();
      return (
        (t.v0 = this.v0.toArray()),
        (t.v1 = this.v1.toArray()),
        (t.v2 = this.v2.toArray()),
        t
      );
    }
    fromJSON(t) {
      return (
        super.fromJSON(t),
        this.v0.fromArray(t.v0),
        this.v1.fromArray(t.v1),
        this.v2.fromArray(t.v2),
        this
      );
    }
  }
  class Ge extends Se {
    constructor(t = new J(), e = new J(), s = new J()) {
      super(),
        (this.isQuadraticBezierCurve3 = !0),
        (this.type = "QuadraticBezierCurve3"),
        (this.v0 = t),
        (this.v1 = e),
        (this.v2 = s);
    }
    getPoint(t, e = new J()) {
      const s = e,
        r = this.v0,
        i = this.v1,
        n = this.v2;
      return (
        s.set(Ee(t, r.x, i.x, n.x), Ee(t, r.y, i.y, n.y), Ee(t, r.z, i.z, n.z)),
        s
      );
    }
    copy(t) {
      return (
        super.copy(t),
        this.v0.copy(t.v0),
        this.v1.copy(t.v1),
        this.v2.copy(t.v2),
        this
      );
    }
    toJSON() {
      const t = super.toJSON();
      return (
        (t.v0 = this.v0.toArray()),
        (t.v1 = this.v1.toArray()),
        (t.v2 = this.v2.toArray()),
        t
      );
    }
    fromJSON(t) {
      return (
        super.fromJSON(t),
        this.v0.fromArray(t.v0),
        this.v1.fromArray(t.v1),
        this.v2.fromArray(t.v2),
        this
      );
    }
  }
  class Le extends Se {
    constructor(t = []) {
      super(),
        (this.isSplineCurve = !0),
        (this.type = "SplineCurve"),
        (this.points = t);
    }
    getPoint(t, e = new Mt()) {
      const s = e,
        r = this.points,
        i = (r.length - 1) * t,
        n = Math.floor(i),
        h = i - n,
        a = r[0 === n ? n : n - 1],
        o = r[n],
        u = r[n > r.length - 2 ? r.length - 1 : n + 1],
        l = r[n > r.length - 3 ? r.length - 1 : n + 2];
      return s.set(Re(h, a.x, o.x, u.x, l.x), Re(h, a.y, o.y, u.y, l.y)), s;
    }
    copy(t) {
      super.copy(t), (this.points = []);
      for (let e = 0, s = t.points.length; e < s; e++) {
        const s = t.points[e];
        this.points.push(s.clone());
      }
      return this;
    }
    toJSON() {
      const t = super.toJSON();
      t.points = [];
      for (let e = 0, s = this.points.length; e < s; e++) {
        const s = this.points[e];
        t.points.push(s.toArray());
      }
      return t;
    }
    fromJSON(t) {
      super.fromJSON(t), (this.points = []);
      for (let e = 0, s = t.points.length; e < s; e++) {
        const s = t.points[e];
        this.points.push(new Mt().fromArray(s));
      }
      return this;
    }
  }
  var Ie = Object.freeze({
    __proto__: null,
    ArcCurve: we,
    CatmullRomCurve3: Be,
    CubicBezierCurve: Te,
    CubicBezierCurve3: Oe,
    EllipseCurve: Me,
    LineCurve: Ne,
    LineCurve3: Fe,
    QuadraticBezierCurve: ke,
    QuadraticBezierCurve3: Ge,
    SplineCurve: Le,
  });
  class De extends Se {
    constructor() {
      super(),
        (this.type = "CurvePath"),
        (this.curves = []),
        (this.autoClose = !1);
    }
    add(t) {
      this.curves.push(t);
    }
    closePath() {
      const t = this.curves[0].getPoint(0),
        e = this.curves[this.curves.length - 1].getPoint(1);
      t.equals(e) || this.curves.push(new Ne(e, t));
    }
    getPoint(t, e) {
      const s = t * this.getLength(),
        r = this.getCurveLengths();
      let i = 0;
      for (; i < r.length; ) {
        if (r[i] >= s) {
          const t = r[i] - s,
            n = this.curves[i],
            h = n.getLength(),
            a = 0 === h ? 0 : 1 - t / h;
          return n.getPointAt(a, e);
        }
        i++;
      }
      return null;
    }
    getLength() {
      const t = this.getCurveLengths();
      return t[t.length - 1];
    }
    updateArcLengths() {
      (this.needsUpdate = !0),
        (this.cacheLengths = null),
        this.getCurveLengths();
    }
    getCurveLengths() {
      if (this.cacheLengths && this.cacheLengths.length === this.curves.length)
        return this.cacheLengths;
      const t = [];
      let e = 0;
      for (let s = 0, r = this.curves.length; s < r; s++)
        (e += this.curves[s].getLength()), t.push(e);
      return (this.cacheLengths = t), t;
    }
    getSpacedPoints(t = 40) {
      const e = [];
      for (let s = 0; s <= t; s++) e.push(this.getPoint(s / t));
      return this.autoClose && e.push(e[0]), e;
    }
    getPoints(t = 12) {
      const e = [];
      let s;
      for (let r = 0, i = this.curves; r < i.length; r++) {
        const n = i[r],
          h = n.isEllipseCurve
            ? 2 * t
            : n.isLineCurve || n.isLineCurve3
            ? 1
            : n.isSplineCurve
            ? t * n.points.length
            : t,
          a = n.getPoints(h);
        for (let t = 0; t < a.length; t++) {
          const r = a[t];
          (s && s.equals(r)) || (e.push(r), (s = r));
        }
      }
      return (
        this.autoClose &&
          e.length > 1 &&
          !e[e.length - 1].equals(e[0]) &&
          e.push(e[0]),
        e
      );
    }
    copy(t) {
      super.copy(t), (this.curves = []);
      for (let e = 0, s = t.curves.length; e < s; e++) {
        const s = t.curves[e];
        this.curves.push(s.clone());
      }
      return (this.autoClose = t.autoClose), this;
    }
    toJSON() {
      const t = super.toJSON();
      (t.autoClose = this.autoClose), (t.curves = []);
      for (let e = 0, s = this.curves.length; e < s; e++) {
        const s = this.curves[e];
        t.curves.push(s.toJSON());
      }
      return t;
    }
    fromJSON(t) {
      super.fromJSON(t), (this.autoClose = t.autoClose), (this.curves = []);
      for (let e = 0, s = t.curves.length; e < s; e++) {
        const s = t.curves[e];
        this.curves.push(new Ie[s.type]().fromJSON(s));
      }
      return this;
    }
  }
  class Ve extends De {
    constructor(t) {
      super(),
        (this.type = "Path"),
        (this.currentPoint = new Mt()),
        t && this.setFromPoints(t);
    }
    setFromPoints(t) {
      this.moveTo(t[0].x, t[0].y);
      for (let e = 1, s = t.length; e < s; e++) this.lineTo(t[e].x, t[e].y);
      return this;
    }
    moveTo(t, e) {
      return this.currentPoint.set(t, e), this;
    }
    lineTo(t, e) {
      const s = new Ne(this.currentPoint.clone(), new Mt(t, e));
      return this.curves.push(s), this.currentPoint.set(t, e), this;
    }
    quadraticCurveTo(t, e, s, r) {
      const i = new ke(this.currentPoint.clone(), new Mt(t, e), new Mt(s, r));
      return this.curves.push(i), this.currentPoint.set(s, r), this;
    }
    bezierCurveTo(t, e, s, r, i, n) {
      const h = new Te(
        this.currentPoint.clone(),
        new Mt(t, e),
        new Mt(s, r),
        new Mt(i, n)
      );
      return this.curves.push(h), this.currentPoint.set(i, n), this;
    }
    splineThru(t) {
      const e = [this.currentPoint.clone()].concat(t),
        s = new Le(e);
      return this.curves.push(s), this.currentPoint.copy(t[t.length - 1]), this;
    }
    arc(t, e, s, r, i, n) {
      const h = this.currentPoint.x,
        a = this.currentPoint.y;
      return this.absarc(t + h, e + a, s, r, i, n), this;
    }
    absarc(t, e, s, r, i, n) {
      return this.absellipse(t, e, s, s, r, i, n), this;
    }
    ellipse(t, e, s, r, i, n, h, a) {
      const o = this.currentPoint.x,
        u = this.currentPoint.y;
      return this.absellipse(t + o, e + u, s, r, i, n, h, a), this;
    }
    absellipse(t, e, s, r, i, n, h, a) {
      const o = new Me(t, e, s, r, i, n, h, a);
      if (this.curves.length > 0) {
        const t = o.getPoint(0);
        t.equals(this.currentPoint) || this.lineTo(t.x, t.y);
      }
      this.curves.push(o);
      const u = o.getPoint(1);
      return this.currentPoint.copy(u), this;
    }
    copy(t) {
      return super.copy(t), this.currentPoint.copy(t.currentPoint), this;
    }
    toJSON() {
      const t = super.toJSON();
      return (t.currentPoint = this.currentPoint.toArray()), t;
    }
    fromJSON(t) {
      return (
        super.fromJSON(t), this.currentPoint.fromArray(t.currentPoint), this
      );
    }
  }
  class qe extends ge {
    constructor(
      t = [new Mt(0, -0.5), new Mt(0.5, 0), new Mt(0, 0.5)],
      e = 12,
      s = 0,
      r = 2 * Math.PI
    ) {
      super(),
        (this.type = "LatheGeometry"),
        (this.parameters = {
          points: t,
          segments: e,
          phiStart: s,
          phiLength: r,
        }),
        (e = Math.floor(e)),
        (r = I(r, 0, 2 * Math.PI));
      const i = [],
        n = [],
        h = [],
        a = [],
        o = [],
        u = 1 / e,
        l = new J(),
        c = new Mt(),
        d = new J(),
        m = new J(),
        y = new J();
      let p = 0,
        x = 0;
      for (let e = 0; e <= t.length - 1; e++)
        switch (e) {
          case 0:
            (p = t[e + 1].x - t[e].x),
              (x = t[e + 1].y - t[e].y),
              (d.x = 1 * x),
              (d.y = -p),
              (d.z = 0 * x),
              y.copy(d),
              d.normalize(),
              a.push(d.x, d.y, d.z);
            break;
          case t.length - 1:
            a.push(y.x, y.y, y.z);
            break;
          default:
            (p = t[e + 1].x - t[e].x),
              (x = t[e + 1].y - t[e].y),
              (d.x = 1 * x),
              (d.y = -p),
              (d.z = 0 * x),
              m.copy(d),
              (d.x += y.x),
              (d.y += y.y),
              (d.z += y.z),
              d.normalize(),
              a.push(d.x, d.y, d.z),
              y.copy(m);
        }
      for (let i = 0; i <= e; i++) {
        const d = s + i * u * r,
          m = Math.sin(d),
          y = Math.cos(d);
        for (let s = 0; s <= t.length - 1; s++) {
          (l.x = t[s].x * m),
            (l.y = t[s].y),
            (l.z = t[s].x * y),
            n.push(l.x, l.y, l.z),
            (c.x = i / e),
            (c.y = s / (t.length - 1)),
            h.push(c.x, c.y);
          const r = a[3 * s + 0] * m,
            u = a[3 * s + 1],
            d = a[3 * s + 0] * y;
          o.push(r, u, d);
        }
      }
      for (let s = 0; s < e; s++)
        for (let e = 0; e < t.length - 1; e++) {
          const r = e + s * t.length,
            n = r,
            h = r + t.length,
            a = r + t.length + 1,
            o = r + 1;
          i.push(n, h, o), i.push(a, o, h);
        }
      this.setIndex(i),
        this.setAttribute("position", new ne(n, 3)),
        this.setAttribute("uv", new ne(h, 2)),
        this.setAttribute("normal", new ne(o, 3));
    }
    static fromJSON(t) {
      return new qe(t.points, t.segments, t.phiStart, t.phiLength);
    }
  }
  class Ue extends qe {
    constructor(t = 1, e = 1, s = 4, r = 8) {
      const i = new Ve();
      i.absarc(0, -e / 2, t, 1.5 * Math.PI, 0),
        i.absarc(0, e / 2, t, 0, 0.5 * Math.PI),
        super(i.getPoints(s), r),
        (this.type = "CapsuleGeometry"),
        (this.parameters = {
          radius: t,
          height: e,
          capSegments: s,
          radialSegments: r,
        });
    }
    static fromJSON(t) {
      return new Ue(t.radius, t.length, t.capSegments, t.radialSegments);
    }
  }
  class Je extends ge {
    constructor(t = 1, e = 8, s = 0, r = 2 * Math.PI) {
      super(),
        (this.type = "CircleGeometry"),
        (this.parameters = {
          radius: t,
          segments: e,
          thetaStart: s,
          thetaLength: r,
        }),
        (e = Math.max(3, e));
      const i = [],
        n = [],
        h = [],
        a = [],
        o = new J(),
        u = new Mt();
      n.push(0, 0, 0), h.push(0, 0, 1), a.push(0.5, 0.5);
      for (let i = 0, l = 3; i <= e; i++, l += 3) {
        const c = s + (i / e) * r;
        (o.x = t * Math.cos(c)),
          (o.y = t * Math.sin(c)),
          n.push(o.x, o.y, o.z),
          h.push(0, 0, 1),
          (u.x = (n[l] / t + 1) / 2),
          (u.y = (n[l + 1] / t + 1) / 2),
          a.push(u.x, u.y);
      }
      for (let t = 1; t <= e; t++) i.push(t, t + 1, 0);
      this.setIndex(i),
        this.setAttribute("position", new ne(n, 3)),
        this.setAttribute("normal", new ne(h, 3)),
        this.setAttribute("uv", new ne(a, 2));
    }
    static fromJSON(t) {
      return new Je(t.radius, t.segments, t.thetaStart, t.thetaLength);
    }
  }
  class He extends ge {
    constructor(
      t = 1,
      e = 1,
      s = 1,
      r = 8,
      i = 1,
      n = !1,
      h = 0,
      a = 2 * Math.PI
    ) {
      super(),
        (this.type = "CylinderGeometry"),
        (this.parameters = {
          radiusTop: t,
          radiusBottom: e,
          height: s,
          radialSegments: r,
          heightSegments: i,
          openEnded: n,
          thetaStart: h,
          thetaLength: a,
        });
      const o = this;
      (r = Math.floor(r)), (i = Math.floor(i));
      const u = [],
        l = [],
        c = [],
        d = [];
      let m = 0;
      const y = [],
        p = s / 2;
      let x = 0;
      function f(s) {
        const i = m,
          n = new Mt(),
          y = new J();
        let f = 0;
        const g = !0 === s ? t : e,
          b = !0 === s ? 1 : -1;
        for (let t = 1; t <= r; t++)
          l.push(0, p * b, 0), c.push(0, b, 0), d.push(0.5, 0.5), m++;
        const S = m;
        for (let t = 0; t <= r; t++) {
          const e = (t / r) * a + h,
            s = Math.cos(e),
            i = Math.sin(e);
          (y.x = g * i),
            (y.y = p * b),
            (y.z = g * s),
            l.push(y.x, y.y, y.z),
            c.push(0, b, 0),
            (n.x = 0.5 * s + 0.5),
            (n.y = 0.5 * i * b + 0.5),
            d.push(n.x, n.y),
            m++;
        }
        for (let t = 0; t < r; t++) {
          const e = i + t,
            r = S + t;
          !0 === s ? u.push(r, r + 1, e) : u.push(r + 1, r, e), (f += 3);
        }
        o.addGroup(x, f, !0 === s ? 1 : 2), (x += f);
      }
      !(function () {
        const n = new J(),
          f = new J();
        let g = 0;
        const b = (e - t) / s;
        for (let o = 0; o <= i; o++) {
          const u = [],
            x = o / i,
            g = x * (e - t) + t;
          for (let t = 0; t <= r; t++) {
            const e = t / r,
              i = e * a + h,
              o = Math.sin(i),
              y = Math.cos(i);
            (f.x = g * o),
              (f.y = -x * s + p),
              (f.z = g * y),
              l.push(f.x, f.y, f.z),
              n.set(o, b, y).normalize(),
              c.push(n.x, n.y, n.z),
              d.push(e, 1 - x),
              u.push(m++);
          }
          y.push(u);
        }
        for (let t = 0; t < r; t++)
          for (let e = 0; e < i; e++) {
            const s = y[e][t],
              r = y[e + 1][t],
              i = y[e + 1][t + 1],
              n = y[e][t + 1];
            u.push(s, r, n), u.push(r, i, n), (g += 6);
          }
        o.addGroup(x, g, 0), (x += g);
      })(),
        !1 === n && (t > 0 && f(!0), e > 0 && f(!1)),
        this.setIndex(u),
        this.setAttribute("position", new ne(l, 3)),
        this.setAttribute("normal", new ne(c, 3)),
        this.setAttribute("uv", new ne(d, 2));
    }
    static fromJSON(t) {
      return new He(
        t.radiusTop,
        t.radiusBottom,
        t.height,
        t.radialSegments,
        t.heightSegments,
        t.openEnded,
        t.thetaStart,
        t.thetaLength
      );
    }
  }
  class Xe extends He {
    constructor(t = 1, e = 1, s = 8, r = 1, i = !1, n = 0, h = 2 * Math.PI) {
      super(0, t, e, s, r, i, n, h),
        (this.type = "ConeGeometry"),
        (this.parameters = {
          radius: t,
          height: e,
          radialSegments: s,
          heightSegments: r,
          openEnded: i,
          thetaStart: n,
          thetaLength: h,
        });
    }
    static fromJSON(t) {
      return new Xe(
        t.radius,
        t.height,
        t.radialSegments,
        t.heightSegments,
        t.openEnded,
        t.thetaStart,
        t.thetaLength
      );
    }
  }
  class We extends ge {
    constructor(t = [], e = [], s = 1, r = 0) {
      super(),
        (this.type = "PolyhedronGeometry"),
        (this.parameters = { vertices: t, indices: e, radius: s, detail: r });
      const i = [],
        n = [];
      function h(t, e, s, r) {
        const i = r + 1,
          n = [];
        for (let r = 0; r <= i; r++) {
          n[r] = [];
          const h = t.clone().lerp(s, r / i),
            a = e.clone().lerp(s, r / i),
            o = i - r;
          for (let t = 0; t <= o; t++)
            n[r][t] = 0 === t && r === i ? h : h.clone().lerp(a, t / o);
        }
        for (let t = 0; t < i; t++)
          for (let e = 0; e < 2 * (i - t) - 1; e++) {
            const s = Math.floor(e / 2);
            e % 2 == 0
              ? (a(n[t][s + 1]), a(n[t + 1][s]), a(n[t][s]))
              : (a(n[t][s + 1]), a(n[t + 1][s + 1]), a(n[t + 1][s]));
          }
      }
      function a(t) {
        i.push(t.x, t.y, t.z);
      }
      function o(e, s) {
        const r = 3 * e;
        (s.x = t[r + 0]), (s.y = t[r + 1]), (s.z = t[r + 2]);
      }
      function u(t, e, s, r) {
        r < 0 && 1 === t.x && (n[e] = t.x - 1),
          0 === s.x && 0 === s.z && (n[e] = r / 2 / Math.PI + 0.5);
      }
      function l(t) {
        return Math.atan2(t.z, -t.x);
      }
      !(function (t) {
        const s = new J(),
          r = new J(),
          i = new J();
        for (let n = 0; n < e.length; n += 3)
          o(e[n + 0], s), o(e[n + 1], r), o(e[n + 2], i), h(s, r, i, t);
      })(r),
        (function (t) {
          const e = new J();
          for (let s = 0; s < i.length; s += 3)
            (e.x = i[s + 0]),
              (e.y = i[s + 1]),
              (e.z = i[s + 2]),
              e.normalize().multiplyScalar(t),
              (i[s + 0] = e.x),
              (i[s + 1] = e.y),
              (i[s + 2] = e.z);
        })(s),
        (function () {
          const t = new J();
          for (let s = 0; s < i.length; s += 3) {
            (t.x = i[s + 0]), (t.y = i[s + 1]), (t.z = i[s + 2]);
            const r = l(t) / 2 / Math.PI + 0.5,
              h =
                ((e = t),
                Math.atan2(-e.y, Math.sqrt(e.x * e.x + e.z * e.z)) / Math.PI +
                  0.5);
            n.push(r, 1 - h);
          }
          var e;
          (function () {
            const t = new J(),
              e = new J(),
              s = new J(),
              r = new J(),
              h = new Mt(),
              a = new Mt(),
              o = new Mt();
            for (let c = 0, d = 0; c < i.length; c += 9, d += 6) {
              t.set(i[c + 0], i[c + 1], i[c + 2]),
                e.set(i[c + 3], i[c + 4], i[c + 5]),
                s.set(i[c + 6], i[c + 7], i[c + 8]),
                h.set(n[d + 0], n[d + 1]),
                a.set(n[d + 2], n[d + 3]),
                o.set(n[d + 4], n[d + 5]),
                r.copy(t).add(e).add(s).divideScalar(3);
              const m = l(r);
              u(h, d + 0, t, m), u(a, d + 2, e, m), u(o, d + 4, s, m);
            }
          })(),
            (function () {
              for (let t = 0; t < n.length; t += 6) {
                const e = n[t + 0],
                  s = n[t + 2],
                  r = n[t + 4],
                  i = Math.max(e, s, r),
                  h = Math.min(e, s, r);
                i > 0.9 &&
                  h < 0.1 &&
                  (e < 0.2 && (n[t + 0] += 1),
                  s < 0.2 && (n[t + 2] += 1),
                  r < 0.2 && (n[t + 4] += 1));
              }
            })();
        })(),
        this.setAttribute("position", new ne(i, 3)),
        this.setAttribute("normal", new ne(i.slice(), 3)),
        this.setAttribute("uv", new ne(n, 2)),
        0 === r ? this.computeVertexNormals() : this.normalizeNormals();
    }
    static fromJSON(t) {
      return new We(t.vertices, t.indices, t.radius, t.details);
    }
  }
  class Ze extends We {
    constructor(t = 1, e = 0) {
      const s = (1 + Math.sqrt(5)) / 2,
        r = 1 / s;
      super(
        [
          -1,
          -1,
          -1,
          -1,
          -1,
          1,
          -1,
          1,
          -1,
          -1,
          1,
          1,
          1,
          -1,
          -1,
          1,
          -1,
          1,
          1,
          1,
          -1,
          1,
          1,
          1,
          0,
          -r,
          -s,
          0,
          -r,
          s,
          0,
          r,
          -s,
          0,
          r,
          s,
          -r,
          -s,
          0,
          -r,
          s,
          0,
          r,
          -s,
          0,
          r,
          s,
          0,
          -s,
          0,
          -r,
          s,
          0,
          -r,
          -s,
          0,
          r,
          s,
          0,
          r,
        ],
        [
          3, 11, 7, 3, 7, 15, 3, 15, 13, 7, 19, 17, 7, 17, 6, 7, 6, 15, 17, 4,
          8, 17, 8, 10, 17, 10, 6, 8, 0, 16, 8, 16, 2, 8, 2, 10, 0, 12, 1, 0, 1,
          18, 0, 18, 16, 6, 10, 2, 6, 2, 13, 6, 13, 15, 2, 16, 18, 2, 18, 3, 2,
          3, 13, 18, 1, 9, 18, 9, 11, 18, 11, 3, 4, 14, 12, 4, 12, 0, 4, 0, 8,
          11, 9, 5, 11, 5, 19, 11, 19, 7, 19, 5, 14, 19, 14, 4, 19, 4, 17, 1,
          12, 14, 1, 14, 5, 1, 5, 9,
        ],
        t,
        e
      ),
        (this.type = "DodecahedronGeometry"),
        (this.parameters = { radius: t, detail: e });
    }
    static fromJSON(t) {
      return new Ze(t.radius, t.detail);
    }
  }
  const Ye = new J(),
    je = new J(),
    $e = new J(),
    Ke = new J(),
    Qe = new J(),
    ts = new J(),
    es = new J(),
    ss = new J(),
    rs = new J(),
    is = new J();
  class ns {
    constructor(t = new J(), e = new J(), s = new J()) {
      (this.a = t), (this.b = e), (this.c = s);
    }
    static getNormal(t, e, s, r) {
      r.subVectors(s, e), Ye.subVectors(t, e), r.cross(Ye);
      const i = r.lengthSq();
      return i > 0 ? r.multiplyScalar(1 / Math.sqrt(i)) : r.set(0, 0, 0);
    }
    static getBarycoord(t, e, s, r, i) {
      Ye.subVectors(r, e), je.subVectors(s, e), $e.subVectors(t, e);
      const n = Ye.dot(Ye),
        h = Ye.dot(je),
        a = Ye.dot($e),
        o = je.dot(je),
        u = je.dot($e),
        l = n * o - h * h;
      if (0 === l) return i.set(-2, -1, -1);
      const c = 1 / l,
        d = (o * a - h * u) * c,
        m = (n * u - h * a) * c;
      return i.set(1 - d - m, m, d);
    }
    static containsPoint(t, e, s, r) {
      return (
        this.getBarycoord(t, e, s, r, Ke),
        Ke.x >= 0 && Ke.y >= 0 && Ke.x + Ke.y <= 1
      );
    }
    static getUV(t, e, s, r, i, n, h, a) {
      return (
        this.getBarycoord(t, e, s, r, Ke),
        a.set(0, 0),
        a.addScaledVector(i, Ke.x),
        a.addScaledVector(n, Ke.y),
        a.addScaledVector(h, Ke.z),
        a
      );
    }
    static isFrontFacing(t, e, s, r) {
      return Ye.subVectors(s, e), je.subVectors(t, e), Ye.cross(je).dot(r) < 0;
    }
    set(t, e, s) {
      return this.a.copy(t), this.b.copy(e), this.c.copy(s), this;
    }
    setFromPointsAndIndices(t, e, s, r) {
      return this.a.copy(t[e]), this.b.copy(t[s]), this.c.copy(t[r]), this;
    }
    setFromAttributeAndIndices(t, e, s, r) {
      return (
        this.a.fromBufferAttribute(t, e),
        this.b.fromBufferAttribute(t, s),
        this.c.fromBufferAttribute(t, r),
        this
      );
    }
    clone() {
      return new this.constructor().copy(this);
    }
    copy(t) {
      return this.a.copy(t.a), this.b.copy(t.b), this.c.copy(t.c), this;
    }
    getArea() {
      return (
        Ye.subVectors(this.c, this.b),
        je.subVectors(this.a, this.b),
        0.5 * Ye.cross(je).length()
      );
    }
    getMidpoint(t) {
      return t
        .addVectors(this.a, this.b)
        .add(this.c)
        .multiplyScalar(1 / 3);
    }
    getNormal(t) {
      return ns.getNormal(this.a, this.b, this.c, t);
    }
    getPlane(t) {
      return t.setFromCoplanarPoints(this.a, this.b, this.c);
    }
    getBarycoord(t, e) {
      return ns.getBarycoord(t, this.a, this.b, this.c, e);
    }
    getUV(t, e, s, r, i) {
      return ns.getUV(t, this.a, this.b, this.c, e, s, r, i);
    }
    containsPoint(t) {
      return ns.containsPoint(t, this.a, this.b, this.c);
    }
    isFrontFacing(t) {
      return ns.isFrontFacing(this.a, this.b, this.c, t);
    }
    intersectsBox(t) {
      return t.intersectsTriangle(this);
    }
    closestPointToPoint(t, e) {
      const s = this.a,
        r = this.b,
        i = this.c;
      let n, h;
      Qe.subVectors(r, s), ts.subVectors(i, s), ss.subVectors(t, s);
      const a = Qe.dot(ss),
        o = ts.dot(ss);
      if (a <= 0 && o <= 0) return e.copy(s);
      rs.subVectors(t, r);
      const u = Qe.dot(rs),
        l = ts.dot(rs);
      if (u >= 0 && l <= u) return e.copy(r);
      const c = a * l - u * o;
      if (c <= 0 && a >= 0 && u <= 0)
        return (n = a / (a - u)), e.copy(s).addScaledVector(Qe, n);
      is.subVectors(t, i);
      const d = Qe.dot(is),
        m = ts.dot(is);
      if (m >= 0 && d <= m) return e.copy(i);
      const y = d * o - a * m;
      if (y <= 0 && o >= 0 && m <= 0)
        return (h = o / (o - m)), e.copy(s).addScaledVector(ts, h);
      const p = u * m - d * l;
      if (p <= 0 && l - u >= 0 && d - m >= 0)
        return (
          es.subVectors(i, r),
          (h = (l - u) / (l - u + (d - m))),
          e.copy(r).addScaledVector(es, h)
        );
      const x = 1 / (p + y + c);
      return (
        (n = y * x),
        (h = c * x),
        e.copy(s).addScaledVector(Qe, n).addScaledVector(ts, h)
      );
    }
    equals(t) {
      return t.a.equals(this.a) && t.b.equals(this.b) && t.c.equals(this.c);
    }
  }
  const hs = new J(),
    as = new J(),
    os = new J(),
    us = new ns();
  class ls extends Ve {
    constructor(t) {
      super(t), (this.uuid = L()), (this.type = "Shape"), (this.holes = []);
    }
    getPointsHoles(t) {
      const e = [];
      for (let s = 0, r = this.holes.length; s < r; s++)
        e[s] = this.holes[s].getPoints(t);
      return e;
    }
    extractPoints(t) {
      return { shape: this.getPoints(t), holes: this.getPointsHoles(t) };
    }
    copy(t) {
      super.copy(t), (this.holes = []);
      for (let e = 0, s = t.holes.length; e < s; e++) {
        const s = t.holes[e];
        this.holes.push(s.clone());
      }
      return this;
    }
    toJSON() {
      const t = super.toJSON();
      (t.uuid = this.uuid), (t.holes = []);
      for (let e = 0, s = this.holes.length; e < s; e++) {
        const s = this.holes[e];
        t.holes.push(s.toJSON());
      }
      return t;
    }
    fromJSON(t) {
      super.fromJSON(t), (this.uuid = t.uuid), (this.holes = []);
      for (let e = 0, s = t.holes.length; e < s; e++) {
        const s = t.holes[e];
        this.holes.push(new Ve().fromJSON(s));
      }
      return this;
    }
  }
  const cs = function (t, e, s = 2) {
    const r = e && e.length,
      i = r ? e[0] * s : t.length;
    let n = ds(t, 0, i, s, !0);
    const h = [];
    if (!n || n.next === n.prev) return h;
    let a, o, u, l, c, d, m;
    if (
      (r &&
        (n = (function (t, e, s, r) {
          const i = [];
          let n, h, a, o, u;
          for (n = 0, h = e.length; n < h; n++)
            (a = e[n] * r),
              (o = n < h - 1 ? e[n + 1] * r : t.length),
              (u = ds(t, a, o, r, !1)),
              u === u.next && (u.steiner = !0),
              i.push(_s(u));
          for (i.sort(bs), n = 0; n < i.length; n++)
            Ss(i[n], s), (s = ms(s, s.next));
          return s;
        })(t, e, n, s)),
      t.length > 80 * s)
    ) {
      (a = u = t[0]), (o = l = t[1]);
      for (let e = s; e < i; e += s)
        (c = t[e]),
          (d = t[e + 1]),
          c < a && (a = c),
          d < o && (o = d),
          c > u && (u = c),
          d > l && (l = d);
      (m = Math.max(u - a, l - o)), (m = 0 !== m ? 1 / m : 0);
    }
    return ys(n, h, s, a, o, m), h;
  };
  function ds(t, e, s, r, i) {
    let n, h;
    if (
      i ===
      (function (t, e, s, r) {
        let i = 0;
        for (let n = e, h = s - r; n < s; n += r)
          (i += (t[h] - t[n]) * (t[n + 1] + t[h + 1])), (h = n);
        return i;
      })(t, e, s, r) >
        0
    )
      for (n = e; n < s; n += r) h = Os(n, t[n], t[n + 1], h);
    else for (n = s - r; n >= e; n -= r) h = Os(n, t[n], t[n + 1], h);
    return h && Cs(h, h.next) && (Ns(h), (h = h.next)), h;
  }
  function ms(t, e) {
    if (!t) return t;
    e || (e = t);
    let s,
      r = t;
    do {
      if (
        ((s = !1), r.steiner || (!Cs(r, r.next) && 0 !== zs(r.prev, r, r.next)))
      )
        r = r.next;
      else {
        if ((Ns(r), (r = e = r.prev), r === r.next)) break;
        s = !0;
      }
    } while (s || r !== e);
    return e;
  }
  function ys(t, e, s, r, i, n, h) {
    if (!t) return;
    !h &&
      n &&
      (function (t, e, s, r) {
        let i = t;
        do {
          null === i.z && (i.z = ws(i.x, i.y, e, s, r)),
            (i.prevZ = i.prev),
            (i.nextZ = i.next),
            (i = i.next);
        } while (i !== t);
        (i.prevZ.nextZ = null),
          (i.prevZ = null),
          (function (t) {
            let e,
              s,
              r,
              i,
              n,
              h,
              a,
              o,
              u = 1;
            do {
              for (s = t, t = null, n = null, h = 0; s; ) {
                for (
                  h++, r = s, a = 0, e = 0;
                  e < u && (a++, (r = r.nextZ), r);
                  e++
                );
                for (o = u; a > 0 || (o > 0 && r); )
                  0 !== a && (0 === o || !r || s.z <= r.z)
                    ? ((i = s), (s = s.nextZ), a--)
                    : ((i = r), (r = r.nextZ), o--),
                    n ? (n.nextZ = i) : (t = i),
                    (i.prevZ = n),
                    (n = i);
                s = r;
              }
              (n.nextZ = null), (u *= 2);
            } while (h > 1);
          })(i);
      })(t, r, i, n);
    let a,
      o,
      u = t;
    for (; t.prev !== t.next; )
      if (((a = t.prev), (o = t.next), n ? xs(t, r, i, n) : ps(t)))
        e.push(a.i / s),
          e.push(t.i / s),
          e.push(o.i / s),
          Ns(t),
          (t = o.next),
          (u = o.next);
      else if ((t = o) === u) {
        h
          ? 1 === h
            ? ys((t = fs(ms(t), e, s)), e, s, r, i, n, 2)
            : 2 === h && gs(t, e, s, r, i, n)
          : ys(ms(t), e, s, r, i, n, 1);
        break;
      }
  }
  function ps(t) {
    const e = t.prev,
      s = t,
      r = t.next;
    if (zs(e, s, r) >= 0) return !1;
    let i = t.next.next;
    for (; i !== t.prev; ) {
      if (
        vs(e.x, e.y, s.x, s.y, r.x, r.y, i.x, i.y) &&
        zs(i.prev, i, i.next) >= 0
      )
        return !1;
      i = i.next;
    }
    return !0;
  }
  function xs(t, e, s, r) {
    const i = t.prev,
      n = t,
      h = t.next;
    if (zs(i, n, h) >= 0) return !1;
    const a = i.x < n.x ? (i.x < h.x ? i.x : h.x) : n.x < h.x ? n.x : h.x,
      o = i.y < n.y ? (i.y < h.y ? i.y : h.y) : n.y < h.y ? n.y : h.y,
      u = i.x > n.x ? (i.x > h.x ? i.x : h.x) : n.x > h.x ? n.x : h.x,
      l = i.y > n.y ? (i.y > h.y ? i.y : h.y) : n.y > h.y ? n.y : h.y,
      c = ws(a, o, e, s, r),
      d = ws(u, l, e, s, r);
    let m = t.prevZ,
      y = t.nextZ;
    for (; m && m.z >= c && y && y.z <= d; ) {
      if (
        m !== t.prev &&
        m !== t.next &&
        vs(i.x, i.y, n.x, n.y, h.x, h.y, m.x, m.y) &&
        zs(m.prev, m, m.next) >= 0
      )
        return !1;
      if (
        ((m = m.prevZ),
        y !== t.prev &&
          y !== t.next &&
          vs(i.x, i.y, n.x, n.y, h.x, h.y, y.x, y.y) &&
          zs(y.prev, y, y.next) >= 0)
      )
        return !1;
      y = y.nextZ;
    }
    for (; m && m.z >= c; ) {
      if (
        m !== t.prev &&
        m !== t.next &&
        vs(i.x, i.y, n.x, n.y, h.x, h.y, m.x, m.y) &&
        zs(m.prev, m, m.next) >= 0
      )
        return !1;
      m = m.prevZ;
    }
    for (; y && y.z <= d; ) {
      if (
        y !== t.prev &&
        y !== t.next &&
        vs(i.x, i.y, n.x, n.y, h.x, h.y, y.x, y.y) &&
        zs(y.prev, y, y.next) >= 0
      )
        return !1;
      y = y.nextZ;
    }
    return !0;
  }
  function fs(t, e, s) {
    let r = t;
    do {
      const i = r.prev,
        n = r.next.next;
      !Cs(i, n) &&
        Bs(i, r, r.next, n) &&
        Ps(i, n) &&
        Ps(n, i) &&
        (e.push(i.i / s),
        e.push(r.i / s),
        e.push(n.i / s),
        Ns(r),
        Ns(r.next),
        (r = t = n)),
        (r = r.next);
    } while (r !== t);
    return ms(r);
  }
  function gs(t, e, s, r, i, n) {
    let h = t;
    do {
      let t = h.next.next;
      for (; t !== h.prev; ) {
        if (h.i !== t.i && As(h, t)) {
          let a = Ts(h, t);
          return (
            (h = ms(h, h.next)),
            (a = ms(a, a.next)),
            ys(h, e, s, r, i, n),
            void ys(a, e, s, r, i, n)
          );
        }
        t = t.next;
      }
      h = h.next;
    } while (h !== t);
  }
  function bs(t, e) {
    return t.x - e.x;
  }
  function Ss(t, e) {
    if (
      ((e = (function (t, e) {
        let s = e;
        const r = t.x,
          i = t.y;
        let n,
          h = -1 / 0;
        do {
          if (i <= s.y && i >= s.next.y && s.next.y !== s.y) {
            const t = s.x + ((i - s.y) * (s.next.x - s.x)) / (s.next.y - s.y);
            if (t <= r && t > h) {
              if (((h = t), t === r)) {
                if (i === s.y) return s;
                if (i === s.next.y) return s.next;
              }
              n = s.x < s.next.x ? s : s.next;
            }
          }
          s = s.next;
        } while (s !== e);
        if (!n) return null;
        if (r === h) return n;
        const a = n,
          o = n.x,
          u = n.y;
        let l,
          c = 1 / 0;
        s = n;
        do {
          r >= s.x &&
            s.x >= o &&
            r !== s.x &&
            vs(i < u ? r : h, i, o, u, i < u ? h : r, i, s.x, s.y) &&
            ((l = Math.abs(i - s.y) / (r - s.x)),
            Ps(s, t) &&
              (l < c ||
                (l === c && (s.x > n.x || (s.x === n.x && Ms(n, s))))) &&
              ((n = s), (c = l))),
            (s = s.next);
        } while (s !== a);
        return n;
      })(t, e)),
      e)
    ) {
      const s = Ts(e, t);
      ms(e, e.next), ms(s, s.next);
    }
  }
  function Ms(t, e) {
    return zs(t.prev, t, e.prev) < 0 && zs(e.next, t, t.next) < 0;
  }
  function ws(t, e, s, r, i) {
    return (
      (t =
        1431655765 &
        ((t =
          858993459 &
          ((t =
            252645135 &
            ((t = 16711935 & ((t = 32767 * (t - s) * i) | (t << 8))) |
              (t << 4))) |
            (t << 2))) |
          (t << 1))) |
      ((e =
        1431655765 &
        ((e =
          858993459 &
          ((e =
            252645135 &
            ((e = 16711935 & ((e = 32767 * (e - r) * i) | (e << 8))) |
              (e << 4))) |
            (e << 2))) |
          (e << 1))) <<
        1)
    );
  }
  function _s(t) {
    let e = t,
      s = t;
    do {
      (e.x < s.x || (e.x === s.x && e.y < s.y)) && (s = e), (e = e.next);
    } while (e !== t);
    return s;
  }
  function vs(t, e, s, r, i, n, h, a) {
    return (
      (i - h) * (e - a) - (t - h) * (n - a) >= 0 &&
      (t - h) * (r - a) - (s - h) * (e - a) >= 0 &&
      (s - h) * (n - a) - (i - h) * (r - a) >= 0
    );
  }
  function As(t, e) {
    return (
      t.next.i !== e.i &&
      t.prev.i !== e.i &&
      !(function (t, e) {
        let s = t;
        do {
          if (
            s.i !== t.i &&
            s.next.i !== t.i &&
            s.i !== e.i &&
            s.next.i !== e.i &&
            Bs(s, s.next, t, e)
          )
            return !0;
          s = s.next;
        } while (s !== t);
        return !1;
      })(t, e) &&
      ((Ps(t, e) &&
        Ps(e, t) &&
        (function (t, e) {
          let s = t,
            r = !1;
          const i = (t.x + e.x) / 2,
            n = (t.y + e.y) / 2;
          do {
            s.y > n != s.next.y > n &&
              s.next.y !== s.y &&
              i < ((s.next.x - s.x) * (n - s.y)) / (s.next.y - s.y) + s.x &&
              (r = !r),
              (s = s.next);
          } while (s !== t);
          return r;
        })(t, e) &&
        (zs(t.prev, t, e.prev) || zs(t, e.prev, e))) ||
        (Cs(t, e) && zs(t.prev, t, t.next) > 0 && zs(e.prev, e, e.next) > 0))
    );
  }
  function zs(t, e, s) {
    return (e.y - t.y) * (s.x - e.x) - (e.x - t.x) * (s.y - e.y);
  }
  function Cs(t, e) {
    return t.x === e.x && t.y === e.y;
  }
  function Bs(t, e, s, r) {
    const i = Es(zs(t, e, s)),
      n = Es(zs(t, e, r)),
      h = Es(zs(s, r, t)),
      a = Es(zs(s, r, e));
    return (
      (i !== n && h !== a) ||
      !(0 !== i || !Rs(t, s, e)) ||
      !(0 !== n || !Rs(t, r, e)) ||
      !(0 !== h || !Rs(s, t, r)) ||
      !(0 !== a || !Rs(s, e, r))
    );
  }
  function Rs(t, e, s) {
    return (
      e.x <= Math.max(t.x, s.x) &&
      e.x >= Math.min(t.x, s.x) &&
      e.y <= Math.max(t.y, s.y) &&
      e.y >= Math.min(t.y, s.y)
    );
  }
  function Es(t) {
    return t > 0 ? 1 : t < 0 ? -1 : 0;
  }
  function Ps(t, e) {
    return zs(t.prev, t, t.next) < 0
      ? zs(t, e, t.next) >= 0 && zs(t, t.prev, e) >= 0
      : zs(t, e, t.prev) < 0 || zs(t, t.next, e) < 0;
  }
  function Ts(t, e) {
    const s = new Fs(t.i, t.x, t.y),
      r = new Fs(e.i, e.x, e.y),
      i = t.next,
      n = e.prev;
    return (
      (t.next = e),
      (e.prev = t),
      (s.next = i),
      (i.prev = s),
      (r.next = s),
      (s.prev = r),
      (n.next = r),
      (r.prev = n),
      r
    );
  }
  function Os(t, e, s, r) {
    const i = new Fs(t, e, s);
    return (
      r
        ? ((i.next = r.next), (i.prev = r), (r.next.prev = i), (r.next = i))
        : ((i.prev = i), (i.next = i)),
      i
    );
  }
  function Ns(t) {
    (t.next.prev = t.prev),
      (t.prev.next = t.next),
      t.prevZ && (t.prevZ.nextZ = t.nextZ),
      t.nextZ && (t.nextZ.prevZ = t.prevZ);
  }
  function Fs(t, e, s) {
    (this.i = t),
      (this.x = e),
      (this.y = s),
      (this.prev = null),
      (this.next = null),
      (this.z = null),
      (this.prevZ = null),
      (this.nextZ = null),
      (this.steiner = !1);
  }
  class ks {
    static area(t) {
      const e = t.length;
      let s = 0;
      for (let r = e - 1, i = 0; i < e; r = i++)
        s += t[r].x * t[i].y - t[i].x * t[r].y;
      return 0.5 * s;
    }
    static isClockWise(t) {
      return ks.area(t) < 0;
    }
    static triangulateShape(t, e) {
      const s = [],
        r = [],
        i = [];
      Gs(t), Ls(s, t);
      let n = t.length;
      e.forEach(Gs);
      for (let t = 0; t < e.length; t++)
        r.push(n), (n += e[t].length), Ls(s, e[t]);
      const h = cs(s, r);
      for (let t = 0; t < h.length; t += 3) i.push(h.slice(t, t + 3));
      return i;
    }
  }
  function Gs(t) {
    const e = t.length;
    e > 2 && t[e - 1].equals(t[0]) && t.pop();
  }
  function Ls(t, e) {
    for (let s = 0; s < e.length; s++) t.push(e[s].x), t.push(e[s].y);
  }
  class Is extends ge {
    constructor(
      t = new ls([
        new Mt(0.5, 0.5),
        new Mt(-0.5, 0.5),
        new Mt(-0.5, -0.5),
        new Mt(0.5, -0.5),
      ]),
      e = {}
    ) {
      super(),
        (this.type = "ExtrudeGeometry"),
        (this.parameters = { shapes: t, options: e }),
        (t = Array.isArray(t) ? t : [t]);
      const s = this,
        r = [],
        i = [];
      for (let e = 0, s = t.length; e < s; e++) {
        n(t[e]);
      }
      function n(t) {
        const n = [],
          h = void 0 !== e.curveSegments ? e.curveSegments : 12,
          a = void 0 !== e.steps ? e.steps : 1,
          o = void 0 !== e.depth ? e.depth : 1;
        let u = void 0 === e.bevelEnabled || e.bevelEnabled,
          l = void 0 !== e.bevelThickness ? e.bevelThickness : 0.2,
          c = void 0 !== e.bevelSize ? e.bevelSize : l - 0.1,
          d = void 0 !== e.bevelOffset ? e.bevelOffset : 0,
          m = void 0 !== e.bevelSegments ? e.bevelSegments : 3;
        const y = e.extrudePath,
          p = void 0 !== e.UVGenerator ? e.UVGenerator : Ds;
        let x,
          f,
          g,
          b,
          S,
          M = !1;
        y &&
          ((x = y.getSpacedPoints(a)),
          (M = !0),
          (u = !1),
          (f = y.computeFrenetFrames(a, !1)),
          (g = new J()),
          (b = new J()),
          (S = new J())),
          u || ((m = 0), (l = 0), (c = 0), (d = 0));
        const w = t.extractPoints(h);
        let _ = w.shape;
        const v = w.holes;
        if (!ks.isClockWise(_)) {
          _ = _.reverse();
          for (let t = 0, e = v.length; t < e; t++) {
            const e = v[t];
            ks.isClockWise(e) && (v[t] = e.reverse());
          }
        }
        const A = ks.triangulateShape(_, v),
          z = _;
        for (let t = 0, e = v.length; t < e; t++) {
          const e = v[t];
          _ = _.concat(e);
        }
        function C(t, e, s) {
          return (
            e || console.error("THREE.ExtrudeGeometry: vec does not exist"),
            e.clone().multiplyScalar(s).add(t)
          );
        }
        const B = _.length,
          R = A.length;
        function E(t, e, s) {
          let r, i, n;
          const h = t.x - e.x,
            a = t.y - e.y,
            o = s.x - t.x,
            u = s.y - t.y,
            l = h * h + a * a,
            c = h * u - a * o;
          if (Math.abs(c) > Number.EPSILON) {
            const c = Math.sqrt(l),
              d = Math.sqrt(o * o + u * u),
              m = e.x - a / c,
              y = e.y + h / c,
              p =
                ((s.x - u / d - m) * u - (s.y + o / d - y) * o) /
                (h * u - a * o);
            (r = m + h * p - t.x), (i = y + a * p - t.y);
            const x = r * r + i * i;
            if (x <= 2) return new Mt(r, i);
            n = Math.sqrt(x / 2);
          } else {
            let t = !1;
            h > Number.EPSILON
              ? o > Number.EPSILON && (t = !0)
              : h < -Number.EPSILON
              ? o < -Number.EPSILON && (t = !0)
              : Math.sign(a) === Math.sign(u) && (t = !0),
              t
                ? ((r = -a), (i = h), (n = Math.sqrt(l)))
                : ((r = h), (i = a), (n = Math.sqrt(l / 2)));
          }
          return new Mt(r / n, i / n);
        }
        const P = [];
        for (
          let t = 0, e = z.length, s = e - 1, r = t + 1;
          t < e;
          t++, s++, r++
        )
          s === e && (s = 0), r === e && (r = 0), (P[t] = E(z[t], z[s], z[r]));
        const T = [];
        let O,
          N = P.concat();
        for (let t = 0, e = v.length; t < e; t++) {
          const e = v[t];
          O = [];
          for (
            let t = 0, s = e.length, r = s - 1, i = t + 1;
            t < s;
            t++, r++, i++
          )
            r === s && (r = 0),
              i === s && (i = 0),
              (O[t] = E(e[t], e[r], e[i]));
          T.push(O), (N = N.concat(O));
        }
        for (let t = 0; t < m; t++) {
          const e = t / m,
            s = l * Math.cos((e * Math.PI) / 2),
            r = c * Math.sin((e * Math.PI) / 2) + d;
          for (let t = 0, e = z.length; t < e; t++) {
            const e = C(z[t], P[t], r);
            G(e.x, e.y, -s);
          }
          for (let t = 0, e = v.length; t < e; t++) {
            const e = v[t];
            O = T[t];
            for (let t = 0, i = e.length; t < i; t++) {
              const i = C(e[t], O[t], r);
              G(i.x, i.y, -s);
            }
          }
        }
        const F = c + d;
        for (let t = 0; t < B; t++) {
          const e = u ? C(_[t], N[t], F) : _[t];
          M
            ? (b.copy(f.normals[0]).multiplyScalar(e.x),
              g.copy(f.binormals[0]).multiplyScalar(e.y),
              S.copy(x[0]).add(b).add(g),
              G(S.x, S.y, S.z))
            : G(e.x, e.y, 0);
        }
        for (let t = 1; t <= a; t++)
          for (let e = 0; e < B; e++) {
            const s = u ? C(_[e], N[e], F) : _[e];
            M
              ? (b.copy(f.normals[t]).multiplyScalar(s.x),
                g.copy(f.binormals[t]).multiplyScalar(s.y),
                S.copy(x[t]).add(b).add(g),
                G(S.x, S.y, S.z))
              : G(s.x, s.y, (o / a) * t);
          }
        for (let t = m - 1; t >= 0; t--) {
          const e = t / m,
            s = l * Math.cos((e * Math.PI) / 2),
            r = c * Math.sin((e * Math.PI) / 2) + d;
          for (let t = 0, e = z.length; t < e; t++) {
            const e = C(z[t], P[t], r);
            G(e.x, e.y, o + s);
          }
          for (let t = 0, e = v.length; t < e; t++) {
            const e = v[t];
            O = T[t];
            for (let t = 0, i = e.length; t < i; t++) {
              const i = C(e[t], O[t], r);
              M ? G(i.x, i.y + x[a - 1].y, x[a - 1].x + s) : G(i.x, i.y, o + s);
            }
          }
        }
        function k(t, e) {
          let s = t.length;
          for (; --s >= 0; ) {
            const r = s;
            let i = s - 1;
            i < 0 && (i = t.length - 1);
            for (let t = 0, s = a + 2 * m; t < s; t++) {
              const s = B * t,
                n = B * (t + 1);
              I(e + r + s, e + i + s, e + i + n, e + r + n);
            }
          }
        }
        function G(t, e, s) {
          n.push(t), n.push(e), n.push(s);
        }
        function L(t, e, i) {
          D(t), D(e), D(i);
          const n = r.length / 3,
            h = p.generateTopUV(s, r, n - 3, n - 2, n - 1);
          V(h[0]), V(h[1]), V(h[2]);
        }
        function I(t, e, i, n) {
          D(t), D(e), D(n), D(e), D(i), D(n);
          const h = r.length / 3,
            a = p.generateSideWallUV(s, r, h - 6, h - 3, h - 2, h - 1);
          V(a[0]), V(a[1]), V(a[3]), V(a[1]), V(a[2]), V(a[3]);
        }
        function D(t) {
          r.push(n[3 * t + 0]), r.push(n[3 * t + 1]), r.push(n[3 * t + 2]);
        }
        function V(t) {
          i.push(t.x), i.push(t.y);
        }
        !(function () {
          const t = r.length / 3;
          if (u) {
            let t = 0,
              e = B * t;
            for (let t = 0; t < R; t++) {
              const s = A[t];
              L(s[2] + e, s[1] + e, s[0] + e);
            }
            (t = a + 2 * m), (e = B * t);
            for (let t = 0; t < R; t++) {
              const s = A[t];
              L(s[0] + e, s[1] + e, s[2] + e);
            }
          } else {
            for (let t = 0; t < R; t++) {
              const e = A[t];
              L(e[2], e[1], e[0]);
            }
            for (let t = 0; t < R; t++) {
              const e = A[t];
              L(e[0] + B * a, e[1] + B * a, e[2] + B * a);
            }
          }
          s.addGroup(t, r.length / 3 - t, 0);
        })(),
          (function () {
            const t = r.length / 3;
            let e = 0;
            k(z, e), (e += z.length);
            for (let t = 0, s = v.length; t < s; t++) {
              const s = v[t];
              k(s, e), (e += s.length);
            }
            s.addGroup(t, r.length / 3 - t, 1);
          })();
      }
      this.setAttribute("position", new ne(r, 3)),
        this.setAttribute("uv", new ne(i, 2)),
        this.computeVertexNormals();
    }
    toJSON() {
      const t = super.toJSON();
      return (function (t, e, s) {
        if (((s.shapes = []), Array.isArray(t)))
          for (let e = 0, r = t.length; e < r; e++) {
            const r = t[e];
            s.shapes.push(r.uuid);
          }
        else s.shapes.push(t.uuid);
        (s.options = Object.assign({}, e)),
          void 0 !== e.extrudePath &&
            (s.options.extrudePath = e.extrudePath.toJSON());
        return s;
      })(this.parameters.shapes, this.parameters.options, t);
    }
    static fromJSON(t, e) {
      const s = [];
      for (let r = 0, i = t.shapes.length; r < i; r++) {
        const i = e[t.shapes[r]];
        s.push(i);
      }
      const r = t.options.extrudePath;
      return (
        void 0 !== r && (t.options.extrudePath = new Ie[r.type]().fromJSON(r)),
        new Is(s, t.options)
      );
    }
  }
  const Ds = {
    generateTopUV: function (t, e, s, r, i) {
      const n = e[3 * s],
        h = e[3 * s + 1],
        a = e[3 * r],
        o = e[3 * r + 1],
        u = e[3 * i],
        l = e[3 * i + 1];
      return [new Mt(n, h), new Mt(a, o), new Mt(u, l)];
    },
    generateSideWallUV: function (t, e, s, r, i, n) {
      const h = e[3 * s],
        a = e[3 * s + 1],
        o = e[3 * s + 2],
        u = e[3 * r],
        l = e[3 * r + 1],
        c = e[3 * r + 2],
        d = e[3 * i],
        m = e[3 * i + 1],
        y = e[3 * i + 2],
        p = e[3 * n],
        x = e[3 * n + 1],
        f = e[3 * n + 2];
      return Math.abs(a - l) < Math.abs(h - u)
        ? [
            new Mt(h, 1 - o),
            new Mt(u, 1 - c),
            new Mt(d, 1 - y),
            new Mt(p, 1 - f),
          ]
        : [
            new Mt(a, 1 - o),
            new Mt(l, 1 - c),
            new Mt(m, 1 - y),
            new Mt(x, 1 - f),
          ];
    },
  };
  class Vs extends We {
    constructor(t = 1, e = 0) {
      const s = (1 + Math.sqrt(5)) / 2;
      super(
        [
          -1,
          s,
          0,
          1,
          s,
          0,
          -1,
          -s,
          0,
          1,
          -s,
          0,
          0,
          -1,
          s,
          0,
          1,
          s,
          0,
          -1,
          -s,
          0,
          1,
          -s,
          s,
          0,
          -1,
          s,
          0,
          1,
          -s,
          0,
          -1,
          -s,
          0,
          1,
        ],
        [
          0, 11, 5, 0, 5, 1, 0, 1, 7, 0, 7, 10, 0, 10, 11, 1, 5, 9, 5, 11, 4,
          11, 10, 2, 10, 7, 6, 7, 1, 8, 3, 9, 4, 3, 4, 2, 3, 2, 6, 3, 6, 8, 3,
          8, 9, 4, 9, 5, 2, 4, 11, 6, 2, 10, 8, 6, 7, 9, 8, 1,
        ],
        t,
        e
      ),
        (this.type = "IcosahedronGeometry"),
        (this.parameters = { radius: t, detail: e });
    }
    static fromJSON(t) {
      return new Vs(t.radius, t.detail);
    }
  }
  class qs extends We {
    constructor(t = 1, e = 0) {
      super(
        [1, 0, 0, -1, 0, 0, 0, 1, 0, 0, -1, 0, 0, 0, 1, 0, 0, -1],
        [
          0, 2, 4, 0, 4, 3, 0, 3, 5, 0, 5, 2, 1, 2, 5, 1, 5, 3, 1, 3, 4, 1, 4,
          2,
        ],
        t,
        e
      ),
        (this.type = "OctahedronGeometry"),
        (this.parameters = { radius: t, detail: e });
    }
    static fromJSON(t) {
      return new qs(t.radius, t.detail);
    }
  }
  class Us extends ge {
    constructor(t = 1, e = 1, s = 1, r = 1) {
      super(),
        (this.type = "PlaneGeometry"),
        (this.parameters = {
          width: t,
          height: e,
          widthSegments: s,
          heightSegments: r,
        });
      const i = t / 2,
        n = e / 2,
        h = Math.floor(s),
        a = Math.floor(r),
        o = h + 1,
        u = a + 1,
        l = t / h,
        c = e / a,
        d = [],
        m = [],
        y = [],
        p = [];
      for (let t = 0; t < u; t++) {
        const e = t * c - n;
        for (let s = 0; s < o; s++) {
          const r = s * l - i;
          m.push(r, -e, 0), y.push(0, 0, 1), p.push(s / h), p.push(1 - t / a);
        }
      }
      for (let t = 0; t < a; t++)
        for (let e = 0; e < h; e++) {
          const s = e + o * t,
            r = e + o * (t + 1),
            i = e + 1 + o * (t + 1),
            n = e + 1 + o * t;
          d.push(s, r, n), d.push(r, i, n);
        }
      this.setIndex(d),
        this.setAttribute("position", new ne(m, 3)),
        this.setAttribute("normal", new ne(y, 3)),
        this.setAttribute("uv", new ne(p, 2));
    }
    static fromJSON(t) {
      return new Us(t.width, t.height, t.widthSegments, t.heightSegments);
    }
  }
  class Js extends ge {
    constructor(t = 0.5, e = 1, s = 8, r = 1, i = 0, n = 2 * Math.PI) {
      super(),
        (this.type = "RingGeometry"),
        (this.parameters = {
          innerRadius: t,
          outerRadius: e,
          thetaSegments: s,
          phiSegments: r,
          thetaStart: i,
          thetaLength: n,
        }),
        (s = Math.max(3, s));
      const h = [],
        a = [],
        o = [],
        u = [];
      let l = t;
      const c = (e - t) / (r = Math.max(1, r)),
        d = new J(),
        m = new Mt();
      for (let t = 0; t <= r; t++) {
        for (let t = 0; t <= s; t++) {
          const r = i + (t / s) * n;
          (d.x = l * Math.cos(r)),
            (d.y = l * Math.sin(r)),
            a.push(d.x, d.y, d.z),
            o.push(0, 0, 1),
            (m.x = (d.x / e + 1) / 2),
            (m.y = (d.y / e + 1) / 2),
            u.push(m.x, m.y);
        }
        l += c;
      }
      for (let t = 0; t < r; t++) {
        const e = t * (s + 1);
        for (let t = 0; t < s; t++) {
          const r = t + e,
            i = r,
            n = r + s + 1,
            a = r + s + 2,
            o = r + 1;
          h.push(i, n, o), h.push(n, a, o);
        }
      }
      this.setIndex(h),
        this.setAttribute("position", new ne(a, 3)),
        this.setAttribute("normal", new ne(o, 3)),
        this.setAttribute("uv", new ne(u, 2));
    }
    static fromJSON(t) {
      return new Js(
        t.innerRadius,
        t.outerRadius,
        t.thetaSegments,
        t.phiSegments,
        t.thetaStart,
        t.thetaLength
      );
    }
  }
  class Hs extends ge {
    constructor(
      t = new ls([new Mt(0, 0.5), new Mt(-0.5, -0.5), new Mt(0.5, -0.5)]),
      e = 12
    ) {
      super(),
        (this.type = "ShapeGeometry"),
        (this.parameters = { shapes: t, curveSegments: e });
      const s = [],
        r = [],
        i = [],
        n = [];
      let h = 0,
        a = 0;
      if (!1 === Array.isArray(t)) o(t);
      else
        for (let e = 0; e < t.length; e++)
          o(t[e]), this.addGroup(h, a, e), (h += a), (a = 0);
      function o(t) {
        const h = r.length / 3,
          o = t.extractPoints(e);
        let u = o.shape;
        const l = o.holes;
        !1 === ks.isClockWise(u) && (u = u.reverse());
        for (let t = 0, e = l.length; t < e; t++) {
          const e = l[t];
          !0 === ks.isClockWise(e) && (l[t] = e.reverse());
        }
        const c = ks.triangulateShape(u, l);
        for (let t = 0, e = l.length; t < e; t++) {
          const e = l[t];
          u = u.concat(e);
        }
        for (let t = 0, e = u.length; t < e; t++) {
          const e = u[t];
          r.push(e.x, e.y, 0), i.push(0, 0, 1), n.push(e.x, e.y);
        }
        for (let t = 0, e = c.length; t < e; t++) {
          const e = c[t],
            r = e[0] + h,
            i = e[1] + h,
            n = e[2] + h;
          s.push(r, i, n), (a += 3);
        }
      }
      this.setIndex(s),
        this.setAttribute("position", new ne(r, 3)),
        this.setAttribute("normal", new ne(i, 3)),
        this.setAttribute("uv", new ne(n, 2));
    }
    toJSON() {
      const t = super.toJSON();
      return (function (t, e) {
        if (((e.shapes = []), Array.isArray(t)))
          for (let s = 0, r = t.length; s < r; s++) {
            const r = t[s];
            e.shapes.push(r.uuid);
          }
        else e.shapes.push(t.uuid);
        return e;
      })(this.parameters.shapes, t);
    }
    static fromJSON(t, e) {
      const s = [];
      for (let r = 0, i = t.shapes.length; r < i; r++) {
        const i = e[t.shapes[r]];
        s.push(i);
      }
      return new Hs(s, t.curveSegments);
    }
  }
  class Xs extends ge {
    constructor(
      t = 1,
      e = 32,
      s = 16,
      r = 0,
      i = 2 * Math.PI,
      n = 0,
      h = Math.PI
    ) {
      super(),
        (this.type = "SphereGeometry"),
        (this.parameters = {
          radius: t,
          widthSegments: e,
          heightSegments: s,
          phiStart: r,
          phiLength: i,
          thetaStart: n,
          thetaLength: h,
        }),
        (e = Math.max(3, Math.floor(e))),
        (s = Math.max(2, Math.floor(s)));
      const a = Math.min(n + h, Math.PI);
      let o = 0;
      const u = [],
        l = new J(),
        c = new J(),
        d = [],
        m = [],
        y = [],
        p = [];
      for (let d = 0; d <= s; d++) {
        const x = [],
          f = d / s;
        let g = 0;
        0 == d && 0 == n
          ? (g = 0.5 / e)
          : d == s && a == Math.PI && (g = -0.5 / e);
        for (let s = 0; s <= e; s++) {
          const a = s / e;
          (l.x = -t * Math.cos(r + a * i) * Math.sin(n + f * h)),
            (l.y = t * Math.cos(n + f * h)),
            (l.z = t * Math.sin(r + a * i) * Math.sin(n + f * h)),
            m.push(l.x, l.y, l.z),
            c.copy(l).normalize(),
            y.push(c.x, c.y, c.z),
            p.push(a + g, 1 - f),
            x.push(o++);
        }
        u.push(x);
      }
      for (let t = 0; t < s; t++)
        for (let r = 0; r < e; r++) {
          const e = u[t][r + 1],
            i = u[t][r],
            h = u[t + 1][r],
            o = u[t + 1][r + 1];
          (0 !== t || n > 0) && d.push(e, i, o),
            (t !== s - 1 || a < Math.PI) && d.push(i, h, o);
        }
      this.setIndex(d),
        this.setAttribute("position", new ne(m, 3)),
        this.setAttribute("normal", new ne(y, 3)),
        this.setAttribute("uv", new ne(p, 2));
    }
    static fromJSON(t) {
      return new Xs(
        t.radius,
        t.widthSegments,
        t.heightSegments,
        t.phiStart,
        t.phiLength,
        t.thetaStart,
        t.thetaLength
      );
    }
  }
  class Ws extends We {
    constructor(t = 1, e = 0) {
      super(
        [1, 1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1],
        [2, 1, 0, 0, 3, 2, 1, 3, 0, 2, 3, 1],
        t,
        e
      ),
        (this.type = "TetrahedronGeometry"),
        (this.parameters = { radius: t, detail: e });
    }
    static fromJSON(t) {
      return new Ws(t.radius, t.detail);
    }
  }
  class Zs extends ge {
    constructor(t = 1, e = 0.4, s = 8, r = 6, i = 2 * Math.PI) {
      super(),
        (this.type = "TorusGeometry"),
        (this.parameters = {
          radius: t,
          tube: e,
          radialSegments: s,
          tubularSegments: r,
          arc: i,
        }),
        (s = Math.floor(s)),
        (r = Math.floor(r));
      const n = [],
        h = [],
        a = [],
        o = [],
        u = new J(),
        l = new J(),
        c = new J();
      for (let n = 0; n <= s; n++)
        for (let d = 0; d <= r; d++) {
          const m = (d / r) * i,
            y = (n / s) * Math.PI * 2;
          (l.x = (t + e * Math.cos(y)) * Math.cos(m)),
            (l.y = (t + e * Math.cos(y)) * Math.sin(m)),
            (l.z = e * Math.sin(y)),
            h.push(l.x, l.y, l.z),
            (u.x = t * Math.cos(m)),
            (u.y = t * Math.sin(m)),
            c.subVectors(l, u).normalize(),
            a.push(c.x, c.y, c.z),
            o.push(d / r),
            o.push(n / s);
        }
      for (let t = 1; t <= s; t++)
        for (let e = 1; e <= r; e++) {
          const s = (r + 1) * t + e - 1,
            i = (r + 1) * (t - 1) + e - 1,
            h = (r + 1) * (t - 1) + e,
            a = (r + 1) * t + e;
          n.push(s, i, a), n.push(i, h, a);
        }
      this.setIndex(n),
        this.setAttribute("position", new ne(h, 3)),
        this.setAttribute("normal", new ne(a, 3)),
        this.setAttribute("uv", new ne(o, 2));
    }
    static fromJSON(t) {
      return new Zs(
        t.radius,
        t.tube,
        t.radialSegments,
        t.tubularSegments,
        t.arc
      );
    }
  }
  class Ys extends ge {
    constructor(t = 1, e = 0.4, s = 64, r = 8, i = 2, n = 3) {
      super(),
        (this.type = "TorusKnotGeometry"),
        (this.parameters = {
          radius: t,
          tube: e,
          tubularSegments: s,
          radialSegments: r,
          p: i,
          q: n,
        }),
        (s = Math.floor(s)),
        (r = Math.floor(r));
      const h = [],
        a = [],
        o = [],
        u = [],
        l = new J(),
        c = new J(),
        d = new J(),
        m = new J(),
        y = new J(),
        p = new J(),
        x = new J();
      for (let h = 0; h <= s; ++h) {
        const g = (h / s) * i * Math.PI * 2;
        f(g, i, n, t, d),
          f(g + 0.01, i, n, t, m),
          p.subVectors(m, d),
          x.addVectors(m, d),
          y.crossVectors(p, x),
          x.crossVectors(y, p),
          y.normalize(),
          x.normalize();
        for (let t = 0; t <= r; ++t) {
          const i = (t / r) * Math.PI * 2,
            n = -e * Math.cos(i),
            m = e * Math.sin(i);
          (l.x = d.x + (n * x.x + m * y.x)),
            (l.y = d.y + (n * x.y + m * y.y)),
            (l.z = d.z + (n * x.z + m * y.z)),
            a.push(l.x, l.y, l.z),
            c.subVectors(l, d).normalize(),
            o.push(c.x, c.y, c.z),
            u.push(h / s),
            u.push(t / r);
        }
      }
      for (let t = 1; t <= s; t++)
        for (let e = 1; e <= r; e++) {
          const s = (r + 1) * (t - 1) + (e - 1),
            i = (r + 1) * t + (e - 1),
            n = (r + 1) * t + e,
            a = (r + 1) * (t - 1) + e;
          h.push(s, i, a), h.push(i, n, a);
        }
      function f(t, e, s, r, i) {
        const n = Math.cos(t),
          h = Math.sin(t),
          a = (s / e) * t,
          o = Math.cos(a);
        (i.x = r * (2 + o) * 0.5 * n),
          (i.y = r * (2 + o) * h * 0.5),
          (i.z = r * Math.sin(a) * 0.5);
      }
      this.setIndex(h),
        this.setAttribute("position", new ne(a, 3)),
        this.setAttribute("normal", new ne(o, 3)),
        this.setAttribute("uv", new ne(u, 2));
    }
    static fromJSON(t) {
      return new Ys(
        t.radius,
        t.tube,
        t.tubularSegments,
        t.radialSegments,
        t.p,
        t.q
      );
    }
  }
  class js extends ge {
    constructor(
      t = new Ge(new J(-1, -1, 0), new J(-1, 1, 0), new J(1, 1, 0)),
      e = 64,
      s = 1,
      r = 8,
      i = !1
    ) {
      super(),
        (this.type = "TubeGeometry"),
        (this.parameters = {
          path: t,
          tubularSegments: e,
          radius: s,
          radialSegments: r,
          closed: i,
        });
      const n = t.computeFrenetFrames(e, i);
      (this.tangents = n.tangents),
        (this.normals = n.normals),
        (this.binormals = n.binormals);
      const h = new J(),
        a = new J(),
        o = new Mt();
      let u = new J();
      const l = [],
        c = [],
        d = [],
        m = [];
      function y(i) {
        u = t.getPointAt(i / e, u);
        const o = n.normals[i],
          d = n.binormals[i];
        for (let t = 0; t <= r; t++) {
          const e = (t / r) * Math.PI * 2,
            i = Math.sin(e),
            n = -Math.cos(e);
          (a.x = n * o.x + i * d.x),
            (a.y = n * o.y + i * d.y),
            (a.z = n * o.z + i * d.z),
            a.normalize(),
            c.push(a.x, a.y, a.z),
            (h.x = u.x + s * a.x),
            (h.y = u.y + s * a.y),
            (h.z = u.z + s * a.z),
            l.push(h.x, h.y, h.z);
        }
      }
      !(function () {
        for (let t = 0; t < e; t++) y(t);
        y(!1 === i ? e : 0),
          (function () {
            for (let t = 0; t <= e; t++)
              for (let s = 0; s <= r; s++)
                (o.x = t / e), (o.y = s / r), d.push(o.x, o.y);
          })(),
          (function () {
            for (let t = 1; t <= e; t++)
              for (let e = 1; e <= r; e++) {
                const s = (r + 1) * (t - 1) + (e - 1),
                  i = (r + 1) * t + (e - 1),
                  n = (r + 1) * t + e,
                  h = (r + 1) * (t - 1) + e;
                m.push(s, i, h), m.push(i, n, h);
              }
          })();
      })(),
        this.setIndex(m),
        this.setAttribute("position", new ne(l, 3)),
        this.setAttribute("normal", new ne(c, 3)),
        this.setAttribute("uv", new ne(d, 2));
    }
    toJSON() {
      const t = super.toJSON();
      return (t.path = this.parameters.path.toJSON()), t;
    }
    static fromJSON(t) {
      return new js(
        new Ie[t.path.type]().fromJSON(t.path),
        t.tubularSegments,
        t.radius,
        t.radialSegments,
        t.closed
      );
    }
  }
  function $s(t, e, s) {
    const r = `${t.x},${t.y},${t.z}-${e.x},${e.y},${e.z}`,
      i = `${e.x},${e.y},${e.z}-${t.x},${t.y},${t.z}`;
    return !0 !== s.has(r) && !0 !== s.has(i) && (s.add(r), s.add(i), !0);
  }
  const Ks = new J();
  class Qs {
    constructor(t, e, s, r = !1) {
      (this.isInterleavedBufferAttribute = !0),
        (this.name = ""),
        (this.data = t),
        (this.itemSize = e),
        (this.offset = s),
        (this.normalized = !0 === r);
    }
    get count() {
      return this.data.count;
    }
    get array() {
      return this.data.array;
    }
    set needsUpdate(t) {
      this.data.needsUpdate = t;
    }
    applyMatrix4(t) {
      for (let e = 0, s = this.data.count; e < s; e++)
        Ks.fromBufferAttribute(this, e),
          Ks.applyMatrix4(t),
          this.setXYZ(e, Ks.x, Ks.y, Ks.z);
      return this;
    }
    applyNormalMatrix(t) {
      for (let e = 0, s = this.count; e < s; e++)
        Ks.fromBufferAttribute(this, e),
          Ks.applyNormalMatrix(t),
          this.setXYZ(e, Ks.x, Ks.y, Ks.z);
      return this;
    }
    transformDirection(t) {
      for (let e = 0, s = this.count; e < s; e++)
        Ks.fromBufferAttribute(this, e),
          Ks.transformDirection(t),
          this.setXYZ(e, Ks.x, Ks.y, Ks.z);
      return this;
    }
    setX(t, e) {
      return (
        this.normalized && (e = q(e, this.array)),
        (this.data.array[t * this.data.stride + this.offset] = e),
        this
      );
    }
    setY(t, e) {
      return (
        this.normalized && (e = q(e, this.array)),
        (this.data.array[t * this.data.stride + this.offset + 1] = e),
        this
      );
    }
    setZ(t, e) {
      return (
        this.normalized && (e = q(e, this.array)),
        (this.data.array[t * this.data.stride + this.offset + 2] = e),
        this
      );
    }
    setW(t, e) {
      return (
        this.normalized && (e = q(e, this.array)),
        (this.data.array[t * this.data.stride + this.offset + 3] = e),
        this
      );
    }
    getX(t) {
      let e = this.data.array[t * this.data.stride + this.offset];
      return this.normalized && (e = V(e, this.array)), e;
    }
    getY(t) {
      let e = this.data.array[t * this.data.stride + this.offset + 1];
      return this.normalized && (e = V(e, this.array)), e;
    }
    getZ(t) {
      let e = this.data.array[t * this.data.stride + this.offset + 2];
      return this.normalized && (e = V(e, this.array)), e;
    }
    getW(t) {
      let e = this.data.array[t * this.data.stride + this.offset + 3];
      return this.normalized && (e = V(e, this.array)), e;
    }
    setXY(t, e, s) {
      return (
        (t = t * this.data.stride + this.offset),
        this.normalized && ((e = q(e, this.array)), (s = q(s, this.array))),
        (this.data.array[t + 0] = e),
        (this.data.array[t + 1] = s),
        this
      );
    }
    setXYZ(t, e, s, r) {
      return (
        (t = t * this.data.stride + this.offset),
        this.normalized &&
          ((e = q(e, this.array)),
          (s = q(s, this.array)),
          (r = q(r, this.array))),
        (this.data.array[t + 0] = e),
        (this.data.array[t + 1] = s),
        (this.data.array[t + 2] = r),
        this
      );
    }
    setXYZW(t, e, s, r, i) {
      return (
        (t = t * this.data.stride + this.offset),
        this.normalized &&
          ((e = q(e, this.array)),
          (s = q(s, this.array)),
          (r = q(r, this.array)),
          (i = q(i, this.array))),
        (this.data.array[t + 0] = e),
        (this.data.array[t + 1] = s),
        (this.data.array[t + 2] = r),
        (this.data.array[t + 3] = i),
        this
      );
    }
    clone(t) {
      if (void 0 === t) {
        console.log(
          "THREE.InterleavedBufferAttribute.clone(): Cloning an interleaved buffer attribute will deinterleave buffer data."
        );
        const t = [];
        for (let e = 0; e < this.count; e++) {
          const s = e * this.data.stride + this.offset;
          for (let e = 0; e < this.itemSize; e++)
            t.push(this.data.array[s + e]);
        }
        return new se(
          new this.array.constructor(t),
          this.itemSize,
          this.normalized
        );
      }
      return (
        void 0 === t.interleavedBuffers && (t.interleavedBuffers = {}),
        void 0 === t.interleavedBuffers[this.data.uuid] &&
          (t.interleavedBuffers[this.data.uuid] = this.data.clone(t)),
        new Qs(
          t.interleavedBuffers[this.data.uuid],
          this.itemSize,
          this.offset,
          this.normalized
        )
      );
    }
    toJSON(t) {
      if (void 0 === t) {
        console.log(
          "THREE.InterleavedBufferAttribute.toJSON(): Serializing an interleaved buffer attribute will deinterleave buffer data."
        );
        const t = [];
        for (let e = 0; e < this.count; e++) {
          const s = e * this.data.stride + this.offset;
          for (let e = 0; e < this.itemSize; e++)
            t.push(this.data.array[s + e]);
        }
        return {
          itemSize: this.itemSize,
          type: this.array.constructor.name,
          array: t,
          normalized: this.normalized,
        };
      }
      return (
        void 0 === t.interleavedBuffers && (t.interleavedBuffers = {}),
        void 0 === t.interleavedBuffers[this.data.uuid] &&
          (t.interleavedBuffers[this.data.uuid] = this.data.toJSON(t)),
        {
          isInterleavedBufferAttribute: !0,
          itemSize: this.itemSize,
          data: this.data.uuid,
          offset: this.offset,
          normalized: this.normalized,
        }
      );
    }
  }
  class tr {
    constructor(t, e) {
      (this.isInterleavedBuffer = !0),
        (this.array = t),
        (this.stride = e),
        (this.count = void 0 !== t ? t.length / e : 0),
        (this.usage = F),
        (this.updateRange = { offset: 0, count: -1 }),
        (this.version = 0),
        (this.uuid = L());
    }
    onUploadCallback() {}
    set needsUpdate(t) {
      !0 === t && this.version++;
    }
    setUsage(t) {
      return (this.usage = t), this;
    }
    copy(t) {
      return (
        (this.array = new t.array.constructor(t.array)),
        (this.count = t.count),
        (this.stride = t.stride),
        (this.usage = t.usage),
        this
      );
    }
    copyAt(t, e, s) {
      (t *= this.stride), (s *= e.stride);
      for (let r = 0, i = this.stride; r < i; r++)
        this.array[t + r] = e.array[s + r];
      return this;
    }
    set(t, e = 0) {
      return this.array.set(t, e), this;
    }
    clone(t) {
      void 0 === t.arrayBuffers && (t.arrayBuffers = {}),
        void 0 === this.array.buffer._uuid && (this.array.buffer._uuid = L()),
        void 0 === t.arrayBuffers[this.array.buffer._uuid] &&
          (t.arrayBuffers[this.array.buffer._uuid] =
            this.array.slice(0).buffer);
      const e = new this.array.constructor(
          t.arrayBuffers[this.array.buffer._uuid]
        ),
        s = new this.constructor(e, this.stride);
      return s.setUsage(this.usage), s;
    }
    onUpload(t) {
      return (this.onUploadCallback = t), this;
    }
    toJSON(t) {
      return (
        void 0 === t.arrayBuffers && (t.arrayBuffers = {}),
        void 0 === this.array.buffer._uuid && (this.array.buffer._uuid = L()),
        void 0 === t.arrayBuffers[this.array.buffer._uuid] &&
          (t.arrayBuffers[this.array.buffer._uuid] = Array.from(
            new Uint32Array(this.array.buffer)
          )),
        {
          uuid: this.uuid,
          buffer: this.array.buffer._uuid,
          type: this.array.constructor.name,
          stride: this.stride,
        }
      );
    }
  }
  const er = new Mt();
  const sr = new J(),
    rr = new J();
  class ir {
    constructor(t = 0, e = 0, s = 0, r = 1) {
      (ir.prototype.isVector4 = !0),
        (this.x = t),
        (this.y = e),
        (this.z = s),
        (this.w = r);
    }
    get width() {
      return this.z;
    }
    set width(t) {
      this.z = t;
    }
    get height() {
      return this.w;
    }
    set height(t) {
      this.w = t;
    }
    set(t, e, s, r) {
      return (this.x = t), (this.y = e), (this.z = s), (this.w = r), this;
    }
    setScalar(t) {
      return (this.x = t), (this.y = t), (this.z = t), (this.w = t), this;
    }
    setX(t) {
      return (this.x = t), this;
    }
    setY(t) {
      return (this.y = t), this;
    }
    setZ(t) {
      return (this.z = t), this;
    }
    setW(t) {
      return (this.w = t), this;
    }
    setComponent(t, e) {
      switch (t) {
        case 0:
          this.x = e;
          break;
        case 1:
          this.y = e;
          break;
        case 2:
          this.z = e;
          break;
        case 3:
          this.w = e;
          break;
        default:
          throw new Error("index is out of range: " + t);
      }
      return this;
    }
    getComponent(t) {
      switch (t) {
        case 0:
          return this.x;
        case 1:
          return this.y;
        case 2:
          return this.z;
        case 3:
          return this.w;
        default:
          throw new Error("index is out of range: " + t);
      }
    }
    clone() {
      return new this.constructor(this.x, this.y, this.z, this.w);
    }
    copy(t) {
      return (
        (this.x = t.x),
        (this.y = t.y),
        (this.z = t.z),
        (this.w = void 0 !== t.w ? t.w : 1),
        this
      );
    }
    add(t) {
      return (
        (this.x += t.x), (this.y += t.y), (this.z += t.z), (this.w += t.w), this
      );
    }
    addScalar(t) {
      return (this.x += t), (this.y += t), (this.z += t), (this.w += t), this;
    }
    addVectors(t, e) {
      return (
        (this.x = t.x + e.x),
        (this.y = t.y + e.y),
        (this.z = t.z + e.z),
        (this.w = t.w + e.w),
        this
      );
    }
    addScaledVector(t, e) {
      return (
        (this.x += t.x * e),
        (this.y += t.y * e),
        (this.z += t.z * e),
        (this.w += t.w * e),
        this
      );
    }
    sub(t) {
      return (
        (this.x -= t.x), (this.y -= t.y), (this.z -= t.z), (this.w -= t.w), this
      );
    }
    subScalar(t) {
      return (this.x -= t), (this.y -= t), (this.z -= t), (this.w -= t), this;
    }
    subVectors(t, e) {
      return (
        (this.x = t.x - e.x),
        (this.y = t.y - e.y),
        (this.z = t.z - e.z),
        (this.w = t.w - e.w),
        this
      );
    }
    multiply(t) {
      return (
        (this.x *= t.x), (this.y *= t.y), (this.z *= t.z), (this.w *= t.w), this
      );
    }
    multiplyScalar(t) {
      return (this.x *= t), (this.y *= t), (this.z *= t), (this.w *= t), this;
    }
    applyMatrix4(t) {
      const e = this.x,
        s = this.y,
        r = this.z,
        i = this.w,
        n = t.elements;
      return (
        (this.x = n[0] * e + n[4] * s + n[8] * r + n[12] * i),
        (this.y = n[1] * e + n[5] * s + n[9] * r + n[13] * i),
        (this.z = n[2] * e + n[6] * s + n[10] * r + n[14] * i),
        (this.w = n[3] * e + n[7] * s + n[11] * r + n[15] * i),
        this
      );
    }
    divideScalar(t) {
      return this.multiplyScalar(1 / t);
    }
    setAxisAngleFromQuaternion(t) {
      this.w = 2 * Math.acos(t.w);
      const e = Math.sqrt(1 - t.w * t.w);
      return (
        e < 1e-4
          ? ((this.x = 1), (this.y = 0), (this.z = 0))
          : ((this.x = t.x / e), (this.y = t.y / e), (this.z = t.z / e)),
        this
      );
    }
    setAxisAngleFromRotationMatrix(t) {
      let e, s, r, i;
      const n = 0.01,
        h = 0.1,
        a = t.elements,
        o = a[0],
        u = a[4],
        l = a[8],
        c = a[1],
        d = a[5],
        m = a[9],
        y = a[2],
        p = a[6],
        x = a[10];
      if (Math.abs(u - c) < n && Math.abs(l - y) < n && Math.abs(m - p) < n) {
        if (
          Math.abs(u + c) < h &&
          Math.abs(l + y) < h &&
          Math.abs(m + p) < h &&
          Math.abs(o + d + x - 3) < h
        )
          return this.set(1, 0, 0, 0), this;
        e = Math.PI;
        const t = (o + 1) / 2,
          a = (d + 1) / 2,
          f = (x + 1) / 2,
          g = (u + c) / 4,
          b = (l + y) / 4,
          S = (m + p) / 4;
        return (
          t > a && t > f
            ? t < n
              ? ((s = 0), (r = 0.*********), (i = 0.*********))
              : ((s = Math.sqrt(t)), (r = g / s), (i = b / s))
            : a > f
            ? a < n
              ? ((s = 0.*********), (r = 0), (i = 0.*********))
              : ((r = Math.sqrt(a)), (s = g / r), (i = S / r))
            : f < n
            ? ((s = 0.*********), (r = 0.*********), (i = 0))
            : ((i = Math.sqrt(f)), (s = b / i), (r = S / i)),
          this.set(s, r, i, e),
          this
        );
      }
      let f = Math.sqrt(
        (p - m) * (p - m) + (l - y) * (l - y) + (c - u) * (c - u)
      );
      return (
        Math.abs(f) < 0.001 && (f = 1),
        (this.x = (p - m) / f),
        (this.y = (l - y) / f),
        (this.z = (c - u) / f),
        (this.w = Math.acos((o + d + x - 1) / 2)),
        this
      );
    }
    min(t) {
      return (
        (this.x = Math.min(this.x, t.x)),
        (this.y = Math.min(this.y, t.y)),
        (this.z = Math.min(this.z, t.z)),
        (this.w = Math.min(this.w, t.w)),
        this
      );
    }
    max(t) {
      return (
        (this.x = Math.max(this.x, t.x)),
        (this.y = Math.max(this.y, t.y)),
        (this.z = Math.max(this.z, t.z)),
        (this.w = Math.max(this.w, t.w)),
        this
      );
    }
    clamp(t, e) {
      return (
        (this.x = Math.max(t.x, Math.min(e.x, this.x))),
        (this.y = Math.max(t.y, Math.min(e.y, this.y))),
        (this.z = Math.max(t.z, Math.min(e.z, this.z))),
        (this.w = Math.max(t.w, Math.min(e.w, this.w))),
        this
      );
    }
    clampScalar(t, e) {
      return (
        (this.x = Math.max(t, Math.min(e, this.x))),
        (this.y = Math.max(t, Math.min(e, this.y))),
        (this.z = Math.max(t, Math.min(e, this.z))),
        (this.w = Math.max(t, Math.min(e, this.w))),
        this
      );
    }
    clampLength(t, e) {
      const s = this.length();
      return this.divideScalar(s || 1).multiplyScalar(
        Math.max(t, Math.min(e, s))
      );
    }
    floor() {
      return (
        (this.x = Math.floor(this.x)),
        (this.y = Math.floor(this.y)),
        (this.z = Math.floor(this.z)),
        (this.w = Math.floor(this.w)),
        this
      );
    }
    ceil() {
      return (
        (this.x = Math.ceil(this.x)),
        (this.y = Math.ceil(this.y)),
        (this.z = Math.ceil(this.z)),
        (this.w = Math.ceil(this.w)),
        this
      );
    }
    round() {
      return (
        (this.x = Math.round(this.x)),
        (this.y = Math.round(this.y)),
        (this.z = Math.round(this.z)),
        (this.w = Math.round(this.w)),
        this
      );
    }
    roundToZero() {
      return (
        (this.x = this.x < 0 ? Math.ceil(this.x) : Math.floor(this.x)),
        (this.y = this.y < 0 ? Math.ceil(this.y) : Math.floor(this.y)),
        (this.z = this.z < 0 ? Math.ceil(this.z) : Math.floor(this.z)),
        (this.w = this.w < 0 ? Math.ceil(this.w) : Math.floor(this.w)),
        this
      );
    }
    negate() {
      return (
        (this.x = -this.x),
        (this.y = -this.y),
        (this.z = -this.z),
        (this.w = -this.w),
        this
      );
    }
    dot(t) {
      return this.x * t.x + this.y * t.y + this.z * t.z + this.w * t.w;
    }
    lengthSq() {
      return (
        this.x * this.x + this.y * this.y + this.z * this.z + this.w * this.w
      );
    }
    length() {
      return Math.sqrt(
        this.x * this.x + this.y * this.y + this.z * this.z + this.w * this.w
      );
    }
    manhattanLength() {
      return (
        Math.abs(this.x) +
        Math.abs(this.y) +
        Math.abs(this.z) +
        Math.abs(this.w)
      );
    }
    normalize() {
      return this.divideScalar(this.length() || 1);
    }
    setLength(t) {
      return this.normalize().multiplyScalar(t);
    }
    lerp(t, e) {
      return (
        (this.x += (t.x - this.x) * e),
        (this.y += (t.y - this.y) * e),
        (this.z += (t.z - this.z) * e),
        (this.w += (t.w - this.w) * e),
        this
      );
    }
    lerpVectors(t, e, s) {
      return (
        (this.x = t.x + (e.x - t.x) * s),
        (this.y = t.y + (e.y - t.y) * s),
        (this.z = t.z + (e.z - t.z) * s),
        (this.w = t.w + (e.w - t.w) * s),
        this
      );
    }
    equals(t) {
      return (
        t.x === this.x && t.y === this.y && t.z === this.z && t.w === this.w
      );
    }
    fromArray(t, e = 0) {
      return (
        (this.x = t[e]),
        (this.y = t[e + 1]),
        (this.z = t[e + 2]),
        (this.w = t[e + 3]),
        this
      );
    }
    toArray(t = [], e = 0) {
      return (
        (t[e] = this.x),
        (t[e + 1] = this.y),
        (t[e + 2] = this.z),
        (t[e + 3] = this.w),
        t
      );
    }
    fromBufferAttribute(t, e) {
      return (
        (this.x = t.getX(e)),
        (this.y = t.getY(e)),
        (this.z = t.getZ(e)),
        (this.w = t.getW(e)),
        this
      );
    }
    random() {
      return (
        (this.x = Math.random()),
        (this.y = Math.random()),
        (this.z = Math.random()),
        (this.w = Math.random()),
        this
      );
    }
    *[Symbol.iterator]() {
      yield this.x, yield this.y, yield this.z, yield this.w;
    }
  }
  "undefined" != typeof __THREE_DEVTOOLS__ &&
    __THREE_DEVTOOLS__.dispatchEvent(
      new CustomEvent("register", { detail: { revision: e } })
    ),
    "undefined" != typeof window &&
      (window.__THREE__
        ? console.warn(
            "WARNING: Multiple instances of Three.js being imported."
          )
        : (window.__THREE__ = e)),
    (t.ACESFilmicToneMapping = 4),
    (t.AddEquation = 100),
    (t.AddOperation = 2),
    (t.AdditiveAnimationBlendMode = 2501),
    (t.AdditiveBlending = 2),
    (t.AlphaFormat = 1021),
    (t.AlwaysDepth = 1),
    (t.AlwaysStencilFunc = 519),
    (t.ArcCurve = we),
    (t.BackSide = 1),
    (t.BasicDepthPacking = 3200),
    (t.BasicShadowMap = 0),
    (t.Box2 = class {
      constructor(t = new Mt(1 / 0, 1 / 0), e = new Mt(-1 / 0, -1 / 0)) {
        (this.isBox2 = !0), (this.min = t), (this.max = e);
      }
      set(t, e) {
        return this.min.copy(t), this.max.copy(e), this;
      }
      setFromPoints(t) {
        this.makeEmpty();
        for (let e = 0, s = t.length; e < s; e++) this.expandByPoint(t[e]);
        return this;
      }
      setFromCenterAndSize(t, e) {
        const s = er.copy(e).multiplyScalar(0.5);
        return this.min.copy(t).sub(s), this.max.copy(t).add(s), this;
      }
      clone() {
        return new this.constructor().copy(this);
      }
      copy(t) {
        return this.min.copy(t.min), this.max.copy(t.max), this;
      }
      makeEmpty() {
        return (
          (this.min.x = this.min.y = 1 / 0),
          (this.max.x = this.max.y = -1 / 0),
          this
        );
      }
      isEmpty() {
        return this.max.x < this.min.x || this.max.y < this.min.y;
      }
      getCenter(t) {
        return this.isEmpty()
          ? t.set(0, 0)
          : t.addVectors(this.min, this.max).multiplyScalar(0.5);
      }
      getSize(t) {
        return this.isEmpty() ? t.set(0, 0) : t.subVectors(this.max, this.min);
      }
      expandByPoint(t) {
        return this.min.min(t), this.max.max(t), this;
      }
      expandByVector(t) {
        return this.min.sub(t), this.max.add(t), this;
      }
      expandByScalar(t) {
        return this.min.addScalar(-t), this.max.addScalar(t), this;
      }
      containsPoint(t) {
        return !(
          t.x < this.min.x ||
          t.x > this.max.x ||
          t.y < this.min.y ||
          t.y > this.max.y
        );
      }
      containsBox(t) {
        return (
          this.min.x <= t.min.x &&
          t.max.x <= this.max.x &&
          this.min.y <= t.min.y &&
          t.max.y <= this.max.y
        );
      }
      getParameter(t, e) {
        return e.set(
          (t.x - this.min.x) / (this.max.x - this.min.x),
          (t.y - this.min.y) / (this.max.y - this.min.y)
        );
      }
      intersectsBox(t) {
        return !(
          t.max.x < this.min.x ||
          t.min.x > this.max.x ||
          t.max.y < this.min.y ||
          t.min.y > this.max.y
        );
      }
      clampPoint(t, e) {
        return e.copy(t).clamp(this.min, this.max);
      }
      distanceToPoint(t) {
        return er.copy(t).clamp(this.min, this.max).sub(t).length();
      }
      intersect(t) {
        return this.min.max(t.min), this.max.min(t.max), this;
      }
      union(t) {
        return this.min.min(t.min), this.max.max(t.max), this;
      }
      translate(t) {
        return this.min.add(t), this.max.add(t), this;
      }
      equals(t) {
        return t.min.equals(this.min) && t.max.equals(this.max);
      }
    }),
    (t.Box3 = It),
    (t.BoxGeometry = be),
    (t.BufferAttribute = se),
    (t.BufferGeometry = ge),
    (t.ByteType = 1010),
    (t.CapsuleGeometry = Ue),
    (t.CatmullRomCurve3 = Be),
    (t.CineonToneMapping = 3),
    (t.CircleGeometry = Je),
    (t.ClampToEdgeWrapping = r),
    (t.ConeGeometry = Xe),
    (t.CubeReflectionMapping = 301),
    (t.CubeRefractionMapping = 302),
    (t.CubeUVReflectionMapping = 306),
    (t.CubicBezierCurve = Te),
    (t.CubicBezierCurve3 = Oe),
    (t.CullFaceBack = 1),
    (t.CullFaceFront = 2),
    (t.CullFaceFrontBack = 3),
    (t.CullFaceNone = 0),
    (t.Curve = Se),
    (t.CurvePath = De),
    (t.CustomBlending = 5),
    (t.CustomToneMapping = 5),
    (t.CylinderGeometry = He),
    (t.DecrementStencilOp = 7683),
    (t.DecrementWrapStencilOp = 34056),
    (t.DepthFormat = 1026),
    (t.DepthStencilFormat = 1027),
    (t.DodecahedronGeometry = Ze),
    (t.DoubleSide = 2),
    (t.DstAlphaFactor = 206),
    (t.DstColorFactor = 208),
    (t.DynamicCopyUsage = 35050),
    (t.DynamicDrawUsage = 35048),
    (t.DynamicReadUsage = 35049),
    (t.EdgesGeometry = class extends ge {
      constructor(t = null, e = 1) {
        if (
          (super(),
          (this.type = "EdgesGeometry"),
          (this.parameters = { geometry: t, thresholdAngle: e }),
          null !== t)
        ) {
          const s = 4,
            r = Math.pow(10, s),
            i = Math.cos(G * e),
            n = t.getIndex(),
            h = t.getAttribute("position"),
            a = n ? n.count : h.count,
            o = [0, 0, 0],
            u = ["a", "b", "c"],
            l = new Array(3),
            c = {},
            d = [];
          for (let t = 0; t < a; t += 3) {
            n
              ? ((o[0] = n.getX(t)),
                (o[1] = n.getX(t + 1)),
                (o[2] = n.getX(t + 2)))
              : ((o[0] = t), (o[1] = t + 1), (o[2] = t + 2));
            const { a: e, b: s, c: a } = us;
            if (
              (e.fromBufferAttribute(h, o[0]),
              s.fromBufferAttribute(h, o[1]),
              a.fromBufferAttribute(h, o[2]),
              us.getNormal(os),
              (l[0] = `${Math.round(e.x * r)},${Math.round(
                e.y * r
              )},${Math.round(e.z * r)}`),
              (l[1] = `${Math.round(s.x * r)},${Math.round(
                s.y * r
              )},${Math.round(s.z * r)}`),
              (l[2] = `${Math.round(a.x * r)},${Math.round(
                a.y * r
              )},${Math.round(a.z * r)}`),
              l[0] !== l[1] && l[1] !== l[2] && l[2] !== l[0])
            )
              for (let t = 0; t < 3; t++) {
                const e = (t + 1) % 3,
                  s = l[t],
                  r = l[e],
                  n = us[u[t]],
                  h = us[u[e]],
                  a = `${s}_${r}`,
                  m = `${r}_${s}`;
                m in c && c[m]
                  ? (os.dot(c[m].normal) <= i &&
                      (d.push(n.x, n.y, n.z), d.push(h.x, h.y, h.z)),
                    (c[m] = null))
                  : a in c ||
                    (c[a] = { index0: o[t], index1: o[e], normal: os.clone() });
              }
          }
          for (const t in c)
            if (c[t]) {
              const { index0: e, index1: s } = c[t];
              hs.fromBufferAttribute(h, e),
                as.fromBufferAttribute(h, s),
                d.push(hs.x, hs.y, hs.z),
                d.push(as.x, as.y, as.z);
            }
          this.setAttribute("position", new ne(d, 3));
        }
      }
    }),
    (t.EllipseCurve = Me),
    (t.EqualDepth = 4),
    (t.EqualStencilFunc = 514),
    (t.EquirectangularReflectionMapping = 303),
    (t.EquirectangularRefractionMapping = 304),
    (t.Euler = it),
    (t.ExtrudeGeometry = Is),
    (t.Float16BufferAttribute = class extends se {
      constructor(t, e, s) {
        super(new Uint16Array(t), e, s), (this.isFloat16BufferAttribute = !0);
      }
    }),
    (t.Float32BufferAttribute = ne),
    (t.Float64BufferAttribute = class extends se {
      constructor(t, e, s) {
        super(new Float64Array(t), e, s);
      }
    }),
    (t.FloatType = 1015),
    (t.FrontSide = 0),
    (t.GLBufferAttribute = class {
      constructor(t, e, s, r, i) {
        (this.isGLBufferAttribute = !0),
          (this.buffer = t),
          (this.type = e),
          (this.itemSize = s),
          (this.elementSize = r),
          (this.count = i),
          (this.version = 0);
      }
      set needsUpdate(t) {
        !0 === t && this.version++;
      }
      setBuffer(t) {
        return (this.buffer = t), this;
      }
      setType(t, e) {
        return (this.type = t), (this.elementSize = e), this;
      }
      setItemSize(t) {
        return (this.itemSize = t), this;
      }
      setCount(t) {
        return (this.count = t), this;
      }
    }),
    (t.GLSL1 = "100"),
    (t.GLSL3 = "300 es"),
    (t.GreaterDepth = 6),
    (t.GreaterEqualDepth = 5),
    (t.GreaterEqualStencilFunc = 518),
    (t.GreaterStencilFunc = 516),
    (t.Group = class extends St {
      constructor() {
        super(), (this.isGroup = !0), (this.type = "Group");
      }
    }),
    (t.HalfFloatType = 1016),
    (t.IcosahedronGeometry = Vs),
    (t.IncrementStencilOp = 7682),
    (t.IncrementWrapStencilOp = 34055),
    (t.InstancedBufferAttribute = class extends se {
      constructor(t, e, s, r = 1) {
        super(t, e, s),
          (this.isInstancedBufferAttribute = !0),
          (this.meshPerAttribute = r);
      }
      copy(t) {
        return (
          super.copy(t), (this.meshPerAttribute = t.meshPerAttribute), this
        );
      }
      toJSON() {
        const t = super.toJSON();
        return (
          (t.meshPerAttribute = this.meshPerAttribute),
          (t.isInstancedBufferAttribute = !0),
          t
        );
      }
    }),
    (t.InstancedBufferGeometry = class extends ge {
      constructor() {
        super(),
          (this.isInstancedBufferGeometry = !0),
          (this.type = "InstancedBufferGeometry"),
          (this.instanceCount = 1 / 0);
      }
      copy(t) {
        return super.copy(t), (this.instanceCount = t.instanceCount), this;
      }
      clone() {
        return new this.constructor().copy(this);
      }
      toJSON() {
        const t = super.toJSON(this);
        return (
          (t.instanceCount = this.instanceCount),
          (t.isInstancedBufferGeometry = !0),
          t
        );
      }
    }),
    (t.InstancedInterleavedBuffer = class extends tr {
      constructor(t, e, s = 1) {
        super(t, e),
          (this.isInstancedInterleavedBuffer = !0),
          (this.meshPerAttribute = s);
      }
      copy(t) {
        return (
          super.copy(t), (this.meshPerAttribute = t.meshPerAttribute), this
        );
      }
      clone(t) {
        const e = super.clone(t);
        return (e.meshPerAttribute = this.meshPerAttribute), e;
      }
      toJSON(t) {
        const e = super.toJSON(t);
        return (
          (e.isInstancedInterleavedBuffer = !0),
          (e.meshPerAttribute = this.meshPerAttribute),
          e
        );
      }
    }),
    (t.Int16BufferAttribute = class extends se {
      constructor(t, e, s) {
        super(new Int16Array(t), e, s);
      }
    }),
    (t.Int32BufferAttribute = class extends se {
      constructor(t, e, s) {
        super(new Int32Array(t), e, s);
      }
    }),
    (t.Int8BufferAttribute = class extends se {
      constructor(t, e, s) {
        super(new Int8Array(t), e, s);
      }
    }),
    (t.IntType = 1013),
    (t.InterleavedBuffer = tr),
    (t.InterleavedBufferAttribute = Qs),
    (t.InterpolateDiscrete = 2300),
    (t.InterpolateLinear = 2301),
    (t.InterpolateSmooth = 2302),
    (t.InvertStencilOp = 5386),
    (t.KeepStencilOp = 7680),
    (t.LatheGeometry = qe),
    (t.LessDepth = 2),
    (t.LessEqualDepth = 3),
    (t.LessEqualStencilFunc = 515),
    (t.LessStencilFunc = 513),
    (t.Line3 = class {
      constructor(t = new J(), e = new J()) {
        (this.start = t), (this.end = e);
      }
      set(t, e) {
        return this.start.copy(t), this.end.copy(e), this;
      }
      copy(t) {
        return this.start.copy(t.start), this.end.copy(t.end), this;
      }
      getCenter(t) {
        return t.addVectors(this.start, this.end).multiplyScalar(0.5);
      }
      delta(t) {
        return t.subVectors(this.end, this.start);
      }
      distanceSq() {
        return this.start.distanceToSquared(this.end);
      }
      distance() {
        return this.start.distanceTo(this.end);
      }
      at(t, e) {
        return this.delta(e).multiplyScalar(t).add(this.start);
      }
      closestPointToPointParameter(t, e) {
        sr.subVectors(t, this.start), rr.subVectors(this.end, this.start);
        const s = rr.dot(rr);
        let r = rr.dot(sr) / s;
        return e && (r = I(r, 0, 1)), r;
      }
      closestPointToPoint(t, e, s) {
        const r = this.closestPointToPointParameter(t, e);
        return this.delta(s).multiplyScalar(r).add(this.start);
      }
      applyMatrix4(t) {
        return this.start.applyMatrix4(t), this.end.applyMatrix4(t), this;
      }
      equals(t) {
        return t.start.equals(this.start) && t.end.equals(this.end);
      }
      clone() {
        return new this.constructor().copy(this);
      }
    }),
    (t.LineCurve = Ne),
    (t.LineCurve3 = Fe),
    (t.LinearEncoding = 3e3),
    (t.LinearFilter = 1006),
    (t.LinearMipMapLinearFilter = 1008),
    (t.LinearMipMapNearestFilter = 1007),
    (t.LinearMipmapLinearFilter = 1008),
    (t.LinearMipmapNearestFilter = 1007),
    (t.LinearSRGBColorSpace = N),
    (t.LinearToneMapping = 1),
    (t.LoopOnce = 2200),
    (t.LoopPingPong = 2202),
    (t.LoopRepeat = 2201),
    (t.LuminanceAlphaFormat = 1025),
    (t.LuminanceFormat = 1024),
    (t.MOUSE = { LEFT: 0, MIDDLE: 1, RIGHT: 2, ROTATE: 0, DOLLY: 1, PAN: 2 }),
    (t.Matrix3 = ht),
    (t.Matrix4 = W),
    (t.MaxEquation = 104),
    (t.MinEquation = 103),
    (t.MirroredRepeatWrapping = i),
    (t.MixOperation = 1),
    (t.MultiplyBlending = 4),
    (t.MultiplyOperation = 0),
    (t.NearestFilter = 1003),
    (t.NearestMipMapLinearFilter = 1005),
    (t.NearestMipMapNearestFilter = 1004),
    (t.NearestMipmapLinearFilter = 1005),
    (t.NearestMipmapNearestFilter = 1004),
    (t.NeverDepth = 0),
    (t.NeverStencilFunc = 512),
    (t.NoBlending = 0),
    (t.NoColorSpace = ""),
    (t.NoToneMapping = 0),
    (t.NormalAnimationBlendMode = 2500),
    (t.NormalBlending = 1),
    (t.NotEqualDepth = 7),
    (t.NotEqualStencilFunc = 517),
    (t.ObjectSpaceNormalMap = 1),
    (t.OctahedronGeometry = qs),
    (t.OneFactor = 201),
    (t.OneMinusDstAlphaFactor = 207),
    (t.OneMinusDstColorFactor = 209),
    (t.OneMinusSrcAlphaFactor = 205),
    (t.OneMinusSrcColorFactor = 203),
    (t.PCFShadowMap = 1),
    (t.PCFSoftShadowMap = 2),
    (t.Path = Ve),
    (t.PlaneGeometry = Us),
    (t.PolyhedronGeometry = We),
    (t.QuadraticBezierCurve = ke),
    (t.QuadraticBezierCurve3 = Ge),
    (t.REVISION = e),
    (t.RGBADepthPacking = 3201),
    (t.RGBAFormat = h),
    (t.RGBAIntegerFormat = 1033),
    (t.RGBA_ASTC_10x10_Format = B),
    (t.RGBA_ASTC_10x5_Format = A),
    (t.RGBA_ASTC_10x6_Format = z),
    (t.RGBA_ASTC_10x8_Format = C),
    (t.RGBA_ASTC_12x10_Format = R),
    (t.RGBA_ASTC_12x12_Format = E),
    (t.RGBA_ASTC_4x4_Format = f),
    (t.RGBA_ASTC_5x4_Format = g),
    (t.RGBA_ASTC_5x5_Format = b),
    (t.RGBA_ASTC_6x5_Format = S),
    (t.RGBA_ASTC_6x6_Format = M),
    (t.RGBA_ASTC_8x5_Format = w),
    (t.RGBA_ASTC_8x6_Format = _),
    (t.RGBA_ASTC_8x8_Format = v),
    (t.RGBA_BPTC_Format = P),
    (t.RGBA_ETC2_EAC_Format = x),
    (t.RGBA_PVRTC_2BPPV1_Format = y),
    (t.RGBA_PVRTC_4BPPV1_Format = m),
    (t.RGBA_S3TC_DXT1_Format = o),
    (t.RGBA_S3TC_DXT3_Format = u),
    (t.RGBA_S3TC_DXT5_Format = l),
    (t.RGBFormat = 1022),
    (t.RGB_ETC1_Format = 36196),
    (t.RGB_ETC2_Format = p),
    (t.RGB_PVRTC_2BPPV1_Format = d),
    (t.RGB_PVRTC_4BPPV1_Format = c),
    (t.RGB_S3TC_DXT1_Format = a),
    (t.RGFormat = 1030),
    (t.RGIntegerFormat = 1031),
    (t.RedFormat = 1028),
    (t.RedIntegerFormat = 1029),
    (t.ReinhardToneMapping = 2),
    (t.RepeatWrapping = s),
    (t.ReplaceStencilOp = 7681),
    (t.ReverseSubtractEquation = 102),
    (t.RingGeometry = Js),
    (t.SRGBColorSpace = O),
    (t.Shape = ls),
    (t.ShapeGeometry = Hs),
    (t.ShapePath = class {
      constructor() {
        (this.type = "ShapePath"),
          (this.color = new Ot()),
          (this.subPaths = []),
          (this.currentPath = null);
      }
      moveTo(t, e) {
        return (
          (this.currentPath = new Ve()),
          this.subPaths.push(this.currentPath),
          this.currentPath.moveTo(t, e),
          this
        );
      }
      lineTo(t, e) {
        return this.currentPath.lineTo(t, e), this;
      }
      quadraticCurveTo(t, e, s, r) {
        return this.currentPath.quadraticCurveTo(t, e, s, r), this;
      }
      bezierCurveTo(t, e, s, r, i, n) {
        return this.currentPath.bezierCurveTo(t, e, s, r, i, n), this;
      }
      splineThru(t) {
        return this.currentPath.splineThru(t), this;
      }
      toShapes(t) {
        function e(t, e) {
          const s = e.length;
          let r = !1;
          for (let i = s - 1, n = 0; n < s; i = n++) {
            let s = e[i],
              h = e[n],
              a = h.x - s.x,
              o = h.y - s.y;
            if (Math.abs(o) > Number.EPSILON) {
              if (
                (o < 0 && ((s = e[n]), (a = -a), (h = e[i]), (o = -o)),
                t.y < s.y || t.y > h.y)
              )
                continue;
              if (t.y === s.y) {
                if (t.x === s.x) return !0;
              } else {
                const e = o * (t.x - s.x) - a * (t.y - s.y);
                if (0 === e) return !0;
                if (e < 0) continue;
                r = !r;
              }
            } else {
              if (t.y !== s.y) continue;
              if ((h.x <= t.x && t.x <= s.x) || (s.x <= t.x && t.x <= h.x))
                return !0;
            }
          }
          return r;
        }
        const s = ks.isClockWise,
          r = this.subPaths;
        if (0 === r.length) return [];
        let i, n, h;
        const a = [];
        if (1 === r.length)
          return (
            (n = r[0]), (h = new ls()), (h.curves = n.curves), a.push(h), a
          );
        let o = !s(r[0].getPoints());
        o = t ? !o : o;
        const u = [],
          l = [];
        let c,
          d,
          m = [],
          y = 0;
        (l[y] = void 0), (m[y] = []);
        for (let e = 0, h = r.length; e < h; e++)
          (n = r[e]),
            (c = n.getPoints()),
            (i = s(c)),
            (i = t ? !i : i),
            i
              ? (!o && l[y] && y++,
                (l[y] = { s: new ls(), p: c }),
                (l[y].s.curves = n.curves),
                o && y++,
                (m[y] = []))
              : m[y].push({ h: n, p: c[0] });
        if (!l[0])
          return (function (t) {
            const e = [];
            for (let s = 0, r = t.length; s < r; s++) {
              const r = t[s],
                i = new ls();
              (i.curves = r.curves), e.push(i);
            }
            return e;
          })(r);
        if (l.length > 1) {
          let t = !1,
            s = 0;
          for (let t = 0, e = l.length; t < e; t++) u[t] = [];
          for (let r = 0, i = l.length; r < i; r++) {
            const i = m[r];
            for (let n = 0; n < i.length; n++) {
              const h = i[n];
              let a = !0;
              for (let i = 0; i < l.length; i++)
                e(h.p, l[i].p) &&
                  (r !== i && s++, a ? ((a = !1), u[i].push(h)) : (t = !0));
              a && u[r].push(h);
            }
          }
          s > 0 && !1 === t && (m = u);
        }
        for (let t = 0, e = l.length; t < e; t++) {
          (h = l[t].s), a.push(h), (d = m[t]);
          for (let t = 0, e = d.length; t < e; t++) h.holes.push(d[t].h);
        }
        return a;
      }
    }),
    (t.ShortType = 1011),
    (t.SphereGeometry = Xs),
    (t.SplineCurve = Le),
    (t.SrcAlphaFactor = 204),
    (t.SrcAlphaSaturateFactor = 210),
    (t.SrcColorFactor = 202),
    (t.StaticCopyUsage = 35046),
    (t.StaticDrawUsage = F),
    (t.StaticReadUsage = 35045),
    (t.StreamCopyUsage = 35042),
    (t.StreamDrawUsage = 35040),
    (t.StreamReadUsage = 35041),
    (t.SubtractEquation = 101),
    (t.SubtractiveBlending = 3),
    (t.TOUCH = { ROTATE: 0, PAN: 1, DOLLY_PAN: 2, DOLLY_ROTATE: 3 }),
    (t.TangentSpaceNormalMap = 0),
    (t.TetrahedronGeometry = Ws),
    (t.Texture = Lt),
    (t.TorusGeometry = Zs),
    (t.TorusKnotGeometry = Ys),
    (t.TriangleFanDrawMode = 2),
    (t.TriangleStripDrawMode = 1),
    (t.TrianglesDrawMode = 0),
    (t.TubeGeometry = js),
    (t.UVMapping = 300),
    (t.Uint16BufferAttribute = re),
    (t.Uint32BufferAttribute = ie),
    (t.Uint8BufferAttribute = class extends se {
      constructor(t, e, s) {
        super(new Uint8Array(t), e, s);
      }
    }),
    (t.Uint8ClampedBufferAttribute = class extends se {
      constructor(t, e, s) {
        super(new Uint8ClampedArray(t), e, s);
      }
    }),
    (t.UnsignedByteType = n),
    (t.UnsignedInt248Type = 1020),
    (t.UnsignedIntType = 1014),
    (t.UnsignedShort4444Type = 1017),
    (t.UnsignedShort5551Type = 1018),
    (t.UnsignedShortType = 1012),
    (t.VSMShadowMap = 3),
    (t.Vector2 = Mt),
    (t.Vector3 = J),
    (t.Vector4 = ir),
    (t.WebGLUtils = function (t, e, s) {
      const r = s.isWebGL2;
      return {
        convert: function (s, i = null) {
          let O;
          if (s === n) return 5121;
          if (1017 === s) return 32819;
          if (1018 === s) return 32820;
          if (1010 === s) return 5120;
          if (1011 === s) return 5122;
          if (1012 === s) return 5123;
          if (1013 === s) return 5124;
          if (1014 === s) return 5125;
          if (1015 === s) return 5126;
          if (1016 === s)
            return r
              ? 5131
              : ((O = e.get("OES_texture_half_float")),
                null !== O ? O.HALF_FLOAT_OES : null);
          if (1021 === s) return 6406;
          if (s === h) return 6408;
          if (1024 === s) return 6409;
          if (1025 === s) return 6410;
          if (1026 === s) return 6402;
          if (1027 === s) return 34041;
          if (1028 === s) return 6403;
          if (1022 === s)
            return (
              console.warn(
                "THREE.WebGLRenderer: THREE.RGBFormat has been removed. Use THREE.RGBAFormat instead. https://github.com/mrdoob/three.js/pull/23228"
              ),
              6408
            );
          if (1035 === s)
            return (
              (O = e.get("EXT_sRGB")), null !== O ? O.SRGB_ALPHA_EXT : null
            );
          if (1029 === s) return 36244;
          if (1030 === s) return 33319;
          if (1031 === s) return 33320;
          if (1033 === s) return 36249;
          if (s === a || s === o || s === u || s === l)
            if (i === T) {
              if (
                ((O = e.get("WEBGL_compressed_texture_s3tc_srgb")), null === O)
              )
                return null;
              if (s === a) return O.COMPRESSED_SRGB_S3TC_DXT1_EXT;
              if (s === o) return O.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT;
              if (s === u) return O.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT;
              if (s === l) return O.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT;
            } else {
              if (((O = e.get("WEBGL_compressed_texture_s3tc")), null === O))
                return null;
              if (s === a) return O.COMPRESSED_RGB_S3TC_DXT1_EXT;
              if (s === o) return O.COMPRESSED_RGBA_S3TC_DXT1_EXT;
              if (s === u) return O.COMPRESSED_RGBA_S3TC_DXT3_EXT;
              if (s === l) return O.COMPRESSED_RGBA_S3TC_DXT5_EXT;
            }
          if (s === c || s === d || s === m || s === y) {
            if (((O = e.get("WEBGL_compressed_texture_pvrtc")), null === O))
              return null;
            if (s === c) return O.COMPRESSED_RGB_PVRTC_4BPPV1_IMG;
            if (s === d) return O.COMPRESSED_RGB_PVRTC_2BPPV1_IMG;
            if (s === m) return O.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG;
            if (s === y) return O.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG;
          }
          if (36196 === s)
            return (
              (O = e.get("WEBGL_compressed_texture_etc1")),
              null !== O ? O.COMPRESSED_RGB_ETC1_WEBGL : null
            );
          if (s === p || s === x) {
            if (((O = e.get("WEBGL_compressed_texture_etc")), null === O))
              return null;
            if (s === p)
              return i === T ? O.COMPRESSED_SRGB8_ETC2 : O.COMPRESSED_RGB8_ETC2;
            if (s === x)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC
                : O.COMPRESSED_RGBA8_ETC2_EAC;
          }
          if (
            s === f ||
            s === g ||
            s === b ||
            s === S ||
            s === M ||
            s === w ||
            s === _ ||
            s === v ||
            s === A ||
            s === z ||
            s === C ||
            s === B ||
            s === R ||
            s === E
          ) {
            if (((O = e.get("WEBGL_compressed_texture_astc")), null === O))
              return null;
            if (s === f)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR
                : O.COMPRESSED_RGBA_ASTC_4x4_KHR;
            if (s === g)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR
                : O.COMPRESSED_RGBA_ASTC_5x4_KHR;
            if (s === b)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR
                : O.COMPRESSED_RGBA_ASTC_5x5_KHR;
            if (s === S)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR
                : O.COMPRESSED_RGBA_ASTC_6x5_KHR;
            if (s === M)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR
                : O.COMPRESSED_RGBA_ASTC_6x6_KHR;
            if (s === w)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR
                : O.COMPRESSED_RGBA_ASTC_8x5_KHR;
            if (s === _)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR
                : O.COMPRESSED_RGBA_ASTC_8x6_KHR;
            if (s === v)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR
                : O.COMPRESSED_RGBA_ASTC_8x8_KHR;
            if (s === A)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR
                : O.COMPRESSED_RGBA_ASTC_10x5_KHR;
            if (s === z)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR
                : O.COMPRESSED_RGBA_ASTC_10x6_KHR;
            if (s === C)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR
                : O.COMPRESSED_RGBA_ASTC_10x8_KHR;
            if (s === B)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR
                : O.COMPRESSED_RGBA_ASTC_10x10_KHR;
            if (s === R)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR
                : O.COMPRESSED_RGBA_ASTC_12x10_KHR;
            if (s === E)
              return i === T
                ? O.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR
                : O.COMPRESSED_RGBA_ASTC_12x12_KHR;
          }
          if (s === P) {
            if (((O = e.get("EXT_texture_compression_bptc")), null === O))
              return null;
            if (s === P)
              return i === T
                ? O.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT
                : O.COMPRESSED_RGBA_BPTC_UNORM_EXT;
          }
          return 1020 === s
            ? r
              ? 34042
              : ((O = e.get("WEBGL_depth_texture")),
                null !== O ? O.UNSIGNED_INT_24_8_WEBGL : null)
            : void 0 !== t[s]
            ? t[s]
            : null;
        },
      };
    }),
    (t.WireframeGeometry = class extends ge {
      constructor(t = null) {
        if (
          (super(),
          (this.type = "WireframeGeometry"),
          (this.parameters = { geometry: t }),
          null !== t)
        ) {
          const e = [],
            s = new Set(),
            r = new J(),
            i = new J();
          if (null !== t.index) {
            const n = t.attributes.position,
              h = t.index;
            let a = t.groups;
            0 === a.length &&
              (a = [{ start: 0, count: h.count, materialIndex: 0 }]);
            for (let t = 0, o = a.length; t < o; ++t) {
              const o = a[t],
                u = o.start;
              for (let t = u, a = u + o.count; t < a; t += 3)
                for (let a = 0; a < 3; a++) {
                  const o = h.getX(t + a),
                    u = h.getX(t + ((a + 1) % 3));
                  r.fromBufferAttribute(n, o),
                    i.fromBufferAttribute(n, u),
                    !0 === $s(r, i, s) &&
                      (e.push(r.x, r.y, r.z), e.push(i.x, i.y, i.z));
                }
            }
          } else {
            const n = t.attributes.position;
            for (let t = 0, h = n.count / 3; t < h; t++)
              for (let h = 0; h < 3; h++) {
                const a = 3 * t + h,
                  o = 3 * t + ((h + 1) % 3);
                r.fromBufferAttribute(n, a),
                  i.fromBufferAttribute(n, o),
                  !0 === $s(r, i, s) &&
                    (e.push(r.x, r.y, r.z), e.push(i.x, i.y, i.z));
              }
          }
          this.setAttribute("position", new ne(e, 3));
        }
      }
    }),
    (t.WrapAroundEnding = 2402),
    (t.ZeroCurvatureEnding = 2400),
    (t.ZeroFactor = 200),
    (t.ZeroSlopeEnding = 2401),
    (t.ZeroStencilOp = 0),
    (t._SRGBAFormat = 1035),
    (t.sRGBEncoding = T),
    Object.defineProperty(t, "__esModule", { value: !0 });
});
