﻿<!DOCTYPE html>
<html class="no-js css-menubar" lang="zh-cn">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <!-- 移动设备 viewport -->
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no,minimal-ui" />
    <meta name="author" content="火星科技 http://mars3d.cn " />
    <!-- 360浏览器默认使用Webkit内核 -->
    <meta name="renderer" content="webkit" />
    <!-- Chrome浏览器添加桌面快捷方式（安卓） -->
    <link rel="shortcut icon" type="image/x-icon" href="/img/favicon/favicon.ico" />
    <meta name="mobile-web-app-capable" content="yes" />
    <!-- Safari浏览器添加到主屏幕（IOS） -->
    <link rel="icon" sizes="192x192" href="/img/favicon/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-title" content="火星科技MarsGIS" />
    <!-- Win8标题栏及ICON图标 -->
    <link rel="apple-touch-icon-precomposed" href="/img/favicon/apple-touch-icon.png" />
    <meta name="msapplication-TileImage" content="/img/favicon/<EMAIL>" />
    <meta name="msapplication-TileColor" content="#62a8ea" />

    <!-- 标题及搜索关键字 -->
    <meta
      name="keywords"
      content="火星科技|合肥火星|合肥火星科技|合肥火星科技有限公司|leaflet|leaflet框架|leaflet开发|cesium|cesium开发|cesium框架|gis|marsgis|地图离线|地图开发|地图框架|地图外包"
    />
    <meta
      name="description"
      content="火星科技 合肥火星 合肥火星科技 合肥火星科技有限公司 leaflet leaflet框架 leaflet开发 cesium cesium开发 cesium框架 gis marsgis 地图离线 地图开发 地图框架 地图外包 框架 开发 外包  地图离线 二维地图 三维地图 全景漫游 地理信息系统 云GIS 三维GIS GIS平台 WebGIS"
    />

    <title>功能示例(原生JS版) | Mars3D三维可视化平台 | 合肥火星科技有限公司</title>

    <link rel="stylesheet" href="./js/editor/editor.css" />
    <link rel="stylesheet" href="./lib/marsgis-editor/style.css" />

    <script src="./lib/include-lib.js" libpath="./lib/" include="jquery,layer,haoutil"></script>
    <script src="./lib/require/require.js"></script>
  </head>

  <body class="hold-transition skin-blue sidebar-mini sidebar-collapse">
    <div id="root"></div>

    <!-- <script type="module"> -->
    <script>
      require.config({ paths: { vs: "./lib/monaco-editor" } })

      require(["vs/editor/editor.main"], function (monaco) {
        require(["./lib/marsgis-editor/editor.iife.js"], function () {
          const marsEditor = new MarsgisEditor.Editor({
            baseUrl: "/",
            code: getQueryString("code"),
            configLibs: window.configLibs,
            resourcePublicPath: "/example",
            libPublicPath: "/lib/",
            framework: "es5",
            configSourceUrl: `/config/example.json`
          })

          marsEditor.renderHTML({
            container: document.getElementById("root"),
            exampleId: getExampleId(),
            exampleKey: getQueryString("key")
          })
        })
      })
      function runScript(script) {
        const newScript = document.createElement("script")
        newScript.innerHTML = script
        document.getElementById("mars-main-view").appendChild(newScript)
      }
      function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)")
        var r = window.location.search.substr(1).match(reg)
        if (r != null) {
          return decodeURI(r[2])
        }
        return null
      }

      function $message(msg, type) {
        return haoutil.msg(msg, type)
      }
      /**
       *  获取示例ID
       * @export
       * @return {string}  示例ID
       */
      function getExampleId() {
        let exampleId = getQueryString("id")
        if (exampleId) {
          return exampleId.replace(/\\/gm, "/").replace("example/", "").replace("/map.js", "").replace("/index.html", "")
        }
      }
    </script>
  </body>
</html>
