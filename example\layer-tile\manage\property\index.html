<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0" />
    <meta name="author" content="火星科技 http://mars3d.cn " />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="x5-fullscreen" content="true" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />

    <!-- 标题及搜索关键字 -->
    <meta name="keywords" content="火星科技,cesium,3D,GIS,marsgis,三维,地球,地图,开发,框架,系统,示例,资料,模型,离线,外包,合肥,安徽,中国" />
    <meta
      name="description"
      content="火星科技 合肥火星 合肥火星科技 合肥火星科技有限公司 leaflet leaflet框架 leaflet开发 cesium cesium开发 cesium框架 三维 地球 模型  gis marsgis 地图离线 地图开发 地图框架 地图外包 框架 开发 外包  地图离线 二维地图 三维地图 全景漫游 地理信息系统 云GIS 三维GIS GIS平台 WebGIS"
    />

    <link rel="shortcut icon" type="image/x-icon" href="/img/favicon/favicon.ico" />
    <title>瓦片底图的参数 | Mars3D | 三维地图 | 火星科技 | 合肥火星科技有限公司</title>

    <!--第三方lib-->
    <script
      type="text/javascript"
      src="/lib/include-lib.js"
      libpath="/lib/"
      include="jquery,layer,toastr,jquery.range,font-awesome,bootstrap,layer,haoutil,mars3d"
    ></script>

    <link href="/css/style.css" rel="stylesheet" />
    <style>
      #toolbar input {
        vertical-align: middle;
        padding-top: 2px;
        padding-bottom: 2px;
      }
    </style>
  </head>

  <body class="dark">


    <div id="mars3dContainer" class="mars3d-container"></div>

    <div id="toolbar" class="infoview infoview-right">
      <h4 class="title">栅格底图参数</h4>
      <table class="mars-table">
        <tbody>
          <tr>
            <td>亮度</td>
            <td>
              <input type="range" min="0" max="3" step="0.01" data-bind="value: brightness, valueUpdate: 'input'" />
            </td>
            <td>
              <input type="text" min="0" max="3" step="0.01" size="5" data-bind="value: brightness" />
            </td>
          </tr>
          <tr>
            <td>对比度</td>
            <td>
              <input type="range" min="0" max="3" step="0.01" data-bind="value: contrast, valueUpdate: 'input'" />
            </td>
            <td>
              <input type="text" min="0" max="3" step="0.01" size="5" data-bind="value: contrast" />
            </td>
          </tr>
          <tr>
            <td>色彩</td>
            <td>
              <input type="range" min="0" max="3" step="0.01" data-bind="value: hue, valueUpdate: 'input'" />
            </td>
            <td>
              <input type="text" min="0" max="3" step="0.01" size="5" data-bind="value: hue" />
            </td>
          </tr>
          <tr>
            <td>饱和度</td>
            <td>
              <input type="range" min="0" max="3" step="0.01" data-bind="value: saturation, valueUpdate: 'input'" />
            </td>
            <td>
              <input type="text" min="0" max="3" step="0.01" size="5" data-bind="value: saturation" />
            </td>
          </tr>
          <tr>
            <td>伽马值</td>
            <td>
              <input type="range" min="0" max="3" step="0.01" data-bind="value: gamma, valueUpdate: 'input'" />
            </td>
            <td>
              <input type="text" min="0" max="3" step="0.01" size="5" data-bind="value: gamma" />
            </td>
          </tr>
          <tr>
            <td>透明度</td>
            <td>
              <input type="range" min="0" max="1" step="0.01" data-bind="value: opacity, valueUpdate: 'input'" />
            </td>
            <td>
              <input type="text" min="0" max="1" step="0.01" size="5" data-bind="value: opacity" />
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <script src="/js/common.js"></script>
    <script src="./map.js"></script>
    <script type="text/javascript">
      "use script" //开发环境建议开启严格模式

      function initUI(options) {
        // The viewModel tracks the state of our mini application.
        let viewModel = {
          brightness: 1, //亮度
          contrast: 1, //对比度
          hue: 0.1, //色彩
          saturation: 1, //饱和度
          gamma: 0.2, //伽马值
          opacity: 1 //透明度
        }

        // Convert the viewModel members into knockout observables.
        Cesium.knockout.track(viewModel)
        let toolbar = document.getElementById("toolbar")
        Cesium.knockout.applyBindings(viewModel, toolbar)

        // Make the active imagery layer a subscriber of the viewModel.
        function subscribeLayerParameter(name) {
          Cesium.knockout.getObservable(viewModel, name).subscribe(function (newValue) {
            setLayerOptions(name, newValue)
          })
        }
        subscribeLayerParameter("brightness")
        subscribeLayerParameter("contrast")
        subscribeLayerParameter("hue")
        subscribeLayerParameter("saturation")
        subscribeLayerParameter("gamma")
        subscribeLayerParameter("opacity")
      }
    </script>
  </body>
</html>
