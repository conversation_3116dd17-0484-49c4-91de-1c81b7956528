<!DOCTYPE html>
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta name="viewport"
    content="width=device-width,initial-scale=1,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0" />
  <meta name="author" content="火星科技 http://mars3d.cn " />
  <meta name="apple-touch-fullscreen" content="yes" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black" />
  <meta name="format-detection" content="telephone=no" />
  <meta name="x5-fullscreen" content="true" />
  <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />

  <!-- 标题及搜索关键字 -->
  <meta name="keywords" content="火星科技,cesium,3D,GIS,marsgis,三维,地球,地图,开发,框架,系统,示例,资料,模型,离线,外包,合肥,安徽,中国" />
  <meta
    name="description"
    content="火星科技 合肥火星 合肥火星科技 合肥火星科技有限公司 leaflet leaflet框架 leaflet开发 cesium cesium开发 cesium框架 三维 地球 模型  gis marsgis 地图离线 地图开发 地图框架 地图外包 框架 开发 外包  地图离线 二维地图 三维地图 全景漫游 地理信息系统 云GIS 三维GIS GIS平台 WebGIS" />

  <link rel="shortcut icon" type="image/x-icon" href="/img/favicon/favicon.ico" />
  <title>矢量单体化编辑(GeoJson叠加) | Mars3D | 三维地图 | 火星科技 | 合肥火星科技有限公司</title>

  <!--第三方lib-->
  <script
    type="text/javascript"
    src="/lib/include-lib.js"
    libpath="/lib/"
    include="jquery,layer,toastr,font-awesome,web-icons,bootstrap,bootstrap-checkbox,layer,toastr,haoutil,mars3d,es5-widget"></script>

  <link href="/css/style.css" rel="stylesheet" />
  <style>
    .infoview div {
      margin-bottom: 5px;
    }
  </style>
</head>

<body class="dark">
  <div id="mars3dContainer" class="mars3d-container"></div>

  <div class="infoview">
    <div>
      <div class="radio radio-info radio-inline">
        <input type="radio" id="queryContent1" name="queryContent" value="1" checked />
        <label for="queryContent1">编辑模式</label>
      </div>
      <div class="radio radio-info radio-inline">
        <input type="radio" id="queryContent2" name="queryContent" value="2" />
        <label for="queryContent2">预览模式</label>
      </div>
    </div>

    <div id="editView">
      <input type="button" class="btn btn-primary" value="单体化面" onclick="drawPolygon()" />
      <input type="button" class="btn btn-danger" value="清除" onclick="deleteAll()" />

      <input id="btnSave" type="button" class="btn btn-primary" value="保存" />
      <input id="btnImpFile" type="button" class="btn btn-primary" value="打开" />
      <input id="input_plot_file" type="file" accept=".json,.geojson" style="display: none" />
    </div>
  </div>

  <!-- 箭头等标号 外部扩展 -->

  <script src="/js/common.js"></script>
  <script src="./map.js"></script>
  <script type="text/javascript">
    "use script" //开发环境建议开启严格模式

    function initUI(options) {
      $("#btnSave").click(function () {
        saveGeoJSON()
        // haoutil.file.downloadFile("单体化.json", JSON.stringify(strResult))
      })

      $("#input_plot_file").change(function (e) {
        let file = this.files[0]

        let fileName = file.name
        let fileType = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length).toLowerCase()
        if (fileType != "json") {
          layer.msg("文件类型不合法,请选择json格式标注文件！")
          clearSelectFile()
          return
        }

        openGeoJSON(file)
      })

      $("#btnImpFile").click(function () {
        $("#input_plot_file").click()
      })

      $('input:radio[name="queryContent"]').change(function () {
        let selectType = $(this).val()
        if (selectType == "1") {
          $("#editView").show()
          toBJMS()
        } else {
          $("#editView").hide()
          toYLMS()
        }
      })

      graphicLayer.on(mars3d.EventType.drawCreated, function (e) {
        showEditor(e)
      })
      // 修改了矢量数据
      graphicLayer.on(
        [mars3d.EventType.editStart, mars3d.EventType.editMovePoint, mars3d.EventType.editStyle, mars3d.EventType.editRemovePoint],
        function (e) {
          showEditor(e)
        }
      )

      // 停止编辑
      graphicLayer.on([mars3d.EventType.editStop, mars3d.EventType.removeGraphic], function (e) {
        setTimeout(() => {
          if (!graphicLayer.isEditing) {
            stopEditing()
          }
        }, 100)
      })
    }

    // 属性面板
    //附加：激活属性编辑widget【非必需，可以注释该方法内部代码】
    let timeTik

    function showEditor(e) {
      const graphic = e.graphic
      clearTimeout(timeTik)

      // if (!graphic._conventStyleJson) {
      //   graphic.options.style = graphic.toJSON().style //因为示例中的样式可能有复杂对象，需要转为单个json简单对象
      //   graphic._conventStyleJson = true //只处理一次
      // }

      let plotAttr = es5widget.getClass("widgets/plotAttr/widget.js")
      if (plotAttr && plotAttr.isActivate) {
        plotAttr.startEditing(graphic)
      } else {
        es5widget.activate({
          map: map,
          uri: "widgets/plotAttr/widget.js",
          name: "属性编辑",
          graphic: graphic
        })
      }
    }

    function stopEditing() {
      timeTik = setTimeout(function () {
        es5widget.disable("widgets/plotAttr/widget.js")
      }, 200)
    }

    function clearSelectFile() {
      if (!window.addEventListener) {
        document.getElementById("input_plot_file").outerHTML += "" //IE
      } else {
        document.getElementById("input_plot_file").value = "" //FF
      }
    }
  </script>
</body>

</html>