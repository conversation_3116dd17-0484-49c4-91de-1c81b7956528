/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as R}from"./chunk-CMAUJTC5.js";import{a as ct}from"./chunk-G76VEUE6.js";import{a as Z,b as H}from"./chunk-GR6GQHHW.js";import{a as V}from"./chunk-YHR5775C.js";import{a as U}from"./chunk-QEBTWX4I.js";import{b as ot}from"./chunk-MNWK3MJ5.js";import{a as it}from"./chunk-G6BXWUC5.js";import{a as nt}from"./chunk-IG67OOLD.js";import{a as et}from"./chunk-R7RI4F7L.js";import{b as J,c as X,d as z}from"./chunk-4NRUVMTY.js";import{f as K}from"./chunk-KRWQFK55.js";import{a as k}from"./chunk-KP2AFTT4.js";import{a as w,b as q,c as M,d as tt,e as F}from"./chunk-WAYT5ODG.js";import{a as I}from"./chunk-KSWW7DXW.js";import{e as x}from"./chunk-TMUAGJIG.js";function ft(n,i){this.positions=x(n)?n:[],this.holes=x(i)?i:[]}var st=ft;function S(){this._array=[],this._offset=0,this._length=0}Object.defineProperties(S.prototype,{length:{get:function(){return this._length}}});S.prototype.enqueue=function(n){this._array.push(n),this._length++};S.prototype.dequeue=function(){if(this._length===0)return;let n=this._array,i=this._offset,u=n[i];return n[i]=void 0,i++,i>10&&i*2>n.length&&(this._array=n.slice(i),i=0),this._offset=i,this._length--,u};S.prototype.peek=function(){if(this._length!==0)return this._array[this._offset]};S.prototype.contains=function(n){return this._array.indexOf(n)!==-1};S.prototype.clear=function(){this._array.length=this._offset=this._length=0};S.prototype.sort=function(n){this._offset>0&&(this._array=this._array.slice(this._offset),this._offset=0),this._array.sort(n)};var $=S;var b={};b.computeHierarchyPackedLength=function(n,i){let u=0,s=[n];for(;s.length>0;){let c=s.pop();if(!x(c))continue;u+=2;let a=c.positions,t=c.holes;if(x(a)&&a.length>0&&(u+=a.length*i.packedLength),x(t)){let o=t.length;for(let e=0;e<o;++e)s.push(t[e])}}return u};b.packPolygonHierarchy=function(n,i,u,s){let c=[n];for(;c.length>0;){let a=c.pop();if(!x(a))continue;let t=a.positions,o=a.holes;if(i[u++]=x(t)?t.length:0,i[u++]=x(o)?o.length:0,x(t)){let e=t.length;for(let r=0;r<e;++r,u+=s.packedLength)s.pack(t[r],i,u)}if(x(o)){let e=o.length;for(let r=0;r<e;++r)c.push(o[r])}}return u};b.unpackPolygonHierarchy=function(n,i,u){let s=n[i++],c=n[i++],a=new Array(s),t=c>0?new Array(c):void 0;for(let o=0;o<s;++o,i+=u.packedLength)a[o]=u.unpack(n,i);for(let o=0;o<c;++o)t[o]=b.unpackPolygonHierarchy(n,i,u),i=t[o].startingIndex,delete t[o].startingIndex;return{positions:a,holes:t,startingIndex:i}};var O=new M;function at(n,i,u,s){return M.subtract(i,n,O),M.multiplyByScalar(O,u/s,O),M.add(n,O,O),[O.x,O.y]}var G=new w;function gt(n,i,u,s){return w.subtract(i,n,G),w.multiplyByScalar(G,u/s,G),w.add(n,G,G),[G.x,G.y,G.z]}b.subdivideLineCount=function(n,i,u){let c=w.distance(n,i)/u,a=Math.max(0,Math.ceil(I.log2(c)));return Math.pow(2,a)};var j=new q,Q=new q,dt=new q,pt=new w,Y=new U;b.subdivideRhumbLineCount=function(n,i,u,s){let c=n.cartesianToCartographic(i,j),a=n.cartesianToCartographic(u,Q),o=new U(c,a,n).surfaceDistance/s,e=Math.max(0,Math.ceil(I.log2(o)));return Math.pow(2,e)};b.subdivideTexcoordLine=function(n,i,u,s,c,a){let t=b.subdivideLineCount(u,s,c),o=M.distance(n,i),e=o/t,r=a;r.length=t*2;let l=0;for(let h=0;h<t;h++){let f=at(n,i,h*e,o);r[l++]=f[0],r[l++]=f[1]}return r};b.subdivideLine=function(n,i,u,s){let c=b.subdivideLineCount(n,i,u),a=w.distance(n,i),t=a/c;x(s)||(s=[]);let o=s;o.length=c*3;let e=0;for(let r=0;r<c;r++){let l=gt(n,i,r*t,a);o[e++]=l[0],o[e++]=l[1],o[e++]=l[2]}return o};b.subdivideTexcoordRhumbLine=function(n,i,u,s,c,a,t){let o=u.cartesianToCartographic(s,j),e=u.cartesianToCartographic(c,Q);Y.setEndPoints(o,e);let r=Y.surfaceDistance/a,l=Math.max(0,Math.ceil(I.log2(r))),h=Math.pow(2,l),f=M.distance(n,i),d=f/h,m=t;m.length=h*2;let p=0;for(let g=0;g<h;g++){let y=at(n,i,g*d,f);m[p++]=y[0],m[p++]=y[1]}return m};b.subdivideRhumbLine=function(n,i,u,s,c){let a=n.cartesianToCartographic(i,j),t=n.cartesianToCartographic(u,Q),o=new U(a,t,n);if(x(c)||(c=[]),o.surfaceDistance<=s)return c.length=3,c[0]=i.x,c[1]=i.y,c[2]=i.z,c;let e=o.surfaceDistance/s,r=Math.max(0,Math.ceil(I.log2(e))),l=Math.pow(2,r),h=o.surfaceDistance/l,f=c;f.length=l*3;let d=0;for(let m=0;m<l;m++){let p=o.interpolateUsingSurfaceDistance(m*h,dt),g=n.cartographicToCartesian(p,pt);f[d++]=g.x,f[d++]=g.y,f[d++]=g.z}return f};var mt=new w,yt=new w,xt=new w,wt=new w;b.scaleToGeodeticHeightExtruded=function(n,i,u,s,c){s=s??tt.default;let a=mt,t=yt,o=xt,e=wt;if(x(n)&&x(n.attributes)&&x(n.attributes.position)){let r=n.attributes.position.values,l=r.length/2;for(let h=0;h<l;h+=3)w.fromArray(r,h,o),s.geodeticSurfaceNormal(o,a),e=s.scaleToGeodeticSurface(o,e),t=w.multiplyByScalar(a,u,t),t=w.add(e,t,t),r[h+l]=t.x,r[h+1+l]=t.y,r[h+2+l]=t.z,c&&(e=w.clone(o,e)),t=w.multiplyByScalar(a,i,t),t=w.add(e,t,t),r[h]=t.x,r[h+1]=t.y,r[h+2]=t.z}return n};b.polygonOutlinesFromHierarchy=function(n,i,u){let s=[],c=new $;c.enqueue(n);let a,t,o;for(;c.length!==0;){let e=c.dequeue(),r=e.positions;if(i)for(o=r.length,a=0;a<o;a++)u.scaleToGeodeticSurface(r[a],r[a]);if(r=V(r,w.equalsEpsilon,!0),r.length<3)continue;let l=e.holes?e.holes.length:0;for(a=0;a<l;a++){let h=e.holes[a],f=h.positions;if(i)for(o=f.length,t=0;t<o;++t)u.scaleToGeodeticSurface(f[t],f[t]);if(f=V(f,w.equalsEpsilon,!0),f.length<3)continue;s.push(f);let d=0;for(x(h.holes)&&(d=h.holes.length),t=0;t<d;t++)c.enqueue(h.holes[t])}s.push(r)}return s};var bt=new q;function Lt(n,i,u){let s=u.cartesianToCartographic(n,j),c=u.cartesianToCartographic(i,Q);if(Math.sign(s.latitude)===Math.sign(c.latitude))return;Y.setEndPoints(s,c);let a=Y.findIntersectionWithLatitude(0,bt);if(!x(a))return;let t=Math.min(s.longitude,c.longitude),o=Math.max(s.longitude,c.longitude);if(Math.abs(o-t)>I.PI){let e=t;t=o,o=e}if(!(a.longitude<t||a.longitude>o))return u.cartographicToCartesian(a)}function Tt(n,i,u,s){if(s===R.RHUMB)return Lt(n,i,u);let c=ot.lineSegmentPlane(n,i,it.ORIGIN_XY_PLANE);if(x(c))return u.scaleToGeodeticSurface(c,c)}var Et=new q;function vt(n,i,u){let s=[],c,a,t,o,e,r=0;for(;r<n.length;){c=n[r],a=n[(r+1)%n.length],t=I.sign(c.z),o=I.sign(a.z);let l=h=>i.cartesianToCartographic(h,Et).longitude;if(t===0)s.push({position:r,type:t,visited:!1,next:o,theta:l(c)});else if(o!==0){if(e=Tt(c,a,i,u),++r,!x(e))continue;n.splice(r,0,e),s.push({position:r,type:t,visited:!1,next:o,theta:l(e)})}++r}return s}function ht(n,i,u,s,c,a,t){let o=[],e=a,r=h=>f=>f.position===h,l=[];do{let h=u[e];o.push(h);let f=s.findIndex(r(e)),d=s[f];if(!x(d)){++e;continue}let{visited:m,type:p,next:g}=d;if(d.visited=!0,p===0){if(g===0){let C=s[f-(t?1:-1)];if(C?.position===e+1)C.visited=!0;else{++e;continue}}if(!m&&t&&g>0||a===e&&!t&&g<0){++e;continue}}if(!(t?p>=0:p<=0)){++e;continue}m||l.push(e);let L=f+(t?1:-1),_=s[L];if(!x(_)){++e;continue}e=_.position}while(e<u.length&&e>=0&&e!==a&&o.length<u.length);n.splice(i,c,o);for(let h of l)i=ht(n,++i,u,s,0,h,!t);return i}b.splitPolygonsOnEquator=function(n,i,u,s){x(s)||(s=[]),s.splice(0,0,...n),s.length=n.length;let c=0;for(;c<s.length;){let a=s[c],t=a.slice();if(a.length<3){s[c]=t,++c;continue}let o=vt(t,i,u);if(t.length===a.length||o.length<=1){s[c]=t,++c;continue}o.sort((r,l)=>r.theta-l.theta);let e=t[0].z>=0;c=ht(s,c,t,o,1,0,e)}return s};b.polygonsFromHierarchy=function(n,i,u,s,c,a){let t=[],o=[],e=new $;e.enqueue(n);let r=x(a);for(;e.length!==0;){let l=e.dequeue(),h=l.positions,f=l.holes,d,m;if(s)for(m=h.length,d=0;d<m;d++)c.scaleToGeodeticSurface(h[d],h[d]);if(i||(h=V(h,w.equalsEpsilon,!0)),h.length<3)continue;let p=u(h);if(!x(p))continue;let g=[],y=H.computeWindingOrder2D(p);if(y===Z.CLOCKWISE&&(p.reverse(),h=h.slice().reverse()),r){r=!1;let P=[h];if(P=a(P,P),P.length>1){for(let v of P)e.enqueue(new st(v,f));continue}}let L=h.slice(),_=x(f)?f.length:0,C=[],T;for(d=0;d<_;d++){let P=f[d],v=P.positions;if(s)for(m=v.length,T=0;T<m;++T)c.scaleToGeodeticSurface(v[T],v[T]);if(i||(v=V(v,w.equalsEpsilon,!0)),v.length<3)continue;let E=u(v);if(!x(E))continue;y=H.computeWindingOrder2D(E),y===Z.CLOCKWISE&&(E.reverse(),v=v.slice().reverse()),C.push(v),g.push(L.length),L=L.concat(v),p=p.concat(E);let D=0;for(x(P.holes)&&(D=P.holes.length),T=0;T<D;T++)e.enqueue(P.holes[T])}t.push({outerRing:h,holes:C}),o.push({positions:L,positions2D:p,holes:g})}return{hierarchy:t,polygons:o}};var Ct=new M,Pt=new w,Dt=new K,It=new F;b.computeBoundingRectangle=function(n,i,u,s,c){let a=K.fromAxisAngle(n,s,Dt),t=F.fromQuaternion(a,It),o=Number.POSITIVE_INFINITY,e=Number.NEGATIVE_INFINITY,r=Number.POSITIVE_INFINITY,l=Number.NEGATIVE_INFINITY,h=u.length;for(let f=0;f<h;++f){let d=w.clone(u[f],Pt);F.multiplyByVector(t,d,d);let m=i(d,Ct);x(m)&&(o=Math.min(o,m.x),e=Math.max(e,m.x),r=Math.min(r,m.y),l=Math.max(l,m.y))}return c.x=o,c.y=r,c.width=e-o,c.height=l-r,c};b.createGeometryFromPositions=function(n,i,u,s,c,a,t){let o=H.triangulate(i.positions2D,i.holes);o.length<3&&(o=[0,1,2]);let e=i.positions,r=x(u),l=r?u.positions:void 0;if(c){let h=e.length,f=new Array(h*3),d=0;for(let g=0;g<h;g++){let y=e[g];f[d++]=y.x,f[d++]=y.y,f[d++]=y.z}let m={attributes:{position:new z({componentDatatype:k.DOUBLE,componentsPerAttribute:3,values:f})},indices:o,primitiveType:J.TRIANGLES};r&&(m.attributes.st=new z({componentDatatype:k.FLOAT,componentsPerAttribute:2,values:M.packArray(l)}));let p=new X(m);return a.normal?ct.computeNormal(p):p}if(t===R.GEODESIC)return H.computeSubdivision(n,e,o,l,s);if(t===R.RHUMB)return H.computeRhumbLineSubdivision(n,e,o,l,s)};var rt=[],ut=[],_t=new w,At=new w;b.computeWallGeometry=function(n,i,u,s,c,a){let t,o,e,r,l,h,f,d,m,p=n.length,g=0,y=0,L=x(i),_=L?i.positions:void 0;if(c)for(o=p*3*2,t=new Array(o*2),L&&(m=p*2*2,d=new Array(m*2)),e=0;e<p;e++)r=n[e],l=n[(e+1)%p],t[g]=t[g+o]=r.x,++g,t[g]=t[g+o]=r.y,++g,t[g]=t[g+o]=r.z,++g,t[g]=t[g+o]=l.x,++g,t[g]=t[g+o]=l.y,++g,t[g]=t[g+o]=l.z,++g,L&&(h=_[e],f=_[(e+1)%p],d[y]=d[y+m]=h.x,++y,d[y]=d[y+m]=h.y,++y,d[y]=d[y+m]=f.x,++y,d[y]=d[y+m]=f.y,++y);else{let E=I.chordLength(s,u.maximumRadius),D=0;if(a===R.GEODESIC)for(e=0;e<p;e++)D+=b.subdivideLineCount(n[e],n[(e+1)%p],E);else if(a===R.RHUMB)for(e=0;e<p;e++)D+=b.subdivideRhumbLineCount(u,n[e],n[(e+1)%p],E);for(o=(D+p)*3,t=new Array(o*2),L&&(m=(D+p)*2,d=new Array(m*2)),e=0;e<p;e++){r=n[e],l=n[(e+1)%p];let A,N;L&&(h=_[e],f=_[(e+1)%p]),a===R.GEODESIC?(A=b.subdivideLine(r,l,E,ut),L&&(N=b.subdivideTexcoordLine(h,f,r,l,E,rt))):a===R.RHUMB&&(A=b.subdivideRhumbLine(u,r,l,E,ut),L&&(N=b.subdivideTexcoordRhumbLine(h,f,u,r,l,E,rt)));let lt=A.length;for(let B=0;B<lt;++B,++g)t[g]=A[B],t[g+o]=A[B];if(t[g]=l.x,t[g+o]=l.x,++g,t[g]=l.y,t[g+o]=l.y,++g,t[g]=l.z,t[g+o]=l.z,++g,L){let B=N.length;for(let W=0;W<B;++W,++y)d[y]=N[W],d[y+m]=N[W];d[y]=f.x,d[y+m]=f.x,++y,d[y]=f.y,d[y+m]=f.y,++y}}}p=t.length;let C=nt.createTypedArray(p/3,p-n.length*6),T=0;for(p/=6,e=0;e<p;e++){let E=e,D=E+1,A=E+p,N=A+1;r=w.fromArray(t,E*3,_t),l=w.fromArray(t,D*3,At),!w.equalsEpsilon(r,l,I.EPSILON10,I.EPSILON10)&&(C[T++]=E,C[T++]=A,C[T++]=D,C[T++]=D,C[T++]=A,C[T++]=N)}let P={attributes:new et({position:new z({componentDatatype:k.DOUBLE,componentsPerAttribute:3,values:t})}),indices:C,primitiveType:J.TRIANGLES};return L&&(P.attributes.st=new z({componentDatatype:k.FLOAT,componentsPerAttribute:2,values:d})),new X(P)};var ie=b;export{ie as a};
