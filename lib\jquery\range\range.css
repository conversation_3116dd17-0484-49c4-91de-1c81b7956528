input[type=range] {
	-webkit-appearance: none; 
    /*margin-left: 10px;*/
	display: initial;
	border-radius: 5px;
	/*这个属性设置使填充进度条时的图形为圆角*/
	background: -webkit-linear-gradient(#059CFA, #059CFA) no-repeat;
	background-size: 0% 100%;
    padding: 0px;
}

input[type=range]::-webkit-slider-thumb {
	-webkit-appearance: none;
}

input[type=range]::-webkit-slider-runnable-track {
	height: 6px;
	border-radius: 5px;
	/*将轨道设为圆角的*/
	box-shadow: 0 1px 1px #def3f8, inset 0 .125em .125em #0d1112;
	/*轨道内置阴影效果*/
}

input[type=range]:focus {
	outline: none;
}

input[type=range]::-webkit-slider-thumb {
	-webkit-appearance: none;
	height: 10px;
	width: 10px;
	margin-top: -2px;
	/*使滑块超出轨道部分的偏移量相等*/
	background: #ffffff;
	border-radius: 50%;
	/*外观设置为圆形*/
	border: solid 0.125em rgba(205, 224, 230, 0.5);
	/*设置边框*/
	box-shadow: 0 .125em .125em #3b4547;
	/*添加底部阴影*/
}