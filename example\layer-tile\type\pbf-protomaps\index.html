<!-- 2017-12-4 14:24:10 | 修改 木遥（微信:  http://marsgis.cn/weixin.html ） -->
<!DOCTYPE html>
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta name="viewport"
    content="width=device-width,initial-scale=1,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0" />
  <meta name="apple-touch-fullscreen" content="yes" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black" />
  <meta name="format-detection" content="telephone=no" />
  <meta name="x5-fullscreen" content="true" />
  <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />

  <!-- 标题及搜索关键字 -->
  <meta name="keywords" content="火星科技,cesium,3D,GIS,marsgis,三维,地球,地图,开发,框架,系统,示例,资料,模型,离线,外包,合肥,安徽,中国" />
  <meta name="description"
    content="火星科技 合肥火星 合肥火星科技 合肥火星科技有限公司 leaflet leaflet框架 leaflet开发 cesium cesium开发 cesium框架 三维 地球 模型  gis marsgis 地图离线 地图开发 地图框架 地图外包 框架 开发 外包  地图离线 二维地图 三维地图 全景漫游 地理信息系统 云GIS 三维GIS GIS平台 WebGIS" />

  <link rel="shortcut icon" type="image/x-icon" href="/img/favicon/favicon.ico" />
  <title>EPSG3857坐标系 | Mars3D | 三维地图 | 火星科技 | 合肥火星科技有限公司</title>

  <script type="text/javascript" src="/lib/include-lib.js" libpath="/lib/"
    include="jquery,layer,toastr,font-awesome,bootstrap,bootstrap-slider,layer,haoutil,mars3d,cesium-pbf-protomaps"></script>

  <link href="/css/style.css" rel="stylesheet" />
  <style>
    .flatTable {
      border: 1px solid rgba(255, 255, 255, 0.5);
    }

    .flatTable tr td,
    th {
      border: 1px solid rgba(255, 255, 255, 0.5);
      text-align: center;
    }

    .title {
      text-align: center;
      font-size: 14px;
    }
  </style>
</head>

<body class="dark">
  <div id="mars3dContainer" class="mars3d-container">
    <div class="infoview">
      <input id="showTable" class="styled" type="checkbox" checked value="显示列表">显示列表</input>

      <!-- 操作单个记录 -->
      <div id="layerTable">
        <table class="mars-table flatTable">
          <thead>
            <th>名称</th>
            <th>编辑</th>
          </thead>
          <tbody id="tbPoly"></tbody>
        </table>

        <div id="showEditor">
          <h4 class="title" id="layerName">底图参数</h4>
          <table class="mars-table">
            <tbody>
              <tr>
                <td>状态</td>
                <td>
                  <input id="show" class="styled" type="checkbox" checked value="显示">显示</input>
                </td>
              </tr>
              <tr>
                <td>亮度</td>
                <td>
                  <input id="brightness" title="亮度" />
                </td>
              </tr>
              <tr>
                <td>对比度</td>
                <td>
                  <input id="contrast" title="对比度" />
                </td>
              </tr>
              <tr>
                <td>色彩</td>
                <td>
                  <input id="hue" title="色彩" />
                </td>
              </tr>
              <tr>
                <td>饱和度</td>
                <td>
                  <input id="saturation" title="饱和度" />
                </td>
              </tr>
              <tr>
                <td>伽马值</td>
                <td>
                  <input id="gamma" title="伽马值" />
                </td>
              </tr>
              <tr>
                <td>透明度</td>
                <td>
                  <input id="opacity" title="透明度" />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <script src="/js/common.js"></script>
    <script src="./map.js"></script>
    <script src="/js/tile_layer_state.js"></script>
    <script type="text/javascript">
      "use script" //开发环境建议开启严格模式

      let $table
      const tileLayerList = []
      let thisLayer = null // 选中的图层

      function initUI() {
        // 是否显示表格
        $("#showTable").change(function () {
          let val = $(this).is(":checked")
          // 隐藏显示表格
          if (val) {
            $("#layerTable").show()
          } else {
            $("#layerTable").hide()
          }
        })

        initTable()
      }
    </script>
</body>

</html>
