/**
 * Mars3D平台插件,支持气象 风向图 功能插件  mars3d-wind
 *
 * 版本信息：v3.6.1
 * 编译日期：2023-08-14 21:01:55
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：免费公开版 ，2023-03-17
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-wind"] = {}));
})(this, (function (exports) {
'use strict';const _0x5e819d=_0x537a;(function(_0x31b8ab,_0x70c04d){const _0x1ed75c=_0x537a,_0x3a6cb1=_0x31b8ab();while(!![]){try{const _0x4d118e=-parseInt(_0x1ed75c(0x180))/0x1*(-parseInt(_0x1ed75c(0x1a0))/0x2)+-parseInt(_0x1ed75c(0x18f))/0x3+parseInt(_0x1ed75c(0x193))/0x4*(parseInt(_0x1ed75c(0x190))/0x5)+-parseInt(_0x1ed75c(0x17e))/0x6*(-parseInt(_0x1ed75c(0x196))/0x7)+-parseInt(_0x1ed75c(0x1a3))/0x8*(-parseInt(_0x1ed75c(0x198))/0x9)+-parseInt(_0x1ed75c(0x185))/0xa*(-parseInt(_0x1ed75c(0x197))/0xb)+-parseInt(_0x1ed75c(0x19c))/0xc;if(_0x4d118e===_0x70c04d)break;else _0x3a6cb1['push'](_0x3a6cb1['shift']());}catch(_0x89e314){_0x3a6cb1['push'](_0x3a6cb1['shift']());}}}(_0x6287,0x34b2c));function _0x537a(_0x5505bb,_0x59e38b){const _0x62870f=_0x6287();return _0x537a=function(_0x537a01,_0x2dc1b7){_0x537a01=_0x537a01-0x17e;let _0x1cd55a=_0x62870f[_0x537a01];return _0x1cd55a;},_0x537a(_0x5505bb,_0x59e38b);}function _0x6287(){const _0x83a214=['33938MtDWIm','getUVByXY','setOptions','18272egTfSh','particles','getParticles','random','push','115572uglyFO','grid','11Ouygme','xmin','particlesNumber','ymax','age','13150BVOFes','cols','destroy','rows','ymin','defineProperty','_speedRate','toGridXY','reverseY','lat','138348nawBKG','1343130NruaHn','lng','vdata','4qQZumV','round','_calc_speedRate','70OnatPz','649FnzjIZ','603NjsTyV','length','_calcUV','tlat','7398600GUWjXQ','maxAge','speedRate','tlng'];_0x6287=function(){return _0x83a214;};return _0x6287();}class CanvasParticle{constructor(){const _0x8ef4=_0x537a;this['lng']=null,this[_0x8ef4(0x18e)]=null,this[_0x8ef4(0x19f)]=null,this['tlat']=null,this[_0x8ef4(0x184)]=null,this['speed']=null;}[_0x5e819d(0x187)](){for(const _0x4da5d8 in this){delete this[_0x4da5d8];}}}class CanvasWindField{constructor(_0x3e9853){const _0x30bc7e=_0x5e819d;this[_0x30bc7e(0x1a2)](_0x3e9853);}get[_0x5e819d(0x19e)](){const _0xfa0d10=_0x5e819d;return this[_0xfa0d10(0x18b)];}set['speedRate'](_0x337bae){const _0x5cad3b=_0x5e819d;this[_0x5cad3b(0x18b)]=(0x64-(_0x337bae>0x63?0x63:_0x337bae))*0x64,this['_calc_speedRate']=[(this['xmax']-this['xmin'])/this['_speedRate'],(this['ymax']-this['ymin'])/this['_speedRate']];}get['maxAge'](){return this['_maxAge'];}set[_0x5e819d(0x19d)](_0x1223ce){this['_maxAge']=_0x1223ce;}['setOptions'](_0xdddc84){const _0x2a2b13=_0x5e819d;this['options']=_0xdddc84,this['maxAge']=_0xdddc84['maxAge']||0x78,this[_0x2a2b13(0x19e)]=_0xdddc84['speedRate']||0x32,this['particles']=[];const _0x4e0e55=_0xdddc84[_0x2a2b13(0x182)]||0x1000;for(let _0x31d651=0x0;_0x31d651<_0x4e0e55;_0x31d651++){const _0x29b81b=this['_randomParticle'](new CanvasParticle());this['particles']['push'](_0x29b81b);}}['setDate'](_0x3e6a90){const _0x5019a0=_0x5e819d;this['rows']=_0x3e6a90[_0x5019a0(0x188)],this['cols']=_0x3e6a90['cols'],this['xmin']=_0x3e6a90[_0x5019a0(0x181)],this['xmax']=_0x3e6a90['xmax'],this[_0x5019a0(0x189)]=_0x3e6a90['ymin'],this[_0x5019a0(0x183)]=_0x3e6a90['ymax'],this[_0x5019a0(0x17f)]=[];const _0x1bb4ec=_0x3e6a90['udata'],_0x58c97f=_0x3e6a90[_0x5019a0(0x192)];let _0x33eff4=![];_0x1bb4ec[_0x5019a0(0x199)]===this['rows']&&_0x1bb4ec[0x0]['length']===this[_0x5019a0(0x186)]&&(_0x33eff4=!![]);let _0x18c7cb=0x0,_0x108122=null,_0x3fb8a4=null;for(let _0x55ad89=0x0;_0x55ad89<this['rows'];_0x55ad89++){_0x108122=[];for(let _0x4fad41=0x0;_0x4fad41<this['cols'];_0x4fad41++,_0x18c7cb++){_0x33eff4?_0x3fb8a4=this[_0x5019a0(0x19a)](_0x1bb4ec[_0x55ad89][_0x4fad41],_0x58c97f[_0x55ad89][_0x4fad41]):_0x3fb8a4=this['_calcUV'](_0x1bb4ec[_0x18c7cb],_0x58c97f[_0x18c7cb]),_0x108122[_0x5019a0(0x1a7)](_0x3fb8a4);}this['grid'][_0x5019a0(0x1a7)](_0x108122);}this['options'][_0x5019a0(0x18d)]&&this['grid']['reverse']();}['clear'](){delete this['rows'],delete this['cols'],delete this['xmin'],delete this['xmax'],delete this['ymin'],delete this['ymax'],delete this['grid'],delete this['particles'];}['toGridXY'](_0x476bb4,_0x4d127e){const _0x288665=_0x5e819d,_0x11f5dd=(_0x476bb4-this[_0x288665(0x181)])/(this['xmax']-this['xmin'])*(this['cols']-0x1),_0x2e2633=(this['ymax']-_0x4d127e)/(this['ymax']-this[_0x288665(0x189)])*(this[_0x288665(0x188)]-0x1);return[_0x11f5dd,_0x2e2633];}['getUVByXY'](_0x2c5e01,_0x246751){const _0x25282=_0x5e819d;if(_0x2c5e01<0x0||_0x2c5e01>=this[_0x25282(0x186)]||_0x246751>=this['rows'])return[0x0,0x0,0x0];const _0x532226=Math['floor'](_0x2c5e01),_0x5ede43=Math['floor'](_0x246751);if(_0x532226===_0x2c5e01&&_0x5ede43===_0x246751)return this['grid'][_0x246751][_0x2c5e01];const _0x2e2a42=_0x532226+0x1,_0x540b86=_0x5ede43+0x1,_0x3038af=this['getUVByXY'](_0x532226,_0x5ede43),_0x573cdd=this['getUVByXY'](_0x2e2a42,_0x5ede43),_0x298898=this[_0x25282(0x1a1)](_0x532226,_0x540b86),_0x5adc85=this['getUVByXY'](_0x2e2a42,_0x540b86);let _0x25d482=null;try{_0x25d482=this['_bilinearInterpolation'](_0x2c5e01-_0x532226,_0x246751-_0x5ede43,_0x3038af,_0x573cdd,_0x298898,_0x5adc85);}catch(_0x55e806){console['log'](_0x2c5e01,_0x246751);}return _0x25d482;}['_bilinearInterpolation'](_0x1eea95,_0x4c4cd1,_0xc4a3da,_0x122579,_0x264628,_0x32c7ea){const _0xf679ac=0x1-_0x1eea95,_0x4d41d7=0x1-_0x4c4cd1,_0x217699=_0xf679ac*_0x4d41d7,_0x29c278=_0x1eea95*_0x4d41d7,_0x343a08=_0xf679ac*_0x4c4cd1,_0x4540f8=_0x1eea95*_0x4c4cd1,_0x2a4eb8=_0xc4a3da[0x0]*_0x217699+_0x122579[0x0]*_0x29c278+_0x264628[0x0]*_0x343a08+_0x32c7ea[0x0]*_0x4540f8,_0x455093=_0xc4a3da[0x1]*_0x217699+_0x122579[0x1]*_0x29c278+_0x264628[0x1]*_0x343a08+_0x32c7ea[0x1]*_0x4540f8;return this['_calcUV'](_0x2a4eb8,_0x455093);}['_calcUV'](_0x4fa3f5,_0x52e9eb){return[+_0x4fa3f5,+_0x52e9eb,Math['sqrt'](_0x4fa3f5*_0x4fa3f5+_0x52e9eb*_0x52e9eb)];}['getUVByPoint'](_0xeca7a9,_0x3307da){const _0x5d1a98=_0x5e819d;if(!this['isInExtent'](_0xeca7a9,_0x3307da))return null;const _0x575c07=this[_0x5d1a98(0x18c)](_0xeca7a9,_0x3307da),_0x109ac5=this['getUVByXY'](_0x575c07[0x0],_0x575c07[0x1]);return _0x109ac5;}['isInExtent'](_0x9c75c5,_0x547aea){return _0x9c75c5>=this['xmin']&&_0x9c75c5<=this['xmax']&&_0x547aea>=this['ymin']&&_0x547aea<=this['ymax']?!![]:![];}['getRandomLatLng'](){const _0x200d2a=_0x5e819d,_0x486c14=fRandomByfloat(this[_0x200d2a(0x181)],this['xmax']),_0x3bf64f=fRandomByfloat(this['ymin'],this[_0x200d2a(0x183)]);return{'lat':_0x3bf64f,'lng':_0x486c14};}[_0x5e819d(0x1a5)](){const _0x13c118=_0x5e819d;let _0x342cc0,_0x366b8f,_0x4417c4;for(let _0x2c0aaf=0x0,_0x51ee00=this['particles']['length'];_0x2c0aaf<_0x51ee00;_0x2c0aaf++){let _0xfcdcde=this[_0x13c118(0x1a4)][_0x2c0aaf];_0xfcdcde['age']<=0x0&&(_0xfcdcde=this['_randomParticle'](_0xfcdcde));if(_0xfcdcde['age']>0x0){const _0x5d4ea6=_0xfcdcde['tlng'],_0x3bd3c8=_0xfcdcde[_0x13c118(0x19b)];_0x4417c4=this['getUVByPoint'](_0x5d4ea6,_0x3bd3c8),_0x4417c4?(_0x342cc0=_0x5d4ea6+this['_calc_speedRate'][0x0]*_0x4417c4[0x0],_0x366b8f=_0x3bd3c8+this['_calc_speedRate'][0x1]*_0x4417c4[0x1],_0xfcdcde[_0x13c118(0x191)]=_0x5d4ea6,_0xfcdcde['lat']=_0x3bd3c8,_0xfcdcde['tlng']=_0x342cc0,_0xfcdcde['tlat']=_0x366b8f,_0xfcdcde['speed']=_0x4417c4[0x2],_0xfcdcde[_0x13c118(0x184)]--):_0xfcdcde['age']=0x0;}}return this['particles'];}['_randomParticle'](_0x14cb4e){const _0x5a6e53=_0x5e819d;let _0x4b91c8,_0x40610d;for(let _0x2671e7=0x0;_0x2671e7<0x1e;_0x2671e7++){_0x4b91c8=this['getRandomLatLng'](),_0x40610d=this['getUVByPoint'](_0x4b91c8['lng'],_0x4b91c8['lat']);if(_0x40610d&&_0x40610d[0x2]>0x0)break;}if(!_0x40610d)return _0x14cb4e;const _0x541e35=_0x4b91c8['lng']+this[_0x5a6e53(0x195)][0x0]*_0x40610d[0x0],_0x43910a=_0x4b91c8['lat']+this['_calc_speedRate'][0x1]*_0x40610d[0x1];return _0x14cb4e['lng']=_0x4b91c8[_0x5a6e53(0x191)],_0x14cb4e['lat']=_0x4b91c8[_0x5a6e53(0x18e)],_0x14cb4e['tlng']=_0x541e35,_0x14cb4e['tlat']=_0x43910a,_0x14cb4e['age']=Math[_0x5a6e53(0x194)](Math[_0x5a6e53(0x1a6)]()*this['maxAge']),_0x14cb4e['speed']=_0x40610d[0x2],_0x14cb4e;}[_0x5e819d(0x187)](){for(const _0xae7575 in this){delete this[_0xae7575];}}}function fRandomByfloat(_0x362eef,_0x116ba7){return _0x362eef+Math['random']()*(_0x116ba7-_0x362eef);}exports['CanvasWindField']=CanvasWindField,Object[_0x5e819d(0x18a)](exports,'__esModule',{'value':!![]});
}));
