/**
 * Mars3D平台插件,结合echarts可视化功能插件  mars3d-echarts
 *
 * 版本信息：v3.10.3
 * 编译日期：2025-08-17 12:11
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：火星科技免费公开版 ，2025-07-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d')), (window.echarts || require('echarts'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d', 'echarts'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-echarts"] = {}, global.mars3d, global.echarts));
})(this, (function (exports, mars3d, echarts) { 
'use strict';(function(_0x22d0dd,_0xa2cfbb){const _0x1de792={_0x12e7ca:0x151,_0x137f72:0x172,_0x482482:0x137,_0x3f8fc8:0x12b,_0x3a78d7:0x168,_0x84cf95:0x189,_0x33a345:0x179,_0x268f97:0x1a3,_0x53493e:0x44f,_0x577c63:0x172,_0xc0d44d:0x135,_0x429681:0x133};function _0x5d8a03(_0x367db5,_0xf5955c){return _0x190a(_0x367db5- -0x54,_0xf5955c);}const _0x183234=_0x22d0dd();function _0x33f3d8(_0x4d6372,_0x19e197){return _0x190a(_0x4d6372-0x29a,_0x19e197);}while(!![]){try{const _0x178abf=-parseInt(_0x5d8a03(_0x1de792._0x12e7ca,_0x1de792._0x137f72))/0x1*(-parseInt(_0x5d8a03(_0x1de792._0x482482,_0x1de792._0x3f8fc8))/0x2)+-parseInt(_0x5d8a03(_0x1de792._0x3a78d7,0x195))/0x3+-parseInt(_0x5d8a03(0x170,0x15a))/0x4+parseInt(_0x5d8a03(0x189,0x15d))/0x5+-parseInt(_0x5d8a03(0x18b,_0x1de792._0x84cf95))/0x6*(-parseInt(_0x5d8a03(_0x1de792._0x33a345,_0x1de792._0x268f97))/0x7)+parseInt(_0x33f3d8(0x46a,_0x1de792._0x53493e))/0x8*(parseInt(_0x5d8a03(_0x1de792._0x577c63,0x196))/0x9)+parseInt(_0x33f3d8(0x45d,0x476))/0xa*(-parseInt(_0x5d8a03(_0x1de792._0xc0d44d,_0x1de792._0x429681))/0xb);if(_0x178abf===_0xa2cfbb)break;else _0x183234['push'](_0x183234['shift']());}catch(_0x33d207){_0x183234['push'](_0x183234['shift']());}}}(_0x4158,0x2c997));function _interopNamespace(_0x4d37d8){const _0x4932e8={_0x2270f1:0x19d},_0x12e1b3={_0x541e73:0x1f7},_0x243d8d={_0x3a358e:0x22c};if(_0x4d37d8&&_0x4d37d8[_0x22ab88(0x421,0x3fb)])return _0x4d37d8;var _0x54e7c1=Object[_0x22ab88(0x3ce,0x3b6)](null);function _0x22ab88(_0x1341ac,_0x5a107d){return _0x190a(_0x5a107d-_0x243d8d._0x3a358e,_0x1341ac);}function _0x189321(_0x389ada,_0xf4f9c3){return _0x190a(_0xf4f9c3-_0x12e1b3._0x541e73,_0x389ada);}return _0x4d37d8&&Object['keys'](_0x4d37d8)['forEach'](function(_0x3e0ceb){function _0x52bc04(_0x1f22ff,_0x5a0ecd){return _0x189321(_0x5a0ecd,_0x1f22ff- -0x208);}if(_0x3e0ceb!=='default'){var _0x269823=Object['getOwnPropertyDescriptor'](_0x4d37d8,_0x3e0ceb);Object[_0x52bc04(0x1b8,_0x4932e8._0x2270f1)](_0x54e7c1,_0x3e0ceb,_0x269823['get']?_0x269823:{'enumerable':!![],'get':function(){return _0x4d37d8[_0x3e0ceb];}});}}),_0x54e7c1['default']=_0x4d37d8,_0x54e7c1;}var mars3d__namespace=_interopNamespace(mars3d),echarts__namespace=_interopNamespace(echarts);const Cesium$1=mars3d__namespace[_0x5993af(0x331,0x355)];class CompositeCoordinateSystem{constructor(_0x1c5fe2,_0x342b34){const _0x67e8f5={_0x2cd422:0x19a};this['_mars3d_scene']=_0x1c5fe2,this['dimensions']=['lng','lat'],this[_0x53a8c9(0x506,0x4f2)]=[0x0,0x0];function _0x2a06b2(_0x18199c,_0x23c8dd){return _0x5993af(_0x23c8dd- -0x4d0,_0x18199c);}function _0x53a8c9(_0x3e3dca,_0x5e5516){return _0x5993af(_0x3e3dca-_0x67e8f5._0x2cd422,_0x5e5516);}this[_0x53a8c9(0x4f9,0x4da)]=_0x342b34;}['setMapOffset'](_0xebf0b4){this['_mapOffset']=_0xebf0b4;}['getBMap'](){return this['_mars3d_scene'];}['dataToPoint'](_0x320d90){const _0x55b67a={_0x2ab956:0x3b2,_0x352c93:0x13e,_0x7e2c07:0x172,_0x259add:0x157,_0x2e3cd9:0x13f,_0x943e78:0x123,_0x130110:0x14d,_0x37cd34:0x145,_0x553da1:0x190,_0x148fae:0x110,_0x29df1e:0x165},_0x55fddd={_0x59b376:0x80},_0x9a1703=this[_0x4cfb24(_0x55b67a._0x2ab956,0x391)],_0xb8e2d0=[NaN,NaN];let _0x3803e5=_0x9a1703[_0x38ea2b(-_0x55b67a._0x352c93,-0x137)];_0x9a1703[_0x38ea2b(-_0x55b67a._0x7e2c07,-_0x55b67a._0x259add)]&&(_0x3803e5=_0x9a1703['getHeight'](Cesium$1['Cartographic']['fromDegrees'](_0x320d90[0x0],_0x320d90[0x1])));const _0x4a473a=Cesium$1['Cartesian3']['fromDegrees'](_0x320d90[0x0],_0x320d90[0x1],_0x3803e5);function _0x4cfb24(_0x3c22df,_0xdcea4d){return _0x5993af(_0x3c22df-_0x55fddd._0x59b376,_0xdcea4d);}if(!_0x4a473a)return _0xb8e2d0;function _0x38ea2b(_0x33477f,_0x1f0f1a){return _0x5993af(_0x1f0f1a- -0x4a9,_0x33477f);}const _0x3bd5e7=mars3d__namespace['PointTrans']['toWindowCoordinates'](_0x9a1703,_0x4a473a);if(!_0x3bd5e7)return _0xb8e2d0;if(_0x9a1703[_0x38ea2b(-0x131,-_0x55b67a._0x2e3cd9)]&&_0x9a1703[_0x38ea2b(-_0x55b67a._0x943e78,-_0x55b67a._0x130110)]===Cesium$1['SceneMode']['SCENE3D']){const _0x5dada6=new Cesium$1[(_0x38ea2b(-0x124,-_0x55b67a._0x37cd34))](_0x9a1703[_0x38ea2b(-_0x55b67a._0x553da1,-0x16b)][_0x38ea2b(-_0x55b67a._0x148fae,-0x11d)],_0x9a1703[_0x38ea2b(-0x148,-_0x55b67a._0x29df1e)]['positionWC']),_0x19b1f3=_0x5dada6['isPointVisible'](_0x4a473a);if(!_0x19b1f3)return _0xb8e2d0;}return[_0x3bd5e7['x']-this['_mapOffset'][0x0],_0x3bd5e7['y']-this['_mapOffset'][0x1]];}['getViewRect'](){const _0x46b3fe={_0x549745:0x174,_0x992bff:0x175},_0x17f5d8=this['_api'];function _0x3a0685(_0x3bc1ba,_0x253fe5){return _0x5993af(_0x3bc1ba- -0x201,_0x253fe5);}function _0x8aa005(_0x1f9fa6,_0x31e121){return _0x5993af(_0x31e121- -0x114,_0x1f9fa6);}return new echarts__namespace['graphic']['BoundingRect'](0x0,0x0,_0x17f5d8[_0x3a0685(_0x46b3fe._0x549745,0x189)](),_0x17f5d8[_0x3a0685(_0x46b3fe._0x992bff,0x165)]());}[_0x387412(0x45b,0x444)](){const _0x4acbb4={_0x4a25d7:0x1ba};function _0x4b10a3(_0x2e8e21,_0xff4032){return _0x387412(_0xff4032- -0x251,_0x2e8e21);}return echarts__namespace['matrix'][_0x4b10a3(_0x4acbb4._0x4a25d7,0x1da)]();}}function _0x5993af(_0x5c5800,_0x4fa2c1){const _0x563329={_0x109008:0x1ab};return _0x190a(_0x5c5800-_0x563329._0x109008,_0x4fa2c1);}function _0x190a(_0x53f0c3,_0x552e71){const _0x415817=_0x4158();return _0x190a=function(_0x190a4b,_0x1311a4){_0x190a4b=_0x190a4b-0x184;let _0x419a65=_0x415817[_0x190a4b];if(_0x190a['IVBXPS']===undefined){var _0xa8422b=function(_0x5d9bf7){const _0x57fea2='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';let _0x2f61d2='',_0x2876dd='';for(let _0x305e9b=0x0,_0x4a5c6a,_0x3ff77c,_0x4d37d8=0x0;_0x3ff77c=_0x5d9bf7['charAt'](_0x4d37d8++);~_0x3ff77c&&(_0x4a5c6a=_0x305e9b%0x4?_0x4a5c6a*0x40+_0x3ff77c:_0x3ff77c,_0x305e9b++%0x4)?_0x2f61d2+=String['fromCharCode'](0xff&_0x4a5c6a>>(-0x2*_0x305e9b&0x6)):0x0){_0x3ff77c=_0x57fea2['indexOf'](_0x3ff77c);}for(let _0x54e7c1=0x0,_0x3e0ceb=_0x2f61d2['length'];_0x54e7c1<_0x3e0ceb;_0x54e7c1++){_0x2876dd+='%'+('00'+_0x2f61d2['charCodeAt'](_0x54e7c1)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0x2876dd);};_0x190a['RWfXRW']=_0xa8422b,_0x53f0c3=arguments,_0x190a['IVBXPS']=!![];}const _0x54648a=_0x415817[0x0],_0x373158=_0x190a4b+_0x54648a,_0x148a0d=_0x53f0c3[_0x373158];return!_0x148a0d?(_0x419a65=_0x190a['RWfXRW'](_0x419a65),_0x53f0c3[_0x373158]=_0x419a65):_0x419a65=_0x148a0d,_0x419a65;},_0x190a(_0x53f0c3,_0x552e71);}function _0x4158(){const _0x7250c=['zgf0yq','mtC2nti2nMfjzvn3vW','AgLKzgvU','zwXSAxbZB2LK','C2nLBMu','Cg9PBNrLCKv2zw50CW','q2vZAxvT','x21HCNmZzf9Zy2vUzq','zxzLBNrqyxjLBNq','ndK1yxHArgH4','y3jLyxrL','mKTwq3bPAG','Cg9ZDfjLBMrLCG','uMvJDgfUz2XL','Bgf5zxi','z2v0uMvJDgfUz2XL','zgLZCg9Zzq','CMvNAxn0zxjby3rPB24','x21HCa','z2XVyMu','x3jLBw92zwriB29R','CMvTB3zLq2HPBgq','B3b0Aw9UCW','zwnjBNn0yw5Jzq','C3r5Bgu','y2fTzxjH','DxbKyxrL','CgfPBNrLCG','Ew1PBG','zM9YrwfJAa','z2v0qxr0CLzHBa','Cg9ZAxrPB24','mhb4','z2v0','D2LKDgG','CMvNAxn0zxjdB29YzgLUyxrLu3LZDgvT','CMvZAxPL','nZqWmKjyCw9kua','x2nYzwf0zunOyxj0t3zLCMXHEq','zwnOyxj0C0f1Dg9izwLNAhq','C2vYAwvZ','x2vJAgfYDhndB250ywLUzxi','zgvMyxvSDe9WDgLVBG','BwfYCZnKtwfW','DxbKyxrLtgf5B3v0','B25cEvf1zxj5','y29VCMrPBMf0zvn5C3rLBq','ywXS','CMvNAxn0zxi','Bw9Kzq','x21VDw50zwriB29R','zwnOyxj0CW','x2fWAq','C2nOzwr1BgvY','Bw92zuHHBMrSzxi','y2fUDMfZ','x3nOB3DiB29R','rwXSAxbZB2LKywXpy2nSDwrLCG','z2v0uM9HBvrYyw5ZzM9YBq','yxbP','oda1nZK0CKvIAvv5','rwnOyxj0C0XHEwvY','DhLWzq','zwnOyxj0C0rLChrOvgvZDa','zwfJAenVBxbVBMvUDa','x21HCe9MzNnLDa','z2v0qK1HCa','ndKWntbxDg9xz3G','mJa5oda4BM94Cfzq','BwLU','mJy3mtq3Ewj5uxDg','zwnOyxj0C0zPEgvKsgvPz2H0','y29VCMrZ','zgvMAw5LuhjVCgvYDhK','z2v0v2LKDgG','z2v0sgvPz2H0','EKLUzgv4','n05XzxnKEG','x19TyxbpzMzZzxq','x19LC01VzhvSzq','mtzLDK5Qvfy','B2zM','y2XPzw50sgvPz2H0','DMfSDwu','zgvWDgHuzxn0','A2v5CW','x2fKzgvKsg9VAW','x2vJAgfYDhnjBNn0yw5Jzq','6k+35BYv5ywLigvJAgfYDhmG5BQtia','x3bVAw50zxjfDMvUDhm','Bg9Nsw5MBW','CMvTB3zLrxzLBNrmAxn0zw5LCG','y2XLyxi','mtGXnZm3nvbRCfPjta'];_0x4158=function(){return _0x7250c;};return _0x4158();}CompositeCoordinateSystem['dimensions']=['lng','lat'];function _0x387412(_0x10e13c,_0xf1ae38){return _0x190a(_0x10e13c-0x2a1,_0xf1ae38);}CompositeCoordinateSystem['create']=function(_0xd8fb3e,_0x28e2d4){const _0x24d1d7={_0x1dd44d:0x210},_0x28da03={_0x5b6ddb:0xba,_0x1a0889:0xd5},_0x35b294={_0x363c78:0x8b,_0x295c4a:0xba,_0x19c5bf:0x78};function _0xc60bf6(_0x41a513,_0x40e85c){return _0x387412(_0x41a513- -0x6,_0x40e85c);}function _0x547c33(_0x5bb2a5,_0x2cddaf){return _0x5993af(_0x5bb2a5- -0x146,_0x2cddaf);}let _0xfad763;const _0xc228a6=_0xd8fb3e['scheduler'][_0xc60bf6(0x432,0x45d)]['_mars3d_scene'];_0xd8fb3e[_0x547c33(0x225,0x247)](_0x547c33(_0x24d1d7._0x1dd44d,0x230),function(_0x2fffcd){const _0x254a8d={_0x723d41:0x28a};function _0x21cef2(_0x11e56b,_0xef83c9){return _0x547c33(_0xef83c9- -_0x254a8d._0x723d41,_0x11e56b);}const _0x11a077=_0x28e2d4['getZr']()[_0x3dd1db(-_0x35b294._0x363c78,-_0x35b294._0x295c4a)];function _0x3dd1db(_0x19a0a1,_0x217980){return _0x547c33(_0x217980- -0x2ba,_0x19a0a1);}if(!_0x11a077)return;!_0xfad763&&(_0xfad763=new CompositeCoordinateSystem(_0xc228a6,_0x28e2d4)),_0x2fffcd['coordinateSystem']=_0xfad763,_0xfad763['setMapOffset'](_0x2fffcd[_0x21cef2(-_0x35b294._0x19c5bf,-0x57)]||[0x0,0x0]);}),_0xd8fb3e['eachSeries'](function(_0x2351e2){const _0x1b0af3={_0x486caf:0x51e};function _0x4730f1(_0x16dc3e,_0x175810){return _0x547c33(_0x175810- -0x83,_0x16dc3e);}function _0x37a5c8(_0x575ad2,_0x389382){return _0xc60bf6(_0x389382- -_0x1b0af3._0x486caf,_0x575ad2);}_0x2351e2[_0x37a5c8(-_0x28da03._0x5b6ddb,-0xe2)]('coordinateSystem')===_0x37a5c8(-0xbe,-0xd8)&&(!_0xfad763&&(_0xfad763=new CompositeCoordinateSystem(_0xc228a6,_0x28e2d4)),_0x2351e2[_0x37a5c8(-0x102,-_0x28da03._0x1a0889)]=_0xfad763);});};if(echarts__namespace!==null&&echarts__namespace!==void 0x0&&echarts__namespace['init']){echarts__namespace[_0x387412(0x444,0x469)]('mars3dMap',CompositeCoordinateSystem);const _0x5d9bf7={};_0x5d9bf7[_0x387412(0x45f,0x449)]='mars3dMapRoam',_0x5d9bf7['event']='mars3dMapRoam',_0x5d9bf7[_0x5993af(0x345,0x353)]=_0x387412(0x44d,0x43e),echarts__namespace[_0x5993af(0x33c,0x337)](_0x5d9bf7,function(_0x369233,_0x19fb70){});const _0x57fea2={};_0x57fea2['roam']=![];const _0x2f61d2={};_0x2f61d2[_0x387412(0x45f,0x432)]='mars3dMap',_0x2f61d2[_0x5993af(0x36d,0x34d)]=function(){const _0x123c14={_0x1f7eb2:0x18a,_0x10e1e5:0x1a7},_0x39f4e8={_0x37fa83:0x5b2};function _0x4ce8c5(_0x39a886,_0x5c6e4f){return _0x387412(_0x39a886- -_0x39f4e8._0x37fa83,_0x5c6e4f);}return this[_0x4ce8c5(-_0x123c14._0x1f7eb2,-_0x123c14._0x10e1e5)];},_0x2f61d2[_0x5993af(0x355,0x379)]=_0x57fea2,echarts__namespace['extendComponentModel'](_0x2f61d2),echarts__namespace['extendComponentView']({'type':'mars3dMap','init':function(_0x1e0c23,_0x5b9286){const _0x33bd04={_0x264633:0x72,_0x51e146:0x1c5,_0x5574df:0x6a,_0x18a2c8:0x75,_0xc2ac21:0x5a},_0xf38019={_0x144f14:0x497};function _0x206f21(_0x27e275,_0x161f82){return _0x387412(_0x27e275- -_0xf38019._0x144f14,_0x161f82);}function _0x468401(_0x3668e2,_0x7902ae){return _0x5993af(_0x3668e2- -0x19b,_0x7902ae);}this['api']=_0x5b9286,this[_0x206f21(-_0x33bd04._0x264633,-0x7d)]=_0x1e0c23[_0x468401(_0x33bd04._0x51e146,0x1c4)]['ecInstance']['_mars3d_scene'],this['scene'][_0x206f21(-_0x33bd04._0x5574df,-_0x33bd04._0x18a2c8)]['addEventListener'](this[_0x206f21(-0x40,-_0x33bd04._0xc2ac21)],this);},'moveHandler':function(_0x5cce87,_0x2ab6c8){const _0x4e594d={_0x10891a:0x2ba,_0x4b3f19:0x2b7},_0x9d3de3={_0x123abd:0xd6},_0x4f5c39={_0x180453:0xaf},_0xf45dd3={};function _0x1c635f(_0x54ed75,_0x3f1778){return _0x5993af(_0x3f1778- -_0x4f5c39._0x180453,_0x54ed75);}function _0x3d1ddf(_0x15036e,_0x999492){return _0x5993af(_0x999492- -_0x9d3de3._0x123abd,_0x15036e);}_0xf45dd3[_0x1c635f(0x2d5,_0x4e594d._0x10891a)]='mars3dMapRoam',this[_0x1c635f(0x2ce,_0x4e594d._0x4b3f19)]['dispatchAction'](_0xf45dd3);},'render':function(_0x52387e,_0x3a0701,_0x222a69){},'dispose':function(_0x8fbe50){const _0x189095={_0x4d25ce:0xfb},_0x518d45={_0x131a5f:0x380};function _0x4fb1a3(_0x4cebb3,_0x50e936){return _0x387412(_0x50e936- -0x528,_0x4cebb3);}function _0x2cdc95(_0x284adb,_0x24835b){return _0x5993af(_0x24835b- -_0x518d45._0x131a5f,_0x284adb);}this['scene'][_0x4fb1a3(-0xff,-_0x189095._0x4d25ce)][_0x2cdc95(0x2f,0x6)](this[_0x2cdc95(-0x39,-0x1f)],this);}});}else throw new Error(_0x387412(0x479,0x486));const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace[_0x387412(0x42f,0x419)]['BaseLayer'];class EchartsLayer extends BaseLayer{constructor(_0x53c635={}){const _0x41ef51={_0x260847:0x272,_0x7d4e3e:0x22f},_0x27a245={_0x561632:0x112};function _0x3678d2(_0x37534e,_0x3acf94){return _0x5993af(_0x3acf94- -_0x27a245._0x561632,_0x37534e);}function _0x2be229(_0xe30a0f,_0x8b046f){return _0x387412(_0xe30a0f- -0x3a,_0x8b046f);}super(_0x53c635),this[_0x3678d2(0x271,_0x41ef51._0x260847)]=this[_0x3678d2(0x23e,_0x41ef51._0x7d4e3e)]['pointerEvents'];}get['layer'](){return this['_echartsInstance'];}get['pointerEvents'](){return this['_pointerEvents'];}set['pointerEvents'](_0x4be180){const _0x2f8955={_0x2a4ab6:0x553,_0x5d869b:0x564};this['_pointerEvents']=_0x4be180;function _0x59c882(_0x4a651c,_0x56fb26){return _0x387412(_0x4a651c-0x103,_0x56fb26);}this['_echartsContainer']&&(_0x4be180?this['_echartsContainer']['style']['pointerEvents']=_0x59c882(_0x2f8955._0x2a4ab6,_0x2f8955._0x5d869b):this['_echartsContainer']['style']['pointerEvents']='none');}['_setOptionsHook'](_0x50ec6b,_0x40a33e){this['setEchartsOption'](_0x50ec6b);}[_0x387412(0x459,0x459)](_0x95fadc){const _0x47f280={_0x5ce2b1:0x147,_0x336cc0:0x147,_0x254c9f:0x2ba,_0x2c0aee:0x2b6,_0x23f179:0x110},_0x5d3543={_0x6bc880:0x49b},_0xdf56fd={_0x298e4c:0x183};function _0x314517(_0x1e96ad,_0x506e1b){return _0x387412(_0x506e1b- -_0xdf56fd._0x298e4c,_0x1e96ad);}function _0x440af4(_0x31e005,_0xd82467){return _0x5993af(_0xd82467- -_0x5d3543._0x6bc880,_0x31e005);}_0x95fadc?this[_0x440af4(-0x146,-_0x47f280._0x5ce2b1)]['style']['visibility']='visible':this[_0x440af4(-0x15b,-_0x47f280._0x336cc0)][_0x314517(_0x47f280._0x254c9f,_0x47f280._0x2c0aee)]['visibility']=_0x440af4(-0x11d,-_0x47f280._0x23f179);}[_0x387412(0x453,0x481)](){const _0x4c1a8d={_0x496b8f:0x198,_0x937034:0x180,_0x59590d:0x280,_0x2b8472:0x29a,_0x306a3c:0x23e,_0x4c9bc6:0x229,_0x57a8a1:0x288,_0x47af9e:0x242},_0xd35ecf={_0x449bcc:0x1f5},_0x445b9c={_0x5a147c:0x5b3};function _0xbe0509(_0x4998ff,_0x5d0521){return _0x387412(_0x5d0521- -_0x445b9c._0x5a147c,_0x4998ff);}function _0x1900c4(_0x578333,_0xcccb96){return _0x387412(_0x578333- -_0xd35ecf._0x449bcc,_0xcccb96);}this[_0xbe0509(-_0x4c1a8d._0x496b8f,-_0x4c1a8d._0x937034)][_0xbe0509(-0x191,-0x18e)]['echartsDepthTest']=this[_0x1900c4(0x242,0x239)][_0x1900c4(_0x4c1a8d._0x59590d,_0x4c1a8d._0x2b8472)]??!![],this['_map']['scene']['echartsAutoHeight']=this['options']['clampToGround']??![],this[_0x1900c4(_0x4c1a8d._0x306a3c,0x226)][_0x1900c4(0x230,_0x4c1a8d._0x4c9bc6)][_0x1900c4(0x273,_0x4c1a8d._0x57a8a1)]=this[_0x1900c4(_0x4c1a8d._0x47af9e,0x244)]['fixedHeight']??0x0;}[_0x387412(0x477,0x482)](){const _0x3ce26c={_0x485644:0x54e,_0x645e32:0x3d4,_0x215323:0x3ce};function _0x38f1b2(_0x161e8d,_0x4ebe56){return _0x5993af(_0x161e8d-0xa5,_0x4ebe56);}function _0x5cc7db(_0x4c9e14,_0x4066a0){return _0x387412(_0x4c9e14-0xa8,_0x4066a0);}this[_0x38f1b2(0x3f9,0x3e0)]=this['_createChartOverlay'](),this['_echartsInstance']=echarts__namespace['init'](this['_echartsContainer']),this[_0x5cc7db(0x520,_0x3ce26c._0x485644)][_0x5cc7db(0x4d0,0x4b0)]=this['_map'][_0x38f1b2(_0x3ce26c._0x645e32,_0x3ce26c._0x215323)],this['setEchartsOption'](this[_0x38f1b2(0x3e6,0x415)]);}[_0x5993af(0x33f,0x31e)](){const _0x4b5a2c={_0x4c76f8:0x310,_0x2558ac:0x24,_0x48acec:0x6b,_0x4a5f84:0x6c},_0x277881={_0xef3dd7:0x168},_0x9ec5d3={_0x2c5609:0x381};function _0x24d2b4(_0x31a3e6,_0x1d6781){return _0x5993af(_0x1d6781- -_0x9ec5d3._0x2c5609,_0x31a3e6);}this['_echartsInstance']&&(this['_echartsInstance'][_0x1d3ff1(0x315,0x31b)](),this[_0x1d3ff1(_0x4b5a2c._0x4c76f8,0x2f1)][_0x24d2b4(-0x30,-0x46)](),delete this[_0x24d2b4(0x6,0x1)]);function _0x1d3ff1(_0x11e6c3,_0x5be625){return _0x387412(_0x11e6c3- -_0x277881._0xef3dd7,_0x5be625);}this[_0x24d2b4(-_0x4b5a2c._0x2558ac,-0x2d)]&&(this[_0x24d2b4(-_0x4b5a2c._0x48acec,-0x44)]['container'][_0x24d2b4(-_0x4b5a2c._0x4a5f84,-0x41)](this['_echartsContainer']),delete this['_echartsContainer']);}[_0x387412(0x447,0x434)](){const _0x36e980={_0x4d9107:0x3e4,_0x4aa95c:0x3e6,_0x597f5b:0x3e3,_0x3ee133:0x3dc,_0x165a39:0x401,_0x2ed657:0x424,_0x5002d5:0x428,_0x19947d:0x408,_0x35db24:0x413,_0x368bad:0x406},_0x2b06e3=mars3d__namespace['DomUtil']['create']('div','mars3d-echarts',this[_0x40ee21(0x3d9,_0x36e980._0x4d9107)]['container']);_0x2b06e3['id']=this['id'],_0x2b06e3[_0x23a3d1(_0x36e980._0x4aa95c,_0x36e980._0x597f5b)][_0x23a3d1(_0x36e980._0x3ee133,0x3ea)]='absolute',_0x2b06e3['style']['top']=_0x40ee21(0x3e7,0x3e8),_0x2b06e3['style']['left']='0px';function _0x23a3d1(_0x539ac3,_0x4a1027){return _0x5993af(_0x4a1027-0xa0,_0x539ac3);}_0x2b06e3['style']['width']=this['_map']['scene']['canvas']['clientWidth']+'px',_0x2b06e3['style']['height']=this[_0x23a3d1(_0x36e980._0x165a39,0x3dd)]['scene'][_0x23a3d1(0x3ec,0x402)]['clientHeight']+'px',_0x2b06e3['style'][_0x23a3d1(0x3fa,0x3d0)]=this[_0x23a3d1(0x440,_0x36e980._0x2ed657)]?_0x23a3d1(_0x36e980._0x5002d5,0x3fa):'none',_0x2b06e3[_0x23a3d1(0x402,0x3e3)][_0x40ee21(0x413,_0x36e980._0x19947d)]=this[_0x40ee21(0x3dd,0x3d5)][_0x40ee21(_0x36e980._0x35db24,_0x36e980._0x368bad)]??0x9;function _0x40ee21(_0x1fe82c,_0x100539){return _0x387412(_0x1fe82c- -0x5a,_0x100539);}return _0x2b06e3;}[_0x5993af(0x34f,0x37a)](){const _0x5d47ee={_0x1f03a5:0x255,_0x4b2c97:0x24e,_0xab5e7e:0x33,_0x4f5e74:0x4d,_0x437471:0x23,_0x1ce8cf:0x263},_0x29be62={_0x423a27:0x3fd};function _0x457ae6(_0x4641ea,_0x472d37){return _0x387412(_0x4641ea- -_0x29be62._0x423a27,_0x472d37);}if(!this['_echartsInstance'])return;function _0x169a6d(_0x1c1e74,_0xe0f57b){return _0x387412(_0x1c1e74- -0x1f5,_0xe0f57b);}this[_0x169a6d(_0x5d47ee._0x1f03a5,0x265)]['style'][_0x169a6d(_0x5d47ee._0x4b2c97,0x270)]=this[_0x457ae6(0x36,_0x5d47ee._0xab5e7e)]['scene']['canvas']['clientWidth']+'px',this[_0x457ae6(_0x5d47ee._0x4f5e74,_0x5d47ee._0x437471)]['style']['height']=this['_map']['scene'][_0x169a6d(_0x5d47ee._0x1ce8cf,0x245)][_0x169a6d(0x27e,0x276)]+'px',this['_echartsInstance'][_0x457ae6(0x48,0x46)]();}['setEchartsOption'](_0x3f2572,_0x216e95,_0x359f84){const _0x431d56={_0x55773c:0x7f},_0x4117f1={_0x5baff3:0x3b2};function _0x3bcb6d(_0x5039e6,_0x436669){return _0x387412(_0x5039e6- -0x88,_0x436669);}function _0x1d1896(_0x4bce36,_0x4f8dda){return _0x5993af(_0x4bce36- -_0x4117f1._0x5baff3,_0x4f8dda);}if(this['_echartsInstance']){const _0x152701={};_0x152701['onlySimpleType']=!![],_0x3f2572={'mars3dMap':{},...mars3d__namespace['Util'][_0x3bcb6d(0x3b7,0x3bc)](_0x3f2572,_0x152701)},delete _0x3f2572[_0x1d1896(-_0x431d56._0x55773c,-0x62)],this['_echartsInstance']['setOption'](_0x3f2572,_0x216e95,_0x359f84);}}[_0x5993af(0x33a,0x363)](_0x354e4a){const _0x508af1={_0x4e2415:0x421},_0xa1a1d0={_0x5bc41d:0x72,_0xd54c8d:0x8f,_0x5bc4fa:0x9f},_0x4b299c={_0x25e7ed:0xb6,_0x59a043:0x1f5,_0x591a8c:0x80},_0x224313={_0x218019:0x13f},_0x9301a8={_0x483e30:0x76};let _0x2359b2,_0x5d70e1,_0x313676,_0x508b18;function _0x46a4c2(_0x3972e0,_0x36a4f6){return _0x387412(_0x36a4f6-0xb,_0x3972e0);}function _0x3c467f(_0x325fb6){if(!Array['isArray'](_0x325fb6))return;function _0x50c2fe(_0x45cd60,_0xbf45ff){return _0x190a(_0xbf45ff- -_0x9301a8._0x483e30,_0x45cd60);}const _0x34acef=_0x325fb6[0x0]||0x0,_0x5a9bbe=_0x325fb6[0x1]||0x0;_0x34acef!==0x0&&_0x5a9bbe!==0x0&&(_0x2359b2===undefined?(_0x2359b2=_0x34acef,_0x5d70e1=_0x34acef,_0x313676=_0x5a9bbe,_0x508b18=_0x5a9bbe):(_0x2359b2=Math[_0x50c2fe(_0x224313._0x218019,0x14f)](_0x2359b2,_0x34acef),_0x5d70e1=Math['max'](_0x5d70e1,_0x34acef),_0x313676=Math['min'](_0x313676,_0x5a9bbe),_0x508b18=Math['max'](_0x508b18,_0x5a9bbe)));}const _0x1fc774=this['options'][_0x46a4c2(0x442,0x454)];function _0x5a0a6b(_0x347c3d,_0x2cc5be){return _0x387412(_0x2cc5be-0x12f,_0x347c3d);}_0x1fc774&&_0x1fc774[_0x5a0a6b(0x55f,0x56d)](_0x361828=>{const _0xacdc9c={_0x37e840:0x16d},_0x11de08={_0x1d8381:0x350};function _0x10fbe4(_0x518669,_0x1afa89){return _0x46a4c2(_0x1afa89,_0x518669- -0x4fc);}function _0x439af8(_0x1cb8f8,_0xa8c9be){return _0x5a0a6b(_0xa8c9be,_0x1cb8f8- -0x19d);}_0x361828[_0x10fbe4(-_0xa1a1d0._0x5bc41d,-_0xa1a1d0._0xd54c8d)]&&_0x361828[_0x10fbe4(-0x72,-_0xa1a1d0._0x5bc4fa)][_0x10fbe4(-0xb3,-0x96)](_0x282f5e=>{function _0x186c8b(_0xf993f2,_0x533e1f){return _0x439af8(_0x533e1f- -_0x11de08._0x1d8381,_0xf993f2);}function _0x5a6392(_0x33c539,_0x2ee900){return _0x10fbe4(_0x2ee900- -_0xacdc9c._0x37e840,_0x33c539);}if(_0x282f5e[_0x186c8b(0xe1,_0x4b299c._0x25e7ed)])_0x3c467f(_0x282f5e['value']);else _0x282f5e['coords']&&_0x282f5e[_0x5a6392(-0x1de,-_0x4b299c._0x59a043)][_0x186c8b(0x82,_0x4b299c._0x591a8c)](_0x2d5b45=>{_0x3c467f(_0x2d5b45);});});});if(_0x2359b2===0x0&&_0x313676===0x0&&_0x5d70e1===0x0&&_0x508b18===0x0)return null;if(_0x354e4a!==null&&_0x354e4a!==void 0x0&&_0x354e4a['isFormat']){const _0x41fcf4={};return _0x41fcf4['xmin']=_0x2359b2,_0x41fcf4['xmax']=_0x5d70e1,_0x41fcf4[_0x46a4c2(_0x508af1._0x4e2415,0x448)]=_0x313676,_0x41fcf4['ymax']=_0x508b18,_0x41fcf4;}else return Cesium[_0x5a0a6b(0x537,0x55d)]['fromDegrees'](_0x2359b2,_0x313676,_0x5d70e1,_0x508b18);}['on'](_0x2bfff3,_0x6e21bb,_0x131704){return this['_echartsInstance']['on'](_0x2bfff3,_0x6e21bb,_0x131704||this),this;}[_0x5993af(0x358,0x373)](_0x3f1c88,_0x2405c4,_0x41690a,_0x4af18a){return this['_echartsInstance']['on'](_0x3f1c88,_0x2405c4,_0x41690a,_0x4af18a||this),this;}[_0x387412(0x472,0x452)](_0x2c8820,_0x2b3e09,_0x3050c3){return this['_echartsInstance']['off'](_0x2c8820,_0x2b3e09,_0x3050c3||this),this;}}mars3d__namespace['LayerUtil'][_0x387412(0x451,0x439)]('echarts',EchartsLayer),mars3d__namespace['layer']['EchartsLayer']=EchartsLayer,mars3d__namespace[_0x5993af(0x35e,0x348)]=echarts__namespace,mars3d__namespace['Log'][_0x5993af(0x385,0x398)]('mars3d-echarts插件\x20注册成功'),exports[_0x387412(0x45e,0x432)]=EchartsLayer,Object[_0x5993af(0x380,0x36f)](echarts)[_0x5993af(0x348,0x35f)](function(_0x4dea1c){if(_0x4dea1c!=='default'&&!exports['hasOwnProperty'](_0x4dea1c))Object['defineProperty'](exports,_0x4dea1c,{'enumerable':!![],'get':function(){return echarts[_0x4dea1c];}});});const _0x3ff77c={};_0x3ff77c['value']=!![],Object['defineProperty'](exports,_0x5993af(0x37a,0x39d),_0x3ff77c);
}));
