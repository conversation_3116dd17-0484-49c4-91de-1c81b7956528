﻿<!DOCTYPE html>
<html class="no-js css-menubar" lang="zh-cn">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <!-- 移动设备 viewport -->
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no,minimal-ui" />
    <meta name="author" content="火星科技 http://mars3d.cn " />
    <!-- 360浏览器默认使用Webkit内核 -->
    <meta name="renderer" content="webkit" />
    <!-- Chrome浏览器添加桌面快捷方式（安卓） -->
    <link rel="shortcut icon" type="image/x-icon" href="/img/favicon/favicon.ico" />
    <meta name="mobile-web-app-capable" content="yes" />
    <!-- Safari浏览器添加到主屏幕（IOS） -->
    <link rel="icon" sizes="192x192" href="/img/favicon/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-title" content="火星科技MarsGIS" />
    <!-- Win8标题栏及ICON图标 -->
    <link rel="apple-touch-icon-precomposed" href="/img/favicon/apple-touch-icon.png" />
    <meta name="msapplication-TileImage" content="/img/favicon/<EMAIL>" />
    <meta name="msapplication-TileColor" content="#62a8ea" />

    <!-- 标题及搜索关键字 -->
    <meta
      name="keywords"
      content="火星科技|合肥火星|合肥火星科技|合肥火星科技有限公司|leaflet|leaflet框架|leaflet开发|cesium|cesium开发|cesium框架|gis|marsgis|地图离线|地图开发|地图框架|地图外包"
    />
    <meta
      name="description"
      content="火星科技 合肥火星 合肥火星科技 合肥火星科技有限公司 leaflet leaflet框架 leaflet开发 cesium cesium开发 cesium框架 gis marsgis 地图离线 地图开发 地图框架 地图外包 框架 开发 外包  地图离线 二维地图 三维地图 全景漫游 地理信息系统 云GIS 三维GIS GIS平台 WebGIS"
    />

    <title>功能示例(原生JS版) - Mars3D平台 | 火星科技 |合肥火星科技有限公司</title>

    <script
      type="text/javascript"
      src="./lib/include-lib.js?time=20250101"
      include="jquery,layer,toastr,bootstrap,admin-lte,haoutil,jquery.scrollTo,lazyload,mars3d"
      libpath="./lib/"
    ></script>

    <link rel="stylesheet" href="js/editor/list.css?time=20250101" />
    <style>
      /*无头部菜单时*/
      .noHeader .main-sidebar,
      .left-side {
        padding-top: 0px;
      }

      .noHeader .category {
        padding-top: 10px;
      }
    </style>
  </head>

  <body class="hold-transition skin-blue sidebar-mini noHeader" data-spy="scroll" data-target="#scrollSpy">
    <div class="wrapper">
      <!-- 侧边栏 -->
      <aside class="sidebar-wrapper main-sidebar" id="scrollSpy">
        <section class="sidebar" id="sidebar">
          <ul class="sidebar-menu nav nav-stacked" id="sidebar-menu"></ul>
        </section>
      </aside>
      <!-- 内容区 -->
      <div class="content-wrapper examples-container" id="main">
        <ul id="charts-list">
          <li class="category">
            <h3 class="category-title"><i class="fa fa-file-text-o"></i>&nbsp;&nbsp;说明</h3>
            <div class="category-content">
              <div class="box box-default color-pavarte-box">
                <span>
                  <a target="_black" href="https://www.npmjs.com/package/mars3d">
                    <img alt="Npm version" src="https://img.shields.io/npm/v/mars3d.svg?style=flat&logo=npm&label=版本号" />
                  </a>
                  <a target="_black" href="https://www.npmjs.com/package/mars3d">
                    <img alt="Npm downloads" src="https://img.shields.io/npm/dt/mars3d?style=flat&logo=npm&label=下载量" />
                  </a>
                  <a target="_black" href="https://github.com/marsgis/mars3d">
                    <img alt="GitHub stars" src="https://img.shields.io/github/stars/marsgis/mars3d?style=flat&logo=github" />
                  </a>
                  <a target="_black" href="https://gitee.com/marsgis/mars3d">
                    <img src="https://gitee.com/marsgis/mars3d/badge/star.svg?theme=dark" alt="star" />
                  </a>
                </span>
                <br />
                <br />
                1. 相关地址：<a href="http://mars3d.cn/example.html?type=es5" target="_blank">在线体验</a>、
                <a href="http://mars3d.cn/doc.html#project/example-es5" target="_blank">教程说明</a>
                <br />
                2. 名称内有 demo 的属于存在已知问题的示例，此处仅做演示。<br />
                3. 如果您访问体验当中发现bug问题或有好的建议，欢迎随时反馈给<a href="http://marsgis.cn/weixin.html" target="_blank">我们</a>。<br />
                4. 如果缺少您想要的示例，可以整理需求发送邮件至 <a href="mailto:<EMAIL>" rel="nofollow"><EMAIL></a>
              </div>
            </div>
          </li>
        </ul>
      </div>

      <!-- footer -->
      <footer class="main-footer">
        <div class="iclient-copyright" id="iclient-footer">版权所有 © 2017-2025 <a href="http://www.marsgis.cn" target="_blank">火星科技</a></div>
      </footer>
    </div>

    <script>
      window.editorUrl = "editor-es5.html"
      window.readUrl = "read-es5.html"
      window.hasPannelIcon = `
      <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" style="vertical-align: -0.125em" width="1em"
        height="1em" preserveAspectRatio="xMidYMid meet" viewBox="0 0 32 32">
        <path fill="#e44f26" d="M5.902 27.201L3.655 2h24.69l-2.25 25.197L15.985 30L5.902 27.201z" />
        <path fill="#f1662a" d="m16 27.858l8.17-2.265l1.922-21.532H16v23.797z" />
        <path fill="#ebebeb"
          d="M16 13.407h-4.09l-.282-3.165H16V7.151H8.25l.074.83l.759 8.517H16v-3.091zm0 8.027l-.014.004l-3.442-.929l-.22-2.465H9.221l.433 4.852l6.332 1.758l.014-.004v-3.216z" />
        <path fill="#fff"
          d="M15.989 13.407v3.091h3.806l-.358 4.009l-3.448.93v3.216l6.337-1.757l.046-.522l.726-8.137l.076-.83h-7.185zm0-6.256v3.091h7.466l.062-.694l.141-1.567l.074-.83h-7.743z" />
      </svg>`
    </script>
    <script src="js/editor/list.js?time=20250101"></script>
  </body>
</html>
