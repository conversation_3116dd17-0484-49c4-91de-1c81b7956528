/**
 * UI v1.0.0
 * Copyright 2015-2017 Muyao
 * Licensed under the Muyao License 1.0 
 */
.fv-has-feedback {
	position: relative;
}
.fv-control-feedback {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 2;
	display: block;
	width: 34px;
	height: 34px;
	line-height: 34px;
	text-align: center;
}
.fv-help-block {
	display: block;
}
/* ~~~~~~~~~~~~~~~~~~~~~~~~~~ */
/* ~~~ For Bootstrap form ~~~ */
/* ~~~~~~~~~~~~~~~~~~~~~~~~~~ */
.fv-form-bootstrap .help-block {
	margin-bottom: 0;
}
.fv-form-bootstrap .tooltip-inner {
	text-align: left;
}
/* Bootstrap不带标签的垂直表单 */
.fv-form-bootstrap .fv-icon-no-label {
	top: 0;
}
.fv-form-bootstrap .fv-bootstrap-icon-input-group {
	z-index: 11;
}
/* Bootstrap内联表单 */
.form-inline.fv-form-bootstrap .form-group {
	vertical-align: top;
}
.fv-form-bootstrap .has-feedback {
	position: relative;
}
.fv-form-bootstrap .has-feedback .form-control {
	padding-right: 40px;
}
.fv-form-bootstrap .has-feedback div.form-control {
	padding-right: 0;
}
.fv-form-bootstrap .form-control-feedback {
	position: absolute;
	right: 0;
	z-index: 2;
	width: 30px;
	height: 30px;
	margin: 1px 1px 0 0;
	line-height: 30px;
	text-align: center;
	pointer-events: none;
	/*background: #fff;*/
	border-radius: 4px;
}
.row > .form-group .form-control-feedback {
	margin-right: 12px;
}
.fv-form-bootstrap.form-group-lg {
	width: 36px;
	height: 36px;
	line-height: 36px;
}
.fv-form-bootstrap.form-group-sm {
	width: 24px;
	height: 24px;
	line-height: 24px;
}
.fv-form-bootstrap .form-control.input-lg + .form-control-feedback {
	width: 36px;
	height: 36px;
	line-height: 36px;
}
.fv-form-bootstrap .form-control.input-sm + .form-control-feedback {
	width: 24px;
	height: 24px;
	line-height: 24px;
}
.help-block[data-fv-validator] + .help-block[data-fv-validator] {
	margin-top: 0;
}