declare module "@quickearth/core" {

    // Generated by dts-bundle v0.7.3
// Dependencies for this module:
//   ../../twgl.js
//   ../../geojson
//   ../../jszip

//@ts-ignore
import { BufferInfo, FramebufferInfo, ProgramInfo } from "twgl.js";
//@ts-ignore
import { FeatureCollection, Geometry, Feature } from "geojson";
//@ts-ignore
import * as GeoJSON from 'geojson';
//@ts-ignore
import { Feature, FeatureCollection, GeometryObject } from "geojson";
//@ts-ignore
import { Feature, FeatureCollection, Geometry } from "geojson";
//@ts-ignore
import { JSZipObject, OutputType } from "jszip";

/**
    * code from https://github.com/mapbox/earcut
    * @ignore
    * @param data 要处理的数据
    * @param holeIndices 洞的索引数组
    * @param dim 维度
    * @returns 三角网信息
    */
export function earcut(data: any, holeIndices: any, dim: any): any[];
/**
    * code from https://github.com/mapbox/earcut
    * turn a polygon in a multi-dimensional array form (e.g. as in GeoJSON) into a form Earcut accepts
    */
export function earcutFlatten(data: any): {
        vertices: any[];
        holes: any[];
        dimensions: any;
};

/**
    * 四叉树数据结构
    */
export interface IQuadtreeRect {
        x: number;
        y: number;
        width: number;
        height: number;
}
/**
    * https://github.com/timohausmann/quadtree-js.git v1.2.3
    * 四叉树
    */
export class Quadtree {
        /**
         * Quadtree Constructor
         * @param Object bounds            bounds of the node { x, y, width, height }
         * @param Integer max_objects      (optional) max objects a node can hold before splitting into 4 subnodes (default: 10)
         * @param Integer max_levels       (optional) total max levels inside root Quadtree (default: 4)
         * @param Integer level            (optional) depth level, required for subnodes (default: 0)
         */
        constructor(bounds: IQuadtreeRect, max_objects?: number, max_levels?: number, level?: number);
        /**
         * Insert the object into the node. If the node
         * exceeds the capacity, it will split and add all
         * objects to their corresponding subnodes.
         * @param Object pRect        bounds of the object to be added { x, y, width, height }
         */
        insert(pRect: IQuadtreeRect): void;
        /**
            * Return all objects that could collide with the given object
            * @param Object pRect      bounds of the object to be checked { x, y, width, height }
            * @param allowDuplicates 是否允许重复对象。默认是允许，可以极大加快查找速度。相同节点可以在业务逻辑中进行处理
            * @return Array            array with all detected objects
            */
        retrieve(pRect: IQuadtreeRect, allowDuplicates?: boolean): any;
        /**
         * Clear the quadtree
         */
        clear(): void;
}

export interface IScatterInterpolatorOptions {
        /**
            * 插值网格的属性，一般格距接近两最近站点之间的距离比较合适，格距过大会导致多点被纳入同一网格，过小导致大量无效计算
            * 对于区域站，建议0.03的分辨率
            *
            * @type {IGridDataOptions}
            * @memberof IScatterInterpolatorOptions
            */
        gridOptions: IGridDataOptions;
}
export interface IScatterInterpolator {
        /**
            * 根据给定数据进行插值
            *
            * @param {IFeaturesProvider} data 要插值的站点数据
            * @param {number} undef 数据中的缺测值
            * @param {boolean} field 要插值的字段
            * @memberof IScatterInterpolator
            */
        interp(data: IFeaturesProvider, undef: number, field: string): GridData;
        /**
            * 插值并返回格点数据访问器
            * @param data 要插值的数据
            * @param undef 数据中的缺测值
            * @param field 要插值的字段
            */
        interpToProvider(data: IFeaturesProvider, undef: number, field: string): IGridDataProvider;
        /**
            * 设置插值参数
            * @param options 插值参数
            */
        init(options: IScatterInterpolatorOptions): this;
        destroy(): any;
}

export interface IIDWScatterInterpolatorOptions extends IScatterInterpolatorOptions {
        /**
            * 幂函数的p值， 距离的p次方的倒数，默认为2。
            * 0的时候表示每个附近点的权重相同，2表示权重与距离的平方成反比，越大表示离得近的点的权重越高，越趋向于领近点插值，一般取[0,3]
            *
            * @type {number}
            * @memberof IIDWScatterInterpolatorOptions
            */
        p?: number;
        /**
            * 一个方向上最大的网格搜索跨度，默认为2，建议[1,5]
            *
            * @type {number}
            * @memberof IIDWScatterInterpolatorOptions
            */
        maxSearchGridSpan?: number;
        /**
            * 当多个站点值落到同一个格点的时候，使用何种策略设置该网格点的值。默认是first，即取第一次被赋值的。
            * last表示取最后一次被赋值的，即覆盖
            * mean表示取均值
            * max表示取最大值
            * min表示取最小值
            *
            * @type {("any"|"mean"|"max"|"min")}
            * @memberof IIDWScatterInterpolatorOptions
            */
        pointInSameGridStrategy?: "first" | "last" | "mean" | "max" | "min" | "negMinPosMax";
}
export class IDWScatterInterpolator implements IScatterInterpolator {
        static DefaultOptions: IIDWScatterInterpolatorOptions;
        options: IIDWScatterInterpolatorOptions;
        gl: WebGLRenderingContext | WebGL2RenderingContext;
        protected interpProgram: ProgramInfo;
        protected interpBuffer: BufferInfo;
        protected interpFramebuffer: FramebufferInfo;
        protected interpUniforms: any;
        init(options: IIDWScatterInterpolatorOptions): this;
        interpToProvider(data: IFeaturesProvider, undef: number, field: string): IGridDataProvider;
        interp(data: IFeaturesProvider, undef: number, field: string): GridData;
        protected getGLDataInfo(data: GridData): {
                rawData: BufferArray;
                dataType: 5121 | 5126;
                innerFormat: number;
                pixelFormat: 6406 | 6403;
        };
        protected getValReplaceFunc(): (oldVal: number, newVal: number) => number;
        protected createStaTexture(data: IFeaturesProvider, undef: number, field: string): WebGLTexture;
        protected deleteBufferInfo(gl: WebGLRenderingContext, bufferInfo: BufferInfo): void;
        protected deleteFrameBufferInfo(gl: WebGLRenderingContext, fbi: FramebufferInfo): void;
        protected destroyShaders(): void;
        destroy(): void;
}

export interface IMCBOptions {
        dataSource: IGridDataProvider;
        /**
            * 分析值列表
            */
        analysisValues: number[];
        /**
            * 如果传入，则当一个分析值完成后会生成一个图层，这样不用等待全部分析完成就可以逐步看到效果
            */
        completeOne?: (layer: any, dataSource: IMeshDataProvider) => void;
        /**
            * 图层创建器
            */
        layerCreator: (dataSource: IMeshDataProvider) => any;
        visibleExtent?: {
                minLon: number;
                maxLon: number;
                minLat: number;
                maxLat: number;
        };
        /**
            * 是否使用wasm进行分析（速度快，但是没有实现插值，效果略差）,默认false
            */
        wasm?: boolean;
        /**
            * 使用的线程数量，一层最多使用一个线程分析，即如果只分析一个值，设置多线程也无意义，默认使用MCBDefaults.threadCount值
            */
        threadCount?: number;
}
export interface IMCBResult {
        layer: any;
        dataSource: IMeshDataProvider;
}
export const MCBDefaults: {
        threadCount: number;
};
/**
    * 执行mcb算法
    * @param options
    * @returns 当提供了completeOne参数时，返回图层的数组，否则合并到单个图层返回
    */
export function mcb(options: IMCBOptions): Promise<IMCBResult>;

/**
    * 屏幕绘制的label属性
    *
    * @interface IPointLabelScreenOptions
    */
export interface IPointLabelScreenOptions {
        /**
            * 屏幕x坐标
            *
            * @type {number}
            * @memberof IPointLabelScreenOptions
            */
        x: number;
        /**
            * 屏幕y坐标
            *
            * @type {number}
            * @memberof IPointLabelScreenOptions
            */
        y: number;
        /**
            * 对象宽度
            *
            * @type {number}
            * @memberof IPointLabelScreenOptions
            */
        width: number;
        /**
            * 未经过处理的原始宽度
            */
        sourceWidth?: number;
        /**
            * 未经过处理的原始高度
            */
        sourceHeight?: number;
        /**
            * 对象高度
            *
            * @type {number}
            * @memberof IPointLabelScreenOptions
            */
        height: number;
        /**
            * 对应点的feature
            *
            * @type {GeoJSON.Feature<GeoJSON.Point>}
            * @memberof IPointLabelScreenOptions
            */
        feature: GeoJSON.Feature<GeoJSON.Point>;
        /**
            * 绘制参数，可选
            *
            * @type {(IFeatureImageStyleOptions | IFeatureTextStyleOptions)}
            * @memberof IPointLabelScreenOptions
            */
        drawOptions?: IFeatureImageStyleOptions | IFeatureTextStyleOptions;
        /**
            * label参数，可选
            *
            * @type {IFeatureLabelStyleOptions}
            * @memberof IPointLabelScreenOptions
            */
        parentOptions?: IFeatureLabelStyleOptions;
        /**
            * 当前对象是图片还是文本
            *
            * @type {("image" | "text")}
            * @memberof IPointLabelScreenOptions
            */
        type: "image" | "text";
        /**
            * 检测后是否被隐藏
            *
            * @type {boolean}
            * @memberof IPointLabelScreenOptions
            */
        hidden?: boolean;
        /**
            * 用户对象
            *
            * @type {*}
            * @memberof IPointLabelScreenOptions
            */
        userObj?: any;
}
/**
    * 当检测到被罩盖时候如何绘制
    */
export enum CollisionDetectMode {
        /**
            * 只要有一个元素与其他的遮盖，整个feature的所有内容都不显示（不参与碰撞检测的除外）
            */
        Hide_All_When_Any = 0,
        /**
            * 只隐藏当前检测到的元素
            */
        Hide_Only_Self = 1
}
/**
    * 碰撞检测类构造参数
    *
    * @export
    * @interface ICollisionDetectorOptions
    */
export interface ICollisionDetectorOptions {
        /**
            * 屏幕上检测区域的开始x位置
            *
            * @type {number}
            * @memberof ICollisionDetectorOptions
            */
        x?: number;
        /**
            * 屏幕上检测区域的开始y位置
            *
            * @type {number}
            * @memberof ICollisionDetectorOptions
            */
        y?: number;
        /**
            * 屏幕上检测区域的宽度
            *
            * @type {number}
            * @memberof ICollisionDetectorOptions
            */
        width: number;
        /**
            * 屏幕上检测区域的高度
            *
            * @type {number}
            * @memberof ICollisionDetectorOptions
            */
        height: number;
        /**
            * 一个节点中最大可以有几个对象，默认是10
            *
            * @type {number}
            * @memberof ICollisionDetectorOptions
            */
        maxNodeItems?: number;
        /**
            * 初始化时候的对象集合
            *
            * @type {IPointLabelScreenOptions[]}
            * @memberof ICollisionDetectorOptions
            */
        objs?: IPointLabelScreenOptions[];
        /**
            * 碰撞检测的模式，默认是只隐藏被检测到的元素
            */
        collisonMode?: CollisionDetectMode;
}
/**
    * 碰撞检测器
    *
    * @export
    * @class CollisonDetector
    */
export class CollisonDetector {
        /**
            * 默认参数
            *
            * @static
            * @type {ICollisionDetectorOptions}
            * @memberof CollisonDetector
            */
        static DefaultOptions: ICollisionDetectorOptions;
        /**
            *
            * 检测树
            * @type {Quadtree}
            * @memberof CollisonDetector
            */
        protected collisionTree: Quadtree;
        protected options: ICollisionDetectorOptions;
        /**
            * 构建CollisonDetector
            * @param {ICollisionDetectorOptions} options
            * @memberof CollisonDetector
            */
        constructor(options: ICollisionDetectorOptions);
        /**
            * 插入一个对象
            *
            * @param {IPointLabelScreenOptions} obj 要插入的对象
            * @memberof CollisonDetector
            */
        insert(obj: IPointLabelScreenOptions): void;
        /**
            * 批量插入对象
            *
            * @param {IPointLabelScreenOptions[]} objs 要插入的对象集合
            * @return {*}
            * @memberof CollisonDetector
            */
        batchInsert(objs: IPointLabelScreenOptions[]): void;
        /**
            * 碰撞检测
            *
            * @param {IPointLabelScreenOptions[]} objs 被检测的对象集合
            * @return {*}  {IPointLabelScreenOptions[]}
            * @memberof CollisonDetector
            */
        collisionDectect(objs: IPointLabelScreenOptions[]): IPointLabelScreenOptions[];
        /**
            * 清除所有检测对象
            *
            * @memberof CollisonDetector
            */
        clear(): void;
}

/**
    * 二进制数据访问器
    */
export class BinaryReader {
        /**
            * 构建数据访问器
            * @param _buffer 数据缓冲区
            * @param littleEndian 是否是小端
            */
        constructor(_buffer: ArrayBuffer, littleEndian?: boolean);
        /**
            * 获取当前数据指针位置
            */
        get offset(): number;
        /**
            * 获取数据缓冲区
            */
        get buffer(): ArrayBuffer;
        /**
            * 获取当前的数据视图
            */
        get dataView(): DataView;
        /**
            * 将数据指针复位
            */
        reset(): void;
        /**
            * 将数据指针跳转到指定位置
            * @param to 目标位置
            */
        seek(to: number): void;
        /**
            * 将数据指针跳过指定字节数
            * @param count 跳过的字节数量
            */
        skip(count: number): void;
        /**
            * 读取一个无符号单字节，并将指针位置后移1个位置
            */
        readUint8(): number;
        /**
            * 读取一个无符号双字节，并将指针位置后移2个位置
            */
        readUint16(): number;
        /**
            * 读取一个无符号整数，并将指针位置后移4个位置
            */
        readUint32(): number;
        /**
            * 读取一个有符号单字节，并将指针位置后移1个位置
            */
        readInt8(): number;
        /**
            * 读取一个有符号双字节，并将指针位置后移2个位置
            */
        readInt16(): number;
        /**
            * 读取一个有符号整数，并将指针位置后移4个位置
            */
        readInt32(): number;
        /**
            * 读取一个32位单精度浮点数，并将指针位置后移4个位置
            */
        readFloat32(): number;
        /**
            * 读取一个64位双精度浮点数，并将指针位置后移8个位置
            */
        readFloat64(): number;
        /**
            * 读取指定字节数量的数据缓冲，并将指针后移byteCount个位置
            * @param byteCount 要读取的字节数量
            */
        readBuffer(byteCount: number): ArrayBuffer;
        /**
            * 读取指定字节数量的数据缓冲后转换为字符串，并将指针后移byteCount个位置
            * @param byteCount 要读取的字节数量
            */
        readString(byteCount: number): string;
}

/**
    * 导出常量
    */
export const consts: {
        /**
            * 是否开启调试模式
            */
        Debug: boolean;
        /**
            * 默认的资源文件路径，使用资源管理器加载图片的时候，如果提供的不是绝对路径，则认为提供的是该目录下的相对路径，默认 public
            */
        resourcePath: string;
        /**
            * 默认的图例颜色文件夹位置，默认值是 public/styles/colors目录
            */
        defaultLegendPath: string;
        /**
            * 配置文件中loader的指示字符，默认是#。不可以是?和&
            */
        fieldLoaderIndicator: string;
        /**
            * 配置文件中，字段从属性表中获取值时的标记字符，默认是$。不可以是?和&
            */
        fieldPropertiesIndicator: string;
        /**
            * 主题的标记字符，如可以使用 color-temp@dark#res,表示深色主题
            * 这样在初始化的时候如果将浅色作为默认主题，则配置为color-temp#res，此时通过map的setTheme设置为dark主题，则图层会自动使用color-temp@dark进行渲染
            */
        themeIndicator: string;
        /**
            * 配置的函数标记字符，如果希望在配置文件中使用字符串表达的函数，那么可以使用//!QuickEarth开头，如：
            * //!QuickEarth
            * (feature)=>{return feature.properties.value;} 或者 &function()=>{}
            */
        functionIndicator: string;
        /**
            * 自定义的pane
            * leaflet默认值如下：
            * tile:200,overlay:400（leaflet中的矢量的默认位置）,shadow:500,marker:600,tooltip:650,popup:700
            * 添加的默认值如下:
            * feature:450。如等值线，格点填值
            * station:480。如自动站等站点图层
            * filled:380。如格点填色
            * topmap:430。如地图名字，在格点相关图层之上，feature图层之下
            * bottommap:260。tile之上，格点之下的图层。
            */
        customPanes: {
                feature: {
                        name: string;
                        zIdx: number;
                };
                filled: {
                        name: string;
                        zIdx: number;
                };
                topmap: {
                        name: string;
                        zIdx: number;
                };
                bottommap: {
                        name: string;
                        zIdx: number;
                };
                station: {
                        name: string;
                        zIdx: number;
                };
                bellowtile: {
                        name: string;
                        zIdx: number;
                };
        };
        /**
            * 默认缺测值 999999
            */
        defaultUndef: number;
        /**
            * 是否使用WebGL2渲染，部分图层需要开启。默认开启。
            * @deprecated 当前已经不支持webgl1在三维下的渲染，默认启用webgl2
            */
        useWebGL2: boolean;
        /**
         * 是否检测高清屏（2k或以上），默认为否，即在高清屏下面的绘制没有普通屏清晰，需要图层本身支持
         */
        checkRetina: boolean;
        /**
            * retian的比例，默认通过window对象获取，如果这里设置，则设置为固定值
            */
        retinaRatio: any;
        /**
            * 授权文件地址，默认是public/qe.lic
            */
        licPath: string;
        /**
            * wasm库的文件夹路径，默认是 public/libs/wasm
            */
        wasmPath: string;
        /**
            * worker的路径，默认是 public/libs/workers
            */
        workerPath: string;
};

/**
    * 消息体
    */
export interface IEventData {
        firer: any;
        msg: any;
}
/**
    * 支持事件的基础对象
    */
export class Evented {
        on(types: any, fn: (event: IEventData) => void, context?: any): this;
        off(types: any, fn: any, context?: any): this;
        lock(): this;
        unlock(fireLocked?: boolean): void;
        fire(type: any, data: IEventData, propagate?: any): this;
        listens(type: any, propagate?: any): boolean;
        once(types: any, fn: (event: IEventData) => void, context?: any): this;
        addEventParent(obj: any): this;
        removeEventParent(obj: any): this;
}

/**
    * 数值配置项获取函数
    */
export type FeatureNumberFunc = (feature: GeoJSON.Feature) => number;
/**
    * 数值配置项类型
    */
export type FeatureNumberField = number | FeatureNumberFunc | IStopRulesOptions | string;
/**
    * 将数值配置项转换为数值配置函数
    *
    * @export
    * @param {FeatureNumberField} field
    * @return {*}  {FeatureNumberFunc}
    */
export function FeatureNumberField2FeatureNumberFunc(sourceField: FeatureNumberField): FeatureNumberFunc;
/**
    * 字符串配置函数
    */
export type FeatureStringFunc = (feature: GeoJSON.Feature) => string;
/**
    * 字符串配置项类型
    */
export type FeatureStringField = string | FeatureStringFunc | IStopRulesOptions | number;
/**
    * 将字符串配置项转换为配置函数
    *
    * @export
    * @param {FeatureStringField} field 字符串配置项
    * @return {*}  {FeatureStringFunc}
    */
export function FeatureStringField2FeatureStringFunc(sourceField: FeatureStringField): FeatureStringFunc;
/**
    * 逻辑配置项函数
    */
export type FeatureBooleanFunc = (feature: GeoJSON.Feature) => boolean;
/**
    * 逻辑配置项类型
    */
export type FeatureBooleanField = string | FeatureBooleanFunc | IStopRulesOptions | number | boolean;
/**
    * 将逻辑配置项转换为逻辑配置函数
    *
    * @export
    * @param {FeatureBooleanField} field
    * @return {*}  {FeatureBooleanFunc}
    */
export function FeatureBooleanField2FeatureBooleanFunc(sourceField: FeatureBooleanField): FeatureBooleanFunc;
/**
    * 数值数组配置项函数
    */
export type FeatureNumberArrayFunc = (feature: GeoJSON.Feature) => number[];
/**
    * 数值数组配置项
    */
export type FeatureNumberArrayField = number[] | FeatureNumberArrayFunc | IStopRulesOptions | string;
/**
    * 将数值数组配置项转换为数值数组配置函数
    *
    * @export
    * @param {FeatureNumberArrayField} field
    * @return {*}  {FeatureNumberArrayFunc}
    */
export function FeatureNumberArrayField2FeatureNumberArrayFunc(sourceField: FeatureNumberArrayField): FeatureNumberArrayFunc;
/**
    * 颜色配置项函数
    */
export type FeatureColorFunc = (feature: GeoJSON.Feature) => Spectra;
/**
    * 可以返回多种颜色表达方式的颜色配置项函数
    */
export type FeatureColorWrapperFunc = (feature: GeoJSON.Feature) => Spectra | number | number[] | string;
/**
    * 颜色字段配置项
    */
export type FeatureColorField = number | number[] | Spectra | FeatureColorFunc | FeatureColorWrapperFunc | IStopRulesOptions | string | {
        r: number;
        g: number;
        b: number;
        a?: number;
} | {
        red: number;
        green: number;
        blue: number;
        alpha?: number;
};
/**
    * 将颜色配置转换为颜色获取函数
    *
    * @export
    * @param {FeatureColorField} field
    * @return {*}  {FeatureColorFunc}
    */
export function FeatureColorField2FeatureColorFunc(sourceField: FeatureColorField): FeatureColorFunc;
/**
    * 图像配置函数
    */
export type FeatureImageFunc = (feature: GeoJSON.Feature) => ImageLike;
/**
    * 图像配置字段
    */
export type FeatureImageField = ImageLike | FeatureImageFunc | IStopRulesOptions | string;
/**
    * 将图像配置转换为图像获取函数
    *
    * @export
    * @param {FeatureImageField} field
    * @return {*}  {FeatureImageFunc}
    */
export function FeatureImageField2FeatureImageFunc(sourceField: FeatureImageField): FeatureImageFunc;
/**
    * 图片样式的配置项
    *
    * @export
    * @interface IFeatureImageStyleOptions
    */
export interface IFeatureImageStyleOptions {
        /**
            *
            * 图片绘制的大小，默认[32,32]
            * @type {FeatureNumberArrayField}
            * @memberof IFeatureImageStyleOptions
            */
        size?: FeatureNumberArrayField;
        /**
            * 是否以米来表示size，仅对三维cesium有效，默认false
            */
        sizeInMeters?: FeatureBooleanField;
        /**
            *
            * 图片绘制的角度，默认0，单位是弧度。如果单位是度，需要设置angleDegree属性为true
            * @type {FeatureNumberField}
            * @memberof IFeatureImageStyleOptions
            */
        angle?: FeatureNumberField;
        /**
            *
            * 图像内容，必选项
            * @type {FeatureImageField}
            * @memberof IFeatureImageStyleOptions
            */
        data: FeatureImageField;
        /**
            * 图片的颜色。当设置该值的时候，将对图片重新上色。
            *
            * @type {FeatureColorField}
            * @memberof IFeatureImageStyleOptions
            */
        color?: FeatureColorField;
        /**
            *
            * 图像距离点位置的偏移量
            * @type {FeatureNumberArrayField}
            * @memberof IFeatureImageStyleOptions
            */
        offset?: FeatureNumberArrayField;
        /**
            *
            * 图像是否可见，默认为是
            * @type {FeatureBooleanField}
            * @memberof IFeatureImageStyleOptions
            */
        visible?: FeatureBooleanField;
        /**
            * 是否避免遮盖，默认为false
            *
            * @type {FeatureBooleanField}
            * @memberof IFeatureImageStyleOptions
            */
        avoidCollison?: FeatureBooleanField;
        /**
            * 阴影的颜色。默认没有阴影
            *
            * @type {FeatureColorField}
            * @memberof IFeatureImageStyleOptions
            */
        shadowColor?: FeatureColorField;
        /**
            *
            * 阴影的偏移量 [xoffset,yoffset]。默认[10,10]
            * @type {FeatureNumberArrayField}
            * @memberof IFeatureImageStyleOptions
            */
        shadowOffset?: FeatureNumberArrayField;
        /**
            * 阴影模糊效果。默认10
            *
            * @type {FeatureNumberField}
            * @memberof IFeatureImageStyleOptions
            */
        shadowBlur?: FeatureNumberField;
        /**
            * 高度模式。none表示绝对高度，relative表示相对于当前位置地表以上高度，clamp表示贴地。
            * 默认为none
            * 仅支持3D
            *
            * @type {("none"|"relative"|"clamp")}
            * @memberof IFeatureImageStyleOptions
            */
        heightMode?: "none" | "relative" | "clamp";
        /**
            * 是否进行深度检测，默认是
            *
            * @type {FeatureBooleanField}
            * @memberof IFeatureImageStyleOptions
            */
        depthTest?: FeatureBooleanField;
        /**
            * 眼坐标系的偏移量，单位米，仅支持Cesium，默认[0,0,0]
            */
        eyeOffset?: FeatureNumberArrayField;
}
/**
    * 矢量样式相关的基础类
    *
    * @class FeatureBaseClass
    */
export abstract class FeatureBaseClass<T> extends StyleBaseClass<T> {
        protected _createCacheId(val: GeoJSON.Feature): string | number;
}
/**
    * 图像样式类
    *
    * @export
    * @class ImageStyle
    */
export class FeatureImageStyle extends FeatureBaseClass<IFeatureImageStyleOptions> {
        /**
            *尺寸获取函数
            *
            * @type {FeatureNumberArrayFunc}
            * @memberof FeatureImageStyle
            */
        size?: FeatureNumberArrayFunc;
        /**
            * 是否以米来表示size，仅对三维cesium有效，默认false
            */
        sizeInMeters?: FeatureBooleanFunc;
        /**
            *旋转角度获取函数，单位是弧度，如果配置为从字段中获取，可以使用 $字段名的方式，如果字段中单位是角度，可以使用loader进行转换，如 $angle#degree2arc
            *
            * @type {FeatureNumberFunc}
            * @memberof FeatureImageStyle
            */
        angle?: FeatureNumberFunc;
        /**
            *图像内容获取函数
            *
            * @type {FeatureImageFunc}
            * @memberof FeatureImageStyle
            */
        data: FeatureImageFunc;
        /**
            * 图片的颜色。如果设置，将对图片进行重新上色。
            *
            * @type {FeatureColorFunc}
            * @memberof FeatureImageStyle
            */
        color: FeatureColorFunc;
        /**
            *
            * 偏移量获取函数
            * @type {FeatureNumberArrayFunc}
            * @memberof FeatureImageStyle
            */
        offset?: FeatureNumberArrayFunc;
        /**
            *
            * 是否可见获取函数
            * @type {FeatureBooleanFunc}
            * @memberof FeatureImageStyle
            */
        visible?: FeatureBooleanFunc;
        /**
            * 是否避免遮盖，默认为false
            *
            * @type {FeatureBooleanFunc}
            * @memberof FeatureImageStyle
            */
        avoidCollison: FeatureBooleanFunc;
        /**
            * 阴影的颜色。默认没有阴影
            *
            * @type {FeatureColorFunc}
            * @memberof FeatureImageStyle
            */
        shadowColor?: FeatureColorFunc;
        /**
            *
            * 阴影的偏移量 [xoffset,yoffset]。默认[10,10]
            * @type {FeatureNumberArrayFunc}
            * @memberof FeatureImageStyle
            */
        shadowOffset?: FeatureNumberArrayFunc;
        /**
            * 阴影模糊效果。默认10
            *
            * @type {FeatureNumberFunc}
            * @memberof FeatureImageStyle
            */
        shadowBlur?: FeatureNumberFunc;
        /**
            * 高度模式。none表示绝对高度，relative表示相对于当前位置地表以上高度，clamp表示贴地。
            * 默认为none。
            * 仅支持3D。
            *
            * @type {("none"|"relative"|"clamp")}
            * @memberof FeatureImageStyle
            */
        heightMode: "none" | "relative" | "clamp";
        /**
            * 是否进行深度检测，默认是
            *
            * @type {FeatureBooleanFunc}
            * @memberof FeatureImageStyle
            */
        depthTest: FeatureBooleanFunc;
        /**
            * 眼坐标系的偏移量，单位米，仅支持Cesium，默认[0,0,0]
            */
        eyeOffset?: FeatureNumberArrayFunc;
        /**
            *
            * 默认配置项
            * @static
            * @type {IFeatureImageStyleOptions}
            * @memberof FeatureImageStyle
            */
        static DefaultOptions: IFeatureImageStyleOptions;
        /**
            * 构建 ImageStyle.
            * @param {IFeatureImageStyleOptions} options 构造器参数
            * @memberof FeatureImageStyle
            */
        constructor(options: IFeatureImageStyleOptions);
        protected _update(options: IFeatureImageStyleOptions, reinit: boolean): void;
}
/**
    * 文本样式配置参数
    *
    * @export
    * @interface IFeatureTextStyleOptions
    */
export interface IFeatureTextStyleOptions {
        /**
            *
            * 文本内容。必选项。
            * 当使用 $方式获取字段值的时候，如果字段是风速且希望使用风杆表示，则需要增加默认的风杆loader，配置方式为
            * $属性名#wind，如 $speed#wind
            * @type {FeatureStringField}
            * @memberof IFeatureTextStyleOptions
            */
        data: FeatureStringField;
        /**
            *
            * 字体。默认 "12px 微软雅黑"
            * @type {FeatureStringField}
            * @memberof IFeatureTextStyleOptions
            */
        font?: FeatureStringField;
        /**
            *
            * 文字旋转角度。二维和三维中默认均使用弧度，如果字段值是角度，可以使用degreeToArc的loader进行转换
            * 如配置为 $WIND_DIR#degreeToArc，表示从WIND_DIR字段获取值，然后使用degreeToArc转换为弧度
            * @type {FeatureNumberField}
            * @memberof IFeatureTextStyleOptions
            */
        angle?: FeatureNumberField;
        /**
            *
            * 文字颜色
            * @type {FeatureColorField}
            * @memberof IFeatureTextStyleOptions
            */
        color?: FeatureColorField;
        /**
            *
            * 文字描边颜色
            * @type {FeatureColorField}
            * @memberof IFeatureTextStyleOptions
            */
        strokeColor?: FeatureColorField;
        /**
            *
            * 文字描边宽度
            * @type {FeatureNumberField}
            * @memberof IFeatureTextStyleOptions
            */
        strokeWidth?: FeatureNumberField;
        /**
            * 阴影的颜色。默认没有阴影
            *
            * @type {FeatureColorField}
            * @memberof IFeatureTextStyleOptions
            */
        shadowColor?: FeatureColorField;
        /**
            *
            * 阴影的偏移量 [xoffset,yoffset]。默认[10,10]
            * @type {FeatureNumberArrayField}
            * @memberof IFeatureTextStyleOptions
            */
        shadowOffset?: FeatureNumberArrayField;
        /**
            * 阴影模糊效果。默认10
            *
            * @type {FeatureNumberField}
            * @memberof IFeatureTextStyleOptions
            */
        shadowBlur?: FeatureNumberField;
        /**
            *
            * 文字背景色
            * @type {FeatureColorField}
            * @memberof IFeatureTextStyleOptions
            */
        backColor?: FeatureColorField;
        /**
            *
            * 文字背景色是否使用圆形，默认为false
            * @type {FeatureBooleanField}
            * @memberof IFeatureTextStyleOptions
            */
        backCircle?: FeatureBooleanField;
        /**
            * 文字背景色是否使用圆角矩形，默认为false。此配置优先级高于backCircle
            *
            * @type {FeatureBooleanField}
            * @memberof IFeatureTextStyleOptions
            */
        backRoundRect?: FeatureBooleanField;
        /**
            *
            * 文字背景色的圆角像素数，默认为6px
            * @type {FeatureNumberField}
            * @memberof IFeatureTextStyleOptions
            */
        backRoundRadius?: FeatureNumberField;
        /**
            * 背景色的高度
            *
            * @type {FeatureNumberField}
            * @memberof IFeatureTextStyleOptions
            */
        backHeight?: FeatureNumberField;
        /**
            * 背景色的宽度，如果设定了该值则不根据字符串计算宽度，统一采用相同宽度
            *
            * @type {FeatureNumberField}
            * @memberof IFeatureTextStyleOptions
            */
        backWidth?: FeatureNumberField;
        /**
            * 背景色描边颜色
            *
            * @type {FeatureBooleanField}
            * @memberof IFeatureTextStyleOptions
            */
        backStrokeColor?: FeatureColorField;
        /**
            * 背景色描边粗细
            *
            * @type {FeatureNumberField}
            * @memberof IFeatureTextStyleOptions
            */
        backStrokeWidth?: FeatureNumberField;
        /**
            *
            * 文字背景色的留边，默认为[5,0]
            * @type {FeatureNumberArrayField}
            * @memberof IFeatureTextStyleOptions
            */
        backPadding?: FeatureNumberArrayField;
        /**
            * 背景色阴影的颜色。默认没有阴影
            *
            * @type {FeatureColorField}
            * @memberof IFeatureTextStyleOptions
            */
        backShadowColor?: FeatureColorField;
        /**
            * 背景色的描边线型。默认正常线段。
            *
            * @type {FeatureNumberArrayField}
            * @memberof IFeatureTextStyleOptions
            */
        backStrokeDashArray?: FeatureNumberArrayField;
        /**
            * 背景色阴影偏移量 [xoffset,yoffset]。默认[3,3]
            *
            * @type {FeatureNumberArrayField}
            * @memberof IFeatureTextStyleOptions
            */
        backShadowOffset?: FeatureNumberArrayField;
        /**
            * 背景色阴影模糊量。默认6
            *
            * @type {FeatureNumberField}
            * @memberof IFeatureTextStyleOptions
            */
        backShadowBlur?: FeatureNumberField;
        /**
            *
            * 文字离中心点的偏移量
            * @type {FeatureNumberArrayField}
            * @memberof IFeatureTextStyleOptions
            */
        offset?: FeatureNumberArrayField;
        /**
            *
            * 文字是否可见，默认可见
            * @type {FeatureBooleanField}
            * @memberof IFeatureTextStyleOptions
            */
        visible?: FeatureBooleanField;
        /**
            * 是否避免遮盖，默认为false
            *
            * @type {FeatureBooleanField}
            * @memberof IFeatureTextStyleOptions
            */
        avoidCollison?: FeatureBooleanField;
        /**
            * 碰撞检测的扩展宽高，用于有背景色时的微调，默认[0,0]
            */
        collisionExtendSize?: FeatureNumberArrayField;
        /**
            * 文本左右对齐方式 start|end|left|center|right。默认是center
            *
            * @type {FeatureStringField}
            * @memberof IFeatureTextStyleOptions
            */
        align?: FeatureStringField;
        /**
            *
            * 文本上下对齐方式 top|bottom|middle|alphabetic|hanging。默认是middle
            * @type {FeatureStringField}
            * @memberof IFeatureTextStyleOptions
            */
        baseline?: FeatureStringField;
        /**
            * 高度模式。none表示绝对高度，relative表示相对于当前位置地表以上高度，clamp表示贴地。
            * 默认为none
            *
            * @type {("none"|"relative"|"clamp")}
            * @memberof IFeatureTextStyleOptions
            */
        heightMode?: "none" | "relative" | "clamp";
        /**
            * 是否强制使用图片方式渲染，如果是将会通过canvas先生成图片，然后将图片渲染到地图。大量中文建议使用。
            * 仅支持3D点图层。
            *
            * @type {FeatureBooleanField}
            * @memberof IFeatureTextStyleOptions
            */
        forceImage?: FeatureBooleanField;
        /**
            * 使用图片渲染文字时，图片画布的大小
            *
            * @type {FeatureNumberArrayField}
            * @memberof IFeatureTextStyleOptions
            */
        forceImageSize?: FeatureNumberArrayField;
        /**
            * 是否进行深度检测，默认是
            *
            * @type {FeatureBooleanField}
            * @memberof IFeatureTextStyleOptions
            */
        depthTest?: FeatureBooleanField;
        /**
            * 眼坐标系的偏移量，单位米，仅支持Cesium，默认[0,0,0]
            */
        eyeOffset?: FeatureNumberArrayField;
}
export class FeatureTextStyle extends FeatureBaseClass<IFeatureTextStyleOptions> {
        /**
            * 默认配置项
            *
            * @static
            * @type {IFeatureTextStyleOptions}
            * @memberof FeatureTextStyle
            */
        static DefaultOptions: IFeatureTextStyleOptions;
        /**
            *
            * 获取文字内容
            * @type {FeatureStringFunc}
            * @memberof FeatureTextStyle
            */
        data: FeatureStringFunc;
        /**
            *
            * 获取字体
            * @type {FeatureStringFunc}
            * @memberof FeatureTextStyle
            */
        font?: FeatureStringFunc;
        /**
            *
            * 获取文字旋转角度
            * @type {FeatureNumberFunc}
            * @memberof FeatureTextStyle
            */
        angle?: FeatureNumberFunc;
        /**
            *
            * 获取文字颜色
            * @type {FeatureColorFunc}
            * @memberof FeatureTextStyle
            */
        color?: FeatureColorFunc;
        /**
            *
            * 获取文字描边颜色
            * @type {FeatureColorFunc}
            * @memberof FeatureTextStyle
            */
        strokeColor?: FeatureColorFunc;
        /**
            *
            * 获取文字描边宽度
            * @type {FeatureNumberFunc}
            * @memberof FeatureTextStyle
            */
        strokeWidth?: FeatureNumberFunc;
        /**
            * 阴影的颜色。默认没有阴影
            *
            * @type {FeatureColorFunc}
            * @memberof FeatureTextStyle
            */
        shadowColor?: FeatureColorFunc;
        /**
            *
            * 阴影的偏移量 [xoffset,yoffset]。默认[3,3]
            * @type {FeatureNumberArrayFunc}
            * @memberof FeatureTextStyle
            */
        shadowOffset?: FeatureNumberArrayFunc;
        /**
            * 阴影模糊效果。默认6
            *
            * @type {FeatureNumberFunc}
            * @memberof FeatureTextStyle
            */
        shadowBlur?: FeatureNumberFunc;
        /**
            *
            * 获取文字背景色
            * @type {FeatureColorFunc}
            * @memberof FeatureTextStyle
            */
        backColor?: FeatureColorFunc;
        /**
            *
            * 获取文字背景色是否使用圆形
            * @type {FeatureBooleanFunc}
            * @memberof FeatureTextStyle
            */
        backCircle?: FeatureBooleanFunc;
        /**
            * 获取文字是否使用圆角矩形
            *
            * @type {FeatureBooleanFunc}
            * @memberof FeatureTextStyle
            */
        backRoundRect?: FeatureBooleanFunc;
        /**
            *
            * 获取文字圆角像素数。默认为6px
            * @type {FeatureNumberFunc}
            * @memberof FeatureTextStyle
            */
        backRoundRadius?: FeatureNumberFunc;
        /**
            * 获取背景色的高度
            *
            * @type {FeatureNumberFunc}
            * @memberof FeatureTextStyle
            */
        backHeight?: FeatureNumberFunc;
        /**
         * 背景色的宽度，如果设定了该值则不根据字符串计算宽度，统一采用相同宽度
         *
         * @type {FeatureNumberFunc}
         * @memberof FeatureTextStyle
         */
        backWidth?: FeatureNumberFunc;
        /**
            * 获取背景色描边颜色
            *
            * @type {FeatureColorFunc}
            * @memberof FeatureTextStyle
            */
        backStrokeColor?: FeatureColorFunc;
        /**
            *
            * 获取背景色描边线型。默认为正常线段。
            * @type {FeatureNumberArrayFunc}
            * @memberof FeatureTextStyle
            */
        backStrokeDashArray?: FeatureNumberArrayFunc;
        /**
            * 获取背景色描边粗细
            *
            * @type {FeatureNumberFunc}
            * @memberof FeatureTextStyle
            */
        backStrokeWidth?: FeatureNumberFunc;
        /**
            *
            * 获取文字背景色的留边。默认为[5,0]
            * @type {FeatureNumberArrayFunc}
            * @memberof FeatureTextStyle
            */
        backPadding?: FeatureNumberArrayFunc;
        /**
            * 背景色阴影的颜色。默认没有阴影
            *
            * @type {FeatureColorFunc}
            * @memberof FeatureTextStyle
            */
        backShadowColor?: FeatureColorFunc;
        /**
            * 背景色阴影偏移量 [xoffset,yoffset]。默认[3,3]
            *
            * @type {FeatureNumberArrayFunc}
            * @memberof FeatureTextStyle
            */
        backShadowOffset?: FeatureNumberArrayFunc;
        /**
            * 背景色阴影模糊量。默认6
            *
            * @type {FeatureNumberFunc}
            * @memberof FeatureTextStyle
            */
        backShadowBlur?: FeatureNumberFunc;
        /**
            *
            * 获取文字距离中心点的偏移量
            * @type {FeatureNumberArrayFunc}
            * @memberof FeatureTextStyle
            */
        offset?: FeatureNumberArrayFunc;
        /**
            *
            * 获取文字是否可见
            * @type {FeatureBooleanFunc}
            * @memberof FeatureTextStyle
            */
        visible?: FeatureBooleanFunc;
        /**
            * 是否避免遮盖，默认为false
            *
            * @type {FeatureBooleanField}
            * @memberof IFeatureTextStyleOptions
            */
        avoidCollison: FeatureBooleanFunc;
        /**
            * 碰撞检测的扩展宽高，用于有背景色时的微调，默认[0,0]
            */
        collisionExtendSize: FeatureNumberArrayFunc;
        /**
            * 获取文本左右对齐方式 start|end|left|center|right。默认是center
            *
            * @type {FeatureStringFunc}
            * @memberof FeatureTextStyle
            */
        align?: FeatureStringFunc;
        /**
            *
            * 获取文本上下对齐方式 top|bottom|middle|alphabetic|hanging。默认是middle
            * @type {FeatureStringFunc}
            * @memberof FeatureTextStyle
            */
        baseline?: FeatureStringFunc;
        /**
            * 高度模式。none表示绝对高度，relative表示相对于当前位置地表以上高度，clamp表示贴地。
            * 默认为none。
            * 仅支持3D
            *
            * @type {("none"|"relative"|"clamp")}
            * @memberof FeatureTextStyle
            */
        heightMode?: "none" | "relative" | "clamp";
        /**
            * 是否强制使用图片方式渲染，如果是将会通过canvas先生成图片，然后将图片渲染到地图。可能会带来性能降低。
            * 仅支持3D图层。
            *
            * @type {FeatureBooleanFunc}
            * @memberof FeatureTextStyle
            */
        forceImage?: FeatureBooleanFunc;
        /**
            * 使用图片渲染文字时，图片画布的大小，默认是64*64
            *
            * @type {FeatureNumberArrayFunc}
            * @memberof FeatureTextStyle
            */
        forceImageSize?: FeatureNumberArrayFunc;
        /**
            * 是否进行深度检测，默认是
            *
            * @type {FeatureBooleanFunc}
            * @memberof FeatureTextStyle
            */
        depthTest?: FeatureBooleanFunc;
        /**
            * 眼坐标系的偏移量，单位米，仅支持Cesium，默认[0,0,0]
            */
        eyeOffset?: FeatureNumberArrayFunc;
        /**
            * 构建 TextStyle
            * @param {IFeatureTextStyleOptions} options 构造参数
            * @memberof FeatureTextStyle
            */
        constructor(options: IFeatureTextStyleOptions);
        protected _update(options: IFeatureTextStyleOptions, reinit: boolean): void;
}
/**
    * 标签样式构造参数
    *
    * @export
    * @interface IFeatureLabelStyleOptions
    */
export interface IFeatureLabelStyleOptions {
        /**
            * 图片配置
            */
        image?: IFeatureImageStyleOptions;
        /**
            * 文本配置
            */
        text?: IFeatureTextStyleOptions;
}
/**
    * 标签样式
    *
    * @export
    * @class LabelStyle
    */
export class FeatureLabelStyle extends FeatureBaseClass<IFeatureLabelStyleOptions> {
        /**
            * 图片样式
            *
            * @type {FeatureImageStyle}
            * @memberof FeatureLabelStyle
            */
        image?: FeatureImageStyle;
        /**
            *
            * 文本样式
            * @type {FeatureTextStyle}
            * @memberof FeatureLabelStyle
            */
        text?: FeatureTextStyle;
        /**
            * 构建 FeatureLabelStyle
            * @param {IFeatureLabelStyleOptions} options 构建参数
            * @memberof FeatureLabelStyle
            */
        constructor(options: IFeatureLabelStyleOptions);
        protected _update(options: IFeatureLabelStyleOptions, reinit: boolean): void;
}
/**
    * 矢量样式基础接口
    *
    * @export
    * @interface IGeometryStyleOptions
    */
export interface IGeometryStyleOptions {
        /**
            * 绘制的颜色
            *
            * @type {FeatureColorField}
            * @memberof IGeometryStyleOptions
            */
        color?: FeatureColorField;
        /**
            *
            * 元素是否可见
            * @type {FeatureBooleanField}
            * @memberof IGeometryStyleOptions
            */
        visible?: FeatureBooleanField;
        /**
            * 阴影的颜色。默认没有阴影
            * 仅支持2D图层
            * @type {FeatureColorField}
            * @memberof IGeometryStyleOptions
            */
        shadowColor?: FeatureColorField;
        /**
            *
            * 阴影的偏移量 [xoffset,yoffset]。默认[10,10]
            * 仅支持2D图层
            * @type {FeatureNumberArrayField}
            * @memberof IGeometryStyleOptions
            */
        shadowOffset?: FeatureNumberArrayField;
        /**
            * 阴影模糊效果。默认10
            * 仅支持2D图层
            * @type {FeatureNumberField}
            * @memberof IGeometryStyleOptions
            */
        shadowBlur?: FeatureNumberField;
        /**
            * 是否使用数据点中的高度。如果使用，则当数据点中只有经纬度时，高度为0
            * 仅支持3D图层
            *
            * @type {FeatureBooleanField}
            * @memberof IGeometryStyleOptions
            */
        usePositionHeight?: FeatureBooleanField;
        /**
            * 矢量的高度，当设置了usePositionHeight时，该属性失效
            * 仅支持3D图层
            *
            * @type {FeatureNumberField}
            * @memberof IGeometryStyleOptions
            */
        height?: FeatureNumberField;
        /**
            * 高度偏移量，在最终的高度上进行偏移，默认10米
            */
        heightOffset?: FeatureNumberField;
        /**
            * 最小的可见级别，如设置为5，表示zoom低于5的均不展示，默认为0，即不做最小级别限制
            * Cesium中请使用heightMin
            */
        zoomMin?: FeatureNumberField;
        /**
            * 最大的可见级别，如设置为10，表示zoom高于10的均不展示，默认为100，即不做最大级别限制
            * Cesium中请使用heightMax
            */
        zoomMax?: FeatureNumberField;
        /**
            * 最小的可见高度，仅对Cesium生效
            * 暂未启用
            */
        heightMin?: FeatureNumberField;
        /**
            * 最大可见高度，仅对Cesium生效
            * 暂未启用
            */
        heightMax?: FeatureNumberField;
}
/**
    * 矢量样式基础类
    *
    * @export
    * @class GeometryStyle
    */
export class GeometryStyle<T> extends FeatureBaseClass<T> {
        /**
            * 默认样式
            *
            * @static
            * @type {IGeometryStyleOptions}
            * @memberof GeometryStyle
            */
        static DefaultOptions: IGeometryStyleOptions;
        /**
            * 获取颜色
            *
            * @type {FeatureColorFunc}
            * @memberof GeometryStyle
            */
        color?: FeatureColorFunc;
        /**
            *
            * 获取是否可见
            * @type {FeatureBooleanFunc}
            * @memberof GeometryStyle
            */
        visible?: FeatureBooleanFunc;
        /**
            * 阴影的颜色。默认没有阴影。
            * 仅支持2D图层
            *
            * @type {FeatureColorFunc}
            * @memberof GeometryStyle
            */
        shadowColor?: FeatureColorFunc;
        /**
            *
            * 阴影的偏移量 [xoffset,yoffset]。默认[10,10]
            * 仅支持2D图层
            * @type {FeatureNumberArrayFunc}
            * @memberof GeometryStyle
            */
        shadowOffset?: FeatureNumberArrayFunc;
        /**
            * 阴影模糊效果。默认10
            * 仅支持2D图层
            * @type {FeatureNumberFunc}
            * @memberof GeometryStyle
            */
        shadowBlur?: FeatureNumberFunc;
        /**
            * 是否使用数据点中的高度。如果使用，则当数据点中只有经纬度时，高度为0
            * 仅支持3D图层
            *
            * @type {FeatureBooleanField}
            * @memberof GeometryStyle
            */
        usePositionHeight?: FeatureBooleanFunc;
        /**
            * 矢量的高度，当设置了usePositionHeight时，该属性失效
            * 仅支持3D图层
            *
            * @type {FeatureNumberField}
            * @memberof GeometryStyle
            */
        height?: FeatureNumberFunc;
        /**
            * 高度偏移量，在最终的高度上进行偏移，默认10米
            */
        heightOffset: FeatureNumberFunc;
        /**
            * 最小的可见级别，如设置为5，表示zoom低于5的均不展示，默认为0，即不做最小级别限制
            */
        zoomMin?: FeatureNumberFunc;
        /**
            * 最大的可见级别，如设置为10，表示zoom高于10的均不展示，默认为100，即不做最大级别限制
            */
        zoomMax?: FeatureNumberFunc;
        /**
            * text或者image的最小的可见高度，仅对Cesium生效。
            * 暂未启用
            */
        heightMin: FeatureNumberFunc;
        /**
            * text或者image的最大可见高度，仅对Cesium生效
            * 暂未启用
            */
        heightMax: FeatureNumberFunc;
        /**
            * 构建 GeometryStyle
            * @param {IGeometryStyleOptions} options
            * @memberof GeometryStyle
            */
        constructor(options: IGeometryStyleOptions);
        protected _update(options: IGeometryStyleOptions, reinit: boolean): void;
}
/**
    * 点样式构建参数
    *
    * @export
    * @interface IPointStyleOptions
    * @extends {IGeometryStyleOptions}
    */
export interface IPointStyleOptions extends IGeometryStyleOptions {
        /**
            * 是否填充点，默认为是
            *
            * @type {FeatureBooleanField}
            * @memberof IPointStyleOptions
            */
        fill?: FeatureBooleanField;
        /**
            *
            * 描边颜色
            * @type {FeatureColorField}
            * @memberof IPointStyleOptions
            */
        strokeColor?: FeatureColorField;
        /**
            *
            * 描边宽度
            * @type {FeatureNumberField}
            * @memberof IPointStyleOptions
            */
        strokeWidth?: FeatureNumberField;
        /**
            * 描边的线型。默认为正常线段。
            *
            * @type {FeatureNumberArrayField}
            * @memberof IPointStyleOptions
            */
        strokeDashArray?: FeatureNumberArrayField;
        /**
            *
            * 点尺寸，单位是像素，默认5px
            * @type {FeatureNumberField}
            * @memberof IPointStyleOptions
            */
        size?: FeatureNumberField;
        /**
            *
            * 标签样式配置数组
            * @type {IFeatureLabelStyleOptions[]}
            * @memberof IPointStyleOptions
            */
        label?: IFeatureLabelStyleOptions[];
        avoidCollison?: FeatureBooleanField;
        /**
            * 点碰撞检测的缓冲区扩展大小，默认是size，设置该值后=size+buffer，默认[0,0]
            */
        collisionBuffer?: FeatureNumberArrayField;
}
/**
    * 点样式
    *
    * @export
    * @class PointStyle
    * @extends {GeometryStyle}
    */
export class PointStyle extends GeometryStyle<IPointStyleOptions> {
        /**
            * 默认参数
            *
            * @static
            * @type {IPointStyleOptions}
            * @memberof PointStyle
            */
        static DefaultOptions: IPointStyleOptions;
        /**
            * 获取是否填充点，默认为是
            *
            * @type {FeatureBooleanFunc}
            * @memberof PointStyle
            */
        fill?: FeatureBooleanFunc;
        /**
            * 获取描边颜色
            *
            * @type {FeatureColorFunc}
            * @memberof PointStyle
            */
        strokeColor?: FeatureColorFunc;
        /**
            *
            * 获取描边宽度
            * @type {FeatureNumberFunc}
            * @memberof PointStyle
            */
        strokeWidth?: FeatureNumberFunc;
        /**
            * 描边的线型。默认为正常线段。
            *
            * @type {FeatureNumberArrayFunc}
            * @memberof PointStyle
            */
        strokeDashArray?: FeatureNumberArrayFunc;
        /**
            *
            * 获取点尺寸
            * @type {FeatureNumberFunc}
            * @memberof PointStyle
            */
        size?: FeatureNumberFunc;
        /**
            *
            * 标签配置数组
            * @type {FeatureLabelStyle[]}
            * @memberof PointStyle
            */
        label?: FeatureLabelStyle[];
        /**
            * 是否开启点的碰撞检测，默认关闭。
            * 碰撞检测失败的点对应的标签也不会显示。
            *
            * @type {FeatureBooleanFunc}
            * @memberof PointStyle
            */
        avoidCollison?: FeatureBooleanFunc;
        /**
            * 点碰撞检测的缓冲区扩展大小，默认是size，设置该值后=size+buffer，默认[0,0]
            */
        collisionBuffer: FeatureNumberArrayFunc;
        constructor(options: IPointStyleOptions);
        protected updateLabels(options: IPointStyleOptions): void;
        protected _update(options: IPointStyleOptions, reinit: boolean): void;
}
/**
    * 线样式构建参数
    *
    * @export
    * @interface IPolylineStyleOptions
    * @extends {IGeometryStyleOptions}
    */
export interface IPolylineStyleOptions extends IGeometryStyleOptions {
        /**
            *
            * 线宽。单位是像素，默认1px
            * @type {FeatureNumberField}
            * @memberof IPolylineStyleOptions
            */
        width?: FeatureNumberField;
        /**
            *
            * 线型。如[5,2,5]
            * @type {FeatureNumberArrayField}
            * @memberof IPolylineStyleOptions
            */
        dashArray?: FeatureNumberArrayField;
        /**
            * 线段的连接方式，"bevel"|"round"|"miter"
            * 默认是miter，即尖角
            */
        lineJoin?: FeatureStringField;
        /**
            * 线段的端点样式，"butt"|"round"|"square"，默认是 butt
            */
        lineCap?: FeatureStringField;
        /**
            *
            * 标签样式配置数组
            * @type {IFeatureLabelStyleOptions[]}
            * @memberof IPolylineStyleOptions
            */
        label?: IFeatureLabelStyleOptions[];
        /**
            * 标签间隔长度。默认是200px
            *
            * @type {FeatureNumberField}
            * @memberof IPolylineStyleOptions
            */
        labelDistance?: FeatureNumberField;
        /**
            * 三维下线标签的间隔，单位是米，默认100000
            */
        labelDistance3D?: FeatureNumberField;
        /**
            * 标签是否自动翻转，默认为是，如果使用线条上的图片装饰，则一般设置为否
            */
        labelAutoFlip?: FeatureBooleanField;
        /**
            *
            * 是否永远绘制第一个点。一般等值线的是否可以设置为true，默认为false。
            * @type {FeatureBooleanField}
            * @memberof IPolylineStyleOptions
            */
        alwaysDrawFirstLabel?: FeatureBooleanField;
}
/**
    * 线样式
    *
    * @export
    * @class PolylineStyle
    * @extends {GeometryStyle}
    */
export class PolylineStyle extends GeometryStyle<IPolylineStyleOptions> {
        /**
            * 默认参数
            *
            * @static
            * @type {IPolylineStyleOptions}
            * @memberof PolylineStyle
            */
        static DefaultOptions: IPolylineStyleOptions;
        /**
            *
            * 获取线宽
            * @type {FeatureNumberFunc}
            * @memberof PolylineStyle
            */
        width?: FeatureNumberFunc;
        /**
            *
            * 获取线型数组
            * @type {FeatureNumberArrayFunc}
            * @memberof PolylineStyle
            */
        dashArray?: FeatureNumberArrayFunc;
        /**
            * 标签样式数组
            *
            * @type {FeatureLabelStyle[]}
            * @memberof PolylineStyle
            */
        label?: FeatureLabelStyle[];
        /**
            * 标签沿线的间隔，单位是像素。默认是200px长度
            *
            * @type {FeatureNumberArrayFunc}
            * @memberof PolylineStyle
            */
        labelDistance?: FeatureNumberFunc;
        /**
            *3D下标签的间隔，单位是米，默认是100000
            *
            * @type {FeatureNumberFunc}
            * @memberof PolylineStyle
            */
        labelDistance3D: FeatureNumberFunc;
        /**
            * 标签是否自动翻转，默认为是，如果使用线条上的图片装饰，则一般设置为否
            */
        labelAutoFlip: FeatureBooleanFunc;
        /**
            *
            * 获取是否永远绘制第一个标签。默认为否。一般等值线的时候可以设置为true。
            * @type {FeatureBooleanFunc}
            * @memberof PolylineStyle
            */
        alwaysDrawFirstLabel?: FeatureBooleanFunc;
        /**
            * 线段的连接方式，"bevel"|"round"|"miter"
            * 默认是miter，即尖角
            */
        lineJoin: FeatureStringFunc;
        /**
            * 线段的端点样式，"butt"|"round"|"square"，默认是 butt
            */
        lineCap: FeatureStringFunc;
        /**
            * 构建 PolylineStyle
            * @param {IPolylineStyleOptions} options 构建参数
            * @memberof PolylineStyle
            */
        constructor(options: IPolylineStyleOptions);
        protected _update(options: IPolylineStyleOptions, reinit: boolean): void;
}
/**
    * 多边形样式参数
    *
    * @export
    * @interface IPolygonStyleOptions
    * @extends {IGeometryStyleOptions}
    */
export interface IPolygonStyleOptions extends IGeometryStyleOptions {
        /**
            *
            * 线条样式。不配置的时候不显示边界线条
            * @type {IPolylineStyleOptions}
            * @memberof IPolygonStyleOptions
            */
        lineStyle?: IPolylineStyleOptions;
        /**
            *
            * 是否填色。默认是
            * @type {FeatureBooleanField}
            * @memberof IPolygonStyleOptions
            */
        fill?: FeatureBooleanField;
        /**
            *
            * 标签样式
            * @type {IFeatureLabelStyleOptions[]}
            * @memberof IPolygonStyleOptions
            */
        label?: IFeatureLabelStyleOptions[];
        /**
            *
            * 填充图案。当设置了该值时，填充颜色失效。
            * @type {FeatureImageField}
            * @memberof IPolygonStyleOptions
            */
        fillImage?: FeatureImageField;
}
/**
    * 多边形样式
    *
    * @export
    * @class PolygonStyle
    * @extends {GeometryStyle}
    */
export class PolygonStyle extends GeometryStyle<IPolygonStyleOptions> {
        /**
            * 默认参数
            *
            * @static
            * @type {IPolygonStyleOptions}
            * @memberof PolygonStyle
            */
        static DefaultOptions: IPolygonStyleOptions;
        /**
            * 获取线条样式
            *
            * @type {PolylineStyle}
            * @memberof PolygonStyle
            */
        lineStyle?: PolylineStyle;
        /**
            *
            * 获取是否填色
            * @type {FeatureBooleanFunc}
            * @memberof PolygonStyle
            */
        fill?: FeatureBooleanFunc;
        /**
            *
            * 标签样式数组
            * @type {FeatureLabelStyle[]}
            * @memberof PolygonStyle
            */
        label?: FeatureLabelStyle[];
        /**
            * 填充图案。当设置了该值的时候填充颜色失效。
            *
            * @type {FeatureImageFunc}
            * @memberof PolygonStyle
            */
        fillImage?: FeatureImageFunc;
        /**
            * 构建 PolygonStyle
            * @param {IPolygonStyleOptions} options 构建参数
            * @memberof PolygonStyle
            */
        constructor(options: IPolygonStyleOptions);
        protected updateLabels(options: IPolygonStyleOptions): void;
        protected updateLineStyle(options: IPolygonStyleOptions): void;
        protected _update(options: IPolygonStyleOptions, reinit: boolean): void;
}
/**
    * 矢量样式构建参数
    *
    * @export
    * @interface IFeatureStyleOptions
    */
export interface IFeatureStyleOptions {
        /**
            *
            * 点样式
            * @type {IPointStyleOptions}
            * @memberof IFeatureStyleOptions
            */
        point?: IPointStyleOptions;
        /**
            *
            * 线样式
            * @type {IPolylineStyleOptions}
            * @memberof IFeatureStyleOptions
            */
        polyline?: IPolylineStyleOptions;
        /**
            *
            * 多边形样式
            * @type {IPolygonStyleOptions}
            * @memberof IFeatureStyleOptions
            */
        polygon?: IPolygonStyleOptions;
        /**
            * 全局的透明，默认是1。由于是全图层生效，如果通过函数方式设置，被调用时不会有feature传入。
            */
        globalOpacity?: FeatureNumberField;
}
/**
    * 矢量样式
    *
    * @export
    * @class FeatureStyle
    */
export class FeatureStyle extends FeatureBaseClass<IFeatureStyleOptions> {
        static DefaultOptions: IFeatureStyleOptions;
        styleName: string;
        /**
            * 点样式
            *
            * @type {PointStyle}
            * @memberof FeatureStyle
            */
        point?: PointStyle;
        /**
            * 线样式
            *
            * @type {PolylineStyle}
            * @memberof FeatureStyle
            */
        polyline?: PolylineStyle;
        /**
            *
            * 多边形样式
            * @type {PolygonStyle}
            * @memberof FeatureStyle
            */
        polygon?: PolygonStyle;
        /**
            * 全局透明度，默认是1。由于是全图层生效，如果通过函数方式设置，被调用时不会有feature传入。
            */
        globalOpacity: FeatureNumberFunc;
        /**
            * 构建 FeatureStyle
            * @param {IFeatureStyleOptions} options 构建参数
            * @memberof FeatureStyle
            */
        constructor(options: IFeatureStyleOptions);
        protected _update(options: IFeatureStyleOptions, reinit: boolean): void;
}
/**
    * 纯数值数组配置函数，即不依赖外部参数变化
    */
export type PlainNumberArrayFunc = () => number[];
/**
    * 纯数值数组配置项
    */
export type PlainNumberArrayField = number[] | PlainNumberArrayFunc | IStopRulesOptions | string;
export function PlainNumberArrayField2PlainNumberArrayFunc(sourceField: PlainNumberArrayField): PlainNumberArrayFunc;
export interface ICSStyleOptions extends IFeatureStyleOptions {
        analysisValues: PlainNumberArrayField;
        /**
            * 是否带色斑的多边形信息，默认是，如果只要等值线可以设置为false，提高效率
            */
        withShaded?: boolean;
        /**
            * 当数据源是点的时候，用来插值的字段名称，点数据源必传
            */
        interpField?: string;
        /**
            * 当数据源是点的时候，插值后的数据信息，点数据必传
            */
        interpGridOptions?: IGridDataOptions;
        /**
            * 数据中的缺测值。
            * 数据源为格点时，优先用数据中的缺测值，若没有则使用此处定义的值
            * 数据源为点时，如果数据中有缺测值，则需要传入，否则计算结果可能异常
            */
        undef?: number;
}
export class CSStyle extends FeatureStyle {
        static DefaultOptions: ICSStyleOptions;
        constructor(options: ICSStyleOptions);
        analysisValues: PlainNumberArrayFunc;
        withShaded: boolean;
        interpField: string;
        interpGridOptions: IGridDataOptions;
        undef: number;
        protected _update(options: ICSStyleOptions, reinit: boolean): void;
}

/**
    * 格点数据框
    *
    * @export
    * @interface IGridDataBounds
    */
export interface IGridDataBounds {
        /**
            *
            * x最小值
            * @type {number}
            * @memberof IGridDataBounds
            */
        minx: number;
        /**
            *
            * x最大值
            * @type {number}
            * @memberof IGridDataBounds
            */
        maxx: number;
        /**
            *
            * y最小值
            * @type {number}
            * @memberof IGridDataBounds
            */
        miny: number;
        /**
            *
            * y最大值
            * @type {number}
            * @memberof IGridDataBounds
            */
        maxy: number;
        /**
            *
            * z最小值
            * @type {number}
            * @memberof IGridDataBounds
            */
        minz?: number;
        /**
            *
            * z最大值
            * @type {number}
            * @memberof IGridDataBounds
            */
        maxz?: number;
}

/**
    * 格点数据属性
    */
export interface IGridDataOptions {
        xStart?: number;
        yStart?: number;
        xDelta?: number;
        yDelta?: number;
        xSize?: number;
        ySize?: number;
        xEnd?: number;
        yEnd?: number;
        zValues?: number[];
        tCount?: number;
}
/**
    * 检查格点属性，确保有效
    * @param options 要检查的属性
    */
export function ensureGridDataOptions(options: IGridDataOptions): void;
/**
    * 数据框
    */
export interface IDataExtent {
        minLat: number;
        maxLat: number;
        minLon: number;
        maxLon: number;
        maxHeight?: number;
        minHeight?: number;
}
/**
    * 格点边界信息转为数据框
    * @param options 格点边界信息
    * @param precesion 保留小数精度，默认6
    * @returns 格点数据框
    */
export function gridOptionsToExtent(options: IGridDataOptions, precesion?: number): IDataExtent;

/**
    * 一个二维格点场数据，支持线性变换后的数据
    */
export class GridData {
        dataType: GridDataType;
        xSize: number;
        ySize: number;
        algo: number;
        dataScale: number;
        dataOffset: number;
        protected _raw: BufferArray;
        /**
            * 原始数据buffer
            */
        get raw(): BufferArray;
        set raw(data: BufferArray);
        /**
            * 当前数据结构中的每个数字所占用的字节数
            */
        typeLen: number;
        get maxMin(): {
                min: number;
                max: number;
        };
        isUndef: (val: number) => boolean;
        protected readFunc: (val: number) => number;
        protected writeFunc: (val: number) => number;
        /**
            * 创建一个二维格点场数据
            * @param dataType 数据类型
            * @param xSize x长度
            * @param ySize y长度
            * @param data 数据信息
            * @param undef 缺测值
            * @param algo 算法类型，0代表原始值，1代表先缩放后偏移，2代表先偏移后缩放。如果是压缩过的单字节顺序，请在构造之前计算好缩放系数和偏移量。如当使用255方式压缩的时候，scale是 (max-min)/254，offset是min。
            * @param dataScale 数据被缩放的系数，如2表示数据要乘以2之后才是原始值，实际表示原始数据是当前数据的两倍
            * @param dataOffset 数据偏移量，如273.15表示数据要加上273.15才是原始值，实际表示原始值比当前数据大273.15
            */
        constructor(dataType: GridDataType, xSize: number, ySize: number, data?: ArrayBuffer | ArrayLike<number>, undef?: number, algo?: number, dataScale?: number, dataOffset?: number, createEmpty?: boolean);
        /**
            * 获取缺测值
            */
        get undef(): number;
        /**
         *是否使用完全相等的缺测值判断方式，默认为true，否则如果缺测值为正，则大于等于该值的都认为缺测，为负同理。
         *为false时会降低对比效率
         * @memberof GridData
         */
        get useAccurateUndef(): boolean;
        set useAccurateUndef(accurate: boolean);
        /**
            * 重新设置缺测值
            */
        set undef(undef: number);
        /**
            * 获取当前数据的buffer
            */
        getBuffer(): ArrayBuffer;
        protected setTypeLen(): void;
        /**
            * 使用一个维度写入数据
            * @param pos 要写入的位置
            * @param val 要写入的数值
            */
        write1D(pos: number, val: number): GridData;
        /**
            * 使用一个维度读取数据
            * @param pos 要读取的数据位置
            */
        read1D(pos: number): number;
        /**
            * 使用两个维度写入数据
            * @param yPos y位置
            * @param xPos x位置
            * @param val 数值
            */
        write2D(yPos: number, xPos: number, val: number): GridData;
        /**
            * 使用两个维度读取数据
            * @param yPos y位置
            * @param xPos x位置
            */
        read2D(yPos: number, xPos: number): number;
        /**
            * 获取当前数据的一维数组表达（可能造成线程等待）
            * @param reverseY 是否翻转y，默认false
            */
        toNumberArr(reverseY?: boolean): number[];
        /**
            * 以一维方式遍历当前网格，逐点处理
            * @param callback 数据处理函数
            */
        forEach(callback: (val: any, idx: any, raw: any) => void): void;
        forEachRealValue(callback: (val: any, idx: any, raw: any) => void): void;
        /**
            * 以二维方式遍历当前网格，逐点处理（y外层，x内层）
            * @param callback 数据处理函数
            */
        forEachYx(callback: (val: any, yIdx: any, xIdx: any) => void): void;
        /**
            * 获取当前格点场的二维数据表达（可能造成线程等待）
            */
        getYxArr(): number[][];
        /**
            * 使用buffer更新当前格点场
            * @param data 要更新的数据
            */
        update(data: ArrayBuffer): any;
        /**
            * 使用一维数组更新当前格点场
            * @param data 要更新的数据
            */
        update(data: ArrayLike<number>): any;
        /**
            * 使用二维数组更新当前格点场
            * @param data 要更新的数据
            */
        update(data: number[][]): any;
        /**
            * 根据二维索引号获取一维索引号
            * @param yPos y索引号
            * @param xPos x索引号
            */
        getPos(yPos: number, xPos: number): number;
        /**
            * 翻转y
            * @param clone 是否返回镜像，是的时候不修改原始格点场，如果否则直接修改原始格点场，默认返回镜像
            */
        flipY(clone?: boolean): BufferArray;
        updateMaxMin(force?: boolean): {
                min: number;
                max: number;
        };
}
export class GrayImageGridData extends GridData {
        xSize: number;
        ySize: number;
        algo: number;
        dataScale: number;
        dataOffset: number;
        dataType: GridDataType;
        static ReplaceImageAfterDecode: boolean;
        imageSource: TexImageSource;
        constructor(xSize: number, ySize: number, data: TexImageSource, undef?: number, autoLoad?: boolean, algo?: number, dataScale?: number, dataOffset?: number, dataType?: GridDataType);
        get raw(): BufferArray;
        loadImageSource(): HTMLCanvasElement;
}

/**
    * GL格点数值配置字段
    */
export type GridNumberFieldGL = number | string | IStopRulesOptions;
/**
    * 将格点数值配置字段转换为格点数值分级规则
    * @param sourceField 格点数值字段
    * @returns 格点数值分级规则
    */
export function gridNumberFieldGL2GridNumberStopRules(sourceField: GridNumberFieldGL): StopRules;
/**
    * GL图层基础样式对象
    */
export abstract class GridGLBaseStyle<T> extends GridBaseStyle<T> {
        protected _createCacheId(val: any): string;
}
/**
    * WebGL直接色例对象
    *
    * @export
    * @interface IGLBitmapColorScaleOptions
    */
export interface IBitmapColorScaleFieldGLOptions {
        /**
            * 第一个颜色对应的数值
            *
            * @type {number}
            * @memberof IBitmapColorScaleOptions
            */
        min: number;
        /**
            * 最后一个颜色对应的数值
            *
            * @type {number}
            * @memberof IBitmapColorScaleOptions
            */
        max: number;
        /**
            * 颜色位图
            *
            * @type {ImageLike}
            * @memberof IBitmapColorScaleOptions
            */
        colorScale: ImageLike;
}
/**
    * 位图的色例
    *
    * @export
    * @class BitmapColorScale
    * @extends {GridGLBaseStyle<IBitmapColorScaleFieldGLOptions>}
    */
export class BitmapColorScaleGL extends GridGLBaseStyle<IBitmapColorScaleFieldGLOptions> {
        /**
            * 构建 BitmapColorScale
            * @param {IBitmapColorScaleFieldGLOptions} options
            * @memberof GLBitmapColorScale
            */
        constructor(options: IBitmapColorScaleFieldGLOptions);
        /**
            * 最小值
            *
            * @type {number}
            * @memberof GLBitmapColorScale
            */
        min: number;
        /**
            * 最大值
            *
            * @type {number}
            * @memberof GLBitmapColorScale
            */
        max: number;
        /**
            * 色例贴图
            *
            * @type {TexImageSource}
            * @memberof GLBitmapColorScale
            */
        colorScale: TexImageSource;
        protected _update(options: IBitmapColorScaleFieldGLOptions, reinit: boolean): void;
        /**
            * 根据提供的参数创建一个bitmap颜色贴图
            *
            * @static
            * @param {number} width 贴图宽度
            * @param {GridStaticColorFieldGL} colors 颜色表
            * @param {number[]} [positions] 每个颜色对应的百分比，需要跟颜色一一对应，且按照从左到右的顺序，最左侧为0，最右侧为1
            * 在非渐变模式下，第一个表示第一个颜色结束位置的百分比
            * 在渐变模式下，第一个表示第一个颜色开始渐变的位置
            * @return {*}  {HTMLCanvasElement}
            * @memberof BitmapColorScaleGL
            */
        static createColorScale(width: number, gradient: boolean, colors: GridStaticColorFieldGL[], positions?: number[], height?: number): HTMLCanvasElement;
        /**
            * 根据提供的参数动态创建一个bitmap色例
            *
            * @static
            * @param {number} min 最小值
            * @param {number} max 最大值
            * @param {number} width 色例宽度
            * @param {GridStaticColorFieldGL} colors 颜色表
            * @param {number[]} [positions] 每个颜色对应的百分比，需要跟颜色一一对应，且按照从左到右的顺序，最左侧为0，最右侧为1
            * @return {*}  {BitmapColorScaleGL}
            * @memberof BitmapColorScaleGL
            */
        static create(min: number, max: number, width: number, gradient: boolean, colors: GridStaticColorFieldGL[], positions?: number[], height?: number): BitmapColorScaleGL;
        /**
            * 从颜色的分级规则创建一个bitmap色例，不改变原有最大最小值以及区间位置，如果希望重新设置最大最小值可以使用copyFromStopRules
            *
            * @static
            * @param {StopRules} sr 颜色的分级规则
            * @param {number} width 色例宽度
            * @param {boolean} gradient 是否渐变
            * @param {boolean} startIdx 开始的idx
            * @param {boolean} endIdx 结束的idx
            * @memberof BitmapColorScaleGL
            */
        static createFromStopRules(sr: StopRules | IStopRulesOptions, width: number, gradient: boolean, startIdx: number, endIdx: number, height?: number): BitmapColorScaleGL;
        /**
            * 根据调色板配置创建一个bitmap色例，支持重新设定最大最小值，适用于色标移植
            * @param sr
            * @param min
            * @param max
            * @param width
            * @param gradient
            * @param startIdx
            * @param endIdx
            * @param keepSourcePositions
            * @param positions
            * @param height
            * @returns
            */
        static copyFromStopRules(sr: StopRules | IStopRulesOptions, min: number, max: number, width: number, gradient: boolean, startIdx: number, endIdx: number, keepSourcePositions?: boolean, positions?: number[], height?: number): BitmapColorScaleGL;
}
/**
    * GL格点静态颜色字段类型
    */
export type GridStaticColorFieldGL = number | number[] | Spectra | string | {
        r: number;
        g: number;
        b: number;
        a?: number;
} | {
        red: number;
        green: number;
        blue: number;
        alpha?: number;
};
/**
    * GL格点动态颜色字段类型
    */
export type GridColorFieldGL = GridStaticColorFieldGL | IStopRulesOptions;
/**
    * 将静态颜色字段转换为GL支持的颜色数组[0-1]
    * @param sourceField 静态颜色字段
    * @returns 格点颜色数组
    */
export function gridStaticColorFieldGL2Spectra(sourceField: GridStaticColorFieldGL): Spectra;
/**
    * 将静态颜色字段转换为GL支持的颜色数组[0-1]
    * @param sourceField 静态颜色字段
    * @returns 格点颜色数组
    */
export function gridStaticColorFieldGL2NumberArray(sourceField: GridStaticColorFieldGL): number[];
/**
 * 将颜色配置转换为颜色获取函数
 *
 * @export
 * @param {GridColorFieldGL} field 格点颜色字段配置
 * @return {*}  {StopRules} 格点颜色分级规则
 */
export function gridColorFieldGL2StopRules(sourceField: GridColorFieldGL): StopRules;
export type BitmapColorScaleFieldGL = IBitmapColorScaleFieldGLOptions | BitmapColorScaleGL | string;
export function bitmapColorScaleFieldGL2BitmapColorScaleGL(sourceField: BitmapColorScaleFieldGL): BitmapColorScaleGL;
/**
    * GL格点数据填色规则
    */
export enum GridDataGLFillMode {
        /**
            * 不填色
            */
        none = -1,
        /**
            * 格点填色
            */
        bitmap = 0,
        /**
            * 双线性插值渲染
            */
        pixel1 = 1,
        /**
            * 样条插值渲染
            */
        pixel2 = 2,
        /**
            * 双线性插值色斑
            */
        shaded1 = 4,
        /**
            * 样条插值色斑
            */
        shaded2 = 3
}
/**
    * GL格点数据填色规则类型
    */
export type GridDataGLFillType = GridDataGLFillMode | "shaded1" | "pixel1" | "bitmap" | "pixel2" | "shaded2";
/**
    * 填色图层样式配置
    */
export interface IPixelLayerStyleOptions {
        /**
            * Pixel2方式渲染的时候，数据插值的系数，范围为[0,1]，默认为0.5。
            *
            * @type {number}
            * @memberof IPixelLayerStyleOptions
            */
        pixelRatio?: number;
        /**
            *
            * 格点颜色
            * @type {GridColorFieldGL}
            * @memberof IPixelLayerStyleOptions
            */
        fillColor?: GridColorFieldGL;
        /**
            * bitmap形式的色例配置（优先级高于fillColor）
            *
            * @type {BitmapColorScaleFieldGL}
            * @memberof IPixelLayerStyleOptions
            */
        colorScale?: BitmapColorScaleFieldGL;
        /**
            *
            * 显示方式
            * @type {GridDataGLFillMode}
            * @memberof IPixelLayerStyleOptions
            */
        fillMode?: GridDataGLFillType;
        /**
            *
            * 等值线颜色。不配置则使用填色的调色板。
            * @type {GridStaticColorFieldGL}
            * @memberof IPixelLayerStyleOptions
            */
        lineColor?: GridStaticColorFieldGL;
        /**
            * 等值线的粗细，建议不超过5。默认为1.5px。此等值线不支持标签显示，仅供粗略显示或者动画显示。分析级别的等值线可以使用tracingService中的算法生成。
            *
            * @type {number}
            * @memberof IPixelLayerStyleOptions
            */
        lineWidth?: number;
        /**
            *
            * 是否显示等值线。默认为不显示。此等值线不支持标签显示，仅供粗略显示或者动画显示。分析级别的等值线可以使用tracingService中的算法生成。
            * 等值线与fillMode shaded可以完全吻合。
            * @type {boolean}
            * @memberof IPixelLayerStyleOptions
            */
        showLine?: boolean;
        /**
            * 是否启用浮雕效果，默认false
            */
        showRelief?: boolean;
        /**
            * 用于GPU等值线的色斑图模式，默认是shaded1。
            *
            * @type {(GridDataGLFillMode.shaded1|GridDataGLFillMode.shaded2|"shaded1"|"shaded2")}
            * @memberof IPixelLayerStyleOptions
            */
        fillModeForLine?: GridDataGLFillMode.shaded1 | GridDataGLFillMode.shaded2 | "shaded1" | "shaded2";
        /**
            * 当图层中启用线条掩膜时，线条的样式（主要是设置宽度）
            */
        maskLineStyle?: IPolylineStyleOptions;
        /**
            * 全局透明度，如果颜色中有透明度，最终透明度是全局透明度*颜色中的透明度
            */
        globalOpacity?: number;
        /**
            * 是否开启插值优化，默认是，如果出现缺测值和正常值之间的插值异常，请关闭，或者在调色配置中设置合理的分级规则（将一异常数据颜色设置为透明）
            */
        optimizedInterp?: boolean;
}
/**
    * 三维填色图层样式
    *
    * @export
    * @interface IPixelLayerStyle3DOptions
    * @extends {IPixelLayerStyleOptions}
    */
export interface IPixelLayerStyle3DOptions extends IPixelLayerStyleOptions, IGrid3DLayerStyleOptions {
        /**
            * 挤压的系数。默认为1。挤压度=(原始值+offset)*scale
            *
            * @type {number}
            * @memberof IPixelLayerStyle3DOptions
            */
        extrudeScale?: number;
        /**
            * 挤压的偏移量。挤压度=(原始值+offset)*scale。默认为0
            */
        extrudeOffset?: number;
        /**
            * 是否忽略高度值，默认为false
            *
            * @type {boolean}
            * @memberof IPixelLayerStyle3DOptions
            */
        noHeight?: boolean;
        /**
            * 是否开启深度检测，默认开启。
            *
            * @type {boolean}
            * @memberof IPixelLayerStyle3DOptions
            */
        depthTest?: boolean;
        /**
            * 是否不透明，默认false
            *
            * @type {boolean}
            * @memberof IPixelLayerStyle3DOptions
            */
        opaque?: boolean;
        /**
            * 数据的高度，noHeight为ture时该设置失效
            */
        height?: number;
        /**
            * 无效值的填充颜色
            */
        discardColor?: GridStaticColorFieldGL;
        /**
            * 是否关闭光照效果，true表示不启用，默认为true
            */
        flat?: boolean;
        /**
            * vec3,固定的漫反射颜色，会与数据的颜色混合，默认为0，即完全使用数据的颜色
            */
        diffuse?: GridStaticColorFieldGL;
        /**
            * vec3 物体本身的放射光，默认为不发射光
            */
        emission?: GridStaticColorFieldGL;
        /**
            * 镜面（高光）反射参数，[0-1]，默认为0.2，
            */
        specular?: number;
        /**
            * 光泽度，默认5.0，数值越大，高光越趋向于聚集到一点，否则越平铺
            */
        shininess?: number;
}
/**
    * 格点填色图层样式
    */
export class PixelLayerStyle extends GridGLBaseStyle<IPixelLayerStyleOptions> {
        /**
            * Pixel2方式渲染的时候，数据插值的系数，范围为[0,1]，默认为0.5。
            *
            * @type {number}
            * @memberof PixelLayerStyle
            */
        pixelRatio: number;
        /**
            *
            * 格点颜色
            * @type {StopRules}
            * @memberof PixelLayerStyle
            */
        fillColor: StopRules;
        /**
            * bitmap形式的GL色例
            *
            * @type {BitmapColorScaleGL}
            * @memberof PixelLayerStyle
            */
        colorScale: BitmapColorScaleGL;
        /**
            *
            * 显示方式
            * @type {GridDataGLFillMode}
            * @memberof PixelLayerStyle
            */
        fillMode: GridDataGLFillMode;
        /**
            *
            * 等值线颜色。不配置则使用填色的调色板。
            * @type {number[]}
            * @memberof PixelLayerStyle
            */
        lineColor: number[];
        /**
            *  等值线的粗细，建议不超过5。默认为1.5px。此等值线不支持标签显示，仅供粗略显示或者动画显示。分析级别的等值线可以使用tracingService中的算法生成。
            *
            * @type {number}
            * @memberof PixelLayerStyle
            */
        lineWidth: number;
        /**
         *
         * 是否显示等值线。默认为不显示。此等值线不支持标签显示，仅供粗略显示或者动画显示。分析级别的等值线可以使用tracingService中的算法生成。
         * 等值线与fillMode shaded可以完全吻合。
         * @type {boolean}
         * @memberof PixelLayerStyle
         */
        showLine?: boolean;
        /**
            * 是否启用浮雕效果，默认false
            */
        showRelief?: boolean;
        /**
            * 用于GPU等值线的色斑图模式，默认是shaded1。
            *
            * @type {(GridDataGLFillMode.shaded1|GridDataGLFillMode.shaded2)}
            * @memberof PixelLayerStyle
            */
        fillModeForLine?: GridDataGLFillMode.shaded1 | GridDataGLFillMode.shaded2;
        /**
            * 用于掩膜的线条样式（主要是设置线条宽度，可以为函数）
            */
        maskLineStyle: PolylineStyle;
        /**
            * 全局透明度，如果颜色中有透明度，最终透明度是全局透明度*颜色中的透明度
            */
        globalOpacity: number;
        /**
            * 是否开启插值优化，默认是，如果出现缺测值和正常值之间的插值异常，请关闭，或者在调色配置中设置合理的分级规则（将一异常数据颜色设置为透明）
            */
        optimizedInterp: boolean;
        /**
            * 默认配置
            */
        static DefaultOptions: IPixelLayerStyleOptions;
        /**
            * 构建格点填色图层
            * @param options 构造参数
            */
        constructor(options: IPixelLayerStyleOptions);
        /**
            *
            * @param val 格点值。对该图层无效
            * @param cache 是否缓存。对该图层无效。
            * @returns 可直接使用的配置对象
            */
        getPlaneOptions(val: any, cache: any): IPixelLayerStyleOptions;
        protected _update(options: IPixelLayerStyleOptions, reinit: boolean): void;
}
/**
    * 三维填色图层
    *
    * @export
    * @class PixelLayerStyle3D
    * @extends {PixelLayerStyle}
    */
export class PixelLayerStyle3D extends PixelLayerStyle {
        constructor(options: IPixelLayerStyle3DOptions);
        /**
            * 挤压的系数。默认为1。挤压度=(原始值+offset)*scale
            *
            * @type {number}
            * @memberof PixelLayerStyle3D
            */
        extrudeScale: number;
        /**
            * 挤压的偏移量。挤压度=(原始值+offset)*scale。默认为0
            */
        extrudeOffset: number;
        /**
            * z轴拉伸系数，默认为1
            *
            * @type {number}
            * @memberof PixelLayerStyle3D
            */
        zScale: number;
        /**
            * 是否忽略高度值，默认为false
            *
            * @type {boolean}
            * @memberof PixelLayerStyle3D
            */
        noHeight: boolean;
        /**
            * 是否开启深度检测，默认开启。
            *
            * @type {boolean}
            * @memberof PixelLayerStyle3D
            */
        depthTest: boolean;
        /**
            * 是否不透明，默认false
            *
            * @type {boolean}
            * @memberof PixelLayerStyle3D
            */
        opaque: boolean;
        /**
            *高度值，noHeight为true时该设置失效，默认为0
            *
            * @type {number}
            * @memberof PixelLayerStyle3D
            */
        height: number;
        /**
            * 无效值的填充颜色
            */
        discardColor?: number[];
        /**
         * 是否关闭光照效果，true表示不启用，默认为false
         */
        flat?: boolean;
        /**
            * vec3,固定的漫反射颜色，会与数据的颜色混合，默认为0，即完全使用数据的颜色
            */
        diffuse?: Spectra;
        /**
            * vec3 物体本身的放射光，默认为不发射光
            */
        emission?: Spectra;
        /**
            * 镜面（高光）反射参数，[0-1]，默认为0，
            */
        specular?: number;
        /**
            * 光泽度，默认1.0
            */
        shininess?: number;
        static DefaultOptions: IPixelLayerStyle3DOptions;
        protected _update(options: IPixelLayerStyle3DOptions, reinit: boolean): void;
}
/**
    * 图像配置字段
    */
export type GridImageFieldGL = ImageLike;
/**
    * 将图像配置转换为图像获取函数
    *
    * @export
    * @param {GridImageFieldGL} field
    * @return {*}  {GridImageFunc}
    */
export function GridImageFieldGL2TexImage(sourceField: GridImageFieldGL): TexImageSource;
/**
    * 风场图层样式配置参数
    */
export interface IWindLayerStyleOptions {
        /**
            * 颜色规则
            */
        color?: GridColorFieldGL;
        /**
            * 丢弃率
            */
        dropRate?: number;
        /**
            * 消失速度
            */
        fadeRate?: number;
        /**
            * 速度缩放系数
            */
        speedFactor?: number;
        /**
            * 是否使用点渲染，默认为false
            */
        usePoint?: boolean;
        /**
            * 最小透明度，默认为0.5
            */
        minOpacity?: number;
        /**
            * 点的尺寸，默认为1
            */
        pointSize?: number;
        /**
            * 最低可见速度，默认为0
            */
        minSpeed?: number;
        /**
            * 点贴图
            */
        texture?: GridImageFieldGL;
        /**
            * 是否混合颜色。暂未启用。
            */
        mixColor?: boolean;
        /**
            * 粒子透明度是否随着速度减小而减小。暂未启用。
            */
        fadeWithSpeed?: boolean;
        /**
            * 粒子透明度动态变化的参照速度
            */
        fadeSpeedMax?: number;
        /**
            * 速度是否自动与缩放倍数匹配。默认是。
            */
        speedFitZoom?: boolean;
        /**
            * 显示的点的数量是否与缩放倍数匹配。暂未启用。
            */
        countFitZoom?: boolean;
        /**
            * 数据插值方式，默认为fast
            */
        interpMethod?: WindInterpMethodType;
        /**
            * 数据插值的平滑系数，当插值方式为smooth的时候生效。默认为0.8
            */
        interpSmoothFactor?: number;
        /**
            * 如果是渲染波浪，建议设置为0.3，默认为1
            */
        pointDropPosY?: number;
        /**
            * 粒子数量=count*count，count默认是64，即粒子数量默认64*64
            */
        count?: number;
        /**
            * 当使用线条渲染的时候，每帧之间的运动范围超过了这个值视为无效。默认是10，单位是像素（二维）
            * 该配置对点渲染无效
            */
        lineMaxDistance?: number;
        /**
            * 粒子发射的限定区域，默认是数据区域
            */
        particleRegion?: IDataExtent;
        /**
            * 是否显示底层填色
            */
        showPixel?: boolean;
        /**
            * 填色样式
            */
        pixelOptions?: IPixelLayerStyleOptions;
}
export type GridNumberArrayFieldGL = number[] | number;
/**
    * 将数值数组配置项转换为数值数组配置函数
    *
    * @export
    * @param {GridNumberArrayFieldGL} field
    * @return {*}  {GridNumberArrayFunc}
    */
export function GridNumberArrayFieldGL2GridNumberArray(sourceField: GridNumberArrayFieldGL): number[];
/**
    * 三维风场样式参数
    */
export interface IWind3DLayerStyleOptions extends IWindLayerStyleOptions, IGrid3DLayerStyleOptions {
        /**
            * 要展示的z层索引列表，如果值是-1，表示展示所有层，默认0
            */
        zIndex?: number;
        /**
            * 高度轴偏移量，默认0，单位是米
            */
        hgtOffset?: number;
        /**
            * 垂直速度的缩放系数，默认为1
            */
        zSpeedFactor?: number;
        /**
            * 是否仅显示垂直风，默认false
            */
        onlyZ?: boolean;
        /**
            * 当使用分级填色的时候，使用哪个字段来进行匹配，默认是uv计算出来的风速
            */
        colorWith?: "uv" | "w" | "hgt";
}
/**
    * 风场数据插值方式
    */
export enum WindInterpMethodType {
        none = 0,
        fast = 1,
        smooth = 2
}
/**
    * 风场图层样式
    */
export class WindLayerStyle extends GridGLBaseStyle<IWindLayerStyleOptions> {
        /**
            * 默认配置
            */
        static DefaultOptions: IWindLayerStyleOptions;
        /**
            * 颜色规则
            */
        color: StopRules;
        /**
            * 丢弃率
            */
        dropRate: number;
        /**
            * 消失速率
            */
        fadeRate: number;
        /**
            * 速度缩放系数。默认为1
            */
        speedFactor: number;
        /**
            * 是否使用点渲染。默认为false
            */
        usePoint: boolean;
        /**
            * 最小透明度
            */
        minOpacity: number;
        /**
            * 点尺寸
            */
        pointSize: number;
        /**
            * 最小速度
            */
        minSpeed: number;
        /**
            * 点贴图
            */
        texture: TexImageSource;
        /**
            * 是否混合颜色
            */
        mixColor: boolean;
        /**
            * 是否随着速度变化透明度
            */
        fadeWithSpeed: boolean;
        /**
            * 随着速度变化透明度的参数速度
            */
        fadeSpeedMax: number;
        /**
            * 速度是否自适应缩放级别
            */
        speedFitZoom: boolean;
        /**
            * 数量是否自适应缩放级别
            */
        countFitZoom: boolean;
        /**
            * 数据插值方式
            */
        interpMethod: WindInterpMethodType;
        /**
            * 数据插值的平滑系统，当使用smooth方式插值时生效
            */
        interpSmoothFactor: number;
        /**
            * 如果是渲染波浪，建议设置为0.3，默认为1
            */
        pointDropPosY: number;
        /**
         * 粒子数量=count*count，count默认是64，即粒子数量默认64*64
         */
        count: number;
        /**
            * 当使用线条渲染的时候，每帧之间的运动范围超过了这个值视为无效。默认是10，单位是像素
            * 该配置对点渲染无效
            */
        lineMaxDistance: number;
        /**
            * 粒子发射的限定区域，默认是数据区域
            */
        particleRegion: IDataExtent;
        showPixel: boolean;
        pixelOptions: IPixelLayerStyleOptions;
        /**
            * 构建风场图层
            * @param options 构造参数
            */
        constructor(options: IWindLayerStyleOptions);
        protected _update(options: IWindLayerStyleOptions, reinit: boolean): void;
}
/**
    * 三维风场样式
    */
export class Wind3DLayerStyle extends WindLayerStyle {
        static DefaultOptions: IWind3DLayerStyleOptions;
        constructor(options: IWind3DLayerStyleOptions);
        /**
            *Z轴拉伸系数
            *
            * @type {number}
            * @memberof Wind3DLayerStyle
            */
        zScale: number;
        /**
            * 高度轴偏移量，默认0
            *
            * @type {number}
            * @memberof Wind3DLayerStyle
            */
        hgtOffset: number;
        /**
            * 要展示的z层索引列表，如果是-1，表示展示所有层，默认0
            */
        zIndex: number;
        /**
            * 垂直速度的缩放系数，默认为1
            */
        zSpeedFactor: number;
        /**
            * 是否仅显示垂直风，默认false
            *
            * @type {boolean}
            * @memberof Wind3DLayerStyle
            */
        onlyZ: boolean;
        /**
            * 当使用分级填色的时候，使用哪个字段来进行匹配，默认是uv计算出来的风速
            */
        colorWith: "uv" | "w" | "hgt";
        protected _update(options: IWind3DLayerStyleOptions, reinit: boolean): void;
}
export interface IGrid3DLayerStyleOptions {
        zScale?: number;
}
export interface IVolumeLayerStyleOptions extends IGrid3DLayerStyleOptions {
        color: GridColorFieldGL;
        visibleExtent?: IDataExtent;
        samplingRate?: number;
        /**
            * 是否不透明，默认false
            */
        opaque?: boolean;
}
export class VolumeLayerStyle extends GridGLBaseStyle<IVolumeLayerStyleOptions> {
        static DefaultOptions: IVolumeLayerStyleOptions;
        constructor(options: IVolumeLayerStyleOptions);
        color: StopRules;
        zScale: number;
        visibleExtent: IDataExtent;
        samplingRate: number;
        opaque: boolean;
        protected _update(options: IVolumeLayerStyleOptions, reinit: boolean): void;
}
export enum Geometry3DSectionMode {
        /**
            * 不使用剖面
            */
        none = 0,
        /**
            * 使用立方体剖面
            */
        rect = 1,
        /**
            * 使用倾斜剖面
            */
        tilt = 2
}
export interface IGeometry3DLayerStyleOptions {
        /**
            * 是否关闭光照效果，true表示不启用，默认为false
            */
        flat?: boolean;
        /**
            * vec3,固定的漫反射颜色，会与数据的颜色混合，默认为0，即完全使用数据的颜色
            */
        diffuse?: GridStaticColorFieldGL;
        /**
            * vec3 物体本身的放射光，默认为不发射光
            */
        emission?: GridStaticColorFieldGL;
        /**
            * 全局透明度，会与颜色中的透明度混合叠加，默认为1
            */
        globalOpacity?: number;
        /**
            * 镜面（高光）反射参数，[0-1]，默认为0，
            */
        specular?: number;
        /**
            * 光泽度，默认1.0，数值越大，高光越趋向于聚集到一点，否则越平铺
            */
        shininess?: number;
        /**
            *
            * 格点颜色
            * @type {GridColorFieldGL}
            * @memberof IGeometry3DLayerStyleOptions
            */
        fillColor?: GridColorFieldGL;
        /**
            *
            * 显示方式
            * @type {GridDataGLFillMode}
            * @memberof IGeometry3DLayerStyleOptions
            */
        fillMode?: GridDataGLFillType;
        /**
            * bitmap形式的色例配置（优先级高于fillColor）
            *
            * @type {BitmapColorScaleFieldGL}
            * @memberof IGeometry3DLayerStyleOptions
            */
        colorScale?: BitmapColorScaleFieldGL;
        /**
            * 是否不透明，默认true
            *
            * @type {boolean}
            * @memberof IGeometry3DLayerStyleOptions
            */
        opaque?: boolean;
        /**
            * z轴拉伸系数，默认为1
            */
        zScale?: number;
        /**
            * 是否开启深度检测，默认true
            */
        depthTest?: boolean;
        /**
            * 剖面信息，当该项被设置时，启用剖面效果
            */
        section?: {
                minLat?: number;
                maxLat?: number;
                minLon?: number;
                maxLon?: number;
                maxHeight?: number;
                minHeight?: number;
        };
        /**
         * 是否使用斜剖，默认是根据提供的最大最小经纬度和高度形成一个立方体，只显示立方体内的部分
         *
         * 如果使用斜剖，则根据最大最小经纬度的连线进行剖面，同时限制只显示最大最小高度之间的部分
         *
         */
        sectionMode?: Geometry3DSectionMode;
}
export class Geometry3DLayerStyle extends GridGLBaseStyle<IGeometry3DLayerStyleOptions> {
        static DefaultOptions: IGeometry3DLayerStyleOptions;
        /**
         * 是否关闭光照效果，true表示不启用，默认为false
         */
        flat?: boolean;
        /**
            * vec3,固定的漫反射颜色，会与数据的颜色混合，默认为0，即完全使用数据的颜色
            */
        diffuse?: Spectra;
        /**
            * vec3 物体本身的放射光，默认为不发射光
            */
        emission?: Spectra;
        /**
            * 全局透明度，会与颜色中的透明度混合叠加，默认为1
            */
        globalOpacity?: number;
        /**
            * 镜面（高光）反射参数，[0-1]，默认为0，
            */
        specular?: number;
        /**
            * 光泽度，默认1.0
            */
        shininess?: number;
        /**
            *
            * 格点颜色
            * @type {GridColorFieldGL}
            * @memberof Geometry3DLayerStyle
            */
        fillColor?: StopRules;
        /**
            *
            * 显示方式
            * @type {GridDataGLFillMode}
            * @memberof Geometry3DLayerStyle
            */
        fillMode: GridDataGLFillMode;
        /**
            * bitmap形式的色例配置（优先级高于fillColor）
            *
            * @type {BitmapColorScaleFieldGL}
            * @memberof Geometry3DLayerStyle
            */
        colorScale?: BitmapColorScaleGL;
        /**
            * 是否不透明，默认true
            *
            * @type {boolean}
            * @memberof Geometry3DLayerStyle
            */
        opaque: boolean;
        /**
            * 原始数据中z轴被拉伸的系数，默认为1
            *
            * 注意：此zScale与其他图层中的zScale意义有所区别，不是在此图层中直接进行拉伸，而是指传入图层的顶点数据有没有被拉伸过
            *
            * 如果传入图层的顶点数据被拉升了2倍，那么这里就要设置为2，否则在根据格点数据进行高度层填色的时候，会出现填色值与实际值不对应的情况
            *
            * @type {number}
            * @memberof Geometry3DLayerStyle
            */
        zScale: number;
        /**
            * 是否开启深度检测，默认true
            */
        depthTest: boolean;
        /**
            * 剖面信息，当该项被设置时，启用剖面效果
            */
        section: {
                minLat?: number;
                maxLat?: number;
                minLon?: number;
                maxLon?: number;
                maxHeight?: number;
                minHeight?: number;
        };
        /**
         * rect是根据提供的最大最小经纬度和高度形成一个立方体，只显示立方体内的部分
         *
         * tilt则根据最小最大经纬度的连线进行剖面(min->max)，同时限制只显示最大最小高度之间的部分
         *
         * 默认为none
         */
        sectionMode: Geometry3DSectionMode;
        constructor(options: IGeometry3DLayerStyleOptions);
        protected _update(options: IGeometry3DLayerStyleOptions, reinit: boolean): void;
}
export interface IWindArrowLayerStyleOptions extends IGeometry3DLayerStyleOptions {
        /**
            * 基础速度，所有的尺寸变化基于这个基础速度的比例，即head和body配置的大小只baseSpeed时对应的大小，速度大于baseSpeed的箭头会等比例增加，反之减小
            *
            * 默认为5m/s
            */
        baseSpeed?: number;
        /**
            * 箭头的半径，单位是米
            */
        headRadius?: number;
        /**
            * 箭头长度，单位米
            */
        headLength?: number;
        /**
            * 箭头分段数
            */
        headSegments?: number;
        /**
            * 风杆的顶部半径，单位是米
            */
        bodyTopRadius?: number;
        /**
            * 风杆的底部半径，单位是米
            */
        bodyBottomRadius?: number;
        /**
            * 风杆长度，单位是米
            */
        bodyLength?: number;
        /**
            * 风杆的分段数
            */
        bodySegments?: number;
        /**
            * 是否需要更新箭头形状，为了提升性能，箭头形状第一次创建后会被缓存，如果修改了样式后需要重新创建，则需要设置为true
            *
            * 若修改完成后下次再次设置其他样式并更新时，需要将该值再次设置为false，以免降低性能
            */
        shapeNeedsUpdate?: boolean;
        /**
            * 箭头的数量，如果是矢量数据源实际展示数量=min(maxCount,矢量点数量)，如果是格点数据则=maxCount
            *
            * 默认500
            */
        maxCount?: number;
        /**
            * 是否翻转箭头，默认是。
            * true的时候： 风吹来的方向——>风吹走的方向
            */
        flipArrow?: boolean;
        /**
            * 当数据源是矢量的时候，需要设置速度的数据值字段，支持loader。格点数据源无需设置。
            */
        speed?: FeatureNumberField;
        /**
            * 当数据源是矢量的时候，需要设置方向的数据值字段，支持loader，使用角度表示。格点数据源无需设置。
            */
        angle?: FeatureNumberField;
        /**
            * 当数据源是矢量的时候，可以指定矢量显示的高度，如果不指定，则当数据坐标中有高度的时候使用坐标的高度，否则为0
            *
            * 当数据源是格点的时候，也支持设置该值，此时函数收到的feature为null，该值的优先级大于图层构造时generator返回的高度值，如果不设置，则为generator返回的高度值，如果没有返回，则为headLength+bodyLength+100
            *
            * @type {FeatureNumberField}
            * @memberof IWindArrowLayerStyleOptions
            */
        height?: FeatureNumberField;
        /**
            * 当数据源是格点风场时，是否使用风向和风速来进行计算。
            *
            * 如果原始格点数据就是风向和风速的，建议在构建风场provider的时候设置lazyCalc为true，然后该字段设置为true，这样可以避免cpu进行风向风速到uv的计算，提升性能。
            *
            * 默认为false，因为大部分模式出来的格点风场都是使用uv来进行表示
            */
        gridPreferSD?: boolean;
        /**
            * 模型的缩放系数，默认为1，可实时修改大小，无需重新生成模型
            */
        shapeScale?: number;
        /**
            * 垂直速度的放大系数，默认为1
            * 当图层带了垂直速度，且需要增加垂直方向显著性时，可以设置垂直速度的放大系数，建议设置为5左右
            */
        verticalDataScale?: number;
}
export class WindArrowLayerStyle extends Geometry3DLayerStyle {
        static DefaultOptions: IWindArrowLayerStyleOptions;
        constructor(options: IWindArrowLayerStyleOptions);
        /**
            * 基础速度，所有的尺寸变化基于这个基础速度的比例，即head和body配置的大小只baseSpeed时对应的大小，速度大于baseSpeed的箭头会等比例增加，反之减小
            *
            * 默认为5m/s
            */
        baseSpeed: number;
        /**
            * 箭头的半径，单位是米
            */
        headRadius: number;
        /**
            * 箭头长度，单位米
            */
        headLength: number;
        /**
            * 箭头分段数
            */
        headSegments: number;
        /**
            * 风杆的顶部半径，单位是米
            */
        bodyTopRadius: number;
        /**
            * 风杆的底部半径，单位是米
            */
        bodyBottomRadius: number;
        /**
            * 风杆长度，单位是米
            */
        bodyLength: number;
        /**
            * 风杆的分段数
            */
        bodySegments: number;
        /**
            * 是否需要更新箭头形状，为了提升性能，箭头形状第一次创建后会被缓存，如果修改了样式后需要重新创建，则需要设置为true
            *
            * 若修改完成后下次再次设置其他样式并更新时，需要将该值再次设置为false，以免降低性能
            */
        shapeNeedsUpdate: boolean;
        /**
            * 箭头的数量，如果是矢量数据源实际展示数量=min(maxCount,矢量点数量)，如果是格点数据则=maxCount
            *
            * 默认1000
            */
        maxCount: number;
        /**
         * 当数据源是矢量的时候，需要设置速度的数据值字段，支持loader。格点数据源无需设置。矢量数据不支持垂直速度渲染。
         */
        speed: FeatureNumberFunc;
        /**
            * 当数据源是矢量的时候，需要设置方向的数据值字段，支持loader，数据值需要为角度[0-360]。格点数据源无需设置。矢量数据不支持垂直速度渲染
            */
        angle: FeatureNumberFunc;
        /**
            * 当数据源是矢量的时候，可以指定矢量显示的高度，如果不指定，则当数据坐标中有高度的时候使用坐标的高度，否则为10
            *
            * 当数据源是格点的时候，也支持设置该值，此时函数收到的feature为null，该值的优先级大于图层构造时generator返回的高度值，如果不设置，则为generator返回的高度值，如果没有返回，则为headLength+bodyLength+100
            *
            * @type {FeatureNumberFunc}
            * @memberof WindArrowLayerStyle
            */
        height: FeatureNumberFunc;
        /**
            * 当数据源是格点风场时，是否使用风向和风速来进行计算。
            *
            * 如果原始格点数据就是风向和风速的，建议在构建风场provider的时候设置lazyCalc为true，然后该字段设置为true，这样可以避免cpu进行风向风速到uv的计算，提升性能。
            *
            * 默认为false，因为大部分模式出来的格点风场都是使用uv来进行表示
            */
        gridPreferSD: boolean;
        /**
            * 模型的缩放系数，默认为1，可实时修改大小，无需重新生成模型
         */
        shapeScale?: number;
        /**
            * 格点数据垂直速度的放大系数，默认为1
            * 当图层带了垂直速度，且需要增加垂直方向显著性时，可以设置垂直速度的放大系数，建议设置为5左右
            * 矢量数据当前不支持垂直速度
         */
        verticalDataScale?: number;
        /**
            * 是否翻转箭头，默认是。
            * true的时候： 风吹来的方向——>风吹走的方向
         */
        flipArrow?: boolean;
        protected _update(options: IWindArrowLayerStyleOptions, reinit: boolean): void;
}

/**
    * 格点数值函数类型
    */
export type GridNumberFunc = (val: number, vals: number[]) => number;
/**
    * 格点数值字段类型
    */
export type GridNumberField = number | string | GridNumberFunc | IStopRulesOptions;
/**
    * 格点数值类型转换为格点数值函数
    * @param sourceField 格点数值类型
    * @returns 格点数值函数
    */
export function GridNumberField2GridNumberFunc(sourceField: GridNumberField): GridNumberFunc;
/**
    * 字符串配置函数
    */
export type GridStringFunc = (val: number, vals: number[]) => string;
/**
    * 字符串配置项类型
    */
export type GridStringField = string | GridStringFunc | IStopRulesOptions;
/**
    * 将字符串配置项转换为配置函数
    *
    * @export
    * @param {GridStringField} field 字符串配置项
    * @return {*}  {GridStringFunc}
    */
export function GridStringField2GridStringFunc(sourceField: GridStringField): GridStringFunc;
/**
    * 逻辑配置项函数
    */
export type GridBooleanFunc = (val: number, vals: number[]) => boolean;
/**
    * 逻辑配置项类型
    */
export type GridBooleanField = string | GridBooleanFunc | IStopRulesOptions | number | boolean;
/**
    * 将逻辑配置项转换为逻辑配置函数
    *
    * @export
    * @param {GridBooleanField} field
    * @return {*}  {GridBooleanFunc}
    */
export function GridBooleanField2GridBooleanFunc(sourceField: GridBooleanField): GridBooleanFunc;
/**
    * 数值数组配置项函数
    */
export type GridNumberArrayFunc = (val: number, vals: number[]) => number[];
/**
    * 数值数组配置项
    */
export type GridNumberArrayField = number[] | GridNumberArrayFunc | IStopRulesOptions | string;
/**
    * 将数值数组配置项转换为数值数组配置函数
    *
    * @export
    * @param {GridNumberArrayField} field
    * @return {*}  {GridNumberArrayFunc}
    */
export function GridNumberArrayField2GridNumberArrayFunc(sourceField: GridNumberArrayField): GridNumberArrayFunc;
/**
    * 颜色配置项函数
    */
export type GridColorFunc = (val: number, vals: number[]) => Spectra;
/**
    * 可以返回多种颜色表达方式的颜色配置项函数
    */
export type GridColorWrapperFunc = (val: number) => Spectra | number | number[] | string;
/**
    * 颜色字段配置项
    */
export type GridColorField = number | number[] | Spectra | GridColorFunc | GridColorWrapperFunc | IStopRulesOptions | string | {
        r: number;
        g: number;
        b: number;
        a?: number;
} | {
        red: number;
        green: number;
        blue: number;
        alpha?: number;
};
/**
    * 将颜色配置转换为颜色获取函数
    *
    * @export
    * @param {GridColorField} field
    * @return {*}  {GridColorFunc}
    */
export function GridColorField2GridColorFunc(sourceField: GridColorField): GridColorFunc;
/**
    * 图像配置函数
    */
export type GridImageFunc = (val: number, vals: number[]) => ImageLike;
/**
    * 图像配置字段
    */
export type GridImageField = ImageLike | GridImageFunc | IStopRulesOptions | string;
/**
    * 将图像配置转换为图像获取函数
    *
    * @export
    * @param {GridImageField} field
    * @return {*}  {GridImageFunc}
    */
export function GridImageField2GridImageFunc(sourceField: GridImageField): GridImageFunc;
/**
    * 格点基础样式
    */
export abstract class GridBaseStyle<T> extends StyleBaseClass<T> {
        styleName: string;
        protected _createCacheId(val: any): string;
}
/**
    * 格点文本样式构建参数
    */
export interface IGridTextStyleOptions {
        /**
            *
            * 格点颜色。
            * @type {GridColorField}
            * @memberof IGridTextStyleOptions
            */
        color?: GridColorField;
        /**
            *
            *格点字体
            * @type {GridStringField}
            * @memberof IGridTextStyleOptions
            */
        font?: GridStringField;
        /**
            *
            * 格点数值。设置为$或者不设置均默认为格点值。
            * 可以使用loader进行小数精度设置，如 "#decimal?len=1"
            * @type {GridStringField}
            * @memberof IGridTextStyleOptions
            */
        data?: GridStringField;
        /**
            *
            * 旋转角度。
            * @type {GridNumberField}
            * @memberof IGridTextStyleOptions
            */
        angle?: GridNumberField;
        /**
            *
            * 文字描边颜色
            * @type {GridColorField}
            * @memberof IGridTextStyleOptions
            */
        strokeColor?: GridColorField;
        /**
            *
            * 文字描边宽度
            * @type {GridNumberField}
            * @memberof IGridTextStyleOptions
            */
        strokeWidth?: GridNumberField;
        /**
            * 阴影的颜色。默认没有阴影
            *
            * @type {GridColorField}
            * @memberof IGridTextStyleOptions
            */
        shadowColor?: GridColorField;
        /**
            *
            * 阴影的偏移量 [xoffset,yoffset]。默认[10,10]
            * @type {GridNumberArrayField}
            * @memberof IGridTextStyleOptions
            */
        shadowOffset?: GridNumberArrayField;
        /**
            * 阴影模糊效果。默认10
            *
            * @type {GridNumberField}
            * @memberof IGridTextStyleOptions
            */
        shadowBlur?: GridNumberField;
        /**
            *
            * 文字背景色
            * @type {GridColorField}
            * @memberof IGridTextStyleOptions
            */
        backColor?: GridColorField;
        /**
            *
            * 文字背景色是否使用圆形，默认为false
            * @type {GridBooleanField}
            * @memberof IGridTextStyleOptions
            */
        backCircle?: GridBooleanField;
        /**
            * 文字背景色是否使用圆角矩形，默认为false。此配置优先级高于backCircle
            *
            * @type {GridBooleanField}
            * @memberof IGridTextStyleOptions
            */
        backRoundRect?: GridBooleanField;
        /**
            *
            * 文字背景色的圆角像素数，默认为6px
            * @type {GridNumberField}
            * @memberof IGridTextStyleOptions
            */
        backRoundRadius?: GridNumberField;
        /**
            * 背景色的高度
            *
            * @type {GridNumberField}
            * @memberof IGridTextStyleOptions
            */
        backHeight?: GridNumberField;
        /**
            * 背景色描边颜色
            *
            * @type {GridBooleanField}
            * @memberof IGridTextStyleOptions
            */
        backStrokeColor?: GridColorField;
        /**
            * 背景色描边粗细
            *
            * @type {GridNumberField}
            * @memberof IGridTextStyleOptions
            */
        backStrokeWidth?: GridNumberField;
        /**
            *
            * 文字背景色的留边，默认为[5,0]
            * @type {GridNumberArrayField}
            * @memberof IGridTextStyleOptions
            */
        backPadding?: GridNumberArrayField;
        /**
            * 背景色阴影的颜色。默认没有阴影
            *
            * @type {GridColorField}
            * @memberof IGridTextStyleOptions
            */
        backShadowColor?: GridColorField;
        /**
            * 背景色的描边线型。默认正常线段。
            *
            * @type {GridNumberArrayField}
            * @memberof IGridTextStyleOptions
            */
        backStrokeDashArray?: GridNumberArrayField;
        /**
            * 背景色阴影偏移量 [xoffset,yoffset]。默认[3,3]
            *
            * @type {GridNumberArrayField}
            * @memberof IGridTextStyleOptions
            */
        backShadowOffset?: GridNumberArrayField;
        /**
            * 背景色阴影模糊量。默认6
            *
            * @type {GridNumberField}
            * @memberof IGridTextStyleOptions
            */
        backShadowBlur?: GridNumberField;
        /**
            *
            * 文字离中心点的偏移量
            * @type {GridNumberArrayField}
            * @memberof IGridTextStyleOptions
            */
        offset?: GridNumberArrayField;
        /**
            *
            * 文字是否可见，默认可见
            * @type {GridBooleanField}
            * @memberof IGridTextStyleOptions
            */
        visible?: GridBooleanField;
        /**
            * 文本左右对齐方式 start|end|left|center|right。默认是center
            *
            * @type {GridStringField}
            * @memberof IGridTextStyleOptions
            */
        align?: GridStringField;
        /**
            *
            * 文本上下对齐方式 top|bottom|middle|alphabetic|hanging。默认是middle
            * @type {GridStringField}
            * @memberof IGridTextStyleOptions
            */
        baseline?: GridStringField;
        /**
            * 最小的可见级别，如设置为5，表示zoom低于5的均不展示，默认为0，即不做最小级别限制
            * 不支持函数方式设置
            */
        zoomMin?: GridNumberField;
        /**
            * 最大的可见级别，如设置为10，表示zoom高于10的均不展示，默认为25，即不做最大级别限制
            * 不支持函数方式设置
            */
        zoomMax?: GridNumberField;
}
/**
    * 格点文本样式
    */
export class GridTextStyle extends GridBaseStyle<IGridTextStyleOptions> {
        /**
            * 默认配置项
            *
            * @static
            * @type {IGridTextStyleOptions}
            * @memberof GridTextStyle
            */
        static DefaultOptions: IGridTextStyleOptions;
        /**
            *
            * 获取文字内容
            * @type {GridStringFunc}
            * @memberof GridTextStyle
            */
        data: GridStringFunc;
        /**
            *
            * 获取字体
            * @type {GridStringFunc}
            * @memberof GridTextStyle
            */
        font?: GridStringFunc;
        /**
            *
            * 获取文字旋转角度
            * @type {GridNumberFunc}
            * @memberof GridTextStyle
            */
        angle?: GridNumberFunc;
        /**
            *
            * 获取文字颜色
            * @type {GridColorFunc}
            * @memberof GridTextStyle
            */
        color?: GridColorFunc;
        /**
            *
            * 获取文字描边颜色
            * @type {GridColorFunc}
            * @memberof GridTextStyle
            */
        strokeColor?: GridColorFunc;
        /**
            *
            * 获取文字描边宽度
            * @type {GridNumberFunc}
            * @memberof GridTextStyle
            */
        strokeWidth?: GridNumberFunc;
        /**
            * 阴影的颜色。默认没有阴影
            *
            * @type {GridColorFunc}
            * @memberof GridTextStyle
            */
        shadowColor?: GridColorFunc;
        /**
            *
            * 阴影的偏移量 [xoffset,yoffset]。默认[3,3]
            * @type {GridNumberArrayFunc}
            * @memberof GridTextStyle
            */
        shadowOffset?: GridNumberArrayFunc;
        /**
            * 阴影模糊效果。默认6
            *
            * @type {GridNumberFunc}
            * @memberof GridTextStyle
            */
        shadowBlur?: GridNumberFunc;
        /**
            *
            * 获取文字背景色
            * @type {GridColorFunc}
            * @memberof GridTextStyle
            */
        backColor?: GridColorFunc;
        /**
            *
            * 获取文字背景色是否使用圆形
            * @type {GridBooleanFunc}
            * @memberof GridTextStyle
            */
        backCircle?: GridBooleanFunc;
        /**
            * 获取文字是否使用圆角矩形
            *
            * @type {GridBooleanFunc}
            * @memberof GridTextStyle
            */
        backRoundRect?: GridBooleanFunc;
        /**
            *
            * 获取文字圆角像素数。默认为6px
            * @type {GridNumberFunc}
            * @memberof GridTextStyle
            */
        backRoundRadius?: GridNumberFunc;
        /**
            * 获取背景色的高度
            *
            * @type {GridNumberFunc}
            * @memberof GridTextStyle
            */
        backHeight?: GridNumberFunc;
        /**
            * 获取背景色描边颜色
            *
            * @type {GridColorFunc}
            * @memberof GridTextStyle
            */
        backStrokeColor?: GridColorFunc;
        /**
            *
            * 获取背景色描边线型。默认为正常线段。
            * @type {GridNumberArrayFunc}
            * @memberof GridTextStyle
            */
        backStrokeDashArray?: GridNumberArrayFunc;
        /**
            * 获取背景色描边粗细
            *
            * @type {GridNumberFunc}
            * @memberof GridTextStyle
            */
        backStrokeWidth?: GridNumberFunc;
        /**
            *
            * 获取文字背景色的留边。默认为[5,0]
            * @type {GridNumberArrayFunc}
            * @memberof GridTextStyle
            */
        backPadding?: GridNumberArrayFunc;
        /**
            * 背景色阴影的颜色。默认没有阴影
            *
            * @type {GridColorFunc}
            * @memberof GridTextStyle
            */
        backShadowColor?: GridColorFunc;
        /**
            * 背景色阴影偏移量 [xoffset,yoffset]。默认[3,3]
            *
            * @type {GridNumberArrayFunc}
            * @memberof GridTextStyle
            */
        backShadowOffset?: GridNumberArrayFunc;
        /**
            * 背景色阴影模糊量。默认6
            *
            * @type {GridNumberFunc}
            * @memberof GridTextStyle
            */
        backShadowBlur?: GridNumberFunc;
        /**
            *
            * 获取文字距离中心点的偏移量
            * @type {GridNumberArrayFunc}
            * @memberof GridTextStyle
            */
        offset?: GridNumberArrayFunc;
        /**
            *
            * 获取文字是否可见
            * @type {GridBooleanFunc}
            * @memberof GridTextStyle
            */
        visible?: GridBooleanFunc;
        /**
            * 是否避免遮盖，默认为false
            *
            * @type {GridBooleanField}
            * @memberof IGridTextStyleOptions
            */
        avoidCollison: GridBooleanFunc;
        /**
            * 获取文本左右对齐方式 start|end|left|center|right。默认是center
            *
            * @type {GridStringFunc}
            * @memberof GridTextStyle
            */
        align?: GridStringFunc;
        /**
            *
            * 获取文本上下对齐方式 top|bottom|middle|alphabetic|hanging。默认是middle
            * @type {GridStringFunc}
            * @memberof GridTextStyle
            */
        baseline?: GridStringFunc;
        /**
            * 最小的可见级别，如设置为5，表示zoom低于5的均不展示，默认为0，即不做最小级别限制
            * 不支持函数方式设置
            */
        zoomMin: GridNumberFunc;
        /**
            * 最大的可见级别，如设置为10，表示zoom高于10的均不展示，默认为100，即不做最大级别限制
            * 不支持函数方式设置
            */
        zoomMax: GridNumberFunc;
        /**
            * 构建格点文本样式
            * @param options 格点文本样式参数
            */
        constructor(options: IGridTextStyleOptions);
        protected _update(options: IGridTextStyleOptions, reinit: boolean): void;
}
/**
    * 格点图片样式参数
    */
export interface IGridImageStyleOptions {
        /**
             *
             * 图片绘制的大小，默认[32,32]
             * @type {GridNumberArrayField}
             * @memberof IGridImageStyleOptions
             */
        size?: GridNumberArrayField;
        /**
            *
            * 图片绘制的角度，默认0，单位是弧度。如果单位是度，需要设置angleDegree属性为true
            * @type {GridNumberField}
            * @memberof IGridImageStyleOptions
            */
        angle?: GridNumberField;
        /**
            *
            * 图像内容，必选项
            * @type {GridImageField}
            * @memberof IGridImageStyleOptions
            */
        data: GridImageField;
        /**
            * 图片的颜色。如果设置，将对图片进行重新填色。
            *
            * @type {GridColorField}
            * @memberof IGridImageStyleOptions
            */
        color?: GridColorField;
        /**
            *
            * 图像距离点位置的偏移量
            * @type {GridNumberArrayField}
            * @memberof IGridImageStyleOptions
            */
        offset?: GridNumberArrayField;
        /**
            *
            * 图像是否可见，默认为是
            * @type {GridBooleanField}
            * @memberof IGridImageStyleOptions
            */
        visible?: GridBooleanField;
        /**
            * 阴影的颜色。默认没有阴影
            *
            * @type {GridColorField}
            * @memberof IGridImageStyleOptions
            */
        shadowColor?: GridColorField;
        /**
            *
            * 阴影的偏移量 [xoffset,yoffset]。默认[10,10]
            * @type {GridNumberArrayField}
            * @memberof IGridImageStyleOptions
            */
        shadowOffset?: GridNumberArrayField;
        /**
            * 阴影模糊效果。默认10
            *
            * @type {GridNumberField}
            * @memberof IGridImageStyleOptions
            */
        shadowBlur?: GridNumberField;
        /**
            * 最小的可见级别，如设置为5，表示zoom低于5的均不展示，默认为0，即不做最小级别限制
            * 不支持函数方式设置
            */
        zoomMin?: GridNumberField;
        /**
            * 最大的可见级别，如设置为10，表示zoom高于10的均不展示，默认为100，即不做最大级别限制
            * 不支持函数方式设置
            */
        zoomMax?: GridNumberField;
}
/**
    * 格点图片样式
    */
export class GridImageStyle extends GridBaseStyle<IGridImageStyleOptions> {
        /**
            *尺寸获取函数
            *
            * @type {GridNumberArrayFunc}
            * @memberof GridImageStyle
            */
        size?: GridNumberArrayFunc;
        /**
            *旋转角度获取函数，单位是弧度，如果配置为从字段中获取，可以使用 $字段名的方式，如果字段中单位是角度，可以使用loader进行转换，如 $angle#degree2arc
            *
            * @type {GridNumberFunc}
            * @memberof GridImageStyle
            */
        angle?: GridNumberFunc;
        /**
            *图像内容获取函数
            *
            * @type {GridImageFunc}
            * @memberof GridImageStyle
            */
        data: GridImageFunc;
        /**
            * 图片的颜色。如果设置，将对图片重新上色。
            *
            * @type {GridColorFunc}
            * @memberof GridImageStyle
            */
        color?: GridColorFunc;
        /**
            *
            * 偏移量获取函数
            * @type {GridNumberArrayFunc}
            * @memberof GridImageStyle
            */
        offset?: GridNumberArrayFunc;
        /**
            *
            * 是否可见获取函数
            * @type {GridBooleanFunc}
            * @memberof GridImageStyle
            */
        visible?: GridBooleanFunc;
        /**
            * 阴影的颜色。默认没有阴影
            *
            * @type {GridColorFunc}
            * @memberof GridImageStyle
            */
        shadowColor?: GridColorFunc;
        /**
            *
            * 阴影的偏移量 [xoffset,yoffset]。默认[10,10]
            * @type {GridNumberArrayFunc}
            * @memberof GridImageStyle
            */
        shadowOffset?: GridNumberArrayFunc;
        /**
            * 阴影模糊效果。默认10
            *
            * @type {GridNumberFunc}
            * @memberof GridImageStyle
            */
        shadowBlur?: GridNumberFunc;
        /**
            * 最小的可见级别，如设置为5，表示zoom低于5的均不展示，默认为0，即不做最小级别限制
            * 不支持函数方式设置
            */
        zoomMin: GridNumberFunc;
        /**
            * 最大的可见级别，如设置为10，表示zoom高于10的均不展示，默认为100，即不做最大级别限制
            * 不支持函数方式设置
            */
        zoomMax: GridNumberFunc;
        /**
            *
            * 默认配置项
            * @static
            * @type {IGridImageStyleOptions}
            * @memberof GridImageStyle
            */
        static DefaultOptions: IGridImageStyleOptions;
        /**
            * 构建 ImageStyle.
            * @param {IGridImageStyleOptions} options 构造器参数
            * @memberof GridImageStyle
            */
        constructor(options: IGridImageStyleOptions);
        protected _update(options: IGridImageStyleOptions, reinit: boolean): void;
}
/**
    * 格点标签样式
    */
export interface IGridLabelStyleOptions {
        /**
            * 绘制的最小间隔，默认[50,50]
            */
        interval?: number[];
        /**
         * 打点的相对位置，默认[0.5,0.5]，左上角[0,0]
         */
        pos?: number[];
        /**
            * 图片样式
            */
        image?: IGridImageStyleOptions;
        /**
            * 文本样式
            */
        text?: IGridTextStyleOptions;
}
/**
    * 格点标签样式
    */
export class GridLabelStyle extends GridBaseStyle<IGridLabelStyleOptions> {
        /**
            * 标签间隔
            */
        interval: number[];
        /**
            * 打点的相对位置，默认[0.5,0.5]，左上角[0,0]，最大[1,1]
            */
        pos: number[];
        /**
            * 图片样式
            */
        image: GridImageStyle;
        /**
            * 文本样式
            */
        text: GridTextStyle;
        /**
            * 默认属性
            */
        static DefaultOptions: IGridLabelStyleOptions;
        /**
            * 构建格点标签
            * @param options 格点标签构建参数
            */
        constructor(options: IGridLabelStyleOptions);
        protected _update(options: IGridLabelStyleOptions, reinit: boolean): void;
}

export interface IFeatureVolumeStyleOptions {
        xDelta?: FeatureNumberField;
        yDelta?: FeatureNumberField;
        height: FeatureNumberField;
        color?: FeatureColorField;
        visible?: FeatureBooleanField;
}
export class FeatureVolumeStyle extends FeatureBaseClass<IFeatureVolumeStyleOptions> {
        static DefaultOptions: IFeatureVolumeStyleOptions;
        xDelta: FeatureNumberFunc;
        yDelta: FeatureNumberFunc;
        height: FeatureNumberFunc;
        color: FeatureColorFunc;
        visible: FeatureBooleanFunc;
        constructor(options: IFeatureVolumeStyleOptions);
        protected _update(options: IFeatureVolumeStyleOptions, reinit: boolean): void;
}
export interface IFeatureLabelStyle3DOptions extends IFeatureLabelStyleOptions {
        volume?: IFeatureVolumeStyleOptions;
}
export class FeatureLabelStyle3D extends FeatureLabelStyle {
        volume: FeatureVolumeStyle;
        protected _update(options: IFeatureLabelStyle3DOptions, reinit: boolean): void;
}
export interface IPointStyle3DOptions extends IPointStyleOptions {
        label?: IFeatureLabelStyle3DOptions[];
}
export class PointStyle3D extends PointStyle {
        label: FeatureLabelStyle3D[];
        constructor(options: IPointStyle3DOptions);
        protected updateLabels(options: IPointStyle3DOptions): void;
}
export interface IPolylineStyle3DOptions extends IPolylineStyleOptions {
        simpleLine?: FeatureBooleanField;
        glowColor?: FeatureColorField;
        glowPower?: FeatureNumberField;
        taperPower?: FeatureNumberField;
        dashColor?: FeatureColorField;
        strokeColor?: FeatureColorField;
        strokeWidth?: FeatureNumberField;
}
export class PolylineStyle3D extends PolylineStyle {
        static DefaultOptions: IPolylineStyle3DOptions;
        simpleLine: FeatureBooleanFunc;
        glowColor: FeatureColorFunc;
        glowPower: FeatureNumberFunc;
        taperPower: FeatureNumberFunc;
        dashColor: FeatureColorFunc;
        strokeColor: FeatureColorFunc;
        strokeWidth: FeatureNumberFunc;
        /**
            * 是否贴地，该属性针对当前图层为整体生效，默认false
            */
        clampToGround?: boolean;
        constructor(options: IPolylineStyle3DOptions);
        protected _update(options: IPolylineStyle3DOptions, reinit: boolean): void;
}
export interface IPolygonStyle3DOptions extends IPolygonStyleOptions {
        label?: IFeatureLabelStyle3DOptions[];
        extrudeHeight?: FeatureNumberField;
        closeTop?: FeatureBooleanField;
        closeBottom?: FeatureBooleanField;
        strokeColor?: FeatureColorField;
        lineStyle?: IPolylineStyle3DOptions;
}
export class PolygonStyle3D extends PolygonStyle {
        static DefaultOptions: IPolygonStyle3DOptions;
        label: FeatureLabelStyle3D[];
        extrudeHeight: FeatureNumberFunc;
        closeTop: FeatureBooleanFunc;
        closeBottom: FeatureBooleanFunc;
        strokeColor: FeatureColorFunc;
        lineStyle: PolylineStyle3D;
        constructor(options: IPolygonStyle3DOptions);
        protected _update(options: IPolygonStyle3DOptions, reinit: boolean): void;
        protected updateLineStyle(options: IPolygonStyleOptions): void;
        protected updateLabels(options: IPolygonStyle3DOptions): void;
}
export interface IFeatureStyle3DOptions extends IFeatureStyleOptions {
        point?: IPointStyle3DOptions;
        polyline?: IPolylineStyle3DOptions;
        polygon?: IPolygonStyle3DOptions;
}
export class FeatureStyle3D extends FeatureStyle {
        point: PointStyle3D;
        polyline: PolylineStyle3D;
        polygon: PolygonStyle3D;
        constructor(options: IFeatureStyle3DOptions);
        protected _update(options: IFeatureStyle3DOptions, reinit: boolean): void;
}
/**
    * 三维PointImageLayer专用样式
    */
export interface IPointImageStyle3DOptions {
        /**
            * 贴图，可以是一个图集
            * 如果是一个url，请使用资源管理器预先加载完成后再传入
            */
        texture: GridImageFieldGL;
        /**
            * 贴图的行列数，默认1x1
            * 一个图集中的所有贴图尺寸必须完全一致，相互之间没有空隙
            */
        textureDim?: GridNumberArrayFieldGL;
        /**
            * 单个贴图的显示大小，默认32
            */
        size?: number;
        /**
            * 随机缩放的系数最大值，默认是1，即不缩放
            * 如果不为1，表示启用随机缩放，放大时候的最大倍数为配置的值，最小值为1.0/配置值
            */
        scaleMax?: number;
}
export class PointImageStyle3D extends StyleBaseClass<IPointImageStyle3DOptions> {
        static DefaultOptions: IPointImageStyle3DOptions;
        constructor(options: IPointImageStyle3DOptions);
        /**
            * 贴图，可以是一个图集
            * 如果是一个url，请使用资源管理器预先加载完成后再传入
            */
        texture: TexImageSource;
        /**
            * 贴图的行列数，默认1x1
            * 一个图集中的所有贴图尺寸必须完全一致，相互之间没有空隙
            */
        textureDim: number[];
        /**
            * 单个贴图的显示大小，默认32
            */
        size: number;
        /**
            * 随机缩放的系数最大值，默认是1，即不缩放
            * 如果不为1，表示启用随机缩放，放大时候的最大倍数为配置的值，最小值为1.0/配置值
            */
        scaleMax: number;
        protected _update(options: IPointImageStyle3DOptions, reinit: boolean): void;
        protected _createCacheId(val: any): string;
}
export class CSStyle3D extends FeatureStyle3D {
        static DefaultOptions: ICSStyleOptions;
        constructor(options: ICSStyleOptions);
        analysisValues: PlainNumberArrayFunc;
        withShaded: boolean;
        interpField: string;
        interpGridOptions: IGridDataOptions;
        undef: number;
        protected _update(options: ICSStyleOptions, reinit: boolean): void;
}

/**
    * QE的基础图层定义
    */
export interface IQELayer {
        setVisible(visible: boolean): this;
        isVisible(): boolean;
}
/**
    * 支持动画的图层接口定义
    */
export interface IAnimationableLayer {
        /**
            * 清除上一关键帧的数据
            */
        clearPreDataSource(): any;
}
/**
    * 可以从配置构建的图层的数据源配置参数
    *
    * @export
    * @interface ILayerDataConfig
    */
export interface ILayerDataConfig {
        /**
            * 数据类型，用于确定provider
            *
            * @type {string}
            * @memberof ILayerDataConfig
            */
        type: string;
        /**
            * 数据源。如 u-500#res，表示从资源管理器中获取名为u-500的资源
            *
            * @type {*}
            * @memberof ILayerDataConfig
            */
        source: any;
        /**
            * 构建数据源provider的参数
            *
            * @type {*}
            * @memberof ILayerDataConfig
            */
        options?: any;
}
export interface IWindLayerDataConfig extends ILayerDataConfig {
        /**
            * 第二个数据源，v分量或者风向的资源
            *
            * @type {*}
            * @memberof IWindLayerDataConfig
            */
        source2: ILayerDataConfig;
        /**
            * u分量或者风速的资源
            *
            * @type {ILayerDataConfig}
            * @memberof IWindLayerDataConfig
            */
        source: ILayerDataConfig;
}
/**
    * 创建图层的配置。用于从配置文件创建初始图层
    *
    * @export
    * @interface ILayersConfig
    */
export interface ILayerConfig {
        /**
            * 图层类型
            *
            * @type {string}
            * @memberof ILayerConfig
            */
        type: string;
        /**
            * 创建图层的构建参数
            *
            * @type {*}
            * @memberof ILayerConfig
            */
        options?: any;
        /**
            * 图层的绘制参数
            *
            * @type {*}
            * @memberof ILayerConfig
            */
        style: any;
        /**
            * 图层的数据源
            *
            * @type {*}
            * @memberof ILayerConfig
            */
        data: ILayerDataConfig | IWindLayerDataConfig;
        /**
            * 图层的名称
            */
        name?: string;
}
/**
    * 可被配置文件创建的图层
    *
    * @export
    * @interface IConfigCreatableLayer
    */
export interface IConfigCreatableLayer extends IQELayer {
        setDrawOptions(drawOptions: any): any;
        setDataSource(dataSource: any): any;
}
/**
    * 图层创建器
    *
    * @export
    * @interface ILayerCreator
    */
export interface ILayerCreator {
        /**
            * 根据类型和图层参数创建一个图层
            *
            * @param {string} type 图层类型。可以参考要具体创建的图层的静态qeName属性
            * @param {ILayerConfig} config 图层创建参数
            * @return {IConfigCreatableLayer}  {IConfigCreatableLayer}
            * @memberof ILayerCreator
            */
        create(config: ILayerConfig): IConfigCreatableLayer;
        /**
            * 注册一个可被创建的图层
            *
            * @param {string} type 图层的类型key，用于配置文件中的type。默认使用传入的图层的静态qeName属性。
            * @param {{ new(options?: any): IConfigCreatableLayer }} ctor 图层类名对象
            * @memberof ILayerCreator
            */
        register(ctor: {
                new (options?: any): IConfigCreatableLayer;
        }, type?: string): any;
        /**
            * 注册数据Provider的构建器。默认使用传入的图层的静态qeName属性。
            *
            * @param {{ new(...args: any): any }} ctor 数据访问器的类名对象
            * @param {string} [type]
            * @memberof ILayerCreator
            */
        registerProviderCreator(ctor: {
                new (...args: any): any;
        }, type?: string): any;
        /**
            * 根据数据源信息创建数据访问器
            *
            * @param {ILayerDataConfig} layerData
            * @memberof ILayerCreator
            */
        createProvider(layerData: ILayerDataConfig): any;
}
export interface IBatchLayerConfig {
        /**
            * 资源文件路径
            */
        resources?: string | RequestInfo | IResourceConfigPredefined;
        /**
            * 资源管理器实例。默认使用共享的资源管理器
            */
        resourceService?: ResourceService;
        /**
            * 初始化图层
            *
            * @type {ILayerConfig[]}
            * @memberof LMapOptions
            */
        layers?: ILayerConfig[];
        /**
            * 图层创建器
            *
            * @type {ILayerCreator}
            * @memberof LMapOptions
            */
        layerCreator?: ILayerCreator;
}
/**
    * 通用的图层创建器，用于从配置创建图层
    *
    * @export
    * @class LayerCreator
    * @implements {ILayerCreator}
    */
export class LayerCreator implements ILayerCreator {
        protected creators: {
                [key: string]: {
                        new (options?: any): IConfigCreatableLayer;
                };
        };
        protected providerCreators: {
                [key: string]: {
                        new (...args: any): any;
                };
        };
        constructor();
        create(config: ILayerConfig): IConfigCreatableLayer;
        createProvider(layerData: ILayerDataConfig): any;
        registerProviderCreator(ctor: {
                new (...args: any): any;
        }, type?: string): void;
        register(ctor: {
                new (options?: any): IConfigCreatableLayer;
        }, type?: string): void;
}
/**
    * 批量加载资源和图层
    *
    * @export
    * @param {IBatchLayerConfig|string} options 资源和图层的配置对象或者路径
    * @param {(layer: IConfigCreatableLayer) => void} created 单个图层创建完成后的回调
    * @param {(layers: any[]) => void} [completed] 全部图层创建完成后的回调
    */
export function loadResourcesAndLayers(options: IBatchLayerConfig | string, created: (layer: IConfigCreatableLayer) => void, completed?: (layers: any[]) => void): void;
/**
    * 默认的通用图层创建器
    */
export const layerCreator: LayerCreator;

/**
    * 抽象的地图接口
    *
    * @export
    * @interface IMap
    */
export interface IMap {
        /**
            * 地图上用于绘制的工具服务
            *
            * @type {MapToolService}
            * @memberof IMap
            */
        toolService: MapToolService;
        /**
            * 通过图层配置进行图层的批量载入
            *
            * @param {IBatchLayerConfig} config 配置对象或者配置对象的绝对路径
            * @memberof IMap
            */
        addLayerByConfig(config: IBatchLayerConfig | string): Promise<IConfigCreatableLayer[]>;
}

/**
    * 地图工具接口定义
    *
    * @export
    * @interface IMapTool
    */
export interface IMapTool {
        /**
            * 触发该工具
            *
            * @param {*} [properties]
            * @memberof IMapTool
            */
        begin(properties?: any): any;
        /**
            * 结束该工具
            *
            * @memberof IMapTool
            */
        end(): any;
        /**
            * 清除该工具产生的内容
            *
            * @memberof IMapTool
            */
        clear(): any;
        /**
            * 设置工具所属地图
            *
            * @param {*} map
            * @memberof IMapTool
            */
        setMap(map: any): any;
        /**
            * 手动往工具中增加一个点
            * @param pt 点坐标
            */
        addPoint(pt: number[]): any;
}

/**
    * 系统内置的颜色
    */
export const predefinedColorNames: {
        transparent: number[];
        aliceblue: number[];
        antiquewhite: number[];
        aqua: number[];
        aquamarine: number[];
        azure: number[];
        beige: number[];
        bisque: number[];
        black: number[];
        blanchedalmond: number[];
        blue: number[];
        blueviolet: number[];
        brown: number[];
        burlywood: number[];
        cadetblue: number[];
        chartreuse: number[];
        chocolate: number[];
        coral: number[];
        cornflowerblue: number[];
        cornsilk: number[];
        crimson: number[];
        cyan: number[];
        darkblue: number[];
        darkcyan: number[];
        darkgoldenrod: number[];
        darkgray: number[];
        darkgreen: number[];
        darkgrey: number[];
        darkkhaki: number[];
        darkmagenta: number[];
        darkolivegreen: number[];
        darkorange: number[];
        darkorchid: number[];
        darkred: number[];
        darksalmon: number[];
        darkseagreen: number[];
        darkslateblue: number[];
        darkslategray: number[];
        darkslategrey: number[];
        darkturquoise: number[];
        darkviolet: number[];
        deeppink: number[];
        deepskyblue: number[];
        dimgray: number[];
        dimgrey: number[];
        dodgerblue: number[];
        firebrick: number[];
        floralwhite: number[];
        forestgreen: number[];
        fuchsia: number[];
        gainsboro: number[];
        ghostwhite: number[];
        gold: number[];
        goldenrod: number[];
        gray: number[];
        green: number[];
        greenyellow: number[];
        grey: number[];
        honeydew: number[];
        hotpink: number[];
        indianred: number[];
        indigo: number[];
        ivory: number[];
        khaki: number[];
        lavender: number[];
        lavenderblush: number[];
        lawngreen: number[];
        lemonchiffon: number[];
        lightblue: number[];
        lightcoral: number[];
        lightcyan: number[];
        lightgoldenrodyellow: number[];
        lightgray: number[];
        lightgreen: number[];
        lightgrey: number[];
        lightpink: number[];
        lightsalmon: number[];
        lightseagreen: number[];
        lightskyblue: number[];
        lightslategray: number[];
        lightslategrey: number[];
        lightsteelblue: number[];
        lightyellow: number[];
        lime: number[];
        limegreen: number[];
        linen: number[];
        magenta: number[];
        maroon: number[];
        mediumaquamarine: number[];
        mediumblue: number[];
        mediumorchid: number[];
        mediumpurple: number[];
        mediumseagreen: number[];
        mediumslateblue: number[];
        mediumspringgreen: number[];
        mediumturquoise: number[];
        mediumvioletred: number[];
        midnightblue: number[];
        mintcream: number[];
        mistyrose: number[];
        moccasin: number[];
        navajowhite: number[];
        navy: number[];
        oldlace: number[];
        olive: number[];
        olivedrab: number[];
        orange: number[];
        orangered: number[];
        orchid: number[];
        palegoldenrod: number[];
        palegreen: number[];
        paleturquoise: number[];
        palevioletred: number[];
        papayawhip: number[];
        peachpuff: number[];
        peru: number[];
        pink: number[];
        plum: number[];
        powderblue: number[];
        purple: number[];
        rebeccapurple: number[];
        red: number[];
        rosybrown: number[];
        royalblue: number[];
        saddlebrown: number[];
        salmon: number[];
        sandybrown: number[];
        seagreen: number[];
        seashell: number[];
        sienna: number[];
        silver: number[];
        skyblue: number[];
        slateblue: number[];
        slategray: number[];
        slategrey: number[];
        snow: number[];
        springgreen: number[];
        steelblue: number[];
        tan: number[];
        teal: number[];
        thistle: number[];
        tomato: number[];
        turquoise: number[];
        violet: number[];
        wheat: number[];
        white: number[];
        whitesmoke: number[];
        yellow: number[];
        yellowgreen: number[];
};
/**
    *颜色对象，rgb使用0-255，a使用0-1
    *改造自：https://github.com/avp/spectra
    * @export
    * @class Spectra
    */
export class Spectra {
        toString: () => string;
        toJSON: () => string;
        /**
            * 构建Spectra颜色
            * @param arg 构建参数，rgb参数范围0-255，a 0-1
            * 支持
            * 内置颜色名称 predefinedColorNames
            * css
            * {r:number,g:number,b:number,a?:number}
            * {red:number,green:number,blue:number,alplh?:number}
            * [255,255,255,1]
            * @returns
            */
        constructor(arg: any);
        /**
            * 获取或设置红色值
            * @param arg 颜色值
            * @returns
            */
        red(arg?: any): any;
        /**
            * 获取或设置绿色值
            * @param arg 绿色值
            * @returns 绿色值
            */
        green(arg?: any): any;
        /**
            * 获取或设置蓝色值
            * @param arg 蓝色值
            * @returns 蓝色值
            */
        blue(arg?: any): any;
        /**
            * 获取或者设置hsv的分量
            * @param arg 分量值
            * @returns 分量值
            */
        hue(arg?: any): any;
        /**
            * 获取或者设置hsv的s分量值
            * @param arg 分量值
            * @returns 分量值
            */
        saturationv(arg?: any): any;
        /**
            * 获取或者设置hsv的v分量
            * @param arg 分量值
            * @returns 分量值
            */
        value(arg?: any): any;
        /**
            * 获取或者设置hsl的s分量
            * @param arg 分量值
            * @returns 分量值
            */
        saturation(arg?: any): any;
        /**
            * 获取或者设置hsl的l分量
            * @param arg 分量值
            * @returns 分量值
            */
        lightness(arg?: any): any;
        /**
            * 获取或者设置alpha分量
            * @param arg 分量值
            * @returns 分量值
            */
        alpha(arg?: any): any;
        /**
            * 获取十六进制字符串
            * @returns 0x开头的十六进制字符串
            */
        hexNumber(): any;
        /**
            * 获取带透明度的十六进制字符串
            * @returns 十六进制字符串
            */
        hexWidthAlpha(): string;
        /**
            * 获取#开头的十六进制字符串
            * @returns 十六进制字符串
            */
        hex(): string;
        /**
            * 获取rgba字符串
            * @returns rgba字符串
            */
        rgbaString(): string;
        /**
            * 获取hsl字符串
            * @returns hsl字符串
            */
        hslString(): string;
        /**
            * 获取hsla字符串
            * @returns hsla字符串
            */
        hslaString(): string;
        /**
            * 获取rgb数字
            * @returns rgb数字
            */
        rgbNumber(): number;
        /**
            * 获取lab对象
            * @returns lab对象
            */
        labObject(): any;
        /**
            * 获取[0-1]范围的gl颜色数组
            * @returns [0-1]范围的gl颜色数组
            */
        glNumberArray(): any[];
        getColor(): {
                r: number;
                g: number;
                b: number;
                r1: number;
                b1: number;
                g1: number;
                a: number;
        };
        /**
            * Tests to see if this color is equal to other.
            * Because other is also a color, it follows that we can simply compare red, green, blue, and alpha
            * to see if the colors are equal.
            */
        equals(other: any): boolean;
        /**
            * Tests to see if an other color is within a percentage range of this color.
            */
        near(other: any, percentage: any): boolean;
        /**
            * Returns the complement of this color.
            */
        complement(): Spectra;
        /**
            * Negates this color.
            * For a color {R, G, B}, returns a new color {R', G', B'}, where R' = 255 - R and so on.
            */
        negate(): Spectra;
        /**
            * Lightens a color based on percentage value from 1 to 100.
            */
        lighten(percentage: any): Spectra;
        /**
            * Darkens a color based on percentage value from 1 to 100.
            */
        darken(percentage: any): Spectra;
        /**
            * Lightens or darkens a color based on a random value in the specified range.
            * Percentage should be passed in as an integer, so 40 would lighten or darken up to 40%.
            */
        randomColorRange(percentage: any): Spectra;
        /**
            * Adds saturation to the color based on a percentage value.
            */
        saturate(percentage: any): Spectra;
        /**
            * Desaturates the color based on a percentage value.
            */
        desaturate(percentage: any): Spectra;
        /**
            * Fades in the current color based on a percentage value, making it less transparent.
            */
        fadeIn(percentage: any): Spectra;
        /**
            * Fades out the current color based on a percentage value, making it less transparent.
            */
        fadeOut(percentage: any): Spectra;
        /**
            * Calculates the luma of the color, i.e. how it appears on screen.
            */
        luma(): number;
        /**
            * Returns a Spectra object, which is the grayscale of the current color.
            */
        grayscale(): Spectra;
        /**
            * If a color is dark then it's best to have white text on it.
            * http://24ways.org/2010/calculating-color-contrast
            */
        isDark(): boolean;
        /**
            * If a color is light then it's best to have black text on it.
            */
        isLight(): boolean;
        /**
            * Returns the color that results from mixing percent of the other color into this color.
            */
        mix(other: any, percentage: any): Spectra;
        /**
            * Returns a number from 0 to 1 representing the color contrast between the two colors.
            */
        contrast(other: any): number;
        /**
            * Returns a gradient of colors approximately from this color to the other, consisting of n colors.
            */
        gradient(other: any, n: any): any[];
        /**
            * Harmony
            *
            * @desc Returns an array of harmonious colors (goo.gl/R3FRlU).
            * <AUTHOR> Fleming (benjamminf)
            * @since 2014-01-06
            * @param type (string) - Type of harmony.
            * @param index (int) - At which point the original color exists on the set harmonies. Since
            *   some types of color harmonies have inconsistent offsets (eg. rectangle) it's useful to
            *   note where this original color lies on the set harmony hues.
            * @return Array of Spectra instances.
            */
        harmony(type: any, index: any): any[];
        /**
            * Generates a random color.
            */
        static random(): Spectra;
}

/**
    *分段规则配置项
    *
    * @export
    * @interface IStopRuleItem
    */
export interface IStopRuleItem {
        /**
            *
            * 分段获取的结果
            * @type {*}
            * @memberof IStopRuleItem
            */
        stop: any;
        /**
            *
            * 分段判断的依据
            * @type {number}
            * @memberof IStopRuleItem
            */
        value: StopRuleValueType;
        /**
            * 一个可选的描述
            */
        desc?: string;
        /**
            * 用户自定义字段
            */
        options?: any;
}
/**
    * 过滤的模式
    *
    * @export
    * @enum {number}
    */
export enum StopRuleItemFilterMode {
        /**
            * 将大于指定值的部分进行设置
            */
        greater = "greater",
        /**
            * 将小于指定值的部分进行设置
            */
        less = "less",
        /**
            * 将指定区间值内的部分进行设置
            */
        inside = "inside",
        /**
            * 将指定区值外的部分进行设置
            */
        outside = "outside"
}
/**
    * 分段渲染的依据的数据类型，字符串或者数值，当使用区间渲染的时候只能是number
    */
export type StopRuleValueType = string | number;
/**
    * 分级规则的匹配方式，class和less_and_equal相同，且是默认模式
    * 格点数值匹配仅支持class模式
    */
export enum StopRuleActionMode {
        /**
            * 分段匹配，使用小于等于的模式
            */
        class = "class",
        /**
            * 分段匹配，使用小于的模式
            */
        less = "less",
        /**
            * 分段匹配，使用小于等于的模式
            */
        less_and_equal = "less_and_equal",
        /**
            * 分段匹配，使用大于的模式
            */
        greater = "greater",
        /**
            * 分段匹配，使用大于等于的模式
            */
        greater_and_equal = "greater_and_equal",
        /**
            * 唯一值匹配，相当于equal
            */
        unique = "unique",
        /**
            * 唯一值匹配，与unique一致
            */
        equal = "equal"
}
/**
    *分段渲染构造器参数
    *
    * @export
    * @interface IStopRulesOptions
    */
export interface IStopRulesOptions {
        /**
            *
            * 分段规则数组，action类型是class的时候，其中配置项的value由小到大顺序放置
            * @type {IStopRuleItem[]}
            * @memberof IStopRulesOptions
            */
        stops: IStopRuleItem[];
        /**
            *
            * 分段类型，默认是class，表示区间分段，unique表示唯一值渲染。class的时候field参数对应的属性值需是数值或者数值的字符串
            * 具体参考 {StopRuleActionMode}
            * @type {StopRuleActionMode}
            * @memberof IStopRulesOptions
            */
        action?: StopRuleActionMode;
        /**
            *
            * 用于分段的属性字段名。必选项
            * @type {string}
            * @memberof IStopRulesOptions
            */
        fieldName: string;
}
/**
    *分段规则类
    *
    * @export
    * @class StopRules
    */
export class StopRules {
        /**
            * 创建 StopRules.
            * @param {IStopRulesOptions} options 构造器选项
            * @param {(stop: IStopRuleItem) => IStopRuleItem} [stopConverter] 可以提供一个函数，在构建分段配置的时候将其配置项进行转换
            * @memberof StopRules
            */
        constructor(options: IStopRulesOptions, stopConverter?: (stop: IStopRuleItem) => IStopRuleItem);
        protected originalOptions: IStopRulesOptions;
        /**
            *
            * 配置项列表
            * @type {IStopRuleItem[]}
            * @memberof StopRules
            */
        stops: IStopRuleItem[];
        /**
            *
            * 分段配置方式，class表示区间，unique表示唯一值。默认是class
            * @type {StopRuleActionMode}
            * @memberof StopRules
            */
        action: StopRuleActionMode;
        /**
            *
            * 分段渲染的属性字段名
            * @type {string}
            * @memberof StopRules
            */
        fieldName: string;
        protected deserialize(): void;
        /**
            * 根据最大最小值重新设置分段值，仅对数值类型的value有效
            * @param min
            * @param max
            * @param decimal 默认1
            * @param fromIdx 默认0
            * @param toIdx 默认-1，表示倒数第二个（=0表示最后一个）
            */
        static updateStopValuesByMaxMin(stops: IStopRuleItem[], min: number, max: number, decimal?: number, fromIdx?: number, toIdx?: number): void;
        /**
            * 根据最大最小值重新设置分段值，仅对数值类型的value有效
            * @param min
            * @param max
            * @param decimal 默认1
            * @param fromIdx 默认0
            * @param toIdx 默认-1，表示倒数第二个（=0表示最后一个）
            */
        updateValuesByMaxMin(min: number, max: number, decimal?: number, fromIdx?: number, toIdx?: number): void;
        /**
            *
            * 根据value获取分段规则索引号
            * @memberof StopRules
            */
        getRuleIndex: (val: StopRuleValueType) => number;
        /**
            *
            * 根据value获取分段描述
            * @memberof StopRules
            */
        getDesc: (val: StopRuleValueType) => string;
        /**
            *
            * 根据value和判断方式获取分段配置
            * @param {StopRuleValueType} val 要判断的值
            * @param {("pre" | "current" | "next")} [type="current"] 获取方式。pre表示获取对应的区间的前一个，current表示获取所在区间，next表示所在区间的后一个。默认是current。
            * @return {*}  {IStopRuleItem} 返回区间配置
            * @memberof StopRules
            */
        getRule(val: StopRuleValueType, type?: ("pre" | "current" | "next")): IStopRuleItem;
        /**
            *
            * 根据stop获取对应的value。需要提供一个id函数，用于确定每个stop的唯一值
            * @param {*} stop 分段结果
            * @param {(stop: any) => string} getId id函数
            * @return {*}  {{ min?: IStopRuleItem, max?: IStopRuleItem }} 返回这个区间的对应的最大值配置和最小值配置。唯一值渲染的对应配置是最大值。
            * @memberof StopRules
            */
        getVal(stop: any, getId: (stop: any) => string): {
                min?: IStopRuleItem;
                max?: IStopRuleItem;
        };
        /**
            *
            * 获取判据数组
            * @return {*}  {number[]}
            * @memberof StopRules
            */
        getVals(): StopRuleValueType[];
        /**
            *
            * 获取结果数组
            * @return {*}  {any[]}
            * @memberof StopRules
            */
        getStops(): any[];
        /**
            * 复制一份当前分段规则的副本
            *
            * @param deep 是否是深拷贝，默认是false
            * @param original 是否是拷贝初始状态值，因为数据可能被动态修改，默认是false，表示拷贝当前状态
            * @return {*}  {StopRules}
            * @memberof StopRules
            */
        clone(deep?: boolean, original?: boolean): StopRules;
        /**
            * 将调色板恢复到初始状态
            *
            * @memberof StopRules
            */
        reset(): void;
        /**
            * 仅针对stop为颜色类型的透明度设置，可以通过reset方法恢复为原始透明度
            * @param {number} start
            * @param {("greater" | "less" | "inside" | "outside")} mode 设置模式，默认为outside
            * greater：表示将大于此索引的设置为指定的透明度
            *
            * less：表示将小于此索引的设置为指定的透明度
            *
            * inside：表示将start和end之间的设置为指定的透明度
            *
            * outside：表示将start和end之外的设置为指定的透明度
            * @param {number} opacity 透明度，默认为0
            * @param {number} [end] 结束的索引号
            * @return {*}
            * @memberof StopRules
            */
        setOpacityByIndex(start: number, mode: StopRuleItemFilterMode, opacity?: number, end?: number): void;
}

/**
    * 根据请求参数字符串获取请求参数对象
    * @param paramsStringAfterQM 请求参数，如 a=b&c=d
    * @param pSpliter 多个参数设置之间的分隔符，默认是&
    * @param eSpliter 参数键值对之间的分隔符，默认是=
    * @returns 请求参数对象
    */
export function getQueryParams(paramsStringAfterQM: string, pSpliter?: string, eSpliter?: string): {};
/**
    * 对字段进行loader处理
    * @param field 字段。wind-color#res
    * @returns loader的返回值
    */
export function processFieldWithLoaders<T>(field: any): T;
/**
    * 将css颜色转换为spectra
    * @param str 颜色字符串，css表达方式
    */
export const string2Spectra: (str: any) => Spectra;
/**
    * 将颜色数值转换为spectra
    * @param num 颜色数值，如0xffffff
    */
export const number2Spectra: (num: number) => Spectra;
/**
    * 将数组颜色转换为spectra
    * @param arr 数组颜色，如[255,0,255,0.5]
    */
export const array2Spectra: (arr: number[]) => Spectra;
/**
    * 样式相关的基础类
    *
    * @class StyleBaseClass
    */
export abstract class StyleBaseClass<T> {
        sourceOptions: any;
        protected abstract _update(options: T, reinit: boolean): any;
        protected _cache: {};
        styleName: string;
        constructor(sourceOptions: any);
        /**
            *
            * 表名当前的类是否是样式相关类。该类永远返回true
            * @return {*}
            * @memberof StyleBaseClass
            */
        isStyle(): boolean;
        protected getPublicKeys(): string[];
        /**
            * 清空缓存
            */
        clearCaches(): void;
        /**
            *
            * 对当前样式对象使用新的参数进行更新
            * @param {T} options 新的参数
            * @param {boolean} [reinit=false] 当为true的时候，将会完成使用当前的options作为参数进行所有属性的构建，未传入值的将使用默认值；false的时候进行传入属性的部分更新。默认为false
            * 如果是整个重新构建，建议创建新的样式类
            * @return {*}
            * @memberof StyleBaseClass
            */
        update(options: T, reinit?: boolean): void;
        protected abstract _createCacheId(val: any): any;
        /**
            * 根据指定的feature获取当前样式对应的实际值
            *
            * @param {GeoJSON.Feature} val
            * @param {boolean} cacheFirst 默认优先从缓存中获取，如果缓存中没有找到，再进行更新。传入false进行强制更新。update方法被调用后会自动清空缓存。
            * @param {any[]} values 如果有多个数值，可以以数组传入，需要样式本身支持多数值。目前格点样式的字符串和数值字段可以通过配置中最后增加|来设置要使用的值的索引号。
            * @return {*}  {T}
            * @memberof FeatureBaseClass
            */
        getPlaneOptions(sourceValue: any, cacheFirst?: boolean, values?: any[]): T;
}
export const colorStopRuleItemConverter: (sr: IStopRuleItem) => IStopRuleItem;

/**
    * buffer数据的type array表达集合
    */
export type BufferArray = Uint8Array | Int8Array | Uint16Array | Int16Array | Uint32Array | Int32Array | Float32Array | Float64Array;
/**
    * 格点数据类型
    */
export enum GridDataType {
        Int8 = 0,
        UInt8 = 1,
        Int16 = 2,
        UInt16 = 3,
        Int32 = 4,
        UInt32 = 5,
        Float32 = 6,
        Float64 = 7
}
/**
    * 图像对象类型
    */
export type ImageLike = HTMLImageElement | HTMLCanvasElement | string | HTMLVideoElement;

/**
    * 使用默认legend，需要将colors目录放置于consts.resourcePath/styles/colors目录下，或者在consts中设置defaultLegendPath
    */
export const predefinedLegendNames: {
        "3gauss": string;
        "3saw": string;
        BkBlAqGrYeOrReViWh200: string;
        BlAqGrYeOrRe: string;
        BlAqGrYeOrReVi200: string;
        BlGrYeOrReVi200: string;
        BlRe: string;
        BlWhRe: string;
        BlueDarkOrange18: string;
        BlueDarkRed18: string;
        BlueGreen14: string;
        BlueRed: string;
        BlueRedGray: string;
        BlueWhiteOrangeRed: string;
        BlueYellowRed: string;
        BrownBlue12: string;
        Cat12: string;
        GHRSST_anomaly: string;
        GMT_cool: string;
        GMT_copper: string;
        GMT_drywet: string;
        GMT_gebco: string;
        GMT_globe: string;
        GMT_gray: string;
        GMT_haxby: string;
        GMT_hot: string;
        GMT_jet: string;
        GMT_nighttime: string;
        GMT_no_green: string;
        GMT_ocean: string;
        GMT_paired: string;
        GMT_panoply: string;
        GMT_polar: string;
        GMT_red2green: string;
        GMT_relief: string;
        GMT_relief_oceanonly: string;
        GMT_seis: string;
        GMT_split: string;
        GMT_topo: string;
        GMT_wysiwyg: string;
        GMT_wysiwygcont: string;
        GrayWhiteGray: string;
        GreenMagenta16: string;
        GreenYellow: string;
        MPL_Accent: string;
        MPL_Blues: string;
        MPL_BrBG: string;
        MPL_BuGn: string;
        MPL_BuPu: string;
        MPL_Dark2: string;
        MPL_GnBu: string;
        MPL_Greens: string;
        MPL_Greys: string;
        MPL_OrRd: string;
        MPL_Oranges: string;
        MPL_PRGn: string;
        MPL_Paired: string;
        MPL_Pastel1: string;
        MPL_Pastel2: string;
        MPL_PiYG: string;
        MPL_PuBu: string;
        MPL_PuBuGn: string;
        MPL_PuOr: string;
        MPL_PuRd: string;
        MPL_Purples: string;
        MPL_RdBu: string;
        MPL_RdGy: string;
        MPL_RdPu: string;
        MPL_RdYlBu: string;
        MPL_RdYlGn: string;
        MPL_Reds: string;
        MPL_Set1: string;
        MPL_Set2: string;
        MPL_Set3: string;
        MPL_Spectral: string;
        MPL_StepSeq: string;
        MPL_YlGn: string;
        MPL_YlGnBu: string;
        MPL_YlOrBr: string;
        MPL_YlOrRd: string;
        MPL_afmhot: string;
        MPL_autumn: string;
        MPL_bone: string;
        MPL_brg: string;
        MPL_bwr: string;
        MPL_cool: string;
        MPL_coolwarm: string;
        MPL_copper: string;
        MPL_cubehelix: string;
        MPL_flag: string;
        MPL_gist_earth: string;
        MPL_gist_gray: string;
        MPL_gist_heat: string;
        MPL_gist_ncar: string;
        MPL_gist_rainbow: string;
        MPL_gist_stern: string;
        MPL_gist_yarg: string;
        MPL_gnuplot: string;
        MPL_gnuplot2: string;
        MPL_hot: string;
        MPL_hsv: string;
        MPL_jet: string;
        MPL_ocean: string;
        MPL_pink: string;
        MPL_prism: string;
        MPL_rainbow: string;
        MPL_s3pcpn: string;
        MPL_s3pcpn_l: string;
        MPL_seismic: string;
        MPL_spring: string;
        MPL_sstanom: string;
        MPL_summer: string;
        MPL_terrain: string;
        MPL_winter: string;
        NCV_banded: string;
        NCV_blu_red: string;
        NCV_blue_red: string;
        NCV_bright: string;
        NCV_gebco: string;
        NCV_jaisnd: string;
        NCV_jet: string;
        NCV_manga: string;
        NCV_rainbow2: string;
        NCV_roullet: string;
        OceanLakeLandSnow: string;
        SVG_Gallet13: string;
        SVG_Lindaa06: string;
        SVG_Lindaa07: string;
        SVG_bhw3_22: string;
        SVG_es_landscape_79: string;
        SVG_feb_sunrise: string;
        SVG_foggy_sunrise: string;
        SVG_fs2006: string;
        StepSeq25: string;
        ViBlGrWhYeOrRe: string;
        WhBlGrYeRe: string;
        WhBlReWh: string;
        WhViBlGrYeOrRe: string;
        WhViBlGrYeOrReWh: string;
        WhiteBlue: string;
        WhiteBlueGreenYellowRed: string;
        WhiteGreen: string;
        WhiteYellowOrangeRed: string;
        amwg: string;
        amwg256: string;
        amwg_blueyellowred: string;
        cb_9step: string;
        cb_rainbow: string;
        cb_rainbow_inv: string;
        cmp_b2r: string;
        cmp_flux: string;
        cmp_haxby: string;
        cosam: string;
        cosam12: string;
        cyclic: string;
        default: string;
        detail: string;
        example: string;
        extrema: string;
        grads_default: string;
        grads_rainbow: string;
        gscyclic: string;
        gsdtol: string;
        gsltod: string;
        gui_default: string;
        helix: string;
        helix1: string;
        hlu_default: string;
        hotcold_18lev: string;
        hotcolr_19lev: string;
        hotres: string;
        matlab_hot: string;
        matlab_hsv: string;
        matlab_jet: string;
        matlab_lines: string;
        mch_default: string;
        ncl_default: string;
        ncview_default: string;
        nice_gfdl: string;
        nrl_sirkes: string;
        nrl_sirkes_nowhite: string;
        perc2_9lev: string;
        percent_11lev: string;
        posneg_1: string;
        posneg_2: string;
        prcp_1: string;
        prcp_2: string;
        prcp_3: string;
        precip2_15lev: string;
        precip2_17lev: string;
        precip3_16lev: string;
        precip4_11lev: string;
        precip4_diff_19lev: string;
        precip_11lev: string;
        precip_diff_12lev: string;
        precip_diff_1lev: string;
        psgcap: string;
        radar: string;
        radar_1: string;
        "rainbow+gray": string;
        "rainbow+white+gray": string;
        "rainbow+white": string;
        rainbow: string;
        rh_19lev: string;
        seaice_1: string;
        seaice_2: string;
        so4_21: string;
        so4_23: string;
        spread_15lev: string;
        sunshine_9lev: string;
        sunshine_diff_12lev: string;
        t2m_29lev: string;
        tbrAvg1: string;
        tbrStd1: string;
        tbrVar1: string;
        "tbr_240-300": string;
        "tbr_stdev_0-30": string;
        "tbr_var_0-500": string;
        temp1: string;
        temp_19lev: string;
        temp_diff_18lev: string;
        temp_diff_1lev: string;
        testcmap: string;
        thelix: string;
        topo_15lev: string;
        uniform: string;
        wgne15: string;
        "wh-bl-gr-ye-re": string;
        wind_17lev: string;
        wxpEnIR: string;
};
/**
    * 根据内置的颜色获取分级规则，当前仅支持通过最大最小值来进行线性插值
    * @param legendName 颜色名称
    * @param min
    * @param max
    * @param skip 跳过的颜色个数，默认是0,1的时候表示隔一个取一个颜色。主要为了避免颜色过多
    * @param field 用于矢量分级时候图需要提供字段名称
    */
export function getPredefinedStopRules(legendName: string, min: number, max: number, skip?: number, field?: string, action?: StopRuleActionMode): Promise<{
        stopRules: StopRules;
        css: string;
}>;
export function getPredefinedBitmapScaleSync(legendName: string, min: number, max: number, gradient: boolean): BitmapColorScaleGL & {
        css: string;
};
/**
    * 根据内置的颜色获取分级色标
    * @param legendName
    * @param min
    * @param max
    * @param gradient
    * @returns
    */
export function getPredefinedBitmapScale(legendName: string, min: number, max: number, gradient: boolean): Promise<BitmapColorScaleGL & {
        css: string;
}>;

export interface IThemeMap {
    setTheme(theme: string): any;
    getTheme(): string;
}

/**
    * shader代码共享区
    * 所有注册过的以qe_开头的代码都可以在glsl代码中直接引用
    */
export const shaderLib: {
        [key: string]: string;
};
export function mergeShaderLib(obj: any): void;
export const qeGLFuncs: {
        /**
            * 根据格点经度或者纬度及其对应的属性（vec4(start,end,delta,size)）获取在贴图中[0,1]的位置
            */
        qe_getGridOneDimPos: string;
        /**
            * 根据经纬度（vec2）和各自属性（vec4(start,end,delta,size)）分别获取在贴图中的位置以vec2返回
            */
        qe_getLonLatTexPos: string;
        /**
            * 是否为缺测值
            * bool (float val,float undef)
            */
        qe_isUndef: string;
        /**
            * 读取二维格点原始数据
            * float (sampler2D grid,vec2 uv,float undef)
            */
        qe_readGrid2DRawVal: string;
        /**
            * 读取二维地形场原始数据
            * float (sampler2D grid,vec2 uv,float undef)
            */
        qe_readHeight2DRawVal: string;
        /**
            * float (sampler3D grid,vec3 p,float undef)
            */
        qe_readGrid3DRawVal: string;
        /**
            * 根据经纬度的UV获取格点值，可以双线性插值
            * 传入参数：
            * sampler2D grid,vec2 lonLat,vec4 xAttr,vec4 yAttr,float undef,int interpMethod 0-none,1-bi,2-cardinal
            * 输出：
            * vec2，第一个值是原始值，如果插值第二个值是插值结果，否则与第一个值相同
            */
        qe_readGridValByUV: string;
        /**
            * 根据经纬度的UV获取地形值，可以双线性插值
            * 传入参数：
            * sampler2D grid,vec2 lonLat,vec4 xAttr,vec4 yAttr,float undef,int interpMethod 0-none,1-bi,2-cardinal
            * 输出：
            * vec2，第一个值是原始值，如果插值第二个值是插值结果，否则与第一个值相同
            */
        qe_readHeightValByUV: string;
        /**
            * 根据经纬度和高度的uvw获取格点值，可以双线性插值
            * vec2 (sampler3D grid,vec3 uv,vec4 xAttr,vec4 yAttr,float undef,int interpMethod)
            * interpMethod:0->none,1->bi-linear
            */
        qe_readGridVal3DByUV: string;
        /**
            *  根据经纬度的UV获取前后两个网格场指定百分比位置的格点值
            * vec2 (sampler2D grid,vec2 uv,vec4 xAttr,vec4 yAttr,float undef,int interpMethod,sampler2D nextGrid,float percent)
            */
        qe_interpGridValByUV: string;
        /**
            * 根据经纬度的uvw获取前后两个网格场指定百分比位置的格点值
            * vec2 (sampler3D grid,vec3 p,vec4 xAttr,vec4 yAttr,float undef,int interpMethod,sampler3D nextGrid,float percent)
            */
        qe_interpGridVal3DByUV: string;
        /**
            * 经纬度（弧度）转84世界坐标，改写自Cartesian3.fromRadians
            * vec3 (vec3 geo)
            */
        qe_geo2cartesian: string;
        /**
            * 经纬度（度数）坐标转84世界坐标
            * vec3 (vec3 deg)
            */
        qe_deg2cartesian: string;
        /**
            * 经纬度（度数）坐标转84世界坐标版本2
            * vec3 (vec3 pos)
            */
        qe_deg2cartesianV2: string;
        /**
            * 根据世界坐标系获取经纬度和高度
            * vec3 (vec3 pos,bool global)
            * pos:坐标，global:是否是球体，当前仅测试过球体
            */
        qe_cartesian2deg: string;
        /**
            * 根据数值获取颜色
            * uniform vec4 colors[]
            * uniform float steps[]
            * const int stepCount
            * vec4 (float val)
            */
        qe_getColor: string;
        /**
            * 根据数值获取插值后的颜色
            * uniform vec4 colors[]
            * uniform float steps[]
            * const int stepCount
            * vec4 (float val)
            */
        qe_interpColor: string;
        /**
            * 根据bitmap色例获取颜色
            */
        qe_getColorByScale: string;
        /**
            * 获取当前点的高度。综合考虑高度贴图和指定的高度值
            * getHeightUniforms()
            * float (vec2 uv,bool useTexture,float percent)
            */
        qe_getHeightByPreAndCurrent: string;
        /**
            * 根据当前是否启用webgl2决定应该如何获取浮点类型的贴图数值
            * float (sampler2D tex,vec2 uv)
            */
        qe_texture2DFloat: string;
        /**
            * 一维索引号转经纬度
            * vec2 (float idx,float xSize,float ySize)
            */
        qe_idx2LonLat: string;
        /**
            * 一维索引号转经纬度uv
            * vec2 (float idx,float xSize,float ySize)
            */
        qe_idx2LonLatTexPos: string;
        /**
            * RGBA值编码为浮点数
            *
            */
        qe_rgba2float: string;
        /**
            * 浮点数编码为RGBA
            */
        qe_float2rgba: string;
        /**
            * 通过screen quad进行裁剪。需要有vec2 reso和maskTexture
            * 在mask多边形区域内返回true，区域外返回false
            */
        qe_maskout: string;
        /**
            * 将glsl中的float编码为rgba，以便在js中直接通过buffer创建浮点数组
            * float qe_decodeFloat(vec4 texelRGBA, bool littleEndian)
            */
        qe_encodeFloat: string;
        /**
            * 两点的球面距离，单位是米
            * float (vec2 pt1,vec2 pt2);
            */
        qe_geoDistance: string;
        /**
            * 将rgba解码为float
            */
        qe_decodeFloat: string;
};
/**
    * 设置shader，框架的init方法中默认调用
    * @returns
    */
export function setShaders(): void;

/**
    * 数组类二维格点场访问器参数
    */
export interface IArray2DGridDataProviderOptions extends IGridDataProviderBaseOptions {
        /**
            * 缺测值，默认为999999
            */
        undef?: number;
        /**
            * 数据放大系数，数据中的值*放大系数=真实值
            */
        dataScale?: number;
        /**
            * 数据偏移量，数据中的值+偏移量=真实值
            */
        dataOffset?: number;
        /**
            * 是否先放大后偏移，默认为是：真实值=(数据值*scale)+offset
            */
        scaleFirst?: boolean;
        /**
            * 数据区域属性
            */
        gridOptions: IGridDataOptions;
        /**
            * 数据的额外元信息
            */
        meta?: {
                [key: string]: any;
        };
}
/**
    * 一个基于数组的单二维格点场的访问器
    */
export class Array2DGridDataProvider extends GridDataProviderBase {
        protected options: IArray2DGridDataProviderOptions;
        /**
            *
            * @param gridArr 格点数组，可以是一维也可以是二维，但是只能是一个二维场，不支持多层次或者多时次，可以基于此构建新的provider实现支持或者使用内存格点provider构建
            * @param options 解析参数
            */
        constructor(gridArr: number[] | number[][] | (() => number[] | number[][]), options?: IArray2DGridDataProviderOptions);
        static qeName: string;
}

/**
    * 解析CIMISS接口返回的单二维场的格点数据
    */
export class CimissGridDataProvider extends Array2DGridDataProvider {
        /**
            * 构建访问器
            * @param jsonData 返回的json对象
            */
        constructor(jsonData: any, options?: IGridDataProviderBaseOptions);
        static qeName: string;
}

/**
    * CIMISS站点数据访问器可选参数
    */
export interface ICimissStationFeatureProviderOptions extends IPointArrayFeatureProviderOptions {
}
/**
    * CIMISS站点数据访问器
    */
export class CimissStationFeatureProvider extends PointArrayFeatureProvider {
        headers: any;
        /**
            * 构建 CimissStationFeatureProvider
            * @param {*} cimissJson cimiss返回的json数据
            * @param {ICimissStationFeatureProviderOptions} [options] 构造参数
            * @memberof CimissStationFeatureProvider
            */
        constructor(cimissJson: any, options?: ICimissStationFeatureProviderOptions);
        protected _updateFeatures(data: any[]): void;
        static qeName: string;
}

/**
    *QE自定义格点数据元信息
    *
    * @export
    * @interface IQE3DGridDataMeta
    */
export interface IQE3DGridDataMeta {
        /**
            * x开始位置，一般是开始经度
            */
        xStart: number;
        /**
            * y开始位置，一般是开始纬度
            */
        yStart: number;
        /**
            * x间隔
            */
        xDelta: number;
        /**
            * y间隔
            */
        yDelta: number;
        /**
            * x轴格点数量，一般是经度格点数量
            */
        xSize: number;
        /**
            * y轴格点数量，一般是纬度格点数量
            */
        ySize: number;
        /**
            * x结束位置，一般是结束经度
            */
        xEnd: number;
        /**
            * y结束位置，一般是结束纬度
            */
        yEnd: number;
        /**
            * 有多少个时效
            */
        times?: number;
        /**
            * 有多少个层次
            */
        levels?: number;
        /**
            * 数据的时间戳
            */
        timestamp?: number;
        /**
            * 高度层列表
            */
        levelList?: string[];
        /**
            * 时间戳列表
            */
        timeList?: any[];
        /**
            * 数据类型
            */
        dataType: string;
        /**
            * 缺测值
            */
        undef: number;
        /**
            * 数据缩放倍数
            */
        dataScale?: number;
        /**
            * 数据偏移量
            */
        dataOffset?: number;
        /**
            * 数据单位
            */
        units?: string;
        /**
            * 是否是小端
            */
        littleEndian?: boolean;
        /**
            * 额外信息
            */
        properties: any;
        /**
            * 是否无符号
            */
        unsigned: boolean;
        /**
            * 最大值列表
            */
        max?: number[];
        /**
            * 最小值列表
            */
        min?: number[];
        /**
            * 算法类型，1表示需要使用 val*(max-min)/254+min的方式还原，0表示原始值，默认为0
            */
        algo?: number;
}
/**
    *QE自定义格点数据访问器的构造参数
    *
    * @export
    * @interface IQEGridDataProviderOptions
    */
export interface IQEGridDataProviderOptions extends IGridDataProviderBaseOptions {
        /**
            * 用户自定义头信息，可以用于覆盖数据中自带的头信息内容
            */
        meta?: IQE3DGridDataMeta;
}
/**
    *QE自定义格点数据访问器
    *
    * @export
    * @class QEGridDataProvider
    * @implements {IGridDataProvider}
    */
export class QEGridDataProvider extends GridDataProviderBase {
        protected options: IQEGridDataProviderOptions;
        /**
            * 构建 QEGridDataProvider
            * @param {ArrayBuffer} buffer 数据缓冲区
            * @param {IQEGridDataProviderOptions} [options] 构造参数
            * @memberof QEGridDataProvider
            */
        constructor(buffer: ArrayBuffer, options?: IQEGridDataProviderOptions);
        static qeName: string;
}

/**
    * 矢量数据源访问器基础类型
    */
export abstract class FeatureDataProviderBase extends Evented implements IFeaturesProvider {
        protected eventNames: {
                dataUpdated: string;
        };
        /**
            * 数据元信息
            *
            * @type {{ [key: string]: any; }}
            * @memberof FeatureDataProviderBase
            */
        meta: {
                [key: string]: any;
        };
        /**
            * 获取FeatureCollection集合
            */
        abstract getFeatures(): FeatureCollection<Geometry, {
                [name: string]: any;
        }>;
        /**
            * 获取Feature Id
            * @param fid Feature Id
            */
        abstract getFeature(fid: string): Feature<Geometry, {
                [name: string]: any;
        }>;
        protected abstract _updateFeatures(data: any): any;
        /**
            * 更新数据源
            * @param data 数据源
            */
        updateFeatures(data: any): void;
        /**
            * 设置数据源更新回调
            * @param cb 更新时候的回调
            * @returns 当前数据访问器实例
            */
        onFeaturesUpdate(cb: () => void): IFeaturesProvider;
        /**
            * 去掉数据源更新回调
            * @param cb 更新回调
            * @returns 当前数据访问器实例
            */
        offFeaturesUpdate(cb: () => void): IFeaturesProvider;
        /**
            * 获取指定字段的最大最小值
            * @param field 字段名称
            * @param undef 缺测值，默认是consts中的设置
            * @param checkUndef 是否检测缺测值，默认true，如果确认数据中不会有缺测值，可以设置为false，以便加速计算
            */
        getMaxMin(field: string, undef?: number, checkUndef?: boolean): {
                min: number;
                max: number;
        };
}

/**
    * 矢量数据访问器接口，通过实现该接口可以扩展支持任意格式的矢量数据
    * 内部使用GeoJSON进行矢量对象的描述
    */
export interface IFeaturesProvider {
        /**
            * 获取所有矢量对象
            */
        getFeatures(): GeoJSON.FeatureCollection<GeoJSON.GeometryObject>;
        /**
            * 根据对象ID获取矢量对象
            * @param fid 对象ID
            */
        getFeature(fid: string): GeoJSON.Feature<GeoJSON.GeometryObject>;
        /**
            * 更新数据内容
            *
            * @param {*} data
            * @memberof IFeaturesProvider
            */
        updateFeatures(data: any): any;
        /**
            * 监听数据更新消息
            *
            * @param {()=>void} cb
            * @return {*}  {IFeaturesProvider}
            * @memberof IFeaturesProvider
            */
        onFeaturesUpdate(cb: () => void): IFeaturesProvider;
        /**
            *
            * 取消数据更新的监听
            * @param {()=>void} cb
            * @return {*}  {IFeaturesProvider}
            * @memberof IFeaturesProvider
            */
        offFeaturesUpdate(cb: () => void): IFeaturesProvider;
        /**
            * 额外的元信息
            */
        meta?: {
                [key: string]: any;
        };
}

/**
    *GeoJSON的数据访问器
    *
    * @export
    * @class GeoJSONFeatureProvider
    * @implements {IFeaturesProvider}
    */
export class GeoJSONFeatureProvider extends FeatureDataProviderBase {
        /**
            * 构建 GeoJSONFeatureProvider.
            * @param {*} jsonObj GeoJSON对象
            * @memberof GeoJSONFeatureProvider
            */
        constructor(jsonObj: any);
        protected _updateFeatures(data: any): void;
        addFeature(feature: GeoJSON.Feature): void;
        removeFeature(fid: string): void;
        getFeatures(): FeatureCollection<GeometryObject>;
        getFeature(fid: string): Feature<GeometryObject>;
        static qeName: string;
}

/**
    *QE自定义格点数据访问器的构造参数
    *
    * @export
    * @interface IGrayImageGridDataProviderOptions
    */
export interface IGrayImageGridDataProviderOptions extends IGridDataProviderBaseOptions {
        gridOptions?: IGridDataOptions;
        undef?: number;
        autoLoadToMemory?: boolean;
        algo?: number;
        scale?: number;
        offset?: number;
        dataType?: GridDataType.Float32 | GridDataType.UInt8 | GridDataType.UInt16;
        /**
            * 是否自带了header，这与文件格式有关，需要咨询数据文件生成方，默认为false，即纯图片
            */
        withHeader?: boolean;
        /**
            * 当检测到不兼容webp的旧设备（主要是ios14以前的版本）时，是否自动转换为兼容版本
            * 默认false。当后端数据使用webp的时候建议设置为true
            *
            */
        compatibleOldDevice?: boolean;
}
/**
    *QE自定义格点数据访问器
    *
    * @export
    * @class QEGridDataProvider
    * @implements {IGridDataProvider}
    */
export class GrayImageGridDataProvider extends GridDataProviderBase {
        protected options: IGrayImageGridDataProviderOptions;
        /**
            * 构建 QEGridDataProvider
            * @param {ArrayBuffer} buffer 数据缓冲区
            * @param {IQEGridDataProviderOptions} [options] 构造参数
            * @memberof QEGridDataProvider
            */
        constructor(source: TexImageSource, options: IGrayImageGridDataProviderOptions);
        static qeName: string;
        static fromBuffer(buffer: ArrayBuffer, isVideo: boolean, options: IGrayImageGridDataProviderOptions): Promise<GrayImageGridDataProvider>;
        static fromURL(url: string, isVideo: boolean, options: IGrayImageGridDataProviderOptions): Promise<GrayImageGridDataProvider>;
}

/**
    * 格点数据访问器构造参数
    */
export interface IGridDataProviderBaseOptions {
}
/**
    * 格点数据访问器基础类型，抽象类
    */
export abstract class GridDataProviderBase extends Evented implements IGridDataProvider {
        protected options: IGridDataProviderBaseOptions;
        /**
            * 构建格点数据访问器，抽象类，不可直接创建
            * @param options 构建参数
            */
        constructor(options?: IGridDataProviderBaseOptions);
        /**
            * 格点属性信息
            */
        gridOptions: IGridDataOptions;
        /**
            * 数据元信息（头信息）
            */
        meta: {
                [key: string]: any;
        };
        protected grids: GridData[][];
        protected addGridTo(grid: GridData, dimIdx: number, levelIdx: number, levelOfZ: number, setActive: boolean, grids: GridData[][]): this;
        /**
            * 获取当前时间维度值，可能为小数
            */
        get currentTIdx(): number;
        /**
            * 设置当前时间维度值，如果超过了数据中的时间维度，则不执行任何操作
            */
        set currentTIdx(idx: number);
        /**
            * 获取当前高度维度值，可能为小数
            */
        get currentZIdx(): number;
        /**
            * 设置当前层次维度信息，当设置超出维度范围时，不执行操作
            */
        set currentZIdx(idx: number);
        centerLon(): number;
        centerLat(): number;
        centerZ(): number;
        center(): {
                lon: number;
                lat: number;
                z: number;
        };
        getGrid(dim?: any, level?: any): GridData;
        allGrids(): GridData[][];
        updateGrid(grid: GridData, dimIdx: number, levelIdx: number, levelOfZ: number, setActive: boolean): IGridDataProvider;
        getIntTIdx(): number;
        getIntZIdx(): number;
        onTChanged(cb: () => void): IGridDataProvider;
        onZChanged(cb: () => void): IGridDataProvider;
        onTZChanged(cb: () => void): IGridDataProvider;
        onIntTChanged(cb: () => void): IGridDataProvider;
        onIntZChanged(cb: () => void): IGridDataProvider;
        onIntTZChanged(cb: () => void): IGridDataProvider;
        offTChanged(cb: () => void): IGridDataProvider;
        offZChanged(cb: () => void): IGridDataProvider;
        offTZChanged(cb: () => void): IGridDataProvider;
        offIntTChanged(cb: () => void): IGridDataProvider;
        offIntZChanged(cb: () => void): IGridDataProvider;
        offIntTZChanged(cb: () => void): IGridDataProvider;
        onActiveGridUpdated(cb: () => void): IGridDataProvider;
        offActiveGridUpdated(cb: () => void): IGridDataProvider;
        /**
            * 从数据中取值
            * @param lon 经度
            * @param lat 纬度
            * @param tIdx t索引，默认当前活跃的t
            * @param zIdx z索引，默认当前活跃的z
            */
        pickValue(lon: number, lat: number, tIdx?: any, zIdx?: any): number;
}

/**
    * 支持[time[level]]的格点数据访问器
    */
export interface IGridDataProvider extends ITZDataProvider {
        /**
            * 格点数据属性
            */
        gridOptions: IGridDataOptions;
        /**
            * 额外的元信息
            */
        meta: {
                [key: string]: any;
        };
        /**
            * 获取一个二维格点场
            * @param dim 时间维度索引
            * @param level 层次维度索引
            */
        getGrid(dim?: number, level?: number): GridData;
        /**
            * 获取数据中心点经度
            */
        centerLon(): number;
        /**
            * 获取数据中心点纬度
            */
        centerLat(): number;
        /**
            * 获取数据中间的高度
            */
        centerZ(): number;
        /**
            * 获取数据的中心点位置
            */
        center(): {
                lon: number;
                lat: number;
                z: number;
        };
        /**
            *
            * 向当前数据源增加一个格点场
            * @param {number} dimIdx 时间索引号
            * @param {number} levelIdx 高度索引号
            * @param {number} levelOfZ 高度值
            * @param {boolean} setActive 是否设置为当前生效的数据
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        updateGrid(grid: GridData, dimIdx: number, levelIdx: number, levelOfZ: number, setActive: boolean): IGridDataProvider;
        /**
            * 获取当前数据访问器中的所有维度数据
            */
        allGrids(): GridData[][];
        /**
            *
            * 数据源中当前生效的时间索引号，默认是0
            * @type {number}
            * @memberof IGridDataProvider
            */
        currentTIdx: number;
        /**
            *
            * 数据源中当前生效的高度层索引号，默认是0
            * @type {number}
            * @memberof IGridDataProvider
            */
        currentZIdx: number;
        /**
            *
            * 获取当前生效的整数形式的时间索引号
            * @return {*}  {number}
            * @memberof IGridDataProvider
            */
        getIntTIdx(): number;
        /**
            *
            * 获取当前生效的整数形式的高度层索引号
            * @return {*}  {number}
            * @memberof IGridDataProvider
            */
        getIntZIdx(): number;
        /**
            *
            * 监听时间索引号发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        onTChanged(cb: () => void): IGridDataProvider;
        /**
            *
            * 监听高度层索引号发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        onZChanged(cb: () => void): IGridDataProvider;
        /**
            *
            * 监听时间和高度索引号发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        onTZChanged(cb: () => void): IGridDataProvider;
        /**
            *
            * 监听时间的整数部分发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        onIntTChanged(cb: () => void): IGridDataProvider;
        /**
            *
            * 监听高度的整数部分发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        onIntZChanged(cb: () => void): IGridDataProvider;
        /**
            *
            * 监听时间和高度的整数部分发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        onIntTZChanged(cb: () => void): IGridDataProvider;
        /**
            *
            * 取消监听时间发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        offTChanged(cb: () => void): IGridDataProvider;
        /**
            *
            * 取消监听高度发生变化的消息
            * @param {(z: number) => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        offZChanged(cb: () => void): IGridDataProvider;
        /**
            *
            * 取消监听时间高度发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        offTZChanged(cb: () => void): IGridDataProvider;
        /**
            *
            * 取消监听时间整数部分发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        offIntTChanged(cb: () => void): IGridDataProvider;
        /**
            *
            * 取消监听高度整数部分发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        offIntZChanged(cb: () => void): IGridDataProvider;
        /**
            *
            * 取消监听时间高度整数发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        offIntTZChanged(cb: () => void): IGridDataProvider;
        /**
            *
            * 监听当前时间和高度索引所在的格点发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        onActiveGridUpdated(cb: () => void): IGridDataProvider;
        /**
            *
            * 取消监听当前时间和高度索引所在的格点发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IGridDataProvider}
            * @memberof IGridDataProvider
            */
        offActiveGridUpdated(cb: () => void): IGridDataProvider;
        /**
            * 从数据中取值
            * @param lon 经度
            * @param lat 纬度
            * @param tIdx t索引，默认当前活跃的t
            * @param zIdx z索引，默认当前活跃的z
            */
        pickValue(lon: number, lat: number, tIdx?: any, zIdx?: any): number;
}

/**
    *内存格点数据访问器参数
    *
    * @export
    * @interface IMemoryGridDataProviderOptions
    */
export interface IMemoryGridDataProviderOptions extends IGridDataProviderBaseOptions {
        /**
            * 用户自定义元信息
            */
        meta?: {
                [key: string]: any;
        };
        /**
            * 格点属性参数
            */
        gridOptions: IGridDataOptions;
}
/**
    *内存格点数据访问器
    *
    * @export
    * @class MemoryGridDataProvider
    * @implements {IGridDataProvider}
    */
export class MemoryGridDataProvider extends GridDataProviderBase {
        protected options: IMemoryGridDataProviderOptions;
        /**
            * 构建 MemoryGridDataProvider
            * @param {GridData[][]} grids [t[z]]格点场数组
            * @param {IMemoryGridDataProviderOptions} [options] 构建的可选参数
            * @memberof MemoryGridDataProvider
            */
        constructor(grids: GridData[][], options?: IMemoryGridDataProviderOptions);
}

/**
    *基于通用格点访问器构建风场访问器的参数
    *
    * @export
    * @interface IMemoryWindDataProviderOptions
    */
export interface IMemoryWindDataProviderOptions extends IGridDataProviderBaseOptions {
        isUV: boolean;
        lazyCalc?: boolean;
        meta?: {
                [key: string]: any;
        };
        defaultGrid?: "speed" | "dir" | "u" | "v";
        gridOptions?: IGridDataOptions;
        wProvider?: IGridDataProvider;
}
/**
    *基于通用格点访问器构建风场访问器
    *
    * @export
    * @class MemoryWindDataProvider
    * @implements {IWindDataProvider}
    */
export class MemoryWindDataProvider extends GridDataProviderBase implements IWindDataProvider {
        /**
            * 默认配置
            *
            * @static
            * @type {IMemoryWindDataProviderOptions}
            * @memberof MemoryWindDataProvider
            */
        static DefaultOptions: IMemoryWindDataProviderOptions;
        options: IMemoryWindDataProviderOptions;
        gridOptions: IGridDataOptions;
        protected u: GridData[][];
        protected v: GridData[][];
        protected speed: GridData[][];
        protected dir: GridData[][];
        defaultGrid: "speed" | "dir" | "u" | "v";
        /**
            * 构建MemoryWindDataProvider.
            * @param {IGridDataProvider} uOrSProvider 第一个格点访问器，可以是U分量或者风速
            * @param {IGridDataProvider} vOrDProvider 第二个格点访问器，可以是V分量或者风向
            * @param {IMemoryWindDataProviderOptions} [options] 构造参数
            * @memberof MemoryWindDataProvider
            */
        constructor(uOrSProvider: IGridDataProvider, vOrDProvider: IGridDataProvider, options?: IMemoryWindDataProviderOptions);
        get wProvider(): IGridDataProvider;
        allU(): GridData[][];
        allV(): GridData[][];
        allW(): GridData[][];
        allS(): GridData[][];
        allD(): GridData[][];
        addUV(u: GridData, v: GridData, dim: number, level: number, zValue: number, setActive: boolean): IWindDataProvider;
        addSD(s: GridData, d: GridData, dim: number, level: number, zValue: number, setActive: boolean): IWindDataProvider;
        getUV(y: number, x: number, dim?: any, level?: any): number[];
        getSD(y: number, x: number, dim?: any, level?: any): number[];
        getU(dim?: any, level?: any): GridData;
        getV(dim?: any, level?: any): GridData;
        getW(dim?: number, level?: number): GridData;
        getS(dim?: any, level?: any): GridData;
        getD(dim?: any, level?: any): GridData;
        getGrid(dim?: number, level?: number): GridData;
        allGrids(): GridData[][];
        static qeName: string;
}

/**
    * 内存矢量数据访问器的构造参数
    */
export interface IPointArrayFeatureProviderOptions {
        /**
            * 经度字段名，默认是Lon
            */
        lonField?: string;
        /**
            * 纬度字段名，默认是Lat
            */
        latField?: string;
        /**
            * id字段名，默认是Station_Id_C
            */
        idField?: string;
        /**
            * 对于相同ID的对象，进行合并的函数。
            * 如果出现了相同ID对象，且没有提供合并函数，则后出现的对象覆盖先出现的
            */
        mergeFunc?: (fieldName: string, preVal: any, currentVal: any) => any;
        /**
            * 给结果字段添加前缀
            */
        prefix?: string;
        /**
            * 给结果字段添加后缀
            */
        suffix?: string;
        /**
            * 站点信息的映射对象，当数据接口中的返回对象仅包含数据本身信息和id的时候，可以提供一个外部对象，使用相同的id作为对象的key，以便获取站点的经纬度等相关信息
            */
        stationInfoMap?: {
                [key: string]: any;
        };
}
/**
    * 数组类站点数据的访问器
    */
export class PointArrayFeatureProvider extends FeatureDataProviderBase {
        static DefaultOptions: IPointArrayFeatureProviderOptions;
        protected features: FeatureCollection<Geometry, {
                [name: string]: any;
        }>;
        protected featuresMap: {
                [key: string]: Feature<Geometry, {
                        [name: string]: any;
                }>;
        };
        /**
            * 根据数据类对象进行站点数据的解析
            * @param dataArr 对象数组，如：[{Lon:xxx,Lat:xxx,Station_Id_C:5xxxx,pre_1h:12.0},...]，或者是可以返回对象数组的函数
            * @param options 可选参数
            */
        constructor(dataArr: any[] | (() => any[]), options?: IPointArrayFeatureProviderOptions);
        protected _updateFeatures(data: any[]): void;
        protected parseFeatures(dataArr: any[]): void;
        getFeatures(): FeatureCollection<Geometry, {
                [name: string]: any;
        }>;
        getFeature(fid: string): Feature<Geometry, {
                [name: string]: any;
        }>;
        static qeName: string;
}

/**
    * 具有时间信息的数据访问器
    *
    * @export
    * @interface ITDataProvider
    */
export interface ITDataProvider {
        /**
            *
            * 当前时次索引
            * @type {number}
            * @memberof ITDataProvider
            */
        currentTIdx: number;
}
/**
    * 具有时间和高度信息的数据访问器
    *
    * @export
    * @interface ITZDataProvider
    */
export interface ITZDataProvider extends ITDataProvider {
        /**
            *
            * 当前层次索引
            * @type {number}
            * @memberof ITZDataProvider
            */
        currentZIdx: number;
        /**
            * 额外的元信息
            */
        meta: {
                [key: string]: any;
        };
}

/**
    * 风场格点数据访问器
    */
export interface IWindDataProvider extends IGridDataProvider {
        wProvider?: IGridDataProvider;
        /**
            * 根据y、x位置和时间、高度索引号获取默认的二维格点场
            */
        getUV: (y: number, x: number, dim?: number, level?: number) => number[];
        /**
            * 获取某点风的方向和大小
            * @param x
            * @param y
            * @returns [speed,dir]
            */
        getSD: (y: number, x: number, dim?: any, level?: any) => number[];
        /**
            * 获取u分量场
            */
        getU: (dim?: number, level?: number) => GridData;
        /**
            * 获取v分量场
            */
        getV: (dim?: number, level?: number) => GridData;
        /**
            * 获取w分量场
            */
        getW?: (dim?: number, level?: number) => GridData;
        /**
            * 获取风速场
            */
        getS: (dim?: number, level?: number) => GridData;
        /**
            * 获取风向场
            */
        getD: (dim?: number, level?: number) => GridData;
        /**
            * 获取所有的U分量
            */
        allU: () => GridData[][];
        /**
            * 获取所有的V分量
            */
        allV: () => GridData[][];
        /**
            * 获取所有的W分量
            */
        allW?: () => GridData[][];
        /**
            * 获取所有的S分量
            */
        allS: () => GridData[][];
        /**
            * 获取所有的D分量
            */
        allD: () => GridData[][];
        /**
            *
            * 增加一个uv场
            * @param {GridData} u
            * @param {GridData} v
            * @param {number} dim
            * @param {number} level
            * @param {number} zValue
            * @param {boolean} setActive 是否设置为当前生效数据
            * @return {*}  {IWindDataProvider}
            * @memberof IWindDataProvider
            */
        addUV(u: GridData, v: GridData, dim: number, level: number, zValue: number, setActive: boolean): IWindDataProvider;
        /**
            *
            * 增加一个sd场
            * @param {GridData} s
            * @param {GridData} d
            * @param {number} dim
            * @param {number} level
            * @param {number} zValue
            * @param {boolean} setActive 是否设置为当前生效数据
            * @return {*}  {IWindDataProvider}
            * @memberof IWindDataProvider
            */
        addSD(s: GridData, d: GridData, dim: number, level: number, zValue: number, setActive: boolean): IWindDataProvider;
        defaultGrid?: "speed" | "dir" | "u" | "v";
}

export class MicapsDiamond4GridDataProvider extends Array2DGridDataProvider {
    protected headers: string[];
    constructor(stringData: string, options?: IArray2DGridDataProviderOptions);
}

export interface IMicapsDiamond131GridDataMeta {
    zoneName: string;
    dataName: string;
    version: string;
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
    interval: number;
    xNumGrids: number;
    yNumGrids: number;
    zNumGrids: number;
    radarCount: number;
    startLon: number;
    startLat: number;
    centerLon: number;
    centerLat: number;
    xReso: number;
    yReso: number;
}
export interface IMicapsDiamond131GridDataOptions extends IGridDataProviderBaseOptions {
    /**
      * z坐标单位是否是千米，如果是的话将自动转换为米，默认是true
      */
    isZUnitKM?: boolean;
}
export class MicapsDiamond131GridDataProvider extends GridDataProviderBase {
    static DefaultOptions: IMicapsDiamond131GridDataOptions;
    protected options: IMicapsDiamond131GridDataOptions;
    constructor(buffer: ArrayBuffer, options?: IMicapsDiamond131GridDataOptions);
}

export class BinaryGridDataProvider extends GridDataProviderBase {
    constructor(buffer: ArrayBuffer, options?: IGridDataProviderBaseOptions);
}

interface ITifGridDataProviderOptions extends IGridDataProviderBaseOptions {
        /**
            * 缺测值，默认为999999
            */
        undef?: number;
        /**
            * 数据区域属性
            */
        gridOptions?: IGridDataOptions;
        /**
            * 是否精确检测缺测值，默认false，当数据中的缺测值为常规值，比如65535，而不是-3.4028230607370965e+38这种非常小或者大的值的时候，使用默认的false。
            */
        useAccurateUndef?: boolean;
}
export class TifGridDataProvider extends GridDataProviderBase {
        constructor(buffers: BufferArray[], options: ITifGridDataProviderOptions);
        static fromBuffer: (buffer: ArrayBuffer, options?: ITifGridDataProviderOptions) => Promise<TifGridDataProvider>;
        static fromUrl(url: string, options?: ITifGridDataProviderOptions): Promise<TifGridDataProvider>;
        static qeName: string;
}
export {};

export interface IMicapsDiamond14Meta {
    year: number;
    mon: number;
    day: number;
    hour: number;
    period: number;
    time: Date;
    title: string;
}
export interface IMicapsDiamond14FeatureProviderOptions {
}
export class MicapsDiamond14FeatureProvider extends FeatureDataProviderBase {
    constructor(data: string, options?: IMicapsDiamond14FeatureProviderOptions);
    protected features: FeatureCollection<Geometry, {
        [name: string]: any;
    }>;
    protected featuresMap: {
        [key: string]: Feature<Geometry, {
            [name: string]: any;
        }>;
    };
    protected _updateFeatures(data: any): void;
    getFeatures(): FeatureCollection<Geometry, {
        [name: string]: any;
    }>;
    getFeature(fid: string): Feature<Geometry, {
        [name: string]: any;
    }>;
    static qeName: string;
}

export interface IRandomFeatureFieldOptions {
        name: string;
        /**
            * 数据产生模式，默认为random
            * list模式为使用随机整数作为索引，从valueList中取值，支持数值和文本类型
            * random模式为生成随机值，随机值只支持数值类型
            */
        valueMode?: "list" | "random";
        /**
            * 当使用list模式的时候，需要提供一个候选列表
            */
        valueList?: number[] | string[];
        /**
            * 随机的最小值，默认为0
            */
        randomMin?: number;
        /**
            * 随机的最大值，默认为100
            */
        randomMax?: number;
        /**
            * 数据处理器，如果配置，则使用系统生成的数据作为参数调用该函数，返回结果作为最终结果
            */
        postProcessor?: (value: number | string, idx: number) => number | string;
}
/**
    * 随机矢量数据源构建参数
    */
export interface IRandomFeatureProviderOptions {
        /**
            * 数据范围，如果高度值不给定默认为0
            */
        extent: IDataExtent;
        /**
            * 生成点数量，默认1000
            */
        count?: number;
        /**
            * 属性列表，如果不提供，则默认只生成一个自增的fid属性
            * 提供的属性列表中不能包含fid字段
            */
        fields?: IRandomFeatureFieldOptions[];
}
export class RandomFeatureProvider extends FeatureDataProviderBase {
        static DefaultOptions: IRandomFeatureProviderOptions;
        constructor(options: IRandomFeatureProviderOptions);
        protected options: IRandomFeatureProviderOptions;
        protected features: FeatureCollection<Geometry, {
                [name: string]: any;
        }>;
        protected featuresMap: {
                [key: string]: Feature<Geometry, {
                        [name: string]: any;
                }>;
        };
        protected _updateFeatures(data: IRandomFeatureProviderOptions): void;
        getFeatures(): FeatureCollection<Geometry, {
                [name: string]: any;
        }>;
        getFeature(fid: string): Feature<Geometry, {
                [name: string]: any;
        }>;
}

/**
  * Micaps2类的格点风场数据解析器
  */
export class MicapsDiamond2WindGridProvider extends MemoryWindDataProvider {
    constructor(data: string, options: Omit<IMemoryWindDataProviderOptions, "isUV" | "wProvider">);
}

/**
    * 数据源动画服务构建参数
    */
export interface IDataAnimationServiceOptions {
        /**
            * 播放的开始索引号，如果设置，则从该位置开始播放，此时如果设置了toIdx，则播放到toIdx结束，否则播放到最后一个后从0开始继续播放
            */
        fromIdx?: number;
        /**
            * 播放的结束位置，与fromIdx配置使用
            */
        toIdx?: number;
        /**
            * 时间间隔，默认30ms，仅对type为定时器类型生效
            */
        timerInterval?: number;
        /**
            * 是否循环播放，默认是
            */
        loop?: boolean;
        /**
            * 是否立即开始播放，默认否
            */
        autoStart?: boolean;
        /**
            * 每次动画的索引递增量，默认是0.05，如currenTIdx=0时，下一次就是0.05
            */
        delta?: number;
        /**
            * 是否使用定时器进行动画，默认是
            */
        useTimer?: boolean;
        /**
            * 动画的最长数量是多少，例如对时次进行动画，总共有10个时次，就是10
            * 如果数据不是一次性载入，而是动态不断载入，这时候可以构建时传入最长可能时次，
            * 这样当数据没有载入完成时，会自动暂停播放直到数据源中出现该时次的数据后继续播放
            */
        all: number;
        /**
            * 数据动画的方式，默认是时间动画
            */
        type?: DataAnimationType;
        /**
            * 动画的图层，该图层需要实现清除上一次数据的方法
            * 该方法用于恢复图层至第一帧的初始状态，避免出现最后一次数据和第一次数据之间出现自动插帧，仅用于支持自动查帧的WebGL图层
            */
        layer: IAnimationableLayer | IAnimationableLayer[];
        /**
            * 如果有多个图层，可以直接传入清空的回调方法
            */
        clearPreSource?: () => void;
}
/**
    * 数据动画类型
    */
export enum DataAnimationType {
        Time = 0,
        Level = 1
}
/**
    * 数据动画服务
    */
export class DataAnimationService extends Evented {
        /**
            * 默认配置
            */
        static DefaultOptions: IDataAnimationServiceOptions;
        static EventTypes: {
                idxUpdate: string;
                /**
                    * 播放完最后一个回到idx=0的状态
                    */
                backUpdate: string;
                stopped: string;
        };
        protected options: IDataAnimationServiceOptions;
        protected startFunc: (cb: () => void, interval?: number) => number;
        protected stopFunc: (id: number) => void;
        protected animationId: number;
        protected paused: number;
        protected dataSources: ITZDataProvider[];
        /**
            * 构建数据动画服务
            * @param dataSource 数据源
            * @param options 构建参数
            */
        constructor(dataSource: ITZDataProvider | ITZDataProvider[], options: IDataAnimationServiceOptions);
        /**
            *
            * @param interval 设置动画间隔
            * @returns
            */
        setInterval(interval: number): void;
        /**
            * 设置是否循环播放
            * @param loop 是否循环播放
            */
        setLoop(loop: boolean): void;
        /**
            * 设置播放间隔
            * @param tDelta 播放间隔
            */
        setDelta(tDelta: number): void;
        /**
            * 开始动画
            * @returns 当前动画实例
            */
        start(): this;
        /**
            * 暂停播放
            * @returns 当前动画实例
            */
        pause(): this;
        /**
            * 恢复播放
            * @returns 当前动画实例
            */
        resume(): this;
        /**
            * 停止播放
            * @returns 当前动画实例
            */
        stop(): this;
}

export class LogService {
        static DefaultStyles: {
                logStyle: string;
                warnStyle: string;
                errorStyle: string;
                noteStyle: string;
        };
        /**
            * 输出一个错误日志
            * @param err 具体错误内容
            * @param throwError 是否抛出异常，默认是
            * @param caller 调用对象
            */
        error(err: any, throwError?: boolean, caller?: any, executor?: any): void;
        /**
            * 输出一段警告消息
            * @param msg 警告消息内容
            * @param caller 调用对象
            */
        warn(msg: any, caller?: any, executor?: any): void;
        /**
            * 输出一段日志信息
            * @param msg 日志内容
            * @param caller 调用对象
            */
        log(msg: any, caller?: any, executor?: any): void;
        /**
            * 输出一段调试信息，当consts常量中的DEBUG为true的时候才会输出
            * @param msg 调试信息内容
            * @param caller 调用对象
            */
        debug(msg: any, caller?: any, executor?: any): void;
        /**
            * 输出一段需要强调的话，将使用加粗高亮样式
            * @param msg
            * @param caller
            * @param executor
            */
        note(msg: any, caller?: any, executor?: any): void;
        /**
            * 输出一段需要强调的话，将使用加粗高亮样式
            * @param msg
            * @param caller
            * @param executor
            */
        custom(msg: any, cssTextStyle?: string, caller?: any, executor?: any): void;
}
/**
    * 日志服务
    */
export const logger: LogService;
export const updateLoggerDebugMode: () => void;

/**
    * 资源描述信息，当加载一个资源的时候需要提供其描述信息
    *
    * @export
    * @interface IResourceInfo
    */
export interface IResourceInfo {
        /**
            *
            * 资源请求信息，可以是以下几种类型：
            * - 一个url地址字符串
            * - 一个详细的请求信息
            * - 对应资源实例的构造参数
            * - 对应资源的实例（此种情况推荐直接使用addResource添加到资源中）
            * @type {(RequestInfo | any)}
            * @memberof IResourceInfo
            */
        requestInfo: RequestInfo | any;
        /**
            * 资源类型
            * - images 图片
            * - stopRules 分段规则
            * - featureStyles 矢量样式
            *
            * @type {string}
            * @memberof IResourceInfo
            */
        resourceType: string;
        /**
            * 资源的唯一ID **相同ID的资源会被覆盖**
            *
            * @type {string}
            * @memberof IResourceInfo
            */
        key: string;
        /**
            * 请求的初始化信息
            *
            * @type {RequestInit}
            * @memberof IResourceInfo
            */
        init?: RequestInit;
}
/**
    * 预定义的资源集合配置对象。
    * 如果希望增加自定义的资源支持，可以注册creator用于创建自定义类型的资源实例
    * 该对象中的key就是资源的类型
    * 每一个二级对象中的key表示具体资源的唯一ID，如下所示
    * ```typescript
    * //这里是资源的类型
    * featureStyles:{
    *      //这里是资源的ID
    *      style1:{
    *      },
    *      style2:{
    *      }
    * },
    * images:{
    *      img1:"",
    *      img2:""
    * }
    * ```
    * @export
    * @interface IResourceConfigPredefined
    */
export interface IResourceConfigPredefined {
        /**
            *
            * 分段规则集合
            * @type {{ [key: string]: IStopRulesOptions }}
            * @memberof IResourceConfigPredefined
            */
        stopRules?: {
                [key: string]: IStopRulesOptions;
        };
        /**
            *
            * 矢量样式集合
            * @type {{ [key: string]: IFeatureStyleOptions }}
            * @memberof IResourceConfigPredefined
            */
        featureStyles?: {
                [key: string]: IFeatureStyleOptions;
        };
        /**
            * 格点样式集合
            */
        gridStyles?: {
                [key: string]: any;
        };
        /**
            *
            * 图片资源集合
            * @type {{ [key: string]: ImageLike }}
            * @memberof IResourceConfigPredefined
            */
        images?: {
                [key: string]: ImageLike;
        };
        /**
            *
            * 图例资源集合（实际是图片）
            * @type {{ [key: string]: ImageLike }}
            * @memberof IResourceConfigPredefined
            */
        colorScales?: {
                [key: string]: ImageLike;
        };
        /**
            *
            * 二进制文件集合
            * @type {string[]}
            * @memberof IResourceConfigPredefined
            */
        rawFiles?: {
                [key: string]: string;
        };
        /**
            *
            * json文件集合
            * @type {string[]}
            * @memberof IResourceConfigPredefined
            */
        jsonFiles?: {
                [key: string]: string;
        };
        /**
            * 字符串集合
            */
        texts?: {
                [key: string]: string;
        };
}
/**
    * 一个实例化之后的资源对象
    *
    * @export
    * @interface IResourceObject
    */
export interface IResourceObject {
        /**
            * 原始资源配置信息，当外部加载后添加可能没有该字段
            *
            * @type {*}
            * @memberof IResourceObject
            */
        source?: IResourceInfo;
        /**
            * 资源实例
            *
            * @type {*}
            * @memberof IResourceObject
            */
        instance: any;
        /**
            * 资源类型
            *
            * @type {string}
            * @memberof IResourceObject
            */
        resourceType: string;
}
/**
    * 资源集合配置信息，预定义的资源配置对象信息参考[[IResourceConfigPredefined]]
    */
export type IResourceConfig = IResourceConfigPredefined | any;
/**
    * 资源实例构建器
    */
export type ResourceCreator = (resourceInfo: IResourceInfo) => Promise<IResourceObject>;
/**
    * 预定义的资源类型
    */
export const resourceTypePreDefined: {
        images: string;
        stopRules: string;
        featureStyles: string;
        gridStyles: string;
        jsonFiles: string;
        rawFiles: string;
        texts: string;
        colorScales: string;
};
/**
    * 图片资源构建器
    * @param resourceInfo 资源描述信息
    * @returns 返回图片实例的Promise对象
    */
export const imageResourceCreator: ResourceCreator;
/**
    * 依次判断资源描述中的请求信息是否是已经是资源实例
    * 如果是，直接构建对象返回，
    * 如果不是，再判断是否是资源构造参数，如果是，构建资源实例返回
    * 如果不是，使用请求对象请求json类型对象，将请求结果作为资源构造参数构造资源实例并返回
    * @param resourceInfo 资源描述信息
    * @param isInstance 是否是资源实例的判断函数
    * @param optionsChecker 是否是资源构造参数的判断函数
    * @param asJson 是否是JSON类型，默认为true
    */
export const resourceCreatorBase: (resourceInfo: IResourceInfo, isInstance: (content: any) => boolean, optionsChecker: (content: any) => boolean, asJson?: boolean) => Promise<any>;
/**
    * 构建json对象缓存
    * @param resourInfo 资源描述信息
    */
export const jsonFileResourceCreator: ResourceCreator;
/**
    * 构建数据buffer资源
    * @param resourInfo 资源描述信息
    */
export const rawFileResourceCreator: ResourceCreator;
/**
    * 构建分段规则的资源构造参数，如调色板
    * @param resourceInfo 资源描述信息
    */
export const stopRuleResouceCreator: ResourceCreator;
/**
    * 构建矢量样式的资源构造参数
    * @param resourceInfo 资源描述信息
    */
export const featureStyleResourceCreator: ResourceCreator;
/**
    * 构建格点样式的资源构造参数
    * @param resourceInfo 资源描述信息
    */
export const gridStyleResourceCreator: ResourceCreator;
/**
    * 资源管理器，提供了资源的加载、更新和删除操作，支持从配置文件或者构造参数构建资源，提供了资源ID级别的事件监听
    * 具有复用价值的资源才需要使用管理器管理
    *
    * @class ResourceService
    * @extends {Evented}
    */
export class ResourceService extends Evented {
        /**
            *
            * 注册资源的构建器
            * @static
            * @param {string} resourceType 支持的资源类型
            * @param {ResourceCreator} creator 构建函数
            * @memberof ResourceService
            */
        static registerCreator(resourceType: string, creator: ResourceCreator): void;
        /**
            *
            * 资源的事件基类型，监听某个具体资源的事件，可以使用 基类型+":"+资源ID 进行监听，如：
            * res:add:style1（即`ResourceService.EventTypes.resAdd+":"+"style1"` ），表示对style1这个资源的增删改进行监听
            * @static
            * @memberof ResourceService
            */
        static EventTypes: {
                /**
                    * 当资源第一次被加载之后触发
                    */
                resAdd: string;
                /**
                    * 当资源被移出之后触发
                    */
                resRemoved: string;
                /**
                    * 当资源被更新后触发
                    */
                resUpdated: string;
                /**
                    * 当资源被加载之后触发（第一次加载或者更新都会触发）
                    */
                resLoaded: string;
        };
        /**
            *注册实例构建器
            *
            * @memberof ResourceService
            */
        registerCreator: typeof ResourceService.registerCreator;
        get allResources(): {
                [key: string]: any;
        };
        /**
            * 添加一个资源实例
            *
            * @param {string} key 资源ID
            * @param {IResourceObject} resourceObj 实例对象
            * @param updateIfExists 资源存在的时候是否进行更新，默认为否
            * @memberof ResourceService
            */
        addResource(key: string, resourceObj: IResourceObject, updateIfExists?: boolean): void;
        /**
            *
            * 加载一个资源实例
            * @param {IResourceInfo} resourceInfo 资源描述信息。
            * @param updateIfExists 如果当前资源已经存在，则更新。默认为否
            * @return {*}  {Promise<IResourceObject>} 返回一个Promise对象，当资源加载完成后进行回调
            * @memberof ResourceService
            */
        loadResource(resourceInfo: IResourceInfo, updateIfExists?: boolean): Promise<IResourceObject>;
        /**
            *
            * 加载一组资源
            * @param {IResourceInfo[]} infos 资源描述信息
            * @return {*}  {Promise<IResourceObject[]>} 返回一个Promise对象，当所有资源加载完成后回调，当有一个资源出现失败即加载失败
            * @memberof ResourceService
            */
        loadResources(infos: IResourceInfo[]): Promise<IResourceObject[]>;
        /**
            *
            * 从资源配置对象信息加载资源
            * @param {IResourceConfig} resourceConfig 资源配置对象。
            * 预定义的资源集合配置对象。
            * 如果希望增加自定义的资源支持，可以注册creator用于创建自定义类型的资源实例
            * 该对象中的key就是资源的类型
            * 每一个二级对象中的key表示具体资源的唯一ID，如下所示
            * ```typescript
            * //这里是资源的类型
            * featureStyles:{
            *      //这里是资源的ID
            *      style1:{
            *      },
            *      style2:{
            *      }
            * },
            * images:{
            *      img1:"",
            *      img2:""
            * }
            * ```
            * @param updateIfExists 如果资源存在的时候，是否重新加载。默认为false，表示不重新加载。
            * 注意如果是相同的资源应该只在一个地方加载
            * @return {*}  {Promise<IResourceObject[]>} 返回一个Promise对象，当所有资源全部加载完成后回调
            * @memberof ResourceService
            */
        loadResourceFromConfig(resourceConfig: IResourceConfig, updateIfExists?: boolean): Promise<IResourceObject[]>;
        /**
            *
            * 从一个配置地址请求资源。
            * 该配置文件返回的信息为预定义的资源集合配置对象。
            * 如果希望增加自定义的资源支持，可以注册creator用于创建自定义类型的资源实例
            * 该对象中的key就是资源的类型
            * 每一个二级对象中的key表示具体资源的唯一ID，如下所示
            * ```typescript
            * //这里是资源的类型
            * featureStyles:{
            *      //这里是资源的ID
            *      style1:{
            *      },
            *      style2:{
            *      }
            * },
            * images:{
            *      img1:"",
            *      img2:""
            * }
            * ```
            * @param {RequestInfo} requestInfo 请求信息
            * 绝对路径请以/开头，否则认为是相对于静态资源目录下的目录（consts.resourcePath中配置静态资源目录，默认是public）
            * @param {RequestInit} [init]
            * @param updateIfExists 如果资源存在的时候，是否重新加载。默认为false，表示不重新加载。
            * 注意如果是相同的资源应该只在一个地方加载
            * @return {*}  {Promise<IResourceObject[]>}
            * @memberof ResourceService
            */
        loadResourceFromConfigPath(requestInfo: RequestInfo, init?: RequestInit, updateIfExists?: boolean): Promise<IResourceObject[]>;
        /**
            *
            * 获取一个资源实例
            * @param {string} key 要获取的key
            * @return {*}  {*} 资源实例
            * @memberof ResourceService
            */
        getResource(key: string): IResourceObject;
        /**
            *
            * 移除一个资源实例
            * @param {string} key 要移除的key
            * @return {*}  {boolean} 返回是否成功移除
            * @memberof ResourceService
            */
        removeResource(key: string): boolean;
        /**
            *
            * 检查是否存在指定key的资源实例
            * @param {string} key
            * @return {*}  {boolean} 返回是否存在
            * @memberof ResourceService
            */
        hasResource(key: string): boolean;
        /**
            * 添加指定资源第一次增加的监听（删除后再添加也算第一次增加）
            *
            * @param {string} key 要监听的资源ID
            * @param added 回调函数
            * @memberof ResourceService
            */
        whenResourceAdded(key: string, added: (resource: IResourceObject) => void): Promise<IResourceObject>;
        /**
            * 添加指定资源被更新后的回调
            *
            * @param {string} key 要监听的资源ID
            * @param {(oldResource: IResourceObject, newResource: IResourceObject) => void} updated 回调函数
            * @memberof ResourceService
            */
        whenResourceUpdated(key: string, updated: (oldResource: IResourceObject, newResource: IResourceObject) => void): void;
        /**
            * 添加指定资源被加载后的回调
            *
            * @param {string} key 要监听的资源ID
            * @param {(resource: IResourceObject) => void} loaded 回调函数
            * @memberof ResourceService
            */
        whenResourceLoaded(key: string, loaded: (resource: IResourceObject) => void): void;
        /**
            * 添加指定资源被移出后的回调
            *
            * @param {string} key 要监听的资源ID
            * @param {(resource: IResourceObject) => void} removed 回调函数
            * @memberof ResourceService
            */
        whenResourceRemoved(key: string, removed: (resource: IResourceObject) => void): void;
}
/**
    * 内置的资源管理器，提供了资源的加载、更新和删除操作，支持从配置文件或者构造参数构建资源，提供了资源ID级别的事件监听
    */
export const resourceService: ResourceService;

/**
    * 等值线色斑图追踪配置
    */
export interface ITracingContourShadedOptions<TLayer, TLayerOptions> {
        /**
            * 数据源
            */
        dataSource: IGridDataProvider;
        /**
            * 是否生成色斑图信息
            */
        withShaded: boolean;
        /**
            * 矢量图形的绘制参数配置
            */
        drawOptions?: IFeatureStyleOptions | FeatureStyle | string;
        /**
            * 追踪结果的字段名，默认为value
            */
        valueFieldName?: string;
        /**
            * 缺测值，如果数据中没有自带，使用该缺测值
            */
        undef?: number;
        /**
            * 分析值列表
            */
        analysisValues: number[];
        /**
            * 矢量图层构建参数
            */
        layerOptions?: TLayerOptions;
        /**
            * 追踪返回值中是否包含追踪的原始信息，如果不包含，则只返回相应的图层
            */
        reserveTracingInfo?: boolean;
}
/**
    * 从离散点进行等值线追踪的配置参数
    *
    * @export
    * @interface ITracingContourShadedFromPointsOptions
    * @template TLayer
    * @template TLayerOptions
    */
export interface ITracingContourShadedFromPointsOptions<TLayer, TLayerOptions> {
        /**
            *
            * 矢量点数据访问器
            * @type {IFeaturesProvider}
            * @memberof ITracingContourShadedFromPointsOptions
            */
        points: IFeaturesProvider;
        /**
            *
            * 要追踪的字段名称
            * @type {string}
            * @memberof ITracingContourShadedFromPointsOptions
            */
        fieldName: string;
        /**
            *
            * 追踪过程中生成网格的属性，网格越密，耗时越久
            * @type {IGridDataOptions}
            * @memberof ITracingContourShadedFromPointsOptions
            */
        gridOpts: IGridDataOptions;
        /**
            *
            * 是否追踪色斑图
            * @type {boolean}
            * @memberof ITracingContourShadedFromPointsOptions
            */
        withShaded: boolean;
        /**
            *
            * 图层的绘制参数
            * @type {(IFeatureStyleOptions | FeatureStyle | string)}
            * @memberof ITracingContourShadedFromPointsOptions
            */
        drawOptions?: IFeatureStyleOptions | FeatureStyle | string;
        /**
            *
            * 生成的追踪结果中，字段存储的名字，默认和fielName一致
            * @deprecated 该设置仅针对遗留的分析算法生效，wasm算法中生成的等值线色斑图的属性字段值固定为value
            * @type {string}
            * @memberof ITracingContourShadedFromPointsOptions
            */
        valueFieldName?: string;
        /**
            *
            * 数据中的缺测值
            * @type {number}
            * @memberof ITracingContourShadedFromPointsOptions
            */
        undef: number;
        /**
            *
            * 分析值列表
            * @type {number[]}
            * @memberof ITracingContourShadedFromPointsOptions
            */
        analysisValues: number[];
        /**
            *
            * 构建图层的参数
            * @type {TLayerOptions}
            * @memberof ITracingContourShadedFromPointsOptions
            */
        layerOptions?: TLayerOptions;
        /**
            *
            * 是否保留追踪结果，如果是，则返回追踪的中间结果
            * @type {boolean}
            * @memberof ITracingContourShadedFromPointsOptions
            */
        reserveTracingInfo?: boolean;
}
/**
    * 色斑图的追踪配置
    *
    * @export
    * @interface ITracingShadedOptions
    * @template TLayer
    * @template TLayerOptions
    */
export interface ITracingShadedOptions<TLayer, TLayerOptions> {
        /**
            *
            * 用于追踪的数据源
            * @type {IGridDataProvider}
            * @memberof ITracingShadedOptions
            */
        dataSource: IGridDataProvider;
        /**
            *
            * 等值线的追踪结果
            * @type {IWContourlineResult<TLayer>}
            * @memberof ITracingShadedOptions
            */
        contourInfo: IWContourlineResult<TLayer>;
        /**
            *
            * 分析值列表
            * @type {number[]}
            * @memberof ITracingShadedOptions
            */
        analysisValues: number[];
        /**
            *
            * 图层的绘制参数
            * @type {(IFeatureStyleOptions | FeatureStyle | string)}
            * @memberof ITracingShadedOptions
            */
        drawOptions?: IFeatureStyleOptions | FeatureStyle | string;
        /**
            * 构建图层的参数
            *
            * @type {TLayerOptions}
            * @memberof ITracingShadedOptions
            */
        layerOptions?: TLayerOptions;
        /**
            * 追踪结果的字段名，默认为value
            *
            * @type {string}
            * @memberof ITracingShadedOptions
            */
        valueFieldName?: string;
}
/**
    * 追踪结果
    *
    * @export
    * @interface IWContourlineResult
    * @template TLayer
    */
export interface IWContourlineResult<TLayer> {
        /**
            *
            * 等值线的追踪结果
            * @type {WCPolyLine[]}
            * @memberof IWContourlineResult
            */
        contourlines?: WCPolyLine[];
        /**
            *
            * 等值线追踪的边界信息
            * @type {WCBorder[]}
            * @memberof IWContourlineResult
            */
        borders?: WCBorder[];
        /**
            *
            * 追踪后生成的图层
            * @type {TLayer}
            * @memberof IWContourlineResult
            */
        layer: TLayer;
        /**
            *
            * 追踪的数据源
            * @type {IFeaturesProvider}
            * @memberof IWContourlineResult
            */
        dataSource?: IFeaturesProvider;
        /**
            *
            * 追踪结果图层的绘制参数
            * @type {(IFeatureStyleOptions | FeatureStyle | string)}
            * @memberof IWContourlineResult
            */
        drawOptions?: IFeatureStyleOptions | FeatureStyle | string;
}
/**
    * 追踪服务配置参数
    *
    * @export
    * @interface ITracingServiceOptions
    * @template TLayer
    * @template TLayerOptions
    */
export interface ITracingServiceOptions<TLayer, TLayerOptions> {
        layerCreator: (layerOptions: TLayerOptions, dataSource: IFeaturesProvider, drawOptions: IFeatureStyleOptions | FeatureStyle | string) => TLayer;
}
/**
    * 流线的追踪配置
    *
    * @export
    * @interface ITracingStreamlineOptions
    * @template TLayer
    * @template TLayerOptions
    */
export interface ITracingStreamlineOptions {
        /**
            *
            * 用于追踪的数据源
            * @type {IGridDataProvider}
            * @memberof ITracingStreamlineOptions
            */
        dataSource: IWindDataProvider;
        /**
            * 缺测值，如果不传入则使用数据中的缺测值
            */
        undef?: number;
        /**
            * 追踪算法的积分步长，单位是米，默认是数据的水平分辨率*55000，大约为网格分辨率的一半
            *
            * 用于控制追踪的精确度，步长越小越精确，计算耗时越长，通常设置为网格分辨率的一半即可
            *
            * 该参数需要结合allLoop和stayLoop参数设置，避免追踪未完成或者长时间停留在相同的网格点（速度很小时）
            */
        step?: number;
        /**
            * 水平方向的追踪密度，默认是数据的xDelta，用于控制结果的疏密
            *
            * 数据越小越密集，计算越耗时，建议设置为略小于或等于数据的水平分辨率的小值
            */
        hReso?: number;
        /**
            * 垂直方向的追踪密度，默认是1000米，越小越耗时，建议设置为数据的垂直分辨率相同或略小
            */
        vReso?: number;
        /**
            * 每条流线允许的最大迭代分析次数，默认是50000，避免由于参数设置不合理导致的无法退出
            */
        allLoop?: number;
        /**
            * 每条流线在同一个点停留的最大迭代次数，默认为10次，即如果连续前进10次还没有跳出该格点则停止分析
            *
            * 用于在风速很小时停止分析
            */
        stayLoop?: number;
        /**
            * 线条的最少点数，默认为10，即点数小于该数量的线条不会被返回
            *
            * 用于排除零散的分析线条
            */
        lineMinCount?: number;
        /**
            * 是否分析反方向，默认true
            */
        backward?: boolean;
        /**
            * 分析的z层次索引，从0开始，-1表示分析整层数据
            *
            * 当设置了wProvider切dataSource层次信息与垂直速度一致时候，默认为-1，否则默认为0
            */
        zIdx?: number;
        /**
            * 线程数量，默认为1，需要综合任务总数和cpu核心数，线程调度存在一定的数据复制成本，如果显示的比较稀疏，需要的line数量较少，建议设置为单线程
            *
            * 一般情况下建议单线程分析，这样可以获得视觉上较好的结果（互相遮盖的线条进行了去重）
            *
            * 多线程由于浏览器限制无法方便的在http模式下支持实时共享各个线程的状态，因此无法进行快速去重，可能会出现较多的离得很近的线条
            */
        threadCount?: number;
        /**
            * 希望分析到的线条的数量，用于确定分析的次数，而非表示最终返回的结果中的线条数量
            * 默认为1000，数量越大分析耗时越长，但是分布会更均匀
            */
        lineCount?: number;
        /**
            * 每个批次分析的线条数量，如设置为500，当lineCount为1000的时候，则需要分两个批次提交
            *
            * 默认为Max(parseInt(lineCount/threaCount)+1,threadCount)
            *
            * 不建议修改默认值
            */
        batchSize?: number;
        /**
            * z方向的拉升系数，为了更好的观察高度层上的变化，建议对z轴高度进行拉升，默认为1
            *
            * 此参数不改变uvw之间的速度比例
            */
        zScale?: number;
        /**
         * 是否禁用密度控制，默认为false，禁用后会增加分析耗时，需要通过调整最大允许迭代次数和同一个点最多停留次数来控制分析结束的条件
         */
        disableDensityControl?: boolean;
        /**
            * 位置生成器，默认是随机点，当zValues长度大于1时会生成带高度的点
            */
        seedGenerator?: (i: any) => number[];
        /**
            * 是否仅返回二维点，默认false
            */
        point2D?: boolean;
        /**
            * 是否需要生成管线，默认false
            */
        needVolume?: boolean;
        /**
            * 生成管线的参数
            */
        volumeOptions?: {
                /**
                    * 平滑系数，默认为1，越高越平滑，计算时间越久
                    */
                tubularSegmentsTimes?: number;
                /**
                    * 管道半径，单位是米，默认3000米
                    */
                radius?: number;
                /**
                    * 管道圆边的数量[1-360]，默认45，越大管线越圆，计算越耗时
                    */
                radiusSegment?: number;
        };
}
/**
    * 等值线色斑图追踪服务
    *
    * @export
    * @class TracingService
    * @template TLayer 生成的图层类型
    * @template TLayerOptions 生成的图层构建参数
    */
export class TracingService<TLayer, TLayerOptions> {
        protected options: ITracingServiceOptions<TLayer, TLayerOptions>;
        /**
            * 等值线色斑图样式的默认配置参数
            *
            * polyline: {
            *      color: "rgba(255,255,255,0.5)",
            *      label: [
            *          {
            *              text: {
            *                  data: "$value",
            *                  backColor: "#edf2ba",
            *                  backPadding: [1, 1],
            *                  backRoundRect: true,
            *                  backHeight: 12,
            *                  color: "black",
            *                  font: "12px Arial",
            *                  backShadowColor: "gray",
            *                  backShadowOffset: [2, 2]
            *              }
            *          }
            *      ]
            *  },
            *  polygon: {
            *
            *  }
            *
            * @static
            * @type {IFeatureStyleOptions}
            * @memberof TracingService
            */
        static DefaultContourShadedStyle: IFeatureStyleOptions;
        /**
            * 默认的颜色分级配置
            *
            * @static
            * @memberof TracingService
            */
        static DefaultColorStopOptions: {
                stops: {
                        value: number;
                        stop: string;
                }[];
                field: string;
        };
        /**
            * 构建默认的渐变规则配置
            *
            * @static
            * @param {number[]} analysisValues 分析值列表
            * @param {string} [fieldName] 生成的渐变规则中的fielName
            * @param {number} [undef] 缺测值
            * @return {*}
            * @memberof TracingService
            */
        static createDefaultColorStops(analysisValues: number[], fieldName?: string, undef?: number): IStopRulesOptions;
        /**
            * 构建 TracingService
            * @param {ITracingServiceOptions<TLayer, TLayerOptions>} options 构建参数
            * @memberof TracingService
            */
        constructor(options: ITracingServiceOptions<TLayer, TLayerOptions>);
        /**
            * 使用等值线追踪的结果继续追踪色斑图
            * @deprecated 请使用tracingContourShadedWASM进行格点数据的等值线色斑图追踪
            * @param {ITracingShadedOptions<TLayer, TLayerOptions>} options 追踪配置
            * @return {TLayer} 追踪生成的图层
            * @memberof TracingService
            */
        traceShadedWithContourResult(options: ITracingShadedOptions<TLayer, TLayerOptions>): TLayer;
        /**
            * 使用格点数据追踪等值线色斑图
            * @deprecated 请使用tracingContourShadedWASM替代，速度更快
            * @param {ITracingContourShadedOptions<TLayer, TLayerOptions>} options 追踪配置
            * @return {*}  {IWContourlineResult<TLayer>}
            * @memberof TracingService
            */
        tracingContourShaded(options: ITracingContourShadedOptions<TLayer, TLayerOptions>): IWContourlineResult<TLayer>;
        tracingContourShadedWASM(options: ITracingContourShadedOptions<TLayer, TLayerOptions>): Promise<IWContourlineResult<TLayer>>;
        /**
            * 使用wasm中的算法进行插值，实际测试表明，该算法编译后在chrome上执行速度不理想。
            * @deprecated 请使用interpToGridProvider获得更快的插值速度
            * @param gridOpts
            * @param provider
            * @param fieldName
            * @param undef
            * @returns
            */
        static interpToGridWASM(gridOpts: IGridDataOptions, provider: IFeaturesProvider, fieldName: string, undef?: number): Promise<IGridDataProvider>;
        /**
            * 经过优化的idw插值算法
            * @param gridOpts
            * @param provider
            * @param fieldName
            * @param undef
            * @returns
            */
        static interpToGridProvider(gridOpts: IGridDataOptions, provider: IFeaturesProvider, fieldName: string, undef?: number): MemoryGridDataProvider;
        /**
            * @deprecated 该算法未经优化，建议使用经过优化的interpToGridProvider算法
            * @param gridOpts
            * @param provider
            * @param fieldName
            * @param undef
            * @returns
            */
        static interpToGrid(gridOpts: IGridDataOptions, provider: IFeaturesProvider, fieldName: string, undef?: number): MemoryGridDataProvider;
        /**
            * 使用站点数据追踪等值线色斑图
            * @deprecated 建议使用tracingContourShadedFromPointsWASM
            * @param {ITracingContourShadedFromPointsOptions<TLayer, TLayerOptions>} options 追踪配置
            * @return {*}
            * @memberof TracingService
            */
        tracingContourShadedFromPoints(options: ITracingContourShadedFromPointsOptions<TLayer, TLayerOptions>): IWContourlineResult<TLayer>;
        /**
            * 使用站点数据追踪等值线色斑图
            *
            * @param {ITracingContourShadedFromPointsOptions<TLayer, TLayerOptions>} options 追踪配置
            * @return {*}
            * @memberof TracingService
            */
        tracingContourShadedFromPointsWASM(options: ITracingContourShadedFromPointsOptions<TLayer, TLayerOptions>): Promise<IWContourlineResult<TLayer>>;
        /**
            * 对格点数据进行风流场追踪，分析的结果是矢量provider
            * 对于显示静态风流场，可以添加一个箭头来表示风的方向，矢量样式需要一个箭头方向朝右的图片来作为贴图
            * 参考配置如下（贴图为32x32，显示为16x16，以便在Retina屏下获得较好的效果）：
            * {
                        polyline: {
                                color: "black",
                                label: [
                                        {
                                                image: {
                                                        data: "arrow#res",  //arrow为贴图的资源id，关于资源的使用请参考文档
                                                        size: [16, 16],
                                                        offset: [-8, -8]
                                                }
                                        }
                                ],
                                labelAutoFlip: false  //务必设置为false，避免系统自动翻转箭头的方向
                        }
                }
            * @param options 追踪选项
            * @param completeOne 当一个批次追踪结束时的回调，一般用于大量数据时分批渐进显示，可以提供较好的用户体验。也可以直接等最终全部分析完成再显示
            * @returns 分析出来的矢量provider
            */
        tracingStreamLinesAsync(options: ITracingStreamlineOptions, completeOne?: (data: {
                /**
                    * 当前批次的原始点结果
                    */
                streamlines: number[][][];
                /**
                    * 到当前为止所有已经分析出来的结果
                    */
                allFC: GeoJSON.FeatureCollection<GeoJSON.LineString>;
                /**
                    * 当前批次分析出来的结果
                    */
                fc: GeoJSON.FeatureCollection<GeoJSON.LineString>;
        }) => void): Promise<{
                provider: GeoJSONFeatureProvider;
                streamlines: number[][][];
        }>;
}

/**
    * 地图工具服务
    *
    * @export
    * @class MapToolService
    */
export class MapToolService {
        protected currentTool: IMapTool;
        /**
            * 构建 MapToolService
            * @param {*} map
            * @memberof MapToolService
            */
        constructor(map: any);
        /**
            * 获取当前活跃的地图工具，如果为空说明当前没有使用任何地图绘制工具
            *
            * @return {*}
            * @memberof MapToolService
            */
        getCurrent(): IMapTool;
        /**
            * 设置当前活跃的地图工具，先取消上一次地图工具的活跃状态，然后设置当前工具为活跃状态。如果当前设置为空，则表示不使用任何地图工具
            *
            * @param {IMapTool} tool 地图工具
            * @memberof MapToolService
            */
        setCurrent(tool: IMapTool): void;
}

export type LegendServiceStopsChangedFunc = () => void;
export class LegendService extends Evented {
        static EventTypes: {
                /**
                    * 每当有图例增加时触发，不管key是否相同都会触发
                    */
                attach: string;
                /**
                    * 当某个key的图例第一次被增加时触发
                    */
                attachFirst: string;
                /**
                    * 当某个key的图例最后一个被移除时候触发
                    */
                detachLast: string;
                /**
                    * 当有图例被移除的时候触发
                    */
                detach: string;
                /**
                    * 当有透明度发生变化的时候触发
                    */
                changeOpacity: string;
        };
        protected stopRulesMap: {
                [key: string]: {
                        sr: StopRules;
                        changed: LegendServiceStopsChangedFunc[];
                };
        };
        /**
            * 增加一个调色板的监听，一般在图层增加的时候调用，在图例组件中监听相关事件实现视图更新
            *
            * @param {string} key 调色板的资源key，同样的key表示相同的调色板
            * @param {LegendServiceStopsChangedFunc} onChanged 当调色板数值发生改变时的回调
            * @param {(IStopRulesOptions | StopRules)} [sr] 调色板。默认使用key作为资源名称从资源服务获取调色板对象
            * @memberof LegendService
            */
        attach(key: string, onChanged: LegendServiceStopsChangedFunc, sr?: IStopRulesOptions | StopRules): void;
        /**
            * 对色标进行过滤，一般可以构建一个色标组件，在组件中相关参数发生变化的时候调用该方法
            *
            * @param {string} key 用于查找色标的key，在attach的时候提供
            * @param {number} min 开始值
            * @param {number} [max] 结束值
            * @param {StopRuleItemFilterMode} [mode=StopRuleItemFilterMode.outside] 过滤方式，默认是outside，即将区间外的值透明度设置为指定透明度
            * @param opacity 透明度，默认为0
            * @memberof LegendService
            */
        setOpacity(key: string, min: number, max?: number, mode?: StopRuleItemFilterMode, opacity?: number): void;
        /**
            * 清除指定key的回调。一般在图层被移除的时候调用，在图例组件中监听相关事件实现视图更新
            *
            * @param {string} key 指定的调色板key
            * @param {LegendServiceStopsChangedFunc} [onChanged] 要清除的回调
            * @memberof LegendService
            */
        detach(key: string, onChanged?: LegendServiceStopsChangedFunc): void;
}

export class DAPParserType {
    type: any;
    attributes: any;
    id: any;
    name: any;
    dimensions: any;
    shape: any;
    array: any;
    maps: any;
    constructor(type?: any);
}
export abstract class SimpleDapInfoParser {
    stream: any;
    constructor(input: any);
    protected peek(expr: any): any;
    protected consume(expr: any): any;
    abstract parse(): any;
}
export class DDSParser extends SimpleDapInfoParser {
    parse(): DAPParserType;
}
export class DASParser extends SimpleDapInfoParser {
    constructor(das: any, dataset: any);
    parse(): any;
}

export interface IDAPFilter {
        varName: string;
        tIdx?: number;
        xStartIdx?: number;
        xEndIdx?: number;
        yStartIdx?: number;
        yEndIdx?: number;
        zIdx?: number;
}
export interface IDAPFullFilter {
        varName: string;
        tFilter?: string;
        zFilter?: string;
        xFilter?: string;
        yFilter?: string;
}
export interface IDAPServiceResult {
        dds: any;
        dods: any;
        gridOptions: IGridDataOptions;
        provider: IGridDataProvider;
}
export class DAPService extends Evented {
        baseUrl: any;
        /**
            * 构建 DAPService.
            * @param {*} baseUrl 不含服务后缀的请求路径，注意大部分OPeNDAP服务并不支持跨域，需要自行代理
            * @param {string[]} [varList=[]] 请求DDS时的要素过滤列表，只请求列表中的要素头信息，请根据实际需要使用的要素，默认为空表示全请求全部头信息
            * @param {boolean} [load=false] 是否构建的时候就请求头信息
            * @memberof DAPService
            */
        constructor(baseUrl: any, varList?: string[], load?: boolean);
        get dataInfo(): any;
        loadDataInfo(forceReload?: boolean, resolveWhenLoading?: boolean): Promise<any>;
        /**
            * 加载数据，返回数据原始信息。
            *
            * @param {string} filter 要素过滤器
            * @return {*}  {Promise<IDODSResult>}
            * @memberof DAPService
            */
        loadDataByRawFilter(filter: string): Promise<IDODSResult>;
        /**
            * 使用原生过滤器获取数据，未传递的字段默认为所有长度
            * @param filter [0:1:0] 表示只取第一个与[0]相同，[0:1:9]表示取0-9这10个,[0:2:9]表示取 0 2 4 6 8这5个
            * @returns
            */
        loadDataByFullFilter(filter: IDAPFullFilter): Promise<any>;
        getFullGridOptions(varName: string, includeTValues?: boolean): Promise<IGridDataOptions>;
        /**
            * 将dods结果解析为provider，如果固定了经纬度点无法解析为二维格点场
            * @param res
            * @param varName
            * @returns
            */
        dapResultToProvider(res: IDODSResult, varName: string): IDAPServiceResult;
        /**
            *
            * 加载数据并生成provider。
            * 暂时不支持自动设置数据中的缩放和偏移以，可以自行根据返回的头信息内容修改GridData中的相关参数
            * missing_value大于999999时，将被设置为999999。如果自动设置的缺测值不正确，可以自行调整
            * @param {IDAPFilter} filter 要素过滤器
            * @return {*}  {Promise<IDAPServiceResult>}
            * @memberof DAPService
            */
        loadData(filter: IDAPFilter): Promise<IDAPServiceResult>;
}

export interface IDODSResult {
    dds: any;
    dods: any;
}
export interface IDODOSUnpackerOptions {
    reshape?: boolean;
}
export class DODSUnpacker {
    static DefaultOptions: IDODOSUnpackerOptions;
    protected _view: DataView;
    dapvar: any;
    protected options: IDODOSUnpackerOptions;
    constructor(dodsBuffer: any, options?: IDODOSUnpackerOptions);
    unpack(): IDODSResult;
    protected getValue(): any;
}

export interface IFrameLineStyle {
        visible?: boolean;
        dashArray?: number[];
        width?: number;
        color?: string;
}
export interface IFrameContentStyle {
        /**
            * 左上padding距离
            */
        ltOffset?: number[];
        /**
            * 右下的padding距离
            */
        rbOffset?: number[];
}
export interface IFrameTickStyle {
        position?: "cross" | "in" | "out";
        length?: number;
        minorCount?: number;
        line?: IFrameLineStyle;
}
export interface IFrameTextStyle {
        visible?: boolean;
        font?: string;
        color?: string;
        offset?: number[];
        textAlign?: string;
        textBase?: string;
        /**
            * 旋转角度，默认是0，单位是度
            */
        angle?: number;
        formater?: (idx: number, lbl: string) => string;
}
export interface IFrameTitleStyle {
        /**
            * 默认center。start是基于content的开始位置，end是基于content的结束位置
            *
            * 设置为start或者end的时候，一般需要同步调整标题的textAlign属性
            */
        position?: "start" | "end" | "center";
        main?: IFrameTextStyle;
        sub?: IFrameTextStyle;
}
export interface IFrameAxisLabelStyle {
        text?: IFrameTextStyle;
        text2?: IFrameTextStyle;
        title?: IFrameTitleStyle;
}
export interface IFrameAxisStyle {
        tick?: IFrameTickStyle;
        label?: {
                bottom?: IFrameAxisLabelStyle;
                left?: IFrameAxisLabelStyle;
        };
}
export interface IFrameLegendStyle {
        mode?: "v" | "h";
        /**
            * 注意默认mode是v，当设置为h的时候需要调整宽高值
            */
        width?: number;
        /**
            * 注意默认mode是v，当设置为h的时候需要调整宽高值
            */
        height?: number;
        skip?: number;
        reverse?: boolean;
        visible?: boolean;
        border?: IFrameLineStyle;
        label?: IFrameTextStyle;
        /**
            * 相对于内容区域右侧（v模式）或者底部（h模式）的偏移量
            *
            * 默认是v模式的 [20,0]，当设置为h模式时，要注意调整
            */
        offset?: number[];
        tick?: {
                style?: IFrameLineStyle;
                length?: number;
        };
        /**
            * 如果提供的是可绘制的图片，则忽略除了宽高之外的其他属性
            *
            * 如果是字符串，将会使用loader进行处理，需要链接图片的时候可以先通过资源管理器加载后使用资源进行表示
            */
        color: StopRules | IStopRulesOptions | CanvasImageSource | string;
}
export interface IFrameDrawStyle {
        background?: string;
        content?: IFrameContentStyle;
        border?: IFrameLineStyle;
        gridLines?: {
                major?: IFrameLineStyle;
                minor?: IFrameLineStyle;
        };
        title?: IFrameTitleStyle;
        axis?: IFrameAxisStyle;
        legend?: IFrameLegendStyle;
}
export interface IFrameUpdateOptions {
        x: {
                percent: number;
                label: string;
        }[];
        y: {
                percent: number;
                label: string;
        }[];
}
export interface IFrameDrawServiceOptions {
        /**
            * 决定了使用哪种布局的默认值，不同的布局方式右侧和下侧空余的空间不同。
            *
            * 注意drawOptions中的值会覆盖默认值
            */
        defaultMode: "h" | "v";
}
export class FrameDrawService {
        static VDefaultOptions: IFrameDrawStyle;
        static HDefaultOptions: IFrameDrawStyle;
        constructor(options?: IFrameDrawServiceOptions);
        /**
            * 设置绘制样式
            * @param drawOptions
            * @param partial 是否部分更新，默认true。如果想完全重新设置一个全新的样式，设置该值为false
            * @returns
            */
        setDrawOptions(drawOptions: IFrameDrawStyle, partial?: boolean): this;
        update(context: {
                content: CanvasImageSource;
                x: {
                        /**
                            * 距离坐标原点的距离
                            */
                        percent: number;
                        label: string;
                        label2?: string;
                }[];
                y: {
                        /**
                            * 距离坐标原点的距离
                            */
                        percent: number;
                        label: string;
                        label2?: string;
                }[];
                mainTitle?: string;
                subTitle?: string;
                vTitle?: string;
                hTitle?: string;
                vSubTitle?: string;
                hSubTitle?: string;
        }): HTMLCanvasElement;
        /**
            * 使用高度层的provider创建x和y轴坐标
            * @param sectionPath
            * @param provider
            * @param yList
            * @param xCount
            * @returns
            */
        static getAxisArrayFromZProviderAndPath(sectionPath: {
                x: number;
                y: number;
        }[], zValues: number[], yList: number[], xCount?: number, xDecimal?: number): {
                xArr: {
                        label: string;
                        label2: string;
                        percent: number;
                }[];
                yArr: {
                        label: string;
                        percent: number;
                }[];
        };
}

/**
    * 绘制形状基础类
    *
    * @export
    * @abstract
    * @class PlotShapeBase
    */
export abstract class PlotShapeBase {
        /**
            * 对应类型可以直接使用的坐标
            */
        protected coordinates: any[];
        protected points: any[];
        protected type: any;
        finishPointCount: number;
        freehand: boolean;
        /**
            * 构建 PlotShapeBase
            * @param {any[]} [points] 初始点
            * @memberof PlotShapeBase
            */
        constructor(points?: any[]);
        /**
            * 根据算法生成的目标坐标创建原始形状
            * @param coords
            */
        protected setCoordinates(coords: any[]): void;
        /**
            * 是否是绘制数据生成类，永远返回true
            *
            * @return {*}
            * @memberof PlotShapeBase
            */
        isPlot(): boolean;
        /**
            * 根据绘制原始点生成目标点
            *
            * @readonly
            * @memberof PlotShapeBase
            */
        get generated(): boolean;
        /**
            * 根据绘制坐标生成目标坐标，传入空值表示将原有目标点置空
            * @param value 绘制坐标
            */
        setPoints(value: any): void;
        /**
            * 增加一个新的点
            *
            * @param {number[]} point 点坐标 [x,y]
            * @return {*}
            * @memberof PlotShapeBase
            */
        addPoint(point: number[]): boolean;
        /**
            * 获取所有点
            *
            * @param {boolean} [copy=true] 是否拷贝，默认true
            * @return {*}
            * @memberof PlotShapeBase
            */
        getPoints(copy?: boolean): any[];
        /**
            * 获取点数量
            *
            * @return {*}
            * @memberof PlotShapeBase
            */
        getPointCount(): number;
        /**
            * 更新指定位置的点并重新生成目标点
            *
            * @param {*} point 新的点坐标 [x,y]
            * @param {*} index 更新位置
            * @memberof PlotShapeBase
            */
        updatePoint(point: any, index: any): void;
        /**
            * 更新最后一个点坐标
            *
            * @param {*} point 点坐标 [x,y]
            * @memberof PlotShapeBase
            */
        updateLastPoint(point: any): void;
        protected abstract generate(): any;
        /**
            * 完成绘制。暂未使用
            *
            * @memberof PlotShapeBase
            */
        finishDrawing(): void;
        /**
            * 获取形状的GeoJSON表达
            *
            * @abstract
            * @return {*}  {GeoJSON.GeometryObject}
            * @memberof PlotShapeBase
            */
        abstract getGeoJSON(): GeoJSON.GeometryObject;
}
/**
    * 多边形数据生成基础类
    *
    * @export
    * @abstract
    * @class PlotPolygonBase
    * @extends {PlotShapeBase}
    */
export abstract class PlotPolygonBase extends PlotShapeBase {
        getGeoJSON(): GeoJSON.Polygon;
}
/**
    * 线条数据生成基础类
    *
    * @export
    * @abstract
    * @class PlotLineBase
    * @extends {PlotShapeBase}
    */
export abstract class PlotLineBase extends PlotShapeBase {
        getGeoJSON(): GeoJSON.LineString;
}
/**
    * 点数据生成基础类
    *
    * @export
    * @abstract
    * @class PlotPointBase
    * @extends {PlotShapeBase}
    */
export abstract class PlotPointBase extends PlotShapeBase {
        getGeoJSON(): GeoJSON.Point;
}

/**
  * 用于绘制的常量
  */
export const PlotConstants: {
    TWO_PI: number;
    HALF_PI: number;
    FITTING_COUNT: number;
    ZERO_TOLERANCE: number;
};

/**
    * 注册自定义的绘制数据生成类的构建器，如果类型和内置的一致，则表示覆盖内置的生成类
    *
    * @export
    * @param {string} drawType 绘制类型
    * @param {(points: number[][]) => PlotShapeBase} creator 生成类构建器
    */
export function registerDrawPlotCreator(drawType: string, creator: (points: number[][]) => PlotShapeBase): void;
/**
    * 创建绘制数据生成类
    *
    * @export
    * @param {string} drawType 绘制类型
    * @param {number[][]} points 初始点
    * @return {*}  {PlotShapeBase}
    */
export function createDrawPlot(drawType: string, points: number[][], options?: any): PlotShapeBase;

/**
  * 绘制类型描述
  */
export const PlotTypes: {
    MARKER: string;
    POLYLINE: string;
    CURVE: string;
    POLYGON: string;
    CLOSED_CURVE: string;
    RECTANGLE: string;
    CIRCLE: string;
    ELLIPSE: string;
    ARC: string;
    SECTOR: string;
    FREEHAND_LINE: string;
    FREEHAND_POLYGON: string;
    LUNE: string;
    DOUBLE_ARROW: string;
    GATHERING_PLACE: string;
    STRAIGHT_ARROW: string;
    ASSAULT_DIRECTION: string;
    ATTACK_ARROW: string;
    TAILED_ATTACK_ARROW: string;
    SQUAD_COMBAT: string;
    TAILED_SQUAD_COMBAT: string;
    FINE_ARROW: string;
};

/**
    * 鼠标按下的类型
    *
    * @export
    * @enum {number}
    */
export enum VectorMapToolPointerButtonType {
        left = 0,
        middle = 1,
        right = 2
}
/**
    * 鼠标事件消息
    *
    * @export
    * @interface IVectorMapToolPointerEventArgs
    */
export interface IVectorMapToolPointerEventArgs {
        /**
            * 坐标点，不同的底层渲染可能会有不同的坐标单位，由各自的tool实现类和toolRenderer实现类分别负责project和unproject
            *
            * @type {number[]}
            * @memberof IVectorMapToolPointerEventArgs
            */
        coords: number[];
        /**
            * 鼠标按下类型
            *
            * @type {VectorMapToolPointerButtonType}
            * @memberof IVectorMapToolPointerEventArgs
            */
        buttonType: VectorMapToolPointerButtonType;
        /**
            * 原始的鼠标事件消息
            *
            * @type {MouseEvent}
            * @memberof IVectorMapToolPointerEventArgs
            */
        originalEvent: MouseEvent;
        /**
            * 具体的实现类触发鼠标事件时的数据
            */
        toolEvent: any;
}
/**
    * 鼠标消息监听函数定义
    *
    * @export
    * @interface IVectorMapToolPointerEventListeners
    */
export interface IVectorMapToolPointerEventListeners {
        /**
            * 鼠标按下后的操作
            */
        onPointerDown: (args: IVectorMapToolPointerEventArgs) => void;
        /**
            * 鼠标移动的操作
            *
            * @memberof IVectorMapToolPointerEventListeners
            */
        onPointerMove: (args: IVectorMapToolPointerEventArgs) => void;
        /**
            * 鼠标抬起的操作
            *
            * @memberof IVectorMapToolPointerEventListeners
            */
        onPointerUp: (args: IVectorMapToolPointerEventArgs) => void;
}
/**
    * 矢量地图工具渲染接口定义
    *
    * @export
    * @interface IVectorMapToolRenderer
    */
export interface IVectorMapToolRenderer {
        /**
            * 更新一个绘制对象
            *
            * @param {string} key 绘制ID
            * @param {PlotShapeBase} shape 绘制对象
            * @param {*} [properties] 绘制属性
            * @memberof IVectorMapToolRenderer
            */
        updateShape(key: string, shape: PlotShapeBase, properties?: any): any;
        /**
            * 拾取绘制对象
            *
            * @param {{ x: number, y: number }} pt 拾取点屏幕坐标
            * @return {*}  {{ key: string, shape: PlotShapeBase, properties?: any }}
            * @memberof IVectorMapToolRenderer
            */
        pick(pt: {
                x: number;
                y: number;
        }): {
                key: string;
                shape: PlotShapeBase;
                properties?: any;
        };
        /**
            * 增加一个绘制对象
            *
            * @param {string} key 绘制ID
            * @param {PlotShapeBase} shape 绘制对象
            * @param {*} [properties] 绘制属性
            * @memberof IVectorMapToolRenderer
            */
        addShape(key: string, shape: PlotShapeBase, properties?: any): any;
        /**
            * 移除一个绘制对象
            *
            * @param {string} key 绘制ID
            * @memberof IVectorMapToolRenderer
            */
        removeShape(key: string): any;
        /**
            * 获取绘制数据的GeoJSON表达
            *
            * @return {*}  {GeoJSON.GeoJsonObject}
            * @memberof IVectorMapToolRenderer
            */
        getGeoJSON(): GeoJSON.GeoJsonObject;
        /**
            * 设置绘制地图
            *
            * @param {*} map 用于绘制的地图
            * @memberof IVectorMapToolRenderer
            */
        setMap(map: any): any;
        /**
            * 清空绘制内容
            *
            * @memberof IVectorMapToolRenderer
            */
        clear(): any;
}
/**
    * 地图矢量绘制工具构建参数
    *
    * @export
    * @interface IVectorMapToolOptions
    */
export interface IVectorMapToolOptions {
        /**
            * 渲染器，需要在使用工具之前设置，是否需要手动设置取决于具体底层引擎是否带默认的渲染器
            *
            * @type {IVectorMapToolRenderer}
            * @memberof IVectorMapToolOptions
            */
        renderer?: IVectorMapToolRenderer;
        /**
            * 在未形成最终图形之前的随动线条渲染器
            *
            * @type {IVectorMapToolRenderer}
            * @memberof IVectorMapToolOptions
            */
        tempLineRenderer?: IVectorMapToolRenderer;
        /**
            * 是否连续绘制，如果是，则绘制完之后不会自动结束，可以继续绘制
            *
            * @type {boolean}
            * @memberof IVectorMapToolOptions
            */
        continious?: boolean;
        /**
            * 绘制图形的类型 详见 PlotTypes
            *
            * @type {string}
            * @memberof IVectorMapToolOptions
            */
        drawType: string;
        /**
            * 是否在绘制下一个之前自动清除上一个，默认false
            */
        autoClear?: boolean;
        /**
            * 绘制过程中是否锁定地图，默认false
            */
        lockMapWhenPlot?: boolean;
        /**
            * 形状的初始化参数，如果形状支持初始化的时候传入参数，可在此传入
            */
        shapeOptions?: any;
}
/**
    * 地图矢量绘制工具
    *
    * @export
    * @abstract
    * @class VectorMapTool
    * @extends {Evented}
    * @implements {IMapTool}
    */
export abstract class VectorMapTool extends Evented implements IMapTool {
        /**
            * 绘制工具的事件消息名称
            *
            * @static
            * @memberof VectorMapTool
            */
        static EventTypes: {
                /**
                    * 绘制完成消息
                    */
                drawFinish: string;
                pointAdd: string;
                /**
                    * 绘制完且释放相关绘制项之后
                    * 如果需要获取绘制的数据，请使用请监听EventTypes.drawFinish
                    */
                afterFinish: string;
        };
        /**
            * 鼠标按下多久视为拖动地图，不执行绘制响应，默认500ms
            *
            * @static
            * @memberof VectorMapTool
            */
        static PointDownAsDragTime: number;
        /**
            * 构建 VectorMapTool
            * @param {IVectorMapToolOptions} options 构建参数
            * @memberof VectorMapTool
            */
        constructor(options: IVectorMapToolOptions);
        protected listeners: IVectorMapToolPointerEventListeners;
        protected abstract setListeners(): any;
        protected abstract unsetListeners(): any;
        options: IVectorMapToolOptions;
        protected shape: PlotShapeBase;
        protected properties: any;
        protected dragging: boolean;
        protected movePointAdded: boolean;
        protected draggingMoved: boolean;
        protected pointDownTime: number;
        protected drawId: string;
        protected tempLineShape: PlotPolyline;
        protected map: any;
        protected abstract unlockMap(): any;
        protected abstract lockMap(): any;
        protected finishDraw(args: any): void;
        protected updateShape(): void;
        protected onPointerDown: (args: IVectorMapToolPointerEventArgs) => void;
        protected onPointerMove: (args: IVectorMapToolPointerEventArgs) => void;
        protected onPointerUp: (args: IVectorMapToolPointerEventArgs) => void;
        addPoint(pt: number[]): void;
        begin(properties?: any): void;
        end(): void;
        setMap(map: any): void;
        /**
            * 更新绘制类型
            *
            * @param {string} drawType 绘制类型
            * @memberof VectorMapTool
            */
        updateDrawType(drawType: string): void;
        /**
            * 获取绘制的GeoJSON数据
            *
            * @return {*}
            * @memberof VectorMapTool
            */
        getGeoJSON(): //@ts-ignore
import("geojson").GeoJsonObject;
        /**
            * 拾取地图上的绘制对象
            *
            * @param {{ x: number, y: number }} pt 拾取点
            * @return {*}
            * @memberof VectorMapTool
            */
        pick(pt: {
                x: number;
                y: number;
        }): {
                key: string;
                shape: PlotShapeBase;
                properties?: any;
        };
        clear(): void;
}

type DetectedInfoType = 'browser' | 'node' | 'bot-device' | 'bot' | 'react-native';
interface DetectedInfo<T extends DetectedInfoType, N extends string, O, V = null> {
        readonly type: T;
        readonly name: N;
        readonly version: V;
        readonly os: O;
}
class BrowserInfo implements DetectedInfo<'browser', Browser, OperatingSystem | null, string> {
        readonly name: Browser;
        readonly version: string;
        readonly os: OperatingSystem | null;
        readonly type = "browser";
        constructor(name: Browser, version: string, os: OperatingSystem | null);
}
class NodeInfo implements DetectedInfo<'node', 'node', NodeJS.Platform, string> {
        readonly version: string;
        readonly type = "node";
        readonly name: 'node';
        readonly os: NodeJS.Platform;
        constructor(version: string);
}
class SearchBotDeviceInfo implements DetectedInfo<'bot-device', Browser, OperatingSystem | null, string> {
        readonly name: Browser;
        readonly version: string;
        readonly os: OperatingSystem | null;
        readonly bot: string;
        readonly type = "bot-device";
        constructor(name: Browser, version: string, os: OperatingSystem | null, bot: string);
}
class BotInfo implements DetectedInfo<'bot', 'bot', null, null> {
        readonly type = "bot";
        readonly bot: true;
        readonly name: 'bot';
        readonly version: null;
        readonly os: null;
}
class ReactNativeInfo implements DetectedInfo<'react-native', 'react-native', null, null> {
        readonly type = "react-native";
        readonly name: 'react-native';
        readonly version: null;
        readonly os: null;
}
type Browser = 'aol' | 'edge' | 'edge-ios' | 'yandexbrowser' | 'kakaotalk' | 'samsung' | 'silk' | 'miui' | 'beaker' | 'edge-chromium' | 'chrome' | 'chromium-webview' | 'phantomjs' | 'crios' | 'firefox' | 'fxios' | 'opera-mini' | 'opera' | 'ie' | 'bb10' | 'android' | 'ios' | 'safari' | 'facebook' | 'instagram' | 'ios-webview' | 'searchbot';
type OperatingSystem = 'iOS' | 'Android OS' | 'BlackBerry OS' | 'Windows Mobile' | 'Amazon OS' | 'Windows 3.11' | 'Windows 95' | 'Windows 98' | 'Windows 2000' | 'Windows XP' | 'Windows Server 2003' | 'Windows Vista' | 'Windows 7' | 'Windows 8' | 'Windows 8.1' | 'Windows 10' | 'Windows 11' | 'Windows ME' | 'Open BSD' | 'Sun OS' | 'Linux' | 'Mac OS' | 'QNX' | 'BeOS' | 'OS/2' | 'Chrome OS';
/**
    * 检测浏览器类型
    * @param userAgent 浏览器标识
    */
export function detectBrowser(userAgent?: string): BrowserInfo | SearchBotDeviceInfo | BotInfo | NodeInfo | ReactNativeInfo | null;
/**
    * 检测操作系统类型
    * @param ua 浏览器标识
    */
export function detectOS(ua: string): OperatingSystem | null;
/**
    * 检测是否是QuickEarth支持的浏览器类型
    */
export function isSupportedBrowser(): boolean;
/**
    * 开启反调试检测
    * @param redirectUrl 检测到处于调试状态时的重定向URL
    */
export function antiDebug(redirectUrl?: string): void;
export const isMobile: typeof isMoblie;
/**
    * 检测是否为移动端浏览器
    * @param strict 是否采用严格检测方案，默认为true
    */
export function isMoblie(strict?: boolean): boolean;
export function getProperlyWebGLVersion(): number;
export function supportWebP(): Promise<boolean>;
export {};

/**
    * 绘制圆角矩形
    *
    * @export
    * @param {*} cxt 绘制上下文
    * @param {*} x x坐标
    * @param {*} y y坐标
    * @param {*} width 矩形宽
    * @param {*} height 矩形高
    * @param {*} radius 圆角半径
    */
export function drawRoundRect(cxt: any, x: any, y: any, width: any, height: any, radius: any): void;
/**
    * 自定义绘制的高度列表。会使用上下两层数据进行线性插值
    *
    * 对于仰角层数据，provider的zValues需要是从小到大的仰角原始值，单位是度
    *
    * 对于高度层数据，provider的zValues需要是从小到大的高度层原始值，单位是米
    * @param provider
    * @param pts
    * @param colorRules
    * @param outputZValues 默认从500到10000，按照500米的间隔
    * @param isElevationLayer 是否是仰角层数据，默认false
    * @param staHeight 测站高度，默认为0。建议传入实际测站高度，否则会有偏差
    * @param width
    * @param height
    * @param maxHgt
    * @param dim
    * @param canvas
    */
export function drawCustomGridSection(provider: IGridDataProvider, pts: ({
        x: number;
        y: number;
}[]) | number[], colorRules: GridColorFieldGL, outputZValues?: number[], isElevationLayer?: boolean, staHeight?: number, width?: number, height?: number, dim?: number, canvas?: any): {
        canvas: HTMLCanvasElement;
        path: {
                x: number;
                y: number;
        }[];
        maxVal: number;
        minVal: number;
        outputZValues?: undefined;
} | {
        canvas: HTMLCanvasElement;
        path: {
                x: number;
                y: number;
        }[];
        maxVal: number;
        minVal: number;
        outputZValues: number[];
};
/**
    * 绘制三维格点剖面。仅填色，不会绘制坐标轴等附加内容
    * @param provider
    * @param pts
    * @param colorRules
    * @param width
    * @param height
    * @param maxHgt
    * @param dim
    * @param canvas 如果传入自定义canvas，则在此canvas上绘制，width和height参数失效
    * @returns
    */
export function drawGridSection(provider: IGridDataProvider, pts: ({
        x: number;
        y: number;
}[]) | number[], colorRules: GridColorFieldGL, width?: number, height?: number, maxHgt?: number, dim?: number, canvas?: any): {
        canvas: HTMLCanvasElement;
        path: {
                x: number;
                y: number;
        }[];
        maxVal: number;
        minVal: number;
};

/**
    * 将风速转换为字体标号
    *
    * @export
    * @param {*} speed 风速
    * @return {*}
    */
export function windSpeed2ttf(speed: any): string;
/**
    * 将天气现象编码转换为天气现象字体
    *
    * @export
    * @param {*} value 天气现象编码
    * @return {*}  {number}
    */
export function ww2ttf(value: any): number;
/**
    *角度转弧度
    *
    * @export
    * @param {*} degree 角度
    * @return {*}
    */
export function degreeToArc(degree: any): any;
/**
    * 内置的loader名称，不支持修改
    * 系统有两种类型的loader，一种是前置loader，不以$开头，直接对配置中的#之前的部分进行处理后作为字段值，如资源loader
    * 一种是后置loader，以$开头。矢量数据中loader会动态获取到字段的值和请求参数，格点数据中会获取到格点值和请求参数，如数值转换的loader
    * 后置loader仅支持非webgl渲染。
    */
export const defaultLoaderNames: {
        /**
            * 将风速值转换为风杆字体编码
            */
        wind: string;
        /**
            * 将天气现象编码转变为天气现象字体编码
            */
        weather: string;
        /**
            * 将角度值转换为弧度
            */
        degree2arc: string;
        /**
            * 根据资源ID获取资源。当资源是stoprules的时候支持field参数，用于替换资源中自带的fieldName
            */
        res: string;
        /**
            * 根据资源ID获取分段渲染规则，从分段渲染规则中获取stop值数组返回
            */
        anaVals: string;
        /**
            * 将提供的数值，根据参数中len字段的配置进行精度设置。默认是原样返回
            */
        decimal: string;
        /**
            * 从现有的图片资源中截取一部分，可以同x y w h四个参数表示原图像中开始的xy位置和截取宽高，截取后的图像大小为wh，具体表现尺寸可以在具体的渲染样式中设置
            */
        subImage: string;
        /**
            * 对数据进行线性变换，使用scale和offset参数进行设置，默认scaleFirst=true
            */
        transform: string;
        /**
            * 根据image对象或者image资源名称生成colorscale
            */
        cs: string;
        /**
            * 串联执行所有loader，将前一个的结果作为后一个的输入，类似reduce
            */
        all: string;
};
/**
    * 注册一个字段的解析器
    * 如 $speed的时候，需要对结果转换为风速的字体，就可以使用 $speed#wind来配置
    *
    * @export
    * @param {string} loaderName
    * @param {(val: any) => any} processFunc
    */
export function registerFieldLoader(loaderName: string, processFunc: (val: any, params?: any) => any): void;
/**
    * 批量注册多个字段为同一个解析器
    *
    * @export
    * @param {string[]} loaderNames
    * @param {(val: any) => any} processFunc
    */
export function registerFieldLoaders(loaderNames: string[], processFunc: (val: any, params?: any) => any): void;
/**
    * 获取引用字段的配置解析器
    *
    * @export
    * @param {string} loaderName
    * @return {*}  {(val: any) => any}
    */
export function getFieldLoader(loaderName: string): (val: any, params?: any) => any;

/**
    * 注入glsl中引用的框架代码
    *
    * @export
    * @param {string} shader glsl源
    * @param {string} [injectPlace="$inject"] 注入点名称，默认$inject
    * @return {*}
    */
export function injectBuiltInShaderFunctions(shader: string, injectPlace?: string): string;
/**
    * 获取地形数据解析相关的consts和uniforms定义
    * 为了支持地形数据与格点数据不同属性，需要对地形数据属性进行单独设置
    *
    * @export
    * @param {GridData} grid 地形数据
    * @param {boolean} [withUndef=false] 是否加入undef，默认false
    * @return {*}
    */
export function getHeightAlgoConstsAndUniforms(grid: GridData, withUndef?: boolean): string;
/**
    * 获取格点数据解析相关的consts和uniforms定义
    *
    * @export
    * @param {GridData} grid 格点数据
    * @param {boolean} [withUndef=false] 是否加入undef，默认false
    * @param is2DTexture 是否是三维texture使用，如果是的话，由于三维texture无法直接使用图片，需要先解码为数据，所以不能带def
    * @return {*}
    */
export function getAlgoConstsAndUniforms(grid: GridData, withUndef?: boolean, is2DTexture?: boolean): string;
/**
    * 根据数据算法获取相应的uniform变量对象
    * @param grid 格点数据
    */
export function setDirectAlgoUniforms(grid: GridData, uniforms?: {}): {};
/**
    * 根据数据算法获取地形相应的uniform变量对象获取函数
    * @param grid 格点数据
    */
export function setHeightAlgoUniforms(grid: GridData, uniforms?: {}): {};
/**
    * 根据数据算法获取相应的uniform变量对象获取函数
    * @param grid 格点数据
    */
export function setAlgoUniforms(grid: GridData, uniforms?: {}): {};
/**
    * 获取高度层相关的uniform定义
    *
    * @export
    * @return {*}
    */
export function getHeightUniforms(): string;
/**
    * 将传入的数据转换为float字符串
    *
    * @export
    * @param {(number | string)} data 要转换的数据
    * @return {*}
    */
export function ensureFloatString(data: number | string): string;
/**
    * 获取RGBA到Float类型转换的常量定义
    *
    * @export
    * @return {*}
    */
export function getRGBA2FloatConsts(): string;
/**
    * 根据格点数据类型获取GL中使用的TypedArray
    *
    * @export
    * @param {GridDataType} dataType 格点数据类型
    * @param {number} dataCount 数据的总个数
    * @return {*}
    */
export function createGLTypedArray(dataType: GridDataType, dataCount: number): Uint8Array | Float32Array;
/**
    * glsl中的相关内置关键字名称，根据全局是否使用WebGL2进行自适应
    */
export let glNames: {
        attrIn: string;
        varIn: string;
        varOut: string;
        outColorDefine: string;
        outColorName: string;
        textureFunc: string;
        header: string;
};
/**
    * 框架调用
    */
export function setGLNames(): void;
/**
    * 对格点provider进行缩放，如果是灰度图生成，必须在创建provider的时候指定了autoLoadToMemory为true
    * @param provider
    * @param scale 默认3，表示缩放到1/3的大小
    * @param maxSize 当x或者y的大小超过maxSize之后才开始缩放，默认4096
    * @returns
    */
export function scaleGridProvider(provider: IGridDataProvider, scale?: number, maxSize?: number): IGridDataProvider;

export function uid(): string;
/**
    * 将二维数组转为一维数组
    * @param data2D 二维数组
    */
export function toOneDim(data2D: number[][]): number[];
/**
    * 检查当前数值，如果不是一个有效值（undefined或者null或者NaN），则返回默认值，否则返回原值
    * @param val 当前数值
    * @param defaultVal 默认数值
    */
export function defaultVal(val: any, defaultVal: any): any;
/**
    * 将options中的值赋值给obj对象
    * @param obj 被设置的对象
    * @param options 要拷贝的对象
    * @param defaultOptions 默认参数
    */
export function setOptions(obj: any, options: any, defaultOptions?: any): any;
/**
    * 对对象和数组执行深拷贝，函数等无法被深拷贝的对象仍然维持原引用
    */
export function deepCopyLiteral(obj: any, newObj?: any): any;
/**
    * 使用提供的自定义值覆盖默认值到一个新的对象中，会执行深度赋值
    *
    * 如 default:{a:1,b:{c:2,d:3}},options:{b:{c:3}}，则会得到 {a:1,b:{c:3,d:3}}
    * @param obj
    * @param options
    * @param defaultOptions
    * @returns
    */
export function setOptionsDeep(obj: any, options: any, defaultOptions?: any): any;
/**
    * 根据UV获取风向风速
    * @param uv [u,v]
    * @returns [speed,dir]
    */
export function getSDFromUV(uv: number[]): number[];
/**
    *
    *根据风向风速获取UV
    * @export
    * @param {number[]} sd 风向风速
    * @return {*}  {number[]} [uv]
    */
export function getUVFromSD(sd: number[]): number[];
/**
    *获取数据并转为JSON
    *
    * @export
    * @param {string} path 数据路径
    * @return {*}  {Promise<any>} 返回异步结果
    */
export function getJSON(path: string): Promise<any>;
/**
    * 读取文本数据，支持zip压缩包
    * @param path 数据路径
    * @param forceUnzip 是否强制视为压缩包，默认false。为了避免数据被迅雷之类拦截，可以修改压缩包后缀并设置为强制解压
    * @returns 文件的文本内容
    */
export function getText(path: string, forceUnzip?: boolean): Promise<any>;
/**
    * 获取zip打包后的JSON数据
    *
    * @export
    * @param {string} path
    * @return {*}  {Promise<any>}
    */
export function getJSONZip(path: string): Promise<any>;
/**
    * 获取二进制数据
    *
    * @export
    * @param {*} path 数据路径
    * @param {(fileInfo: JSZipObject) => boolean} [filter] 过滤函数，返回false则不会触发文件回调
    * @param {OutputType} [type="arraybuffer"] 压缩包中的文件类型
    * @param forceUnzip 是否强制进行解压缩，用于后缀非zip的文件或者数据流进行解压操作
    */
export function getBinary(path: string, filter?: (fileInfo: JSZipObject) => boolean, type?: OutputType | ((fileInfo: JSZipObject) => OutputType), forceUnzip?: boolean, eachFile?: (content: any, fileInfo: JSZipObject, fileCount: number) => void): Promise<any[]>;
/**
    *获取一系列二进制数据，支持解压缩
    *
    * @export
    * @param {string[]} paths 文件请求地址数组
    * @param {(datas: any[]) => void} [onEachZipFiles] 每个请求完成时的回调（按数组中的顺序）
    * @param {(fileInfo: JSZipObject) => boolean} [filter] 文件过滤器
    * @param {(OutputType | ((fileInfo: JSZipObject) => OutputType))} [type="arraybuffer"]
    * @param {boolean} [forceUnzip=false] 是否强行解压
    * @return {*}  {Promise<any[]>}
    */
export function getBinaries(paths: string[], onEachZipFiles?: (datas: any[]) => void, filter?: (fileInfo: JSZipObject) => boolean, type?: OutputType | ((fileInfo: JSZipObject) => OutputType), forceUnzip?: boolean): Promise<any[]>;
export function getRaw(path: string): Promise<any>;
/**
    * QE中的fetch方法，默认是window中的fetch，可以通过自定义该方法实现请求拦截
    */
export let qeFetch: typeof fetch;
/**
    *检测是否为undefined或者null
    *如果是返回默认值；如果否，当有执行器参数的时候，运行执行器，将value作为参数传入，返回执行器执行结果，如果没有执行器参数，返回原始值
    *
    * @export
    * @param {*} value
    * @return {*}
    */
export function whenValid(value: any, defaultValue: any, executor?: (value: any) => any): any;
/**
    * 合并对象
    *
    * @export
    * @param {*} dest
    * @return {*}
    */
export function extend(dest: any, ...args: any[]): any;
/**
    * 获取一个对象的唯一id，如果不存在，则进行赋值后返回
    *
    * @export
    * @param {*} obj
    * @return {*}
    */
export function stamp(obj: any): any;
/**
    * 使用空格分隔字符串
    *
    * @export
    * @param {*} str
    * @return {*}
    */
export function splitWords(str: string): string[];
/**
    * 按权重对颜色进行反距离权重插值
    *
    * @export
    * @param {{ r: number, g: number, b: number, a?: number }} color1 第一个颜色
    * @param {{ r: number, g: number, b: number, a?: number }} color2 第二个颜色
    * @param {number} weight 距离权重，数值越接近0，说明color1所占比重越大
    * @return {*}  {{ r: number, g: number, b: number, a: number }}
    */
export function interpColor(color1: {
        r: number;
        g: number;
        b: number;
        a?: number;
}, color2: {
        r: number;
        g: number;
        b: number;
        a?: number;
}, weight: number): {
        r: number;
        g: number;
        b: number;
        a: number;
};
/**
    * 获取在格点中的索引号
    *
    * @export
    * @param {number} start 开始值
    * @param {number} interval 间隔
    * @param {number} current 当前值
    * @return {*}  {number}
    */
export function getGridPosIndex(start: number, interval: number, current: number): number;
/**
    * 根据一维索引号获取二维索引号
    * @param pos1D 在一维中的索引号
    * @param xSize
    * @returns
    */
export function getPos2DFrom1D(pos1D: number, xSize: number): {
        x: number;
        y: number;
};
export function getPos1DFrom2D(x: number, y: number, xSize: number): number;
/**
    * 检查该值是否被定义。正常值返回true，未定义（undefined or null or NaN）返回false
    *
    * @export
    * @param {*} val
    * @return {*}  {boolean}
    */
export function defined(val: any): boolean;
/**
    * polyfill of typeof
    *
    * @export
    * @param {*} value
    * @return {*}
    */
export function TypeOf(value: any): string;
/**
    * 判断是否为函数
    * @param value
    * @returns {boolean}
    */
export function isFunction(value: any): boolean;
/**
    * 判断是否为对象
    * @param value
    * @returns {boolean}
    */
export function isObject(value: any): boolean;
/**
    * is date value
    * @param val
    * @returns {boolean}
    */
export function isDate(val: any): boolean;
/**
    * is array buffer
    * @param val
    * @returns {boolean}
    */
export function isArrayBuffer(val: any): boolean;
/**
    * 判断是否为合法字符串
    * @param value
    * @returns {boolean}
    */
export function isString(value: any): boolean;
/**
    * 判断是否为数字
    * @param value
    * @returns {boolean}
    */
export function isNumber(value: any): boolean;
/**
    * check isEmpty object
    * @param object
    * @returns {boolean}
    */
export function isEmpty(object: {}): boolean;
/**
    * check is null
    * @param obj
    * @returns {boolean}
    */
export function isNull(obj: any): boolean;
/**
    * check is array
    * @param arr
    */
export function isArray(arr: any): boolean;
/**
    * assign object
    * @param target
    * @param sources
    */
export function assign(target: object, ...sources: any[]): any;
/**
    * Get floored division
    * @param a
    * @param n
    * @returns {Number} returns remainder of floored division,
    * i.e., floor(a / n). Useful for consistent modulo of negative numbers.
    * See http://en.wikipedia.org/wiki/Modulo_operation.
    */
export function floorMod(a: number, n: number): number;
/**
    * 检查值是否合法
    * @param val
    * @returns {boolean}
    */
export function isValid(val: any): boolean;
export function onWindowSizeChanged(changeEnd: () => void, changed?: () => void, changeStart?: () => void): {
        remove: () => void;
};
export function downloadImage(dataUrl: string, fileName: string): void;
/**
    * 将经度设置在0-360之间
    *
    * @export
    */
export function wrapLon(lon: number): number;
export function pickColor(color1: Spectra, color2: Spectra, weight: any, a: any): Spectra;
export const formatDate: (date: Date, exp: string) => string;
export function calcMaxMin(grid: GridData): {
        min: number;
        max: number;
};
export function getCR(provider: IGridDataProvider, combineZIndex: number, needUpper?: boolean, needLower?: boolean, tIdx?: number): {
        upper: IGridDataProvider;
        lower: IGridDataProvider;
        maxPos: number;
        maxVal: number;
};
export function clipGridData(data: GridData, xStart: number, xEnd: number, yStart: number, yEnd: number): GridData;
/**
    * 根据给定的范围对数据源进行裁剪，暂不支持风场，裁剪后的数据存放顺序与原来相同
    * @param provider
    * @param extent
    * @returns
    */
export function clipGridProvider(provider: IGridDataProvider, extent: IDataExtent): IGridDataProvider;
export function saveGridMesh(gridOptions: IGridDataOptions, name?: string): void;
export function loadGridMesh(gridOptions: IGridDataOptions, path: string): Promise<{
        ids: Float32Array;
        indices: Uint32Array;
}>;
export function createTypeArr(dataType: GridDataType, sba: SharedArrayBuffer | ArrayBuffer): Uint8Array | Int8Array | Uint16Array | Int16Array | Uint32Array | Int32Array | Float32Array | Float64Array;
export function loadAllImages(images: string[]): Promise<{
        [key: string]: HTMLImageElement;
}>;
export function randomValueInRange(min: number, max: number): number;
/**
    * 根据extent生成三维空间随机点
    * @param extent
    * @param withZ 默认true
    * @returns
    */
export function randomPointInExtent(extent: IDataExtent, withZ?: boolean): number[];
/**
    * 获取数据空间内的一个随机点
    * @param options
    * @param withZ 默认true
    */
export function randomPointInGrid(options: IGridDataOptions, withZ?: boolean): number[];
/**
    * 根据中心点和宽高构建一个缓冲的矩形
    * @param lat 中心纬度
    * @param lon 中心经度
    * @param width 宽度
    * @param height 高度
    */
export function bufferExtent(lat: number, lon: number, width: number, height: number): IDataExtent;
/**
    * 压高公式
    * @param p 气压
    * @param p0 地面气压，默认为标准大气压 1013.225
    * @returns 高度
    */
export function p2h(p: number, p0?: number): number;
/**
    * 创建雷达距离圈的GeoJSON
    * @param centerLonlat 中心点，经度在前，纬度在后
    * @param radiusArrInKM 距离圈数组，单位是千米
    * @param crossArrInDeg 交叉线的角度数组，一条交叉线会默认延伸至另一端，即45度的交叉线会从45度延伸至225度，所以不需要再传入225。默认为[0,45,90,135]
    * @param labelPositions 标记点的位置数组，使用距离和角度来确定标签点。可以传入自定义的value之，会被写入属性中。显示的位置可以通过图层的offset进行微调。
    *
    * 默认值为：
    * [
        {
            distance: 100,
            angle: 90,
            value: 100
        },
        {
            distance: 200,
            angle: 90,
            value: 200
        },
        {
            distance: 300,
            angle: 90,
            value: "300km"
        }
    ]
    * @returns 返回具有距离圈，角度线以及给定标签位置的点的geojson FeatureCollection
    *
    * 其中距离圈是多边形，具有value字段表示距离（千米）
    * 角度线是线条，具有value字段表示角度（degree）
    * 标签位置是点，具有value字段表示传入的标签value（不传默认为距离）
    */
export function createRadarRangeRingGeoJSON(centerLonlat: number[], radiusArrInKM: number[], crossArrInDeg?: number[], labelPositions?: {
        distance: number;
        angle: number;
        value?: string | number;
}[], steps?: number): GeoJSON.FeatureCollection;
export const loadScript: (url: string, id: string, globalName: string) => Promise<unknown>;

/**
    * 内置的各类切片地图的key
    */
export const preDeinfedImageTileTokens: {
        tdt: string;
        zkxt: string;
};
/**
    * 内置的切片地图的名称
    */
export const predefinedImageTiles: {
        tdtSatellite: string;
        tdtSatelliteAnnotation: string;
        tdtNormal: string;
        tdtNormalAnnotation: string;
        tdtBounds: string;
        tdtTerrain: string;
        tdtTerrainAnnotation: string;
        gdSatellite: string;
        gdSatelliteAnnotation: string;
        gdNormal: string;
        gdWarm: string;
        geoQNormal: string;
        geoQPurplishBlue: string;
        geoQGray: string;
        geoQWarm: string;
        geoQBoundary: string;
        geoQWater: string;
        tencentStreet: string;
        tencentDem: string;
        tencentAnnotation: string;
        stamenTerrain: string;
        stamenWaterColor: string;
        esriSate: string;
        esriTerrain: string;
        esriShaded: string;
        mtb: string;
        nasaNight: string;
        windy: string;
        vectorTop: string;
        zkxtNormal: string;
        zkxtTerrain: string;
        zkxtSatellite: string;
        zkxtSatelliteAnnotation: string;
        zkxtTerrainAnnotation: string;
};
/**
    * 基于URL模板的切片地图参数
    *
    * @export
    * @interface IImageTileRule
    */
export interface IImageTileRule {
        /**
            * URL模板
            *
            * @type {string}
            * @memberof IImageTileRule
            */
        url: string;
        /**
            *
            *
            * @type {(string[] | string)}
            * @memberof IImageTileRule
            */
        subdomains?: string[] | string;
        tms?: boolean;
        ext?: string;
        minZoom?: number;
        maxZoom?: number;
        tilematrixset?: string;
        bounds?: number[][];
        format?: string;
        time?: string;
        zoomOffset?: number;
        detectRetina?: boolean;
}
/**
    * 增加自定义URL模板切片图层参数。添加后可以在创建图层的时候直接使用key代替。
    *
    * @export
    * @param {string} name
    * @param {IImageTileRule} rule
    */
export function addCustomImageTileRules(name: string, rule: IImageTileRule): void;
/**
    * 根据切片图层名称获取切片图层url参数
    *
    * @export
    * @param {string} tileName
    * @return {*}  {IImageTileRule}
    */
export function getImageTileUrls(tileName: string): IImageTileRule;

export function createBoxGeometry(width: any, height: any, depth: any, widthSegments?: number, heightSegments?: number, depthSegments?: number): {
    indices: number[];
    vertices: number[];
    normals: number[];
    uvs: number[];
};
export function getTiltPathOfGridDatas(pts: {
    x: number;
    y: number;
}[], deltaX: number, deltaY: number): {
    posMap: {
        x: number;
        y: number;
    }[];
};

/**
  * 应用初始化之前调用，用于初始化QE相关图层定义
  * 二维免费版本支持部署，三维免费版本支持本地开发测试
  * 如有需要购买三维商业版本，可以联系微信mofangbao咨询
  * @param token 离线版本传任意字符串
  * @param freeVersion 是否是免费版本，默认是，如果传否，将进行证书验证。
  * @returns
  */
export function init(token?: string, freeVersion?: boolean): void;
export function auth(): Promise<{
    code: string;
}>;

export const limit = "%3Egjgijhlh%60%60%60%60";
export function setNewPos(): void;
export const authInfo: {
    code: string;
};
export function better(code: string): string;
export function mix(code: string): string;

/**
    * 支持[time[level]]的格点数据访问器
    */
export interface IMeshDataProvider extends ITZDataProvider {
        /**
            * 生成Mesh的格点属性，当Mesh是由格点数据生成的时候可以提供
            */
        gridOptions?: IGridDataOptions;
        /**
            *
            * 向当前数据源增加一个格点场
            * @param {number} dimIdx 时间索引号
            * @param {number} levelIdx 高度索引号
            * @param {number} levelOfZ 高度值
            * @param {boolean} setActive 是否设置为当前生效的数据
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        updateMesh(mesh: IMeshCollection, dimIdx: number, levelIdx: number, levelOfZ: number, setActive: boolean): IMeshDataProvider;
        /**
            * 获取当前数据访问器中的所有维度数据
            */
        allMeshes(): IMeshCollection[][];
        /**
            *
            * 数据源中当前生效的时间索引号，默认是0
            * @type {number}
            * @memberof IMeshDataProvider
            */
        currentTIdx: number;
        /**
            *
            * 数据源中当前生效的高度层索引号，默认是0
            * @type {number}
            * @memberof IMeshDataProvider
            */
        currentZIdx: number;
        /**
            *
            * 获取当前生效的整数形式的时间索引号
            * @return {*}  {number}
            * @memberof IMeshDataProvider
            */
        getIntTIdx(): number;
        /**
            *
            * 获取当前生效的整数形式的高度层索引号
            * @return {*}  {number}
            * @memberof IMeshDataProvider
            */
        getIntZIdx(): number;
        /**
            *
            * 监听时间索引号发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        onTChanged(cb: () => void): IMeshDataProvider;
        /**
            *
            * 监听高度层索引号发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        onZChanged(cb: () => void): IMeshDataProvider;
        /**
            *
            * 监听时间和高度索引号发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        onTZChanged(cb: () => void): IMeshDataProvider;
        /**
            *
            * 监听时间的整数部分发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        onIntTChanged(cb: () => void): IMeshDataProvider;
        /**
            *
            * 监听高度的整数部分发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        onIntZChanged(cb: () => void): IMeshDataProvider;
        /**
            *
            * 监听时间和高度的整数部分发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        onIntTZChanged(cb: () => void): IMeshDataProvider;
        /**
            *
            * 取消监听时间发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        offTChanged(cb: () => void): IMeshDataProvider;
        /**
            *
            * 取消监听高度发生变化的消息
            * @param {(z: number) => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        offZChanged(cb: () => void): IMeshDataProvider;
        /**
            *
            * 取消监听时间高度发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        offTZChanged(cb: () => void): IMeshDataProvider;
        /**
            *
            * 取消监听时间整数部分发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        offIntTChanged(cb: () => void): IMeshDataProvider;
        /**
            *
            * 取消监听高度整数部分发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        offIntZChanged(cb: () => void): IMeshDataProvider;
        /**
            *
            * 取消监听时间高度整数发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        offIntTZChanged(cb: () => void): IMeshDataProvider;
        /**
            *
            * 监听当前时间和高度索引所在的格点发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        onActiveGridUpdated(cb: () => void): IMeshDataProvider;
        /**
            *
            * 取消监听当前时间和高度索引所在的格点发生变化的消息
            * @param {() => void} cb
            * @return {*}  {IMeshDataProvider}
            * @memberof IMeshDataProvider
            */
        offActiveGridUpdated(cb: () => void): IMeshDataProvider;
}
/**
    * 格点数据访问器构造参数
    */
export interface IMeshDataProviderOptions {
        /**
            *
            * updateMesh的最大时间维度，小于等于0表示没有限制。默认为0。
            * 当设置的数值小于当前初始化的时候已经存在的时次数时将会自动设置为当前已经存在的时次数
            * 启用限制之后，当调用addGridTo方法时，如果已经达到数量，则自动将第一个时次移除
            * @type {number}
            * @memberof IMeshDataProviderBaseOptions
            */
        gridOptions?: IGridDataOptions;
}
/**
    * 格点数据访问器基础类型，抽象类
    */
export class MeshDataProvider extends Evented implements IMeshDataProvider {
        protected options: IMeshDataProviderOptions;
        gridOptions?: IGridDataOptions;
        /**
            * 构建格点数据访问器，抽象类，不可直接创建
            * @param options 构建参数
            */
        constructor(meshes: IMeshCollection[][], options?: IMeshDataProviderOptions);
        /**
            * 数据元信息（头信息）
            */
        meta: {
                [key: string]: any;
        };
        meshOptions: IMeshDataOptions;
        protected mc: IMeshCollection[][];
        allMeshes(): IMeshCollection[][];
        protected addMeshTo(grid: IMeshCollection, dimIdx: number, levelIdx: number, levelOfZ: number, setActive: boolean, meshes: IMeshCollection[][]): this;
        /**
            * 获取当前时间维度值，可能为小数
            */
        get currentTIdx(): number;
        /**
            * 设置当前时间维度值，如果超过了数据中的时间维度，则不执行任何操作
            */
        set currentTIdx(idx: number);
        /**
            * 获取当前高度维度值，可能为小数
            */
        get currentZIdx(): number;
        /**
            * 设置当前层次维度信息，当设置超出维度范围时，不执行操作
            */
        set currentZIdx(idx: number);
        getGrid(dim?: any, level?: any): IMeshCollection;
        allGrids(): IMeshCollection[][];
        updateMesh(grid: IMeshCollection, dimIdx: number, levelIdx: number, levelOfZ: number, setActive: boolean): IMeshDataProvider;
        getIntTIdx(): number;
        getIntZIdx(): number;
        onTChanged(cb: () => void): IMeshDataProvider;
        onZChanged(cb: () => void): IMeshDataProvider;
        onTZChanged(cb: () => void): IMeshDataProvider;
        onIntTChanged(cb: () => void): IMeshDataProvider;
        onIntZChanged(cb: () => void): IMeshDataProvider;
        onIntTZChanged(cb: () => void): IMeshDataProvider;
        offTChanged(cb: () => void): IMeshDataProvider;
        offZChanged(cb: () => void): IMeshDataProvider;
        offTZChanged(cb: () => void): IMeshDataProvider;
        offIntTChanged(cb: () => void): IMeshDataProvider;
        offIntZChanged(cb: () => void): IMeshDataProvider;
        offIntTZChanged(cb: () => void): IMeshDataProvider;
        onActiveGridUpdated(cb: () => void): IMeshDataProvider;
        offActiveGridUpdated(cb: () => void): IMeshDataProvider;
}

/**
  * PolyLine class
  *
  * <AUTHOR> Wang
  */
export class WCPolyLine {
    Value: number;
    Type: string;
    BorderIdx: number;
    PointList: WCPointD[];
}

/**
  * Border class - contour line border
  *
  * <AUTHOR> Wang
  * @version $Revision: 1.6 $
  */
export class WCBorder {
    LineList: WCBorderLine[];
    getLineNum(): number;
}

/**
  * 线条生成类
  *
  * @export
  * @class PlotPolyline
  * @extends {PlotLineBase}
  */
export class PlotPolyline extends PlotLineBase {
    constructor(points?: any, options?: {
        finishPointCount: number;
    });
    protected type: string;
    protected generate(): void;
}

export interface IMeshDataOptions {
    tCount: number;
    zValues?: number[];
}
export interface IMeshObject {
    vertices: BufferArray;
    indices?: BufferArray;
    properties?: {
        [key: string]: any;
    };
}
export interface IMeshCollection {
    meshes: IMeshObject[];
}

export class WCPointD {
    X: number;
    Y: number;
    constructor(X?: number, Y?: number);
    clone(): WCPointD;
}

/**
  * BorderLine class
  *
  * <AUTHOR> Wang
  */
export class WCBorderLine {
    area: number;
    extent: WCExtent;
    isOutLine: boolean;
    isClockwise: boolean;
    pointList: WCPointD[];
    ijPointList: WCIJPoint[];
}

/**
  * Extent class
  *
  * <AUTHOR> Wang
  */
export class WCExtent {
    xMin?: number;
    xMax?: number;
    yMin?: number;
    yMax?: number;
    constructor(xMin?: number, xMax?: number, yMin?: number, yMax?: number);
    Include(bExtent: WCExtent): boolean;
}

/**
  * Point integer, to indicate the position in grid data
  *
  * <AUTHOR> Wang
  */
export class WCIJPoint {
    I: number;
    J: number;
}

    


}