/*! Terraformer JS - 1.2.0 - 2018-05-17
*   https://github.com/esri/terraformer-wkt-parser
*   Copyright (c) 2013-2018 Esri, Inc.
*   Licensed MIT */!function(a,b){if("object"==typeof module&&"object"==typeof module.exports)exports=module.exports=b(require("terraformer"));else if("object"==typeof navigator){if(!a.Terraformer)throw new Error("Terraformer.WKT requires the core Terraformer library. http://github.com/esri/terraformer");a.Terraformer.WKT=b(a.Terraformer)}}(this,function(a){function b(a){this.data=[a],this.type="PointArray"}function c(a){this.data=a,this.type="Ring"}function d(a){this.data=[a],this.type="RingList"}function e(a){this.data=[a],this.type="PolygonList"}function f(b){var c;try{c=q.parse(b)}catch(a){throw Error("Unable to parse: "+a)}return a.Primitive(c)}function g(a){for(var b=[],c="",d=0;d<a.length;d++)b.push(a[d].join(" "));return c+="("+b.join(", ")+")"}function h(a){var b="POINT ";return void 0===a.coordinates||0===a.coordinates.length?b+="EMPTY":(3===a.coordinates.length?b+=a.properties&&a.properties.m===!0?"M ":"Z ":4===a.coordinates.length&&(b+="ZM "),b+="("+a.coordinates.join(" ")+")")}function j(a){var b="LINESTRING ";return void 0===a.coordinates||0===a.coordinates.length||0===a.coordinates[0].length?b+="EMPTY":(3===a.coordinates[0].length?b+=a.properties&&a.properties.m===!0?"M ":"Z ":4===a.coordinates[0].length&&(b+="ZM "),b+=g(a.coordinates))}function k(a){var b="POLYGON ";if(void 0===a.coordinates||0===a.coordinates.length||0===a.coordinates[0].length)return b+="EMPTY";3===a.coordinates[0][0].length?b+=a.properties&&a.properties.m===!0?"M ":"Z ":4===a.coordinates[0][0].length&&(b+="ZM "),b+="(";for(var c=[],d=0;d<a.coordinates.length;d++)c.push(g(a.coordinates[d]));return b+=c.join(", "),b+=")"}function l(a){var b="MULTIPOINT ";return void 0===a.coordinates||0===a.coordinates.length||0===a.coordinates[0].length?b+="EMPTY":(3===a.coordinates[0].length?b+=a.properties&&a.properties.m===!0?"M ":"Z ":4===a.coordinates[0].length&&(b+="ZM "),b+=g(a.coordinates))}function m(a){var b="MULTILINESTRING ";if(void 0===a.coordinates||0===a.coordinates.length||0===a.coordinates[0].length)return b+="EMPTY";3===a.coordinates[0][0].length?b+=a.properties&&a.properties.m===!0?"M ":"Z ":4===a.coordinates[0][0].length&&(b+="ZM "),b+="(";for(var c=[],d=0;d<a.coordinates.length;d++)c.push(g(a.coordinates[d]));return b+=c.join(", "),b+=")"}function n(a){var b="MULTIPOLYGON ";if(void 0===a.coordinates||0===a.coordinates.length||0===a.coordinates[0].length)return b+="EMPTY";3===a.coordinates[0][0][0].length?b+=a.properties&&a.properties.m===!0?"M ":"Z ":4===a.coordinates[0][0][0].length&&(b+="ZM "),b+="(";for(var c=[],d=0;d<a.coordinates.length;d++){for(var e="(",f=[],h=0;h<a.coordinates[d].length;h++)f.push(g(a.coordinates[d][h]));e+=f.join(", "),e+=")",c.push(e)}return b+=c.join(", "),b+=")"}function o(a){switch(a.type){case"Point":return h(a);case"LineString":return j(a);case"Polygon":return k(a);case"MultiPoint":return l(a);case"MultiLineString":return m(a);case"MultiPolygon":return n(a);case"GeometryCollection":var b="GEOMETRYCOLLECTION",c=[];for(i=0;i<a.geometries.length;i++)c.push(o(a.geometries[i]));return b+"("+c.join(", ")+")";default:throw Error("Unknown Type: "+a.type)}}var p={},q=function(){function a(){this.yy={}}var f={trace:function(){},yy:{},symbols_:{error:2,expressions:3,point:4,EOF:5,linestring:6,polygon:7,multipoint:8,multilinestring:9,multipolygon:10,coordinate:11,DOUBLE_TOK:12,ptarray:13,COMMA:14,ring_list:15,ring:16,"(":17,")":18,POINT:19,Z:20,ZM:21,M:22,EMPTY:23,point_untagged:24,polygon_list:25,polygon_untagged:26,point_list:27,LINESTRING:28,POLYGON:29,MULTIPOINT:30,MULTILINESTRING:31,MULTIPOLYGON:32,$accept:0,$end:1},terminals_:{2:"error",5:"EOF",12:"DOUBLE_TOK",14:"COMMA",17:"(",18:")",19:"POINT",20:"Z",21:"ZM",22:"M",23:"EMPTY",28:"LINESTRING",29:"POLYGON",30:"MULTIPOINT",31:"MULTILINESTRING",32:"MULTIPOLYGON"},productions_:[0,[3,2],[3,2],[3,2],[3,2],[3,2],[3,2],[11,2],[11,3],[11,4],[13,3],[13,1],[15,3],[15,1],[16,3],[4,4],[4,5],[4,5],[4,5],[4,2],[24,1],[24,3],[25,3],[25,1],[26,3],[27,3],[27,1],[6,4],[6,5],[6,5],[6,5],[6,2],[7,4],[7,5],[7,5],[7,5],[7,2],[8,4],[8,5],[8,5],[8,5],[8,2],[9,4],[9,5],[9,5],[9,5],[9,2],[10,4],[10,5],[10,5],[10,5],[10,2]],performAction:function(a,f,g,h,i,j,k){var l=j.length-1;switch(i){case 1:return j[l-1];case 2:return j[l-1];case 3:return j[l-1];case 4:return j[l-1];case 5:return j[l-1];case 6:return j[l-1];case 7:this.$=new b([Number(j[l-1]),Number(j[l])]);break;case 8:this.$=new b([Number(j[l-2]),Number(j[l-1]),Number(j[l])]);break;case 9:this.$=new b([Number(j[l-3]),Number(j[l-2]),Number(j[l-1]),Number(j[l])]);break;case 10:this.$=j[l-2].addPoint(j[l]);break;case 11:this.$=j[l];break;case 12:this.$=j[l-2].addRing(j[l]);break;case 13:this.$=new d(j[l]);break;case 14:this.$=new c(j[l-1]);break;case 15:this.$={type:"Point",coordinates:j[l-1].data[0]};break;case 16:this.$={type:"Point",coordinates:j[l-1].data[0],properties:{z:!0}};break;case 17:this.$={type:"Point",coordinates:j[l-1].data[0],properties:{z:!0,m:!0}};break;case 18:this.$={type:"Point",coordinates:j[l-1].data[0],properties:{m:!0}};break;case 19:this.$={type:"Point",coordinates:[]};break;case 20:this.$=j[l];break;case 21:this.$=j[l-1];break;case 22:this.$=j[l-2].addPolygon(j[l]);break;case 23:this.$=new e(j[l]);break;case 24:this.$=j[l-1];break;case 25:this.$=j[l-2].addPoint(j[l]);break;case 26:this.$=j[l];break;case 27:this.$={type:"LineString",coordinates:j[l-1].data};break;case 28:this.$={type:"LineString",coordinates:j[l-1].data,properties:{z:!0}};break;case 29:this.$={type:"LineString",coordinates:j[l-1].data,properties:{m:!0}};break;case 30:this.$={type:"LineString",coordinates:j[l-1].data,properties:{z:!0,m:!0}};break;case 31:this.$={type:"LineString",coordinates:[]};break;case 32:this.$={type:"Polygon",coordinates:j[l-1].toJSON()};break;case 33:this.$={type:"Polygon",coordinates:j[l-1].toJSON(),properties:{z:!0}};break;case 34:this.$={type:"Polygon",coordinates:j[l-1].toJSON(),properties:{m:!0}};break;case 35:this.$={type:"Polygon",coordinates:j[l-1].toJSON(),properties:{z:!0,m:!0}};break;case 36:this.$={type:"Polygon",coordinates:[]};break;case 37:this.$={type:"MultiPoint",coordinates:j[l-1].data};break;case 38:this.$={type:"MultiPoint",coordinates:j[l-1].data,properties:{z:!0}};break;case 39:this.$={type:"MultiPoint",coordinates:j[l-1].data,properties:{m:!0}};break;case 40:this.$={type:"MultiPoint",coordinates:j[l-1].data,properties:{z:!0,m:!0}};break;case 41:this.$={type:"MultiPoint",coordinates:[]};break;case 42:this.$={type:"MultiLineString",coordinates:j[l-1].toJSON()};break;case 43:this.$={type:"MultiLineString",coordinates:j[l-1].toJSON(),properties:{z:!0}};break;case 44:this.$={type:"MultiLineString",coordinates:j[l-1].toJSON(),properties:{m:!0}};break;case 45:this.$={type:"MultiLineString",coordinates:j[l-1].toJSON(),properties:{z:!0,m:!0}};break;case 46:this.$={type:"MultiLineString",coordinates:[]};break;case 47:this.$={type:"MultiPolygon",coordinates:j[l-1].toJSON()};break;case 48:this.$={type:"MultiPolygon",coordinates:j[l-1].toJSON(),properties:{z:!0}};break;case 49:this.$={type:"MultiPolygon",coordinates:j[l-1].toJSON(),properties:{m:!0}};break;case 50:this.$={type:"MultiPolygon",coordinates:j[l-1].toJSON(),properties:{z:!0,m:!0}};break;case 51:this.$={type:"MultiPolygon",coordinates:[]}}},table:[{3:1,4:2,6:3,7:4,8:5,9:6,10:7,19:[1,8],28:[1,9],29:[1,10],30:[1,11],31:[1,12],32:[1,13]},{1:[3]},{5:[1,14]},{5:[1,15]},{5:[1,16]},{5:[1,17]},{5:[1,18]},{5:[1,19]},{17:[1,20],20:[1,21],21:[1,22],22:[1,23],23:[1,24]},{17:[1,25],20:[1,26],21:[1,28],22:[1,27],23:[1,29]},{17:[1,30],20:[1,31],21:[1,33],22:[1,32],23:[1,34]},{17:[1,35],20:[1,36],21:[1,38],22:[1,37],23:[1,39]},{17:[1,40],20:[1,41],21:[1,43],22:[1,42],23:[1,44]},{17:[1,45],20:[1,46],21:[1,48],22:[1,47],23:[1,49]},{1:[2,1]},{1:[2,2]},{1:[2,3]},{1:[2,4]},{1:[2,5]},{1:[2,6]},{11:51,12:[1,52],13:50},{17:[1,53]},{17:[1,54]},{17:[1,55]},{5:[2,19]},{11:58,12:[1,52],17:[1,59],24:57,27:56},{17:[1,60]},{17:[1,61]},{17:[1,62]},{5:[2,31]},{15:63,16:64,17:[1,65]},{17:[1,66]},{17:[1,67]},{17:[1,68]},{5:[2,36]},{11:58,12:[1,52],17:[1,59],24:57,27:69},{17:[1,70]},{17:[1,71]},{17:[1,72]},{5:[2,41]},{15:73,16:64,17:[1,65]},{17:[1,74]},{17:[1,75]},{17:[1,76]},{5:[2,46]},{17:[1,79],25:77,26:78},{17:[1,80]},{17:[1,81]},{17:[1,82]},{5:[2,51]},{14:[1,84],18:[1,83]},{14:[2,11],18:[2,11]},{12:[1,85]},{11:51,12:[1,52],13:86},{11:51,12:[1,52],13:87},{11:51,12:[1,52],13:88},{14:[1,90],18:[1,89]},{14:[2,26],18:[2,26]},{14:[2,20],18:[2,20]},{11:91,12:[1,52]},{11:58,12:[1,52],17:[1,59],24:57,27:92},{11:58,12:[1,52],17:[1,59],24:57,27:93},{11:58,12:[1,52],17:[1,59],24:57,27:94},{14:[1,96],18:[1,95]},{14:[2,13],18:[2,13]},{11:51,12:[1,52],13:97},{15:98,16:64,17:[1,65]},{15:99,16:64,17:[1,65]},{15:100,16:64,17:[1,65]},{14:[1,90],18:[1,101]},{11:58,12:[1,52],17:[1,59],24:57,27:102},{11:58,12:[1,52],17:[1,59],24:57,27:103},{11:58,12:[1,52],17:[1,59],24:57,27:104},{14:[1,96],18:[1,105]},{15:106,16:64,17:[1,65]},{15:107,16:64,17:[1,65]},{15:108,16:64,17:[1,65]},{14:[1,110],18:[1,109]},{14:[2,23],18:[2,23]},{15:111,16:64,17:[1,65]},{17:[1,79],25:112,26:78},{17:[1,79],25:113,26:78},{17:[1,79],25:114,26:78},{5:[2,15]},{11:115,12:[1,52]},{12:[1,116],14:[2,7],18:[2,7]},{14:[1,84],18:[1,117]},{14:[1,84],18:[1,118]},{14:[1,84],18:[1,119]},{5:[2,27]},{11:58,12:[1,52],17:[1,59],24:120},{18:[1,121]},{14:[1,90],18:[1,122]},{14:[1,90],18:[1,123]},{14:[1,90],18:[1,124]},{5:[2,32]},{16:125,17:[1,65]},{14:[1,84],18:[1,126]},{14:[1,96],18:[1,127]},{14:[1,96],18:[1,128]},{14:[1,96],18:[1,129]},{5:[2,37]},{14:[1,90],18:[1,130]},{14:[1,90],18:[1,131]},{14:[1,90],18:[1,132]},{5:[2,42]},{14:[1,96],18:[1,133]},{14:[1,96],18:[1,134]},{14:[1,96],18:[1,135]},{5:[2,47]},{17:[1,79],26:136},{14:[1,96],18:[1,137]},{14:[1,110],18:[1,138]},{14:[1,110],18:[1,139]},{14:[1,110],18:[1,140]},{14:[2,10],18:[2,10]},{12:[1,141],14:[2,8],18:[2,8]},{5:[2,16]},{5:[2,17]},{5:[2,18]},{14:[2,25],18:[2,25]},{14:[2,21],18:[2,21]},{5:[2,28]},{5:[2,29]},{5:[2,30]},{14:[2,12],18:[2,12]},{14:[2,14],18:[2,14]},{5:[2,33]},{5:[2,34]},{5:[2,35]},{5:[2,38]},{5:[2,39]},{5:[2,40]},{5:[2,43]},{5:[2,44]},{5:[2,45]},{14:[2,22],18:[2,22]},{14:[2,24],18:[2,24]},{5:[2,48]},{5:[2,49]},{5:[2,50]},{14:[2,9],18:[2,9]}],defaultActions:{14:[2,1],15:[2,2],16:[2,3],17:[2,4],18:[2,5],19:[2,6],24:[2,19],29:[2,31],34:[2,36],39:[2,41],44:[2,46],49:[2,51],83:[2,15],89:[2,27],95:[2,32],101:[2,37],105:[2,42],109:[2,47],117:[2,16],118:[2,17],119:[2,18],122:[2,28],123:[2,29],124:[2,30],127:[2,33],128:[2,34],129:[2,35],130:[2,38],131:[2,39],132:[2,40],133:[2,43],134:[2,44],135:[2,45],138:[2,48],139:[2,49],140:[2,50]},parseError:function(a,b){throw new Error(a)},parse:function(a){function b(){var a;return a=c.lexer.lex()||1,"number"!=typeof a&&(a=c.symbols_[a]||a),a}var c=this,d=[0],e=[null],f=[],g=this.table,h="",i=0,j=0,k=0;this.lexer.setInput(a),this.lexer.yy=this.yy,this.yy.lexer=this.lexer,this.yy.parser=this,"undefined"==typeof this.lexer.yylloc&&(this.lexer.yylloc={});var l=this.lexer.yylloc;f.push(l);var m=this.lexer.options&&this.lexer.options.ranges;"function"==typeof this.yy.parseError&&(this.parseError=this.yy.parseError);for(var n,o,p,q,r,s,t,u,v,w={};;){if(p=d[d.length-1],this.defaultActions[p]?q=this.defaultActions[p]:(null!==n&&"undefined"!=typeof n||(n=b()),q=g[p]&&g[p][n]),"undefined"==typeof q||!q.length||!q[0]){var x="";if(!k){v=[];for(s in g[p])this.terminals_[s]&&s>2&&v.push("'"+this.terminals_[s]+"'");x=this.lexer.showPosition?"Parse error on line "+(i+1)+":\n"+this.lexer.showPosition()+"\nExpecting "+v.join(", ")+", got '"+(this.terminals_[n]||n)+"'":"Parse error on line "+(i+1)+": Unexpected "+(1==n?"end of input":"'"+(this.terminals_[n]||n)+"'"),this.parseError(x,{text:this.lexer.match,token:this.terminals_[n]||n,line:this.lexer.yylineno,loc:l,expected:v})}}if(q[0]instanceof Array&&q.length>1)throw new Error("Parse Error: multiple actions possible at state: "+p+", token: "+n);switch(q[0]){case 1:d.push(n),e.push(this.lexer.yytext),f.push(this.lexer.yylloc),d.push(q[1]),n=null,o?(n=o,o=null):(j=this.lexer.yyleng,h=this.lexer.yytext,i=this.lexer.yylineno,l=this.lexer.yylloc,k>0&&k--);break;case 2:if(t=this.productions_[q[1]][1],w.$=e[e.length-t],w._$={first_line:f[f.length-(t||1)].first_line,last_line:f[f.length-1].last_line,first_column:f[f.length-(t||1)].first_column,last_column:f[f.length-1].last_column},m&&(w._$.range=[f[f.length-(t||1)].range[0],f[f.length-1].range[1]]),r=this.performAction.call(w,h,j,i,this.yy,q[1],e,f),"undefined"!=typeof r)return r;t&&(d=d.slice(0,-1*t*2),e=e.slice(0,-1*t),f=f.slice(0,-1*t)),d.push(this.productions_[q[1]][0]),e.push(w.$),f.push(w._$),u=g[d[d.length-2]][d[d.length-1]],d.push(u);break;case 3:return!0}}return!0}},g=function(){var a={EOF:1,parseError:function(a,b){if(!this.yy.parser)throw new Error(a);this.yy.parser.parseError(a,b)},setInput:function(a){return this._input=a,this._more=this._less=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var a=this._input[0];this.yytext+=a,this.yyleng++,this.offset++,this.match+=a,this.matched+=a;var b=a.match(/(?:\r\n?|\n).*/g);return b?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),a},unput:function(a){var b=a.length,c=a.split(/(?:\r\n?|\n)/g);this._input=a+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-b-1),this.offset-=b;var d=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),c.length-1&&(this.yylineno-=c.length-1);var e=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:c?(c.length===d.length?this.yylloc.first_column:0)+d[d.length-c.length].length-c[0].length:this.yylloc.first_column-b},this.options.ranges&&(this.yylloc.range=[e[0],e[0]+this.yyleng-b]),this},more:function(){return this._more=!0,this},less:function(a){this.unput(this.match.slice(a))},pastInput:function(){var a=this.matched.substr(0,this.matched.length-this.match.length);return(a.length>20?"...":"")+a.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var a=this.match;return a.length<20&&(a+=this._input.substr(0,20-a.length)),(a.substr(0,20)+(a.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var a=this.pastInput(),b=new Array(a.length+1).join("-");return a+this.upcomingInput()+"\n"+b+"^"},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0);var a,b,c,d,e;this._more||(this.yytext="",this.match="");for(var f=this._currentRules(),g=0;g<f.length&&(c=this._input.match(this.rules[f[g]]),!c||b&&!(c[0].length>b[0].length)||(b=c,d=g,this.options.flex));g++);return b?(e=b[0].match(/(?:\r\n?|\n).*/g),e&&(this.yylineno+=e.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:e?e[e.length-1].length-e[e.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+b[0].length},this.yytext+=b[0],this.match+=b[0],this.matches=b,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._input=this._input.slice(b[0].length),this.matched+=b[0],a=this.performAction.call(this,this.yy,this,f[d],this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),a?a:void 0):""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var a=this.next();return"undefined"!=typeof a?a:this.lex()},begin:function(a){this.conditionStack.push(a)},popState:function(){return this.conditionStack.pop()},_currentRules:function(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function(a){this.begin(a)}};return a.options={},a.performAction=function(a,b,c,d){switch(c){case 0:break;case 1:return 17;case 2:return 18;case 3:return 12;case 4:return 19;case 5:return 28;case 6:return 29;case 7:return 30;case 8:return 31;case 9:return 32;case 10:return 14;case 11:return 23;case 12:return 22;case 13:return 20;case 14:return 21;case 15:return 5;case 16:return"INVALID"}},a.rules=[/^(?:\s+)/,/^(?:\()/,/^(?:\))/,/^(?:-?[0-9]+(\.[0-9]+)?([eE][\-\+]?[0-9]+)?)/,/^(?:POINT\b)/,/^(?:LINESTRING\b)/,/^(?:POLYGON\b)/,/^(?:MULTIPOINT\b)/,/^(?:MULTILINESTRING\b)/,/^(?:MULTIPOLYGON\b)/,/^(?:,)/,/^(?:EMPTY\b)/,/^(?:M\b)/,/^(?:Z\b)/,/^(?:ZM\b)/,/^(?:$)/,/^(?:.)/],a.conditions={INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],inclusive:!0}},a}();return f.lexer=g,a.prototype=f,f.Parser=a,new a}();return b.prototype.addPoint=function(a){return"PointArray"===a.type?this.data=this.data.concat(a.data):this.data.push(a),this},b.prototype.toJSON=function(){return this.data},c.prototype.toJSON=function(){for(var a=[],b=0;b<this.data.data.length;b++)a.push(this.data.data[b]);return a},d.prototype.addRing=function(a){return this.data.push(a),this},d.prototype.toJSON=function(){for(var a=[],b=0;b<this.data.length;b++)a.push(this.data[b].toJSON());return 1===a.length?a:a},e.prototype.addPolygon=function(a){return this.data.push(a),this},e.prototype.toJSON=function(){for(var a=[],b=0;b<this.data.length;b++)a=a.concat([this.data[b].toJSON()]);return a},p.parser=q,p.Parser=q.Parser,p.parse=f,p.convert=o,p});